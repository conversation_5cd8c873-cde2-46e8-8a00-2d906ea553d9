													=======================
													 Publisher Pseudo code:
													=======================

ASUMPTIONS: 
   1. Publisher will be running as a service (say every 10 mins, which is configurable)
   2. Subscriber whenever set the next_bill_fetch_date, it always sets time around night time (say 1 AM), we will refer it as PRIMARY_CYCLE in further discussion, So in this case our PRIMARY_CYCLE is 1 AM
   3. Retry attempts within publisher will add 1 hour (configurable) time in the next_bill_fetch_date field of records and we will refer it as SECONDARY_CYCLE in further discussions


PUBLISHER START:
	Below Steps will be repeated every 10 mins:

	    Step 1 is for those records (DEAD RECORDS) which have been published for more than 1 day (configurable) but haven't returned
		1. Fetch records from bills whose STATUS = 'published' and UPDATED_AT < now()-1 day in BATCHES (say 1000), 
			    For each batch do this: 
				    For each record in current batch do this: 
					     CRITICAL Log (if want to): Message not returned from GW Process
					     Retry (if want to) like this:
						      PROMOTE_TO_RETRIAL_PHASE (record, NEXT_PRIMARY_CYCLE)  //we will be postponing it to next PRIMARY_CYCLE


	    Step 2 is for all those records (FRESH RECORDS) who are still to be published today (either they are pending or are there for retry)
		2. Fetch records from bills whose STATUS!='published' and NEXT_BILL_FETCH_DATE < now() and RETRY_COUNT < MAX_RETRY_COUNT and this happens in batches (say 1000)
		        For each batch do this: 
				    For each record in current batch do this: 
					    a.  Get ingress and egress rates of the corresponding queue from RMQ rest api (this happens on-demand)
					    b.  Get latest avgerage latency of the corresponding gateway service from datadog api (this happens periodically, not on-demand)
					    c.  Calculate (maxPossibleOutputRate) max possible consumption rate of gateway queue using formula:
					             prefetchCountPerConsumer / avg_latency  
					    d.  if current_ingress_rate > 85% of maxPossibleOutputRate  {   
						        then wait for [ Math.ceil(1000/maxPossibleOutputRate) ] milliseconds
						        and then GOTO step (a) again 
						    }
					        else {  //Go ahead with publishing the record
					            Try Publishing the record to queue
					            update the record in DB and set STATUS = 'published' and RETRY_COUNT += 1
		                        Now, wait for [ Math.ceil(1000/maxPossibleOutputRate) ] milliseconds and proceed with next record
					        } 



/** OLD LOGIC for Step 2 **

	    Step 2 is for all those records (FRESH RECORDS) who are still to be published today (either they are pending or are there for retry)
		2. Fetch records from bills whose STATUS!='published' and NEXT_BILL_FETCH_DATE < now() and RETRY_COUNT < MAX_RETRY_COUNT and this happens in batches (say 1000)
		        For each batch do this: 
				    For each record in current batch do this: 
					    a.  Get queue count  
					    b.  if queue count > MAX_QUEUE_COUNT  {   // (RMQ is full)
						        PROMOTE_TO_RETRIAL_PHASE (record, NEXT_SECONDARY_CYCLE)
						    }
					        else {  //Go ahead with publishing the record
					            Try Publishing the record to queue

					            //It might be possible that below code may not be needed (may be its guranteed that record will be published)
			                    if successful then
			                        update the record in DB and set
					                    STATUS = 'published'
					                    RETRY_COUNT += 1
		                        else 
		                            PROMOTE_TO_RETRIAL_PHASE (record, NEXT_SECONDARY_CYCLE)
					        } 
*/


END
---------------------------------------------------------------------------------------------

where MAX_QUEUE_COUNT is the const value and represents the max limit of the rabbitmq queue



==================================================================================
Procedure to increase or max out the retry count in case the record not published:
==================================================================================


PROMOTE_TO_RETRIAL_PHASE ( $record, $cycle) :
	START
		  if($record.retry_count == MAX_RETRY_COUNT) { //Special Case, when retry_count is reached to max value
		      update the $record in DB and set 
		          STATUS = 'pending' and 
		          RETRY_COUNT = 0 and 
		          NEXT_BILL_FETCH_DATE = N-th PRIMARY_CYCLE      /* The value of N needs to be decided here */
		  }
		  else {
		      update the $record in DB and set 
		          STATUS = 'retry' and 
		          RETRY_COUNT += 1 and 
		          NEXT_BILL_FETCH_DATE = $cycle 
		  }

    END 

---------------------------------------------------------------------------------------------
where: MAX_RETRY_COUNT is a const value and represents the maximum permissible retry count
       $record is the record reference to be updated
       $cycle can either be (PRIMARY_CYCLE or SECONDARY_CYCLE) 

NOTE: Whenever a record got updated from subscriber side, these things will happen: 
        STATUS will be set to 'pending' again
        RETRY_COUNT will be set to 0
        NEXT_BILL_FETCH_DATE will be set to some N-th PRIMARY_CYCLE (N needs to be decided) 
        along with this other important things like amount,billDueDate etc will also be updated, but as far as publisher logic is concerned only first three things are necessary.

Definitions: 
   DEAD RECORD: A record whose status is 'published' and last updated was more than 1 day
   FRESH RECORD: A record whose status is still not 'published' and next_bill_fetch_date < now() and retry_count < MAX_RETRY_COUNT