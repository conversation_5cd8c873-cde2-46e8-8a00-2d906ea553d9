/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    
    import chai, { assert } from "chai";
    import LOCALISATION_CLIENT from 'localisation-client';
    import sinon from 'sinon';
    import MOMENT from 'moment';
    import sinon<PERSON><PERSON> from "sinon-chai";
    import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>IBER from '../../services/billSubscriber';
    import NOTIFICATION from '../../models/notification'
    import CONTROLLER from '../../controllers';
    import L from 'lgr';
    import config from '../../config';
    // import {mockNotificationCallbackdata} from '../helpers';
    import STARTUP_MOCK from '../__mocks__/startUp'
    import chaiAsPromised from "chai-as-promised";
    import { body, header, query } from 'express-validator';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;



    describe("getBillV2 | Validation failure errors", function () {    
        let controller;
        let res =  {
            status: (code) => {
                expect(code).to.be.equal(422);
                return res;
            },
            json: (result) => {
                expect(result.error_message).to.be.equal('Operator, customerId,productId and rechargeNumber is mandatory field');
                return;
            }
        };
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);
                done();
            });
        });        
    
        it("getBillV2 | productId not present", () => {
            let req = {
                query: {
                    "customerId": "4234528",                
                    "operator": "airtel",    
                    "rechargeNumber": "**********"                
                }
            };
            controller.getBillV2(req, res);
        });
    
        it("getBillV2 | customerId not present", () => {
            let req = {
                query: {     
                    "operator": "airtel",            
                    "productId": "21",                                
                    "rechargeNumber": "**********"                                    
                }
            };
            controller.getBillV2(req, res);
        });
    
        it("getBillV2 | rechargeNumber not present", () => {
            let req = {
                query: {
                    "customerId": "4234528",  
                    "productId": "21",                
                    "operator": "airtel" 
                                                                        
                }
            };
            controller.getBillV2(req, res);
        });
    
        it("getBillV2 | operator not present", () => {
            let req = {
                query: {
                    "customerId": "4234528", 
                    "productId": "21",                                   
                    "rechargeNumber": "**********"
                }
            };
            controller.getBillV2(req, res);
        });
    
    })
    
    describe("getBillV2 | All Edge cases test suits", function () {    
        let controller;  
        let req = {
            query: { "customerId": "667781","operator": "visa_hdfcbank","productId": "********","rechargeNumber": "5555 77XX XXXX 8767" }
        } 
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                controller = new CONTROLLER(options);                          
                done();
            });
        });        
    
        it("getBillV2 | Unable to identify internal flow", () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(500);
                    return res;
                },
                json: (result) => {            
                    expect(result.error_message).to.be.equal('Unable to identify internal flow');                
                    return;
                }
            };
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').callsFake(null);            
            controller.getBillV2(req, res);
            stub.restore();
        });

        it("getBillV2 | CIN is not present for CC operator", () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(422);
                    return res;
                },
                json: (result) => {            
                    expect(result.error_message).to.be.equal('CIN is not present for CC operator');                
                    return;
                }
            };

            let reminderFlowInstance = {
	            notificationManager_validateReq : (query) => {  
                    if (!query.referenceId){
                        return {
                            status : false,
                            errors : []
                        }                        
                    }else {
                        return {
                            status : true,
                            errors : []
                        }
                    }
                }
            };            
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.getBillV2(req, res);
            stub.restore();
        });

        it("getBillV2 | Operator not migrated", () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: (result) => {            
                    expect(result.error).to.be.equal('Operator not migrated');                
                    return;
                }
            };

            let reminderFlowInstance = {
	            notificationManager_validateReq : () => {                    
                    return {
                        status : true,
                        errors : []
                    }
                },
                getTableName : () => {
                    return null
                }
            };            
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.getBillV2(req, res);
            stub.restore();
        });


        it("getBillV2 | Database error", () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: (result) => {            
                    expect(result.error).to.be.equal('Database error');                
                    return;
                }
            };

            let reminderFlowInstance = {
	            notificationManager_validateReq : () => {                    
                    return {
                        status : true,
                        errors : []
                    }
                },
                getTableName : () => {
                    return "bills_airtel"
                },
                notificationManager_getBill : (error, data) =>{
                    error = "Database error";
                    data = [];                    
                    if (error) {
                        return res.status(200).json({ "status": 204, "error": error });
                    }else{
                        if (data.length === 0) {                            
                            return res.status(200).json({ "status": 204, "error": 'No Data found!!!' });                        
                        }else {                                      
                            return res.status(200).json({ "status": 200, "data": data });
                        }                         
                    }           
                }
            };     
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.getBillV2(req, res);
            stub.restore();
        });

        it("getBillV2 | No Data found!!!", () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: (result) => {            
                    expect(result.error).to.be.equal('No Data found!!!');                
                    return;
                }
            };

            let reminderFlowInstance = {
	            notificationManager_validateReq : () => {                    
                    return {
                        status : true,
                        errors : []
                    }
                },
                getTableName : () => {
                    return "bills_airtel"
                },
                notificationManager_getBill : (error, data) =>{
                    error = "";
                    data = [];                    
                    if (error) {
                        return res.status(200).json({ "status": 204, "error": error });
                    }else{
                        if (data.length === 0) {                            
                            return res.status(200).json({ "status": 204, "error": 'No Data found!!!' });                        
                        }else {                                      
                            return res.status(200).json({ "status": 200, "data": data });
                        }                         
                    }           
                }
            };     
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.getBillV2(req, res);
            stub.restore();
        });

        it("getBillV2 | Sucessfully Data send ", () => {
            let getData = {"id":39849,"customerId":1000314568,"rechargeNumber":"888899990013","productId":1200139386,"reference_id":null,"operator":"jaipur vidyut vitran nigam ltd. (jvvnl)","amount":0,"billDate":"2021-06-08T11:29:44.000Z","dueDate":"2019-07-27T00:00:00.000Z","billFetchDate":null,"nextBillFetchDate":"2021-07-13T11:29:44.000Z","gateway":"jvvnlvcid","paytype":null,"service":"electricity","circle":null,"customerMobile":null,"customerEmail":null,"paymentChannel":null,"retryCount":0,"status":11,"reason":null,"extra":null,"published_date":null,"createdAt":"2021-07-13T11:29:44.000Z","updatedAt":"2021-07-15T14:04:39.000Z","userData":null,"notification_status":0,"paymentDate":"2021-01-12T11:29:44.000Z","service_id":1,"customerOtherInfo":null,"is_automatic":0};
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: (result) => {            
                    expect(result.status).to.be.equal(200);                
                    return;
                }
            };            

            let reminderFlowInstance = {
	            notificationManager_validateReq : () => {                    
                    return {
                        status : true,
                        errors : []
                    }
                },
                getTableName : () => {
                    return "bills_airtel"
                },
                notificationManager_getBill : (error, data) =>{
                    error = "";
                    data = [{"id":39849,"customerId":1000314568,"rechargeNumber":"888899990013","productId":1200139386,"reference_id":null,"operator":"jaipur vidyut vitran nigam ltd. (jvvnl)","amount":0,"billDate":"2021-06-08T11:29:44.000Z","dueDate":"2019-07-27T00:00:00.000Z","billFetchDate":null,"nextBillFetchDate":"2021-07-13T11:29:44.000Z","gateway":"jvvnlvcid","paytype":null,"service":"electricity","circle":null,"customerMobile":null,"customerEmail":null,"paymentChannel":null,"retryCount":0,"status":11,"reason":null,"extra":null,"published_date":null,"createdAt":"2021-07-13T11:29:44.000Z","updatedAt":"2021-07-15T14:04:39.000Z","userData":null,"notification_status":0,"paymentDate":"2021-01-12T11:29:44.000Z","service_id":1,"customerOtherInfo":null,"is_automatic":0}];                    
                    if (error) {
                        return res.status(200).json({ "status": 204, "error": error });
                    }else{
                        if (data.length === 0) {                            
                            return res.status(200).json({ "status": 204, "error": 'No Data found!!!' });                        
                        }else {                                      
                            return res.status(200).json({ "status": 200, "data": data[0] });
                        }                         
                    }           
                }
            };     
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.getBillV2(req, res);
            stub.restore();
        });
    }); 


    describe("getRealTimeBill | Validation failure errors", function () {    
        let controller;
        let res =  {
            status: (code) => {
                expect(code).to.be.equal(422);
                return res;
            },
            json: (result) => {
                expect(result.error_message).to.be.equal('Operator, productId and rechargeNumber is mandatory field');
                return;
            }
        };
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);
                console.log(controller.options)
                done();
            });
        });        
    
        it("getRealTimeBill | productId not present", () => {
            let req = {
                query: {                
                    "operator": "airtel",    
                    "rechargeNumber": "**********"                
                }
            };
            controller.getRealTimeBill(req, res);
        });        
    
        it("getRealTimeBill | rechargeNumber not present", () => {
            let req = {
                query: {
                    "productId": "21",                
                    "operator": "airtel" 
                                                                        
                }
            };
            controller.getRealTimeBill(req, res);
        });
    
        it("getRealTimeBill | operator not present", () => {
            let req = {
                query: { 
                    "productId": "21",                                   
                    "rechargeNumber": "**********"
                }
            };
            controller.getRealTimeBill(req, res);
        });
    
    });  

    describe("getRealTimeBill | Validation failure errors for not supporting operator", function () {    
        let controller;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                controller = new CONTROLLER(options);
                console.log(controller.options)
                done();
            });
        });        
    
        it("getRealTimeBill | prepaid operator", () => {
            let req = {
                query: {                
                    "operator": "suntv",    
                    "productId": "21",          
                    "paytype" : "prepaid",
                    "rechargeNumber": "123456789"                
                }
            };
            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(422);
                    return res;
                },
                json: (result) => {
                    expect(result.error_message).to.be.equal(`getRealTimeBill not supported for OP:${req.query.operator}_paytype:${req.query.paytype}`);
                    return;
                }
            };
            controller.getRealTimeBill(req, res);
        });        
    
        it("getRealTimeBill | credit card operator", () => {
            let req = {
                query: {
                    "productId": "21",   
                    "paytype" : "credit card",             
                    "operator": "visa_hdfc" ,
                    "rechargeNumber": "5432 21XX XXXX"   
                                                                        
                }
            };
            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(422);
                    return res;
                },
                json: (result) => {
                    expect(result.error_message).to.be.equal(`getRealTimeBill not supported for OP:${req.query.operator}_paytype:${req.query.paytype}`);
                    return;
                }
            };
            controller.getRealTimeBill(req, res);
        });
    
        it("getRealTimeBill | rent payment operator", () => {
            let req = {
                query: { 
                    "productId": "21",     
                    "paytype" : "postpaid",             
                    "operator": "rent payment" ,                              
                    "rechargeNumber": "**********"
                }
            };
            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(422);
                    return res;
                },
                json: (result) => {
                    expect(result.error_message).to.be.equal(`getRealTimeBill not supported for OP:${req.query.operator}_paytype:${req.query.paytype}`);
                    return;
                }
            };
            controller.getRealTimeBill(req, res);
        });
    
    });

    describe("updateNotificationStatusV3 | test suits", function () {    
        let controller;  
        let req = {
            "body" : { "operator": "visa_hdfcbank","productId": "********","rechargeNumber": "5555 77XX XXXX 8767","notificationStatus" : 0 },            
            header : (xUserId) => {
                if (xUserId)  return "667781"                
            }               
        };
        let reminderFlowInstance = {
            notificationManager_validateReq : () => {  
                if (!req.body.referenceId){
                    return {
                        status : false,
                        errors : [{ "msg": "Invalid value", "param": "referenceId" }]                            
                    }                        
                }else {
                    return {
                        status : true,
                        errors : []
                    }
                }
            },
            getTableName : () => {
                if (!req.body.operator) return null
                return true
            },
            notificationManager_updateReminderDB : () => {
                expect(req.body).to.include.all.keys('rechargeNumber', 'operator','productId','notificationStatus');
                expect(req.header('X-USER-ID')).to.be.equal("667781")
                return 
            },
            notificationManager_updateBillReminderFlag : () => {
                expect(req.body).to.include.all.keys('rechargeNumber', 'operator','productId','notificationStatus');
                expect(req.header('X-USER-ID')).to.be.equal("667781")
                return 
            }
        };
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);                            
                done();
            });
        });        
    
        it("updateNotificationStatusV3 | Unable to identify internal flow", async () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(500);
                    return res;
                },
                json: (result) => {            
                    expect(result.message).to.be.equal('Unable to identify internal flow');                
                    return;
                }
            };
            req.body.referenceId = 'hdfc_ref_id_001';
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').callsFake(null);    
            controller.updateNotificationStatusV3(req, res);
            stub.restore();
        });

        it("updateNotificationStatusV3 | referenceId is not present for CC operator",async () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(422);
                    return res;
                },
                json: (result) => {                                                                               
                    expect(result.message[0].msg).to.be.equal('Invalid value');                
                    expect(result.message[0].param).to.be.equal('referenceId'); 
                    return;
                }
            };  
            req.body.referenceId = '';
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.updateNotificationStatusV3(req, res);
            stub.restore();
        });
  
        it("updateNotificationStatusV3 | Operator not migrated",async () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: (result) => {            
                    expect(result.message).to.be.equal('Operator not migrated');                
                    return;
                }
            };
            req.body.operator = '';
            req.body.referenceId = 'hdfc_ref_id_001';                        
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.updateNotificationStatusV3(req, res);
            stub.restore();
        });

        it("updateNotificationStatusV3 | Notification updated sucessfully",async () => {
            let 
                res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: (result) => {            
                    expect(result.message).to.be.equal('Notification status updates successfully');                
                    return;
                }
            };
            req.body.operator = 'visa_hdfcbank';                                
            let stub = sinon.stub(controller.reminderFlowManager, 'getFlowInstance').returns(reminderFlowInstance);
            controller.updateNotificationStatusV3(req, res);
            stub.restore();
        });

    });
  

    describe("GET /v1/userConsentStatus | test suits", function () {
        let controller;  
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);                          
                done();
            });
        });                

        it("getUserConsentStatus | Missing preferenceKey", () => {

            let req = {

                query: { },

                header : (xUserId) => {

                    if (xUserId)  return 667781                

                }               

            };

            let 

                res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('CustomerId and preferenceKey values are Invalid/missing');                

                    return;

                }

            };

            controller.getUserConsentStatus(req, res);

        });


        it("getUserConsentStatus | Invalid preferenceKey", () => {

            let req = {

                query: { "preferenceKey": "ocl.user.consent.ru_whatsapp_reminders1"},

                header : (xUserId) => {

                    if (xUserId)  return 667781                

                }               

            };

            let 

                res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Invalid preferenceKey');                

                    return;

                }

            };

            controller.getUserConsentStatus(req, res);

        });


        it("getUserConsentStatus | Parameter validation failure", () => {

            let req = {

                query: { "preferenceKey": "ocl.user.consent.ru_whatsapp_reminders"},

                header : (xUserId) => {

                    if (xUserId)  return ''                

                }               

            };

            let 

                res =  {

                status: (code) => {

                    expect(code).to.be.equal(500);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Error while serving request,Please try again in some time');                

                    return;

                }

            };

            controller.getUserConsentStatus(req, res);

        });


        /**

        it("getUserConsentStatus | fetchCustomerWhatsAppNotificationStatus returns DB error", () => {

            let req = {

                query: { "preferenceKey": "ocl.user.consent.ru_whatsapp_reminders"},

                header : (xUserId) => {

                    if (xUserId)  return 1231233                

                }               

            };

            let 

                res =  {

                status: (code) => {

                    expect(code).to.be.equal(500);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Something went wrong');                

                    return;

                }

            };


            let result = {

                response: null,

                statusInfo : {

                    status : null,

                    statusMessage : null

                }

            };


            let fetchCustomerWhatsAppNotificationStatus = (error, whatsapp_notification_status) => {  

                    error = "Database error in customer_transaction_counter table";

                    if(error) {

                        result.statusInfo.status = "FAILURE";

                        result.statusInfo.statusMessage = "Something went wrong";    

                        return res.status(500).json({ "status": 500, "data": result });

                    }

                };

                      

            let stub = sinon.stub(controller , 'transactionCounterModel').returns(fetchCustomerWhatsAppNotificationStatus);

            //let stub = sinon.stub(, 'fetchCustomerWhatsAppNotificationStatus').returns(reminderFlowInstance);

            controller.getUserConsentStatus(req, res);

            stub.restore();


        });

        */


        it("getUserConsentStatus | fetchCustomerWhatsAppNotificationStatus returns success with value -1", () => {

            let req = {

                query: { "preferenceKey": "ocl.user.consent.ru_whatsapp_reminders"},

                header : (xUserId) => {

                    if (xUserId)  return 1231233                

                }               

            };

            let 

                res =  {

                status: (code) => {

                    expect(code).to.be.equal(200);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('SUCCESS');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Request sucessfully served');                

                    expect(result.data.response.preferences[0].key).to.be.equal('ocl.user.consent.ru_whatsapp_reminders');                

                    expect(result.data.response.preferences[0].value).to.be.equal(-1);                

                    return;

                }

            };

            controller.getUserConsentStatus(req, res);

        });


        // it("getUserConsentStatus | fetchCustomerWhatsAppNotificationStatus returns success with value 1/0", () => {    

        // });


        

    });


    

    describe("PUT /v1/userConsentStatus | test suits", function () {

        let controller;  

        before(function (done) {

            STARTUP_MOCK.init(function(error, options){

                controller = new CONTROLLER(options);                          

                done();

            });

        });        

        

        it("updateUserConsentStatus | Missing preferenceKey", () => {

            let req = {

                header : (headerValue) => {

                    if(headerValue == "X-USER-ID") return 667781;

                },

                body: {

                    'preferenceValue' : -1

                }           

            };

            

            let res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('customer_id and preferenceKey values are Invalid/missing');                

                    return;

                }

            };

            controller.updateUserConsentStatus(req, res);

        });



        


        it("updateUserConsentStatus | Missing preferenceValue", () => {

            let req = {

                header : (headerValue) => {

                    if(headerValue == "X-USER-ID") return 667781;

                },

                body: {

                    'preferenceKey' : 'ocl.user.consent.ru_whatsapp_reminders'

                }           

            };

            

            let res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Invalid preferenceValue');                

                    return;

                }

            };

            controller.updateUserConsentStatus(req, res);

        });


        

        it("updateUserConsentStatus | Invalid preferenceValue = 2", () => {

            let req = {

                header : (headerValue) => {

                    if(headerValue == "X-USER-ID") return 667781;

                },

                body: {

                    'preferenceKey' : 'ocl.user.consent.ru_whatsapp_reminders',

                    'preferenceValue' : 2

                }           

            };

            

            let res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Invalid preferenceValue');                

                    return;

                }

            };

            controller.updateUserConsentStatus(req, res);

        });


        

        it("updateUserConsentStatus | Invalid preferenceKey", () => {

            let req = {

                header : (headerValue) => {

                    if(headerValue == "X-USER-ID") return 667781;

                },

                body: {

                    'preferenceKey' : 'ocl.user.consent.ru_whatsapp_reminders_XXX',

                    'preferenceValue' : 1

                }           

            };

            

            let res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Invalid preferenceKey');                

                    return;

                }

            };

            controller.updateUserConsentStatus(req, res);

        });


        /**

        it("updateUserConsentStatus | success", () => {

            let req = {

                header : (headerValue) => {

                    if(headerValue == "X-USER-ID") return 667781;

                },

                body: {

                    'preferenceKey' : 'ocl.user.consent.ru_whatsapp_reminders',

                    'preferenceValue' : 1

                }           

            };

            let res =  {

                status: (code) => {

                    expect(code).to.be.equal(422);

                    return res;

                },

                json: (result) => {            

                    expect(result.data.statusInfo.status).to.be.equal('FAILURE');                

                    expect(result.data.statusInfo.statusMessage).to.be.equal('Invalid preferenceKey');                

                    return;

                }

            };

            

            controller.updateUserConsentStatus(req, res);

        });

        

        */

    });

    describe("getNonPaytmBills | response validation", function () {    
        let controller;

        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);
                done();
            });
        });
    
        it("getNonPaytmBills | success", () => {
            let req = {
                query: {     
                    "service": "mobile"                                  
                },
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                }
            };
            let stub = sinon.stub(controller.nonPaytmBillsModel, 'readRecentRecords').yields(null, {"test": "data"});
            controller.getNonPaytmBills(req, res);
            stub.restore();
        });

        it("getNonPaytmBills | failure", () => {
            let req = {
                query: {     
                    "service": "mobile"                                  
                },
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                }
            };
            let stub = sinon.stub(controller.nonPaytmBillsModel, 'readRecentRecords').yields('error');
            controller.getNonPaytmBills(req, res);
            stub.restore();
        });
    })

    describe("markAsPaid | response validation", function () {    
        let controller;

        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);
                done();
            });
        });
    
        it("markAsPaid | success | postpaid flow bills_*", () => {
            let req = {    
                    "operator": "bsnl",
                    "paytype": "postpaid",
                    "service": "electricity",
                    "productId": 123456,    
                    "rechargeNumber": "abc",
                    "customerID": 12345,                             
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(202);
                    return res;
                },
                json: ()=>{
                }
            };
            let stub1 = sinon.stub(controller.billSubscriber, 'markAsPaid').resolves({"changedRows": 1});

            controller.markAsPaid(req, res);
            stub1.restore();
        });
        it("markAsPaid | success | prepaid flow plan_validity*", () => {
            let req = {    
                    "operator": "vodafone",
                    "paytype": "prepaid",
                    "service": "mobile",
                    "productId": 123456,    
                    "rechargeNumber": "abc",
                    "customerID": 12345,                             
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(202);
                    return res;
                },
                json: ()=>{
                }
            };
            let stub1 = sinon.stub(controller, 'deleteFromPlanValidityAndNonPaytm').resolves([{"affectedRows": 1}]);

            controller.markAsPaid(req, res);
            stub1.restore();
        });
        it("markAsPaid | success | airtel flow both bills_* plan_validity*", () => {
            let req = {    
                    "operator": "airtel",
                    "paytype": "prepaid",
                    "service": "mobile",
                    "productId": 123456,    
                    "rechargeNumber": "abc",
                    "customerID": 12345,                             
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(202);
                    return res;
                },
                json: ()=>{
                }
            };
            let stub1 = sinon.stub(controller, 'deleteFromPlanValidityAndNonPaytm').resolves([{"affectedRows": 1}]);

            controller.markAsPaid(req, res);
            stub1.restore();
        });
        it("markAsPaid | success | nonRU or nonPaytm flow", () => {
            let req = {    
                    "operator": "airtel",
                    "paytype": "postpaid",
                    "service": "mobile",
                    "productId": 341207918,    
                    "rechargeNumber": "abc",
                    "customerID": 12345,                             
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(202);
                    return res;
                },
                json: ()=>{
                }
            };
            let stub1 = sinon.stub(controller.cassandraBills, 'markAsPaid').resolves([{"affectedRows": 1}]);

            controller.markAsPaid(req, res);
            stub1.restore();
        });
        it("markAsPaid | success | paytm credit card with no referenceID", () => {
            let req = {    
                    "operator": "airtel",
                    "paytype": "credit card",
                    "service": "financial services",
                    "productId": 12345,    
                    "rechargeNumber": "abc",
                    "customerID": 12345,                             
                header : (xUserId) => {
                    if (xUserId)  return "667781"                
                }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(422);
                    return res;
                },
                json: ()=>{
                }
            };
            let stub1 = sinon.stub(controller.cassandraBills, 'markAsPaid').resolves([{"affectedRows": 1}]);

            controller.markAsPaid(req, res);
            stub1.restore();
        });
    })

    describe("updateNonPaytmNotificationStatusV1 | response validation", function () {    
        let controller;

        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                controller = new CONTROLLER(options);
                done();
            });
        });
    
        it("updateNonPaytmNotificationStatusV1 | success | p2p flow", () => {
            let req = {
                "body":{
                    "operator": "bsnl",
                    "paytype": "postpaid",
                    "service": "UPI P2P",
                    "productId": 12345677,    
                    "rechargeNumber": "abc",
                    "notificationStatus": 0
                },    
                    header : (xUserId) => {
                        if (xUserId)  return "12345"                
                    }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(200);
                    return res;
                },
                json: ()=>{
                }
            };
            let cb = sinon.spy();

            let stub1 = sinon.stub(controller.cassandraBills, 'updateNotificationStatusForNonRU').callsFake(function(req,cb){
                return cb(null,{"rows": [{
                    "applied":true
                }]});
            });

            controller.updateNonPaytmNotificationStatusV1(req, res);
            stub1.restore();
        });
        it("updateNonPaytmNotificationStatusV1 | success | p2p flow", () => {
            let req = {
                "body":{
                    "operator": "bsnl",
                    "paytype": "postpaid",
                    "service": "UPI P2P",
                    "productId": 12345677,    
                    "rechargeNumber": "abc",
                    "notificationStatus": 0
                },    
                    header : (xUserId) => {
                        if (xUserId)  return "12345"                
                    }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(202);
                    return res;
                },
                json: ()=>{
                }
            };
            let cb = sinon.spy();

            let stub1 = sinon.stub(controller.cassandraBills, 'updateNotificationStatusForNonRU').callsFake(function(req,cb){
                return cb(null,{"rows": [{
                    "applied":false
                }]});
            });

            controller.updateNonPaytmNotificationStatusV1(req, res);
            stub1.restore();
        });
        it("updateNonPaytmNotificationStatusV1 | success | p2p flow 2", () => {
            let req = {
                "body":{
                    "operator": "bsnl",
                    "paytype": "postpaid",
                    "service": "mobile",
                    "productId": 1234,    
                    "rechargeNumber": "abc",
                    "notificationStatus": 0
                },    
                    header : (xUserId) => {
                        if (xUserId)  return "12345"                
                    }    
            };
            controller.publishToCT = function(req){};

            let res =  {
                status: (code) => {
                    expect(code).to.be.equal(500);
                    return res;
                },
                json: ()=>{
                }
            };
            let stub1 = sinon.stub(controller.nonPaytmBillsModel, 'updateNonPaytmRecords').callsFake(function(req,cb){
                return cb(null,{"rows": [{
                    "applied":false
                }]});
            });

            controller.updateNonPaytmNotificationStatusV1(req, res);
            stub1.restore();
        });

        it("should generate and update recharge number for loan service", async () => {
            let req = {
                body: {
                    operator: "dmi finance",
                    rechargeNumber: "220724683",
                    notificationStatus: 0,
                    productId: "1234604873",
                    service: "loan"
                },
                header: (key) => {
                    if(key === 'X-USER-ID') return "220724683";
                }
            };
            let res = {
                status: (code) => {
                    expect(code).to.equal(200);
                    return res;
                },
                json: (result) => {
                    expect(result.message).to.equal("Notification status updated successfully");
                    return res;
                }
            };
            const updateRNtoDummyStub = sinon.stub(controller.loanUtil, 'updateRNtoDummy');
            controller.updateNonPaytmNotificationStatusV1(req, res);

            // expect(updateRNtoDummyStub).to.have.been.calledOnce;
            
            updateRNtoDummyStub.restore();
        });
    });


    
// describe("API: /v1/deleteRecord: Validation failure errors", function () {
//     let billsSubscriberObj;
//     let controller;
//     let res =  {
//         status: (code) => {
//             expect(code).to.be.equal(422);
//             return res;
//         },
//         json: (result) => {
//             expect(result.code).to.be.equal(422);
//             return;
//         }
//     };
//     before(function () { 
//         controller = new CONTROLLER({ L, config: config, LOCALISATION_CLIENT });
//         billsSubscriberObj = new BILLSUBSCRIBER({L, config:config,LOCALISATION_CLIENT});
//     });


//     beforeEach(function () {
         
//     });


//     afterEach(function () {
       
//     });

//     it("recharge_number Not Present in req.body!", () => {
//         let req = {
//             body: {
//                 "customer_id": 1,
//                 "operator": "paytmfirstcc",
//                 "reference_id": "abc12er4512er5667",
//                 "service": "credit card"
//             }
//         };
//         controller.deleteRecord(req, res);
//     });

//     it("customer_id Not Present in req.body!", () => {
//         let req = {
//             body: {
//                 "operator": "paytmfirstcc",
//                 "reference_id": "abc12er4512er5667",
//                 "service": "credit card",
//                 "recharge_number": "1000 XXXXX XXXX 1212"
//             }
//         };
//         controller.deleteRecord(req, res);
//     });

//     it("operator Not Present in req.body!", () => {
//         let req = {
//             body: {
//                 "customer_id": 1,
//                 "recharge_number": "1000 XXXXX XXXX 1212",
//                 "reference_id": "abc12er4512er5667",
//                 "service": "credit card"
//             }
//         };
//         controller.deleteRecord(req, res);
//     });

//     it("service Not Present in req.body!", () => {
//         let req = {
//             body: {
//                 "customer_id": 1,
//                 "operator": "paytmfirstcc",
//                 "reference_id": "abc12er4512er5667",
//                 "recharge_number": "1000 XXXXX XXXX 1212",
//             }
//         };
//         controller.deleteRecord(req, res);
//     });
 
//     // it("Operator not migrated!", () => {
//     //     let req = {
//     //         body: {
//     //             "customer_id": 1,
//     //             "operator": "paytmfirst",
//     //             "reference_id": "abc12er4512er5667",
//     //             "recharge_number": "1000 XXXXX XXXX 1212",
//     //             "service": "credit card"
//     //         }
//     //     };
//     //     controller.deleteRecord(req, res);
//     // });

//     it("reference_id not present for credit card operator!", () => {
//         let req = {
//             body: {
//                 "customer_id": 1,
//                 "operator": "paytmfirstcc",
//                 "recharge_number": "1000 XXXXX XXXX 1212",
//                 "service": "credit card"
//             }
//         };
//         controller.deleteRecord(req, res);
//     });

// });



// describe("API: /v1/deleteRecord: Validation Success: PaytmfirstCC", function () {
//     let billsSubscriberObj;
//     let controller;
//     let res = {
//         status: (code) => {
//             expect(code).to.be.equal(200);
//             return res;
//         },
//         json: (result) => {
//             expect(result.code).to.be.equal(200);
//             return;
//         }
//     };
//     before(function () {
//         controller = new CONTROLLER({ L, config: config, LOCALISATION_CLIENT });
//         billsSubscriberObj = new BILLSUBSCRIBER({ L, config: config, LOCALISATION_CLIENT });
//         billsSubscriberObj.bills = {
//             getRecordForDeletetion: (params, cb) => {
//                 expect(params).to.include.all.keys('recharge_number', 'customer_id','reference_id','service');
//                 expect(params.destTable).to.be.equal('bills_archive');
//                 expect(params.isCreditCardOperator).to.be.equal(true);
//                 expect(params.tableName).to.be.equal('bills_paytmfirstcc');
//                 return cb(null,[{ id: 1 }]);
//             },
//             removeRecord: (cb,table,id) => {
//                 expect(id.id).to.be.equal(1);
//                 cb(null);
//             }
//         };
//         controller.billSubscriber = billsSubscriberObj;
//         controller.bills = {
//             insertRecordInArchive: (cb, table, data) => {
//                 expect(table).to.be.equal('bills_archive');
//                 expect(data.id).to.be.equal(1);
//                 return cb(null, null);
//             }
//         };
//     });



//     it("Request contains all parameters!", () => {
//         let req = {
//             body: {
//                 "customer_id": 1,
//                 "operator": "paytmfirstcc",
//                 "recharge_number": "1000 XXXXX XXXX 1212",
//                 "reference_id": "abc12er4512er5667",
//                 "service": "credit card"
//             }
//         };
//         controller.deleteRecord(req, res);
//     });

// });

// describe("API: /v1/deleteRecord: Validation Success: Vodafone idea", function () {
//     let billsSubscriberObj;
//     let controller;
//     let res = {
//         status: (code) => {
//             expect(code).to.be.equal(200);
//             return res;
//         },
//         json: (result) => {
//             expect(result.code).to.be.equal(200);
//             return;
//         }
//     };
//     before(function () {
//         controller = new CONTROLLER({ L, config: config, LOCALISATION_CLIENT });
//         billsSubscriberObj = new BILLSUBSCRIBER({ L, config: config, LOCALISATION_CLIENT });
//         billsSubscriberObj.bills = {
//             getRecordForDeletetion: (params, cb) => {
//                 expect(params).to.include.all.keys('recharge_number', 'customer_id','service');
//                 expect(params.destTable).to.be.equal('bills_archive');
//                 expect(params.isCreditCardOperator).to.be.equal(false);
//                 expect(params.tableName).to.be.equal('bills_vodafone');
//                 return cb(null,[{ id: 1 }]);
//             },
//             removeRecord: (cb,table,id) => {
//                 expect(id.id).to.be.equal(1);
//                 cb(null);
//             }
//         };
//         controller.billSubscriber = billsSubscriberObj;
//         controller.bills = {
//             insertRecordInArchive: (cb, table, data) => {
//                 expect(table).to.be.equal('bills_archive');
//                 expect(data.id).to.be.equal(1);
//                 return cb(null, null);
//             }
//         };
//     });



//     it("Request contains all parameters!", () => {
//         let req = {
//             body: {
//                 "customer_id": 1,
//                 "operator": "vodafone idea",
//                 "recharge_number": "9866123727",
//                 "service": "mobile"
//             }
//         };
//         controller.deleteRecord(req, res);
//     });

// });

// describe("API - Notfication callback", () => {
//     let controller;
//     let notificationObj;
    
//     before(() => {
//         // sinon.stub(NOTIFICATION.prototype, 'updateNotification').resolves(null, 0);
//         controller = new CONTROLLER({ L, config: config, LOCALISATION_CLIENT }); 
//         notificationObj = new NOTIFICATION({ L, config: config, LOCALISATION_CLIENT });
        
//         notificationObj.updateNotification = (cb, fields, whereCondition, value) => {
//             expect(whereCondition).to.be.equal('job_id = ?');
//             expect(fields).to.eql(['status']);
//             expect(value).to.eql([5, '01d6fa36-4cbd-4ff3-84a1-8aee6e91b034']);
//             return cb(null,[{ id: 1 }]);
//         }
//         controller.notification = notificationObj
//     });

//     afterEach(() => {
//         // NOTIFICATION.prototype.updateNotification.restore();
//     })

//     it("Request parses data correctly", async () => {
//         const params = mockNotificationCallbackdata();
//         const req = {
//             body: params
//         };

//         let res = {
//             status: (code) => {
//                 expect(code).to.be.equal(200);
//                 return res;
//             },
//             json: (result) => {
//                 expect(200).to.be.equal(200);
//                 return;
//             }
//         };

//         await controller.notificationCallback(req, res);
        
//         // sinon.assert.calledOnce(NOTIFICATION.prototype.updateNotification);
//     })
// })