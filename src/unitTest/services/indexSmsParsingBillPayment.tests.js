/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    import utility from '../../lib/index'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import smsParsingBillPayment from '../../services/smsParsingBillPayment/ccindex';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new smsParsingBillPayment(options);
                done();
            });
        });

        // it("testing configureKafka function | Error case from initProducer function",()=>{
        //     serviceObj.producer = new serviceObj.infraUtils.kafka.producer();
        //     let stub1 = sinon.stub(serviceObj.producer, "initProducer").callsFake(function fakeFn(callback){
        //         return callback("Error from initProducer");
        //     });
        //     serviceObj.configureKafka((error)=>{
        //         if (error) {
        //             expect(error).to.be.equal("Error from initProducer");
        //         }
        //     });
        //     stub1.restore();
        // });
        it("testing configureKafka function | Error case from initConsumer function",()=>{
            serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback("Error from initConsumer");
            });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal("Error from initConsumer");
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            //serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
            //     return callback(null);
            // });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            // serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").returns(null);
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
    
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
                }
            };
                let resolveOffset = sinon.stub();
                let topic = "dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT";
                let partition = 0;
            serviceObj.execSteps(record, resolveOffset , topic , partition , (err)=>{
                expect(err).to.be.equal(undefined);
            });
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
    
        it("start function || configureKafka error case", () => {
            let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
            sinon.stub(process, 'exit');
            let stub2 = sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback("configureKafka error response callback");
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            process.exit.restore();
            stub1.restore();
            stub2.restore();
        })
        it("start function || Error in configureKafka", () => {
            let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
            serviceObj.configureKafka = (callback)=>{
                return callback("Error in configureKafka");
            }

            sinon.stub(process, 'exit');     
            let clock = sinon.useFakeTimers();
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);            
                }
            });
            expect(stub1).to.have.callCount(1);
            clock.tick(86400000) 
            process.exit.restore();
            stub1.restore();
        })
        it("start function || configureKafka success case", () => {
            let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
            let stub2 =sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback();
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })

    
    });

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
         let record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                serviceObj = new smsParsingBillPayment(options);
                done();
            });
        });

        it("testing processBatch | execution count", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                },{
                    "topic":"dwh-ingest-SMS_PARSING_LENDING",
                    "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').callsFake(function fakeFn(callback){
                setTimeout(() => {
                    callback();
                }, 1)
            });
            // let stub2 = sinon.spy(serviceObj, 'processData');
            serviceObj.processBatch(data, function(error){
                if(error){
                    console.log("what is the eror", error);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processBatch | correct payload", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | correct payload processData rejects", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').rejects();
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | incorrect payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("testing processBatch | empty payload", () => {
            let data =[];  
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });

        it("testing processData | correct payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'processRecords').returns(null);
            serviceObj.processData(data,function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processData | incorrect payload", () => {
            let data ={}   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'processRecords').returns(null);
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("testing processData | correct payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"record\":[{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}]}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'processRecords').returns(null);
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("testing processData | incoorect topic ", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":\"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT\"}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'executeStrategy').returns(null);
            let stub2 = sinon.stub(serviceObj, 'defaultStrategy').returns(null);
            serviceObj.processData(data,function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0)
            stub1.restore();
            stub2.restore();
        });
        it("testing processData | correct payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'executeStrategy').callsFake(function fakeFn(callback){
                return callback(null);
            });
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processData | inccorrect payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"dwh-ingest\"]}"
                }    
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'executeStrategy').callsFake(function fakeFn(callback){
                return callback(null);
            });
            let stub2 = sinon.stub(serviceObj, 'defaultStrategy').callsFake(function fakeFn(callback){
                return callback(null);
            });
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        });

        it("testing defaultStrategy | correct payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}]}"
                }   
            let result = serviceObj.defaultStrategy(function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            },data);
            expect(result).to.equal(undefined);
        });

    
    });