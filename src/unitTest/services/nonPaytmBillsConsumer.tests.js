/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach } from 'mocha';
    
    import chai, { assert } from "chai";
    import sinon from 'sinon';
    import MOMENT from 'moment';
    import sinonChai from "sinon-chai";
    import _ from 'lodash';
    
    import chaiAsPromised from "chai-as-promised";
    import NonPaytmBills from '../../services/nonPaytmBillsConsumer';
    
    import STARTUP_MOCK from '../__mocks__/startUp'
import NonPaytmBillsModel from '../../models/nonPaytmBills';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let data = {
        "dbEvent": "upsert",
        "customerId": ********,
        "rechargeNumber": "6666 77XX XXXX 5165",
        "productId": "**********",
        "operator": "visa_hdfcbank",
        "amount": 2222,
        "bill_fetch_date": "2022-02-20T12:31:12.000Z",
        "paytype": "postpaid",
        "service": "financial services",
        "circle": "",
        "customer_mobile": "**********",
        "customer_email": "<EMAIL>",
        "status": 7,
        "userData": "",
        "createdAt": "2018-11-20T11:16:28.000Z",
        "updatedAt": "2018-11-28T06:14:02.000Z",
        "billDate": "2022-02-20T12:31:12.000Z",
        "extra": null,
        "notificationStatus": 1,
        "dueDate": "2022-03-20T18:30:00.000Z",
        "customerOtherInfo": null,
        "cardNetwork": "visa",
        "bankName": "HDFC",
        "partialBillState": "Expired"
      }
        
    
    describe("nonPaytmBillsConsumer | Kafka consumer validations", () => {
        let serviceObj;
    
        beforeEach((done) => {
            STARTUP_MOCK.init((error, options) => {
                serviceObj = new NonPaytmBills(options)
                // clock.tick(40 * 60 * 1000)
                done();
            })
        });
    
        it("setVarFromDynamicConfig funcation ", () => {
            let stub2 = sinon.stub(serviceObj, 'setVarFromDynamicConfig');
            serviceObj.setVarFromDynamicConfig();
            expect(stub2).to.have.callCount(1);
        })
    
        it("setVarFromDynamicConfig funcation ", () => {
            let clock = sinon.useFakeTimers();
            serviceObj.greyScaleEnv = true;
            serviceObj.setVarFromDynamicConfig();
            clock.tick(40 * 60 * 1000)
        })

        it("setVarFromDynamicConfig function : update allowed operators ", () => {
            _.set(serviceObj.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATORS'],['airtel'])
            serviceObj.setVarFromDynamicConfig()
            assert.deepEqual(serviceObj.allowed_operators, ['airtel'] )
        })
    
        it("testing configureKafka function | Error case from initConsumer function",()=>{
            serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback("Error from initConsumer");
            });
            serviceObj._initializeKafkaConsumer((error)=>{
                if (error) {
                    expect(error).to.be.equal("Error from initConsumer");
                }
            });
        });
    
        it("testing configureKafka function | success ",()=>{
            serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback(null);
            });
            serviceObj._initializeKafkaConsumer((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
        });
    
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            let resolveOffset = sinon.stub();
            let topic = "topic";
            let partition = 0;

            serviceObj._processKafkaData(record,resolveOffset , topic , partition,(error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
                expect(error).to.be.equal(undefined);
            });
        })

        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "_processBillsData").resolves();
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj._processKafkaData(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            process.exit.restore();
        })

        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "_processBillsData").resolves();
            let clock = sinon.useFakeTimers();
    
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj._processKafkaData(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
        })
    
        it("start function || configureKafka error case", () => {
            let stub2 = sinon.stub(serviceObj, '_processKafkaData').yields(null);
            sinon.stub(process, 'exit');
            sinon.stub(serviceObj, "_initializeKafkaConsumer").callsFake(function fakeFn(callback){
                return callback("configureKafka error response callback");
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
            expect(stub2).to.have.callCount(0);
            process.exit.restore();
        })
    
        it("start function || Error in configureKafka", () => {
            let stub2 = sinon.stub(serviceObj, '_processKafkaData').yields(null);
            serviceObj._initializeKafkaConsumer = (callback)=>{
                return callback("Error in configureKafka");
            }

            sinon.stub(process, 'exit');     
            let clock = sinon.useFakeTimers();
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);            
                }
            });
            expect(stub2).to.have.callCount(0);
            clock.tick(86400000) 
            process.exit.restore();
        })
    })
    
    describe("Module: nonPaytmBillsConsumer::", function () {
        let serviceObj;
    
        beforeEach(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
                serviceObj = new NonPaytmBills(options);
                done();
            });               
        });
    
        it.skip("_processBillsData | upsert validation", (done) => {
            let stub = sinon.stub(serviceObj, 'upsertData').yields(null);
            let stub2 = sinon.stub(serviceObj, 'publishInBillFetchKafkaRealtime').callsFake(function(cb){
                return cb(null);
            })
            serviceObj._processBillsData(data, (error) =>{
                expect(stub).to.have.callCount(1);
                stub2.restore();
                done();
            })
        })

        it("_processBillsData | delete validation", (done) => {
            let stub = sinon.stub(serviceObj, 'deleteData').yields(null);
            let p = _.clone(data);
            p.dbEvent = 'delete'
            serviceObj._processBillsData(p, (error) =>{
                expect(stub).to.have.callCount(1);
                stub.restore();
                done();
            })
        })

        it("deleteData | delete validation 2", (done) => {
            let stub1 = sinon.stub(serviceObj, 'deleteDataExtended').yields(null);
            let p = _.clone(data);
            p.service = 'electricity';
            p.status = 4;
            p.operator = 'paschimanchal vidyut vitran nigam limited (pvvnl)';
            _.set(serviceObj.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', p.operator, 'DEMERGER_MAPPED_OPERATOR'], 'paschimanchal vidyut vitran nigam limited (pvvnl)');
            p.dbEvent = 'delete'
            serviceObj.deleteData((error) => {
                expect(stub1).to.have.callCount(2);
                stub1.restore();
                done();
            }, p)
        })

        it("_processBillsData | findAndUpdateData validation", (done) => {
            let p = _.clone(data);
            let stub = sinon.stub(serviceObj, 'findAndUpdateData').yields(p);
            p.dbEvent = 'findAndUpdateData'
            serviceObj._processBillsData(p, (error) =>{
                expect(stub).to.have.callCount(1);
                done();
            })
        })

        it("validateKafkaRecord | correct values", () => {
            let response = serviceObj.validateKafkaRecord(data);
            expect(response).to.be.equal('')
        })

        // it("validateKafkaRecord | missing rechargeNumber", () => {
        //     let p = _.clone(data);
        //     delete p.rechargeNumber;
        //     let response = serviceObj.validateKafkaRecord(p);
        //     expect(response).to.be.equal('Mandatory fields not present:: rech_num:undefined::operator:visa_hdfcbank::productId:**********::custId:******** Missing params:: rechargeNumber')
        // })

        // it("validateKafkaRecord | missing operator", () => {
        //     let p = _.clone(data);
        //     delete p.operator;
        //     let response = serviceObj.validateKafkaRecord(p);
        //     expect(response).to.be.equal('Mandatory fields not present:: rech_num:6666 77XX XXXX 5165::operator:undefined::productId:**********::custId:******** Missing params:: operator')
        // })

        // it("validateKafkaRecord | missing customerId", () => {
        //     let p = _.clone(data);
        //     delete p.customerId;
        //     let response = serviceObj.validateKafkaRecord(p);
        //     expect(response).to.be.equal('Mandatory fields not present:: rech_num:6666 77XX XXXX 5165::operator:visa_hdfcbank::productId:**********::custId:undefined Missing params:: customerId')
        // })

        // it("validateKafkaRecord | missing dbEvent", () => {
        //     let p = _.clone(data);
        //     delete p.dbEvent;
        //     let response = serviceObj.validateKafkaRecord(p);
        //     expect(response).to.be.equal('Mandatory fields not present:: rech_num:6666 77XX XXXX 5165::operator:visa_hdfcbank::productId:**********::custId:******** Missing params:: dbEvent')
        // })

        // it("validateKafkaRecord | missing service", () => {
        //     let p = _.clone(data);
        //     delete p.service;
        //     let response = serviceObj.validateKafkaRecord(p);
        //     expect(response).to.be.equal('Mandatory fields not present:: rech_num:6666 77XX XXXX 5165::operator:visa_hdfcbank::productId:**********::custId:******** Missing params:: service')
        // })

        // it("validateKafkaRecord | missing paytype", () => {
        //     let p = _.clone(data);
        //     delete p.paytype;
        //     let response = serviceObj.validateKafkaRecord(p);
        //     expect(response).to.be.equal('Mandatory fields not present:: rech_num:6666 77XX XXXX 5165::operator:visa_hdfcbank::productId:**********::custId:******** Missing params:: paytype')
        // })

        it("validateExistingRecord | amount and date null in db", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-05-20T18:30:00.000Z",
                "extra": "{}",
                "notification_status": 1,
                "payment_date": null,
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
               let error = serviceObj.validateExistingRecord(billsKafkaRow, existingRecord);
               expect(error).to.be.equal(null);
           })
           it("validateExistingRecord | amount null and paymentdate in grace", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-05-20T18:30:00.000Z",
                "extra": "{}",
                "notification_status": 1,
                "payment_date": "2022-05-25T08:14:33.487Z",
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
               let error = serviceObj.validateExistingRecord(billsKafkaRow, existingRecord);
               expect(error).to.be.equal(null);
           })
           it("validateExistingRecord | amount in grace and paymentdate null", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-05-20T18:30:00.000Z",
                "extra": "{\"last_paid_amount\":9999}",
                "notification_status": 1,
                "payment_date": null,
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
               let error = serviceObj.validateExistingRecord(billsKafkaRow, existingRecord);
               expect(error).to.be.equal(null);
           })
           it("validateExistingRecord | amount in grace and paymentdate in grace", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-05-20T18:30:00.000Z",
                "extra": "{\"last_paid_amount\":9999}",
                "notification_status": 1,
                "payment_date": "2022-05-25T08:14:33.487Z",
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
               let error = serviceObj.validateExistingRecord(billsKafkaRow, existingRecord);
               expect(error).to.be.equal("Bill already paid in last x days or Bill has not generated yet");
           })
           it("validateExistingRecord | duedate correct", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-05-20T18:30:00.000Z",
                "extra": "{\"last_paid_amount\":999}",
                "notification_status": 1,
                "payment_date": "2022-05-01T08:14:33.487Z",
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
               let error = serviceObj.validateExistingRecord(billsKafkaRow, existingRecord);
               expect(error).to.be.equal(null);
           })
           it("validateExistingRecord | duedate older", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-04-25T18:30:00.000Z",
                "extra": "{\"last_paid_amount\":999}",
                "notification_status": 1,
                "payment_date": "2022-02-25T08:14:33.487Z",
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
               let error = serviceObj.validateExistingRecord(billsKafkaRow, existingRecord);
               expect(error).to.be.equal("Bill already paid in last x days or Bill has not generated yet");
           })
           it("getDataToUpdate | correct record", () => {
            let billsKafkaRow = {
                "customerId":*********,
                "rechargeNumber":"XXXX XXXX XXXX 2003",
                "productId":**********,
                "operator":"neft_hdfc bank",
                "amount":10000,
                "customerOtherInfo":"{\"customerId\":*********,\"lastCC\":\"2003\",\"currentPaidAmount\":10000,\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"smsSenderID\":\"VM-KOTAKB\",\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\"}",
                "paytype":"credit card",
                "service":"financial services",
                "customerMobile":null,
                "customerEmail":null,
                "notificationStatus":1,
                "bankName":"KOTAK",
                "cardNetwork":"dummyNetwork",
                "status":14,
                "dbEvent":"findAndUpdateData"
             }
            let existingRecord = {
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-04-25T18:30:00.000Z",
                "extra": "{\"last_paid_amount\":999}",
                "notification_status": 1,
                "payment_date": "2022-02-25T08:14:33.487Z",
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }
            // let existingRecord = {
            //     "recharge_number": "XXXX XXXX XXXX 2003",
            //     "customer_id": *********,
            //     "service": "financial services",
            //     "operator": "hdfc",
            //     "amount": -9977,
            //     "bank_name": null,
            //     "bill_date": "2022-02-20T12:31:12.000Z",
            //     "bill_fetch_date": null,
            //     "card_network": "dummyNetwork",
            //     "circle": "",
            //     "customer_email": null,
            //     "customer_mobile": null,
            //     "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
            //     "due_date": "2022-04-25T18:30:00.000Z",
            //     "extra": "\"\\\"\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\"\\\"\"",
            //     "notification_status": 1,
            //     "payment_date": "2022-02-25T08:14:33.487Z",
            //     "paytype": "credit card",
            //     "product_id": **********,
            //     "status": 14,
            //     "user_data": null
            // }
           // "\"\\\"\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"{}\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\"\\\"\""
            
               let updatedRecord = serviceObj.getDataToUpdate(billsKafkaRow, existingRecord);
               console.log("updatedrecord", updatedRecord);
               expect(updatedRecord.amount).to.be.equal(-19977);
               expect(updatedRecord.payment_date).to.be.equal('2022-05-30T08:14:33.487Z');
               expect(JSON.parse(updatedRecord.extra).last_paid_amount).to.be.equal(10000);
           })

           it("_processBillsData | findAndCreate validation", (done) => {
                let p = _.clone(data);
                let stub = sinon.stub(serviceObj, 'findAndCreateData').yields(p);
                p.dbEvent = 'findAndCreate'
                serviceObj._processBillsData(p, (error) =>{
                    expect(stub).to.have.callCount(1);
                    stub.restore();
                    done();
                })
            })
    });

    describe('findAndCreateData', () => {
        let serviceObj, nonPaytmBillsModel;
    
        beforeEach(function (done) {
            STARTUP_MOCK.init(function (error, options) {
                serviceObj = new NonPaytmBills(options);
                nonPaytmBillsModel = new NonPaytmBillsModel(options)
                done();
            });
        });
    
        it('should return "data is present in the DB" if record exists', async () => {
            const mockReadBills = sinon.stub().resolves([{
                "recharge_number": "XXXX XXXX XXXX 2003",
                "customer_id": *********,
                "service": "financial services",
                "operator": "hdfc",
                "amount": -9977,
                "bank_name": null,
                "bill_date": "2022-02-20T12:31:12.000Z",
                "bill_fetch_date": null,
                "card_network": "dummyNetwork",
                "circle": "",
                "customer_email": null,
                "customer_mobile": null,
                "customer_other_info": "{\"customerId\":*********,\"lastCC\":\"2003\",\"smsSenderID\":null,\"bankName\":\"KOTAK\",\"debugKey\":\"smsSenderID:VM-KOTAKB_custId:*********_lastCC:2003\",\"paymentDate\":\"2022-05-30T08:14:33.487Z\",\"currentBillAmount\":-9977,\"currentMinBillAmount\":null}",
                "due_date": "2022-05-20T18:30:00.000Z",
                "extra": "{\"last_paid_amount\":9999}",
                "notification_status": 1,
                "payment_date": null,
                "paytype": "credit card",
                "product_id": **********,
                "status": 14,
                "user_data": null
            }]);
    
            serviceObj.nonPaytmBillsModel.readBills = mockReadBills;
            const cb = sinon.spy();
            await serviceObj.findAndCreateData(cb, data); // Provide a sample data for this test case
    
            expect(cb.calledWithExactly('data is present in the DB')).to.be.true;
        });
    
        it('should return billsKafkaRow after creating a new record', async () => {
            const mockReadBills = sinon.stub().resolves([]); // Simulate no existing record
            const mockWriteBatchRecords = sinon.stub().resolves({}); // Simulate successful write
            const mockPublishCassandraCDCvents = sinon.stub();
    
            serviceObj.nonPaytmBillsModel.readBills = mockReadBills;
            serviceObj.nonPaytmBillsModel.writeBatchRecords = mockWriteBatchRecords;
            serviceObj.publishCassandraCDCvents = mockPublishCassandraCDCvents;
    
            const cb = sinon.spy();
            await serviceObj.findAndCreateData(cb, data); // Provide a sample data for this test case
            
            expect(mockWriteBatchRecords.calledOnce).to.be.true;
        });
    
        it('should call cb with error if writeBatchRecords fails', async () => {
            const mockReadBills = sinon.stub().resolves([]);
            const mockWriteBatchRecords = sinon.stub().rejects(new Error('Write failed'));
    
            serviceObj.nonPaytmBillsModel.readBills = mockReadBills;
            serviceObj.nonPaytmBillsModel.writeBatchRecords = mockWriteBatchRecords;
    
            const cb = sinon.spy();
            await serviceObj.findAndCreateData(cb, data); // Provide a sample data for this test case
    
            expect(mockWriteBatchRecords.calledOnce).to.be.true;
        });
    });
    
  
    describe("Module:: nonPaytmBillsConsumer :: CT tests", function () {
        let serviceObj;
        let payLoad = {
            value: JSON.stringify(data)
        }
    
    
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                // billsSubscriberObj = new EmiDueCommonConsumer(options);
                serviceObj = new NonPaytmBills(options);
                done();
            });
        });
    
        it("CT tests || ensure publishCtAndPFCCEvents is skipped if skipNotification is true", (done) => {
    
            let vs = _.cloneDeep(serviceObj);
            vs.publishCtAndPFCCEvents = sinon.spy()
            let stub = sinon.stub(vs, 'validateKafkaRecord').returns('error')
            vs._processBillsData(payLoad, (err, result) => {
                expect(vs.publishCtAndPFCCEvents).to.have.callCount(0)
                stub.restore();
                return done();
            });
        })    
    
        it("publishCtAndPFCCEvents function | check function calls", (done) => { 
            let tempRecord = _.cloneDeep(data)
            tempRecord.dbData = data
    
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, [])
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
    
            serviceObj.publishCtAndPFCCEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(1)
            expect(cb).to.have.been.calledWith(null)
            return done();
        })
    
        it("publishCtAndPFCCEvents function | no retailerStatus", (done) => {
            let tempRecord = _.cloneDeep(data)
            tempRecord.dbData = data
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, data)
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
    
            serviceObj.publishCtAndPFCCEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(0)
            expect(stub3).to.have.callCount(0)
            return done();
        })
    
        it("publishCtAndPFCCEvents function | no thumbnail", (done) => {
            let tempRecord = _.cloneDeep(data)
            tempRecord.dbData = data
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, data)
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
    
            serviceObj.publishCtAndPFCCEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            return done();
        })
    
        it("publishCtAndPFCCEvents function |  publishData", (done) => {
            let tempRecord = _.cloneDeep(data)
            tempRecord.dbData = data
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : ([], callback)=>{
                    return callback(null);
                }
            };
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
            let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, "publishData").callsFake(function fakeFn([], callback){
                return callback(null);
            });   
    
            serviceObj.publishCtAndPFCCEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(1)
            expect(stub4).to.have.callCount(1)
            return done();
        })
    
        it("publishCtAndPFCCEvents function |  publishData error case", (done) => {
            let tempRecord = _.cloneDeep(data)
            tempRecord.dbData = data
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : ([], callback)=>{
                    return callback(null);
                }
            };
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
            let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, "publishData").callsFake(function fakeFn([], callback){
                return callback("Error in publishData function ");
            });   
    
            serviceObj.publishCtAndPFCCEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(1)
            expect(stub4).to.have.callCount(1)
            return done();
        })
    
    
        it("publishCtAndPFCCEvents function |  notification_status is 0 ", (done)=>{
            let tempRecord = _.cloneDeep(data)
            tempRecord.notificationStatus = 0;
    
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, [])
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
    
            serviceObj.publishCtAndPFCCEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(1)
            expect(cb).to.have.been.calledWith(null)
            return done();
    
        })
    });

    describe('publishToUPMSRegistration', () => {
        let serviceObj;

        before(function () {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new NonPaytmBills(options);
                done();
            });
        });

        it('should push to registration when non-ambiguous and service is registered', async () => {
            const billsKafkaRow = { service: 'service1', operator: 'operator1', source: 'source1' };
            serviceObj.billPushServiceRegistration = ['service1'];
            let stub1 = sinon.stub(serviceObj, 'getExtra').returns({});
            let stub2 = sinon.stub(serviceObj.L, 'log');

            await serviceObj.publishToUPMSRegistration(billsKafkaRow);

            sinon.assert.calledWith(stub2, "Going to push data for billPush Registration as it is confirmed nonRU and non ambiguous");
            stub1.restore();
            stub2.restore();
        });

        it('should push to registration when non-ambiguous and operator is allowed', async () => {
            const billsKafkaRow = { service: 'service2', operator: 'operator2', source: 'source2' };
            serviceObj.billPushOperatorRegistrationAllowed = ['operator2'];
            let stub1 = sinon.stub(serviceObj, 'getExtra').returns({});
            let stub2 = sinon.stub(serviceObj.L, 'log');

            await serviceObj.publishToUPMSRegistration(billsKafkaRow);

            sinon.assert.calledWith(stub2, "Going to push data for billPush Registration as it is confirmed nonRU and non ambiguous");
            stub1.restore();
            stub2.restore();
        });

        it('should not push to registration and send metrics when ambiguous', async () => {
            const billsKafkaRow = { service: 'service1', operator: 'operator1', source: 'source1' };
            let stub1 = sinon.stub(serviceObj, 'getExtra').returns({ ambiguous: { board : true} });
            let stub2 = sinon.stub(serviceObj.L, 'log');

            await serviceObj.publishToUPMSRegistration(billsKafkaRow);
            sinon.assert.calledWith(stub2, "Skipping billPush Registration as it is ambiguous or service/operator is not allowed");

            stub1.restore();
            stub2.restore();
        });

        it('should not push to registration and send metrics when service and operator are not registered', async () => {
            const billsKafkaRow = { service: 'service3', operator: 'operator3', source: 'source3' };
            let stub1 = sinon.stub(serviceObj, 'getExtra').returns({ ambiguous: { board : true} });
            let stub2 = sinon.stub(serviceObj.L, 'log');

            await serviceObj.publishToUPMSRegistration(billsKafkaRow);
            sinon.assert.calledWith(stub2, "Skipping billPush Registration as it is ambiguous or service/operator is not allowed");

            stub1.restore();
            stub2.restore();
        });

    });

    describe('checkExistingDueDateRangeForIsValidityExpired', () => {
        let serviceObj;
    
        before(function () {
            STARTUP_MOCK.init(function (error, options) {
                serviceObj = new NonPaytmBills(options);
            });
        });
    
        it('should send metrics and invoke callback when due date is in range', () => {
            const billsKafkaRow = {
                isValaidationSync: true,
                isValidityExpired: true,
                source: 'source1',
                service: 'service1',
                operator: 'operator1',
            };
    
            const existingDueDate = MOMENT().utc().startOf('day');
            serviceObj.DUE_DATE_RANGE_VALIDITY_EXPIRED = 7;
    
            const cb = sinon.stub();
    
            serviceObj.checkExistingDueDateRangeForIsValidityExpired(billsKafkaRow, existingDueDate, cb);
    
            sinon.assert.calledWith(
                cb,
                `The existing due date is within the range of lower_bound - 3 and upper_bound - 3 days.`
            );
    
        });
    
        it('should not send metrics or invoke callback when isValaidationSync is false', () => {
            const billsKafkaRow = {
                isValaidationSync: false,
                isValidityExpired: true,
            };
    
            const existingDueDate = MOMENT().utc().startOf('day');
            serviceObj.DUE_DATE_RANGE_VALIDITY_EXPIRED = 7;
    
            const cb = sinon.stub();
    
            serviceObj.checkExistingDueDateRangeForIsValidityExpired(billsKafkaRow, existingDueDate, cb);
            sinon.assert.notCalled(cb);
        });
    
        it('should not send metrics or invoke callback when isValidityExpired is false', () => {
            const billsKafkaRow = {
                isValaidationSync: true,
                isValidityExpired: false,
            };
    
            const existingDueDate = MOMENT().utc().startOf('day');
            serviceObj.DUE_DATE_RANGE_VALIDITY_EXPIRED = 7;
    
            const cb = sinon.stub();
    
            serviceObj.checkExistingDueDateRangeForIsValidityExpired(billsKafkaRow, existingDueDate, cb);
            sinon.assert.notCalled(cb);
        });
    
        it('should not send metrics or invoke callback when due date is out of range', () => {
            const billsKafkaRow = {
                isValaidationSync: true,
                isValidityExpired: true,
            };
    
            const existingDueDate = MOMENT().utc().startOf('day').subtract(10, 'days');
            serviceObj.DUE_DATE_RANGE_VALIDITY_EXPIRED = 7;
    
            const cb = sinon.stub();
    
            serviceObj.checkExistingDueDateRangeForIsValidityExpired(billsKafkaRow, existingDueDate, cb);
            sinon.assert.notCalled(cb);
            });
    });
    
    describe("Module: nonPaytmBillsConsumer:: upsertData smartFetchFlow", function () {
        let serviceObj;

        beforeEach(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new NonPaytmBills(options);
                serviceObj.nonPaytmBillsModel = {
                    readBills: sinon.stub(),
                    writeBatchRecords: sinon.stub().resolves()
                };
                done();
            });
        });

        it("should skip inserting when is_active_expired_user is true and NBFD is in future", (done) => {
            const billsKafkaRow = {
                is_active_expired_user: true,
                rechargeNumber: "test",
                customerId: 123,
                service: "test",
                operator: "test"
            };

            serviceObj.nonPaytmBillsModel.readBills.resolves([{
                extra: JSON.stringify({}),
                next_bill_fetch_date: MOMENT().add(1, 'days').valueOf(),
                notification_status: 1
            }]);

            serviceObj.upsertData((error) => {
                expect(serviceObj.nonPaytmBillsModel.writeBatchRecords.called).to.be.false;
                done();
            }, billsKafkaRow);
        });

        it("should skip inserting when notification_status is 0", (done) => {
            const billsKafkaRow = {
                is_active_expired_user: true,
                rechargeNumber: "test",
                customerId: 123,
                service: "test", 
                operator: "test"
            };

            serviceObj.nonPaytmBillsModel.readBills.resolves([{
                extra: JSON.stringify({}),
                next_bill_fetch_date: MOMENT().subtract(1, 'days').valueOf(),
                notification_status: 0
            }]);

            serviceObj.upsertData((error) => {
                expect(serviceObj.nonPaytmBillsModel.writeBatchRecords.called).to.be.false;
                done();
            }, billsKafkaRow);
        });

        it.skip("should continue processing when is_active_expired_user is true but NBFD is in past", (done) => {
            const billsKafkaRow = {
                is_active_expired_user: true,
                rechargeNumber: "test",
                customerId: 123,
                service: "test",
                operator: "test"
            };

            serviceObj.nonPaytmBillsModel.readBills.resolves([{
                extra: JSON.stringify({}),
                next_bill_fetch_date: MOMENT().subtract(1, 'days').valueOf(),
                notification_status: 1
            }]);

            serviceObj.upsertData((error) => {
                expect(error).to.be.null;
                done();
            }, billsKafkaRow);
        });
    });

    describe("Module: nonPaytmBillsConsumer:: upsertData smartFetchFlow", function () {
        let serviceObj;

        beforeEach(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new NonPaytmBills(options);
                serviceObj.nonPaytmBillsModel = {
                    readBills: sinon.stub(),
                    writeBatchRecords: sinon.stub().resolves(),
                    readBillsWithoutCustId: sinon.stub().resolves([{
                        customer_id: 124,
                        recharge_number: "test",
                        service: "test",
                        operator: "test",
                        extra: JSON.stringify({}),
                        next_bill_fetch_date: MOMENT().subtract(1, 'days').valueOf(),
                        notification_status: 1
                    }])
                };
                done();
            });
        });

        it("updateMultipleRecordsWithSameRN | record not present in sql", (done) => {
            let billsKafkaRow = {
                rechargeNumber: "test",
                customerId: 123,
                service: "test",
                operator: "test",
                isSmsParsedCustIdPresentInSql: false,
                smsParsedCustId: 123,
                airtelPrepaidPublisherDetails: {
                    list_of_other_customer_ids: [125, 126, 127]
                }
            };
            let billsKafkaRowCloneForDataExhaust = _.cloneDeep(billsKafkaRow);

            let stub1 = sinon.stub(serviceObj, 'preProcessData').returns(billsKafkaRow);

            let stub2 = sinon.stub(serviceObj, 'processUpdateOnEachRecord').returns(Promise.resolve());

            serviceObj.updateMultipleRecordsWithSameRN((error) => {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(5);
                expect(serviceObj.nonPaytmBillsModel.readBillsWithoutCustId.called).to.be.true;
                stub1.restore();
                stub2.restore();
                done();
            }, billsKafkaRow, billsKafkaRowCloneForDataExhaust);
        });

        it("updateMultipleRecordsWithSameRN | record present in sql", (done) => {
            let billsKafkaRow = {
                rechargeNumber: "test",
                customerId: 123,
                service: "test",
                operator: "test",
                isSmsParsedCustIdPresentInSql: true,
                smsParsedCustId: 123,
                airtelPrepaidPublisherDetails: {
                    list_of_other_customer_ids: [125, 126, 127]
                }
            };
            let billsKafkaRowCloneForDataExhaust = _.cloneDeep(billsKafkaRow);

            let stub1 = sinon.stub(serviceObj, 'preProcessData').returns(billsKafkaRow);

            let stub2 = sinon.stub(serviceObj, 'processUpdateOnEachRecord').returns(Promise.resolve());

            serviceObj.updateMultipleRecordsWithSameRN((error) => {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(4);
                expect(serviceObj.nonPaytmBillsModel.readBillsWithoutCustId.called).to.be.true;
                stub1.restore();
                stub2.restore();
                done();
            }, billsKafkaRow, billsKafkaRowCloneForDataExhaust);
        });
        
    });