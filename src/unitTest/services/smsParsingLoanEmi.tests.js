import { expect } from 'chai';
import sinon from 'sinon';
import SmsParsingLoanEmi from '../../services/smsParsingLoanEmi';
import MOMENT from 'moment';
import _ from 'lodash';
import chai from 'chai';
import sinonChai from 'sinon-chai';

chai.use(sinonChai);

describe('SmsParsingLoanEmi', () => {
    let smsParsingLoanEmi;
    let mockOptions;
    let mockRecord;
    let sandbox;
    const FIXED_TIMESTAMP = '2024-03-20T10:00:00Z';
    
    beforeEach(() => {
        sandbox = sinon.createSandbox();
        sandbox.useFakeTimers(new Date(FIXED_TIMESTAMP).getTime());

        mockOptions = {
            L: {
                log: sandbox.stub(),
                error: sandbox.stub(),
                critical: sandbox.stub(),
                verbose: sandbox.stub()
            },
            INFRAUTILS: {
                kafka: {
                    producer: sandbox.stub().returns({
                        initProducer: sandbox.stub().callsFake((mode, cb) => cb(null))
                    }),
                    consumer: sandbox.stub().returns({
                        initConsumer: sandbox.stub().callsFake((exec, cb) => cb(null)),
                        commitOffset: sandbox.stub(),
                        close: sandbox.stub()
                    })
                }
            },
            config: {
                KAFKA: {
                    TOPICS: {
                        AUTOMATIC_SYNC: { HOSTS: 'localhost:9092' },
                        NON_PAYTM_RECORDS: { HOSTS: 'localhost:9092' },
                        REMINDER_BILLFETCH_PIPELINE: { HOSTS: 'localhost:9092' },
                        REMINDER_BILLFETCH_PIPELINE_REALTIME: { HOSTS: 'localhost:9092' },
                        CT_EVENTS_PUBLISHER: { HOSTS: 'localhost:9092' },
                        SMS_PARSING_LOAN_EMI: { HOSTS: 'localhost:9092' }
                    },
                    SERVICES: {
                        SMS_PARSING_LOAN_EMI: {
                            TOPIC: 'test-topic'
                        }
                    }
                },
                COMMON: {
                    bills_status: {
                        BILL_FETCHED: 4,
                        OLD_BILL_FOUND: 5,
                        DISABLED: 7,
                        NOT_IN_USE: 13
                    },
                    notification_status: {
                        ENABLED: 1
                    }
                },
                DYNAMIC_CONFIG: {
                    SMS_PARSING_LOAN_EMI: {
                        ENABLED_PREDICTED_CATEGORIES: { CATEGORIES: ['1', '2', '3'] },
                        ENABLED_SERVICE_LIST: { SERVICES: ['loan', 'emi'] },
                        ENABLED_OPERATOR_LIST: { OPERATORS: ['hdfc_bank', 'sbi_bank'] },
                        COMMON: {
                            MIN_AMOUNT: 100,
                            MAX_AMOUNT: 500000,
                            DUEDATE_NBFD_DAYS: 20,
                            BILLDATE_NBFD_DAYS: 30,
                            MASKED_RN_MATCH_LENGTH: 2,
                            DEFAULT_BILLS_LIMIT: 50
                        }
                    }
                }
            },
            activePidLib: {},
            greyScaleEnv: false
        };

        mockRecord = {
            cId: '12345',
            customerId: '12345',
            rechargeNumber: '********',
            operator: 'hdfc_bank',
            service: 'loan',
            predicted_category: '1',
            emi_due: '10000',
            due_date: '2024-03-30',
            bill_date: '2024-03-20',
            smsDateTime: *************,
            debugKey: 'test123',
            appVersion: '1.0.0',
            smsSenderID: 'HDFCBK'
        };

        smsParsingLoanEmi = new SmsParsingLoanEmi(mockOptions);
    });

    afterEach(() => {
        sandbox.restore();
    });

    it('validateAndProcessRecord', () => {
        it('should validate and process a valid record', (done) => {
            smsParsingLoanEmi.validateAndProcessRecord(mockRecord, (error, processedRecord) => {
                expect(error).to.be.null;
                expect(processedRecord).to.exist;
                expect(processedRecord.customerId).to.equal('12345');
                expect(processedRecord.rechargeNumber).to.equal('********');
                expect(processedRecord.amount).to.equal(10000);
                done();
            });
        });

        it('should reject records with invalid category', (done) => {
            const invalidRecord = { ...mockRecord, predicted_category: '5' };
            
            smsParsingLoanEmi.validateAndProcessRecord(invalidRecord, (error) => {
                expect(error).to.equal('CATEGORY_NOT_ENABLED');
                done();
            });
        });

        it('should reject records with amount below minimum', (done) => {
            const lowAmountRecord = { ...mockRecord, emi_due: '50' };
            
            smsParsingLoanEmi.validateAndProcessRecord(lowAmountRecord, (error) => {
                expect(error).to.equal('AMOUNT_LESS_THAN_MIN_AMOUNT');
                done();
            });
        });

        it('should reject records with amount above maximum', (done) => {
            const highAmountRecord = { ...mockRecord, emi_due: '600000' };
            
            smsParsingLoanEmi.validateAndProcessRecord(highAmountRecord, (error) => {
                expect(error).to.equal('AMOUNT_GREATER_THAN_MAX_AMOUNT');
                done();
            });
        });

        it('should reject records with past due dates', (done) => {
            const pastDueRecord = { ...mockRecord, due_date: '2024-03-19' };
            
            smsParsingLoanEmi.validateAndProcessRecord(pastDueRecord, (error) => {
                expect(error).to.equal('DUE_DATE_IN_PAST');
                done();
            });
        });

        it('should reject records with invalid due dates', (done) => {
            const invalidDueRecord = { ...mockRecord, due_date: 'invalid-date' };
            
            smsParsingLoanEmi.validateAndProcessRecord(invalidDueRecord, (error) => {
                expect(error).to.equal('INVALID_DUE_DATE');
                done();
            });
        });
    });

    it('findRechargeNumberMatch', () => {
        let processedRecord;

        beforeEach(() => {
            processedRecord = {
                debugKey: 'test123',
                rechargeNumber: '********',
                dueDate: '2024-04-01',
                tableName: 'test_table',
                dbData: []
            };
        });

        it('should return findAndUpdateToCassandra when no records match', async () => {
            processedRecord.dbData = [];
            
            const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
            expect(result.action).to.equal('findAndUpdateToCassandra');
        });

        it('should return update action for exact recharge number match', async () => {
            processedRecord.dbData = [{
                recharge_number: '********',
                status: 4
            }];
            
            const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
            expect(result.action).to.equal('update');
        });

        it('should skip when due date is null', async () => {
            processedRecord.dueDate = null;
            processedRecord.dbData = [{
                recharge_number: '********',
                status: 4
            }];
            
            const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
            expect(result.action).to.equal('skip');
            expect(result.reason).to.equal('DUE_DATE_IS_NULL');
        });

        it('should handle records hitting default limit without match', async () => {
            // Create array with defaultBillsLimit + 1 records
            processedRecord.dbData = Array(51).fill({
                recharge_number: 'different_number',
                status: 4
            });
            
            const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
            expect(result.action).to.equal('skip');
            expect(result.reason).to.equal('DEFAULT_BILLS_LIMIT_REACHED');
        });

        it('Masked Number Handling', () => {
            it('should identify masked number with XXX pattern', async () => {
                processedRecord.rechargeNumber = 'XXX5432';
                processedRecord.dbData = [{
                    recharge_number: '9875432',
                    status: 4
                }];
                
                const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
                expect(result.action).to.equal('skip');
                expect(result.reason).to.equal('PARTIAL_RN_MATCH_FOUND');
            });

            it('should identify masked number with *** pattern', async () => {
                processedRecord.rechargeNumber = '***5432';
                processedRecord.dbData = [{
                    recharge_number: '9875432',
                    status: 4
                }];
                
                const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
                expect(result.action).to.equal('skip');
                expect(result.reason).to.equal('PARTIAL_RN_MATCH_FOUND');
            });

            it('should not match when masked digits dont match', async () => {
                processedRecord.rechargeNumber = 'XXX5432';
                processedRecord.dbData = [{
                    recharge_number: '9876543',
                    status: 4
                }];
                
                const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
                expect(result.action).to.equal('findAndUpdateToCassandra');
            });

            it('should handle invalid masked patterns', async () => {
                processedRecord.rechargeNumber = 'XX*5432';
                processedRecord.dbData = [{
                    recharge_number: '9875432',
                    status: 4
                }];
                
                const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
                expect(result.action).to.equal('findAndUpdateToCassandra');
            });
        });

        it('Error Handling', () => {
            it('should handle getBillsOfSameRN error', async () => {
                processedRecord.dbData = [{
                    recharge_number: '********',
                    status: 4
                }];
                
                // Mock getBillsOfSameRN to throw error
                smsParsingLoanEmi.getBillsOfSameRN = sandbox.stub().rejects(new Error('DB Error'));
                
                const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
                expect(result.reason).to.equal('ERROR_GETTING_BILLS_OF_SAME_RN');
            });

            it('should handle null recharge number', async () => {
                processedRecord.rechargeNumber = null;
                
                const result = await smsParsingLoanEmi.findRechargeNumberMatch(processedRecord);
                expect(result.action).to.equal('findAndUpdateToCassandra');
            });
        });
    });

    it('checkEligibilityAndSetNBFD', () => {
        it('should set NBFD for valid records', async () => {
            const processedRecord = {
                debugKey: 'test123',
                dueDate: MOMENT().add(5, 'days').format('YYYY-MM-DD HH:mm:ss'),
                billDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                dbData: [{
                    next_bill_fetch_date: null,
                    due_date: MOMENT().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss')
                }]
            };

            return new Promise((resolve) => {
                smsParsingLoanEmi.checkEligibilityAndSetNBFD((error) => {
                    expect(error).to.be.null;
                    expect(processedRecord.nextBillFetchDate).to.exist;
                    resolve();
                }, processedRecord);
            });
        });

        it('should reject when NBFD is in future', async () => {
            const processedRecord = {
                debugKey: 'test123',
                dbData: [{
                    next_bill_fetch_date: MOMENT().add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
                }]
            };

            await new Promise((resolve) => {
                smsParsingLoanEmi.checkEligibilityAndSetNBFD((error) => {
                    expect(error).to.equal('NBFD_IN_FUTURE');
                    resolve();
                }, processedRecord);
            });
        });
    });

    it('generateDummyRechargeNumber', () => {
        it('should generate valid dummy recharge number', () => {
            const record = {
                customerId: '12345',
                operator: 'hdfc_bank'
            };

            const dummyRN = smsParsingLoanEmi.generateDummyRechargeNumber(record);
            expect(dummyRN).to.equal('12345_hdfc_bank');
        });

        it('should throw error for invalid input', () => {
            const invalidRecord = {};
            
            expect(() => {
                smsParsingLoanEmi.generateDummyRechargeNumber(invalidRecord);
            }).to.throw('Missing customerId or operator');
        });
    });

    it('publishToCassandra', () => {
        it('should successfully publish to Cassandra via Kafka', async () => {
            const mockData = {
                customerId: '12345',
                rechargeNumber: '********',
                amount: 10000
            };

            const mockProcessedRecord = {
                debugKey: 'test123'
            };

            smsParsingLoanEmi.nonPaytmKafkaPublisher = {
                publishData: sandbox.stub().callsFake((messages, callback) => callback(null))
            };

            await smsParsingLoanEmi.publishToCassandra(mockData, mockProcessedRecord);
            expect(smsParsingLoanEmi.nonPaytmKafkaPublisher.publishData).to.have.been.calledOnce;
        });

        it('should handle publish errors', async () => {
            const mockData = {
                customerId: '12345'
            };

            smsParsingLoanEmi.nonPaytmKafkaPublisher = {
                publishData: sandbox.stub().callsFake((messages, callback) => callback(new Error('Kafka error')))
            };

            try {
                await smsParsingLoanEmi.publishToCassandra(mockData, {});
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect(error).to.equal('Error while publishing message in Kafka');
            }
        });
    });

    it('start', () => {
        it('should initialize successfully', async () => {
            const startPromise = smsParsingLoanEmi.start();
            await expect(startPromise).to.be.fulfilled;
        });
    });

    it('suspendOperations', () => {
        it('should close kafka consumer successfully', async () => {
            smsParsingLoanEmi.kafkaSMSParsingLoanConsumer = {
                close: sandbox.stub().callsFake((callback) => callback(null, 'success'))
            };

            await smsParsingLoanEmi.suspendOperations();
            expect(smsParsingLoanEmi.kafkaSMSParsingLoanConsumer.close).to.have.been.calledOnce;
        });
    });

    describe('prepareExtraDetails', () => {
        it('should prepare extra details correctly', () => {
            const record = {
                smsTimeStamp: '2024-03-20 10:00:00',
                partialRecordFound: true
            };
            const dbExtra = { existingField: 'value' };
            const dbRecord = {
                bill_fetch_date: '2024-03-19',
                due_date: '2024-04-01',
                bill_date: '2024-03-15',
                amount: 1000
            };

            const result = smsParsingLoanEmi.prepareExtraDetails(record, dbExtra, dbRecord);

            expect(result).to.include({
                existingField: 'value',
                billSource: 'sms_parsed',
                updated_source: 'sms',
                updated_data_source: 'SMS_PARSING_DWH',
                source_subtype_2: 'PARTIAL_BILL'
            });
            expect(result.lastSuccessBFD).to.equal('2024-03-19');
            expect(result.lastDueDt).to.equal('2024-04-01');
            expect(result.lastBillDt).to.equal('2024-03-15');
            expect(result.lastAmount).to.equal(1000);
            expect(result.isDwhSmsParsing).to.be.true;
            expect(result.errorCounters).to.deep.equal(undefined);
        });
    });

    describe('publishToCassandra', () => {
        beforeEach(() => {
            smsParsingLoanEmi.nonPaytmKafkaPublisher = {
                publishData: sandbox.stub()
            };
        });

        it('should handle kafka publish errors', async () => {
            const mockData = { test: 'data' };
            const mockError = new Error('Kafka error');
            
            smsParsingLoanEmi.nonPaytmKafkaPublisher.publishData.callsFake((messages, callback) => {
                callback(mockError);
            });

            try {
                await smsParsingLoanEmi.publishToCassandra(mockData, { debugKey: 'test123' });
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect(error).to.equal('Error while publishing message in Kafka');
            }
        });

        it('should send metrics on successful publish', async () => {
            const mockData = { test: 'data' };
            const processedRecord = { debugKey: 'test123' };
            
            smsParsingLoanEmi.nonPaytmKafkaPublisher.publishData.callsFake((messages, callback) => {
                callback(null);
            });

            await smsParsingLoanEmi.publishToCassandra(mockData, processedRecord);
            expect(mockOptions.L.log).to.have.been.called;
        });
    });

    describe('getRecordsFromDb', () => {
        it('should handle database errors', async () => {
            const mockError = new Error('DB error');
            sandbox.stub(smsParsingLoanEmi.bills, 'getBillsOfCustIdServiceOperator')
                .callsFake((callback) => callback(mockError));

            return new Promise((resolve) => {
                smsParsingLoanEmi.getRecordsFromDb((error, result) => {
                    expect(error).to.equal(mockError);
                    expect(result).to.be.false;
                    resolve();
                }, mockRecord);
            });
        });

        it('should handle empty results', async () => {
            sandbox.stub(smsParsingLoanEmi.bills, 'getBillsOfCustIdServiceOperator')
                .callsFake((callback) => callback(null, []));

            return new Promise((resolve) => {
                smsParsingLoanEmi.getRecordsFromDb((error, result) => {
                    expect(error).to.be.null;
                    expect(result).to.be.false;
                    resolve();
                }, mockRecord);
            });
        });
    });

    describe('loadConfigs', () => {
        it('should load all dynamic configs successfully', () => {
            smsParsingLoanEmi.loadConfigs();
            
            expect(smsParsingLoanEmi.enabledPredictedCategories).to.deep.equal(['1', '2', '3']);
            expect(smsParsingLoanEmi.enabledServiceList).to.deep.equal(['loan', 'emi']);
            expect(smsParsingLoanEmi.enabledOperatorList).to.deep.equal(['hdfc_bank', 'sbi_bank']);
            expect(smsParsingLoanEmi.minAmount).to.equal(100);
            expect(smsParsingLoanEmi.maxAmount).to.equal(500000);
        });

        it('should handle missing configs and use defaults', () => {
            smsParsingLoanEmi.config.DYNAMIC_CONFIG = {};
            smsParsingLoanEmi.loadConfigs();
            
            expect(smsParsingLoanEmi.enabledPredictedCategories).to.deep.equal([]);
            expect(smsParsingLoanEmi.minAmount).to.equal(100);
            expect(smsParsingLoanEmi.maxAmount).to.equal(500000);
        });
    });

    describe('startConfigRefresh', () => {
        beforeEach(() => {
            sandbox.useFakeTimers();
        });

        it('should refresh configs at specified interval', () => {
            const loadConfigsSpy = sandbox.spy(smsParsingLoanEmi, 'loadConfigs');
            smsParsingLoanEmi.startConfigRefresh();
            
            sandbox.clock.tick(15 * 60 * 1000); // 15 minutes
            expect(loadConfigsSpy.callCount).to.equal(1);
            
            sandbox.clock.tick(15 * 60 * 1000); // Another 15 minutes
            expect(loadConfigsSpy.callCount).to.equal(2);
        });
    });

    describe('start', () => {
        it('should initialize kafka and refresh DCAT cache', async () => {
            const configureKafkaSpy = sandbox.spy(smsParsingLoanEmi, 'configureKafka');
            const refreshDCATSpy = sandbox.spy(smsParsingLoanEmi.smsParsingSyncCCBillLib, 'refreshDCATCacheData');
            
            await smsParsingLoanEmi.start();
            
            expect(configureKafkaSpy.calledOnce).to.be.true;
            expect(refreshDCATSpy.calledOnce).to.be.true;
        });

        it('should handle kafka configuration errors', async () => {
            sandbox.stub(smsParsingLoanEmi, 'configureKafka').rejects(new Error('Kafka Error'));
            
            try {
                await smsParsingLoanEmi.start();
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error.message).to.equal('Kafka Error');
            }
        });
    });

    describe('suspendOperations', () => {
        it('should clean up resources properly', async () => {
            const clearIntervalSpy = sandbox.spy(global, 'clearInterval');
            const kafkaCloseSpy = sandbox.spy();
            
            smsParsingLoanEmi.kafkaSMSParsingLoanConsumer = {
                close: (cb) => {
                    kafkaCloseSpy();
                    cb(null, 'success');
                }
            };
            
            await smsParsingLoanEmi.suspendOperations();
            
            expect(clearIntervalSpy.calledOnce).to.be.true;
            expect(kafkaCloseSpy.calledOnce).to.be.true;
        });

        it('should handle kafka close errors', async () => {
            smsParsingLoanEmi.kafkaSMSParsingLoanConsumer = {
                close: (cb) => cb(new Error('Close Error'))
            };
            
            try {
                await smsParsingLoanEmi.suspendOperations();
                expect.fail('Should have thrown error');
            } catch (error) {
                expect(error.message).to.equal('Close Error');
            }
        });
    });

    describe('processData', () => {
        it('should process valid kafka records', (done) => {
            const record = {
                timestamp: Date.now(),
                value: JSON.stringify({
                    data: [mockRecord]
                })
            };

            const processRecordsSpy = sandbox.spy(smsParsingLoanEmi, 'processRecords');
            
            smsParsingLoanEmi.processData(record, () => {
                expect(processRecordsSpy.calledOnce).to.be.true;
                expect(processRecordsSpy.firstCall.args[1]).to.deep.include(mockRecord);
                done();
            });
        });

        it('should handle invalid kafka records', (done) => {
            const invalidRecord = {
                timestamp: Date.now(),
                value: 'invalid json'
            };

            smsParsingLoanEmi.processData(invalidRecord, () => {
                // Should not throw error and complete callback
                done();
            });
        });
    });

    describe('publishInKafka', () => {
        beforeEach(() => {
            smsParsingLoanEmi.billFetchKafkaPublisher = {
                publishData: sandbox.stub().callsFake((data, cb) => cb(null))
            };
            smsParsingLoanEmi.ctKafkaPublisher = {
                publishData: sandbox.stub().callsFake((data, cb) => cb(null))
            };
        });

        it('should publish to both bill fetch and CT kafka topics', (done) => {
            const processedRecord = {
                debugKey: 'test123',
                dbData: [{
                    customer_id: '12345',
                    recharge_number: '********',
                    notification_status: 1,
                    is_automatic: 0,
                    status: 4
                }]
            };

            smsParsingLoanEmi.publishInKafka((error) => {
                expect(error).to.be.null;
                expect(smsParsingLoanEmi.billFetchKafkaPublisher.publishData.calledOnce).to.be.true;
                expect(smsParsingLoanEmi.ctKafkaPublisher.publishData.calledOnce).to.be.true;
                done();
            }, processedRecord);
        });

        it('should handle kafka publish errors', (done) => {
            smsParsingLoanEmi.billFetchKafkaPublisher.publishData = 
                sandbox.stub().callsFake((data, cb) => cb(new Error('Publish Error')));

            const processedRecord = {
                debugKey: 'test123',
                dbData: [{
                    customer_id: '12345',
                    notification_status: 1
                }]
            };

            smsParsingLoanEmi.publishInKafka((error) => {
                expect(error).to.exist;
                expect(error.message).to.equal('Publish Error');
                done();
            }, processedRecord);
        });
    });

    describe('configureKafka', () => {
        it('should initialize all kafka publishers', (done) => {
            smsParsingLoanEmi.configureKafka((error) => {
                expect(error).to.be.null;
                expect(smsParsingLoanEmi.nonPaytmKafkaPublisher).to.exist;
                expect(smsParsingLoanEmi.billFetchKafkaPublisher).to.exist;
                expect(smsParsingLoanEmi.ctKafkaPublisher).to.exist;
                done();
            });
        });

        it('should handle kafka initialization errors', (done) => {
            smsParsingLoanEmi.infraUtils.kafka.producer = sandbox.stub().returns({
                initProducer: sandbox.stub().callsFake((mode, cb) => cb(new Error('Kafka Init Error')))
            });

            smsParsingLoanEmi.configureKafka((error) => {
                expect(error.message).to.equal('Kafka Init Error');
                done();
            });
        });
    });
});