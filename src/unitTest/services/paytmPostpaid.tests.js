/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before} from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import sinon from 'sinon';
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import _, { isNull } from 'lodash';
    
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    import SERVICE from '../../services'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    describe("Module: recents service test suite", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.paytmPostpaid(options);
                done();
            });
        });
    
        it("_prepareDataToInsert | success case", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(1)
                expect(stub6).to.have.callCount(1)
                return done();
            });
        });

        it("_prepareDataToInsert | error in validation", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns(["error in validation",processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(0)
                expect(stub3).to.have.callCount(0)
                expect(stub4).to.have.callCount(0)
                expect(stub5).to.have.callCount(0)
                expect(stub6).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | error in getBillsOfSameRech", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb("error in getBillsOfSameRech",[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(0)
                expect(stub4).to.have.callCount(0)
                expect(stub5).to.have.callCount(0)
                expect(stub6).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | null from getBillsData", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns(null);
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(0)
                expect(stub5).to.have.callCount(0)
                expect(stub6).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | error in createBillForPaytmPostpaid", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb("error in creation");
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(0)
                expect(stub6).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | error in getUpdatedRecordFromDB", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb("error in getUpdatedRecordFromDB")
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(1)
                expect(stub6).to.have.callCount(0)
                return done();
            });
        });

        it("_prepareDataToInsert | error in publishInKafka", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb("error in publishing")
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(1)
                expect(stub6).to.have.callCount(1)
                return done();
            });
        });

        it("_prepareDataToInsert | error in publishInKafka", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null,processedRecord]);

            let stub2 = sinon.stub(serviceObj.billsModel, 'getBillsOfSameRech').callsFake(function(cb,tableName, customerId) {
                return cb(null,[]);
            });
            let stub3 = sinon.stub(serviceObj, 'getBillsData').returns({});
            let stub4 = sinon.stub(serviceObj.bills, 'createBillForPaytmPostpaid').callsFake(function(cb,recentBillsData, tableName){
                return cb(null);
            })
            let stub5 = sinon.stub(serviceObj, 'getUpdatedRecordFromDB').callsFake(function(cb, recentBillsData){
                return cb(null)
            });
            let stub6 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function(cb){
                return cb(null)
            });
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj._prepareDataToInsert(recordData,function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(1)
                expect(stub6).to.have.callCount(1)
                return done();
            });
        });

        it("publishInKafka | error in publishInKafka", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":"2023-08-31T18:30:01.000+0000","billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":*********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }

            let stub1 = sinon.stub(serviceObj, 'pushToKafkaForAutomaticSync').callsFake(function(done, recentBillsData,tableName){
                return done(null)
            });
            let stub2 = sinon.stub(serviceObj, 'publishInBillFetchKafka').callsFake(function(done, recentBillsData){
                return done(null)
            });
            let stub3 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function(done, recentBillsData){
                return done(null)
            });
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            serviceObj.publishInKafka(function(){
                stub1.restore();
                stub2.restore();
                stub3.restore();
                
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                return done();
            },recentBillsData, tableName);
        });

        it("validateAndProcessRecord | sucess", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(*********)
            expect(record.rechargeNumber).to.equal('**********')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });

        it("validateAndProcessRecord | success, dueDate older", (done) => {
            let recordData = {"account_number":"**********","customer_id":"*********","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().subtract(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(*********)
            expect(record.rechargeNumber).to.equal('**********')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            // expect(record.dueDate).to.equal(MOMENT().subtract(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });

        it("validateAndProcessRecord | failure, cust_id empty string", (done) => {
            let recordData = {"account_number":"**********","customer_id":"","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(null)
            expect(record.rechargeNumber).to.equal('**********')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params customerId is Missing / Invalid')
            return done();
        });

        it("validateAndProcessRecord | failure, recharge_number empty string", (done) => {
            let recordData = {"account_number":"","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":100,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(100)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params rechargeNumber is Missing / Invalid')
            return done();
        });

        it("validateAndProcessRecord | failure, due_amount not present empty string", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(null)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params due_amount is Missing / Invalid')
            return done();
        });

        it("validateAndProcessRecord | failure, due_amount is string", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":"abc","due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(null)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal('Mandatory Params due_amount is Missing / Invalid')
            return done();
        });

        it("validateAndProcessRecord | failure, due_amount is zero", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":'0',"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":**********,"product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(0)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal(**********)
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });
        it("validateAndProcessRecord | failure, productid is string", (done) => {
            let recordData = {"account_number":"1234566","customer_id":"12345","current_month_bill_amount":199.0,"unbilled_spends":900.0,"due_amount":150,"due_date":MOMENT().add(1,'months').format("YYYY-MM-DDTHH:mm:ss.SSSZ"),"billing_date":"2023-08-01T00:20:53.000+0000","catalog_repayment_pid":"**********","product_type":"POSTPAID","mobile_number":**********,"next_bill_date":"2023-08-31T18:30:00.489+0000","event_type":"BILL_GEN"};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let [error,record] = serviceObj.validateAndProcessRecord(recordData);
            console.log("record record record::",record);
            expect(record.customerId).to.equal(12345)
            expect(record.rechargeNumber).to.equal('1234566')
            expect(record.currentBillAmount).to.equal(199)
            expect(record.dueAmount).to.equal(150)
            expect(record.unBilledAmount).to.equal(900)
            expect(record.dueDate).to.equal(MOMENT().add(1,'months').startOf('day').format('YYYY-MM-DD HH:mm:ss'))
            expect(record.billDate).to.equal('2023-08-01 05:50:53')
            expect(record.nextBillFetchDate).to.equal('2023-09-01 00:00:00')
            expect(record.billFetchDate).to.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
            expect(record.productId).to.equal('**********')
            expect(record.productType).to.equal('POSTPAID')
            expect(record.custMobile).to.equal(**********)
            expect(record.eventType).to.equal('BILL_GEN')
            expect(record.billGen).to.equal(true)
            expect(record.operator).to.equal('airtel')
            expect(record.service).to.equal('mobile')
            expect(error).to.equal(null)
            return done();
        });

        it("getBillsData | same bill updated with billGen true", (done) => {
            let dbrecord = {transactionHistory : [{customerOtherInfo : "{\"billGen\":true}",due_date:"2023-12-05 05:30:00", amount : 100}]};
            let processedRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                dueDate : "2023-12-05 00:00:00",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }
            let tableName = 'bills_paytmpostpaid';
            let customerId = 667770;
            let recentBillsData = {};
            let fromRecents = true;
            serviceObj.nonPaytmKafkaPublisher = {
                publishData : () => {
                    return cb(null, {})
                }
            }
    
            // let stub2 = sinon.stub(serviceObj1, 'extractLastCC').returns('1234');
            let billsData = serviceObj.getBillsData(dbrecord,processedRecord);
            expect(billsData).to.equal(null)
            return done();
        });
    
    });
    
    describe("Module publisher:: paytmPostpaid :: CT tests", function () {
        let serviceObj;    
    
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                // billsSubscriberObj = new EmiDueCommonConsumer(options);
                serviceObj = new SERVICE.paytmPostpaid(options);
                done();
            });
        });
    
        it("publishCtEvents function | check function calls", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }  
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, [])
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    
            serviceObj.publishCtEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(1)
            expect(cb).to.have.been.calledWith(null)
            return done();
        })
    
        it("publishCtEvents function | no retailerStatus", (done) => {
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            } 
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, data)
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    
            serviceObj.publishCtEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(0)
            expect(stub3).to.have.callCount(0)
            return done();
        })
    
        it("publishCtEvents function | no thumbnail", (done) => {
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            } 
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, data)
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    
            serviceObj.publishCtEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            expect(stub3).to.have.callCount(0)
            return done();
        })
    
        it("publishCtEvents function | notification status!=1", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    notification_status : 0
                }]
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }             
    
            let cb = sinon.spy();
    
            serviceObj.ctKafkaPublisher = {
                publishData : () => {
                    return cb(null, [])
                }
            }
            let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
            let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
            let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    
            serviceObj.publishCtEvents(cb, tempRecord);
            stub1.restore();
            stub2.restore();
            stub3.restore();
    
            expect(stub1).to.have.callCount(0)
            expect(stub2).to.have.callCount(0)
            expect(stub3).to.have.callCount(0)
            return done();
        })
    
        
    })

    describe("Module publisher:: paytmPostpaid :: billFetch kafka publish tests", function () {
        let serviceObj;    
    
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                // billsSubscriberObj = new EmiDueCommonConsumer(options);
                serviceObj = new SERVICE.paytmPostpaid(options);
                done();
            });
        });
    
        it("publishInBillFetchKafka function | check function calls", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 1,
                    is_automatic:0,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }  
            let cb = sinon.spy();
    
            serviceObj.billFetchKafkaRealtime = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.publishInBillFetchKafka(cb, tempRecord);
            expect(cb).to.have.callCount(1)
            return done();
        })
    
        it("publishInBillFetchKafka function | check function calls, notification status 0", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 0,
                    is_automatic:0,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }  
            let cb = sinon.spy();
    
            serviceObj.billFetchKafka = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.publishInBillFetchKafka(function(err){
                expect(cb).to.have.callCount(0)
                return done();
            }, tempRecord);
        })
    
        it("publishInBillFetchKafka function | check function calls, isautomatic 1", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 1,
                    is_automatic:1,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }  
            let cb = sinon.spy();
    
            serviceObj.billFetchKafka = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.publishInBillFetchKafka(function(err){
                expect(cb).to.have.callCount(0)
                return done();
            }, tempRecord);
        })
    })


    describe("Module publisher:: paytmPostpaid :: automaticBillFetch kafka publish tests", function () {
        let serviceObj;    
    
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                // billsSubscriberObj = new EmiDueCommonConsumer(options);
                serviceObj = new SERVICE.paytmPostpaid(options);
                done();
            });
        });
    
        it("pushToKafkaForAutomaticSync function | check function calls", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 1,
                    is_automatic:1,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }  
            let cb = sinon.spy();
    
            serviceObj.automatic_sync_publisher = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.pushToKafkaForAutomaticSync(cb, tempRecord);
            expect(cb).to.have.callCount(1)
            return done();
        })
    
        it("publishInBillFetchKafka function | check function calls, is automatic= 0", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 1,
                    is_automatic:0,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
                // dueDate : "2023-09",
                // billDate :  _.get(billsData, 'billing_date', null)? MOMENT(_.get(billsData, 'billing_date', null)).format(dateFormat):MOMENT().format(dateFormat),
                // nextBillFetchDate : _.get(billsData, 'next_bill_date', null)? MOMENT(_.get(billsData, 'next_bill_date', null)).format(dateFormat):null,
                // billFetchDate : MOMENT().format(dateFormat),
                // productId : _.get(billsData, 'product_id', null),
                // productType :  _.get(billsData, 'product_type', null),
                // custMobile : _.get(billsData, 'mobile_number', null),
                // eventType :  _.get(billsData, 'event_type', null),
                // billGen : _.toLower(_.get(billsData,'event_type',null))=="bill_gen"?true:false,
                // service : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'service'] , null)),
                // paytype : _.toLower(_.get(self.config, ['CVR_DATA', _.get(billsData, 'product_id', null), 'paytype'] , null)),
            }  
            let cb = sinon.spy();
    
            serviceObj.automatic_sync_publisher = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.pushToKafkaForAutomaticSync(function(err){
                expect(cb).to.have.callCount(0)
                return done();
            }, tempRecord);
        })


        it("publishInBillFetchKafka function | check function calls, isautomatic 3", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 1,
                    is_automatic: 3,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
            }  
            let cb = sinon.spy();
    
            serviceObj.billFetchKafka = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.publishInBillFetchKafka(function(err){
                expect(cb).to.have.callCount(0)
                return done();
            }, tempRecord);
        })
        it("publishInBillFetchKafka function | check function calls, isautomatic 4", (done) => { 
            let tempRecord ={
                customerId :*********,
                rechargeNumber : "**********",
                currentBillAmount : 100,
                dueAmount : 100,
                unBilledAmount :  100,
                operator : "paytm postpaid",
                billGen : true,
                updatedDBRecord : [{
                    customer_id : 12345,
                    recharge_number : "1234567",
                    notification_status : 1,
                    is_automatic: 4,
                    operator: "paytm postpaid",
                    service: "paytm postpaid",
                    amount: 100,
                    circle: null,
                    customer_mobile: "**********",
                    customer_email: "<EMAIL>",
                    bill_date: null,
                    due_date:null,
                    customer_other_info:null
                }],
                processedRecord : {
                    billGen : true
                }
            }  
            let cb = sinon.spy();
    
            serviceObj.billFetchKafka = {
                publishData : () => {
                    return cb(null, [])
                }
            }
    
            serviceObj.publishInBillFetchKafka(function(err){
                expect(cb).to.have.callCount(0)
                return done();
            }, tempRecord);
        })
    })