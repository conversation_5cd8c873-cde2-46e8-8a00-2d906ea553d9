/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before } from 'mocha';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import utility from '../../lib/index'

import STARTUP_MOCK from '../__mocks__/startUp'
import dthSmsParsing from '../../services/smsParsingBillPayment/dthSmsParsing';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("Module: DTH SMS Parsing suite :: validation record", function () {
    let serviceObj;

    let data, record;
    before(function () {    
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new dthSmsParsing(options);
            done();
        });
    });

    

    it("initializeVariable : update variables if updated in config",()=>{
        let og = _.get(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{});
        _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{suntv : 'bills_suntv'});
        
        serviceObj.initializeVariable()
        assert.deepEqual(serviceObj.recent_bills_operators.suntv,'bills_suntv')
        // /** Revert to original table */
        _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],og);
        serviceObj.initializeVariable()
    })
      

    it("executeStrategy | Empty SMS record",()=>{

        let stub1 = sinon.stub(serviceObj, "processRecord").returns(null);
        let record = null;
        serviceObj.executeStrategy((error)=>{
            if (error) {
                expect(error).to.be.equal(null);
            }
        },record);
        expect(stub1).to.have.callCount(0);
        stub1.restore();
    });
    
    it("executeStrategy |  valid SMS record",()=>{
        let record = {"appVersion":"9.6.2","netWorkType":"WIFI","latitude":0,"deviceId":"1223jkdlfdfdfda232434dsafafdsf","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"wakeUpTimeInterval":null,"osVersion":"28","osType":"android","model":"TA-1021","msg_id":"cc442446-72dd-4fb1-a66d-95ae1a3f40bb","brand":"Nokia","user_agent":"Paytm Release/10.23.0/721096 (net.one97.paytm; source=com.android.vending; integrity=true; auth=true; en-IN; okhttp 4.9.1) Android/11 Xiaomi/Redmi_Note_8_Pro (arm64-v8a; resolution=3.0; cores=8)","cId":"227316905","longitude":0,"timestamp":"Tue May 07 08:35:10 UTC 2024","appCount":1,"uploadFrequency":null,"clientId":"androidapp","preference":[{"prefCat":"permission","prefKeys":"ocl.permission.creditcard.sms_read_consent","prefSubCat":"sms consent"}],"mId":"","eventType":"smsEvent","uploadTime":1620903472035,"true_client_ip":null,"consent":false,"realTime":null,"db_name":"sms_parsed_data","newUser":null,"event_name":"sms","batteryPercentage":100,"smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","smsDateTime":1611296208802,"smsRecSubId":1,"smsBody":"d2h Cust id 223939864 Amt Recvd:Rs.340 Bal:Rs.2 DueDate:15-07-24. Congrats! 7 Extra days have been credited in your d2h a/c. To unlock 2months Extra Offer, give a Missed Call on 18003157375 from your RMN before 11/04/2024. TnC","smsSenderID":"CP-MYDTUH","smsReceiver":"+************","smsOperator":"MYDTUH","predicted_category":"dth_bill_payment","level_2_category":"1","dth_cable_cylinder_details":{"recharge_number":"223939864","due_date":"2024-10-15","operator":"suntv","dth_cable_cylinder_model_version":"1.0"},"producerTime":1715070912};
        let stub1 = sinon.stub(serviceObj, "processRecord").returns();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        serviceObj.executeStrategy((error)=>{
            if (error) {
                expect(error).to.be.equal(null);
            }
        },record);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(0);
        stub1.restore();
        stub2.restore();
    });
    
    it("processRecord |  validateAndProcessRecord | Invalid record ",()=>{
        let record = {"appVersion":"9.6.2","netWorkType":"WIFI","latitude":0,"deviceId":"1223jkdlfdfdfda232434dsafafdsf","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"wakeUpTimeInterval":null,"osVersion":"28","osType":"android","model":"TA-1021","msg_id":"cc442446-72dd-4fb1-a66d-95ae1a3f40bb","brand":"Nokia","user_agent":"Paytm Release/10.23.0/721096 (net.one97.paytm; source=com.android.vending; integrity=true; auth=true; en-IN; okhttp 4.9.1) Android/11 Xiaomi/Redmi_Note_8_Pro (arm64-v8a; resolution=3.0; cores=8)","cId":"227316905","longitude":0,"timestamp":"Tue May 07 08:35:10 UTC 2024","appCount":1,"uploadFrequency":null,"clientId":"androidapp","preference":[{"prefCat":"permission","prefKeys":"ocl.permission.creditcard.sms_read_consent","prefSubCat":"sms consent"}],"mId":"","eventType":"smsEvent","uploadTime":1620903472035,"true_client_ip":null,"consent":false,"realTime":null,"db_name":"sms_parsed_data","newUser":null,"event_name":"sms","batteryPercentage":100,"smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","smsDateTime":1611296208802,"smsRecSubId":1,"smsBody":"d2h Cust id 223939864 Amt Recvd:Rs.340 Bal:Rs.2 DueDate:15-07-24. Congrats! 7 Extra days have been credited in your d2h a/c. To unlock 2months Extra Offer, give a Missed Call on 18003157375 from your RMN before 11/04/2024. TnC","smsSenderID":"CP-MYDTUH","smsReceiver":"+************","smsOperator":"MYDTUH","predicted_category":"dth_bill_payment","level_2_category":"1","producerTime":1715070912};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('invalid_record');
        },record);
    });

    it("processRecord |  validateAndProcessRecord | Invalid record 2",()=>{
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('invalid_record');
        },null);
    });
    

  
    it("processRecord |  validateAndProcessRecord | productId missing ",()=>{
        let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":"2023-10-15","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
        _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],null);
        _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal("Mandatory Params productId is Missing / Invalid");
            expect(processedRecord.customerId).to.be.equal(227316905);
            expect(processedRecord.rechargeNumber).to.be.equal('223939864');
            expect(processedRecord.operator).to.be.equal("suntv");
            // expect(processedRecord.service).to.be.equal("dth");
            // expect(processedRecord.paytype).to.be.equal("prepaid");
            expect(processedRecord.productId).to.be.equal(null);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });

    it("processRecord |  validateAndProcessRecord | customerId missing ",()=>{
      let record = {"cId":"","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":"2024-10-15","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
      _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],1235420470);
      _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('Mandatory Params customerId is Missing / Invalid');
            expect(processedRecord.customerId).to.be.equal(null);
            expect(processedRecord.rechargeNumber).to.be.equal('223939864');
            expect(processedRecord.operator).to.be.equal("suntv");
            expect(processedRecord.service).to.be.equal("dth");
            expect(processedRecord.paytype).to.be.equal("prepaid");
            expect(processedRecord.productId).to.be.equal(1235420470);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });
   
    it("processRecord |  validateAndProcessRecord | rechargeNumber missing ",()=>{
      let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":null,"operator":"suntv","amount":null,"dueDate":"2024-10-15","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
      _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],1235420470);
      _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('Mandatory Params rechargeNumber is Missing / Invalid');
            expect(processedRecord.customerId).to.be.equal(227316905);
            expect(processedRecord.rechargeNumber).to.be.equal(null);
            expect(processedRecord.operator).to.be.equal("suntv");
            expect(processedRecord.service).to.be.equal("dth");
            expect(processedRecord.paytype).to.be.equal("prepaid");
            expect(processedRecord.productId).to.be.equal(1235420470);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });
    

    it("processRecord |  validateAndProcessRecord | dueDate missing ",()=>{
      let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":"","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
      _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],1235420470);
      _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('Mandatory Params dueDate is Missing / Invalid');
            expect(processedRecord.customerId).to.be.equal(227316905);
            expect(processedRecord.rechargeNumber).to.be.equal("223939864");
            expect(processedRecord.operator).to.be.equal("suntv");
            expect(processedRecord.service).to.be.equal("dth");
            expect(processedRecord.paytype).to.be.equal("prepaid");
            expect(processedRecord.productId).to.be.equal(1235420470);
            expect(processedRecord.dueDate).to.be.equal(null);
        },record);
    });
     
    // it("processRecord |  validateAndProcessRecord | amount is null ",()=>{
    //   let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":"2024-10-15","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
    //   _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],1235420470);
    //   _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
    //    serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
    //         expect(errorResponse).to.be.equal(null);
    //         expect(processedRecord.customerId).to.be.equal(227316905);
    //         expect(processedRecord.rechargeNumber).to.be.equal("223939864");
    //         expect(processedRecord.operator).to.be.equal("suntv");
    //         expect(processedRecord.service).to.be.equal("dth");
    //         expect(processedRecord.paytype).to.be.equal("prepaid");
    //         expect(processedRecord.amount).to.be.equal(null);
    //         expect(processedRecord.productId).to.be.equal(1235420470);
    //         expect(processedRecord.dueDate).to.be.equal(MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
    //     },record);
    // });
    

    it("processRecord |  validateAndProcessRecord | dueDate in past ",()=>{
      let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":"2023-10-15","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
      _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],1235420470);
      _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
       expect(errorResponse).to.be.equal('DueDate is invalid || DueDate in past');
            expect(processedRecord.customerId).to.be.equal(227316905);
            expect(processedRecord.rechargeNumber).to.be.equal('223939864');
            expect(processedRecord.operator).to.be.equal("suntv");
            expect(processedRecord.service).to.be.equal("dth");
            expect(processedRecord.paytype).to.be.equal("prepaid");
            expect(processedRecord.productId).to.be.equal(1235420470);
            expect(processedRecord.amount).to.be.equal(null);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });

    it("processRecord |  validateAndProcessRecord | success | valid record ",()=>{
      let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":"2023-10-15","rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true};
      _.set(serviceObj.config,['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', 'suntv'],1235420470);
      _.set(serviceObj.config,['CVR_DATA','1235420470'],{"paytype":"prepaid","service":"dth"});
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            // expect(errorResponse).to.be.equal(null);
            expect(processedRecord.customerId).to.be.equal(227316905);
            expect(processedRecord.rechargeNumber).to.be.equal("223939864");
            expect(processedRecord.operator).to.be.equal("suntv");
            expect(processedRecord.service).to.be.equal("dth");
            expect(processedRecord.paytype).to.be.equal("prepaid");
            expect(processedRecord.productId).to.be.equal(1235420470);
            expect(processedRecord.amount).to.be.equal(null);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.dueDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });

    it('processRecord | getNextBillFetchDate::  Get next_bill_fetch_date billDate based operator', () => {
        let record = {
            operator: 'suntv',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        serviceObj.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['suntv'];
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT().add(20, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    // it('processRecord | getNextBillFetchDate::  Get next_bill_fetch_date dueDate based operator', () => {
    //     let record = {
    //         operator: 'suntv',
    //         billDate: MOMENT().format('YYYY-MM-DD'),
    //         dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
    //     }
    //     let result = serviceObj.getNextBillFetchDate(record);
    //     expect(result).to.be.equal(MOMENT(record.dueDate).add(20, 'days').format('YYYY-MM-DD 00:00:00'));
    // })

    it('processRecord | getNextBillFetchDate::  Get next_bill_fetch_date operator whose config is not present', () => {
        let record = {
            operator: 'qwert',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT(record.dueDate).add(20, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    it('processRecord | getNextBillFetchDate::  Get next_bill_fetch_date billDate based operator and billDate is absent', () => {
        let record = {
            operator: 'dishtv',
            billDate: null,
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        serviceObj.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['dishtv'];
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT().add(35, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    it('processRecord | getNextBillFetchDate::  Get next_bill_fetch_date dueDate based operator and NBFD < now()', () => {
        let record = {
            operator: 'suntv',
            billDate: null,
            dueDate: MOMENT().add(-45,'days').format('YYYY-MM-DD')
        }
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss'));  // case value other than mid-night time is stored in DB  
    })
    //when we have to add 30 days. we will be adding 1 month instead of adding days to maintain bill cycle
    it('processRecord | getNextBillFetchDate::  Get next_bill_fetch_date when NEXT_BILL_FETCH_DATES is -1', () => {
        let record = {
            operator: 'jio',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        serviceObj.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['jio'];
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT(record.billDate).add(1, 'months').format('YYYY-MM-DD HH:mm:ss'));  // case value other than mid-night time is stored in DB  
    })

});


describe('DTH function getRecordsFromDb', () => {
  let serviceObj;

  before(() => {
      STARTUP_MOCK.init(function(error, options){
          serviceObj = new dthSmsParsing(options);
  });
})

  it('should handle error from bills.getBillsOfSameRech', (done) => {
    
    const billsMock = {
      getBillsOfSameRech: sinon.stub().callsFake((callback) => {
        callback(new Error('DB query error'), null);
      }),
    };
    let fakeRecord={};

    serviceObj.bills = billsMock; 

      serviceObj.getRecordsFromDb((err, result) => {
     
      assert.instanceOf(err, Error);
      assert.isFalse(result); 
      done();
    }, fakeRecord);

    });

  it('should handle no data found', (done) => {
    const billsMock = {
      getBillsOfSameRech: sinon.stub().callsFake((callback) => {
        callback(null, []); 
      }),
    };

    let fakeRecord={};

    serviceObj.bills = billsMock; 

      serviceObj.getRecordsFromDb((err, result) => {
      assert.isNull(err); 
      assert.isFalse(result); 
      done();
    }, fakeRecord);

    });

  it('should handle data found', (done) => {
    
    const data = [
      {}
    ];
    const billsMock = {
      getBillsOfSameRech: sinon.stub().callsFake((callback) => {
        callback(null, data);
      }),
    };
    let fakeRecord={};


    serviceObj.bills = billsMock; 

      serviceObj.getRecordsFromDb((err, result) => {
      assert.isNull(err); 
      assert.isTrue(result); 
      
      done();
    }, fakeRecord);

    });
  
});

describe('DTH function publishToAutomaticSync', () => {
  let serviceObj;

  before(() => {
      STARTUP_MOCK.init(function(error, options){
          serviceObj = new dthSmsParsing(options);
    });
  })

  // it('should publish data to AUTOMATIC_SYNC_TOPIC', (done) => {
  //   const fakeRecord = {
  //     "service":"dth",
  //         "operator":"suntv",
  //         "rechargeNumber":"12345",
  //         "customerId":1234,
  //         "productId":124,
  //         "is_automatic":1,
  //         dbData:[
  //             {rechargeNumber: "12345",
  //         custId:12345}
  //         ]
  //   };
    
  //   const dbData = [
  //     {rechargeNumber: "12345",
  //     custId:12345}
  //   ];

    
  //   const mappedRow = {
  //     rechargeNumber: "12345",
  //     custId:12345,
  //     is_automatic:1
  //   };
  //   const commonLibMock = {
  //     mapBillsTableColumns: sinon.stub().returns(mappedRow),
  //   };

  //   // Mock parent.kafkaPublisher.publishData to call the callback with no error (success case)
  //   const kafkaPublisherMock = {
  //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
  //       callback(null);
  //     }),
  //   };

  //   serviceObj.commonLib = commonLibMock; 
  //   serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };
  //     serviceObj.publishToAutomaticSync((error) => {
  //     // Check if the error is null, indicating successful publishing
  //     assert.isNull(error);
  //     done();
  //   }, fakeRecord);

  //   });

  // it('should handle error during data publishing', (done) => {
  //     const fakeRecord = {
  //         "service":"dth",
  //         "operator":"suntv",
  //         "rechargeNumber":"12345",
  //         "customerId":1234,
  //         "productId":124,
  //         dbData:[
  //             {rechargeNumber: "12345",
  //         custId:12345}
  //         ]
  //       };
        
  //       const dbData = [
  //         {rechargeNumber: "12345",
  //         custId:12345}
  //       ];
    
        
  //       const mappedRow = {
  //         rechargeNumber: "12345",
  //         custId:12345,
  //         is_automatic:1
  //       };
  //   const commonLibMock = {
  //     mapBillsTableColumns: sinon.stub().returns(mappedRow),
  //   };

    
  //   const kafkaPublisherMock = {
  //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
  //       callback(new Error('Kafka publish error'));
  //     }),
  //   };

  //   serviceObj.commonLib = commonLibMock; 
  //   serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };

  //     serviceObj.publishToAutomaticSync((error) => {
     
  //     assert.isNull(error);
  //     done();
  //   }, fakeRecord);

  //   });

  // it('should handle error during data publishing | is_automatic=0', (done) => {
  //     const fakeRecord = {
  //         "service":"dth",
  //         "operator":"suntv",
  //         "rechargeNumber":"12345",
  //         "customerId":1234,
  //         "productId":124,
  //         dbData:[
  //             {rechargeNumber: "12345",
  //         custId:12345}
  //         ]
  //       };
        
  //       const dbData = [
  //         {rechargeNumber: "12345",
  //         custId:12345}
  //       ];
    
        
  //       const mappedRow = {
  //         rechargeNumber: "12345",
  //         custId:12345,
  //       };
  //   const commonLibMock = {
  //     mapBillsTableColumns: sinon.stub().returns(mappedRow),
  //   };

    
  //   const kafkaPublisherMock = {
  //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
  //       callback(new Error('Kafka publish error'));
  //     }),
  //   };

  //   serviceObj.commonLib = commonLibMock; 
  //   serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };

  //     serviceObj.publishToAutomaticSync((error) => {
     
  //     assert.isNull(error);
  //     done();
  //   }, fakeRecord);

  //   });

  // it('should handle error during data publishing | status=13', (done) => {
  //     const fakeRecord = {
  //         "service":"dth",
  //         "operator":"suntv",
  //         "rechargeNumber":"12345",
  //         "customerId":1234,
  //         "productId":124,
  //         dbData:[
  //             {rechargeNumber: "12345",
  //         custId:12345,
  //     status:13}
  //         ]
  //       };
        
  //       const dbData = [
  //         {rechargeNumber: "12345",
  //         custId:12345}
  //       ];
    
        
  //       const mappedRow = {
  //         rechargeNumber: "12345",
  //         custId:12345,
  //       };
  //   const commonLibMock = {
  //     mapBillsTableColumns: sinon.stub().returns(mappedRow),
  //   };

    
  //   const kafkaPublisherMock = {
  //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
  //       callback(new Error('Kafka publish error'));
  //     }),
  //   };

  //   serviceObj.commonLib = commonLibMock; 
  //   serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };

  //     serviceObj.publishToAutomaticSync((error) => {
     
  //     assert.isNull(error);
  //     done();
  //   }, fakeRecord);

  //   });

  // it('should handle error during data publishing | incomplete details', (done) => {
  //     const fakeRecord = {
  //         "service":"dth",
  //         "operator":"suntv",
  //         "rechargeNumber":"12345",
  //         "customerId":1234,
  //         dbData:[
  //             {rechargeNumber: "12345",
  //         custId:12345}
  //         ]
  //       };
        
  //       const dbData = [
  //         {rechargeNumber: "12345",
  //         custId:12345}
  //       ];
    
        
  //       const mappedRow = {
  //         rechargeNumber: "12345",
  //         custId:12345,
  //       };
  //   const commonLibMock = {
  //     mapBillsTableColumns: sinon.stub().returns(mappedRow),
  //   };

    
  //   const kafkaPublisherMock = {
  //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
  //       callback(new Error('Kafka publish error'));
  //     }),
  //   };

  //   serviceObj.commonLib = commonLibMock; 
  //   serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };

  //     serviceObj.publishToAutomaticSync((error) => {
     
  //     expect(error).to.be.equal('invalid inputs')
  //     done();
  //   }, fakeRecord);
  // })

  
  });

  describe('DTH function publishInKafka', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new dthSmsParsing(options);
      });
      });
  
    // it('should publish data to all three Kafka topics', (done) => {
    //   const fakeRecord = {
    //     "isDwhSmsParsing":true,
    //     "isMigrated":true,
    //     // "dueDate":  MOMENT().add(15,'days').format('YYYY-MM-DD')

        
    //   };
  
    //   // Mock publishToAutomaticSync, publishInBillFetchKafka, and publishCtEvents functions to call the callbacks without error
    //   const publishToAutomaticSyncMock = sinon.stub().callsFake((cb) => {
    //     cb(null);
    //   });
    //   const publishInBillFetchKafkaMock = sinon.stub().callsFake((cb) => {
    //     cb(null);
    //   });
    //   const publishCtEventsMock = sinon.stub().callsFake((cb) => {
    //     cb(null);
    //   });
  

    //   serviceObj.publishToAutomaticSync = publishToAutomaticSyncMock; 
    //   serviceObj.publishInBillFetchKafka = publishInBillFetchKafkaMock;
    //   serviceObj.publishCtEvents = publishCtEventsMock;
  
    //     serviceObj.publishInKafka((error) => {
        
    //     assert.isNull(error);
    //     done();
    //   }, fakeRecord);
  
    //   });
  
    it('should handle error during publishing', (done) => {
      const fakeRecord = {
        
        "action": 'plan_validity_update'
      };
  
      const publishCtEventsMock = sinon.stub().callsFake((cb) => {
        cb(new Error('CT events publish error'));
      });
  
      serviceObj.publishCtEvents = publishCtEventsMock;
  
        serviceObj.publishInKafka((error) => {
       
        assert.instanceOf(error, Error);
        done();
      }, fakeRecord);
  
      });
  
    
  });


  describe('DTH Async function publishCtEvents', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new dthSmsParsing(options);
      });
      });
  
    it('should publish data to CT_EVENTS_PUBLISHER topic', (done) => {
      const fakeRecord = {
        "service":"dth",
        "operator":"suntv",
        "rechargeNumber":"12345",
        "customerId":1234,
        dbData:[
            {rechargeNumber: "12345",
        custId:12345}
        ]
      };
  
      const dbData = [
        {rechargeNumber: "12345",
        custId:12345}
      ];
  
      
      const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(null);
      });
  
      
      const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
        cb(null);
      });
  
      
      const ctKafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(null);
        }),
      };
  

      serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
      serviceObj.commonLib.getCvrData = getCvrDataMock;
      serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };

        serviceObj.publishCtEvents((error) => {
        
        assert.isNull(error);
        expect(getRetailerDataMock).to.have.been.calledOnce;
        expect(getCvrDataMock).to.have.been.calledOnce;
        expect(ctKafkaPublisherMock.publishData).to.have.been.calledOnce;
        done();
      }, fakeRecord);
  
      });
  
    it('status=13 not publishing', (done) => {
        const fakeRecord = {
          "service":"dth",
        "operator":"suntv",
          "rechargeNumber":"12345",
          "customerId":1234,
          dbData:[
              {rechargeNumber: "12345",
          custId:12345,
        status:13}
          ]
        };
    
        const dbData = [
          {rechargeNumber: "12345",
          custId:12345}
        ];
    
        
        const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
          cb(null);
        });
    
        
        const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
          cb(null);
        });
    
        
        const ctKafkaPublisherMock = {
          publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
            callback(null);
          }),
        };
    
  
        serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
        serviceObj.commonLib.getCvrData = getCvrDataMock;
        serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };
  
            serviceObj.publishCtEvents((error) => {
          
        assert.isNull(error);
        expect(getRetailerDataMock).to.have.callCount(0);
        expect(getCvrDataMock).to.have.callCount(0);
        expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
          done();
        }, fakeRecord);
    
          });

      it('notification_status=0 not publishing', (done) => {
        const fakeRecord = {
          "service":"dth",
        "operator":"suntv",
          "rechargeNumber":"12345",
          "customerId":1234,
          dbData:[
              {rechargeNumber: "12345",
          custId:12345,
        notification_status:0}
          ]
        };
    
        const dbData = [
          {rechargeNumber: "12345",
          custId:12345}
        ];
    
        
        const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
          cb(null);
        });
    
        
        const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
          cb(null);
        });
    
        
        const ctKafkaPublisherMock = {
          publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
            callback(null);
          }),
        };
    
  
        serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
        serviceObj.commonLib.getCvrData = getCvrDataMock;
        serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };
  
            serviceObj.publishCtEvents((error) => {
          
        assert.isNull(error);
        expect(getRetailerDataMock).to.have.callCount(0);
        expect(getCvrDataMock).to.have.callCount(0);
        expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
          done();
        }, fakeRecord);
    
          });

    it('should handle error during publishing', (done) => {
      const fakeRecord = {
        "service":"dth",
        "operator":"suntv",
        "rechargeNumber":"12345",
        "customerId":1234,
        dbData:[
            {rechargeNumber: "12345",
        custId:12345}
        ]
      };
  
      const dbData = [
        {rechargeNumber: "12345",
        custId:12345}
      ];
  
      
      const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(new Error('Get retailer data error'));
      });
  
      
      const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
        cb(new Error('Get CVR data error'));
      });
  
     
      const ctKafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('CT Kafka publish error'));
        }),
      };
  

      serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
      serviceObj.commonLib.getCvrData = getCvrDataMock;
      serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };
  
        serviceObj.publishCtEvents((error) => {
       
        assert.instanceOf(error, Error);
        expect(getRetailerDataMock).to.have.callCount(1);
        expect(getCvrDataMock).to.have.callCount(0);
        expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
        done();
      }, fakeRecord);
  
      });

  it('should handle error during publishing cvrmock', (done) => {
    const fakeRecord = {
      "service":"dth",
        "operator":"suntv",
      "rechargeNumber":"12345",
      "customerId":1234,
      dbData:[
          {rechargeNumber: "12345",
      custId:12345}
      ]
    };

    const dbData = [
      {rechargeNumber: "12345",
      custId:12345}
    ];

    
    const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(null);
      });

    
    const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
      cb(new Error('Get CVR data error'));
    });

   
    const ctKafkaPublisherMock = {
      publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
        callback(new Error('CT Kafka publish error'));
      }),
    };


    serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
    serviceObj.commonLib.getCvrData = getCvrDataMock;
    serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };

    serviceObj.publishCtEvents((error) => {
     
      assert.instanceOf(error, Error);
      expect(getRetailerDataMock).to.have.callCount(1);
      expect(getCvrDataMock).to.have.callCount(1);
      expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
      done();
    }, fakeRecord);

  });

  it('should handle error during publishing ct error', (done) => {
    const fakeRecord = {
      "service":"dth",
        "operator":"suntv",
      "rechargeNumber":"12345",
      "customerId":1234,
      dbData:[
          {rechargeNumber: "12345",
      custId:12345}
      ]
    };

    const dbData = [
      {rechargeNumber: "12345",
      custId:12345}
    ];

    
    const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(null);
      });

    
    const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
      cb(null);
    });

   
    const ctKafkaPublisherMock = {
      publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
        callback(new Error('CT Kafka publish error'));
      }),
    };


    serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
    serviceObj.commonLib.getCvrData = getCvrDataMock;
    serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };

    serviceObj.publishCtEvents((error) => {
     
      assert.instanceOf(error, Error);
      expect(getRetailerDataMock).to.have.callCount(1);
      expect(getCvrDataMock).to.have.callCount(1);
      expect(ctKafkaPublisherMock.publishData).to.have.callCount(1);
      done();
    }, fakeRecord);

  });
});


// describe('DTH Async function publishInBillFetchKafka', () => {
//   let serviceObj;

//   before(() => {
//       STARTUP_MOCK.init(function(error, options){
//           serviceObj = new dthSmsParsing(options);
//     });
//    });

//   // it('should publish data to REMINDER_BILL_FETCH_REALTIME topic', (done) => {
//   //   const fakeProcessedRecord = {
//   //       "isRuSmsParsing":true,
//   //       "isDwhSmsParsing":true,
//   //       "isMigrated":true,
//   //       "dbData":[
//   //         {"notification_status":1,
//   //       "is_automatic":0}
//   //       ]
//   //      };

//   //   const billFetchKafkaPublisherMock = {
//   //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
//   //       callback(null);
//   //     }),
//   //   };

//   //   serviceObj.parent = { billFetchKafkaPublisher: billFetchKafkaPublisherMock };

    
//   //   serviceObj.publishInBillFetchKafka((error) => {
      
//   //     assert.isNull(error);

//   //     expect(billFetchKafkaPublisherMock.publishData).to.have.been.calledOnce;

//   //     done();
//   //   }, fakeProcessedRecord);

//   //   });

//   // it('should handle error during publishing', (done) => {
//   //   const fakeProcessedRecord = {
//   //     "isRuSmsParsing":true,
//   //     "isDwhSmsParsing":true,
//   //     "isMigrated":true,
//   //     "dbData":[
//   //       {"notification_status":1,
//   //     "is_automatic":0}
//   //     ]
//   //   };

    
//   //   const billFetchKafkaPublisherMock = {
//   //     publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
//   //       callback(new Error('Kafka publish error'));
//   //     }),
//   //   };

//   //   serviceObj.parent = { billFetchKafkaPublisher: billFetchKafkaPublisherMock };

    
//   //   serviceObj.publishInBillFetchKafka((error) => {
     
//   //     assert.isNull(error);

//   //     expect(billFetchKafkaPublisherMock.publishData).to.have.been.calledOnce;

//   //     done();
//   //   }, fakeProcessedRecord);

//   //   });

//   // it('not publishing if isRuSmsParsing is false', (done) => {
//   //     const fakeProcessedRecord = {
//   //       "isRuSmsParsing":false
//   //     };
  
      
//   //     const billFetchKafkaPublisherMock = {
//   //       publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
//   //         callback(new Error('Kafka publish error'));
//   //       }),
//   //     };
  
//   //     serviceObj.parent = { billFetchKafkaPublisher: billFetchKafkaPublisherMock };
  
      
//   //     serviceObj.publishInBillFetchKafka((error) => {
       
//   //       assert.isNull(error);
  
//   //         expect(billFetchKafkaPublisherMock.publishData).to.have.callCount(0);
  
//   //       done();
//   //     }, fakeProcessedRecord);
  
//   //       });
  
// });



describe("Module: DTH  SMS Parsing suite :: Record processing", function () {
  let serviceObj;

  let data, record;
  before(function () {    
      STARTUP_MOCK.init(function(error, options){
          serviceObj = new dthSmsParsing(options);
          done();
      });
  });
  
  it("processRecords  | error in validate function", (done) => {
      let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
      let processedRecord = { "operator": "suntv" ,
      "service": 'dth',
      "customerId": 227316905,
      "rechargeNumber": "223939864",
      "gateway": null,
      "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
      "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
      "dueDate": record.dueDate ? (record.dueDate) : null,
      "amount": record.amount,
      "status": 4,
      "paytype": 'prepaid',
      "cache": null,
      "service_id": 0,
      "customerMobile":"1234567890" ,
      "customerEmail": _.get(record, 'smsReceiverEmail', null),
      'paymentChannel': null,
      "retryCount": 0,
      "reason": null,
      "extra": null,
      "customer_type":null, 
      "paymentDate": null,
      "user_data":null,
      "circle":'all circles',
      "productId": 11,
      "tableName" : 'bills_airtel',
      "recordFoundOfSameCustId":true
      };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done("error in validateAndProcessedRecord", processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, 'update', processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null, 'update', processedRecord);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(0);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(0);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });

  it("processRecords  | error in getForward action function", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'bills_airtel',
    "recordFoundOfSameCustId":true
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done("error in getForwardActionFlow");
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null, 'update', processedRecord);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(0);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });

  it("processRecords  | error in update cassandra function", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'bills_airtel',
    "recordFoundOfSameCustId":true
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "findAndUpdateToCassandra", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done("Error in updateCassandra");
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(1);
          expect(stub4).to.have.callCount(0);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });

  it("processRecords  | error in publishInKafka function", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'bills_airtel',
    "recordFoundOfSameCustId":true
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "update", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done("error in publishInKafka");
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(1);
          expect(stub5).to.have.callCount(1);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });

  it("processRecords  | error in update bills db record function", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'bills_airtel',
    "recordFoundOfSameCustId":true
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "update", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done("Error in updatedbRecord");
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(1);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });
  it("processRecords  | error in update plan_validity db record function", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'plan_validity',
    "recordFoundOfSameCustId":true
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "plan_validity_update", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done("Error in updatedbRecord");
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(1);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });
  it("processRecords  | error in update billsrecord notfound findAndUpdateToCassandra flow", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'plan_validity',
    "recordFoundOfSameCustId":false
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "findAndUpdateToCassandra", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done("Error in updateCassandra");
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(1);
          expect(stub4).to.have.callCount(0);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });

  it("processRecords  | Cassandra insert | record Not Found Of SameCustId", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'plan_validity',
    "recordFoundOfSameCustId":false
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "update", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done("error update cassandra");
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(1);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });

  it("processRecords  | Cassandra insert | record Not Found Of SameCustId plan validity", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "tableName" : 'plan_validity',
    "recordFoundOfSameCustId":false
    };

      let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
          return done(null, "plan_validity_update", processedRecord);
      });

      let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
          return done(null);
      });

      let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
          return done("error update cassandra");
      });

      let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
          return done(null, processedRecord);
      });

      // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
      //     return done(null, processedRecord);
      // });


      serviceObj.processRecord(record, function (error) {
          expect(error).to.be.equal(undefined);
          expect(stub1).to.have.callCount(1);
          expect(stub2).to.have.callCount(1);
          expect(stub3).to.have.callCount(0);
          expect(stub4).to.have.callCount(1);
          expect(stub5).to.have.callCount(0);
          // expect(stub6).to.have.callCount(1);

          stub1.restore();
          stub2.restore();
          stub3.restore();
          stub4.restore();
          stub5.restore();
          // stub6.restore();

          return done();
      });
  });
  
  it("processRecords  | billsrecord notfound findAndUpdateToCassandra flow", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "isMigrated":true,
    "tableName" : 'plan_validity',
    "recordFoundOfSameCustId":false
    };

      let stub1 = sinon.stub(serviceObj, 'getRecordsFromDb').callsFake(function fakeFn(done) {
          return done(null, false);
      });

      serviceObj.getForwardActionFlow(function (error,action) {
          expect(error).to.be.equal(null);
          expect(stub1).to.have.callCount(1);
      

          stub1.restore();
        
          return done(null, 'update', processedRecord);
      },processedRecord);
  });

  it("processRecords  | plan validity not found findAndUpdateToCassandra flow", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "isMigrated":false,
    "tableName" : 'plan_validity',
    "recordFoundOfSameCustId":false
    };

      let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
          return done(null, false);
      });

      serviceObj.getForwardActionFlow(function (error,action) {
          expect(error).to.be.equal(null);
          expect(stub1).to.have.callCount(1);
      

          stub1.restore();
        
          return done(null, 'plan_validity_update', processedRecord);
      },processedRecord);
  });

  it("processRecords  | record found smsduedate < dbduedate", (done) => {

    let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
    let processedRecord = { "operator": "suntv" ,
    "service": 'dth',
    "customerId": 227316905,
    "rechargeNumber": "223939864",
    "gateway": null,
    "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    "dueDate": record.dueDate ? (record.dueDate) : null,
    "amount": record.amount,
    "status": 4,
    "paytype": 'prepaid',
    "cache": null,
    "service_id": 0,
    "customerMobile":"1234567890" ,
    "customerEmail": _.get(record, 'smsReceiverEmail', null),
    'paymentChannel': null,
    "retryCount": 0,
    "reason": null,
    "extra": null,
    "customer_type":null, 
    "paymentDate": null,
    "user_data":null,
    "circle":'all circles',
    "productId": 11,
    "isMigrated":true,
    "tableName" : 'bills_suntv',
    "recordFoundOfSameCustId":true,
    "dbData":[{"due_date":"3024-10-07"}]
    };

      let stub1 = sinon.stub(serviceObj, 'getRecordsFromDb').callsFake(function fakeFn(done) {
          return done(null, true);
      });
      serviceObj.getForwardActionFlow(function (error,action) {
          expect(error).to.be.equal('smsDueDate less than dbDueDate');
          expect(stub1).to.have.callCount(1);
          stub1.restore();
        
          return done(null);
      },processedRecord);
  });

  // it("processRecords  | plan validity record found smsduedate < dbduedate", (done) => {

  //   let record = {"cId":"227316905","smsDateTime":1611296208802,"timestamp":"Tue May 07 08:35:10 UTC 2024","deviceDateTime":1620903472035,"collector_timestamp":1715070910009,"uploadTime":1620903472035,"smsOperator":"MYDTUH","smsReceiver":"+************","smsSenderID":"CP-MYDTUH","smsUUID":"689b0c7d-1966-4acc-a185-9c6a544e438d","category":"dth_bill_payment","rechargeNumber":"223939864","operator":"suntv","amount":null,"dueDate":String(MOMENT().add(1,'months').format('YYYY-MM-DD')),"rechargeNumber2":null,"level_2_category":"1","circle":null,"isDwhSmsParsing":true} ;
  //   let processedRecord = { "operator": "suntv" ,
  //   "service": 'dth',
  //   "customerId": 227316905,
  //   "rechargeNumber": "223939864",
  //   "gateway": null,
  //   "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
  //   "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
  //   "dueDate": record.dueDate ? (record.dueDate) : null,
  //   "amount": record.amount,
  //   "status": 4,
  //   "paytype": 'prepaid',
  //   "cache": null,
  //   "service_id": 0,
  //   "customerMobile":"1234567890" ,
  //   "customerEmail": _.get(record, 'smsReceiverEmail', null),
  //   'paymentChannel': null,
  //   "retryCount": 0,
  //   "reason": null,
  //   "extra": null,
  //   "customer_type":null, 
  //   "paymentDate": null,
  //   "user_data":null,
  //   "circle":'all circles',
  //   "productId": 11,
  //   "isMigrated":false,
  //   "tableName" : 'bills_suntv',
  //   "recordFoundOfSameCustId":true,
  //   "dbData":[{"validity_expiry_date":"3024-10-07"}]
  //   };

  //     let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
  //         return done(null, true);
  //     });

  //     serviceObj.getForwardActionFlow(function (error,action) {
  //         expect(error).to.be.equal('smsDueDate less than validity_expiry_date');
  //         expect(stub1).to.have.callCount(1);
  //         stub1.restore();
        
  //         return done(null);
  //     },processedRecord);
  // });

  
});