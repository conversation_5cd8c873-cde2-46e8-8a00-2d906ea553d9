/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach } from 'mocha';

import chai, { assert } from "chai";
import sinon from 'sinon';
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import _ from 'lodash';

import chaiAsPromised from "chai-as-promised";
import ValidationSync from '../../services/validationSync';

import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

const data = {
    "@timestamp": "2020-10-28T06:32:15.630Z",
    "@version": "1",
    "catalogProductID": 202,
    "connectionError": null,
    "currentGw": "bharatbillpay",
    "currentTime": "2020-10-28T06:32:15.626Z",
    "customerInfo_channel_id": "androidapp 8.14.0",
    "customerInfo_customer_email": "",
    "customerInfo_customer_firstname": "",
    "customerInfo_customer_id": 1,
    "customerInfo_customer_mobile": 8173832332,
    "customerInfo_customer_type": 1,
    "customerInfo_remote_ip": "**************",
    "errorMessageCode": 5,
    "gwMachineId": "counode_onusbbps_v1_36_97",
    "noBill": null,
    "originalPid": 202,
    "productInfo_brand": "Airtel",
    "productInfo_category_id": 21,
    "productInfo_circle": "jammu kashmir",
    "productInfo_merchant_id": 2,
    "productInfo_operator": "airtel",
    "productInfo_paytype": "postpaid",
    "productInfo_producttype": "",
    "productInfo_service": "mobile",
    "productInfo_validationtimeout": 120000,
    "productInfo_verticalId": 4,
    "publishToCluster": null,
    "rechargeGwResponse_operatorResponseCode": "BFR003",
    "routeToGwForValidation": true,
    "serverId": "ffrbluenode_billpayments_v1_36_228_32103",
    "timestamps_current": "2020-10-28 12:02:14",
    "timestamps_init": "2020-10-28 12:02:14",
    "useRedisData": null,
    "userData_amount": 249,
    "userData_category_id": "21",
    "userData_channel_id": "330a4567-d701-4542-824f-817f9eb220b1",
    "userData_price": 249,
    "userData_recharge_number": "9541637582",
    "userData_recharge_number_2": "sample rech_num2",
    "userData_recharge_number_2_length": 0,
    "userData_recharge_number_length": 10,
    "userData_totalamount": "249",
    "validationSuccessful": true,
    "customerDataResponse": {
        "currentBillAmount": 600,
        "billDueDate": MOMENT().add(2, 'months').format('YYYY-MM-DD hh:mm:ss'),
        "billDate": "2020-10-10 12:00:00"
    },
    "gwParamsToPass":{
        "isValidityExpired":"1"
    }
};
    

describe("ValidationSync Kafka consumer validations", () => {
    let serviceObj;

    beforeEach((done) => {
        STARTUP_MOCK.init((error, options) => {
            serviceObj = new ValidationSync(options)
            // clock.tick(40 * 60 * 1000)
            done();
        })
    });

    it("setVarFromDynamicConfig funcation ", () => {
        let stub2 = sinon.stub(serviceObj, 'setVarFromDynamicConfig');
        serviceObj.setVarFromDynamicConfig();
        expect(stub2).to.have.callCount(1);
    })

    it("setVarFromDynamicConfig funcation ", () => {
        let clock = sinon.useFakeTimers();
        serviceObj.greyScaleEnv = true;
        serviceObj.setVarFromDynamicConfig();
        clock.tick(40 * 60 * 1000)
    })

    it("setVarFromDynamicConfig function : update recent_bills_operator ", () => {
        let og = _.get(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{});
        _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{airtel : 'bills_airtel2'});
        
        serviceObj.setVarFromDynamicConfig()
        assert.deepEqual(serviceObj.recent_bills_operators.airtel,'bills_airtel2')
        // /** Revert to original table */
        _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],og);
        serviceObj.setVarFromDynamicConfig()
    })


    // it("testing initialize consumer | default values", () => {
    //     let cb = sinon.spy();
    //     serviceObj._configureKafkaPublisher(cb);
    //     expect(cb).to.have.callCount(1);
    //     expect(cb).to.have.calledWith(null)
        
    // })

    
    // it("testing initialize consumer | _configureKafkaPublisher", async () => {
    //     // sinon.stub(serviceObj.kafkaPublisher, "initProducer").callsFake(function fakeFn(type, callback){
    //     //     return callback("Error from _configureKafkaPublisher");
    //     // });
    //     // sinon.stub(serviceObj.infraUtils.kafka, "producer").callsFake(function fakeFn(callback){
    //     //     return;
    //     // });
    //     // serviceObj._configureKafkaPublisher((error)=>{
    //     //     if (error) {
    //     //         expect(error).to.be.equal("Error while making kafka connection for topic AUTOMATIC_SYNC");
    //     //     }
    //     // });
    //     //
    //     serviceObj.kafkaPublisher = new serviceObj.infraUtils.kafka.producer();
    //     sinon.stub(serviceObj.kafkaPublisher, "initProducer").callsFake(function fakeFn(callback){
    //         return callback("Error from initProducer");
    //     });
    //     serviceObj._configureKafkaPublisher((error)=>{
    //         if (error) {
    //             expect(error).to.be.equal("Error from initProducer");
    //         }
    //     });


    // })

    

    it("testing configureKafka function | Error case from _configureKafkaPublisher function",()=>{
        sinon.stub(serviceObj, "_configureKafkaPublisher").callsFake(function fakeFn(callback){
            return callback("Error from _configureKafkaPublisher");
        });
        serviceObj.configureKafka((error)=>{
            if (error) {
                expect(error).to.be.equal("Error from _configureKafkaPublisher");
            }
        });
    });

    it("testing configureKafka function | Error case from initConsumer function",()=>{
        serviceObj.kafkaBillFetchConsumer = new serviceObj.infraUtils.kafka.consumer();
        sinon.stub(serviceObj.kafkaBillFetchConsumer, "initConsumer").callsFake(function fakeFn(callback){
            return callback("Error from initConsumer");
        });
        serviceObj.configureKafka((error)=>{
            if (error) {
                expect(error).to.be.equal("Error from initConsumer");
            }
        });
    });

    it("testing configureKafka function | success ",()=>{
        sinon.stub(serviceObj, "_configureKafkaPublisher").callsFake(function fakeFn(callback){
            return callback(null);
        });
        serviceObj.kafkaBillFetchConsumer = new serviceObj.infraUtils.kafka.consumer();
        sinon.stub(serviceObj.kafkaBillFetchConsumer, "initConsumer").callsFake(function fakeFn(callback){
            return callback(null);
        });
        serviceObj.configureKafka((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);
            }
        });
    });

    it("execSteps || ensure empty records are validated", () => {
        let record = {}
        let processedRecords = serviceObj.execSteps(record);
        expect(processedRecords).to.be.equal(undefined)
    })
    it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
        let record = [
        ];
        let stub1 = sinon.stub(serviceObj, "processRecord").resolves();
        sinon.stub(process, 'exit');
        let clock = sinon.useFakeTimers();
        serviceObj.kafkaBillFetchConsumer = {
            _pauseConsumer : () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb("Error in commitOffset");
            },
            _resumeConsumer: () => {
            }
        };
        serviceObj.greyScaleEnv = true;
        
        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(0);
        process.exit.restore();
    })
    it("execSteps || greyScaleEnv || commitOffset function", () => {
        let record = [
        ];
        let stub1 = sinon.stub(serviceObj, "processRecord").resolves();
        let clock = sinon.useFakeTimers();

        serviceObj.kafkaBillFetchConsumer = {
            _pauseConsumer : () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb();
            },
            _resumeConsumer: () => {

            }
        };
        serviceObj.greyScaleEnv = true;
        
        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(0);
    })
    
    it("execSteps || processBatch function", () => {
        let payLoad = {
            value: JSON.stringify(data)
        };
        let record = [
            {...payLoad}
        ];
        let clock = sinon.useFakeTimers();
        let stub1 = sinon.stub(serviceObj, 'processBatch').yields();
        
        serviceObj.kafkaBillFetchConsumer = {
            _pauseConsumer : () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb();
            },
            _resumeConsumer: () => {
            }
        };
        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(1);
    })

    it("convertKafkaPayloadToRecord || ensure JSON records are validated", () => {
        let record = {
            value: '[a,b,v,c,d]'
        }    
        serviceObj.convertKafkaPayloadToRecord(record)
        .then((convertedRecords)=>{
            expect(convertedRecords).to.be.equal(undefined);
        })
        
    })

    it("start funcation || setConsentData error case", () => {
        sinon.stub(serviceObj, "setConsentData").callsFake(function fakeFn(callback){
            return callback("setConsentData error response callback");
        });
        let stub2 = sinon.stub(serviceObj, 'configureKafka').yields(null);

        serviceObj.start((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);
            }
        });
        expect(stub2).to.have.callCount(0);
    })

    it("start funcation || configureKafka error case", () => {
        let stub2 = sinon.stub(serviceObj, 'setConsentData').yields(null);
        sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
            return callback("configureKafka error response callback");
        });        
        serviceObj.start((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);
            }
        });
        expect(stub2).to.have.callCount(1);
    })
    
    it("start funcation || success execution in 1st recursive call || Error in setConsentData", () => {
        serviceObj.configureKafka = ()=>{
            return;
        }        
        let stub2 = sinon.stub(serviceObj, "setConsentData").callsFake((function fakeFn(callback) {
            let flag = false;
            return function (callback) {    // closures used to toggle flag & send alternate callback params.
                flag = !flag;
                if(flag) 
                    return callback(null);
                else
                    return callback("setConsentData error response callback");
            };
        })());

        serviceObj.consentData = { 'productId1' : true , 'productId2' : true };
        sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
            return callback(null);
        });       
        let clock = sinon.useFakeTimers();
        serviceObj.start((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);            
            }
        });
        expect(stub2).to.have.callCount(1);// need to be verified its location or count
        clock.tick(86400000) 
    })

    it("start funcation || Error in configureKafka", () => {
        serviceObj.configureKafka = (callback)=>{
            return callback("Error in configureKafka");
        }        
        let stub2 = sinon.stub(serviceObj, "setConsentData").callsFake((function fakeFn(callback) {
            let flag = false;
            return function (callback) {
                flag = !flag;
                if(flag) 
                    return callback(null);
                else
                    return callback("setConsentData error response callback");
            };
        })());

        serviceObj.consentData = { 'productId1' : true , 'productId2' : true };
        sinon.stub(process, 'exit');     
        let clock = sinon.useFakeTimers();
        serviceObj.start((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);            
            }
        });
        expect(stub2).to.have.callCount(1);
        clock.tick(86400000) 
        process.exit.restore();
    })

    it("start funcation || success execution", () => {
        serviceObj.configureKafka = ()=>{
            return;
        }
        serviceObj.setConsentData = ()=>{
            return;
        }
        serviceObj.consentData = { 'productId1' : true , 'productId2' : true };
        let stub2 = sinon.stub(serviceObj, 'setConsentData').yields(null);
        sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
            return callback(null);
        });       
        let clock = sinon.useFakeTimers();

        serviceObj.start((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);            
            }
        });
        expect(stub2).to.have.callCount(1);
        clock.tick(86400000) 
    })
     
})

describe("Module: ValidationSync::", function () {
    let validationSync;
    let payLoad = {
        value: JSON.stringify(data)
    };

    beforeEach(function (done) {
        STARTUP_MOCK.init(function(error, options){
            validationSync = new ValidationSync(options);
            done();
        });               
    });

    it("setConsentData | remindable boolean cases true or false", ()=>{
        validationSync.setConsentData((error)=>{
            if(Object.keys(validationSync.consentData).length) {
                expect(validationSync.consentData['202']).to.be.equal(false);
                expect(validationSync.consentData['234']).to.be.equal(true);
            }
        })
    });

    it("setConsentData | remindable not set", ()=>{
       validationSync.setConsentData((error)=>{
           if(Object.keys(validationSync.consentData).length) {
               expect(validationSync.consentData['235']).to.be.equal(false);
           }
       })
   });

   it("setConsentData | where clause", ()=>{
        validationSync.consentData['product_id'] = true;
        validationSync.setConsentData((error)=>{
            if(Object.keys(validationSync.consentData).length) {
                expect(validationSync.consentData['235']).to.be.equal(false);
            }
        })
    })


    it("setConsentData | Exception handling cases", ()=>{
        sinon.stub(validationSync.catalogVerticalRecharge, "getCvrData").callsFake(function fakeFn(callback){
            return callback("Exception", null);
        });
        validationSync.setConsentData((error)=>{
            if (error) {
                expect(error).to.be.equal("Exception");
            }    
        })
    });

    it("setConsentData | where cause cases", ()=>{
        let whereClauseCondition = '';
        sinon.stub(validationSync.catalogVerticalRecharge, "getCvrData").callsFake(function fakeFn(callback){
            return callback(null, []);
        }, whereClauseCondition);
        validationSync.setConsentData((error)=>{
            if (error) {
                expect(error).to.be.equal(undefined);
            }    
        })
    });


    it("setConsentData | mock mysql data", ()=>{
        let whereClauseCondition = '';
        sinon.stub(validationSync.catalogVerticalRecharge, "getCvrData").callsFake(function fakeFn(callback){
            return callback(null, {});
        }, whereClauseCondition);
        validationSync.setConsentData((error)=>{
            expect(error).to.be.an('error');
        })
    });

     
        it("convertKafkaPayloadToRecord | Payload has all information", () => {
             validationSync.convertKafkaPayloadToRecord(payLoad)
             .then((record) =>{
                expect(record).to.have.all.keys('operator', 'validationSuccess','billDate', 'customer_type','user_data', 'dueDate','extendedDataUsed', 'rechargeNumber', 'customerId', 'amount', 'paytype', 'service', 'productId', 'cache', 'categoryId', 'billFetchDate','circle','custInfoValues','customerEmail','customerMobile','extra','gateway','paymentDate','paymentChannel','validationChannel','service_id','reason','retryCount','validationTimeStamp','billAlreadyPaid','noBill','skipBillCheck', 'limitExpired','source');
                expect(record.operator).to.be.equal('airtel');
                expect(record.service).to.be.equal('mobile');
                expect(record.paytype).to.be.equal('postpaid');
                expect(record.rechargeNumber).to.be.equal('9541637582');
                expect(record.customerId).to.be.equal(1);
                expect(record.circle).to.be.equal('jammu kashmir');
                expect(record.productId).to.be.equal(202);
                expect(record.amount).to.be.equal(600);
                expect(record.customerMobile).to.be.equal(8173832332);
                expect(record.validationSuccess).to.be.equal(true);
                expect(record.isValidityExpired).to.be.equal(true);
             })
            
        });
    
        it("convertKafkaPayloadToRecord | if product id is null", () => {
            let p = _.clone(data);
            p.catalogProductID = null;
            let payLoad = {
                value: JSON.stringify(p)
            };
             validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record) =>{
                console.log(record)
                expect(record).to.have.all.keys('operator','validationSuccess', 'billDate', 'user_data','dueDate', 'extendedDataUsed','rechargeNumber', 'customer_type','customerId', 'amount', 'paytype', 'service', 'productId', 'cache', 'categoryId','billFetchDate','circle','custInfoValues','customerEmail','customerMobile','extra','gateway','paymentDate','paymentChannel','validationChannel','service_id','reason','retryCount','validationTimeStamp','billAlreadyPaid','noBill','skipBillCheck', 'limitExpired','source');
                expect(record.productId).to.be.equal(null);
            })
            
        });
    
        it("convertKafkaPayloadToRecord | if customerId is null", () => {
            let p = _.clone(data);
            p.customerInfo_customer_id = null;
            let payLoad = {
                value: JSON.stringify(p)
            };
            validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record) =>{
                expect(record).to.have.all.keys('operator','validationSuccess', 'billDate', 'user_data','dueDate', 'extendedDataUsed','rechargeNumber', 'customer_type', 'customerId', 'amount', 'paytype', 'service', 'productId', 'cache', 'categoryId','billFetchDate','circle','custInfoValues','customerEmail','customerMobile','extra','gateway','paymentDate','paymentChannel','validationChannel','service_id','reason','retryCount','validationTimeStamp','billAlreadyPaid','noBill','skipBillCheck','limitExpired','source');
                expect(record.customerId).to.be.equal(null);
            })
            
        });
    
    
        it("convertKafkaPayloadToRecord | if rechargeNumber is null", () => {
            let p = _.clone(data);
            p.userData_recharge_number = null;
            let payLoad = {
                value: JSON.stringify(p)
            };
            validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record) =>{
                expect(record).to.have.all.keys('operator','validationSuccess', 'billDate', 'user_data','dueDate', 'extendedDataUsed','rechargeNumber', 'customerId', 'amount', 'paytype', 'service', 'productId', 'cache', 'categoryId','billFetchDate','circle','custInfoValues','customerEmail','customerMobile','customer_type','extra','gateway','paymentDate','paymentChannel','validationChannel','service_id','reason','retryCount','validationTimeStamp','billAlreadyPaid','noBill','skipBillCheck','limitExpired','source');
                expect(record.rechargeNumber).to.be.equal(null);
            })
            
        });

        it("convertKafkaPayloadToRecord | if gwParamsToPass is null", () => {
            let p = _.clone(data);
            p.gwParamsToPass = null;
            let payLoad = {
                value: JSON.stringify(p)
            };
            validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record) =>{
                expect(record).to.have.all.keys('operator','validationSuccess', 'billDate', 'user_data','dueDate', 'extendedDataUsed','rechargeNumber', 'customerId', 'amount', 'paytype', 'service', 'productId', 'cache', 'categoryId','billFetchDate','circle','custInfoValues','customerEmail','customerMobile','customer_type','extra','gateway','paymentDate','paymentChannel','validationChannel','service_id','reason','retryCount','validationTimeStamp','billAlreadyPaid','noBill','skipBillCheck','limitExpired','source');
                expect(record.isValidityExpired).to.be.equal(false);
            })
            
        });
    
        // it.skip('validateKafkaPayload:: _isRemindable false', () => {
        //     let record = validationSync.convertKafkaPayloadToRecord(payLoad);
        //     let errorResponse = validationSync.validateKafkaPayload(record);
        //     expect(errorResponse).to.be.equal('_isRemindable false for rech_num:9541637582::operator:airtel::productId:202::custId:1');
        // })
    
        it('validateKafkaPayload:: Operator is not migrated', () => {
            _.set(validationSync.config,['DYNAMIC_CONFIG','OPERATOR_CONFIG','airtel','DISABLE_VALIDATION_SYNC'],true)
            validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record) =>{
                let errorResponse = validationSync.validateKafkaPayload(record);
                expect(errorResponse).to.match(/Validation Sync disabled/g);
            })
            
        })

        it('validateKafkaPayload:: store in dropped tranaction table when  operator is down and channel is not mentioned', async () => {
            let vs = _.cloneDeep(validationSync);
            vs.operatorUpAllowedCategoryOperatorMap = { '21': ['airtel', 'jio'] };
            vs.operatorUpAmountAllowedCategory = [21];
            vs.droppedTransactions.getDroppedTransactionByCustAndRecharge = sinon.spy();
            let record = {extendedDataUsed: false, custInfoValues: {billerDown:true} , categoryId: '21' , operator: 'airtel'};
            let errorResponse = await vs.validateKafkaPayload(record);
            expect(vs.droppedTransactions.getDroppedTransactionByCustAndRecharge).to.have.callCount(0)
        })

        it('validateKafkaPayload:: store in dropped tranaction table when  operator is down and channel is android', async () => {
            let vs = _.cloneDeep(validationSync);
            vs.operatorUpAllowedCategoryOperatorMap = { '21': ['airtel', 'jio'] };
            vs.operatorUpAmountAllowedCategory = [21];
            vs.droppedTransactions.getDroppedTransactionByCustAndRecharge = sinon.spy();
            let record = {extendedDataUsed: false, custInfoValues: {billerDown:true}, validationChannel: "androidapp 11.2.0", service: "mobile", categoryId: '21', operator: 'airtel'};
            let errorResponse = await vs.validateKafkaPayload(record);
            expect(vs.droppedTransactions.getDroppedTransactionByCustAndRecharge).to.have.callCount(1)
        })

        it('validateKafkaPayload:: handle biller down scenario with valid parameters', async () => {
            let vs = _.cloneDeep(validationSync);
            // Setup dependencies
            vs.operatorUpAllowedCategoryOperatorMap = { '21': ['airtel', 'jio'] };
            vs.operatorUpAmountAllowedCategory = [21];
            vs.droppedTransactions.getDroppedTransactionByCustAndRecharge = sinon.stub().callsFake((callback, customerId, rechargeNumber) => {
                callback(null, []);
            });
            vs.droppedTransactions.storeDroppedTransaction = sinon.stub().callsFake((callback, record) => {
                callback(null, {});
            });
            vs.billFetchAnalytics = {
                saveAndPublishBillFetchAnalyticsData: sinon.stub().resolves()
            };
            vs.createRecordForAnalytics = sinon.stub().returns({});
            
            // Setup record
            const record = {
                validationSuccess: false,
                extendedDataUsed: false,
                custInfoValues: {
                    billerDown: true
                },
                categoryId: '21',
                validationChannel: 'androidapp 8.0.0',
                operator: 'airtel',
                customerId: '12345',
                rechargeNumber: '9876543210',
                amount: 499,
                user_data: JSON.stringify({})
            };
            
            // Execute
            const result = await vs.validateKafkaPayload(record);
            
            // Assert
            expect(result).to.equal('Invalid record!');
            expect(vs.droppedTransactions.getDroppedTransactionByCustAndRecharge).to.have.been.calledOnce;
            expect(vs.droppedTransactions.storeDroppedTransaction).to.have.been.calledOnce;
            expect(vs.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData).to.have.been.calledOnce;
        })

        it('validateKafkaPayload:: store in dropped tranaction table when  operator is down and value is fetched from extended cache', () => {
            let vs = _.cloneDeep(validationSync);
            vs.droppedTransactions.storeDroppedTransaction = sinon.spy();
            let record = {extendedDataUsed: true, custInfoValues: {billerDown:true}};
            let errorResponse = vs.validateKafkaPayload(record);
            expect(vs.droppedTransactions.storeDroppedTransaction).to.have.callCount(0)
        })
    
        it('validateKafkaPayload:: Paytype is Unknown', () => {
            _.set(validationSync.config,['DYNAMIC_CONFIG','OPERATOR_CONFIG','airtel','DISABLE_VALIDATION_SYNC'],false)
            let p = _.clone(data);
            p.productInfo_paytype = 'new_paytype';
            let payLoad = {
                value: JSON.stringify(p)
            };
            validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record) =>{
                let errorResponse = validationSync.validateKafkaPayload(record);
                expect(errorResponse).to.match(/_isRemindable false for /g);
            })
            
        })
    
        it('validateKafkaPayload:: DueDate is invalid', () => {
            let p = _.clone(data);
            p.customerDataResponse_billDueDate = 'abcd';
            let payLoad = {
                value: JSON.stringify(p)
            };
            validationSync.convertKafkaPayloadToRecord(payLoad)
            .then((record)=>{
                validationSync.validateKafkaPayload(record)
                .then((errorResponse) =>{
                    expect(errorResponse).to.match(/Either amount < 0 || dueDate is invalid || dueDate in past/g);
                })
            })
        })

        it('validateKafkaPayload:: Invalid record!', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "androidapp 8.14.0",
                "validationSuccess": false,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            validationSync.validateKafkaPayload(record)
            .then((errorResponse) =>{
                expect(errorResponse).to.match(/Invalid record/g);

            })
        })

        it('validateKafkaPayload:: disableValidationSync', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "androidapp 8.14.0",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            validationSync.disableValidationSync = true;
            validationSync.validateKafkaPayload(record)
            .then((errorResponse) =>{
                expect(errorResponse).to.match(/Validation Sync Service is Diabled/g);

            })
        })
        it('validateKafkaPayload:: amount == 0', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": 0,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "androidapp 8.14.0",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            validationSync.validateKafkaPayload(record)
            .then((errorResponse) =>{
                expect(errorResponse).to.match(/^Mandatory fields not present::/);
            })
            
        })

        it('validateKafkaPayload:: amount < 0', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": -10,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "androidapp 8.14.0",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            validationSync.validateKafkaPayload(record)
            .then((errorResponse)=>{
                expect(errorResponse).to.match(/^Either amount < 0/);
            })
            
        })

        it('validateKafkaPayload:: validationChannel disabled', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "IOS",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            _.set(validationSync.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'EXCLUDE_CHANNEL_ID'], ['IOS'])
            validationSync.validateKafkaPayload(record)
            .then((errorResponse) =>{
                expect(errorResponse).to.be.equal("Channel_id: IOS is disabled, excludedChannelIds: IOS");

            })
        })

        it('validateKafkaPayload:: success', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "androidapp 8.14.0",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            validationSync.validateKafkaPayload(record)
            .then((errorResponse) =>{
                expect(errorResponse).to.be.equal("");
            })
            
        })

        it('validateKafkaPayload:: success when validationSuccess is false and isValidityExpired is true', () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": MOMENT().add(15,'days').format('YYYY-MM-DD'),
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 12:34:17",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "androidapp 8.14.0",
                "validationSuccess": false,
                "isValidityExpired":true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}"
            };
            validationSync.validateKafkaPayload(record)
            .then((errorResponse) =>{
                expect(errorResponse).to.be.equal("");
            })
            
        })

        it('getNextBillFetchDate:: Get next_bill_fetch_date billDate based operator', () => {
            let p = {
                operator: 'airtel',
                billDate: MOMENT().format('YYYY-MM-DD'),
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
            }
            validationSync.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['airtel'];
            let result = validationSync.getNextBillFetchDate(p);
            expect(result).to.be.equal(MOMENT().add(20, 'days').format('YYYY-MM-DD 00:00:00'));
        })

        it('checkForInvalidDateForCC:: Invalid date for the field nextBillFetchDate for CCBP', () => {
            let p = {
                operator: 'sbi',
                billDate: MOMENT().format('YYYY-MM-DD'),
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
                nextBillFetchDate: 'Invalid date'
            }
            validationSync.checkForInvalidDateForCC(p);
            expect(p.nextBillFetchDate).to.be.equal(MOMENT().add(2, 'days').format('YYYY-MM-DD HH:mm:ss'));
        });
    
        it('getNextBillFetchDate:: Get next_bill_fetch_date dueDate based operator', () => {
            let p = {
                operator: 'power & electricity department - mizoram',
                billDate: MOMENT().format('YYYY-MM-DD'),
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
            }
            let result = validationSync.getNextBillFetchDate(p);
            expect(result).to.be.equal(MOMENT(p.dueDate).add(20, 'days').format('YYYY-MM-DD 00:00:00'));
        })
    
        it('getNextBillFetchDate:: Get next_bill_fetch_date operator whose config is not present', () => {
            let p = {
                operator: 'qwert',
                billDate: MOMENT().format('YYYY-MM-DD'),
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
            }
            let result = validationSync.getNextBillFetchDate(p);
            expect(result).to.be.equal(MOMENT(p.dueDate).add(20, 'days').format('YYYY-MM-DD 00:00:00'));
        })

        it('getNextBillFetchDate:: Get next_bill_fetch_date billDate based operator and billDate is absent', () => {
            let p = {
                operator: 'airtel',
                billDate: null,
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
            }
            validationSync.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['airtel'];
            let result = validationSync.getNextBillFetchDate(p);
            expect(result).to.be.equal(MOMENT().add(35, 'days').format('YYYY-MM-DD 00:00:00'));
        })

        it('getNextBillFetchDate:: Get next_bill_fetch_date dueDate based operator and NBFD < now()', () => {
            let p = {
                operator: 'airtel',
                billDate: null,
                dueDate: MOMENT().add(-45,'days').format('YYYY-MM-DD')
            }
            let result = validationSync.getNextBillFetchDate(p);
            expect(result).to.be.equal(MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss'));  // case value other than mid-night time is stored in DB  
        })
        //when we have to add 30 days. we will be adding 1 month instead of adding days to maintain bill cycle
        it('getNextBillFetchDate:: Get next_bill_fetch_date when NEXT_BILL_FETCH_DATES is -1', () => {
            let p = {
                operator: 'jio',
                billDate: MOMENT().format('YYYY-MM-DD'),
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
            }
            validationSync.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['jio'];
            let result = validationSync.getNextBillFetchDate(p);
            expect(result).to.be.equal(MOMENT(p.billDate).add(1, 'months').format('YYYY-MM-DD HH:mm:ss'));  // case value other than mid-night time is stored in DB  
        })
    
        it('processRecord:: processPayload', () => {
            let p = {
                operator: 'qwert',
                billDate: MOMENT().format('YYYY-MM-DD'),
                dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
            }
            validationSync.processRecord(() => {console.log('done done') },payLoad);
           
        });

        it('processRecord:: processPayload | publishCtEvents reject promise ', async () => {
            let stub1 = sinon.stub(validationSync, "compareAndUpdateDbData").resolves();
            let stub2 = sinon.stub(validationSync, "publishCtEvents").rejects("Error publishCtEvents function");
            let stub3 = sinon.stub(validationSync, "updateBillsInRecent").resolves();
            let processRecordPromise = await validationSync.processRecord(payLoad);    
            expect(processRecordPromise).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);

        });

        it('processRecord:: processPayload | processRecord:: dummy_operator not migrated', async () => {
            let record = {
                "operator": "dummy_operator",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": "2022-02-28 23:59:59",
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 01:41:58",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "SUBS 1",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                "debugKey": "rech_num:9541637582::operator:airtel::productId:202::custId:1",
                "tableName": "bills_airtel",
                "nextBillFetchDate": "2022-03-20 23:59:59",
                "status": 4,
                "skipNotification": true,
                "updateInRecents": false,
                "recordFoundOfSameCustId": false,
                "billsData": {
                    "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                    "nextBillFetchDate": "2022-03-20 23:59:59",
                    "billFetchDate": "2020-10-28 12:02:14",
                    "commonAmount": 600,
                    "commonDueDate": "2022-02-28",
                    "rechargeNumber": "9541637582",
                    "billDate": "2020-10-10",
                    "productId": 202,
                    "commonStatus": 4,
                    "customerId": 1,
                    "customerMobile": 8173832332,
                    "operator": "airtel",
                    "circle": "jammu kashmir",
                    "service": "mobile",
                    "gateway": "bharatbillpay",
                    "retryCount": 0,
                    "reason": null,
                    "paytype": "postpaid",
                    "customerEmail": "",
                    "service_id": 0,
                    "is_automatic": 0,
                    "extra": "{\"billSource\":\"ValidationSync-1970-01-01 06:10:00\",\"lastSuccessBFD\":null,\"billFetchDate\":\"2020-10-28 12:02:14\",\"lastDueDt\":null,\"lastBillDt\":null,\"lastAmount\":null,\"customer_type\":1}",
                    "customerOtherInfo": "{\"currentBillAmount\":600,\"billDueDate\":\"2022-02-28 01:41:58\",\"billDate\":\"2020-10-10 12:00:00\"}"
                }
            };

            let stub1 = sinon.stub(validationSync, 'convertKafkaPayloadToRecord').returns(record);
            let stub2 = sinon.stub(validationSync, 'validateKafkaPayload').returns('');
            let stub3 = sinon.stub(validationSync, "compareAndUpdateDbData").resolves();
            let stub4 = sinon.stub(validationSync, "publishCtEvents").resolves();
            let stub5 = sinon.stub(validationSync, "publishInKafka").resolves();
            validationSync.skipNotification = true;
            let processRecordPromise = await validationSync.processRecord(payLoad);    
            expect(processRecordPromise).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);

        })



        it('processRecord:: processPayload | Notification Disabled from Dynamic Config ', async () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": "2022-02-28 23:59:59",
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 01:41:58",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "SUBS 1",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                "debugKey": "rech_num:9541637582::operator:airtel::productId:202::custId:1",
                "tableName": "bills_airtel",
                "nextBillFetchDate": "2022-03-20 23:59:59",
                "status": 4,
                "skipNotification": true,
                "updateInRecents": false,
                "recordFoundOfSameCustId": false,
                "billsData": {
                    "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                    "nextBillFetchDate": "2022-03-20 23:59:59",
                    "billFetchDate": "2020-10-28 12:02:14",
                    "commonAmount": 600,
                    "commonDueDate": "2022-02-28",
                    "rechargeNumber": "9541637582",
                    "billDate": "2020-10-10",
                    "productId": 202,
                    "commonStatus": 4,
                    "customerId": 1,
                    "customerMobile": 8173832332,
                    "operator": "airtel",
                    "circle": "jammu kashmir",
                    "service": "mobile",
                    "gateway": "bharatbillpay",
                    "retryCount": 0,
                    "reason": null,
                    "paytype": "postpaid",
                    "customerEmail": "",
                    "service_id": 0,
                    "is_automatic": 0,
                    "extra": "{\"billSource\":\"ValidationSync-1970-01-01 06:10:00\",\"lastSuccessBFD\":null,\"billFetchDate\":\"2020-10-28 12:02:14\",\"lastDueDt\":null,\"lastBillDt\":null,\"lastAmount\":null,\"customer_type\":1}",
                    "customerOtherInfo": "{\"currentBillAmount\":600,\"billDueDate\":\"2022-02-28 01:41:58\",\"billDate\":\"2020-10-10 12:00:00\"}"
                }
            };

            let stub1 = sinon.stub(validationSync, 'convertKafkaPayloadToRecord').returns(record);
            let stub2 = sinon.stub(validationSync, "compareAndUpdateDbData").resolves();
            let stub3 = sinon.stub(validationSync, "publishCtEvents").resolves();
            let stub4 = sinon.stub(validationSync, "publishInKafka").resolves();
            validationSync.skipNotification = true;
            let processRecordPromise = await validationSync.processRecord(payLoad);    
            expect(processRecordPromise).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);

        })

        it('processRecord:: processPayload | Skiping Notification because skipNotification flag was set = true ', async () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": "2022-02-28 23:59:59",
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 01:41:58",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "SUBS 1",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                "debugKey": "rech_num:9541637582::operator:airtel::productId:202::custId:1",
                "tableName": "bills_airtel",
                "nextBillFetchDate": "2022-03-20 23:59:59",
                "status": 4,
                "skipNotification": true,
                "updateInRecents": false,
                "recordFoundOfSameCustId": false,
                "billsData": {
                    "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                    "nextBillFetchDate": "2022-03-20 23:59:59",
                    "billFetchDate": "2020-10-28 12:02:14",
                    "commonAmount": 600,
                    "commonDueDate": "2022-02-28",
                    "rechargeNumber": "9541637582",
                    "billDate": "2020-10-10",
                    "productId": 202,
                    "commonStatus": 4,
                    "customerId": 1,
                    "customerMobile": 8173832332,
                    "operator": "airtel",
                    "circle": "jammu kashmir",
                    "service": "mobile",
                    "gateway": "bharatbillpay",
                    "retryCount": 0,
                    "reason": null,
                    "paytype": "postpaid",
                    "customerEmail": "",
                    "service_id": 0,
                    "is_automatic": 0,
                    "extra": "{\"billSource\":\"ValidationSync-1970-01-01 06:10:00\",\"lastSuccessBFD\":null,\"billFetchDate\":\"2020-10-28 12:02:14\",\"lastDueDt\":null,\"lastBillDt\":null,\"lastAmount\":null,\"customer_type\":1}",
                    "customerOtherInfo": "{\"currentBillAmount\":600,\"billDueDate\":\"2022-02-28 01:41:58\",\"billDate\":\"2020-10-10 12:00:00\"}"
                }
            };

            let stub1 = sinon.stub(validationSync, 'convertKafkaPayloadToRecord').returns(record);
            let stub2 = sinon.stub(validationSync, "compareAndUpdateDbData").resolves();
            let stub3 = sinon.stub(validationSync, "publishCtEvents").resolves();
            let stub4 = sinon.stub(validationSync, "publishInKafka").resolves();
            let processRecordPromise = await validationSync.processRecord(payLoad);    
            expect(processRecordPromise).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);

        })

        it('processRecord:: processPayload | publishInKafka ', async () => {
            let record = {
                "operator": "airtel",
                "service": "mobile",
                "customerId": 1,
                "rechargeNumber": "9541637582",
                "gateway": "bharatbillpay",
                "billFetchDate": "1970-01-01 06:10:00",
                "billDate": "2020-10-10",
                "dueDate": "2023-11-28 23:59:59",
                "amount": 600,
                "custInfoValues": {
                    "currentBillAmount": 600,
                    "billDueDate": "2022-02-28 01:41:58",
                    "billDate": "2020-10-10 12:00:00"
                },
                "paytype": "postpaid",
                "productId": 202,
                "validationTimeStamp": "2020-10-28 12:02:14",
                "cache": null,
                "service_id": 0,
                "customerMobile": 8173832332,
                "customerEmail": "",
                "paymentChannel": null,
                "circle": "jammu kashmir",
                "retryCount": 0,
                "reason": null,
                "extra": null,
                "customer_type": 1,
                "paymentDate": null,
                "validationChannel": "SUBS 1",
                "validationSuccess": true,
                "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                "debugKey": "rech_num:9541637582::operator:airtel::productId:202::custId:1",
                "tableName": "bills_airtel",
                "nextBillFetchDate": "2022-03-20 23:59:59",
                "status": 4,
                "skipNotification": false,
                "updateInRecents": false,
                "recordFoundOfSameCustId": false,
                "billsData": {
                    "user_data": "{\"recharge_number_2\":\"sample rech_num2\"}",
                    "nextBillFetchDate": "2022-03-20 23:59:59",
                    "billFetchDate": "2020-10-28 12:02:14",
                    "commonAmount": 600,
                    "commonDueDate": "2022-02-28",
                    "rechargeNumber": "9541637582",
                    "billDate": "2020-10-10",
                    "productId": 202,
                    "commonStatus": 4,
                    "customerId": 1,
                    "customerMobile": 8173832332,
                    "operator": "airtel",
                    "circle": "jammu kashmir",
                    "service": "mobile",
                    "gateway": "bharatbillpay",
                    "retryCount": 0,
                    "reason": null,
                    "paytype": "postpaid",
                    "customerEmail": "",
                    "service_id": 0,
                    "is_automatic": 0,
                    "extra": "{\"billSource\":\"ValidationSync-1970-01-01 06:10:00\",\"lastSuccessBFD\":null,\"billFetchDate\":\"2020-10-28 12:02:14\",\"lastDueDt\":null,\"lastBillDt\":null,\"lastAmount\":null,\"customer_type\":1}",
                    "customerOtherInfo": "{\"currentBillAmount\":600,\"billDueDate\":\"2022-02-28 01:41:58\",\"billDate\":\"2020-10-10 12:00:00\"}"
                }
            };

            let stub1 = sinon.stub(validationSync, 'convertKafkaPayloadToRecord').returns(record);
            let stub2 = sinon.stub(validationSync, "publishCtEvents").resolves();
            let stub3 = sinon.stub(validationSync, "publishInKafka").resolves();
            let stub4 = sinon.stub(validationSync, "publishNonPaytmEvents").resolves();
            let processRecordPromise = await validationSync.processRecord(payLoad);    
            expect(processRecordPromise).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
        })

    it("getRecordsFromDb: Record Found!", () => {
        let record = validationSync.convertKafkaPayloadToRecord(payLoad);
        record.tableName = 'bills_airtel';
        validationSync.getRecordsFromDb(record, (err, data) => {
            expect(data.recordsFound).to.be.equal(true);
            expect(record.is_automatic).to.be.equal(0)
            expect(record.dbData[0].customer_id).to.be.equal(123)
        })
    })


    it("getRecordsFromDb:: Record Not Found!", () => {
        let payLoad2 = _.cloneDeep(payLoad);
        let record = validationSync.convertKafkaPayloadToRecord(payLoad2);
        record.tableName = 'bills_airtel';
        validationSync.getRecordsFromDb(record, (err, data) => {
            expect(data.recordsFound).to.be.equal(false)
        })
    })

    it("getRecordsFromDb:: getBillsOfSameRech mocked || Promise reject getBillsOfSameRech", async () => {
        let payLoad2 = _.cloneDeep(payLoad);
        let record = validationSync.convertKafkaPayloadToRecord(payLoad2);
        record.tableName = 'bills_airtel';
        let stub4 = sinon.stub(validationSync.bills, "getBillsOfSameRech").callsFake(function fakeFn(callback){
            return callback("Promise reject getBillsOfSameRech");
        });
        let getRecordsFromDbPromsie;
        try {
            getRecordsFromDbPromsie = await validationSync.getRecordsFromDb(record);
        } catch (error) {
            expect(getRecordsFromDbPromsie).to.be.equal(undefined)
        }
    })

    it("getRecordsFromDb:: getBillsOfSameRech mocked || Promise resolved with value false getBillsOfSameRech", async () => {
        let payLoad2 = _.cloneDeep(payLoad);
        let record = validationSync.convertKafkaPayloadToRecord(payLoad2);
        record.tableName = 'bills_airtel';
        let stub4 = sinon.stub(validationSync.bills, "getBillsOfSameRech").callsFake(function fakeFn(callback){
            return callback(null, {});
        });
        let getRecordsFromDbPromsie;
        getRecordsFromDbPromsie = await validationSync.getRecordsFromDb(record);
        expect(getRecordsFromDbPromsie).to.be.equal(false)
        
    })

    it("getRecordsFromDb:: getBillsOfSameRech mocked || Promise resolved with value true getBillsOfSameRech", async () => {
        let payLoad2 = _.cloneDeep(payLoad);
        let record = validationSync.convertKafkaPayloadToRecord(payLoad2);
        record.tableName = 'bills_airtel';
        let stub4 = sinon.stub(validationSync.bills, "getBillsOfSameRech").callsFake(function fakeFn(callback){
            return callback(null, [
                {customer_id: 1 , payment_date: MOMENT().add(-1,'days').format('YYYY-MM-DD'), is_automatic: 0},
                {customer_id: 1 , payment_date: MOMENT().add(-2,'days').format('YYYY-MM-DD'), is_automatic: 0},
                {customer_id: 2 , payment_date: MOMENT().add(-4,'days').format('YYYY-MM-DD'), is_automatic: 0}
            ]);
        });
        let getRecordsFromDbPromsie = await validationSync.getRecordsFromDb(record);
        expect(getRecordsFromDbPromsie).to.be.equal(true)
        
    })
    
    it("compareDBRecordWithValidation:: Record Not Found|Create Record", () => {
        let p = _.cloneDeep(data);
        p.userData_recharge_number = "9876543121";
        let payLoad2 = {
            value: JSON.stringify(p)
        };
        let vs = _.cloneDeep(validationSync);
        //payLoad2.userData_recharge_number = "9876543121";
        validationSync.convertKafkaPayloadToRecord(payLoad2)
        .then((record) =>{
            record.recordFoundOfSameCustId = false;
            vs.bills.updateBillForSameRNandCID = sinon.spy();
            vs.bills.createBillOfValidationSync = sinon.spy();
            vs.compareAndUpdateDbData(record, false)
            expect(vs.bills.createBillOfValidationSync).to.have.callCount(1)
        })
        
    })

    it("compareDBRecordWithValidation:: Record Found but same custId Record is not present | Create Record | Update Record", () => {
        let create = (cb) => {
            return cb(null);
        }
        let update = (cb) => {
            return cb(null);
        }
        let vs = _.cloneDeep(validationSync);
        validationSync.convertKafkaPayloadToRecord(payLoad)
        .then((record) =>{
            record.recordFoundOfSameCustId = false;
            vs.bills.updateBillForSameRNandCID = sinon.spy(update);
            vs.bills.createBillOfValidationSync = sinon.spy(create);
            record.dbData = [{due_date: MOMENT().add(10,'days').format('YYYY-MM-DD')}]
            vs.compareAndUpdateDbData(record, true)
            expect(vs.bills.createBillOfValidationSync).to.have.been.calledOnce
        })
        
    })

    it("compareDBRecordWithValidation:: Record Found and same custId Record is present | Update Record", () => {
        let spyFunc = (cb) => {
            return cb(null);
        }
        let vs = _.cloneDeep(validationSync);
        validationSync.convertKafkaPayloadToRecord(payLoad)
        .then((record) =>{
            record.recordFoundOfSameCustId = true;
            vs.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
            vs.bills.createBillOfValidationSync = sinon.spy(spyFunc);
            record.dbData = [{due_date: MOMENT().add(10,'days').format('YYYY-MM-DD')}]
            vs.compareAndUpdateDbData(record, true)
            expect(vs.bills.createBillOfValidationSync).to.have.callCount(0)
            expect(vs.bills.updateBillForSameRNandCID).to.have.callCount(1)
        })
        
    })

    it("compareDBRecordWithValidation:: Error case | createBill execution failed", async() => {
        let spyFunc = (cb) => {
            return cb(null);
        }
        let vs = _.cloneDeep(validationSync);
        let record = validationSync.convertKafkaPayloadToRecord(payLoad);
        record.recordFoundOfSameCustId = false;
        vs.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        let stub4 = sinon.stub(vs, "createBill").rejects("Error while inserting data in createBill function");
        
        record.dbData = [{due_date: MOMENT().add(10,'days').format('YYYY-MM-DD')}]
        vs.compareAndUpdateDbData(record, true)
        // expect(vs.bills.createBillOfValidationSync).to.have.callCount(0)
        expect(vs.bills.updateBillForSameRNandCID).to.have.callCount(0)
    })

    it("compareDBRecordWithValidation:: Don't create & update record when Payment Event already processed", () => {
        let spyFunc = (cb) => {
            return cb(null);
        }
        let vs = _.cloneDeep(validationSync);
        let record = validationSync.convertKafkaPayloadToRecord(payLoad);
        record.recordFoundOfSameCustId = false;
        record.validationTimeStamp = MOMENT().add(-2,'days').format('YYYY-MM-DD');
        record.maxPaymentDate = MOMENT().add(0,'days').format('YYYY-MM-DD');
        
        vs.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        vs.bills.createBillOfValidationSync = sinon.spy(spyFunc);
        
        record.dbData = [{due_date: MOMENT().add(10,'days').format('YYYY-MM-DD')}]
        vs.compareAndUpdateDbData(record, true)
        expect(vs.bills.createBillOfValidationSync).to.have.callCount(0)
        expect(vs.bills.updateBillForSameRNandCID).to.have.callCount(0)
    })

    it("getActiveRecords function || some of the records were active", ()=>{
        let records = [
            {status : 0},
            {status : 4},
            {status : 6},
            {status : 7},
            {status : 11},
            {status : 13}
        ];
        let numberOfActiveRecords = 4;
        let activeRecords = validationSync.getActiveRecords(records);
        expect(activeRecords).to.be.equal(numberOfActiveRecords);
    });
    it("getActiveRecords function || no record is active", ()=>{
        let records = [
        ];
        let numberOfActiveRecords = 0;
        let activeRecords = validationSync.getActiveRecords(records);
        expect(activeRecords).to.be.equal(numberOfActiveRecords);
    });

    it("createBill function || remindable true flow", async ()=>{
        let record = {remindable : true, validationChannel : 'COU'};
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.createBillOfValidationSync = sinon.spy(spyFunc);
        let promiseCreateBill = await validationSync.createBill(record);
        expect(promiseCreateBill).to.be.equal(undefined);    
        expect(validationSync.bills.createBillOfValidationSync).to.have.callCount(0);
    });
    it("createBill function || remindable false ||  validationChannel 'BOU 1' flow ", async ()=>{
        let record = {remindable : false, validationChannel: 'BOU 1'};
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.createBillOfValidationSync = sinon.spy(spyFunc);
        let promiseCreateBill = await validationSync.createBill(record);
        expect(promiseCreateBill).to.be.equal(undefined);    
        expect(validationSync.bills.createBillOfValidationSync).to.have.callCount(0);
    });
    it("createBill function || remindable false ||  validationChannel 'BOU 1' || createBillOfValidationSync error", async ()=>{
        let record = {remindable : false, validationChannel: 'COU'};
        let spyFunc = (cb) => {
            return cb("createBillOfValidationSync callback error");
        };
        validationSync.bills.createBillOfValidationSync = sinon.spy(spyFunc);
        try {
            let promiseCreateBill = await validationSync.createBill(record);
            expect(promiseCreateBill).to.be.equal(undefined);    
        } catch(rejectedPromiseError) {
            expect(rejectedPromiseError).to.be.equal("createBillOfValidationSync callback error");
            expect(validationSync.bills.createBillOfValidationSync).to.have.callCount(1);
        }
    });
    it("createBill function || remindable false ||  validationChannel 'BOU 1' || createBillOfValidationSync success || skipNotification false & sendAllNotifications true", async ()=>{
        let record = {remindable : false, validationChannel: 'SUBS 1'};
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.createBillOfValidationSync = sinon.spy(spyFunc);
        let promiseCreateBill = await validationSync.createBill(record);
        expect(promiseCreateBill).to.be.equal(undefined);    
        expect(record.skipNotification).to.be.equal(false);
        expect(record.sendAllNotifications).to.be.equal(true);
        expect(validationSync.bills.createBillOfValidationSync).to.have.callCount(1);
    });
    it("createBill function || remindable false ||  validationChannel 'BOU 1' || createBillOfValidationSync success || skipNotification true & sendAllNotifications undefined", async ()=>{
        let record = {remindable : false, validationChannel: 'COU', skipNotification : true };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.createBillOfValidationSync = sinon.spy(spyFunc);
        let promiseCreateBill = await validationSync.createBill(record);
        expect(promiseCreateBill).to.be.equal(undefined);    
        expect(record.skipNotification).to.be.equal(true);
        expect(record.sendAllNotifications).to.be.equal(undefined);
        expect(validationSync.bills.createBillOfValidationSync).to.have.callCount(1);
    });

    it("updateDbRecord function || DB_RECORD_UPTO_DATE ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(-15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate } ],
            dueDate: MOMENT().add(-16,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(null);    
        expect(record.skipNotification).to.be.equal(undefined);
        expect(record.updateInRecents).to.be.equal(undefined);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(0);
    });

    it("updateDbRecord function || NO_ACTIVE_RECORDS ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(-15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 0
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);    
        expect(record.skipNotification).to.be.equal(undefined);
        expect(record.updateInRecents).to.be.equal(undefined);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(0);
    });

    it("updateDbRecord function || whole flow execution ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`,
            dbDueDate = MOMENT().add(-15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate } ],
            dueDate: MOMENT().add(-1,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);    
        expect(record.skipNotification).to.be.equal(undefined);
        expect(record.updateInRecents).to.be.equal(undefined);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(0);
    });

    it("updateDbRecord function || success execution ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(-15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            amount : 500,
            debugKey : debugKey,
            activeRecordsInDB: 1,
            rechargeNumber : "xyz"
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        validationSync.bills.updateBillForSameRNandDiffCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(null);    
        expect(record.skipNotification).to.be.equal(false);
        expect(record.updateInRecents).to.be.equal(true);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(1);
    });    

    it("updateDbRecord function || success execution || due_date same & amount different ", async () => {
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate, amount : 500 } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            amount : 600,
            activeRecordsInDB: 1
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        let spyNewFunc = () => {
            return;
        }
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        validationSync.bills.updateBillForSameRNandDiffCID = sinon.spy(spyFunc);
        validationSync.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData = sinon.spy(spyNewFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(null);
    });  

    it.skip("updateDbRecord function || failure execution || due_date same & amount same ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate, amount : 500 } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            amount : 500,
            activeRecordsInDB: 1
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        validationSync.bills.updateBillForSameRNandDiffCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);    
        expect(record.skipNotification).to.be.equal(undefined);
        expect(record.updateInRecents).to.be.equal(undefined);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(0);
    });  

    it("updateDbRecord function || success execution || due_date different & amount same ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(10,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate, amount : 500 } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            amount : 500,
            activeRecordsInDB: 1
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        validationSync.bills.updateBillForSameRNandDiffCID = sinon.spy(spyFunc);
        
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(null);    
        expect(record.skipNotification).to.be.equal(false);
        expect(record.updateInRecents).to.be.equal(true);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(1);
    });  

    it.skip("updateDbRecord function || failure execution || due_date same & amount null ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(10,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate, amount : 500 } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord = await validationSync.updateDbRecord(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);    
        expect(record.skipNotification).to.be.equal(undefined);
        expect(record.updateInRecents).to.be.equal(undefined);
        expect(validationSync.bills.updateBillForSameRNandCID).to.have.callCount(0);
    }); 
    

    it("updateDbRecord function || updateBillForSameRNandCID error case ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`, dbDueDate = MOMENT().add(-15,'days').format('YYYY-MM-DD');
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ { due_date : dbDueDate } ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1
        };

        let stub4 = sinon.stub(validationSync.bills, "updateBillForSameRNandCID").callsFake(function fakeFn(callback){
            return callback("Error in publishData function ");
        }); 
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.bills.updateBillForSameRNandDiffCID = sinon.spy(spyFunc);
        // validationSync.bills.updateBillForSameRNandDiffCID = sinon.spy(spyFunc);
        let promiseUpdateDbRecord;  
        try {
            promiseUpdateDbRecord = await validationSync.updateDbRecord(record);    
        } catch (error) {
            expect(promiseUpdateDbRecord).to.be.equal(undefined);    
            expect(record.skipNotification).to.be.equal(false);
            expect(record.updateInRecents).to.be.equal(true);
            expect(stub4).to.have.callCount(1);    
        }        
    });    
    
    it("processBatch function | Success case execution",  ()=>{
        let payLoad = {
            value: JSON.stringify(data)
        };
        let records = [
            { ...payLoad },
            { ...payLoad }
        ];
        validationSync.processBatch(records,(data)=>{
            expect(data).to.be.equal(undefined);    
        });
    });

    it("processBatch function | Error case execution",  ()=>{
        let payLoad = {
            value: JSON.stringify(data)
        };
        let records = [
            { ...payLoad }
        ];
        sinon.stub(validationSync, "processRecord").rejects("Promise rejected inside function processRecord");
        validationSync.processBatch(records,(data)=>{
            expect(data).to.be.equal(undefined);    
        });
    });


    it("getSortedDbData function | record 1 & record 2 both having invalid dueDate",  ()=>{
        let dbRecords = [
            { due_date : 'XX' },
            { due_date : 'XXXXXXX' }
        ];

        let sortedDBRecords = [...dbRecords];
        validationSync.getSortedDbData(sortedDBRecords);
        
        expect(sortedDBRecords[0].due_date).to.be.equal(dbRecords[0].due_date);    
        expect(sortedDBRecords[1].due_date).to.be.equal(dbRecords[1].due_date);    
    });

    it("getSortedDbData function | record 1 invalid dueDate",  ()=>{
        let dbRecords = [
            { due_date : 'XXXXXX' },
            { due_date : MOMENT().add(1,'days').format('YYYY-MM-DD') }
        ];

        let sortedDBRecords = [...dbRecords];
        validationSync.getSortedDbData(sortedDBRecords);
        
        expect(sortedDBRecords[0].due_date).to.be.equal(dbRecords[1].due_date);    
        expect(sortedDBRecords[1].due_date).to.be.equal(dbRecords[0].due_date);    
    });

    it("getSortedDbData function | record 2 invalid dueDate",  ()=>{
        let dbRecords = [
            { due_date : MOMENT().add(1,'days').format('YYYY-MM-DD') },
            { due_date : 'XXXXXX' }
        ];

        let sortedDBRecords = [...dbRecords];
        validationSync.getSortedDbData(sortedDBRecords);
        
        expect(sortedDBRecords[0].due_date).to.be.equal(dbRecords[0].due_date);    
        expect(sortedDBRecords[1].due_date).to.be.equal(dbRecords[1].due_date);    
    });

    it("getSortedDbData function | sort asc. dueDate to desc. dueDate",  ()=>{
        let dbRecords = [
            { due_date : MOMENT().add(-1,'days').format('YYYY-MM-DD') },
            { due_date : MOMENT().add(1,'days').format('YYYY-MM-DD') }
        ];

        let sortedDBRecords = [...dbRecords];
        validationSync.getSortedDbData(sortedDBRecords);
        
        expect(sortedDBRecords[0].due_date).to.be.equal(dbRecords[1].due_date);    
        expect(sortedDBRecords[1].due_date).to.be.equal(dbRecords[0].due_date);    
    });

    it("getSortedDbData function | sort des. dueDate to desc. dueDate",  ()=>{
        let dbRecords = [
            { due_date : MOMENT().add(1,'days').format('YYYY-MM-DD') },
            { due_date : MOMENT().add(-1,'days').format('YYYY-MM-DD') }
        ];

        let sortedDBRecords = [...dbRecords];
        validationSync.getSortedDbData(sortedDBRecords);
        
        expect(sortedDBRecords[0].due_date).to.be.equal(dbRecords[0].due_date);    
        expect(sortedDBRecords[1].due_date).to.be.equal(dbRecords[1].due_date);    
    });


    it.skip("_isRemindable function | blockedPaytype",  ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "1234XXXXXXXX1234",
            productId: 12901,
            operator: "neft_hdfc",
            service: "finance service",
            paytype: 'credit card'
        };
        
        let recent_bills_operators = {};

        let _isRemindableFlag = validationSync._isRemindable(record, recent_bills_operators);
        expect(_isRemindableFlag).to.be.equal(false);
    });

    it("_isRemindable function | Remindable operator",  ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "9541637582",
            productId: 202,
            operator: "airtel",
            service: "Mobile",
            paytype: "prepaid"
        };
        let recent_bills_operators = {
            airtel: {paytypes : ["prepaid"] }
        };

        let _isRemindableFlag =  validationSync._isRemindable(record, recent_bills_operators);
        expect(_isRemindableFlag).to.be.equal(true);
    });

    it("_isRemindable function | non-remindable operator",  ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "9541637582",
            productId: 202,
            operator: "airtel",
            service: "Mobile",
            paytype: "postpaid"
        };
        let recent_bills_operators = {
            airtel: {paytypes : ["prepaid"] }
        };

        let _isRemindableFlag = validationSync._isRemindable(record, recent_bills_operators);
        expect(_isRemindableFlag).to.be.equal(false);
    });

    it("_isRemindable function | non-remindable operator | operator not on-boarded",  ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "9541637582",
            productId: 202,
            operator: "airtel",
            service: "Mobile",
            paytype: "postpaid"
        };
        let recent_bills_operators = {
            idea: {paytypes : ["prepaid"] }
        };

        let _isRemindableFlag = validationSync._isRemindable(record, recent_bills_operators);
        expect(_isRemindableFlag).to.be.equal(true);
    });

    it("updateBillsInRecent function | recentsLayerLib error", async ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "9541637582",
            productId: 202,
            operator: "airtel",
            service: "Mobile",
            paytype: "postpaid"
        };
        let spyFunc = (cb) => {
            return cb("Error in recentsLayerLib.update");
        };
        validationSync.recentsLayerLib.update = sinon.spy(spyFunc);
        let updateBillsInRecentPromise = await validationSync.updateBillsInRecent(record);

        expect(updateBillsInRecentPromise).to.be.equal(null);
    });
    
    it("updateBillsInRecent function | success", async ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "9541637582",
            productId: 202,
            operator: "airtel",
            service: "Mobile",
            paytype: "postpaid"
        };
        let spyFunc = (cb) => {
            return cb(null);
        };
        validationSync.recentsLayerLib.update = sinon.spy(spyFunc);
        let updateBillsInRecentPromise = await validationSync.updateBillsInRecent(record);

        expect(updateBillsInRecentPromise).to.be.equal(null);
    });

    it("getNewRecordForPublishing function", ()=>{
        let record = {
            customerId: 1,
            rechargeNumber: "9541637582",
            productId: 202,
            operator: "airtel"
            // amount: record.amount,
            // billDate: record.billDate,
            // dueDate: record.dueDate,
            // billFetchDate: record.billFetchDate,
            // nextBillFetchDate: record.nextBillFetchDate,
            // gateway: record.gateway,
            // paytype: record.paytype,
            // service: record.service,
            // circle: record.circle,
            // customerMobile: record.customerMobile,
            // customerEmail: record.customerEmail,
            // paymentChannel: record.paymentChannel,
            // retryCount: record.retryCount,
            // status: record.status,
            // reason: record.reason,
            // extra: record.extra,
            // published_date: record.published_date,
            // createdAt: record.createdAt,
            // updatedAt: record.updatedAt,
            // userData: record.user_data,
            // notification_status: record.notification_status,
            // paymentDate: record.paymentDate,
            // service_id: record.service_id,
            // customerOtherInfo: record.customerOtherInfo,
            // is_automatic: record.is_automatic,
            // dl_last_updated: record.dl_last_updated,
            // billGen: record.billGen,
            // source: record.source,
            // machineId: record.machineId
        };
        let newRecord = validationSync.getNewRecordForPublishing(record)
        expect(newRecord.customerId).to.be.equal(record.customerId);    
        expect(newRecord.operator).to.be.equal(record.operator);    
        expect(newRecord.rechargeNumber).to.be.equal(record.rechargeNumber);    
        expect(newRecord.rechargeNumber).to.be.equal(record.rechargeNumber);    

    });

    it("getBillsData function | execution", ()=>{
        let record = {
            dbData:[
                {
                    bill_fetch_date: MOMENT().add(-2,'days').format('YYYY-MM-DD'),
                    due_date: MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    bill_date: MOMENT().add(15,'days').format('YYYY-MM-DD'),
                    amount: 700

                }
            ],
            validationTimeStamp: MOMENT().add(-2,'days').format('YYYY-MM-DD'),
            customer_type: "",
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            service: "mobile",
            operator: "airtel",
            rechargeNumber: "1234",
            productId: "202"
        };
        let billsData = validationSync.getBillsData(record);

        expect(billsData.rechargeNumber).to.be.equal(record.rechargeNumber)
        expect(billsData.operator).to.be.equal(record.operator)
    });


    it('addParamsForIsValidityExpired:: with isValidityExpired set to true', async () => {
        let record = {
            "operator":'jio',
            "isValidityExpired": true,
            "billFetchDate": "2023-10-10",
            "extra": "{\"key\":\"value\"}",
        };
    
        let expectedRecord = {
            ...record,
            "partialBillState": "EXPIRED",
            "billDate": MOMENT(record.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
            "dueDate": null,
            "amount": 299,
            "extra": JSON.stringify({
                key: "value",
                partialBillDate: MOMENT(record.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
                partialBillState: "EXPIRED",
                isValidityExpired: true,
            }),
        };
    
        let updatedRecord = await validationSync.addParamsForIsValidityExpired(record);
        expect(updatedRecord).to.deep.equal(expectedRecord);
    });
    
    it('addParamsForIsValidityExpired:: with isValidityExpired false', async () => {
        let record = {
            "isValidityExpired": false,
            "billFetchDate": "2023-10-10",
            "extra": "{\"key\":\"value\"}",
        };
    
        let updatedRecord = await validationSync.addParamsForIsValidityExpired(record);
        expect(updatedRecord).to.deep.equal(record); // Record should remain unchanged
    });
    
    it('addParamsForIsValidityExpired:: with malformed JSON in extra', async () => {
        let record = {
            "operator":'jio',
            "isValidityExpired": true,
            "billFetchDate": "2023-10-10",
            "extra": "{malformed_json",
        };
    
        let updatedRecord = await validationSync.addParamsForIsValidityExpired(record);
        expect(updatedRecord.partialBillState).to.equal("EXPIRED");
        expect(updatedRecord.billDate).to.equal(MOMENT(record.billFetchDate).format('YYYY-MM-DD HH:mm:ss'));
        expect(updatedRecord.dueDate).to.be.null;
        expect(updatedRecord.amount).to.equal(299);
        expect(updatedRecord.extra).to.be.a('string'); // Ensure extra is set even if parsing fails
    });
})
describe("Module: ValidationSync:: publishInKafka:: test cases", function () {
    let validationSync;
    let payLoad = {
        value: JSON.stringify(data)
    }

    beforeEach(function (done) {
        STARTUP_MOCK.init(function(error, options){
            validationSync = new ValidationSync(options);
            done();
        });               
    });

    it("publishInKafka function || invalid inputs ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD') },
                { due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD') },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: null,
            operator: null,
            rechargeNumber: null,
            productId: null
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn(callback){
            return callback(null);
        });   
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
        expect(validationSync.kafkaPublisher.publishData).to.have.callCount(0);
    });

    it("publishInKafka function || Skipping sending Notfication ", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        validationSync.kafkaBillFetchPublisher = new validationSync.infraUtils.kafka.producer(); 
        validationSync.kafkaBillFetchPublisher.publishData = function(data,cb){};
        sinon.stub(validationSync.kafkaBillFetchPublisher, 'publishData').callsFake(function([],cb){
            return cb(null);
        });
        
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn(callback){
            return callback(null);
        }); 
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
        expect(validationSync.kafkaPublisher.publishData).to.have.callCount(0);
    });

    it("publishInKafka function || publishing for bdData is_automatic=1 & dueDate >  dbDueDate", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 1
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4,
                    is_automatic: 1
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1"
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        validationSync.kafkaBillFetchPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn1(args, callback){
            return callback(null);
        });  
        sinon.stub(validationSync.kafkaBillFetchPublisher, "publishData").callsFake(function fakeFn2(args, callback){
            return callback(null);
        });  
        sinon.stub(validationSync.commonLib, "decideTopicToPublishBillGen").returns(false);
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
        expect(validationSync.kafkaPublisher.publishData).to.have.callCount(1);
        expect(validationSync.kafkaBillFetchPublisher.publishData).to.have.callCount(1);
    });

    it("publishInKafka function || publishing for is_automatic=1 & dueDate >  dbDueDate", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 1
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4,
                    is_automatic: 1
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1",
            is_automatic: 1
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        validationSync.kafkaBillFetchPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn1(args, callback){
            return callback(null);
        });  
        sinon.stub(validationSync.kafkaBillFetchPublisher, "publishData").callsFake(function fakeFn2(args, callback){
            return callback(null);
        });  
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
        expect(validationSync.kafkaPublisher.publishData).to.have.callCount(1);
        expect(validationSync.kafkaBillFetchPublisher.publishData).to.have.callCount(0);
    });

    it("publishInKafka function || error while publishing dueDate >  dbDueDate", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 1
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1"
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        validationSync.kafkaBillFetchPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn1(args, callback){
            return callback("Error while publishing message in Kafka topic:AUTOMATIC_SYNC");
        });  
        sinon.stub(validationSync.kafkaBillFetchPublisher, "publishData").callsFake(function fakeFn2(args, callback){
            return callback(null);
        });  
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
    });

    it("publishInKafka function || error while publishing for is_automatic=0 & dueDate >  dbDueDate", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 1
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1",
            is_automatic: 0
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        validationSync.kafkaBillFetchPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn1(args, callback){
            return callback(null);
        });  
        sinon.stub(validationSync.kafkaBillFetchPublisher, "publishData").callsFake(function fakeFn2(args, callback){
            return callback("Error while publishing message in Kafka topic:REMINDER_BILL_FETCH");
        });  
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
    });

    it("publishInKafka function || error while publishing for is_automatic=0 & dueDate <  dbDueDate", async ()=>{
        let debugKey = `rech:7503251652::cust:$12345::op:airtel`;
        let record = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 1
                }
            ],
            dueDate: MOMENT().add(-15,'days').format('YYYY-MM-DD'),
            debugKey : debugKey,
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1",
            is_automatic: 0
        };
        validationSync.kafkaPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        validationSync.kafkaBillFetchPublisher = {
            publishData : (callback)=>{
                return callback(null);
            }
        };
        sinon.stub(validationSync.kafkaPublisher, "publishData").callsFake(function fakeFn1(args, callback){
            return callback(null);
        });  
        sinon.stub(validationSync.kafkaBillFetchPublisher, "publishData").callsFake(function fakeFn2(args, callback){
            return callback(null);
        });  
        let promiseUpdateDbRecord = await validationSync.publishInKafka(record);
        expect(promiseUpdateDbRecord).to.be.equal(undefined);
    });
});
describe("Module publisher:: ValidationSync :: CT tests", function () {
    let serviceObj;
    let payLoad = {
        value: JSON.stringify(data)
    }


    before(function () {
        STARTUP_MOCK.init(function(error, options){
            // billsSubscriberObj = new EmiDueCommonConsumer(options);
            serviceObj = new ValidationSync(options);
            done();
        });
    });

    it("CT tests || ensure publishCtevents is skipped if skipNotification is true", (done) => {

        let vs = _.cloneDeep(serviceObj);
        vs.publishCtEvents = sinon.spy()
        let stub = sinon.stub(vs, 'validateKafkaPayload').returns('')
        vs.processRecord(payLoad);
        stub.restore();
        expect(vs.publishCtEvents).to.have.callCount(0)
        return done();
    })    

    it("publishCtEvents function | check function calls", (done) => { 
        let tempRecord = _.cloneDeep(data)
         tempRecord.amount = 500;

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, [])
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});

        serviceObj.publishCtEvents(tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        return done();
    })

    it("publishCtEvents function | no retailerStatus", (done) => {
        let tempRecord = _.cloneDeep(data)
        tempRecord.amount = 500;

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});

        serviceObj.publishCtEvents(tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(0)
        expect(stub3).to.have.callCount(0)
        return done();
    })

    it("publishCtEvents function | no thumbnail", (done) => {
        let tempRecord = _.cloneDeep(data)
        tempRecord.amount = 500;

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});

        serviceObj.publishCtEvents(tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(0)
        return done();
    })

    it("publishCtEvents function |  publishData", (done) => {
        let tempRecord = _.cloneDeep(data)
        tempRecord.amount = 500;

        serviceObj.ctKafkaPublisher = {
            publishData : ([], callback)=>{
                return callback(null);
            }
        };
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
        let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, "publishData").callsFake(function fakeFn([], callback){
            return callback(null);
        });   

        serviceObj.publishCtEvents(tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(stub4).to.have.callCount(1)
        return done();
    })

    it("publishCtEvents function |  publishData error case", (done) => {
        let tempRecord = _.cloneDeep(data)
        tempRecord.amount = 500;

        serviceObj.ctKafkaPublisher = {
            publishData : ([], callback)=>{
                return callback(null);
            }
        };
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
        let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, "publishData").callsFake(function fakeFn([], callback){
            return callback("Error in publishData function ");
        });   

        serviceObj.publishCtEvents( tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(stub4).to.have.callCount(1)
        return done();
    })


    it("publishCtEvents function |  notification_status is null & is_automatic is 1 ", (done)=>{
        let tempRecord = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 1,
                    notification_status: null
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4,
                    is_automatic: 1,
                    notification_status: 0
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1",
            is_automatic: 1,
            amount: 500
        }
        serviceObj.ctKafkaPublisher = {
            publishData : ([], callback)=>{
                return callback(null);
            }
        };
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
        let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, "publishData").callsFake(function fakeFn([], callback){
            return callback("Error in publishData function ");
        });   
        serviceObj.publishCtEvents(tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(stub4).to.have.callCount(1)
        return done();
    });
    it("publishCtEvents function |  notification_status is 0 ", (done)=>{
        let tempRecord = {
            billsData : MOMENT().format('YYYY-MM-DD'),
            dbData: [ 
                { 
                    due_date : MOMENT().add(-15,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 10,
                    operator: "Airtel",
                    status : 13,
                    is_automatic: 0,
                    notification_status: 0
                },
                { 
                    due_date : MOMENT().add(-12,'days').format('YYYY-MM-DD'),
                    recharge_number:  "1234",
                    customer_id: 20,
                    operator: "Airtel",
                    status : 4,
                    notification_status: 0
                },
            ],
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD'),
            activeRecordsInDB: 1,
            sendAllNotifications: true,
            service: "Mobile",
            operator: "Airtel",
            rechargeNumber: "123456789",
            productId: "202",
            customerId: 20,
            validationChannel: "SUBS 1",
            amount: 500
        }
        serviceObj.ctKafkaPublisher = {
            publishData : ([], callback)=>{
                return callback(null);
            }
        };
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns({});
        let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, "publishData").callsFake(function fakeFn([], callback){
            return callback(null);
        });   

        serviceObj.publishCtEvents(tempRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(stub4).to.have.callCount(1)
        return done();
    });
});