/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import nock from 'nock'

import MOMENT from 'moment'

import AIRTEL_PUBLISHER from '../../services/airtelPublisher'

import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;
let token = 'dummy-token-for-ut';

describe("Module: Airtel Prepaid Publisher test suite", function () {
    let serviceObj;
    
    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new AIRTEL_PUBLISHER(options);

            //serviceObj.start();
            done();
        });
    });
    
});