/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    
    
    import chai, { assert } from "chai";
    import MOMENT from 'moment';
    import sinonChai from "sinon-chai";
    import PrepaidHistoricRecords from '../../services/prepaidHistoricRecords';
    import L from 'lgr'
    import config from '../../config'
    import _ from 'lodash';
    import helper from '../__mocks__';
    import sinon from 'sinon';
    import chaiAsPromised from "chai-as-promised";
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;

    let server;
    
    describe("Module:: Prepaid Historic Records :: Kafka consumer validations", function () {
        let prepaidHistoricRecordsObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                prepaidHistoricRecordsObj = new PrepaidHistoricRecords(options);
                done();
            });
        });

        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
        });
    

        it("initializeVariable : update includedTables variable if updated in config", () => {
          let og = _.get(prepaidHistoricRecordsObj.config,['DYNAMIC_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'TABLES', 'INCLUDE'],'plan_validity')
          console.log("abccc",og)
          _.set(prepaidHistoricRecordsObj.config,['DYNAMIC_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'TABLES', 'INCLUDE'],'newTable')
          prepaidHistoricRecordsObj.initializeVariable()
          assert.deepEqual(prepaidHistoricRecordsObj.includedTables,['newTable'])
          /** Revert to original table */
          _.set(prepaidHistoricRecordsObj.config,['DYNAMIC_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'TABLES', 'INCLUDE'],og)
      });
    
        it("testing initialize consumer | configureKafka || default values", () => {
            let cb = sinon.spy();
            prepaidHistoricRecordsObj.configureKafka(cb);
            expect(cb).to.have.callCount(1);
            expect(cb).to.have.calledWith(null)
            
        });
    
    
        it("start service || start || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(prepaidHistoricRecordsObj, 'configureKafka').yields(null);
            prepaidHistoricRecordsObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        });


        it("testing configureKafka function | Error case from initConsumer function",()=>{
            prepaidHistoricRecordsObj.kafkaBillFetchConsumer = new prepaidHistoricRecordsObj.infraUtils.kafka.consumer();
            sinon.stub(prepaidHistoricRecordsObj.kafkaBillFetchConsumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback("Error from initConsumer");
            });
            prepaidHistoricRecordsObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal("Error from initConsumer");
                }
            });
        });
    
        it("testing configureKafka function | success ",()=>{
            prepaidHistoricRecordsObj.kafkaBillFetchConsumer = new prepaidHistoricRecordsObj.infraUtils.kafka.consumer();
            sinon.stub(prepaidHistoricRecordsObj.kafkaBillFetchConsumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback(null);
            });
            prepaidHistoricRecordsObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
        });
    
        it("processKafkaData ||  execSteps || ensure empty records are validated", () => {
            let record = {};
            let processedRecords = prepaidHistoricRecordsObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        });


        it("processKafkaData || execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(prepaidHistoricRecordsObj, "processBatch").resolves();
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            prepaidHistoricRecordsObj.kafkaConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            prepaidHistoricRecordsObj.greyScaleEnv = true;
            
            let processedRecords = prepaidHistoricRecordsObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            process.exit.restore();
            stub1.restore();
        });

        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(prepaidHistoricRecordsObj, "processBatch").resolves();

            let clock = sinon.useFakeTimers();
            prepaidHistoricRecordsObj.kafkaBillFetchConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            prepaidHistoricRecordsObj.greyScaleEnv = true;
            
            let processedRecords = prepaidHistoricRecordsObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);

            stub1.restore();
        });

        it("processKafkaData || execSteps || ensure records flow", () => {
            let record=[{
                "database": "recharge_ananlytics",
                "table": "plan_validity",
                "type": "update",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
              }];

            let stub1= sinon.stub(prepaidHistoricRecordsObj,'processBatch')
            prepaidHistoricRecordsObj.execSteps(record);
            expect(stub1).to.have.callCount(1);

            stub1.restore();
        });

    });

    describe("Module:: Prepaid Historic Records :: validations", function () {
        let prepaidHistoricRecordsObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                prepaidHistoricRecordsObj = new PrepaidHistoricRecords(options);
                done();
            });
        });

        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
        });
        
        it("processBatch || storePrepaidHistoricRecords", () => {
          
            let stub1 = sinon.stub(prepaidHistoricRecordsObj, 'storePrepaidHistoricRecords');
            let stub2 = sinon.stub(prepaidHistoricRecordsObj.bills, 'insertPrepaidHistoricRecords').returns(null);
            let stub3 = sinon.stub(prepaidHistoricRecordsObj, 'validatePayload');
            let records = [{
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "table": "plan_validity",
                "type": "insert",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              }];

            prepaidHistoricRecordsObj.processBatch(records, function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                return done();
            });

            stub1.restore();
            stub2.restore();
            stub3.restore();

        });

        it("storePrepaidHistoricRecords || success", () => {
          
            let stub1 = sinon.stub(prepaidHistoricRecordsObj.bills, 'insertPrepaidHistoricRecords');
            let record = {
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "table": "plan_validity",
                "type": "insert",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              };

            prepaidHistoricRecordsObj.storePrepaidHistoricRecords(function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
            },record);

            stub1.restore();

        });

        it("storePrepaidHistoricRecords || validatePayload || No data found in the payload", () => {
            
            let stub1 = sinon.stub(prepaidHistoricRecordsObj, 'validatePayload');
            let stub2 = sinon.stub(prepaidHistoricRecordsObj.bills, 'insertPrepaidHistoricRecords').returns(null);
            let record = {};
            prepaidHistoricRecordsObj.storePrepaidHistoricRecords(function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
            },record);

            stub1.restore();
            stub2.restore();
        });

        it("storePrepaidHistoricRecords || validatePayload || Table not found in kafka payload", () => {
            
            let stub1 = sinon.stub(prepaidHistoricRecordsObj, 'validatePayload');
            let stub2 = sinon.stub(prepaidHistoricRecordsObj.bills, 'insertPrepaidHistoricRecords').returns(null);
            let record = {
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "type": "insert",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              };
              prepaidHistoricRecordsObj.storePrepaidHistoricRecords(function (error) {
                expect(error).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
            },record);
            stub1.restore();
            stub2.restore();
        });


        
        it("validatePayload || success", () => {

            let stub1= sinon.stub(prepaidHistoricRecordsObj,'convertKafkaPayloadToRecord');
            let kafkaPayload={
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "table": "plan_validity",
                "type": "update",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              };
          
              prepaidHistoricRecordsObj.validatePayload(kafkaPayload);
              expect(stub1).to.have.callCount(1);
           
              stub1.restore();

            
        });
        
        it("validatePayload  || No data found in the payload", () => {
          let kafkaPayload = {};

          expect(function(){prepaidHistoricRecordsObj.validatePayload(kafkaPayload)}).to.throw('No data found in the payload');
        });

        it("validatePayload  || Table not found in kafka payload", () => {
          
          let kafkaPayload = {
              "value":JSON.stringify({
              "database": "recharge_ananlytics",
              "type": "insert",
              "ts": 1604053432,
              "xid": 6583,
              "commit": true,
              "data": {
                "id": 1,
                "customer_id": 56785678,
                "recharge_number": 7987901755,
                "amount": 100,
                "product_id": 1,
                "vertical_id": 4,
                "category_id": 17,
                "brand": "BSNL",
                "service": "Mobile",
                "paytype": "prepaid",
                "operator": "BSNL",
                "circle": "Andhra Pradesh",
                "producttype": "Topup",
                "validity_expiry_date": "2020-12-30 10:23:52",
                "service_label": "Mobile",
                "order_date": "2022-08-23 13:22:53",
                "latest_recharge_date": "2022-08-23 13:22:53",
                "plan_bucket": "Recharge Plan",
                "category_name": "Recharge Plan",
                "created_at": "2021-03-18 11:22:03",
                "updated_at": "2020-10-30 10:23:52",
                "paytype_label": "Recharge",
                "operator_label": "BSNL",
                "circle_label": "Andhra Pradesh",
                "customer_mobile": null,
                "customer_email": null,
                "notification_status": 1,
                "extra": null,
                "producttype_label": "Talktime Topup",
                "merchant_id": null,
                "status": 0,
                "order_ids": 12856287935,
                "schedulable": null
              },
              "old": {
                "brand": "BSNL1",
                "updated_at": "2017-09-12 12:45:07"
              }
              })
            };
     
            expect(function(){prepaidHistoricRecordsObj.validatePayload(kafkaPayload)}).to.throw('Table not found in kafka payload');
          });
         

        it("convertKafkaPayloadToRecord || No data found in the payload", () => {
          let record = {};

          expect(function(){prepaidHistoricRecordsObj.convertKafkaPayloadToRecord(record)}).to.throw('No data found in the payload');
        });

        it("convertKafkaPayloadToRecord || Table not found in kafka payload", () => {
          
          let record = {
              "value":JSON.stringify({
              "database": "recharge_ananlytics",
              "type": "insert",
              "ts": 1604053432,
              "xid": 6583,
              "commit": true,
              "data": {
                "id": 1,
                "customer_id": 56785678,
                "recharge_number": 7987901755,
                "amount": 100,
                "product_id": 1,
                "vertical_id": 4,
                "category_id": 17,
                "brand": "BSNL",
                "service": "Mobile",
                "paytype": "prepaid",
                "operator": "BSNL",
                "circle": "Andhra Pradesh",
                "producttype": "Topup",
                "validity_expiry_date": "2020-12-30 10:23:52",
                "service_label": "Mobile",
                "order_date": "2022-08-23 13:22:53",
                "latest_recharge_date": "2022-08-23 13:22:53",
                "plan_bucket": "Recharge Plan",
                "category_name": "Recharge Plan",
                "created_at": "2021-03-18 11:22:03",
                "updated_at": "2020-10-30 10:23:52",
                "paytype_label": "Recharge",
                "operator_label": "BSNL",
                "circle_label": "Andhra Pradesh",
                "customer_mobile": null,
                "customer_email": null,
                "notification_status": 1,
                "extra": null,
                "producttype_label": "Talktime Topup",
                "merchant_id": null,
                "status": 0,
                "order_ids": 12856287935,
                "schedulable": null
              },
              "old": {
                "brand": "BSNL1",
                "updated_at": "2017-09-12 12:45:07"
              }
              })
            };
     
            expect(function(){prepaidHistoricRecordsObj.convertKafkaPayloadToRecord(record)}).to.throw('Table not found in kafka payload');
          });

        it("convertKafkaPayloadToRecord || Not in allowedActions || delete ", () => {
            let kafkaPayload = {
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "table": "plan_validity",
                "type": "delete",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              };
            let processedRecord = prepaidHistoricRecordsObj.convertKafkaPayloadToRecord(kafkaPayload);
            expect(processedRecord).to.be.equal(null);
            
        });
        
        it("convertKafkaPayloadToRecord || Table is not included ", () => {
            let kafkaPayload = {
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "table": "bills_adabigas",
                "type": "update",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              };
            let processedRecord = prepaidHistoricRecordsObj.convertKafkaPayloadToRecord(kafkaPayload);
             expect(processedRecord).to.be.equal(null);
            
        });


        it("convertKafkaPayloadToRecord || Successful Insert/Update ", () => {
            let kafkaPayload = {
                "value":JSON.stringify({
                "database": "recharge_ananlytics",
                "table": "plan_validity",
                "type": "insert",
                "ts": 1604053432,
                "xid": 6583,
                "commit": true,
                "data": {
                  "id": 1,
                  "customer_id": 56785678,
                  "recharge_number": 7987901755,
                  "amount": 100,
                  "product_id": 1,
                  "vertical_id": 4,
                  "category_id": 17,
                  "brand": "BSNL",
                  "service": "Mobile",
                  "paytype": "prepaid",
                  "operator": "BSNL",
                  "circle": "Andhra Pradesh",
                  "producttype": "Topup",
                  "validity_expiry_date": "2020-12-30 10:23:52",
                  "service_label": "Mobile",
                  "order_date": "2022-08-23 13:22:53",
                  "latest_recharge_date": "2022-08-23 13:22:53",
                  "plan_bucket": "Recharge Plan",
                  "category_name": "Recharge Plan",
                  "created_at": "2021-03-18 11:22:03",
                  "updated_at": "2020-10-30 10:23:52",
                  "paytype_label": "Recharge",
                  "operator_label": "BSNL",
                  "circle_label": "Andhra Pradesh",
                  "customer_mobile": null,
                  "customer_email": null,
                  "notification_status": 1,
                  "extra": null,
                  "producttype_label": "Talktime Topup",
                  "merchant_id": null,
                  "status": 0,
                  "order_ids": 12856287935,
                  "schedulable": null
                },
                "old": {
                  "brand": "BSNL1",
                  "updated_at": "2017-09-12 12:45:07"
                }
                })
              };
            let processedRecord = prepaidHistoricRecordsObj.convertKafkaPayloadToRecord(kafkaPayload);
            expect(processedRecord.customer_id).to.be.equal(56785678);
            expect(processedRecord.service).to.be.equal("Mobile");
            expect(processedRecord.recharge_number).to.be.equal(7987901755);
            expect(processedRecord.operator).to.be.equal("BSNL");
            expect(processedRecord.status).to.be.equal(0);
            expect(processedRecord.circle).to.be.equal("Andhra Pradesh");
        });
    

    });