/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';

import chai, { assert } from "chai";
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import EmiDueCommonConsumer from '../../services/emiDueCommonConsumer';
import L from 'lgr'
import config from '../../config'
import _ from 'lodash';
import helper from '../__mocks__';
import sinon from 'sinon';
import chaiAsPromised from "chai-as-promised";
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module publisher:: EMI Due Common Consumer :: Kafka consumer validations", function () {
    let emiDueCommonConsumerObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            // billsSubscriberObj = new EmiDueCommonConsumer(options);
            emiDueCommonConsumerObj = new EmiDueCommonConsumer(options);
            done();
        });
    });

    it("testing initialize consumer | default values", () => {
        let cb = sinon.spy();
        emiDueCommonConsumerObj.configureKafka(cb);
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
        
    });

    it("start service || ensure service initialises consumer", () => {
        let initializeStub = sinon.stub(emiDueCommonConsumerObj, 'configureKafka').yields(null);
        emiDueCommonConsumerObj.start();
        expect(initializeStub).to.have.been.calledOnce;
    });

    it("processKafkaData || ensure empty records are validated", () => {
        let record = {}
        let processedRecords = emiDueCommonConsumerObj.execSteps(record);
        expect(processedRecords).to.be.equal(undefined)
    })

});


describe("Module publisher:: EMI Due Common Consumer :: test suite", function () {
    let emiDueCommonConsumerObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            // billsSubscriberObj = new EmiDueCommonConsumer(options);
            emiDueCommonConsumerObj = new EmiDueCommonConsumer(options);
            done();
        });
    });

    it("validateRecord function | invalid record", () => {
        let record = '';
        let [error, validRecord] =emiDueCommonConsumerObj.validateRecord(record);
        expect(error).to.be.equal('Invalid record');
    });

    it("validateRecord function | Invalid Kafka record received", () => {
        let record = '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}';
        let [error, validRecord] =emiDueCommonConsumerObj.validateRecord(record);
        expect(error).to.be.equal('Invalid Kafka record received');
    });

    it("validateRecord function | Kafka record Parsing Error", () => {
        let record = { "value" : [{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}] };
        let [error, validRecord] =emiDueCommonConsumerObj.validateRecord(record);    
        expect(error).to.be.equal('Kafka record Parsing Error');
    });

    it("processRecords function | validateRecord function | invalid record", (done) => {
        let record = '';    
        emiDueCommonConsumerObj.processRecords((error) => {
            return done();
        }, record);
        
    });
    it("processRecords function | validateRecord function | Invalid Kafka record received", (done) => {
        let record = '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}';
        emiDueCommonConsumerObj.processRecords((error) => {
            return done();
        }, record);
    });


    it("checkOperatorEligibility function | Error in finding product_id/operator", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing"};
        let [error, validRecord] = emiDueCommonConsumerObj.checkOperatorEligibility(record);    
        expect(error).to.be.equal('Error in finding product_id/operator');
    });


    it("checkOperatorEligibility function | operator config. not available...skipping it", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "test operator"} ;
        let [error, validRecord] = emiDueCommonConsumerObj.checkOperatorEligibility(record);    
        expect(error).to.be.equal('Operator test operator config. not available...skipping it');
    });

    /**
    it("checkOperatorEligibility function | Flow execution", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"} ;
        let [error, validRecord] = emiDueCommonConsumerObj.checkOperatorEligibility(record);    
        expect(error).to.be.equal(null);
    }); */

    it("processRecord function | Mandatory Params amount is Missing / Invalid", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "-5","installment_date": "4-Jun-21","emi_due": "50","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"} ;
        emiDueCommonConsumerObj.operator = "Fullerton India Housing Finance Limited";

        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal('Mandatory Params amount is Missing / Invalid');
    });

    it("processRecord function | Mandatory Params customerId is Missing / Invalid", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "50","opening_bucket": "1","dpd": "27","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"} ;
        emiDueCommonConsumerObj.operator = "Fullerton India Housing Finance Limited";

        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal('Mandatory Params customerId is Missing / Invalid');
    });


    it("processRecord function | Mandatory Params rechargeNumber is Missing / Invalid", () => {
        let record = {"record_type": "New","application_id": "3152750","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "50","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"} ;
        emiDueCommonConsumerObj.operator = "Fullerton India Housing Finance Limited";

        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal('Mandatory Params rechargeNumber is Missing / Invalid');
    });

    it("processRecord function | Flow execution", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "50","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"} ;
        emiDueCommonConsumerObj.operator = "Fullerton India Housing Finance Limited";

        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal(null);
    });


    it("processRecord function | Flow execution | when recharge_number_2 is DOB ", () => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"23-08-1994","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal(null);

    });

    it("processRecord function | Flow execution | when recharge_number_2 is DOB ", () => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"31-12-1900","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal(null);
    });

    it("processRecord function | Mandatory Params recharge_number_2 is Missing / Invalid | DOB missing", () => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal("Mandatory Params recharge_number_2 is Missing / Invalid");
    });


    it("processRecord function |  Mandatory Params recharge_number_2 is Missing / Invalid | DOB invalid", () => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"1994-08-31","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal(null);
       
    });


    it("processRecord function | Mandatory Params recharge_number_2 is Missing / Invalid | DOB invalid", () => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"1994-23-08","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal("Mandatory Params recharge_number_2 is Missing / Invalid");

    });

    it("processRecord function | Mandatory Params recharge_number_2 is Missing / Invalid | DOB invalid", () => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"05-May-1990","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal("Mandatory Params recharge_number_2 is Missing / Invalid");


    });

    it("processRecord function | Mandatory Params recharge_number_2 is Missing / Invalid | DOB invalid", () => {
       let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"05/07/1990","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        emiDueCommonConsumerObj.operator = "StashFin-CAAS";
        let [error, processedRecord] = emiDueCommonConsumerObj.processRecord(record);    
        expect(error).to.be.equal("Mandatory Params recharge_number_2 is Missing / Invalid");

    });

    it("getDbRecordToUpdate function", () => {
        let record = { "productId" : "330778558" ,"rechargeNumber": "055602210545639","customerMobile": "9822468899","customerEmail": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","amount": "15561","emi_due": "50","customerId": "1147438071","operator": "Fullerton India Housing Finance Limited"} ;
        emiDueCommonConsumerObj.operator = "Fullerton India Housing Finance Limited";

        let updatedDbRecord = emiDueCommonConsumerObj.getDbRecordToUpdate(record);    
        expect(updatedDbRecord.customerId).to.be.equal(record.customerId);
        expect(updatedDbRecord.rechargeNumber).to.be.equal(record.rechargeNumber);
        expect(updatedDbRecord.customerMobile).to.be.equal(record.customerMobile);
        expect(updatedDbRecord.customerEmail).to.be.equal(record.customerEmail);
    });
    

    /**
    it("processRecords function | Flow execution", (done) => {
        let record = { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}' };    
        emiDueCommonConsumerObj.operator = "Fullerton India Housing Finance Limited";
        emiDueCommonConsumerObj.processRecords(() => {
            return done();
        }, record); 
    });
     */


    it("processBatch function", (done) => {
        let records = [
            { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}' },
            { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}' },    
            { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Fullerton India Housing Finance Limited"}' }
        ];
        emiDueCommonConsumerObj.processBatch(records, () => {
            return done();
        });
    });



    /** helper function test ases */
    it("parseAmount | Valid Record | Integer - Rs.590", () => {
        expect(emiDueCommonConsumerObj.parseAmount("Rs.590")).to.be.equal(590);
    });
    it("parseAmount | Valid Record | Float - Rs.590.78", () => {
        expect(emiDueCommonConsumerObj.parseAmount("Rs.590.78")).to.be.equal(590.78);
    });
    it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....-1223.34", () => {
        expect(emiDueCommonConsumerObj.parseAmount("Rs   ....-1223.34")).to.be.equal(-1223.34);
    });
    it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....+1223.34", () => {
        expect(emiDueCommonConsumerObj.parseAmount("Rs   ....+1223.34")).to.be.equal(1223.34);
    });
    it("parseAmount | Valid Record | zero amount -> Rs.0", () => {
        expect(emiDueCommonConsumerObj.parseAmount("Rs.0")).to.be.equal(0);
    });
    it("parseAmount | Valid Record | without Rs. string", () => {
        expect(emiDueCommonConsumerObj.parseAmount("590")).to.be.equal(590);
    });
    it("parseAmount | Valid Record | without Rs. string", () => {
        expect(emiDueCommonConsumerObj.parseAmount("-590")).to.be.equal(-590);
    });
    it("parseAmount | Valid Record | as Number", () => {
        expect(emiDueCommonConsumerObj.parseAmount(590)).to.be.equal(590);
    });
    it("parseAmount | Valid Record | as Number", () => {
        expect(emiDueCommonConsumerObj.parseAmount(590.67)).to.be.equal(590.67);
    });
    it("parseAmount | InValid Record | as null", () => {
        expect(emiDueCommonConsumerObj.parseAmount(null)).to.be.equal(null);
    });
    it("parseAmount | InValid Record | as normal string", () => {
        expect(emiDueCommonConsumerObj.parseAmount("amount")).to.be.equal(null);
    });

    it("parseAmount | InValid Record | as normal string with comma as 1,948", () => {
        expect(emiDueCommonConsumerObj.parseAmount("1,948")).to.be.equal(1948);
    });

    it("parseAmount | InValid Record | as normal string with comma as 99,11,948", () => {
        expect(emiDueCommonConsumerObj.parseAmount("99,11,948")).to.be.equal(9911948);
    });

    it("parseAmount | InValid Record | as normal string with comma as 10,", () => {
        expect(emiDueCommonConsumerObj.parseAmount("10,")).to.be.equal(10);
    });

    it("parseAmount | InValid Record | as normal string with comma as 99.12,120", () => {
        expect(emiDueCommonConsumerObj.parseAmount("99.12,120")).to.be.equal(99.12);
    });

    it("parseAmount | InValid Record | as normal string with comma as -99.19,198", () => {
        expect(emiDueCommonConsumerObj.parseAmount("-99.19,198")).to.be.equal(-99.19);
    });

    it("getParsedDate | invalid date | '' ", () => {
        let date = '';
        let parsedDateOfBirthObj = emiDueCommonConsumerObj.getParsedDate(date);
        expect(parsedDateOfBirthObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateOfBirthObj.value).to.be.equal(null);
    });

    it("getParsedDate | invalid date | null ", () => {
        let date = null;
        let parsedDateOfBirthObj = emiDueCommonConsumerObj.getParsedDate(date);
        expect(parsedDateOfBirthObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateOfBirthObj.value).to.be.equal(null);
    });

    it("getParsedDate | valid date | 31-12-1980 | dateFormat == 'YYYY-MM-DD'", () => {
        let date = "31-12-1980", formattedDate = "1980-12-31", dateFormat = "YYYY-MM-DD";

        let parsedDateOfBirthObj = emiDueCommonConsumerObj.getParsedDate(date, dateFormat);
        expect(parsedDateOfBirthObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateOfBirthObj.value).to.be.equal(formattedDate);
    });

    it("getParsedDate | invalid date | 1980-12-05 | dateFormat == 'YYYY-MM-DD' ", () => {
        let date = "1980-12-05" , formattedDate = "1980-12-05", dateFormat = "YYYY-MM-DD";

        let parsedDateOfBirthObj = emiDueCommonConsumerObj.getParsedDate(date, dateFormat);
        expect(parsedDateOfBirthObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateOfBirthObj.value).to.be.equal(formattedDate);
    });

    it("getParsedDate | valid date | 31-12-1980 | dateFormat == 'DD-MM-YYYY'", () => {
        let date = "31-12-1980", formattedDate = "31-12-1980", dateFormat = "DD-MM-YYYY";

        let parsedDateOfBirthObj = emiDueCommonConsumerObj.getParsedDate(date, dateFormat);
        expect(parsedDateOfBirthObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateOfBirthObj.value).to.be.equal(formattedDate);
    });

    it("getParsedDate | invalid date | 1980-12-05 | dateFormat == 'DD-MM-YYYY' ", () => {
        let date = "1980-12-05" , formattedDate = "05-12-1980", dateFormat = "DD-MM-YYYY";

        let parsedDateOfBirthObj = emiDueCommonConsumerObj.getParsedDate(date, dateFormat);
        expect(parsedDateOfBirthObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateOfBirthObj.value).to.be.equal(formattedDate);
    });


});

describe("Module publisher:: EMI Due Common Consumer :: CT tests", function () {
    let serviceObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            // billsSubscriberObj = new EmiDueCommonConsumer(options);
            serviceObj = new EmiDueCommonConsumer(options);
            done();
        });
    });

    it("publishCtEvents function | Flow execution", (done) => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"31-12-1900","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        serviceObj.operator = "StashFin-CAAS";
        // serviceObj.tableName = 'bills_hero'
        let [error, processedRecord] = serviceObj.processRecord(record);
        let finalRecord = serviceObj.getDbRecordToUpdate(processedRecord)
        let cb = sinon.spy();

        let stub = sinon.stub(serviceObj, 'publishCtEvents');
        let stub2 = sinon.stub(serviceObj, 'sendNotification').yields(null);
        sinon.stub(serviceObj, 'validateRecord').returns([null,record])

        serviceObj.processRecords(cb, record)

        stub.restore();
        stub2.restore();
        expect(stub).to.have.callCount(1)
        return done();
    });

    it("publishCtEvents function | check function calls", (done) => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"31-12-1900","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        serviceObj.operator = "StashFin-CAAS";
        serviceObj.tableName = 'bills_hero'
        let [error, processedRecord] = serviceObj.processRecord(record);
        let finalRecord = serviceObj.getDbRecordToUpdate(processedRecord)
        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, finalRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(cb).to.have.been.calledWith(null)
        return done();
    })

    it("publishCtEvents function | no retailerStatus", (done) => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"31-12-1900","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        serviceObj.operator = "StashFin-CAAS";
        serviceObj.tableName = 'bills_hero'
        let [error, processedRecord] = serviceObj.processRecord(record);
        let finalRecord = serviceObj.getDbRecordToUpdate(processedRecord)
        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, finalRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(0)
        expect(stub3).to.have.callCount(0)
        return done();
    })

    it("publishCtEvents function | no thumbnail", (done) => {
        let record = {"record_type":"N","application_id":1455486,"loan_agreement_no":2893453,"customername":"Dhamodharan","branch":null,"branch_state_name":null,"customer_add_mobile":9578321809,"cust_mailing_add_email":null,"pin_code":null,"productflag":"PL Self","loan_amount":null,"loan_tenure":null,"loan_start_date":null,"installment_frequency":"Monthly","emi_amount":1725,"installment_date":null,"emi_due":10149,"opening_bucket":"DPD 360+","dpd":null,"DOB":"31-12-1900","cust_id":"342469525","operator":"StashFin","reminder_operator":"StashFin-CAAS"} ;
        serviceObj.operator = "StashFin-CAAS";
        serviceObj.tableName = 'bills_hero'
        let [error, processedRecord] = serviceObj.processRecord(record);
        let finalRecord = serviceObj.getDbRecordToUpdate(processedRecord)
        let cb = sinon.spy();

        serviceObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(serviceObj.commonLib, 'getCvrData').yields("error")
        let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        serviceObj.publishCtEvents(cb, finalRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(0)
        return done();
    })
})