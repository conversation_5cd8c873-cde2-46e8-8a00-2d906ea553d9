/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert } from "chai";
import sinon from 'sinon';
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import _ from 'lodash';
import chaiAsPromised from "chai-as-promised";

import BillPreprocessor from '../../services/nonPaytmBills/nonRuBillProcessor.js';
import STARTUP_MOCK from '../__mocks__/startUp';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

// Mock data for testing
const mockBillData = {
    "dbEvent": "upsert",
    "customerId": ********,
    "rechargeNumber": "6666 77XX XXXX 5165",
    "productId": "**********",
    "operator": "visa_hdfcbank",
    "amount": 2222,
    "bill_fetch_date": "2022-02-20T12:31:12.000Z",
    "paytype": "postpaid",
    "service": "financial services",
    "circle": "",
    "customer_mobile": "**********",
    "customer_email": "<EMAIL>",
    "status": 7,
    "userData": "",
    "createdAt": "2018-11-20T11:16:28.000Z",
    "updatedAt": "2018-11-28T06:14:02.000Z",
    "billDate": "2022-02-20T12:31:12.000Z",
    "extra": null,
    "notificationStatus": 1,
    "dueDate": "2022-03-20T18:30:00.000Z",
    "customerOtherInfo": null,
    "cardNetwork": "visa",
    "bankName": "HDFC",
    "partialBillState": "Expired",
    "debugKey": "test-debug-key-123"
};

const mockInvalidBillData = {
    "dbEvent": "invalid_event",
    "debugKey": "invalid-debug-key"
};

describe("BillPreprocessor | Unit Tests", () => {
    let billPreprocessor;
    let options;
    let sandbox;

    before((done) => {
        STARTUP_MOCK.init((error, startupOptions) => {
            if (error) {
                done(error);
                return;
            }
            
            options = {
                ...startupOptions,
                L: {
                    log: sinon.stub(),
                    error: sinon.stub(),
                    info: sinon.stub(),
                    warn: sinon.stub(),
                    verbose: sinon.stub()
                },
                config: {
                    ...startupOptions.config,
                    DYNAMIC_CONFIG: {
                        PREPAIDSMSPARSING: {
                            COMMON: {
                                OPERATORS: ['airtel', 'vodafone']
                            }
                        }
                    }
                }
            };
            done();
        });
    });

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        
        // Mock utility functions - we'll handle this differently
        // sandbox.stub(require('../../lib/datadog.js'), 'sendNonPaytmBillsMetrics').returns();
        


        // Mock AnalyticsHandler
        const mockAnalyticsHandler = {
            handleInvalidBillData: sandbox.stub().resolves()
        };

        // Mock BillDataValidator
        const mockValidator = {
            validateBillsData: sandbox.stub().returns({ isValid: true, error: null })
        };

        // Mock strategy classes by overriding the constructor
        const mockStrategyClass = function(options) {
            const strategy = {
                process: sandbox.stub().resolves(mockBillData),
                preprocess: sandbox.stub().resolves(mockBillData), // Add preprocess method for validation
                getServiceType: sandbox.stub().returns('mobile'),
                getPaytype: sandbox.stub().returns('postpaid'),
                constructor: { name: 'MockStrategy' }
            };
            return strategy;
        };

        const mockDatabaseStrategyClass = function(options) {
            const strategy = {
                execute: sandbox.stub().resolves(),
                constructor: { name: 'MockDatabaseStrategy' }
            };
            return strategy;
        };

        // Mock preprocessing strategies - only mock the ones that exist
        try {
            sandbox.stub(require('../../services/nonPaytmBills/nonRuPreProcessingStrategy/DefaultStrategy.js'), 'default')
                .returns(mockStrategyClass);
        } catch (error) {
            // Strategy module might not exist, continue
        }

        // Mock database event strategies - only mock the ones that exist
        try {
            sandbox.stub(require('../../services/nonPaytmBills/nonRuDatabaseEventStrategy/UpsertStrategy.js'), 'default')
                .returns(mockDatabaseStrategyClass);
        } catch (error) {
            // Strategy module might not exist, continue
        }

        // Mock AnalyticsHandler
        sandbox.stub(require('../../services/nonPaytmBills/AnalyticsHandler.js'), 'default')
            .returns(mockAnalyticsHandler);

        // Mock BillDataValidator
        sandbox.stub(require('../../services/nonPaytmBills/nonRuBillDataValidator.js'), 'default')
            .returns(mockValidator);

        billPreprocessor = new BillPreprocessor(options);
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe("Constructor", () => {
        it("should initialize BillPreprocessor with all required components", () => {
            expect(billPreprocessor).to.be.instanceOf(BillPreprocessor);
            expect(billPreprocessor.config).to.deep.equal(options.config);
            expect(billPreprocessor.L).to.deep.equal(options.L);
            expect(billPreprocessor.preprocessingStrategies).to.be.instanceOf(Map);
            expect(billPreprocessor.databaseEventStrategies).to.be.instanceOf(Map);
            expect(billPreprocessor.defaultStrategy).to.not.be.null;
        });

        it("should log initialization messages", () => {
            expect(options.L.log).to.have.been.calledWith('BillPreprocessor', 'Initialized with integrated bill processing, preprocessing, and database event strategies');
            expect(options.L.log).to.have.been.calledWith('BillPreprocessor', sinon.match.string);
        });

        it("should initialize preprocessing strategies correctly", () => {
            expect(billPreprocessor.preprocessingStrategies.size).to.be.greaterThan(0);
            // Check that at least some strategies are loaded
            expect(billPreprocessor.defaultStrategy).to.not.be.null;
        });

        it("should initialize database event strategies correctly", () => {
            expect(billPreprocessor.databaseEventStrategies.size).to.be.greaterThan(0);
            expect(billPreprocessor.databaseEventStrategies.has('upsert')).to.be.true;
            expect(billPreprocessor.databaseEventStrategies.has('upsertWithRead')).to.be.true;
            expect(billPreprocessor.databaseEventStrategies.has('delete')).to.be.true;
            expect(billPreprocessor.databaseEventStrategies.has('findAndUpdateData')).to.be.true;
            expect(billPreprocessor.databaseEventStrategies.has('findAndCreate')).to.be.true;
            expect(billPreprocessor.databaseEventStrategies.has('updateMultipleRecordsWithSameRN')).to.be.true;
        });
    });

    describe("_createStrategyKey", () => {
        it("should create strategy key with service and paytype", () => {
            const key = billPreprocessor._createStrategyKey('mobile', 'postpaid');
            expect(key).to.equal('mobile:postpaid');
        });

        it("should create strategy key with service only when paytype is null", () => {
            const key = billPreprocessor._createStrategyKey('electricity', null);
            expect(key).to.equal('electricity');
        });

        it("should create strategy key with service only when paytype is undefined", () => {
            const key = billPreprocessor._createStrategyKey('electricity', undefined);
            expect(key).to.equal('electricity');
        });

        it("should create strategy key with service only when paytype is empty string", () => {
            const key = billPreprocessor._createStrategyKey('electricity', '');
            expect(key).to.equal('electricity');
        });
    });

    describe("_getPreprocessingStrategy", () => {
        it("should return specific strategy for service and paytype combination", () => {
            const strategy = billPreprocessor._getPreprocessingStrategy('mobile', 'postpaid');
            expect(strategy).to.not.be.null;
            // The actual strategy name will depend on what's loaded, so just check it exists
            expect(strategy.constructor.name).to.be.a('string');
        });

        it("should return service-only strategy when paytype-specific strategy not found", () => {
            const strategy = billPreprocessor._getPreprocessingStrategy('electricity', 'unknown');
            expect(strategy).to.not.be.null;
            // The actual strategy name will depend on what's loaded, so just check it exists
            expect(strategy.constructor.name).to.be.a('string');
        });

        it("should return default strategy when no specific strategy found", () => {
            const strategy = billPreprocessor._getPreprocessingStrategy('unknown_service', 'unknown_paytype');
            expect(strategy).to.not.be.null;
            expect(strategy).to.equal(billPreprocessor.defaultStrategy);
        });

        it("should log when using default strategy", () => {
            billPreprocessor._getPreprocessingStrategy('unknown_service', 'unknown_paytype');
            expect(options.L.log).to.have.been.calledWith('BillPreprocessor', 
                sinon.match.string);
        });
    });

    describe("_getDatabaseEventStrategy", () => {
        it("should return valid database event strategy", () => {
            const strategy = billPreprocessor._getDatabaseEventStrategy('upsert');
            expect(strategy).to.not.be.null;
            // The actual strategy name will depend on what's loaded, so just check it exists
            expect(strategy.constructor.name).to.be.a('string');
        });

        it("should throw error for invalid database event", () => {
            expect(() => {
                billPreprocessor._getDatabaseEventStrategy('invalid_event');
            }).to.throw('No valid database event strategy found for event: invalid_event');
        });

        it("should log error for invalid database event", () => {
            try {
                billPreprocessor._getDatabaseEventStrategy('invalid_event');
            } catch (error) {
                // Error expected
            }
            expect(options.L.error).to.have.been.calledWith('getDatabaseEventStrategy', 
                'No valid database event strategy found for event: invalid_event');
        });
    });

    describe("processBillData", () => {
        it("should process valid bill data successfully", async () => {
            const result = await billPreprocessor.processBillData(mockBillData);
            expect(result).to.be.undefined; // processBillData doesn't return anything on success
        });

        it("should handle validation failure", async () => {
            // Mock validation failure
            billPreprocessor.validator.validateBillsData.returns({ 
                isValid: false, 
                error: 'Validation failed' 
            });

            await billPreprocessor.processBillData(mockBillData);

            expect(options.L.log).to.have.been.calledWith('processBillData', 
                sinon.match.string);
            expect(billPreprocessor.analyticsManager.handleInvalidBillData).to.have.been.calledWith(
                mockBillData, 'Validation failed');
        });

        it("should handle preprocessing error", async () => {
            // Mock preprocessing error
            const preprocessingError = new Error('Preprocessing failed');
            billPreprocessor._preprocessBillData = sandbox.stub().rejects(preprocessingError);

            await billPreprocessor.processBillData(mockBillData);

            expect(options.L.error).to.have.been.calledWith('processBillData', 
                sinon.match.string);
            expect(billPreprocessor.analyticsManager.handleInvalidBillData).to.have.been.calledWith(
                mockBillData, 'Preprocessing failed');
        });

        it("should handle database event strategy error", async () => {
            // Mock successful preprocessing
            billPreprocessor._preprocessBillData = sandbox.stub().resolves(mockBillData);

            // Mock database strategy error
            const dbError = new Error('Database operation failed');
            const mockDbStrategy = {
                execute: sandbox.stub().rejects(dbError)
            };
            billPreprocessor._getDatabaseEventStrategy = sandbox.stub().returns(mockDbStrategy);

            // Mock analytics handler
            billPreprocessor.analyticsManager.handleInvalidBillData = sandbox.stub().resolves();

            await billPreprocessor.processBillData(mockBillData);

            expect(options.L.error).to.have.been.calledWith('processBillData', 
                sinon.match.string);
            expect(billPreprocessor.analyticsManager.handleInvalidBillData).to.have.been.calledWith(
                mockBillData, 'Database operation failed');
        });

        it("should handle missing debugKey gracefully", async () => {
            const billDataWithoutDebugKey = { ...mockBillData };
            delete billDataWithoutDebugKey.debugKey;

            await billPreprocessor.processBillData(billDataWithoutDebugKey);

            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledWith(
                billDataWithoutDebugKey, 'upsert');
        });

        it("should handle minimal bill data", async () => {
            const minimalBillData = { debugKey: 'minimal-key' };
            
            // Mock validation to handle minimal data
            billPreprocessor.validator.validateBillsData.returns({ 
                isValid: false, 
                error: 'Minimal bill data validation failed' 
            });

            // Mock analytics handler
            billPreprocessor.analyticsManager.handleInvalidBillData = sandbox.stub().resolves();

            // Mock utility
            const mockUtility = {
                sendNonPaytmBillsMetrics: sandbox.stub()
            };
            sandbox.stub(require('../../lib/index.js'), 'default').returns(mockUtility);

            await billPreprocessor.processBillData(minimalBillData);

            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledWith(
                minimalBillData, undefined);
            expect(billPreprocessor.analyticsManager.handleInvalidBillData).to.have.been.calledWith(
                minimalBillData, 'Minimal bill data validation failed');
        });
    });

    describe("_preprocessBillData", () => {
        it("should preprocess bill data successfully", async () => {
            // Mock the strategy to return processed data
            const mockStrategy = {
                process: sandbox.stub().resolves(mockBillData),
                constructor: { name: 'MockStrategy' }
            };
            billPreprocessor._getPreprocessingStrategy = sandbox.stub().returns(mockStrategy);

            const result = await billPreprocessor._preprocessBillData(mockBillData);
            expect(result).to.deep.equal(mockBillData);
        });

        it("should handle preprocessing error", async () => {
            const preprocessingError = new Error('Strategy processing failed');
            billPreprocessor._getPreprocessingStrategy = sandbox.stub().returns({
                process: sandbox.stub().rejects(preprocessingError),
                constructor: { name: 'MockStrategy' }
            });

            await expect(billPreprocessor._preprocessBillData(mockBillData))
                .to.be.rejectedWith('Strategy processing failed');
        });

        it("should log strategy usage", async () => {
            // Mock the strategy
            const mockStrategy = {
                process: sandbox.stub().resolves(mockBillData),
                constructor: { name: 'MockStrategy' }
            };
            billPreprocessor._getPreprocessingStrategy = sandbox.stub().returns(mockStrategy);

            await billPreprocessor._preprocessBillData(mockBillData);
            expect(options.L.log).to.have.been.calledWith('preprocessBillData', 
                sinon.match.string);
        });

        it("should handle case-insensitive service and paytype", async () => {
            const billDataWithUpperCase = {
                ...mockBillData,
                service: 'MOBILE',
                paytype: 'POSTPAID'
            };

            // Mock the strategy to avoid actual processing
            const mockStrategy = {
                process: sandbox.stub().resolves(billDataWithUpperCase),
                constructor: { name: 'MockStrategy' }
            };
            billPreprocessor._getPreprocessingStrategy = sandbox.stub().returns(mockStrategy);

            await billPreprocessor._preprocessBillData(billDataWithUpperCase);
            
            // Should convert to lowercase internally
            expect(billPreprocessor._getPreprocessingStrategy).to.have.been.calledWith('mobile', 'postpaid');
        });

        it("should handle missing service and paytype", async () => {
            const billDataWithoutService = { ...mockBillData };
            delete billDataWithoutService.service;
            delete billDataWithoutService.paytype;

            // Mock the strategy
            const mockStrategy = {
                process: sandbox.stub().resolves(billDataWithoutService),
                constructor: { name: 'MockStrategy' }
            };
            billPreprocessor._getPreprocessingStrategy = sandbox.stub().returns(mockStrategy);

            await billPreprocessor._preprocessBillData(billDataWithoutService);
            
            expect(billPreprocessor._getPreprocessingStrategy).to.have.been.calledWith('', '');
        });
    });

    describe("registerPreprocessingStrategy", () => {
        it("should register valid preprocessing strategy", () => {
            const mockStrategy = {
                process: sandbox.stub(),
                preprocess: sandbox.stub(), // Add preprocess method for validation
                getServiceType: sandbox.stub().returns('gas'),
                getPaytype: sandbox.stub().returns('prepaid'),
                constructor: { name: 'GasStrategy' }
            };

            billPreprocessor.registerPreprocessingStrategy(mockStrategy);

            expect(billPreprocessor.preprocessingStrategies.has('gas:prepaid')).to.be.true;
            expect(options.L.log).to.have.been.calledWith('BillPreprocessor', 
                sinon.match.string);
        });

        it("should throw error for invalid strategy", () => {
            const invalidStrategy = {
                process: sandbox.stub(),
                // Missing getServiceType method
                constructor: { name: 'InvalidStrategy' }
            };

            expect(() => {
                billPreprocessor.registerPreprocessingStrategy(invalidStrategy);
            }).to.throw('Invalid strategy: must have preprocess() and getServiceType() methods');
        });

        it("should throw error for null strategy", () => {
            expect(() => {
                billPreprocessor.registerPreprocessingStrategy(null);
            }).to.throw('Invalid strategy: must have preprocess() and getServiceType() methods');
        });

        it("should log error when registration fails", () => {
            const invalidStrategy = null;

            try {
                billPreprocessor.registerPreprocessingStrategy(invalidStrategy);
            } catch (error) {
                // Error expected
            }

            expect(options.L.error).to.have.been.calledWith('BillPreprocessor', 
                sinon.match.string);
        });
    });

    describe("registerDatabaseEventStrategy", () => {
        it("should register valid database event strategy", () => {
            const mockStrategy = {
                execute: sandbox.stub(),
                constructor: { name: 'ArchiveStrategy' }
            };

            billPreprocessor.registerDatabaseEventStrategy('archive', mockStrategy);

            expect(billPreprocessor.databaseEventStrategies.has('archive')).to.be.true;
            expect(options.L.log).to.have.been.calledWith('BillPreprocessor', 
                sinon.match.string);
        });

        it("should throw error for invalid strategy", () => {
            const invalidStrategy = {
                // Missing execute method
                constructor: { name: 'InvalidStrategy' }
            };

            expect(() => {
                billPreprocessor.registerDatabaseEventStrategy('invalid', invalidStrategy);
            }).to.throw('Invalid strategy: must have execute() method');
        });

        it("should throw error for null strategy", () => {
            expect(() => {
                billPreprocessor.registerDatabaseEventStrategy('invalid', null);
            }).to.throw('Invalid strategy: must have execute() method');
        });

        it("should log error when registration fails", () => {
            const invalidStrategy = null;

            try {
                billPreprocessor.registerDatabaseEventStrategy('invalid', invalidStrategy);
            } catch (error) {
                // Error expected
            }

            expect(options.L.error).to.have.been.calledWith('BillPreprocessor', 
                sinon.match.string);
        });
    });

    describe("Integration Tests", () => {
        it("should process complete bill data flow successfully", async () => {
            // Mock successful validation
            billPreprocessor.validator.validateBillsData.returns({ 
                isValid: true, 
                error: null 
            });

            // Mock successful preprocessing
            billPreprocessor._preprocessBillData = sandbox.stub().resolves(mockBillData);

            // Mock successful database operation
            const mockDbStrategy = {
                execute: sandbox.stub().resolves()
            };
            billPreprocessor._getDatabaseEventStrategy = sandbox.stub().returns(mockDbStrategy);

            await billPreprocessor.processBillData(mockBillData);

            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledOnce;
            expect(billPreprocessor._preprocessBillData).to.have.been.calledOnce;
            expect(billPreprocessor._getDatabaseEventStrategy).to.have.been.calledWith('upsert');
            expect(mockDbStrategy.execute).to.have.been.calledWith(mockBillData);
        });

        it("should handle complete error flow", async () => {
            // Mock validation failure
            billPreprocessor.validator.validateBillsData.returns({ 
                isValid: false, 
                error: 'Complete validation failure' 
            });

            await billPreprocessor.processBillData(mockBillData);

            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledOnce;
            expect(billPreprocessor.analyticsManager.handleInvalidBillData).to.have.been.calledWith(
                mockBillData, 'Complete validation failure');
        });

        it("should handle different database events", async () => {
            const testEvents = ['upsert', 'delete', 'findAndCreate', 'findAndUpdateData'];
            
            for (const event of testEvents) {
                const billDataWithEvent = { ...mockBillData, dbEvent: event };
                
                // Mock successful processing
                billPreprocessor.validator.validateBillsData.returns({ 
                    isValid: true, 
                    error: null 
                });
                billPreprocessor._preprocessBillData = sandbox.stub().resolves(billDataWithEvent);
                
                const mockDbStrategy = {
                    execute: sandbox.stub().resolves()
                };
                billPreprocessor._getDatabaseEventStrategy = sandbox.stub().returns(mockDbStrategy);

                await billPreprocessor.processBillData(billDataWithEvent);

                expect(billPreprocessor._getDatabaseEventStrategy).to.have.been.calledWith(event);
                expect(mockDbStrategy.execute).to.have.been.calledWith(billDataWithEvent);
            }
        });
    });

    describe("Edge Cases", () => {
        it("should handle empty bill data object", async () => {
            const emptyBillData = {};
            
            await billPreprocessor.processBillData(emptyBillData);
            
            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledWith(
                emptyBillData, undefined);
        });

        it("should handle bill data with only required fields", async () => {
            const minimalBillData = {
                dbEvent: 'upsert',
                debugKey: 'minimal-key'
            };
            
            await billPreprocessor.processBillData(minimalBillData);
            
            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledWith(
                minimalBillData, 'upsert');
        });

        it("should handle very large bill data object", async () => {
            const largeBillData = {
                ...mockBillData,
                extra: {
                    largeField: 'x'.repeat(10000),
                    nestedObject: {
                        deepField: 'y'.repeat(5000)
                    }
                }
            };
            
            await billPreprocessor.processBillData(largeBillData);
            
            expect(billPreprocessor.validator.validateBillsData).to.have.been.calledWith(
                largeBillData, 'upsert');
        });

        it("should handle concurrent processing", async () => {
            const promises = [];
            
            for (let i = 0; i < 5; i++) {
                const billData = { ...mockBillData, debugKey: `concurrent-${i}` };
                promises.push(billPreprocessor.processBillData(billData));
            }
            
            await Promise.all(promises);
            
            expect(billPreprocessor.validator.validateBillsData).to.have.callCount(5);
        });
    });

    describe("Performance Tests", () => {
        it("should process bill data within reasonable time", async () => {
            const startTime = Date.now();
            
            await billPreprocessor.processBillData(mockBillData);
            
            const endTime = Date.now();
            const processingTime = endTime - startTime;
            
            // Should complete within 100ms (adjust based on your requirements)
            expect(processingTime).to.be.lessThan(100);
        });

        it("should handle multiple strategy lookups efficiently", async () => {
            const services = ['mobile', 'electricity', 'creditcard', 'dth', 'fastag', 'rent'];
            const paytypes = ['prepaid', 'postpaid', null];
            
            const startTime = Date.now();
            
            for (const service of services) {
                for (const paytype of paytypes) {
                    billPreprocessor._getPreprocessingStrategy(service, paytype);
                }
            }
            
            const endTime = Date.now();
            const lookupTime = endTime - startTime;
            
            // Should complete within 50ms
            expect(lookupTime).to.be.lessThan(50);
        });
    });
}); 