/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    import utility from '../../lib/index'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import electricitysmsParsingBillPayment from '../../services/smsParsingBillPayment/electricityIndex';
import ElectricitySMSParsingBillPayment from '../../services/smsParsingBillPayment/electricityIndex';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new ElectricitySMSParsingBillPayment(options);
                done();
            });
        });

        // it("testing configureKafka function | Error case from initProducer function",()=>{
        //     serviceObj.producer = new serviceObj.infraUtils.kafka.producer();
        //     let stub1 = sinon.stub(serviceObj.producer, "initProducer").callsFake(function fakeFn(callback){
        //         return callback("Error from initProducer");
        //     });
        //     serviceObj.configureKafka((error)=>{
        //         if (error) {
        //             expect(error).to.be.equal("Error from initProducer");
        //         }
        //     });
        //     stub1.restore();
        // });
        it("testing configureKafka function | Error case from initConsumer function",()=>{
            serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback("Error from initConsumer");
            });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal("Error from initConsumer");
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            //serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
            //     return callback(null);
            // });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            // serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").returns(null);
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
    
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
                }
            };
            let resolveOffset = () => {
                return;
            }
            let topic = "SMS_PARSER_ELECTRICITY";
            let partition = 0;
            serviceObj.execSteps(record, resolveOffset , topic , partition ,(err)=>{
                expect(err).to.be.equal(undefined)
            });
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkasmsParsingBillPaymentConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
    
        it("start function || configureKafka error case", () => {
            let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
            sinon.stub(process, 'exit');
            let stub2 = sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback("configureKafka error response callback");
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            process.exit.restore();
            stub1.restore();
            stub2.restore();
        })
        it("start function || Error in configureKafka", () => {
            let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
            serviceObj.configureKafka = (callback)=>{
                return callback("Error in configureKafka");
            }

            sinon.stub(process, 'exit');     
            let clock = sinon.useFakeTimers();
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);            
                }
            });
            expect(stub1).to.have.callCount(1);
            clock.tick(86400000) 
            process.exit.restore();
            stub1.restore();
        })
        it("start function || configureKafka success case", () => {
            let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
            let stub2 =sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback();
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })

    
    });

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
         let record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new ElectricitySMSParsingBillPayment(options);
                done();
            });
        });

        it("testing processBatch | execution count", () => {
            let data =[{
                "topic":"SMS_PARSER_ELECTRICITY",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                },{
                    "topic":"SMS_PARSER_ELECTRICITY",
                    "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').callsFake(function fakeFn(callback){
                setTimeout(() => {
                    callback();
                }, 1)
            });
            // let stub2 = sinon.spy(serviceObj, 'processData');
            serviceObj.processBatch(data, function(error){
                if(error){
                    console.log("what is the eror", error);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processBatch | correct payload", () => {
            let data =[{
                "topic":"SMS_PARSER_ELECTRICITY",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | correct payload processData rejects", () => {
            let data =[{
                "topic":"SMS_PARSER_ELECTRICITY",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').rejects();
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | incorrect payload", () => {
            let data ={
                "topic":"SMS_PARSER_ELECTRICITY",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("testing processBatch | empty payload", () => {
            let data =[];  
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });

        it("testing processData | correct payload for level_2_category = 1", () => {
            let data ={
                    "topic":"SMS_PARSER_ELECTRICITY",
                    "value":'{"data":[{"deviceDateTime":1694585795894,"collector_timestamp":1694585797277,"wakeUpTimeInterval":14434132,"msg_id":"1253e25e-f6bb-4198-a37b-451c393d0eb0","cId":"1707104162","timestamp":"Wed Sep 13 06:16:37 UTC 2023","uploadTime":1694585796087,"smsUUID":"7b99f73e-8223-49f2-942e-57710ed60ecc","smsDateTime":1694574670782,"smsBody":" CA No. 000101185359 , Rs. 6,400 is due on 15-SEP-23. To view & pay your bill click rml.fm/bTsB3a .Team BYPL\\r","smsSenderID":"JD-BSESYP","smsReceiver":"9891055031","smsOperator":"JIO 4G — Jio","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","secondary_ca_no":null,"bill_due_date":"2023-10-15","bill_due_amount":"6400.0","operator":"MSEDCL"},"level_2_category":1}],"kafka_topic":["SMS_PARSER_ELECTRICITY"]}'
                }   
            let stub1 = sinon.stub(serviceObj.postpaidSmsParsing, 'executeStrategy').returns(null);
            serviceObj.processData(data,function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processData | correct payload for level_2_category = 2", () => {
            let data ={
                    "topic":"SMS_PARSER_ELECTRICITY",
                    "value":'{"data":[{"appVersion":"10.32.1","netWorkType":"4G","latitude":19.5977419,"deviceDateTime":1694585795530,"collector_timestamp":1694585797371,"wakeUpTimeInterval":16622187,"osVersion":"29","osType":"android","model":"CPH2137","msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","longitude":76.7073808,"timestamp":"Wed Sep 13 06:16:37 UTC 2023","appCount":2,"uploadFrequency":14400,"clientId":"androidapp","eventType":"smsEvent","uploadTime":1694585796081,"true_client_ip":"2409:4042:e80:9e7c:de09:97b8:d24d:a4a","realTime":false,"db_name":"sms_parsed_data","newUser":false,"event_name":"sms","batteryPercentage":37,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","secondary_ca_no":null,"bill_due_date":null,"bill_due_amount":"2090.0","operator":"MSEDCL"},"level_2_category":2}],"kafka_topic":["SMS_PARSER_ELECTRICITY"]}'
                }   
            let stub1 = sinon.stub(serviceObj.postpaidSmsParsingBillPaid, 'executeStrategy').returns(null);
            serviceObj.processData(data,function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processData | incorrect payload level_2_category other than 1 or 2", () => {
            let data ={
                "topic":"SMS_PARSER_ELECTRICITY",
                "value":'{"data":[{"appVersion":"10.32.1","netWorkType":"4G","latitude":19.5977419,"deviceDateTime":1694585795530,"collector_timestamp":1694585797371,"wakeUpTimeInterval":16622187,"osVersion":"29","osType":"android","model":"CPH2137","msg_id":"d3da2a82-342e-4cb5-93e1-567d52b6f116","cId":"1707104162","longitude":76.7073808,"timestamp":"Wed Sep 13 06:16:37 UTC 2023","appCount":2,"uploadFrequency":14400,"clientId":"androidapp","eventType":"smsEvent","uploadTime":1694585796081,"true_client_ip":"2409:4042:e80:9e7c:de09:97b8:d24d:a4a","realTime":false,"db_name":"sms_parsed_data","newUser":false,"event_name":"sms","batteryPercentage":37,"smsUUID":"7a94021e-632f-4346-91ae-65de910d69a2","smsDateTime":1694585781607,"smsBody":"Thanks for Online payment of Rs 2090.00 towards MSEDCL Energy Bill For Cons No ************.Follow Us on Social Media @Facebook/Twitter/YouTube/Instagram.","smsSenderID":"AX-MSEDCL","smsReceiver":"7249201212","smsOperator":"Jio","predicted_category":"electricity","electricity_details":{"primary_ca_no":"************","secondary_ca_no":null,"bill_due_date":null,"bill_due_amount":"2090.0","operator":"MSEDCL"},"level_2_category":3}],"kafka_topic":["SMS_PARSER_ELECTRICITY"]}'
            } 
            let stub1 = sinon.stub(serviceObj.postpaidSmsParsing, 'executeStrategy').returns(null);
            let stub2 = sinon.stub(serviceObj.postpaidSmsParsingBillPaid, 'executeStrategy').returns(null);
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        });

        it("testing processData | correct payload", () => {
            let data ={
                "topic":"SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"record\":[{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}]}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'processRecords').returns(null);
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });

        it("testing processData | incoorect topic ", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":\"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT\"}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'executeStrategy').returns(null);
            let stub2 = sinon.stub(serviceObj, 'defaultStrategy').returns(null);
            serviceObj.processData(data,function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0)
            stub1.restore();
            stub2.restore();
        });

        it("testing processData | correct payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'executeStrategy').callsFake(function fakeFn(callback){
                return callback(null);
            });
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processData | inccorrect payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"dwh-ingest\"]}"
                }    
            let stub1 = sinon.stub(serviceObj.processCreditCardStrategy, 'executeStrategy').callsFake(function fakeFn(callback){
                return callback(null);
            });
            let stub2 = sinon.stub(serviceObj, 'defaultStrategy').callsFake(function fakeFn(callback){
                return callback(null);
            });
            serviceObj.processData(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        });

        it("testing defaultStrategy | correct payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT",
                "value":"{\"data\":[{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}]}"
                }   
            let result = serviceObj.defaultStrategy(function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            },data);
            expect(result).to.equal(undefined);
        });
    });