/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';

import chai, { assert } from "chai";
import MOMENT from 'moment';
import sinon from 'sinon';
import sinon<PERSON>hai from "sinon-chai";
import BILLSUBSCRIBER from '../../services/billSubscriber';
import L from 'lgr'
import config from '../../config'
import _ from 'lodash';
import helper from '../__mocks__';
import chaiAsPromised from "chai-as-promised";
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module publisher:: _dumpInSQL", function () {
    let billsSubscriberObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("initializeVariable : update ottOperators when updated in config", () => {
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'OPERATOR_OTT', 'COMMON', 'INCLUDE_OPERATOR'],'hotstar,abc')
        billsSubscriberObj.initializeVariable()
        assert.deepEqual(billsSubscriberObj.ottOperators,['hotstar','abc'])
    });

    it("tata power delhi | Bill Fetched| due_date null| setDueDate-> null", () => {
        data = helper.getFFRResponse({operator: "tata power delhi distribution limited", service: "electricity",paytype:"postpaid",bill_date: MOMENT().format('YYYY-MM-DD') , due_date:null});
        record = helper.getDBRecord({operator: "tata power delhi distribution limited", service: "electricity",paytype:"postpaid",bill_date:  MOMENT().format('YYYY-MM-DD'), due_date:null});
        billsSubscriberObj._dumpInSQL(data, record, () => {
            return;
        });
    });

    it("paytmfirst | Bill Fetched | due_date present", () => {
        billsSubscriberObj.bills = {
            updateBillForSameRechargeNum: (cb, tableName, billData) => {
                expect(billData).to.include.all.keys('id','isCreditCardOperator','nextBillFetchDate', 'amount','billDueDate','billDate','billFetchDate','rechargeNumber','productId','customerId','customerMobile','commonAmount','commonStatus','commonDueDate');
                expect(billData.commonDueDate).to.be.equal(data.customerDataResponse.billDueDate);
                expect(billData.commonStatus).to.be.equal(4);
                expect(billData.commonAmount).to.be.equal(data.customerDataResponse.currentBillAmount);
                return cb();
            }
        };
        data = helper.getFFRResponse({bill_date: MOMENT().format('YYYY-MM-DD') , due_date:MOMENT().add(10,'days').format('YYYY-MM-DD')});
        record = helper.getDBRecord({bill_date:  MOMENT().format('YYYY-MM-DD'), due_date:MOMENT().add(10,'days').format('YYYY-MM-DD')});
        billsSubscriberObj._dumpInSQL(data, record, () => {
            return;
        });
    });


    it('airtel | Bill Fetched | due_date null | setDueDate-> NOW()+7', () => {
        billsSubscriberObj.bills = {
            updateBillForSameRNandCID: (cb, tableName, billData) => {
                expect(billData).to.include.all.keys('id','nextBillFetchDate', 'amount','billDueDate','billDate','billFetchDate','rechargeNumber','productId','customerId','customerMobile','commonAmount','commonStatus','commonDueDate');
                expect(billData.commonDueDate).to.be.equal(MOMENT().add(7,'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'));
                expect(billData.commonStatus).to.be.equal(4);
                expect(billData.commonAmount).to.be.equal(data.customerDataResponse.currentBillAmount);
                return cb();
            },
            updateBillForSameRNandDiffCID: (cb, tableName, billData) => {
                expect(billData).to.include.all.keys('id','nextBillFetchDate', 'amount','billDueDate','billDate','billFetchDate','rechargeNumber','productId','customerId','customerMobile','commonAmount','commonStatus','commonDueDate');
                expect(billData.commonDueDate).to.be.equal(MOMENT().add(7,'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'));
                expect(billData.commonStatus).to.be.equal(4);
                expect(billData.commonAmount).to.be.equal(data.customerDataResponse.currentBillAmount);
                return cb();
            }
        };
        data = helper.getFFRResponse({operator: "airtel", service: "mobile",paytype:"postpaid",bill_date: MOMENT().format('YYYY-MM-DD') , due_date:null});
        record = helper.getDBRecord({operator: "airtel", service: "mobile",paytype:"postpaid",bill_date:  MOMENT().format('YYYY-MM-DD'), due_date:null});
        billsSubscriberObj._dumpInSQL(data, record, () => {
            return;
        });
    })

});

describe("Module publisher:: dumpInSQL", function () {
    let billsSubscriberObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("dumpInSQL || success execution", () => {
        let data = {"productInfo":{"operator": "airtel"}},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, 'getBillsRecord').callsFake(function(cb){
            return cb(null,[{"id":1}])
        },tableName, operator, service, rechargeNumber, customerId);
        
        let stub2 = sinon.stub(billsSubscriberObj, '_dumpInSQL').callsFake(function fakeFn(data, tableName, callback){
            return callback()
        })
        billsSubscriberObj.dumpInSQL(data, function(err,res) {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            return;
        });
        
    });
    it("dumpInSQL || record not found", () => {
        let data = {"productInfo":{"operator": "airtel"}},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, 'getBillsRecord').callsFake(function(cb){
            return cb(null,[])
        },tableName, operator, service, rechargeNumber, customerId);
        let stub2 = sinon.stub(billsSubscriberObj, '_dumpInSQL').callsFake(function fakeFn(data, tableName, callback){
            return callback()
        })
        billsSubscriberObj.dumpInSQL(data, () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            return;
        });
    });
});

describe("Module publisher:: updateBillStatus", function () {
    let billsSubscriberObj;
    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("updateBillStatus || success execution", () => {
        let params = {"operator": "airtel"};
        let stub1 = sinon.stub(billsSubscriberObj.bills, 'updateBillStatus').callsFake(function(cb){
            return cb(null)
        });
        billsSubscriberObj.updateBillStatus( function(err,res) {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        },params);
        
    });

    it("updateBillStatus || operator not migrated", () => {
        let params = {"operator": "air"};
        let stub1 = sinon.stub(billsSubscriberObj.bills, 'updateBillStatus').callsFake(function(cb){
            return cb(null)
        });
        billsSubscriberObj.updateBillStatus( function(err,res) {
            expect(stub1).to.have.callCount(0);
            stub1.restore();
            return;
        },params);
        
    });

    it("updateAmount || success", () => {
        let params = {"operator": "air"};
        let stub1 = sinon.stub(billsSubscriberObj.bills, 'updateAmount').callsFake(function(cb){
            return cb(null)
        });
        billsSubscriberObj.updateAmount( function(err,res) {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        },params);
        
    });

    it("isCreateBillParamsValid || success", () => {
        let params = {
            "operator": "air",
            "customerId":123,
            "productId": 244,
            "rechargeNumber":'1234'
        };
        let result = billsSubscriberObj.isCreateBillParamsValid(params);
        expect(result).equals(true);
    });
    it("isCreateBillParamsValid || fail", () => {
        let params = {
            "operator": "air",
            "customerId":123,
            "productId": 244,
        };
        let result = billsSubscriberObj.isCreateBillParamsValid(params);
        expect(result).equals(false);
    });
    it("markAsPaid || success", async () => {
        let params = {
            "operator": "airtel",
            "customerId":123,
            "productId": 244,
        };
        let tableName = "bills_airtel"
        let stub1 = sinon.stub(billsSubscriberObj.bills, 'markAsPaid').resolves(true)
        let result = await billsSubscriberObj.markAsPaid(tableName, params);
        expect(result).equals(true);
        expect(stub1).to.have.callCount(1);
        stub1.restore()
    });
    it("markAsPaid || fail", async () => {
        let params = {
            "operator": "airtel",
            "customerId":123,
            "productId": 244,
        };
        let tableName = "bills_airtel"
        let stub1 = sinon.stub(billsSubscriberObj, 'markAsPaid').rejects("unexpected error")
        let result = await billsSubscriberObj.markAsPaid(tableName, params)
        .catch((error)=>{
            expect(result).to.be.equal(undefined);
        })
        expect(stub1).to.have.callCount(1);
        stub1.restore()

    });
    it("startDummyLogs ", () => {
        billsSubscriberObj.startDummyLogs()
    });
    it("suspendOperations ", () => {
        billsSubscriberObj.suspendOperations()
    });

    it("deleteRecord || success", () => {
        let data = {"operator": "airtel"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let extra = JSON.stringify({"reference_id":4})
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getRecordForDeletetion").callsFake(function fakeFn(tableName,cb){
            return cb(null,[{"id":1,"extra":extra}])
        });
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'removeRecord').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, rechargeNumber);
        billsSubscriberObj.deleteRecord(data, () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            return;
        });
    });
    it("deleteRecord || success with no extra", () => {
        let data = {"operator": "airtel"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getRecordForDeletetion").callsFake(function fakeFn(tableName,cb){
            return cb(null,[{"id":1}])
        });
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'removeRecord').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, rechargeNumber);
        billsSubscriberObj.deleteRecord(data, () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            return;
        });
    });
    it("deleteRecord || data null", () => {
        let data = {"operator": "airtel"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getRecordForDeletetion").callsFake(function fakeFn(tableName,cb){
            return cb(null,[])
        });
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'removeRecord').callsFake(function fakeFn(cb){
            return cb(null,[])
        },tableName, rechargeNumber);
        billsSubscriberObj.deleteRecord(data, () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            return;
        });
    });

    it("deleteRecord || error in data", () => {
        let data = {"operator": "airtel"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getRecordForDeletetion").callsFake(function fakeFn(tableName,cb){
            return cb("error in data")
        });
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'removeRecord').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, rechargeNumber);
        billsSubscriberObj.deleteRecord(data, () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            return;
        });
    });
    
    it("updateNotificationStatus || success", () => {
        let data = {"operator": "airtel"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateNotificationStatus").callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub2 = sinon.stub(billsSubscriberObj.users, 'updateBillReminderFlag').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data, false);
        billsSubscriberObj.updateNotificationStatus( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            return;
        }, data, tableName);
    });

    it("updateBill || success", () => {
        let data = {"operator": "airtel"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateBill").callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub2 = sinon.stub(billsSubscriberObj.users, 'fetchAndUpdateUserBill').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data);
        billsSubscriberObj.updateBill( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            return;
        }, data);
    });
    it("updateBill || operator not migrated", () => {
        let data = {"operator": "air"},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateBill").callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub2 = sinon.stub(billsSubscriberObj.users, 'fetchAndUpdateUserBill').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data);
        billsSubscriberObj.updateBill( () => {
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            return;
        }, data);
    });
    
    it("updateRecentBill || success || updateAllCustIdRecords -> true", () => {
        let data = {"operator": "airtel",
    "updateAllCustIdRecords":true},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateRecentBill").callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data, true);
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'updateBillsForSameRechargeNum').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub3 = sinon.stub(billsSubscriberObj.users, 'updateBillReminderFlag').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data, true);
        billsSubscriberObj.updateRecentBill( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, false);
    });
    it("updateRecentBill || success || operator not migrated", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":true},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateRecentBill").callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data, true);
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'updateBillsForSameRechargeNum').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub3 = sinon.stub(billsSubscriberObj.users, 'updateBillReminderFlag').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data, true);
        billsSubscriberObj.updateRecentBill( () => {
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, false);
    });
    it("updateRecentBill || success || error in updateRecentBill", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":true},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateRecentBill").callsFake(function fakeFn(cb){
            return cb("error received")
        },tableName, data, true);
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'updateBillsForSameRechargeNum').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub3 = sinon.stub(billsSubscriberObj.users, 'updateBillReminderFlag').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data, true);
        billsSubscriberObj.updateRecentBill( () => {
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, false);
    });
    it("updateRecentBill || success || updateAllCustIdRecords:false", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "updateRecentBill").callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data, true);
        let stub2 = sinon.stub(billsSubscriberObj.bills, 'updateBillsForSameRechargeNum').callsFake(function fakeFn(cb){
            return cb(null)
        },tableName, data);
        let stub3 = sinon.stub(billsSubscriberObj.users, 'updateBillReminderFlag').callsFake(function fakeFn(cb){
            return cb(null)
        },'users', data, true);
        billsSubscriberObj.updateRecentBill( () => {
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, false);
    });
    
    it("getMultipleBill || success", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getMultipleBill").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, operator, customerId, productId, rechargeNumber);
        billsSubscriberObj.getMultipleBill( () => {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        }, operator, customerId, productId, rechargeNumber);
    });
    it("getMultipleBill || operator not migrated", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName ='bills_airtel',
        operator='air',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getMultipleBill").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, operator, customerId, productId, rechargeNumber);
        billsSubscriberObj.getMultipleBill( () => {
            expect(stub1).to.have.callCount(0);
            stub1.restore();
            return;
        }, operator, customerId, productId, rechargeNumber);
    });
    it("getMultipleBill || db query error", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getMultipleBill").callsFake(function fakeFn(cb){
            return cb("error received")
        }, tableName, operator, customerId, productId, rechargeNumber);
        billsSubscriberObj.getMultipleBill( () => {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        }, operator, customerId, productId, rechargeNumber);
    });
    it("getMultipleBill || db query null response", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName ='bills_airtel',
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getMultipleBill").callsFake(function fakeFn(cb){
            return cb(null,[])
        }, tableName, operator, customerId, productId, rechargeNumber);
        billsSubscriberObj.getMultipleBill( () => {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        }, operator, customerId, productId, rechargeNumber);
    });
    
    it("getAllNotificationRecords || success", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard','bills_airtel'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getNotificationRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        let stub2 = sinon.stub(billsSubscriberObj.bills, "getNotificationPrepaidRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        let stub3 = sinon.stub(billsSubscriberObj.bills, "getNotificationCCRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        billsSubscriberObj.getAllNotificationRecords( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, operator, tableName);
    });

    it("getAllNotificationRecords || error case 1", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard','bills_airtel'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getNotificationRecords").callsFake(function fakeFn(cb){
            return cb("error here")
        }, tableName, data, operator);

        let stub2 = sinon.stub(billsSubscriberObj.bills, "getNotificationPrepaidRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        let stub3 = sinon.stub(billsSubscriberObj.bills, "getNotificationCCRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        billsSubscriberObj.getAllNotificationRecords( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, operator, tableName);
    });
    it("getAllNotificationRecords || error case 2", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard','bills_airtel'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getNotificationRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        let stub2 = sinon.stub(billsSubscriberObj.bills, "getNotificationPrepaidRecords").callsFake(function fakeFn(cb){
            return cb("error here")
        }, tableName, data, operator);

        let stub3 = sinon.stub(billsSubscriberObj.bills, "getNotificationCCRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        billsSubscriberObj.getAllNotificationRecords( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, operator, tableName);
    });
    it("getAllNotificationRecords || error case 3", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard','bills_airtel'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getNotificationRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        let stub2 = sinon.stub(billsSubscriberObj.bills, "getNotificationPrepaidRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        let stub3 = sinon.stub(billsSubscriberObj.bills, "getNotificationCCRecords").callsFake(function fakeFn(cb){
            return cb("error here")
        }, tableName, data, operator);

        billsSubscriberObj.getAllNotificationRecords( () => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            return;
        }, data, operator, tableName);
    });
    
    it("getNotificationRecords || successs", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard','bills_airtel'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getNotificationRecords").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        billsSubscriberObj.getNotificationRecords( () => {
            expect(stub1).to.have.callCount(2);
            stub1.restore();
            return;
        }, data, operator, tableName);
    });

    it("getCCBill || success", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        referenceId='abc1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getCCBill").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        billsSubscriberObj.getCCBill( () => {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        }, operator, customerId, productId, referenceId);
    });
    it("getCCBill || operator not migrated", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard'],
        operator='air',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        referenceId='abc1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getCCBill").callsFake(function fakeFn(cb){
            return cb(null,[{"id":1}])
        }, tableName, data, operator);

        billsSubscriberObj.getCCBill( () => {
            expect(stub1).to.have.callCount(0);
            stub1.restore();
            return;
        }, operator, customerId, productId, referenceId);
    });
    it("getCCBill || error returned", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        referenceId='abc1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getCCBill").callsFake(function fakeFn(cb){
            return cb("got error")
        }, tableName, data, operator);

        billsSubscriberObj.getCCBill( () => {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        }, operator, customerId, productId, referenceId);
    });
    it("getCCBill || got null db records", () => {
        let data = {"operator": "air",
    "updateAllCustIdRecords":false},
        tableName =['bills_creditcard'],
        operator='airtel',
        service='mobile',
        rechargeNumber = '1234',
        productId=234,
        referenceId='abc1234',
        customerId = 123;
        let stub1 = sinon.stub(billsSubscriberObj.bills, "getCCBill").callsFake(function fakeFn(cb){
            return cb(null, [])
        }, tableName, data, operator);

        billsSubscriberObj.getCCBill( () => {
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return;
        }, operator, customerId, productId, referenceId);
    });

});



 

describe("Module publisher:: getDateCategorization", function () {
    let billsSubscriberObj;

    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("Date parsing | VALID_DATE | input date format 'DD-MM-YYYY' | input date : '21-12-2021'", (done) => {
        let inputDate = String(MOMENT().add(1, 'months').format('DD-MM-YYYY'));
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateObj.type).to.be.equal("VALID_DATE");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });

    it("Date parsing | IN-ACTIONABLE_DATE | input date format 'DD-MM-YYYY' | input date : '20-03-2000'", (done) => {
        let inputDate = "20-03-2000";
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateObj.type).to.be.equal("IN-ACTIONABLE_DATE");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });


    it("Date parsing | IN-ACTIONABLE_DATE | input date format 'DD-MM-YYYY' | input date : '20-03-2100'", (done) => {
        let inputDate = "20-03-2100";
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(true);
        expect(parsedDateObj.type).to.be.equal("IN-ACTIONABLE_DATE");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });

    it("Date parsing | IN-ACTIONABLE_DATE | input date format 'DD-MM-YYYY' | input date : '20-03-1900'", (done) => {
        let inputDate = "20-03-1900";
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateObj.type).to.be.equal("IN-ACTIONABLE_DATE");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });

    it("Date parsing | IN-ACTIONABLE_DATE | input date format 'DD-MM-YYYY' | input date : '20-03-0022'", (done) => {
        let inputDate = "20-03-0022";
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateObj.type).to.be.equal("IN-ACTIONABLE_DATE");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });

    it("Date parsing | INVALID_DATE_FORMAT | input date format 'DD-YYYY-MM' | input date : '20-2020-03'", (done) => {
        let inputDate = "20-2020-03";
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateObj.type).to.be.equal("INVALID_DATE_FORMAT");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-YYYY-MM') , 'seconds')).to.be.equal(0);
        }
        return done();
    });
    it("Date parsing | DATE_MONTH_OUT_OF_BOUND | input date format 'MM-DD-YYYY' | input date : '03-20-2020'", (done) => {
        let inputDate = "03-20-2020";
        debugger
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateObj.type).to.be.equal("DATE_MONTH_OUT_OF_BOUND");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'MM-DD-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });

    it("Date parsing | DATE_MONTH_OUT_OF_BOUND | input date format 'DD-MM-YYYY' | input date : '35-03-2020'", (done) => {
        let inputDate = "35-03-2020";
        let parsedDateObj = billsSubscriberObj.getDateCategorization(inputDate);
        expect(parsedDateObj.isDateFmtValid).to.be.equal(false);
        expect(parsedDateObj.type).to.be.equal("DATE_MONTH_OUT_OF_BOUND");

        if (parsedDateObj.isDateFmtValid) {
            expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
        }
        return done();
    });


    it("sendDateForAnalytics ", (done) => {
        
        billsSubscriberObj.sendDateForAnalytics({
            operator: "airtel",
            productId: 978,
            billDueDate: "20-04-2022",
            billDate:  "05-04-2022",
            parsedDueDateObj : { value: MOMENT("20-04-2022",'DD-MM-YYYY') , isDateFmtValid : true },
            parsedBillDateObj : { value: MOMENT("20-04-2022",'DD-MM-YYYY') , isDateFmtValid : true },
            rechargeNumber: "1231314314"
        });
        
        return done();
    });


    


});


describe("Module publisher:: getNextBillFetchDateForRetry", function () {
    let billsSubscriberObj;

    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });
    it("getNextBillFetchDateForRetry | NEXT_BILL_FETCH_DATES = -1   ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), dueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD')
        let date = billsSubscriberObj.getNextBillFetchDateForRetry('jio',billDate, dueDate, null, null, 1, ['jio'])
        console.log("date",date);
        expect(MOMENT(date).startOf('day').format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT(billDate).add(1,'days').format('YYYY-MM-DD HH:mm:ss'));
    });
    
});


describe("Module publisher:: _calcNextBillFetchDate", function () {
    let billsSubscriberObj;

    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });
    it("getNextBillFetchDateForRetry | connection error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: true,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        // if(MOMENT().diff(MOMENT().startOf('day').add(9,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        console.log("result",result);
        expect(result.status).to.be.equal(8);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(9,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });
    it("getNextBillFetchDateForRetry | validation error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: false,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        // if(MOMENT().diff(MOMENT().startOf('day').add(10,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        expect(result.status).to.be.equal(9);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(10,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });        
            
        
    it("getNextBillFetchDateForRetry | no bill error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: true,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        expect(result.status).to.be.equal(6);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT(lastBillDate).startOf('day').add(1,'months').format('YYYY-MM-DD HH:mm:ss'));
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });      
    it("getNextBillFetchDateForRetry | no bill error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: true,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        expect(result.status).to.be.equal(6);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT(lastBillDate).startOf('day').add(1,'months').format('YYYY-MM-DD HH:mm:ss'));
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });     
    
    it("getNextBillFetchDateForRetry | no bill error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = null, lastDueDate = null;

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: true,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        // if(MOMENT().diff(MOMENT().startOf('day').add(16,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        console.log("result",result);
        expect(result.status).to.be.equal(6);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(16,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    }); 

    it("getNextBillFetchDateForRetry | invalid due date error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: false,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        // if(MOMENT().diff(MOMENT().startOf('day').add(11,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        expect(result.status).to.be.equal(10);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(11,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });
    it("getNextBillFetchDateForRetry | invalid record status 13 error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: {
                invalid : 1
            }
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        // if(MOMENT().diff(MOMENT().startOf('day').add(12,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        expect(result.status).to.be.equal(13);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(12,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });
    it("getNextBillFetchDateForRetry | INVALID_BILLDATE_DUEDATE error rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(MOMENT(billDate).add(10,'days').format('YYYY-MM-DD')),
        lastBillDate = MOMENT(MOMENT().subtract(20,'days').format('YYYY-MM-DD')), lastDueDate = MOMENT(MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD'));

        let params = {
            operator : "kerala state electricity board ltd (kseb ltd)",
            amount: "-1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_ksebprepaid',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['kerala state electricity board ltd (kseb ltd)'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        // if(MOMENT().diff(MOMENT().startOf('day').add(16,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        expect(result.status).to.be.equal(6);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(16,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });
    it("getNextBillFetchDateForRetry | INVALID_BILLDATE_DUEDATE but successful fetch rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(MOMENT(billDate).add(10,'days').format('YYYY-MM-DD')),
        lastBillDate = MOMENT(MOMENT().subtract(20,'days').format('YYYY-MM-DD')), lastDueDate = MOMENT(MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD'));

        let params = {
            operator : "kerala state electricity board ltd (kseb ltd)",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_ksebprepaid',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['kerala state electricity board ltd (kseb ltd)'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        expect(result.status).to.be.equal(4);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'months').format('YYYY-MM-DD'));    
        stub1.restore();
        stub2.restore();
    });
    it("getNextBillFetchDateForRetry | deduced status true but validation fail error rescheduling ", () => {
        let billDate=  null, billDueDate = null,
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "0",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        // if(MOMENT().diff(MOMENT().startOf('day').add(10,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        expect(result.status).to.be.equal(9);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(10,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });    
    it("getNextBillFetchDateForRetry | early bill fetch rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(MOMENT(billDate).add(35,'days').format('YYYY-MM-DD')),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        // if(MOMENT().diff(MOMENT().startOf('day').add(13,'hours'))>0){
        //     console.log("result",result);
        //     expect(result.status).to.be.equal(5);
        //     expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'days').format('YYYY-MM-DD'));    
        // }
        // else{
        console.log("result",result);
        expect(result.status).to.be.equal(16);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(13,'hours').format('YYYY-MM-DD HH:mm:ss'));
        // }
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });  
    it("getNextBillFetchDateForRetry | old bill found rescheduling ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(MOMENT(billDate).subtract(10,'days').format('YYYY-MM-DD')),
        lastBillDate = null, lastDueDate =null;

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
            console.log("result",result);
            expect(result.status).to.be.equal(5);
            expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD')).to.be.equal(MOMENT().startOf('day').add(1,'months').format('YYYY-MM-DD'));    
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    }); 

    it("getNextBillFetchDateForRetry | old bill found rescheduling for same day ", () => {
        let billDate=  MOMENT().subtract(1,'months').format('YYYY-MM-DD'), billDueDate = MOMENT(MOMENT(billDate).subtract(10,'days').format('YYYY-MM-DD')),
        lastBillDate = null, lastDueDate =null;

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
            console.log("result",result);
            expect(result.status).to.be.equal(5);
            expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().startOf('day').add(14,'hours').format('YYYY-MM-DD HH:mm:ss'));
            expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    }); 

    it("getNextBillFetchDateForRetry | success billl fetch | NBFD retry check to set in future", () => {
        let billDate=  MOMENT().subtract(31,'days').format('YYYY-MM-DD'), billDueDate = MOMENT(MOMENT(billDate).add(32,'days').format('YYYY-MM-DD')),
        lastBillDate = null, lastDueDate =null;

        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: billDate,
            billDueDate: billDueDate,
            lastDueDate: lastDueDate,
            lastBillDate: lastBillDate,
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
            console.log("result",result);
            expect(result.status).to.be.equal(4);
            expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT().add(1,'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'));
            expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    }); 

    it("getNextBillFetchDateForRetry | successfull bill fetch ", () => {
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');
            
        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT(billDate),
            billDueDate: MOMENT(billDueDate),
            lastDueDate: MOMENT(lastDueDate),
            lastBillDate: MOMENT(lastBillDate),
            isConnectionError: false,
            deducedStatus: true,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: null,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        console.log("result",result);
        expect(result.status).to.be.equal(4);
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT(billDate).startOf('day').add(1,'months').format('YYYY-MM-DD HH:mm:ss'));
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });
    
    it("getNextBillFetchDateForRetry | invalid CA number ", () => { 
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'GATEWAY_ERROR_MAPPING', 'euronetpostpaid', 'INVALID_CA_ERROR_CODE'],1033)
        let billDate=  MOMENT().format('YYYY-MM-DD'), billDueDate = MOMENT(billDate).add(10,'days').format('YYYY-MM-DD'),
        lastBillDate = MOMENT().subtract(20,'days').format('YYYY-MM-DD'), lastDueDate = MOMENT(lastBillDate).add(10,'days').format('YYYY-MM-DD');
            
        let params = {
            operator : "jio",
            amount: "1000",
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT(billDate),
            billDueDate: MOMENT(billDueDate),
            lastDueDate: MOMENT(lastDueDate),
            lastBillDate: MOMENT(lastBillDate),
            isConnectionError: false,
            deducedStatus: false,
            noBill: false,
            isDateFmtValid: true,
            defaultNextBillDueDate: MOMENT().add(7, 'days'),
            errorMessageCode: 1033,
            tableName: 'bills_jio',
            lastAmount: 200,
            productId: null,
            oldProductId: null,
            serviceId: null,
            billDateBasedGateways: ['jio'],
            custInfoValues: null,
            gateway : 'euronetpostpaid'
        }

        let stub1 = sinon.stub(billsSubscriberObj,'getNextBillFetchDateForRetry').returns(MOMENT().add(1,'days'));
        let stub2 = sinon.stub(billsSubscriberObj,'getNextRetryFreq').returns(1);
        let result = billsSubscriberObj._calcNextBillFetchDate(params)
        expect(result.status).to.be.equal(13);
        expect(result.reason).to.be.equal('INVALID_CA_NUMBER')
        expect(MOMENT(result.nextBillFetchDate).format('YYYY-MM-DD HH:mm:ss')).to.be.equal(MOMENT(billDate).add(12,'hours').format('YYYY-MM-DD HH:mm:ss'));
        expect(stub1).to.have.callCount(2);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    });
    
});

describe("Module publisher:: getRecordToPublishOnAutomatic", function () {
    let billsSubscriberObj;

    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("should return null when dataArray is empty", () => {
        const result = billsSubscriberObj.getRecordToPublishOnAutomatic([]);
        expect(result).to.be.null;
    });

    it("should return record with status 4 when present", () => {
        const dataArray = [
            { is_automatic: 1, status: 13 },
            { is_automatic: 1, status: 4 },
            { is_automatic: 1, status: 13 }
        ];
        
        const result = billsSubscriberObj.getRecordToPublishOnAutomatic(dataArray);
        expect(result.status).to.equal(4);
    });

    it("should return first status 13 record when no status 4 present", () => {
        const dataArray = [
            { is_automatic: 1, status: 13 },
            { is_automatic: 1, status: 13 },
            { is_automatic: 1, status: 15 }
        ];
        
        const result = billsSubscriberObj.getRecordToPublishOnAutomatic(dataArray);
        expect(result.status).to.equal(13);
    });

    it("should skip records with is_automatic 0 or 5", () => {
        const dataArray = [
            { is_automatic: 0, status: 4 },
            { is_automatic: 5, status: 4 },
            { is_automatic: 1, status: 13 }
        ];
        
        const result = billsSubscriberObj.getRecordToPublishOnAutomatic(dataArray);
        expect(result.status).to.equal(13);
    });

    it("should return null when no valid records found", () => {
        const dataArray = [
            { is_automatic: 0, status: 4 },
            { is_automatic: 5, status: 13 },
            { is_automatic: 0, status: 15 }
        ];
        
        const result = billsSubscriberObj.getRecordToPublishOnAutomatic(dataArray);
        expect(result).to.be.null;
    });
});

describe("Module publisher:: isDisableBillFetchValid", function () {
    let billsSubscriberObj;

    before(function () {
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("isDisableBillFetchValid | valid config ", () => { 
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'DISABLE_BILL_FETCH_ENABLED', 'ENABLE'],1)
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'electricity', 'ERROR_CODE_COUNT_MAPPING'], {"RN1":"5"})
        
        let extraDetails = {};
        let result = billsSubscriberObj.isDisableBillFetchValid('electricity', 'uppcl', 'RN1', extraDetails)
        expect(result).to.be.equal(false);
        expect(extraDetails.errorCounters.RN1Count).to.be.equal(1)
    });

    it("isDisableBillFetchValid | valid config (count reached)", () => {
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'DISABLE_BILL_FETCH_ENABLED', 'ENABLE'],1)
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'electricity', 'ERROR_CODE_COUNT_MAPPING'], {"RN1":"5"})
        
        let extraDetails = {"errorCounters":{"RN1Count": 4}};
        let result = billsSubscriberObj.isDisableBillFetchValid('electricity', 'uppcl', 'RN1', extraDetails)
        expect(result).to.be.equal(true);
        expect(extraDetails.errorCounters.RN1Count).to.be.equal(5)
    });

    it("isDisableBillFetchValid | counter reset", () => {
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'DISABLE_BILL_FETCH_ENABLED', 'ENABLE'],1)
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'electricity', 'ERROR_CODE_COUNT_MAPPING'], {"RN1":"5"})
        
        let extraDetails = {"errorCounters":{"RN1Count": 3}};
        let result = billsSubscriberObj.isDisableBillFetchValid('electricity', 'uppcl', '00', extraDetails)
        expect(result).to.be.equal(false);
        expect(extraDetails.errorCounters).to.not.equal({"RN1Count": 3})
    });

    it("isDisableBillFetchValid | no config", () => {
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'DISABLE_BILL_FETCH_ENABLED', 'ENABLE'],1)
        _.set(billsSubscriberObj.config,['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'electricity', 'ERROR_CODE_COUNT_MAPPING'], null)
        
        let extraDetails = {};
        let result = billsSubscriberObj.isDisableBillFetchValid('electricity', 'uppcl', 'RN1', extraDetails)
        expect(result).to.be.equal(false);
    });

    // it("addOffsetToNBFDbyCustomerRank | valid config pull back NBFD by offset", () => {
    //     let currentRecord = {
    //         customer_bucket: "gold",
    //         service: "electricity",
    //         paytype: "postpaid"
    //     },
    //     nextBillFetchDate = MOMENT().startOf('day').add(7,'days');

    //     let result = billsSubscriberObj.addOffsetToNBFDbyCustomerRank(nextBillFetchDate, currentRecord);
    //     expect(result.startOf('day').diff(MOMENT().startOf('day').add(7,'days'), 'days')).to.be.equal(-2);
    // })

    // it("addOffsetToNBFDbyCustomerRank | if NBFD comes of past", () => {
    //     let currentRecord = {
    //         customer_bucket: "gold",
    //         service: "electricity",
    //         paytype: "postpaid"
    //     },
    //     nextBillFetchDate = MOMENT().startOf('day').add(1,'days');

    //     let result = billsSubscriberObj.addOffsetToNBFDbyCustomerRank(nextBillFetchDate, currentRecord);
    //     expect(result.startOf('day').diff(MOMENT().startOf('day').add(1,'days'), 'days')).to.be.equal(0);
    // })

})


/*
describe("Module: publisher:: _calcNBFD", function () {
    let billsSubscriberObj;
    before(function () { 
        STARTUP_MOCK.init(function(error, options){
            billsSubscriberObj = new BILLSUBSCRIBER(options);
            done();
        });
    });

    it("billBasedGatway operator- VIL: Case:: Bill Found!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT().startOf('day'),
            billDueDate: MOMENT().add(12,'days').startOf('day'),
            lastDueDate: MOMENT().add(12-30,'days').startOf('day'),
            lastBillDate: MOMENT().add(-30,'days').startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(params.billDate, 'days')).to.be.equal(30);
        expect(result.status).to.be.equal(4);
    });

    it("billBasedGatway operator- VIL: Case:: Old Bill!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT().add(-35,'days').startOf('day'), // previous cycle
            billDueDate: MOMENT().add(-65,'days').startOf('day'), // previous cycle
            lastDueDate: MOMENT().add(12-30-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-20).startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT(MOMENT().startOf('day')), 'days')).to.be.equal(1);
        expect(result.status).to.be.equal(5);
    });



    it("billBasedGatway operator- VIL: Case:: No Bill!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, // previous cycle
            billDueDate: null, // previous cycle
            lastDueDate: MOMENT().add(-50,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-65,'days').startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:true,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT(MOMENT().startOf('day')), 'days')).to.be.equal(1);
        expect(result.status).to.be.equal(6);
    });


    it("billBasedGatway operator- VIL: Case:: Connection Error!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT().add(-35,'days').startOf('day'), // previous cycle
            billDueDate: MOMENT().add(-65,'days').startOf('day'), // previous cycle
            lastDueDate: MOMENT().add(12-30-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-20).startOf('day'),
            isConnectionError:true,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT(MOMENT().startOf('day')), 'days')).to.be.equal(1);
        expect(result.status).to.be.equal(8);
    });


    it("billBasedGatway operator- VIL: Case:: Validation failure!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT().add(-35,'days').startOf('day'), // previous cycle
            billDueDate: MOMENT().add(-65,'days').startOf('day'), // previous cycle
            lastDueDate: MOMENT().add(12-30-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-20).startOf('day'),
            isConnectionError:false,
            deducedStatus:false,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT(MOMENT().startOf('day')), 'days')).to.be.equal(1);
        expect(result.status).to.be.equal(9);
    });


    it("billBasedGatway operator- VIL: Case:: No Bill, One day after due_date!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, // previous cycle
            billDueDate: null, // previous cycle
            lastDueDate: MOMENT().add(-5,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-15,'days').startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:true,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(params.lastBillDate, 'days')).to.be.equal(30);
        expect(result.status).to.be.equal(6);
    });

    it("billBasedGatway operator- VIL: Case:: Invalid=1", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT().add(-35,'days').startOf('day'), // previous cycle
            billDueDate: MOMENT().add(-65,'days').startOf('day'), // previous cycle
            lastDueDate: MOMENT().add(12-30-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-20).startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:1}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.status).to.be.equal(13);
    });


    it("billBasedGatway operator- VIL: Case:: Invalid Date Format!", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: MOMENT().add(-35,'days').startOf('day'), // previous cycle
            billDueDate: MOMENT().add(-65,'days').startOf('day'), // previous cycle
            lastDueDate: MOMENT().add(12-30-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-20).startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:false,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT(MOMENT().startOf('day')), 'days')).to.be.equal(1);
        expect(result.status).to.be.equal(10);
    });



    it("billBasedGatway operator- VIL: Case:: customNextBillFetchDate", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: MOMENT().add(10,'days').startOf('day'),
            evaluatedNbfd: null,
            billDate: MOMENT().add(-35,'days').startOf('day'), // previous cycle
            billDueDate: MOMENT().add(-65,'days').startOf('day'), // previous cycle
            lastDueDate: MOMENT().add(12-30-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-20).startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(params.customNextBillFetchDate, 'days')).to.be.equal(0);
        expect(result.status).to.be.equal(4);
    });


    it("billBasedGatway operator- VIL: Case:: DueDate and billDate is null and amount > 0", () => {
        let params = {
            operator: "vodafone idea",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, // previous cycle
            billDueDate: null, // previous cycle
            lastDueDate: MOMENT().add(-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-40).startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_vodafone",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.status).to.be.equal(4);
    });

    it("DueDate based operator- bsesr: Case:: Bill Found", () => {
        let params = {
            operator: "bsesr",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, 
            billDueDate: MOMENT().add(10,'days').startOf('day'), 
            lastDueDate: MOMENT().add(-30,'days').startOf('day'), 
            lastBillDate: MOMENT().add(-40).startOf('day'),
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_relianceenergy",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(params.billDueDate, 'days')).to.be.equal(9);
        expect(result.status).to.be.equal(4);
    });

    it("INVALID_BILLDATE_DUEDATE operator- kseb: Case:: Bill Found", () => {
        let params = {
            operator: "kerala state electricity board ltd (kseb ltd)",
            amount : 234,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, 
            billDueDate: null, 
            lastDueDate: null, 
            lastBillDate: null,
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_ksebprepaid",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT().startOf('day'),'days')).to.be.equal(30);
        expect(result.status).to.be.equal(4);
    });


    it("INVALID_BILLDATE_DUEDATE operator- kseb: Case:: Bill Not Found", () => {
        let params = {
            operator: "kerala state electricity board ltd (kseb ltd)",
            amount : 0,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, 
            billDueDate: null, 
            lastDueDate: null, 
            lastBillDate: null,
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_ksebprepaid",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea'],
            custInfoValues:{invalid:0}
        };
        
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT().startOf('day'), 'days')).to.be.equal(2);
        expect(result.status).to.be.equal(9);
    });

    it("operator: Airtel :: Case:- NoBill-false, dateToBeUsed- null and amount <=0", () => {
        let params = {
            operator: "airtel",
            amount : 0,
            customNextBillFetchDate: null,
            evaluatedNbfd: null,
            billDate: null, 
            billDueDate: null, 
            lastDueDate: null, 
            lastBillDate: null,
            isConnectionError:false,
            deducedStatus:true,
            noBill:false,
            isDateFmtValid:true,
            defaultNextBillDueDate:null,
            errorMessageCode:null,
            tableName:"bills_airtel",
            lastAmount:100,
            productId:1,
            oldProductId: 1,
            serviceId: 0,
            billDateBasedGateways: ['vodafone','vodafone idea','idea','airtel'],
            custInfoValues:{invalid:0}
        };
        let result = billsSubscriberObj._calcNextBillFetchDate(params);
        expect(result.nextBillFetchDate.startOf('day').diff(MOMENT().startOf('day'),'days')).to.be.equal(1);
        expect(result.status).to.be.equal(9);
    });

});
*/