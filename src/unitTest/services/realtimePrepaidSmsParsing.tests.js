/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    import utility from '../../lib/index'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import realtimeSmsParsingPrepaid from '../../services/realtimeSmsParsing/prepaid';
    import ASYNC from 'async';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    describe('start function', () => {
        let options, instance,serviceObj;
      
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new realtimeSmsParsingPrepaid(options);
            });
        });
      
        it('should configure <PERSON>fka and connect to MongoDB successfully', (done) => {
            const connectMongoStub = sinon.stub(serviceObj.mongoDbInstance, 'connect').resolves();
            const asyncWaterfallStub = sinon.stub(serviceObj, 'configureKafka').callsFake((cb)=>{
                return cb(null);
            })
            // Call the start function
            try{
                serviceObj.start();
            }catch(err){
                  // Assertions
            expect(serviceObj.configureKafka.calledOnce).to.be.true;
            expect(connectMongoStub.calledOnce).to.be.true;
            expect(asyncWaterfallStub.calledOnce).to.be.true;
            }finally{
                connectMongoStub.restore();
                 asyncWaterfallStub.restore();
            }        
        
            // Restore the stubs
            return done();
          });
        
          it('should handle errors during Kafka configuration and MongoDB connection', async () => {
            // Mock the necessary functions for promisify
            const connectMongoStub = sinon.stub(serviceObj.mongoDbInstance, 'connect').resolves();

        
            const asyncWaterfallStub = sinon.stub(serviceObj, 'configureKafka').callsFake((cb)=>{
                return cb(null);
            })
        
            // Call the start function
            try{
                serviceObj.start()
            }catch(error){
            // Assertions
            expect(serviceObj.configureKafka.calledOnce).to.be.true;
            expect(connectMongoStub.calledOnce).to.be.true;
            expect(asyncWaterfallStub.calledOnce).to.be.true;
        
            // Restore the stubs
            connectMongoStub.restore();
            asyncWaterfallStub.restore();
            }
          });
      });

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new realtimeSmsParsingPrepaid(options);
                done();
            });
        });

        // it("testing configureKafka function | Error case from initProducer function",()=>{
        //     serviceObj.producer = new serviceObj.infraUtils.kafka.producer();
        //     let stub1 = sinon.stub(serviceObj.producer, "initProducer").callsFake(function fakeFn(callback){
        //         return callback("Error from initProducer");
        //     });
        //     serviceObj.configureKafka((error)=>{
        //         if (error) {
        //             expect(error).to.be.equal("Error from initProducer");
        //         }
        //     });
        //     stub1.restore();
        // });
        it("testing configureKafka function | Error case from initConsumer function",()=>{
            serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback("Error from initConsumer");
            });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal("Error from initConsumer");
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            //serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
            //     return callback(null);
            // });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            // serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").returns(null);
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
    
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
                }
            };
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(2);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
    
        it("start function || configureKafka error case", () => {
            sinon.stub(process, 'exit');
            let stub2 = sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback("configureKafka error response callback");
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
            expect(stub2).to.have.callCount(1);
            process.exit.restore();
            stub2.restore();
        })
        it("start function || Error in configureKafka", () => {
            serviceObj.configureKafka = (callback)=>{
                return callback("Error in configureKafka");
            }

            sinon.stub(process, 'exit');     
            let clock = sinon.useFakeTimers();
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);            
                }
            });
            clock.tick(86400000) 
            process.exit.restore();
        })
        it("start function || configureKafka success case", () => {
            let stub2 =sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback();
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub2).to.have.callCount(1);
            stub2.restore();
        })

    
    });

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
         let record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new realtimeSmsParsingPrepaid(options);
                done();
            });
        });

        it("testing processBatch | execution count", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                },{
                    "topic":"dwh-ingest-SMS_PARSING_LENDING",
                    "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').callsFake(function fakeFn(callback){
                setTimeout(() => {
                    callback();
                }, 1)
            });
            // let stub2 = sinon.spy(serviceObj, 'processData');
            serviceObj.processBatch(data, function(error){
                if(error){
                    console.log("what is the eror", error);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processBatch | correct payload", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | correct payload processData rejects", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').rejects();
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | incorrect payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("testing processBatch | empty payload", () => {
            let data =[];  
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
    });

    describe('processData', function () {
        let serviceObj;
            before(function () {    
                STARTUP_MOCK.init(function(error, options){
                    serviceObj = new realtimeSmsParsingPrepaid(options);
                });
            });
            it('should handle a valid record with "data" key and execute defaultStrategy', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId: 'PREPAID_CLASSIFIER_ID' }],
                  }),
                };
                const nextStub = sinon.stub().callsArg(1);
                const defaultStrategyStub = sinon.stub(serviceObj, 'defaultStrategy').callsFake((callback, smsData) => callback());
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(defaultStrategyStub.calledOnce).to.be.true;
                  defaultStrategyStub.restore();
                  done();
                });
              });

              it('should handle an error in executeStrategy and send metrics to Datadog', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = 'POSTPAID_CLASSIFIER_ID'; // Make sure this matches one of the IDs in POSTPAID_CLASSIFIERS
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const errorMessage = 'Some error occurred in defaultStrategy';
                const executeStrategyStub = sinon.stub(serviceObj, 'defaultStrategy').callsFake((callback, smsData, self) => {
                  // Call the callback with an error
                  callback(new Error(errorMessage));
                });
            
                const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeStrategyStub.calledOnce).to.be.true;
                  expect(sendMetricsToDDStub.calledTwice).to.be.true;
                  executeStrategyStub.restore();
                  sendMetricsToDDStub.restore();
                  done();
                });
              });

              it('should handle a valid record with "data" key and execute prepaid data packs flow', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId: 1 }],
                  }),
                };
                const nextStub = sinon.stub().callsArg(1);
                const executeDataPackStrategyStub = sinon.stub(serviceObj, 'executeDataPackStrategy').callsFake((callback, smsData) => callback());
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeDataPackStrategyStub.calledOnce).to.be.true;
                  executeDataPackStrategyStub.restore();
                  done();
                });
              });

              it('should handle an error in executeDataPackStrategy and send metrics to Datadog', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = '1'; // Make sure this matches one of the IDs in POSTPAID_CLASSIFIERS
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const errorMessage = 'Some error occurred in executeDataPackStrategy';
                const executeDataPackStrategyStub = sinon.stub(serviceObj, 'executeDataPackStrategy').callsFake((callback, smsData, self) => {
                  // Call the callback with an error
                  callback(new Error(errorMessage));
                });
            
                const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeDataPackStrategyStub.calledOnce).to.be.true;
                  console.log(sendMetricsToDDStub.callCount);
                  expect(sendMetricsToDDStub.calledTwice).to.be.true;
                  executeDataPackStrategyStub.restore();
                  sendMetricsToDDStub.restore();
                  done();
                });
              });

              it('should handle a valid record with "data" key and execute segregateValidityAndNoValidty flow', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId: 5 }],
                  }),
                };
                const nextStub = sinon.stub().callsArg(1);
                const segregateValidityAndNoValidtyStub = sinon.stub(serviceObj, 'segregateValidityAndNoValidty').callsFake((callback, smsData, self) => callback());
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(segregateValidityAndNoValidtyStub.calledOnce).to.be.true;
                  segregateValidityAndNoValidtyStub.restore();
                  done();
                });
              });

              it('should handle an error in segregateValidityAndNoValidty and send metrics to Datadog', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = '5'; // Make sure this matches one of the IDs in POSTPAID_CLASSIFIERS
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const errorMessage = 'Some error occurred in segregateValidityAndNoValidtyStub';
                const segregateValidityAndNoValidtyStub = sinon.stub(serviceObj, 'segregateValidityAndNoValidty').callsFake((callback, smsData, self) => {
                  // Call the callback with an error
                  callback(new Error(errorMessage));
                });
            
                const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(segregateValidityAndNoValidtyStub.calledOnce).to.be.true;
                  console.log(sendMetricsToDDStub.callCount);
                  expect(sendMetricsToDDStub.calledTwice).to.be.true;
                  segregateValidityAndNoValidtyStub.restore();
                  sendMetricsToDDStub.restore();
                  done();
                });
              });

              it('should handle a valid record with PREPAID_CLASSIFIERS["RECHARGE_DONE_IDS"]', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = '8'; // Make sure this matches one of the IDs in PREPAID_CLASSIFIERS["RECHARGE_DONE_IDS"]
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const executeStrategyStub = sinon.stub(serviceObj.prepaidSmsparsing, 'executeStrategy').callsFake((callback, smsData, self) => callback());
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeStrategyStub.calledOnce).to.be.true;
                  executeStrategyStub.restore();
                  done();
                });
              });

              it('should handle an error in PREPAID_CLASSIFIERS and send metrics to Datadog', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = '8'; // Make sure this matches one of the IDs in POSTPAID_CLASSIFIERS
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const errorMessage = 'Some error occurred in executeStrategy';
                const executeStrategyStub = sinon.stub(serviceObj.prepaidSmsparsing, 'executeStrategy').callsFake((callback, smsData, self) => {
                  // Call the callback with an error
                  callback(new Error(errorMessage));
                });
            
                const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeStrategyStub.calledOnce).to.be.true;
                  console.log(sendMetricsToDDStub.callCount);
                  expect(sendMetricsToDDStub.calledTwice).to.be.true;
                  executeStrategyStub.restore();
                  sendMetricsToDDStub.restore();
                  done();
                });
              });
            
              it('should handle a valid record with PREPAID_CLASSIFIERS["CT_EVENT_IDS"]', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = '3'; // Make sure this matches one of the IDs in PREPAID_CLASSIFIERS["CT_EVENT_IDS"]
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const executeCtEventsStrategyStub = sinon.stub(serviceObj, 'executeCtEventsStrategy').callsFake((callback, smsData, self) => callback());
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeCtEventsStrategyStub.calledOnce).to.be.true;
                  executeCtEventsStrategyStub.restore();
                  done();
                });
              });
              it('should handle an error in CT_EVENT_IDS and send metrics to Datadog', (done) => {
                // Mock the necessary data for the test case
                const rtspClassId = '3'; // Make sure this matches one of the IDs in POSTPAID_CLASSIFIERS
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId }],
                  }),
                };
                const errorMessage = 'Some error occurred in executeCtEventsStrategyStub';
                const executeCtEventsStrategyStub = sinon.stub(serviceObj, 'executeCtEventsStrategy').callsFake((callback, smsData, self) => {
                  // Call the callback with an error
                  callback(new Error(errorMessage));
                });
            
                const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');
            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(executeCtEventsStrategyStub.calledOnce).to.be.true;
                  console.log(sendMetricsToDDStub.callCount);
                  expect(sendMetricsToDDStub.calledTwice).to.be.true;
                  executeCtEventsStrategyStub.restore();
                  sendMetricsToDDStub.restore();
                  done();
                });
              });

              it('should handle a record with missing "data" key', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: JSON.stringify({
                    // No "data" key in the record
                  }),
                };
                const criticalStub = sinon.stub(serviceObj.L, 'critical');
                const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');

            
                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(criticalStub.calledOnce).to.be.true;
                  expect(sendMetricsToDDStub.calledTwice).to.be.true;
                  sendMetricsToDDStub.restore();
                  criticalStub.restore();
                  done();
                });
              });
        
              // it('should handle a record with empty "data" array', (done) => {
              //   // Mock the necessary data for the test case
              //   const record = {
              //     value: JSON.stringify({
              //       data: [],
              //     }),
              //   };
              //   const sendMetricsToDDStub = sinon.stub(utility, '_sendMetricsToDD');
              //   const logStub = sinon.stub(serviceObj.L, 'log');
            
              //   // Call the processData function
              //   serviceObj.processData(record, () => {
              //     // Assertions
              //     expect(sendMetricsToDDStub.calledTwice).to.be.true;
              //     expect(logStub.calledWith('realtimeSmsParsing :: Empty sms Data found')).to.be.true;
              //     done();
              //   });
              // });
              it('should handle an invalid record and execute defaultStrategy', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: JSON.stringify({
                    data: [{ rtspClassId: 'INVALID_CLASSIFIER_ID' }],
                  }),
                };

                const nextStub = sinon.stub().callsArg(1);
                const defaultStrategyStub = sinon.stub(serviceObj, 'defaultStrategy').callsFake((callback, smsData) => callback());
            
                const errorSpy = sinon.spy(serviceObj.L, 'error');

                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  console.log("Invalid records found! :", JSON.stringify(record));
                  expect(defaultStrategyStub.calledOnce).to.be.true;
                  defaultStrategyStub.restore();
                  errorSpy.restore();
                  done();
                });
              });
              it('parsing error', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: ({
                    data: [{ rtspClassId: 'INVALID_CLASSIFIER_ID' }],
                  }),
                };

                const nextStub = sinon.stub().callsArg(1);
                const defaultStrategyStub = sinon.stub(serviceObj, 'defaultStrategy').callsFake((callback, smsData) => callback());
            
                const errorSpy = sinon.spy(serviceObj.L, 'critical');

                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(defaultStrategyStub).to.have.callCount(0);
                  expect(errorSpy.calledWith('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received`, record)).to.be.true;
                  defaultStrategyStub.restore();
                  errorSpy.restore();
                  done();
                });
              });
              it('not array error', (done) => {
                // Mock the necessary data for the test case
                const record = {
                  value: JSON.stringify({
                    data: { rtspClassId: 'INVALID_CLASSIFIER_ID' },
                  }),
                };

                const nextStub = sinon.stub().callsArg(1);
                const defaultStrategyStub = sinon.stub(serviceObj, 'defaultStrategy').callsFake((callback, smsData) => callback());
            
                const errorSpy = sinon.spy(serviceObj.L, 'critical');

                // Call the processData function
                serviceObj.processData(record, () => {
                  // Assertions
                  expect(defaultStrategyStub).to.have.callCount(0);
                  expect(errorSpy.calledWith('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received`, record.value)).to.be.true;
                  defaultStrategyStub.restore();
                  errorSpy.restore();
                  done();
                });
              });
      });

      describe('validateDataPackRecord', () => {
        let serviceObj;
        before(function () {    
          STARTUP_MOCK.init(function(error, options){
              serviceObj = new realtimeSmsParsingPrepaid(options);
          });
      });
        it('should return error when validity expiry date is not today', () => {
          const record = {
            dueDate: '2023-07-25', 
            cId: 'customerId',
            rechargeNumber: 'recharge123',
            dataConsumed: '100MB',
            operator: 'operatorA',
            smsOperator: 'operatorB',
            rtspClassId: 'classId',
            rtspClassName: 'className',
            smsSenderID: 'senderId',
          };
      
          const [error, processedRecord] = serviceObj.validateDataPackRecord(record);
      
          expect(error).to.equal('Validity expiry date is not today');
          expect(processedRecord).to.be.null;
        });
        it('should handle isRuSmsParsing true', () => {
          const record = {
            cId: 'customerId',
            rechargeNumber: 'recharge123',
            dataConsumed: '100MB',
            dueDate: MOMENT().format('YYYY-MM-DD'),
            operator: 'operatorA',
            isRuSmsParsing: true,
            rtspClassId: 'classId',
            rtspClassName: 'className',
            smsSenderID: 'senderId',
          };
      
          const [error, processedRecord] = serviceObj.validateDataPackRecord(record);
      
          expect(error).to.be.equal('Mandatory Params rechargeNumber is Missing / Invalid');
          expect(processedRecord.isRuSmsParsing).to.be.true;
        });
      
        it('should return error for missing mandatory parameters', () => {
          const record = {
            dueDate: MOMENT().format('YYYY-MM-DD'),
            operator: 'operatorA',
            rtspClassId: 'classId',
            rtspClassName: 'className',
          };
      
          const [error, processedRecord] = serviceObj.validateDataPackRecord(record);
          expect(error).to.equal('Mandatory Params customerId,rechargeNumber,dataConsumed is Missing / Invalid');
          expect(processedRecord.customerId).to.equal(null)
          expect(processedRecord.rechargeNumber).to.equal(null)
          expect(processedRecord.dataConsumed).to.equal(null)
          expect(processedRecord.operator).to.equal('operatora')
          expect(MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss')).to.equal(MOMENT(record.dueDate).format('YYYY-MM-DD HH:mm:ss'))
          expect(processedRecord.service).to.equal('mobile')
          expect(processedRecord.paytype).to.equal('prepaid')
          expect(processedRecord.msgId).to.equal('')
          expect(processedRecord.rtspClassId).to.equal('classId')
          expect(processedRecord.rtspClassName).to.equal('className')
          expect(processedRecord.senderId).to.equal(null)
          expect(processedRecord.debugKey).to.equal('operator:operatora_custId:null_rechargeNo:null')
        });
      
        it('should return processed record for a valid input', () => {
          const record = {
            dueDate: MOMENT().format('YYYY-MM-DD'),
            cId: 'customerId',
            rechargeNumber: '123',
            dataConsumed: '100MB',
            operator: 'operatorA',
            rtspClassId: 'classId',
            rtspClassName: 'className',
            smsSenderID: 'senderId',
          };
      
          const [error, processedRecord] = serviceObj.validateDataPackRecord(record);
      
          expect(error).to.equal(null);
          expect(processedRecord.customerId).to.equal('customerId')
          expect(processedRecord.rechargeNumber).to.equal('123')
          expect(processedRecord.dataConsumed).to.equal(100)
          expect(processedRecord.operator).to.equal('operatora')
          expect(MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss')).to.equal(MOMENT(record.dueDate).format('YYYY-MM-DD HH:mm:ss'))
          expect(processedRecord.service).to.equal('mobile')
          expect(processedRecord.paytype).to.equal('prepaid')
          expect(processedRecord.msgId).to.equal('')
          expect(processedRecord.rtspClassId).to.equal('classId')
          expect(processedRecord.rtspClassName).to.equal('className')
          expect(processedRecord.senderId).to.equal('senderId')
          expect(processedRecord.debugKey).to.equal('operator:operatora_custId:customerId_rechargeNo:123')
        });

      });         

      describe('fetchDataTemplates', () => {
        let serviceObj;
        before(function () {    
          STARTUP_MOCK.init(function(error, options){
              serviceObj = new realtimeSmsParsingPrepaid(options);
          });
      });
      it('should return error for missing dataConsumed field', () => {
        const record = {
          // other properties except dataConsumed
        };
      
        serviceObj.fetchDataTemplates((error, templates) => {
          expect(error).to.equal('Missing dataConsumed field');
          expect(templates).to.be.undefined;
        }, record);
      });

      it('should return templates object for valid dataConsumed field', () => {
        const record = {
          dataConsumed: '10MB',
          // other properties
        };
      
        serviceObj.fetchDataTemplates((error, templates) => {
          expect(error).to.be.null;
          expect(templates).to.be.an('object');
          expect(templates).to.have.property('SMS');
          expect(templates).to.have.property('PUSH');
          expect(templates).to.have.property('CHAT');
          expect(templates).to.have.property('EMAIL');

          // Add more assertions based on your expected template structure
        }, record);
      });
      

      });

      describe('formatPayloadToIngest', () => {
        let serviceObj;
        before(function () {    
          STARTUP_MOCK.init(function(error, options){
              serviceObj = new realtimeSmsParsingPrepaid(options);
          });
      });
      it('should return formatted payload with error details', () => {
        const error = 'Some error message';
        const record = {
          rechargeNumber: '1234567890',
          cId: 'customer123',
          operator: 'Operator1',
          rtspClassId: 'classifier123',
          rtspClassName: 'ClassifierName',
          templateBody: 'Template Body Content',
          status: false,
          smsSenderID: 'sender123',
          amount: '50',
          dataConsumed: '2GB',
          dueDate: '2023-07-25',
        };
        const extraDetails = {
          productId: 'product123',
        };
      
        const result = serviceObj.formatPayloadToIngest(error, record, extraDetails);
      
        expect(result).to.deep.equal({
          "recharge_number": "1234567890",
          "customer_id": null,
          "operator": "Operator1",
          "product_id": "product123",
          "service": "mobile",
          "paytype": "prepaid",
          "classifier_id": "classifier123",
          "classifier_name": "ClassifierName",
          "template_body": "Template Body Content",
          "status": 0,
          "error_message": "Some error message",
          "source": "REALTIME_SMS_PARSING_TELECOM",
          "source_kafka_topic": "ru_sms_parsing_prepaid",
          "sender_id": "sender123",
          "payload": {
              rechargeNumber: '1234567890',
              cId: 'customer123',
              operator: 'Operator1',
              rtspClassId: 'classifier123',
              rtspClassName: 'ClassifierName',
              templateBody: 'Template Body Content',
              status: false,
              smsSenderID: 'sender123',
              amount: "50",
              dataConsumed: "2GB",
              dueDate: '2023-07-25',
          },
          "amount": 50,
          "dataConsumed": 2,
          "ruOnboarded": false,
          "due_date": "2023-07-25 00:00:00",
        });
      });

      it('should return templates object for valid dataConsumed field', () => {
        const record = {
          dataConsumed: '10MB',
          // other properties
        };
      
        serviceObj.fetchDataTemplates((error, templates) => {
          expect(error).to.be.null;
          expect(templates).to.be.an('object');
          expect(templates).to.have.property('SMS');
          expect(templates).to.have.property('PUSH');
          expect(templates).to.have.property('CHAT');
          expect(templates).to.have.property('EMAIL');

          // Add more assertions based on your expected template structure
        }, record);
      });
      

      });