/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment';
    import sinon from 'sinon';
    import _ from 'lodash';
    import REQUEST from 'request';
    import Q from 'q';
    
    import SERVICE from '../../services';
    import STARTUP_MOCK from '../__mocks__/startUp';
    import SMSParsingValidator from '../../services/smsParsingValidator';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    
    describe("Module: SMS Parsing Validator Service :: Kafka Initialization", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                // Setup encryption config for testing
                options.config = options.config || {};
                options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
                options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025';
                options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3';
                
                // Initialize service object
                serviceObj = new SERVICE.SMSParsingValidator(options);
                done();
            });
        });
    
        it("start service :: should configure Kafka successfully", async () => {
            let configureKafkaStub = sinon.stub(serviceObj, 'configureKafka').resolves();
            
            await serviceObj.start();
            
            expect(configureKafkaStub).to.have.been.calledOnce;
            configureKafkaStub.restore();
        });
    
        it("configureKafka :: should initialize Kafka consumer and producers", async () => {
            // Stub the consumer initialization
            const consumerInitStub = sinon.stub().callsFake((callback, cb) => {
                cb(null);
            });
            
            // Stub the producer initialization
            const producerInitStub = sinon.stub().callsFake((mode, cb) => {
                cb(null);
            });
            
            // Create mocks
            const KafkaConsumerMock = sinon.stub().returns({
                initConsumer: consumerInitStub
            });
            
            sinon.stub(serviceObj.infraUtils.kafka, 'producer').returns({
                initProducer: producerInitStub
            });
            
            // Replace the actual KafkaConsumer with our mock
            const originalKafkaConsumer = require('../../lib/KafkaConsumer');
            require('../../lib/KafkaConsumer').default = KafkaConsumerMock;
            
            await serviceObj.configureKafka();
            
            expect(KafkaConsumerMock).to.have.been.calledOnce;
            expect(consumerInitStub).to.have.been.calledOnce;
            expect(serviceObj.infraUtils.kafka.producer).to.have.been.calledOnce;
            expect(producerInitStub).to.have.been.calledOnce;
            
            // Restore the stubs
            serviceObj.infraUtils.kafka.producer.restore();
            require('../../lib/KafkaConsumer').default = originalKafkaConsumer;
        });
    });
    
    describe("Module: SMS Parsing Validator Service :: Record Processing", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                serviceObj = new SERVICE.SMSParsingValidator(options);
                done();
            });
        });
    
        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
            sinon.restore();
        });
    
        it("processRecords :: should handle empty records", async () => {
            const resolveOffsetStub = sinon.stub();
            const callback = sinon.stub();
            
            await serviceObj.processRecords(null, resolveOffsetStub, 'test-topic', 0, callback);
            
            expect(callback).to.have.been.calledOnce;
            expect(resolveOffsetStub).to.not.have.been.called;
        });
    
        it("processRecords :: should process valid records", async () => {
            const resolveOffsetStub = sinon.stub().resolves();
            const callback = sinon.stub();
            const processBatchStub = sinon.stub(serviceObj, 'processBatch').callsFake((records, done) => done());
            
            const records = [
                { offset: 1, value: '{"data": [{"smsUUID": "123", "predicted_category": "electricity", "cId": "12345"}]}' },
                { offset: 2, value: '{"data": [{"smsUUID": "456", "predicted_category": "gas", "cId": "67890"}]}' }
            ];
            
            // Stub consumer functions
            serviceObj.consumer = {
                _pauseConsumer: sinon.stub()
            };
            
            // Stub kafkaConsumerChecks
            serviceObj.kafkaConsumerChecks = {
                findOffsetDuplicates: sinon.stub()
            };
            
            await serviceObj.processRecords(records, resolveOffsetStub, 'test-topic', 0, callback);
            
            expect(serviceObj.consumer._pauseConsumer).to.have.been.calledOnce;
            expect(processBatchStub).to.have.been.calledOnce;
            expect(serviceObj.kafkaConsumerChecks.findOffsetDuplicates).to.have.been.calledOnce;
            expect(resolveOffsetStub).to.have.been.calledWith(records[1].offset);
            expect(callback).to.have.been.calledOnce;
        });
    
        it("processBatch :: should process each record in batch", (done) => {
            const processRecordStub = sinon.stub(serviceObj, 'processRecord').callsFake((record, callback) => callback());
            
            const records = [
                { value: '{"data": [{"smsUUID": "123", "predicted_category": "electricity", "cId": "12345"}]}' },
                { value: '{"data": [{"smsUUID": "456", "predicted_category": "gas", "cId": "67890"}]}' }
            ];
            
            serviceObj.processBatch(records, () => {
                expect(processRecordStub).to.have.been.calledTwice;
                processRecordStub.restore();
                done();
            });
        });
    
        it("processRecord :: should handle invalid JSON", (done) => {
            const record = { value: '{invalid-json}' };
            
            // Create a spy for datadog metrics
            const sendMetricsStub = sinon.stub(serviceObj.utility, '_sendMetricsToDD');
            
            serviceObj.processRecord(record, () => {
                expect(sendMetricsStub).to.have.been.calledWith(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                sendMetricsStub.restore();
                done();
            });
        });
    
        it("processRecord :: should handle missing data field", (done) => {
            const record = { value: '{"not_data": []}' };
            
            // Create a spy for datadog metrics
            const sendMetricsStub = sinon.stub(serviceObj.utility, '_sendMetricsToDD');
            
            serviceObj.processRecord(record, () => {
                expect(sendMetricsStub).to.have.been.calledWith(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                sendMetricsStub.restore();
                done();
            });
        });
    
        it("processRecord :: should process valid records and validate with FFR", (done) => {
            const record = {
                topic: 'SMS_PARSER_PIPED_GAS',
                value: JSON.stringify({
                    data: [{
                        smsUUID: "test-uuid",
                        predicted_category: "electricity",
                        cId: "12345",
                        smsDateTime: Date.now()
                    }]
                }),
                timestamp: Date.now()
            };
            
            // Configure config
            serviceObj.config.DYNAMIC_CONFIG = {
                SMS_PARSING_VALIDATOR: {
                    COMMON: {
                        ENABLED_CATEGORIES: ["electricity"],
                    },
                    PERCENTAGE_ROLLOUT: {
                        electricity: 100
                    },
                    ENABLED_CUSTOMER_IDS: {
                        electricity: []
                    }
                }
            };
            
            // Stub validateWithFFR
            const validateWithFFRStub = sinon.stub(serviceObj, 'validateWithFFR').callsFake((data, callback) => callback());
            
            serviceObj.processRecord(record, () => {
                expect(validateWithFFRStub).to.have.been.calledOnce;
                validateWithFFRStub.restore();
                done();
            });
        });
    });
    
    describe("Module: SMS Parsing Validator Service :: Data Validation", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                serviceObj = new SERVICE.SMSParsingValidator(options);
                done();
            });
        });
    
        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
            sinon.restore();
        });
    
        it("parseSMSData :: should parse SMS data correctly", (done) => {
            const smsData = {
                cId: "12345",
                smsDateTime: "2023-05-01T10:30:00Z",
                predicted_category: "electricity",
                detailsPath: {
                    recharge_number: "9876543210",
                    operator: "tata_power",
                    due_amount: "1500.75",
                    due_date: "2023-05-15"
                }
            };
            
            // Set up config for the test
            serviceObj.config.DYNAMIC_CONFIG = {
                GENERIC_SMS_PARSING_CONFIG: {
                    electricity: {
                        detailsPath: "detailsPath",
                        fieldMappings: {
                            rechargeNumber: "recharge_number",
                            operator: "operator",
                            amount: "due_amount",
                            dueDate: "due_date"
                        }
                    }
                }
            };
    
            // Stub utility functions
            sinon.stub(serviceObj.utility, 'getFilteredAmount').returns("1500.75");
            sinon.stub(serviceObj.utility, 'getFilteredDate').returns({ value: "2023-05-15" });
            sinon.stub(serviceObj.activePidLib, 'getActivePID').returns("1234");
            
            serviceObj.parseSMSData(smsData, (err, parsedData) => {
                expect(err).to.be.null;
                expect(parsedData).to.not.be.null;
                expect(parsedData.customerId).to.equal(12345);
                expect(parsedData.rechargeNumber).to.equal("9876543210");
                expect(parsedData.operator).to.equal("tata_power");
                expect(parsedData.amount).to.equal("1500.75");
                expect(parsedData.dueDate).to.not.be.null;
                expect(parsedData.productId).to.equal("1234");
                done();
            });
        });
    
        it("hitFfrValidationApi :: should make request to FFR API", (done) => {
            const parsedData = {
                customerId: 12345,
                rechargeNumber: "9876543210",
                operator: "airtel",
                amount: "750.50",
                service: "mobile",
                dueDate: "2023-05-15"
            };
            
            // Configure FFR URL
            serviceObj.config.FFR = {
                VALIDATION_URL: "https://api.example.com/v1/recharge/validate"
            };
            
            // Stub REQUEST
            const requestStub = sinon.stub(REQUEST, 'Request').callsFake((options, callback) => {
                const response = {
                    statusCode: 200
                };
                const body = {
                    cart_items: [{
                        customerDataResponse: {
                            currentBillAmount: "750.50",
                            billDueDate: "2023-05-15"
                        },
                        validationGwResponse: {
                            allowBillFetch: "YES"
                        }
                    }]
                };
                callback(null, response, body);
            });
            
            serviceObj.hitFfrValidationApi(parsedData, (err, ffrResponse) => {
                expect(err).to.be.null;
                expect(ffrResponse).to.have.property('customerDataResponse');
                expect(ffrResponse).to.have.property('validationGwResponse');
                requestStub.restore();
                done();
            });
        });
    
        it("compareDataWithFFR :: should detect amount mismatch", (done) => {
            const parsedData = {
                amount: "1000.00",
                dueDate: "2023-05-15",
                operator: "airtel",
                service: "mobile"
            };
            
            const ffrResponse = {
                customerDataResponse: {
                    currentBillAmount: "1200.00",
                    billDueDate: "2023-05-15"
                }
            };
            
            // Stub utility functions
            sinon.stub(serviceObj.utility, 'getFilteredAmount').returns("1200.00");
            sinon.stub(serviceObj.utility, 'getFilteredDate').returns({ value: "2023-05-15" });
            
            serviceObj.compareDataWithFFR(parsedData, ffrResponse, (err, results) => {
                expect(err).to.be.null;
                expect(results.amountMismatch).to.be.true;
                expect(results.dueDateMismatch).to.be.false;
                expect(results.ffrAmount).to.equal("1200.00");
                expect(results.smsAmount).to.equal("1000.00");
                done();
            });
        });
    
        it("pushComparisonMetrics :: should send metrics for mismatches", (done) => {
            const parsedData = {
                operator: "airtel",
                service: "mobile"
            };
            
            const comparisonResults = {
                amountMismatch: true,
                dueDateMismatch: true,
                smsAmount: "1000.00",
                ffrAmount: "1200.00",
                smsDueDate: "2023-05-15",
                ffrDueDate: "2023-05-20"
            };
            
            // Stub metrics function
            const sendMetricsStub = sinon.stub(serviceObj.utility, '_sendMetricsToDD');
            
            serviceObj.pushComparisonMetrics(parsedData, comparisonResults, (err) => {
                expect(err).to.be.null;
                expect(sendMetricsStub).to.have.been.calledTwice;
                expect(sendMetricsStub).to.have.been.calledWith(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:AMOUNT_MISMATCH',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service,
                    'SMS_AMOUNT:' + comparisonResults.smsAmount,
                    'FFR_AMOUNT:' + comparisonResults.ffrAmount
                ]);
                expect(sendMetricsStub).to.have.been.calledWith(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:DUE_DATE_MISMATCH',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service,
                    'SMS_DUE_DATE:' + comparisonResults.smsDueDate,
                    'FFR_DUE_DATE:' + comparisonResults.ffrDueDate
                ]);
                sendMetricsStub.restore();
                done();
            });
        });
    
        it("publishToDWH :: should publish validation results to DWH", (done) => {
            const originalSmsData = {
                level_2_category: "BILL_PAYMENT",
                smsSenderID: "AIRTEL",
                smsReceiverNumber: "1234567890",
                source_topic: "SMS_PARSER_PIPED_GAS"
            };
            
            const parsedData = {
                customerId: 12345,
                rechargeNumber: "9876543210",
                operator: "airtel",
                amount: "750.50",
                service: "mobile",
                dueDate: "2023-05-15",
                billDate: "2023-05-01",
                paymentDate: null,
                rawCategory: "electricity",
                smsDateTime: "2023-05-01T10:30:00Z"
            };
            
            const ffrResponse = {
                customerDataResponse: {
                    currentBillAmount: "800.25",
                    billDueDate: "2023-05-17",
                    bill_date: "2023-05-01"
                }
            };
            
            const comparisonResults = {
                amountMismatch: true,
                dueDateMismatch: true,
                ffrAmount: "800.25",
                ffrDueDate: "2023-05-17",
                smsAmount: "750.50",
                smsDueDate: "2023-05-15",
                ffrDeducedStatus: true,
                noBill: false,
                ffrErrorMsgCode: NaN,
                ffrConnectionError: false
            };
            
            // Mock DWH publisher
            serviceObj.dwhSmsPublisher = {
                publishData: sinon.stub().callsFake((data, callback) => callback(null))
            };
            
            serviceObj.publishToDWH(originalSmsData, parsedData, ffrResponse, comparisonResults, (err) => {
                expect(err).to.be.null;
                expect(serviceObj.dwhSmsPublisher.publishData).to.have.been.calledOnce;
                
                const publishCall = serviceObj.dwhSmsPublisher.publishData.getCall(0);
                const publishData = publishCall.args[0][0];
                
                expect(publishData.topic).to.equal("SMS_PARSING_VALIDATOR_DWH");
                expect(JSON.parse(publishData.messages)).to.have.property('type', 'AMOUNT_AND_DUE_DATE_MISMATCH');
                done();
            });
        });
    
        it("validateWithFFR :: should perform the full validation flow", (done) => {
            const smsData = {
                cId: "12345",
                smsDateTime: "2023-05-01T10:30:00Z",
                predicted_category: "electricity",
                smsUUID: "test-uuid",
                source_topic: "SMS_PARSER_PIPED_GAS"
            };
            
            // Stub the waterfall functions
            const parseSMSDataStub = sinon.stub(serviceObj, 'parseSMSData').callsFake((data, callback) => {
                callback(null, {
                    customerId: 12345,
                    rechargeNumber: "9876543210",
                    operator: "airtel",
                    amount: "750.50",
                    service: "mobile",
                    dueDate: "2023-05-15"
                });
            });
            
            const hitFfrValidationApiStub = sinon.stub(serviceObj, 'hitFfrValidationApi').callsFake((data, callback) => {
                callback(null, {
                    customerDataResponse: {
                        currentBillAmount: "800.25",
                        billDueDate: "2023-05-17"
                    },
                    validationGwResponse: {}
                });
            });
            
            const compareDataWithFFRStub = sinon.stub(serviceObj, 'compareDataWithFFR').callsFake((data, ffrResponse, callback) => {
                callback(null, {
                    amountMismatch: true,
                    dueDateMismatch: true,
                    ffrAmount: "800.25",
                    ffrDueDate: "2023-05-17",
                    smsAmount: "750.50",
                    smsDueDate: "2023-05-15"
                });
            });
            
            const pushComparisonMetricsStub = sinon.stub(serviceObj, 'pushComparisonMetrics').callsFake((data, results, callback) => {
                callback(null);
            });
            
            const publishToDWHStub = sinon.stub(serviceObj, 'publishToDWH').callsFake((original, parsed, ffr, results, callback) => {
                callback(null);
            });
            
            serviceObj.validateWithFFR(smsData, (err) => {
                expect(err).to.be.null;
                expect(parseSMSDataStub).to.have.been.calledOnce;
                expect(hitFfrValidationApiStub).to.have.been.calledOnce;
                expect(compareDataWithFFRStub).to.have.been.calledOnce;
                expect(pushComparisonMetricsStub).to.have.been.calledOnce;
                expect(publishToDWHStub).to.have.been.calledOnce;
                
                parseSMSDataStub.restore();
                hitFfrValidationApiStub.restore();
                compareDataWithFFRStub.restore();
                pushComparisonMetricsStub.restore();
                publishToDWHStub.restore();
                
                done();
            });
        });
    });
    
    describe("Module: SMS Parsing Validator Service :: Service Shutdown", function () {
        let serviceObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                options.config = options.config || {};
                serviceObj = new SERVICE.SMSParsingValidator(options);
                done();
            });
        });
    
        it("suspendOperations :: should close the Kafka consumer", async () => {
            // Mock consumer close function
            serviceObj.consumer = {
                close: sinon.stub().callsFake((callback) => callback(null))
            };
            
            await serviceObj.suspendOperations();
            
            expect(serviceObj.consumer.close).to.have.been.calledOnce;
        });
    
        it("suspendOperations :: should handle errors when closing consumer", async () => {
            // Mock consumer close function that errors
            serviceObj.consumer = {
                close: sinon.stub().callsFake((callback) => callback(new Error("Failed to close consumer")))
            };
            
            try {
                await serviceObj.suspendOperations();
                // Should not reach here
                expect.fail("Should have thrown an error");
            } catch (error) {
                expect(error).to.be.an.instanceOf(Error);
                expect(error.message).to.equal("Failed to close consumer");
            }
        });
    }); 