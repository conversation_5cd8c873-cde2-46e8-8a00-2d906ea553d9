/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach } from 'mocha';

import chai, { assert } from "chai";
import sinon from 'sinon';
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import _ from 'lodash';

import chaiAsPromised from "chai-as-promised";
import notifyRejectedBills from '../../services/notifyRejectedBills';

import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let data = {
    "customer_id": *********,
    "service": "financial services",
    "recharge_number": "XXXXXXX7006",
    "operator": "sbi",
    "due_amount": 250.75,
    "source": "SMS",
    "source_subtype_2": "FULL",
    "user_type": "RU",
    "reject_reason": "Mandatory Params bankName is Missing / Invalid in record",
    "sender_id": "sender123",
    "sms_date_time": "2023-11-26 23:45:31.000",
    "dwh_class_id": "",
    "created_at": "2023-11-26 23:45:31.000",
    "additional_info": "",
    "updated_at": "2023-11-26 23:45:31.000",
    "sms_id": "SMS123",
    "paytype": "credit card"
}

describe("Module: notifyRejectedBills::", function () {
    let serviceObj;

    beforeEach(function (done) {
        STARTUP_MOCK.init(function (error, options) {
            options.config = options.config || {};
            options.config.ENCRYPTION_CONFIG = options.config.ENCRYPTION_CONFIG || {};
            options.config.ENCRYPTION_CONFIG.DEFAULT = options.config.ENCRYPTION_CONFIG.DEFAULT || {};
            options.config.ENCRYPTION_CONFIG.DEFAULT.IV = '68e232d2639555a0cf08aaed9d50a025'; //adding staging keys as mock config
            options.config.ENCRYPTION_CONFIG.DEFAULT.ENCRYPTION_KEY = 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; 
            serviceObj = new notifyRejectedBills(options);
            done();
        });
    });
    it('processRejectedBills | valid payload 1', async () => {
        let stub1 = sinon.stub(serviceObj, 'validateData').returns({});
        let stub2 = sinon.stub(serviceObj, 'getRejectedBillTemplates').returns({});
        let stub3 = sinon.stub(serviceObj, 'getPayloadForNotification').returns({});
        let stub4 = sinon.stub(serviceObj, 'publishToNonruNotificationCreate').callsFake(function (data, cb) {
            return cb();
        })
        serviceObj.processRejectedBills(function (err) {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
        }, data)
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();
    })

    it('processRejectedBills | error in validateData', async () => {
        let stub1 = sinon.stub(serviceObj, 'validateData').throwsException('Unable to validate Data');
        let stub2 = sinon.stub(serviceObj, 'getRejectedBillTemplates').returns({});
        let stub3 = sinon.stub(serviceObj, 'getPayloadForNotification').returns({});
        let stub4 = sinon.stub(serviceObj, 'publishToNonruNotificationCreate').callsFake(function (data, cb) {
            return cb();
        })
        serviceObj.processRejectedBills(function (err) {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
        }, data)
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();
    })

    it('processRejectedBills | error in validateData', async () => {
        let stub1 = sinon.stub(serviceObj, 'validateData').returns({});
        let stub2 = sinon.stub(serviceObj, 'getRejectedBillTemplates').returns({});
        let stub3 = sinon.stub(serviceObj, 'getPayloadForNotification').returns({});
        let stub4 = sinon.stub(serviceObj, 'publishToNonruNotificationCreate').callsFake(function (data, cb) {
            return cb('Error in publishing');
        })
        serviceObj.processRejectedBills(function (err) {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
        }, data)
        stub1.restore();
        stub2.restore();
        stub3.restore();
        stub4.restore();
    })

    it('validate | error in validateData | invalid json parsing issue', async () => {
        let returnedPayload = _.cloneDeep(data);
        delete returnedPayload.customer_id;
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').throwsException('Parsing issue')
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Parsing issue')
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | mandatory param missing', async () => {
        let returnedPayload = _.cloneDeep(data);
        delete returnedPayload.customer_id;
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Missing mandatory params : customer_id for debugKey : null')
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | mandatory param missing 2', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.customer_id = null;
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Missing mandatory params : customer_id for debugKey : null')
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | service not enabled', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_POSTPAID';
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Service_paytype not enabled : MOBILE_POSTPAID for debugKey : null')
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | categoryId not found', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_PREPAID';
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('CategoryId not found for this service_paytype : null')
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | percentage traffic pass', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'ELECTRICITY_POSTPAID';
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Source not enabled: null'); //when cust_id even -> bypass
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | percentage traffic fail', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.customer_id = 101;
        returnedPayload.service_paytype = 'ELECTRICITY_POSTPAID';
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Notification not enabled for this customer : null'); //when cust_id odd -> block
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | source not enabled', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'ELECTRICITY_PREPAID';
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Source not enabled: null');
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | source enabled', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'ELECTRICITY_PREPAID';
        returnedPayload.source = 'SMS_PARSING_DWH'
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Reject reason not enabled : Mandatory Params bankName is Missing / Invalid in record for debugKey : null');
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | error_message not enabled', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'ELECTRICITY_PREPAID';
        returnedPayload.source = 'SMS_PARSING_DWH'
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            expect(e.message).to.equal('Reject reason not enabled : Mandatory Params bankName is Missing / Invalid in record for debugKey : null');
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | error_message enabled', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'ELECTRICITY_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'data format issue'
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
        } catch (e) {
            console.log(e.message)
            expect(e.message).to.equal('Error code not found for reject reason : data format issue and standard_error_message : data format issue for debugKey : null');
            expect(stub1).to.have.callCount(1);
        }
        stub1.restore();
    })

    it('validate | error in validateData | error_code found', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        let stub1 = sinon.stub(serviceObj, 'convertKafkaPayloadToRecord').returns(returnedPayload);
        try {
            let validationResult = serviceObj.validateData(data);
            expect(validationResult).to.equal(returnedPayload);
        } catch (e) {
            expect(e).to.equal(null);
        }
        stub1.restore();
    })

    it('getRejectedBillTemplates | success execution', async () => {
        let stub1 = sinon.stub(serviceObj, 'getTemplateId').returns(1234);
        let templates = serviceObj.getRejectedBillTemplates(data);
        expect(templates.SMS).to.equal(1234)
        expect(templates.PUSH).to.equal(1234)
        expect(templates.EMAIL).to.equal(1234)
        expect(templates.CHAT).to.equal(1234)
        expect(templates.WHATSAPP).to.equal(1234)
        expect(stub1).to.have.callCount(5);
        stub1.restore();
    })

    it('getTemplateId | specific error success push ', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(true);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'PUSH');
        expect(templateId).to.equal(1234);
        stub1.restore();
    })

    it('getTemplateId | specific error success chat', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(true);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'CHAT');
        expect(templateId).to.equal(3456);
        stub1.restore();
    })

    it('getTemplateId | specific error success email', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(true);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'EMAIL');
        expect(templateId).to.equal(2345);
        stub1.restore();
    })

    it('getTemplateId | specific error success sms', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(true);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'SMS');
        expect(templateId).to.equal(4567);
        stub1.restore();
    })

    it('getTemplateId | specific error success whatsapp', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(true);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'WHATSAPP');
        expect(templateId).to.equal(5678);
        stub1.restore();
    })

    it('getTemplateId | generic success push ', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_002'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(true);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'PUSH');
        expect(templateId).to.equal(9876);
        stub1.restore();
    })

    it('getTemplateId | generic success chat', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(false);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'CHAT');
        expect(templateId).to.equal(7654);
        stub1.restore();
    })

    it('getTemplateId | generic success email', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(false);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'EMAIL');
        expect(templateId).to.equal(8765);
        stub1.restore();
    })

    it('getTemplateId | generic success sms', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(false);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'SMS');
        expect(templateId).to.equal(6543);
        stub1.restore();
    })

    it('getTemplateId | generic success whatsapp', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let stub1 = sinon.stub(serviceObj, 'isMandatoryParamsPresentForSpecilizedTemplate').returns(false);
        let templateId = serviceObj.getTemplateId(returnedPayload, 'WHATSAPP');
        expect(templateId).to.equal(5432);
        stub1.restore();
    })

    it('isMandatoryParamsPresentForSpecilizedTemplate | success', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_001'
        let mandates = serviceObj.isMandatoryParamsPresentForSpecilizedTemplate(returnedPayload);
        expect(mandates).to.equal(true);
    })

    it('isMandatoryParamsPresentForSpecilizedTemplate | fail', async () => {
        let returnedPayload = _.cloneDeep(data);
        returnedPayload.service_paytype = 'MOBILE_AUTOPAY';
        returnedPayload.source = 'SMS_PARSING_DWH'
        returnedPayload.reject_reason = 'cannot process data'
        returnedPayload.error_code = 'ERR_002'
        let mandates = serviceObj.isMandatoryParamsPresentForSpecilizedTemplate(returnedPayload);
        expect(mandates).to.equal(false);
    })

    it('convertKafkaPayloadToRecord | success', async () => {
        let payload = {
            "value": JSON.stringify(data)
        }
        let result = serviceObj.convertKafkaPayloadToRecord(payload);
        expect(result.customer_id).to.equal(data.customer_id);
        expect(result.recharge_number).to.equal(data.recharge_number);
    })

    it('convertKafkaPayloadToRecord | fail', async () => {
        let payload = {
            "value": data
        }
        try {
            let result = serviceObj.convertKafkaPayloadToRecord(payload);
        } catch (e) {
            expect(e.message).to.equal('SyntaxError: Unexpected token o in JSON at position 1')
        }
    })

    it('getPayloadForNotification | success', async () => {
        let result = serviceObj.getPayloadForNotification(data, {});
        expect(result.data.customer_id).to.equal(data.customer_id);
        expect(result.data.recharge_number).to.equal(data.recharge_number);
    })

    it('publishToNonruNotificationCreate | success', async () => {
        let cb = sinon.spy();
        serviceObj.nonruBillFetchKafkaRealtime = {
            publishData: () => {
                return cb(null)
            }
        }
        serviceObj.publishToNonruNotificationCreate(data, function (err) {
            if (err) {
                expect(err).to.equal(null);
            }
            expect(cb).to.have.calledOnceWith(null);
        })
    })

    it('publishToNonruNotificationCreate | error', async () => {
        let cb = sinon.spy();
        serviceObj.nonruBillFetchKafkaRealtime = {
            publishData: () => {
                return cb('Error while publishing')
            }
        }
        serviceObj.publishToNonruNotificationCreate(data, function (err) {
            if (err) {
                expect(err).to.equal(null);
            }
            expect(cb).to.have.calledOnceWith('Error while publishing');
        })
    })

    it('processBatch | success', async () => {
        let payload = [data, data];
        let stub1 = sinon.stub(serviceObj, 'processRejectedBills').callsFake(function (cb, data) {
            return cb();
        })
        serviceObj.processBatch(payload, function (err) {
            expect(err).to.equal(undefined);
            expect(stub1).to.have.callCount(2);
        })
        stub1.restore();
    })

    it('start | success', async () => {
        let stub1 = sinon.stub(serviceObj, 'configureKafka').callsFake(function (cb) {
            return cb();
        })
        serviceObj.start();
        expect(stub1).to.have.callCount(1);
    })

    it('start | fail', async () => {
        let stub1 = sinon.stub(serviceObj, 'configureKafka').callsFake(function (cb) {
            return cb('Error in configure kafka');
        })
        let stub2 = sinon.stub(process, 'exit');
        serviceObj.start();
        expect(stub1).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    })

    it('execSteps | success', async () => {
        let resolveOffset = () => {
            return new Promise((resolve, reject) => {
                resolve();
            })
        }
        let input = [data, data];
        let stub1 = sinon.stub(serviceObj, 'processBatch').callsFake(function (data, callb) {
            return callb();
        })
        serviceObj.execSteps(input, resolveOffset, 'topic', 'partition', function (err) {
            console.log("done");
            expect(stub1).to.have.callCount(1);
        })
        stub1.restore();
    })

    it('execSteps | invalid payload', async () => {
        let resolveOffset = () => {
            return new Promise((resolve, reject) => {
                resolve();
            })
        }
        let stub1 = sinon.stub(serviceObj, 'processBatch').callsFake(function (data, callb) {
            return callb();
        })
        serviceObj.execSteps(data, resolveOffset, 'topic', 'partition', function (err) {
            console.log("done");
            expect(stub1).to.have.callCount(0);
        })
        stub1.restore();
    })

    it("initializeVariable funcation ", () => {
        // let stub2 = sinon.stub(serviceObj, 'initializeVariable');
        serviceObj.initializeVariable();
        // expect(stub2).to.have.callCount(1);
    })

    it('configureKafka | success ', () => {
        serviceObj.configureKafka(function (err) {
            if (err) {
                expect(err).to.equal(null);
            }
            sinon.assert.calledOnce(serviceObj.nonruBillFetchKafkaRealtime.initProducer);
            sinon.assert.calledOnce(serviceObj.kafkaConsumer.initConsumer);
        })
    })

    it('suspendOperations | success', () => {
        let cb = sinon.spy();
        serviceObj.kafkaConsumer = {
            close: ()=>{
                return cb(null);
            }
        }
        serviceObj.suspendOperations();
    });

    it('suspendOperations | failure', () => {
        let cb = sinon.spy();
        serviceObj.kafkaConsumer = {
            close: ()=>{
                return cb('Error');
            }
        }
        serviceObj.suspendOperations();
    });

});