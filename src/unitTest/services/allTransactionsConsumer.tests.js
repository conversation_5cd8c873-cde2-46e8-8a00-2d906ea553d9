/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before} from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import sinon from "sinon"
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _ from 'lodash';

import STARTUP_MOCK from '../__mocks__/startUp'

import SERVICE from '../../services'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Kafka consumer validations", () => {
    let serviceObj;
    before((done) => {
        STARTUP_MOCK.init((error, options) => {
            serviceObj = new SERVICE.allTransactionsConsumer(options)
            done();
        })
    });

    it("testing initialize consumer | default values", () => {
        let cb = sinon.spy();
        serviceObj._initializeKafkaConsumer(cb);
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
        
    })

    it("testing initialize consumer | fail at infraUtils create consumer", () => {
        let cb = sinon.spy();
        let stub = sinon.stub(serviceObj.infraUtils.kafka, 'consumer').callsFake('fake Error');
        serviceObj._initializeKafkaConsumer(cb);
        stub.restore();
        expect(cb).to.have.callCount(1);
        expect(cb).to.not.have.calledWith(null)
    })

    it("start service || ensure service initialises consumer", () => {
        let initializeStub = sinon.stub(serviceObj, '_initializeKafkaConsumer').yields(null);
        serviceObj.start();
        expect(initializeStub).to.have.been.calledOnce;
    })

    it("processKafkaData || ensure empty records are validated", () => {
        let record = {}
        let processedRecords = serviceObj._processKafkaData(record);
        expect(processedRecords).to.be.equal(undefined)
    })

    it("processKafkaData || ensure JSON records are validated", () => {
        let record = [
            { 
                value: "testing wrong JSON"
            }
        ]
        serviceObj.consumer = {
            _pauseConsumer : () => {
                return;
            },
            commitOffset: () => {
                return;
            },
            _resumeConsumer: () => {

            }
        };
        let processStub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null)
        serviceObj._processKafkaData(record);
        processStub.restore();
        expect(processStub).to.not.have.been.called;
    })

    it("processKafkaData || ensure normal flow works", () => {
        let record = [
            { 
                value: '{"order_id":1.3832694924E10,"paytype":"postpaid","paytype_label":"recharge","price":94}'
            }
        ]

        serviceObj.consumer = {
            _pauseConsumer : () => {
                return;
            },
            commitOffset: () => {
                return;
            },
            _resumeConsumer: () => {
                return;
            }
        };

        let processStub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null)
        serviceObj._processKafkaData(record);
        processStub.restore();
        expect(processStub).to.have.been.calledWith(JSON.parse(record[0].value));
    })

    it("processKafkaData || ensure normal flow works for multiple records", () => {
        let record = [
            { 
                value: '{"order_id":1.3832694924E10,"paytype":"postpaid","paytype_label":"recharge","price":94}'
            },
            {
                value: '{"order_id":1.3832694924E10,"paytype":"postpaid","paytype_label":"recharge","price":94}'
            }
        ]

        serviceObj.consumer = {
            _pauseConsumer : () => {
                return;
            },
            commitOffset: () => {
                return;
            },
            _resumeConsumer: () => {
                return;
            }
        };

        let processStub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null)
        serviceObj._processKafkaData(record);
        processStub.restore();
        expect(processStub).to.have.callCount(2);
    })
})

describe("Module: allTransactionsConsumer service test suite", function () {
    let serviceObj;
    let currentDate = MOMENT().format("YYYY-MM-DD");

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new SERVICE.allTransactionsConsumer(options);
            console.log(serviceObj.options)
            done();
        });
    });

    it("allTransactionsConsumer::_prepareDataToInsert | Test transaction", (done) => {
        let recordData = {"id":"154789762622","catalogProductID":"194","reqType":"RECHARGE","userData_recharge_number":"recents_airtel_rn_001","customerInfo_customer_id":667773,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"airtel","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        serviceObj._prepareDataToInsert(recordData,done);
    });

    it("allTransactionsConsumer::_prepareDataToInsert | recharge_status != 00", () => {
        let allTransactionsConsumerdata = {recharge_status : '01'};
        let cb = sinon.spy();
        let stub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null);
        serviceObj._prepareDataToInsert(allTransactionsConsumerdata, cb);
        stub.restore();
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
    })

    it("allTransactionsConsumer::_prepareDataToInsert | invalid product id", () => {
        let allTransactionsConsumerdata = {recharge_status : '01', "reqType":"RECHARGE","userData_recharge_number":"recents_airtel_rn_001","customerInfo_customer_id":667773,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"airtel","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        let cb = sinon.spy();
        let stub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null);
        serviceObj._prepareDataToInsert(allTransactionsConsumerdata, cb);
        stub.restore();
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
    })

    it("allTransactionsConsumer::_prepareDataToInsert | invalid recharge number", () => {
        let allTransactionsConsumerdata = {recharge_status : '01', catalogProductID:"194","reqType":"RECHARGE","customerInfo_customer_id":667773,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"airtel","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        let cb = sinon.spy();
        let stub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null);
        serviceObj._prepareDataToInsert(allTransactionsConsumerdata, cb);
        stub.restore();
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
    })

    it("allTransactionsConsumer::_prepareDataToInsert | invalid customer_id", () => {
        let allTransactionsConsumerdata = {recharge_status : '01', catalogProductID:"194","reqType":"RECHARGE","userData_recharge_number":"recents_airtel_rn_001","userData_amount":600,"inStatusMap_responseCode":"00","productInfo_operator":"airtel","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        let cb = sinon.spy();
        let stub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null);
        serviceObj._prepareDataToInsert(allTransactionsConsumerdata, cb);
        stub.restore();
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
    })

    it("allTransactionsConsumer::_prepareDataToInsert | invalid operator", () => {
        let allTransactionsConsumerdata = {recharge_status : '01', catalogProductID:"194","reqType":"RECHARGE","userData_recharge_number":"recents_airtel_rn_001","customerInfo_customer_id":667773,"userData_amount":600,"inStatusMap_responseCode":"00","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        let cb = sinon.spy();
        let stub = sinon.stub(serviceObj, '_prepareDataToInsert').yields(null);
        serviceObj._prepareDataToInsert(allTransactionsConsumerdata, cb);
        stub.restore();
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
    })

    it("allTransactionsConsumer::_prepareDataToInsert | data validated successfully", (done) => {
        let allTransactionsConsumerdata = {recharge_status : '01', catalogProductID:"194","reqType":"RECHARGE","userData_recharge_number":"recents_airtel_rn_001","customerInfo_customer_id":667773,"userData_amount":600,"productInfo_operator":"airtel","inStatusMap_responseCode":"00","productInfo_paytype" : "postpaid","currentGw":"bharatbillpay","customerInfo_customer_type":1};
        serviceObj._prepareDataToInsert(allTransactionsConsumerdata, err => {
            console.log(err)
            expect(err).to.be.equal(undefined);
            return done();
        });
    })

    it("allTransactionsConsumer::_processCustomerInfo | new record", (done) => {
        let customerRecordfromDB = [];
        let customerid = 123456;
        let operator = "testOperator";
        let rechargeNumber = '112233';

        serviceObj._processCustomerInfo((err, data) =>{
            expect(err).to.be.equal(null);
            expect(data.customer_id).to.be.equal(customerid);
            expect(data.is_retailer).to.be.equal(false);
            expect(data.counter).to.be.equal(1);
            expect(data.min_date).to.be.equal(currentDate);
            return done();
        }, customerRecordfromDB, customerid, operator, rechargeNumber)
    });

    it("allTransactionsConsumer::_processCustomerInfo | counter increase by 1", (done) => {
        let customerRecordfromDB = [{
            counter: 2,
            is_retailer: false,
            min_date : currentDate
        }];
        let customerid = 123456;
        let operator = "testOperator";
        let rechargeNumber = '112233';

        serviceObj._processCustomerInfo((err, data) =>{
            expect(err).to.be.equal(null);
            expect(data.customer_id).to.be.equal(customerid);
            expect(data.is_retailer).to.be.equal(false);
            expect(data.counter).to.be.equal(3);
            expect(data.min_date).to.be.equal(currentDate);
            return done();
        }, customerRecordfromDB, customerid, operator, rechargeNumber)
    })

    it("allTransactionsConsumer::_processCustomerInfo | becoming retailer", (done) => {
        const counterThreshold = _.get(serviceObj.config, ['DYNAMIC_CONFIG', "NOTIFICATION_EXCEPTIONS", "BLOCK_SMS", "COUNTER_THRESHOLD"], 6);
        let customerRecordfromDB = [{
            counter: counterThreshold,
            is_retailer: false,
            min_date : currentDate
        }];
        let customerid = 123456;
        let operator = "testOperator";
        let rechargeNumber = '112233';

        serviceObj._processCustomerInfo((err, data) =>{
            expect(err).to.be.equal(null);
            expect(data.customer_id).to.be.equal(customerid);
            expect(data.is_retailer).to.be.equal(true);
            expect(data.counter).to.be.equal(1);
            expect(data.min_date).to.be.equal(currentDate);
            return done();
        }, customerRecordfromDB, customerid, operator, rechargeNumber)
    })

    it("allTransactionsConsumer::_processCustomerInfo | min date greater than 30 days ", (done) => {
        let oldDate = MOMENT().subtract(45, "days");
        oldDate = oldDate.format("YYYY-MM-DD")

        let customerRecordfromDB = [{
            counter: 4,
            is_retailer: true,
            min_date : oldDate
        }];
        let customerid = 123456;
        let operator = "testOperator";
        let rechargeNumber = '112233';

        serviceObj._processCustomerInfo((err, data) =>{
            expect(err).to.be.equal(null);
            expect(data.customer_id).to.be.equal(customerid);
            expect(data.is_retailer).to.be.equal(false);
            expect(data.counter).to.be.equal(1);
            expect(data.min_date).to.be.equal(currentDate);
            return done();
        }, customerRecordfromDB, customerid, operator, rechargeNumber)
    })

});