
/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach } from 'mocha';

import chai, { assert } from "chai";
import sinon from 'sinon';
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import _ from 'lodash';

import chaiAsPromised from "chai-as-promised";
import prepaidSmsParsing from '../../services/smsParsingBillPayment/prepaidSmsParsing';
import parent from '../../services/smsParsingBillPayment';


import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("Module: prepaid SMS Parsing Data validation", function () {
    let serviceObj;

    before(function () {
        STARTUP_MOCK.init(function (error, options) {
            serviceObj = new prepaidSmsParsing(options);
            done();
        });
    });
    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });

    it("initializeVariable : update variables if updated in config",()=>{
        let self = serviceObj
        
        _.set(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'MIN_AMOUNT'], 25);
        _.set(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'MAX_AMOUNT'], 600000);
         serviceObj.initializeVariable()
         assert(self.minAmount == 25,"Min amount should be updated")
         assert(self.maxAmount == 600000,"Max amount should be updated")
    })

    it("executeStrategy | null data", (done) => {
        let data = null;
        let stub = sinon.stub(serviceObj, 'processRecords').returns(null);
        let processedRecord = serviceObj.executeStrategy(function (error) {
            if (error) {
                expect(error).to.be.equal(null);
            }
        }, data, parent);
        expect(stub).to.have.callCount(0);
        expect(processedRecord).to.be.equal(undefined);
        stub.restore();
        done();
    })

    it("executeStrategy | null data", (done) => {
        let data = { "data": [{ "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 2, "expiry_date": "20-02-2022" } }], "kafka_topic": ["SMS_PARSING_OTHERS"] }
        // let data = { "data": [{ "cId": "227316909", "deviceDateTime": *************, "mId": "", "model": "TA-1021", "uploadTime": *************, "timestamp": null, "user_agent": null, "true_client_ip": null, "preference": ["Array"], "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "VM-KOTAKB", "smsOperator": "JIO", "smsReceiver": "xxxxxxxxx6365", "smsUUID": "9f14ea09-b7ae-4278-b26e-762efefdd641", "predicted_category": "2", "level_2_category": "2.0", "fastag_class": "-1", "fastag_features": {}, "mode_of_transaction": ["Object"], "ccbp_category": "1", "account_no": null, "card_no": "2003", "amount": 10000, "available_Balance": null, "date": "2021-03-15", "bankName": "KOTAK" }], "kafka_topic": ["SMS_PARSING_CC_BILLS_PAYMENT"] }
        let stub = sinon.stub(serviceObj, 'processRecords').returns(null);
        let processedRecord = serviceObj.executeStrategy(function (error) {
            if (error) {
                expect(error).to.be.equal(null);
            }
        }, data, parent);
        expect(stub).to.have.callCount(1);
        expect(processedRecord).to.equal(undefined);
        stub.restore();
        done();
    })

    it.skip("validateAndProcessRecord | Valid Record(20-12-2022) | As per Production data", async() => {
        let recordData = { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 2, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal(null);
        expect(record.customerId).to.be.equal(**********);
        expect(record.rechargeNumber).to.be.equal("9770636074");
        expect(record.validityDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-12-20 00:00:00");
        expect(record.operator).to.be.equal("airtel");
        expect(record.service).to.be.equal("mobile");
        expect(record.paytype).to.be.equal("prepaid");
    });

    it("validateAndProcessRecord | Valid Record(16-05-2024) | As per Production data | partial bill state case | level category 7", async() => {
        let recordData ={"appVersion": "10.41.4", "netWorkType": "5G", "latitude": 25.8345693, "deviceId": "3e00afe23eec2f44", 
            "deviceDateTime": 1715850068426, "collector_timestamp": 1715850069257, "wakeUpTimeInterval": 0, "osVersion": "34", 
            "osType": "android", "model": "V2303", "msg_id": "ba43d3ca-ba26-4a1a-9c89-df7195e61bf7", "brand": "vivo", "user_agent": null, 
            "cId": "264934121", "longitude": 81.3686416, "timestamp": null, "appCount": 2, "uploadFrequency": 0, "clientId": "androidapp",
            "preference": [{"prefCat": "permission", "prefKeys": "ocl.permission.universal.sms_read_consent", "prefSubCat": "sms consent"}, 
            {"prefCat": "permission", "prefKeys": "ocl.permission.universal.whatsapp_consent", "prefSubCat": "sms consent"}],
            "mId": "", "eventType": "smsEvent", "uploadTime": 1715850068644, "true_client_ip": null, "consent": true, 
            "realTime": true, "db_name": null, "newUser": false, "event_name": "sms", "batteryPercentage": 57, 
            "smsUUID": "6340c24e-f699-47bc-8693-14b8ea2aa112", "smsDateTime": "1715850068308", "subId": 2, "smsSenderID": "AE-AIRTEL",
            "smsTemplateId": "telecom_group_1", "smsBody": "Aap calls miss kar rahe hain. Airtel No. 6387448007 ki sewayein shuru karne ke liye Unlimited Pack se recharge karein i.airtel.in/FDPNew Ignore if recharged", "slot": 1,
            "smsReceiver": "+************", "smsOperator": "airtel", 
            "telecom_details": {"plan_amount": null, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, 
            "mobile_number": "6387448007", "telecom_model_version": "2.0", "data_expiry_percentage": null, "is_GPU": false}, 
            "predicted_category": "telecom", "level_2_category": "7", "producerTime": 1715850069};
        
        // { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 2, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        console.log("recordData :", record);
        console.log("error :", error);

        expect(error).to.be.equal(null);
        expect(record.partialBillState).to.be.equal("INCOMING_STOPPED");
        expect(record.is_wo_validity).to.be.equal(true);
        expect(record.validityDate).to.be.equal(null);
    });

    it("validateAndProcessRecord | Valid Record(16-05-2024) | As per Production data | partial bill state case | level category 6", async() => {
        let recordData ={"appVersion": "10.41.4", "netWorkType": "5G", "latitude": 25.8345693, "deviceId": "3e00afe23eec2f44", "deviceDateTime": 1715850068426, "collector_timestamp": 1715850069257, "wakeUpTimeInterval": 0, "osVersion": "34", "osType": "android", "model": "V2303", "msg_id": "ba43d3ca-ba26-4a1a-9c89-df7195e61bf7", "brand": "vivo", "user_agent": null, "cId": "264934121", "longitude": 81.3686416, "timestamp": null, "appCount": 2, "uploadFrequency": 0, "clientId": "androidapp", "preference": [{"prefCat": "permission", "prefKeys": "ocl.permission.universal.sms_read_consent", "prefSubCat": "sms consent"}, {"prefCat": "permission", "prefKeys": "ocl.permission.universal.whatsapp_consent", "prefSubCat": "sms consent"}], "mId": "", "eventType": "smsEvent", "uploadTime": 1715850068644, "true_client_ip": null, "consent": true, "realTime": true, "db_name": null, "newUser": false, "event_name": "sms", "batteryPercentage": 57, "smsUUID": "6340c24e-f699-47bc-8693-14b8ea2aa112", "smsDateTime": "1715850068308", "subId": 2, "smsSenderID": "AE-AIRTEL", "smsTemplateId": "telecom_group_1", "smsBody": "Aap calls miss kar rahe hain. Airtel No. 6387448007 ki sewayein shuru karne ke liye Unlimited Pack se recharge karein i.airtel.in/FDPNew Ignore if recharged", "slot": 1, "smsReceiver": "+************", "smsOperator": "airtel", "telecom_details": {"plan_amount": null, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "mobile_number": "6387448007", "telecom_model_version": "2.0", "data_expiry_percentage": null, "is_GPU": false}, "predicted_category": "telecom", "level_2_category": "6", "producerTime": 1715850069};
        
        // { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 2, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal(null);
        expect(record.partialBillState).to.be.equal("EXPIRED");
        expect(record.is_wo_validity).to.be.equal(true);
        expect(record.validityDate).to.be.equal(null);
    });

    it("validateAndProcessRecord | Valid Record(16-05-2024) | As per Production data | partial bill state case | level category 5", async() => {
        let recordData ={"appVersion": "10.41.4", "netWorkType": "5G", "latitude": 25.8345693, "deviceId": "3e00afe23eec2f44", "deviceDateTime": 1715850068426, "collector_timestamp": 1715850069257, "wakeUpTimeInterval": 0, "osVersion": "34", "osType": "android", "model": "V2303", "msg_id": "ba43d3ca-ba26-4a1a-9c89-df7195e61bf7", "brand": "vivo", "user_agent": null, "cId": "264934121", "longitude": 81.3686416, "timestamp": null, "appCount": 2, "uploadFrequency": 0, "clientId": "androidapp", "preference": [{"prefCat": "permission", "prefKeys": "ocl.permission.universal.sms_read_consent", "prefSubCat": "sms consent"}, {"prefCat": "permission", "prefKeys": "ocl.permission.universal.whatsapp_consent", "prefSubCat": "sms consent"}], "mId": "", "eventType": "smsEvent", "uploadTime": 1715850068644, "true_client_ip": null, "consent": true, "realTime": true, "db_name": null, "newUser": false, "event_name": "sms", "batteryPercentage": 57, "smsUUID": "6340c24e-f699-47bc-8693-14b8ea2aa112", "smsDateTime": "1715850068308", "subId": 2, "smsSenderID": "AE-AIRTEL", "smsTemplateId": "telecom_group_1", "smsBody": "Aap calls miss kar rahe hain. Airtel No. 6387448007 ki sewayein shuru karne ke liye Unlimited Pack se recharge karein i.airtel.in/FDPNew Ignore if recharged", "slot": 1, "smsReceiver": "+************", "smsOperator": "airtel", "telecom_details": {"plan_amount": null, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "mobile_number": "6387448007", "telecom_model_version": "2.0", "data_expiry_percentage": null, "is_GPU": false}, "predicted_category": "telecom", "level_2_category": "5", "producerTime": 1715850069};
        
        // { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 2, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal(null);
        expect(record.partialBillState).to.be.equal("EXPIRING_SOON");
        expect(record.is_wo_validity).to.be.equal(true);
        expect(record.validityDate).to.be.equal(null);
    });


    it("executeStrategy | processRecord | check processingOnDbRecords called", async() => {

        let rec = {"customerId":1188476171,"rechargeNumber":"7502347350","planAmount":239,"validityDate":null,"operator":"airtel","service":"mobile","paytype":"prepaid","isDefaultAmount":true,"msgId":"0b43043d-8a95-49d1-9242-4cf73438e72a","rtspClassId":null,"rtspClassName":null,"sender_id":"AT-AIRTEL","sms_id":null,"sms_date_time":"1715868054539","dwh_classId":5,"is_wo_validity":false,"is_wo_rech_num":true,"dataConsumed":null,"smsDateTime":"1715868054539","dwhClassId":5,"isDwhSmsParsingRealtime":true,"debugKey":"operator:airtel_custId:1188476171_rechargeNo:7502347350","plan_bucket":"Special Recharge", "partialBillState": "EXPIRING_SOON"}
       
        let dbRec = [  {
                id: 2325332343,
                recharge_number: '8178296625',
                customer_id: 1487309661,
                service: 'mobile',
                operator: 'jio',
                circle: 'delhi ncr',
                amount: 149,
                validity_expiry_date: "2024-05-17T13:12:04.000Z",
                order_date: "2024-03-18T13:12:04.000Z",
                latest_recharge_date: "2024-04-27T13:59:05.000Z",
                plan_bucket: 'Special Recharge',
                category_name: 'Recharge Plan',
                created_at: "2024-03-18T13:14:23.000Z",
                updated_at: "2024-04-27T14:00:55.000Z",
                cust_mobile: '8178296625',
                cust_email: '',
                rn_customer_id: null,
                order_ids: ',23224464816,23301018202,23409951349',
                status: 11,
                extra: '{"customer_type":1,"updated_data_source":"transaction","recon_id":"Pdew186hf9e8gR+FrL2XpUGTw4o=","user_type":"RU","source_subtype_2":"FULL_BILL","paytype":"prepaid","bill_date":null,"bill_fetch_date":null,"is_automatic":null}',
                notification_status: 1,
                cust_rech_meta: '',
                product_id: 465268138 } ]
        let stub1 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);
        let processedRecord = await serviceObj.processingOnDbRecords( function(){

            if (error) {
                expect(error).to.be.equal(null);
            }
        }, dbRec, rec, parent);
        // expect(stub).to.have.callCount(1);
        expect(stub1).to.have.callCount(0);
        expect(processedRecord).to.equal(undefined);
        // stub.restore();
        stub1.restore();
        
    })


    it.skip("validateAndProcessRecord | Valid Record(20-dec-2022) | As per Production data", async() => {
        let recordData = { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": ************, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 2, "expiry_date": "20-dec-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal(null);
        expect(record.customerId).to.be.equal(**********);
        expect(record.rechargeNumber).to.be.equal("9770636074");
        expect(record.validityDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-12-20 00:00:00");
        expect(record.operator).to.be.equal("airtel");
        expect(record.service).to.be.equal("mobile");
        expect(record.paytype).to.be.equal("prepaid");
    });

    it.skip("validateAndProcessRecord | Valid Record(reminaing days) | As per Production data", async() => {
        let recordData = { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": 1657511608671, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "+************", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": null } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal(null);
        expect(record.customerId).to.be.equal(**********);
        expect(record.rechargeNumber).to.be.equal("9770636074");
        expect(record.validityDate.format('YYYY-MM-DD HH:mm:ss')).to.be.equal("2022-11-08 09:23:28");
        expect(record.operator).to.be.equal("airtel");
        expect(record.service).to.be.equal("mobile");
        expect(record.paytype).to.be.equal("prepaid");
    });

    it.skip("validateAndProcessRecord | Invalid Record", async() => {
        let recordData = { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "cId": **********, "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": null } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal("Validity Already Expired");
    });


    it.skip("validateAndProcessRecord | customerId not passed", async() => {
        let recordData = { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params customerId is Missing / Invalid');

    });

    it("validateAndProcessRecord | invalid validity", async() => {
        let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": MOMENT().add(2, 'year').format('DD-MM-YYYY') } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('ValidityMoreThan_1_year');

    });


    it.skip("validateAndProcessRecord | Invalid customerId - alpha numeric", async() => {
        let recordData = { "cId": "273XP69A5", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "************", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params customerId is Missing / Invalid');
    });

    it.skip("validateAndProcessRecord | Plan Amount missing", async() => {
        let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 0, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params planAmount is Missing / Invalid');

    });
    it.skip("validateAndProcessRecord | Invalid Plan Amount", async() => {
        let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": "sj", "operator": "AIRTEL", "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params planAmount is Missing / Invalid');

    });

    it.skip("validateAndProcessRecord | Plan Amount -ve", async() => {
        let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": -82, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params planAmount is Missing / Invalid');

    });

    it.skip("validateAndProcessRecord | operator is missing", async() => {
        let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": null, "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params operator is Missing / Invalid');

    });

    it.skip("validateAndProcessRecord | Recharge number missing", async() => {
        let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsOperator": "JIO", "smsReceiver": null, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": "AIRTEL", "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params rechargeNumber is Missing / Invalid');

    });


    it.skip("validateAndProcessRecord |  InValid Record | Missing all mandatory params", async() => {
        let recordData = { "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsReceiver": null, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 0, "operator": null, "remaining_days": 120, "expiry_date": "20-12-2022" } };
        let [error, record] = await serviceObj.validateAndProcessRecord(recordData);
        expect(error).to.be.equal('Mandatory Params customerId,rechargeNumber,planAmount,operator is Missing / Invalid');

    });

    it("parseAmount | Valid Record | Integer - Rs.590", () => {
        expect(serviceObj.parseAmount("Rs.590")).to.be.equal(590);
    });
    it("parseAmount | Valid Record | Float - Rs.590.78", () => {
        expect(serviceObj.parseAmount("Rs.590.78")).to.be.equal(590.78);
    });
    it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....-1223.34", () => {
        expect(serviceObj.parseAmount("Rs   ....-1223.34")).to.be.equal(-1223.34);
    });
    it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....+1223.34", () => {
        expect(serviceObj.parseAmount("Rs   ....+1223.34")).to.be.equal(1223.34);
    });
    it("parseAmount | Valid Record | zero amount -> Rs.0", () => {
        expect(serviceObj.parseAmount("Rs.0")).to.be.equal(null);
    });
    it("parseAmount | Valid Record | without Rs. string", () => {
        expect(serviceObj.parseAmount("590")).to.be.equal(590);
    });
    it("parseAmount | Valid Record | without Rs. string", () => {
        expect(serviceObj.parseAmount("-590")).to.be.equal(-590);
    });
    it("parseAmount | Valid Record | as Number", () => {
        expect(serviceObj.parseAmount(590)).to.be.equal(590);
    });
    it("parseAmount | Valid Record | as Number", () => {
        expect(serviceObj.parseAmount(590.67)).to.be.equal(590.67);
    });
    it("parseAmount | InValid Record | as null", () => {
        expect(serviceObj.parseAmount(null)).to.be.equal(null);
    });
    it("parseAmount | InValid Record | as normal string", () => {
        expect(serviceObj.parseAmount("amount")).to.be.equal(null);
    });

    // it("getRecordFromPlanValidity | Valid record | getRecordsFromRecentMongo not called function", (done) => {

    //     let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": null, "remaining_days": 120, "expiry_date": "20-12-2022" } };
    //     let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "jio" }

    //     let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
    //     let stub3 = sinon.stub(serviceObj, 'processingOnDbRecords').callsFake(function fakeFn(done) {
    //         return done(null);
    //     })
    //     let stub4 = sinon.stub(serviceObj, 'get_circle_of_record').callsFake(function fakeFn(done) {
    //         return done(null, "delhi");
    //     });

    //     let stub5 = sinon.stub(serviceObj, 'get_productId_of_record').callsFake(function fakeFn(done) {
    //         return done(null, 12938109);
    //     });
    //     let stub6 = sinon.stub(serviceObj, 'get_plan_details_of_record').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });
    //     let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
    //         return done(null, []);
    //     });

    //     let stub7 = sinon.stub(serviceObj, 'getRecordsFromRecentMongo').callsFake(function fakeFn(done) {
    //         return done(null, true);
    //     });
        
    //     let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });

    //     //let stub7 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);

    //     serviceObj.processRecords(recordData, function (err) {
    //         if (err) {
    //             expect(err).to.equal('Error from getRecordFromPlanValidity');
    //         }
    //         done();
    //     });
        
    //     stub1.restore();
    //     stub2.restore();
    //     stub3.restore();
    //     stub4.restore();
    //     stub5.restore();
    //     stub6.restore();
    //     stub7.restore();
    //     stub8.restore();


    //     expect(stub1).to.have.callCount(1);
    //     expect(stub4).to.have.callCount(1);
    //     expect(stub5).to.have.callCount(1);
    //     expect(stub6).to.have.callCount(1);
    //     expect(stub2).to.have.callCount(1);
    //     expect(stub7).to.have.callCount(0);
    //     expect(stub3).to.have.callCount(1);
    //     expect(stub8).to.have.callCount(1);
      


    // });



    // it("getRecordsFromRecentMongo | Valid record | Valid getRecordsFromRecentMongo function", (done) => {

    //     let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": null, "remaining_days": 120, "expiry_date": "20-12-2022" } };
    //     let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "jio" }

    //     let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
    //     let stub3 = sinon.stub(serviceObj, 'processingOnDbRecords').callsFake(function fakeFn(done) {
    //         return done(null);
    //     })
    //     let stub4 = sinon.stub(serviceObj, 'get_circle_of_record').callsFake(function fakeFn(done) {
    //         return done(null, "delhi");
    //     });

    //     let stub5 = sinon.stub(serviceObj, 'get_productId_of_record').callsFake(function fakeFn(done) {
    //         return done(null, 12938109);
    //     });
    //     let stub6 = sinon.stub(serviceObj, 'get_plan_details_of_record').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });
    //     let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
    //         return done(null, []);
    //     });

    //     let stub7 = sinon.stub(serviceObj, 'getRecordsFromRecentMongo').callsFake(function fakeFn(done) {
    //         return done(null, true);
    //     });
    //     //let stub7 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);

    //     let stub8 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });

    //     serviceObj.processRecords(recordData, function (err) {
    //         if (err) {
    //             expect(err).to.equal('Error from getRecordFromPlanValidity');
    //         }
    //         done();
    //     });

    //     stub1.restore();
    //     stub2.restore();
    //     stub3.restore();
    //     stub4.restore();
    //     stub5.restore();
    //     stub6.restore();
    //     stub7.restore();
    //     stub8.restore();

    //     expect(stub1).to.have.callCount(1);
    //     expect(stub4).to.have.callCount(1);
    //     expect(stub5).to.have.callCount(1);
    //     expect(stub6).to.have.callCount(1);
    //     expect(stub2).to.have.callCount(1);
    //     expect(stub7).to.have.callCount(1);
    //     expect(stub3).to.have.callCount(0);
    //     expect(stub8).to.have.callCount(1);
       


    // });

    it.skip("getRecordsFromRecentMongo |  Error in exec", (done) => {

        let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "jio" }
        let stub1 = sinon.stub(serviceObj, "mongoThrottleWapper").resolves();

        serviceObj.getRecordsFromRecentMongo(function (error) {
            expect(error).to.be.equal(null);
        }, processedRecord);
        expect(stub1).to.have.callCount(1);
        stub1.restore();

    });


    // it("getRecordsFromRecentMongo | Valid record | Error in getRecordsFromPlanValidity function", (done) => {

    //     let recordData = { "cId": "98872662", "clientId": "androidapp", "eventType": "smsEvent", "event_name": "sms", "msg_id": null, "latitude": 0, "longitude": 0, "mId": "", "model": "TA-1021", "netWorkType": "WIFI", "osType": "android", "osVersion": "28", "newUser": null, "realTime": null, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsBody": "Aapka Airtel pack kal samapt ho jayega. Apne pasandida unlimited pack se abhi recharge karein. Aap apne main ac", "smsReceiver": "9770636074", "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "predicted_category": "telecom", "fastag_class": "-1", "fastag_features": {}, "level_2_category": 3, "telecom_details": { "plan_amount": 99, "operator": null, "remaining_days": 120, "expiry_date": "20-12-2022" } };
    //     let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "jio" }

    //     let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
    //     let stub3 = sinon.stub(serviceObj, 'processingOnDbRecords').callsFake(function fakeFn(done) {
    //         return done(null);
    //     })
    //     let stub4 = sinon.stub(serviceObj, 'get_circle_of_record').callsFake(function fakeFn(done) {
    //         return done(null, "delhi");
    //     });

    //     let stub5 = sinon.stub(serviceObj, 'get_productId_of_record').callsFake(function fakeFn(done) {
    //         return done(null, 12938109);
    //     });
    //     let stub6 = sinon.stub(serviceObj, 'get_plan_details_of_record').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });
    //     let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
    //         return done("Error from getRecordsFromPlanValidity");
    //     });

    //     //let stub7 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);

    //     serviceObj.processRecords(recordData, function (err) {
    //         if (err) {
    //             expect(err).to.equal('Error from getRecordFromPlanValidity');
    //         }
    //         done();
    //     });

    //       stub1.restore();
    //     stub2.restore();
    //     stub3.restore();
    //     stub4.restore();
    //     stub5.restore();
    //     stub6.restore();
        
    //     expect(stub1).to.have.callCount(1);
    //     expect(stub4).to.have.callCount(1);
    //     expect(stub5).to.have.callCount(1);
    //     expect(stub6).to.have.callCount(1);
    //     expect(stub2).to.have.callCount(1);
    //     expect(stub3).to.have.callCount(0);
      


    // });

    /*
        it("getRecordsFromPlanValidity | Valid record | Error in getRecordsFromValidity function", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
                return done("Error from getRecordsFromPlanValidity");
            });
            let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
            let stub3 = sinon.stub(serviceObj, 'processingOnDbRecords').callsFake(function fakeFn(done) {
                return done(null);
            })
            let stub4 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);
    
            serviceObj.processRecords(function (err) {
                if (err) {
                    expect(err).to.equal('Error from getRecordFromPlanValidity');
                }
                done();
            }, recordData);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
    
        });
    
        it("getRecordsFromPlanValidity | Valid record | Error in processingOnDbRecords function", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
                return done(null, []);
            });
            let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
            let stub3 = sinon.stub(serviceObj, 'processingOnDbRecords').callsFake(function fakeFn(done) {
                return done("Error from processingOnDbRecords")
            })
            let stub4 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);
    
            serviceObj.processRecords(function (err) {
                if (err) {
                    expect(err).to.equal('Error from processingOnDbRecords');
                }
                done();
            }, recordData);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
    
        });
    
    
        it("getRecordsFromPlanValidity | Valid record | Success ", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj, 'getRecordsFromPlanValidity').callsFake(function fakeFn(done) {
                return done(null, []);
            });
            let stub2 = sinon.stub(serviceObj, 'validateAndProcessRecord').returns([null, processedRecord]);
            let stub3 = sinon.stub(serviceObj, 'processingOnDbRecords').callsFake(function fakeFn(done) {
                return done(null);
            })
            let stub4 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);
    
            serviceObj.processRecords(function (err) {
                if (err) {
                    expect(err).to.equal(null);
                }
                done();
            }, recordData);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
    
        });
    
        it("getRecordsFromPlanValidity |  Error in exec", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj.dbInstance, 'exec').callsFake(function (done) {
                return done("Error in exec");
            })
            serviceObj.getRecordsFromPlanValidity(function (error) {
                expect(error).to.be.equal("Error in exec");
            }, processedRecord);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
    
        });
    
    
        it("getRecordsFromPlanValidity |  Null response of exec", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj.dbInstance, 'exec').callsFake(function (done) {
                return done(null, []);
            })
            serviceObj.getRecordsFromPlanValidity(function (error) {
                expect(error).to.be.equal(null);
            }, processedRecord);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
    
        });
    
        it(" processingOnDbRecords |  valid  | error in Cassandra", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);
            let stub2 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function (done) {
                return done("Error in Cassandra");
            })
            serviceObj.processingOnDbRecords(function (error) {
                if (error) {
                    expect(error).to.be.equal("Error in Cassandra");
                }
            }, recordData, processedRecord);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
    
        });
    
        it(" processingOnDbRecords |  valid  | Success", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj, 'updatePlanValidty').returns(null);
            let stub2 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function (done) {
                return done(null);
            })
            serviceObj.processingOnDbRecords(function (error) {
                if (error) {
                    expect(error).to.be.equal(null);
                }
            }, recordData, processedRecord);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
    
        });
    
        it("updatePlanValidity |  Error in exec", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj.dbInstance, 'exec').callsFake(function (done) {
                return done("Error in exec");
            })
            let stub2 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
                return done(null);
            })
            serviceObj.updatePlanValidity(function (error) {
                expect(error).to.be.equal("Error in exec");
            }, processedRecord, []);
            expect(stub1).to.have.callCount(1);
            expect(stub1).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        });
    
        it("updatePlanValidity |  Valid", (done) => {
    
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            let processedRecord = { "customerId": "227316905", "rechargeNumber": 9876543210, "planAmount": 99, "validityDate": "2022-06-04T13:44:33.487", "operator": "AIRTEL" }
            let stub1 = sinon.stub(serviceObj.dbInstance, 'exec').callsFake(function (done) {
                return done(null, []);
            })
            let stub2 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
                return done(null);
            })
            serviceObj.updatePlanValidity(function (error) {
                expect(error).to.be.equal(null);
            }, processedRecord, []);
            expect(stub1).to.have.callCount(1);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        });
    */


        it("validateAndProcessRecord | Valid record | Success", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };

            let dwhClassId = 6;
            let telecomDetails = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null };
            let [invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted] = [false, recordData, false, false];
            let validityDate = null;
            let is_wo_validity = true;
            let invalidValidityDate = false;
            let invalidParams = false;

            let processedRecord = {
                "customerId": "227316905", 
                "rechargeNumber": 9876543210, 
                "planAmount": 99, 
                "validityDate": "2022-06-04T13:44:33.487", 
                "operator": "AIRTEL"
            }

            //checkBasicValidation is a promise function
            let stub1 = sinon.stub(serviceObj, 'checkBasicValidation').returns([null, recordData]);

            let stub2 = sinon.stub(serviceObj, 'setTimestamps').returns(recordData);

            let stub3 = sinon.stub(serviceObj, 'getDwhClassIdAndTelecomDetails').returns({
                dwhClassId, telecomDetails
            });

            let stub4 = sinon.stub(serviceObj, 'validateDataExpiryMessages').returns([
                invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted
            ]);

            let stub5 = sinon.stub(serviceObj, 'getValidityDate').returns([
                validityDate, is_wo_validity
            ]);

            let stub6 = sinon.stub(serviceObj, 'validateValidityDates').returns([
                invalidValidityDate, record, validityDate
            ]);

            let stub7 = sinon.stub(serviceObj, 'processRecordDetails').returns(processedRecord);

            let stub8 = sinon.stub(serviceObj, 'validateRecord').returns([
                invalidParams, processedRecord
            ]);

            let stub9 = sinon.stub(serviceObj, 'sendMetrics').returns(null);

            let [error, result] = await serviceObj.validateAndProcessRecord(recordData);

            expect(result).to.be.equal(processedRecord);
            expect(error).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(1);
            expect(stub8).to.have.callCount(1);
            expect(stub9).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();
        });

        it("validateAndProcessRecord | checkBasicValidation failed", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };

            let dwhClassId = 6;
            let telecomDetails = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null };
            let [invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted] = [false, recordData, false, false];
            let validityDate = null;
            let is_wo_validity = true;
            let invalidValidityDate = false;
            let invalidParams = false;

            let processedRecord = {
                "customerId": "227316905", 
                "rechargeNumber": 9876543210, 
                "planAmount": 99, 
                "validityDate": "2022-06-04T13:44:33.487", 
                "operator": "AIRTEL"
            }

            //checkBasicValidation is a promise function
            let stub1 = sinon.stub(serviceObj, 'checkBasicValidation').returns(['Basic Validation Failed', recordData]);

            let stub2 = sinon.stub(serviceObj, 'setTimestamps').returns(recordData);

            let stub3 = sinon.stub(serviceObj, 'getDwhClassIdAndTelecomDetails').returns({
                dwhClassId, telecomDetails
            });

            let stub4 = sinon.stub(serviceObj, 'validateDataExpiryMessages').returns([
                invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted
            ]);

            let stub5 = sinon.stub(serviceObj, 'getValidityDate').returns([
                validityDate, is_wo_validity
            ]);

            let stub6 = sinon.stub(serviceObj, 'validateValidityDates').returns([
                invalidValidityDate, record, validityDate
            ]);

            let stub7 = sinon.stub(serviceObj, 'processRecordDetails').returns(processedRecord);

            let stub8 = sinon.stub(serviceObj, 'validateRecord').returns([
                invalidParams, processedRecord
            ]);

            let stub9 = sinon.stub(serviceObj, 'sendMetrics').returns(null);

            let [error, result] = await serviceObj.validateAndProcessRecord(recordData);

            expect(result).to.be.equal(recordData);
            expect(error).to.be.equal('Basic Validation Failed');
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();
        });

        it("validateAndProcessRecord | DataLessThan90", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };

            let dwhClassId = 6;
            let telecomDetails = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null };
            let [invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted] = ['DataLessThan90', recordData, null, false];
            let validityDate = null;
            let is_wo_validity = true;
            let invalidValidityDate = false;
            let invalidParams = false;

            let processedRecord = {
                "customerId": "227316905", 
                "rechargeNumber": 9876543210, 
                "planAmount": 99, 
                "validityDate": "2022-06-04T13:44:33.487", 
                "operator": "AIRTEL"
            }

            //checkBasicValidation is a promise function
            let stub1 = sinon.stub(serviceObj, 'checkBasicValidation').returns([null, recordData]);

            let stub2 = sinon.stub(serviceObj, 'setTimestamps').returns(recordData);

            let stub3 = sinon.stub(serviceObj, 'getDwhClassIdAndTelecomDetails').returns({
                dwhClassId, telecomDetails
            });

            let stub4 = sinon.stub(serviceObj, 'validateDataExpiryMessages').returns([
                invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted
            ]);

            let stub5 = sinon.stub(serviceObj, 'getValidityDate').returns([
                validityDate, is_wo_validity
            ]);

            let stub6 = sinon.stub(serviceObj, 'validateValidityDates').returns([
                invalidValidityDate, record, validityDate
            ]);

            let stub7 = sinon.stub(serviceObj, 'processRecordDetails').returns(processedRecord);

            let stub8 = sinon.stub(serviceObj, 'validateRecord').returns([
                invalidParams, processedRecord
            ]);

            let stub9 = sinon.stub(serviceObj, 'sendMetrics').returns(null);

            let [error, result] = await serviceObj.validateAndProcessRecord(recordData);

            expect(result).to.be.equal(recordData);
            expect(error).to.be.equal('DataLessThan90');
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();
        });

        it("validateAndProcessRecord | Incorrect validity date", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };

            let dwhClassId = 6;
            let telecomDetails = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null };
            let [invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted] = [null, recordData, false, false];
            let validityDate = null;
            let is_wo_validity = true;
            let invalidValidityDate = 'Incorrect validity date'
            let invalidParams = false;

            let processedRecord = {
                "customerId": "227316905", 
                "rechargeNumber": 9876543210, 
                "planAmount": 99, 
                "validityDate": "2022-06-04T13:44:33.487", 
                "operator": "AIRTEL"
            }

            //checkBasicValidation is a promise function
            let stub1 = sinon.stub(serviceObj, 'checkBasicValidation').returns([null, recordData]);

            let stub2 = sinon.stub(serviceObj, 'setTimestamps').returns(recordData);

            let stub3 = sinon.stub(serviceObj, 'getDwhClassIdAndTelecomDetails').returns({
                dwhClassId, telecomDetails
            });

            let stub4 = sinon.stub(serviceObj, 'validateDataExpiryMessages').returns([
                invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted
            ]);

            let stub5 = sinon.stub(serviceObj, 'getValidityDate').returns([
                validityDate, is_wo_validity
            ]);

            let stub6 = sinon.stub(serviceObj, 'validateValidityDates').returns([
                invalidValidityDate, record, validityDate
            ]);

            let stub7 = sinon.stub(serviceObj, 'processRecordDetails').returns(processedRecord);

            let stub8 = sinon.stub(serviceObj, 'validateRecord').returns([
                invalidParams, processedRecord
            ]);

            let stub9 = sinon.stub(serviceObj, 'sendMetrics').returns(null);

            let [error, result] = await serviceObj.validateAndProcessRecord(recordData);

            expect(result).to.be.equal(recordData);
            expect(error).to.be.equal('Incorrect validity date');
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(0);
            expect(stub8).to.have.callCount(0);
            expect(stub9).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();
        });

        it("validateAndProcessRecord | Incorrect validity date", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };

            let dwhClassId = 6;
            let telecomDetails = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null };
            let [invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted] = [null, recordData, false, false];
            let validityDate = null;
            let is_wo_validity = true;
            let invalidValidityDate = false;
            let invalidParams = 'Duedate missing';

            let processedRecord = {
                "customerId": "227316905", 
                "rechargeNumber": 9876543210, 
                "planAmount": 99, 
                "validityDate": "2022-06-04T13:44:33.487", 
                "operator": "AIRTEL"
            }

            //checkBasicValidation is a promise function
            let stub1 = sinon.stub(serviceObj, 'checkBasicValidation').returns([null, recordData]);

            let stub2 = sinon.stub(serviceObj, 'setTimestamps').returns(recordData);

            let stub3 = sinon.stub(serviceObj, 'getDwhClassIdAndTelecomDetails').returns({
                dwhClassId, telecomDetails
            });

            let stub4 = sinon.stub(serviceObj, 'validateDataExpiryMessages').returns([
                invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted
            ]);

            let stub5 = sinon.stub(serviceObj, 'getValidityDate').returns([
                validityDate, is_wo_validity
            ]);

            let stub6 = sinon.stub(serviceObj, 'validateValidityDates').returns([
                invalidValidityDate, record, validityDate
            ]);

            let stub7 = sinon.stub(serviceObj, 'processRecordDetails').returns(processedRecord);

            let stub8 = sinon.stub(serviceObj, 'validateRecord').returns([
                invalidParams, processedRecord
            ]);

            let stub9 = sinon.stub(serviceObj, 'sendMetrics').returns(null);

            let [error, result] = await serviceObj.validateAndProcessRecord(recordData);

            expect(result).to.be.equal(recordData);
            expect(error).to.be.equal('Duedate missing');
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(1);
            expect(stub8).to.have.callCount(1);
            expect(stub9).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            stub8.restore();
            stub9.restore();
        });

        it("checkBasicValidation | Valid Record", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            
            let stub1 = sinon.stub(serviceObj, 'saveForAnalyticsInCassandraAndKafka').returns(null);

            let stub2 = sinon.stub(serviceObj, 'saveForAnalytics').returns(null);

            let [error, record] = await serviceObj.checkBasicValidation(recordData);

            expect(error).to.be.equal(null);
            expect(record).to.be.equal(recordData);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        })

        it("checkBasicValidation | invalid Record", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null } };
            
            let stub1 = sinon.stub(serviceObj, 'saveForAnalyticsInCassandraAndKafka').returns(true);

            let stub2 = sinon.stub(serviceObj, 'saveForAnalytics').returns(null);

            let [error, record] = await serviceObj.checkBasicValidation(recordData);

            expect(error).to.be.equal('invalid_record');
            expect(record).to.be.equal(recordData);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })

        it("validateDataExpiryMessages | Valid Record", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "90%" } };

            let telecom_details = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "90%" };
            
            let dwhClassId = 1;
            
            let stub1 = sinon.stub(serviceObj, 'saveForAnalyticsInCassandraAndKafka').returns(true);

            let stub2 = sinon.stub(serviceObj, 'saveForAnalytics').returns(null);

            let [error, record, dataConsumed, isRealTimeDataExhausted] = await serviceObj.validateDataExpiryMessages(recordData, dwhClassId, telecom_details);

            expect(error).to.be.equal(null);
            expect(record).to.be.equal(recordData);
            expect(dataConsumed).to.be.equal("90%");
            expect(isRealTimeDataExhausted).to.be.equal(true);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        })

        it("validateDataExpiryMessages | Invalid Record", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "90%" } };

            let telecom_details = { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "50%" };
            
            let dwhClassId = 1;
            
            let stub1 = sinon.stub(serviceObj, 'saveForAnalyticsInCassandraAndKafka').returns(true);

            let stub2 = sinon.stub(serviceObj, 'saveForAnalytics').returns(null);

            let [error, record, dataConsumed, isRealTimeDataExhausted] = await serviceObj.validateDataExpiryMessages(recordData, dwhClassId, telecom_details);

            expect(error).to.be.equal('DataLess90');
            expect(record).to.be.equal(recordData);
            expect(dataConsumed).to.be.equal(null);
            expect(isRealTimeDataExhausted).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })

        it("fetchRecordsFromAirtelPrepaidTable | Valid Record", (done) => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "90%" } };
            
            let stub1 = sinon.stub(serviceObj, 'getTableName').returns('bills_airtelprepaid1');

            let stub2 = sinon.stub(serviceObj.bills, 'getRecordsFromAirtelPrepaidTable').callsFake((tableName, processedRecord, callback) => {
                callback(null, [{
                    "customerId": "227316905",
                    "rechargeNumber": 9876543210,
                    "planAmount": 99,
                    "validityDate": "2022-06-04T13:44:33.487",
                    "operator": "AIRTEL"
                }]);
            });

            let stub3 = sinon.stub(serviceObj, 'filterRecordsFromAirtelPrepaidTable').returns([
                '2025-06-04T13:44:33.487', ["227316905"]
            ]);

            let stub4 = sinon.stub(serviceObj, 'setAirtelPrepaidPublisherDetails').returns({
                "maxDueDate": "2025-06-04T13:44:33.487",
                "listOfOtherCustomerIds": ["227316905"]
            });

            serviceObj.fetchRecordsFromAirtelPrepaidTable(function (error, recordData) {
                console.log("recordData :", recordData);
                expect(error).to.be.equal(null);
                expect(recordData.airtelPrepaidPublisherDetails.maxDueDate).to.be.equal('2025-06-04T13:44:33.487');
                expect(recordData.airtelPrepaidPublisherDetails.listOfOtherCustomerIds).to.deep.equal(["227316905"]);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                done();
            }, recordData);
        })

        it("setAirtelPrepaidPublisherDetails | Valid Record", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "90%" } };
            
            let airtelPrepaidPublisherDetails = serviceObj.setAirtelPrepaidPublisherDetails(recordData, "2025-06-04T13:44:33.487", ["227316905"]);

            expect(airtelPrepaidPublisherDetails.max_due_date).to.be.equal('2025-06-04T13:44:33.487');
            expect(airtelPrepaidPublisherDetails.list_of_other_customer_ids).to.deep.equal(["227316905"]);
        })

        it("filterRecordsFromAirtelPrepaidTable | Valid Record", async () => {
            let recordData = { "cId": "227316905", "deviceDateTime": *************, "uploadTime": *************, "smsDateTime": *************, 
                "smsSenderID": "AR-AIRTEL", "smsOperator": "JIO", "smsReceiver": 9876543210, "smsUUID": "e55f86cc-439e-4484-9592-982d9419b97a", 
                "telecom_details": { "plan_amount": 99, "expiry_date": null, "operator": "AIRTEL", "remaining_days": null, "data_expiry_percentage": "90%" } };
                
            let result = [{
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "due_date": "2022-06-04T13:44:33.487",
                "operator": "AIRTEL",
                "status": 130
            }, {
                "customer_id": "227316906",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "due_date": "2022-06-05T13:44:33.487",
                "operator": "AIRTEL",
                "status": 4
            },{
                "customer_id": "227316907",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "due_date": "2022-06-08T13:44:33.487",
                "operator": "AIRTEL",
                "status": 1
            }]

            let processedRecord = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
            }

            let [maxDueDate, listOfOtherCustomerIds] = serviceObj.filterRecordsFromAirtelPrepaidTable(result, processedRecord);
            console.log("maxDueDate :", maxDueDate);
            console.log("listOfOtherCustomerIds :", listOfOtherCustomerIds);
            expect(maxDueDate).to.be.equal('2022-06-08 13:44:33');
            expect(listOfOtherCustomerIds).to.deep.equal(["227316906", "227316907"]);
        })

        it("setMaxValidityBetweenAirtelPrepaidAndSmsRecord | Valid Record | all present dbDate is highest", async () => {
            let airtelDate = MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let smsDate = MOMENT().add(2, 'days').format('YYYY-MM-DD HH:mm:ss');
            let dbDate = MOMENT().add(3, 'days').format('YYYY-MM-DD HH:mm:ss');
            let processedRecord = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "validityDate": smsDate,
                "airtelPrepaidPublisherDetails": {
                    "max_due_date": airtelDate,
                    "listOfOtherCustomerIds": ["227316906", "227316907"]
                }
            }
            
            let record = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "validity_expiry_date": dbDate,
                "operator": "AIRTEL",
                "status": 11
            }

            let isPlanValidityUpdate = false;

            [processedRecord, isPlanValidityUpdate] = serviceObj.setMaxValidityBetweenAirtelPrepaidAndSmsRecord(processedRecord, record, isPlanValidityUpdate);
            console.log("processedRecord :", processedRecord);
            console.log("isPlanValidityUpdate :", isPlanValidityUpdate);
            expect(processedRecord.validityDate).to.be.equal(dbDate);
            expect(isPlanValidityUpdate).to.be.equal(false);
        })

        it("setMaxValidityBetweenAirtelPrepaidAndSmsRecord | Valid Record | all present smsDate is highest", async () => {
            let airtelDate = MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let smsDate = MOMENT().add(4, 'days').format('YYYY-MM-DD HH:mm:ss');
            let dbDate = MOMENT().add(3, 'days').format('YYYY-MM-DD HH:mm:ss');
            let processedRecord = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "validityDate": smsDate,
                "airtelPrepaidPublisherDetails": {
                    "max_due_date": airtelDate,
                    "listOfOtherCustomerIds": ["227316906", "227316907"]
                }
            }
            
            let record = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "validity_expiry_date": dbDate,
                "operator": "AIRTEL",
                "status": 11
            }

            let isPlanValidityUpdate = false;

            [processedRecord, isPlanValidityUpdate] = serviceObj.setMaxValidityBetweenAirtelPrepaidAndSmsRecord(processedRecord, record, isPlanValidityUpdate);
            console.log("processedRecord :", processedRecord);
            console.log("isPlanValidityUpdate :", isPlanValidityUpdate);
            expect(processedRecord.validityDate).to.be.equal(smsDate);
            expect(isPlanValidityUpdate).to.be.equal(true);
        })

        it("setMaxValidityBetweenAirtelPrepaidAndSmsRecord | Valid Record | all present airtelPrepaid is highest", async () => {
            let airtelDate = MOMENT().add(5, 'days').format('YYYY-MM-DD HH:mm:ss');
            let smsDate = MOMENT().add(4, 'days').format('YYYY-MM-DD HH:mm:ss');
            let dbDate = MOMENT().add(3, 'days').format('YYYY-MM-DD HH:mm:ss');
            let processedRecord = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "validityDate": smsDate,
                "airtelPrepaidPublisherDetails": {
                    "max_due_date": airtelDate,
                    "listOfOtherCustomerIds": ["227316906", "227316907"]
                }
            }
            
            let record = {
                "customer_id": "227316905",
                "recharge_number": 9876543210,
                "plan_amount": 99,
                "validity_expiry_date": dbDate,
                "operator": "AIRTEL",
                "status": 11
            }

            let isPlanValidityUpdate = false;

            [processedRecord, isPlanValidityUpdate] = serviceObj.setMaxValidityBetweenAirtelPrepaidAndSmsRecord(processedRecord, record, isPlanValidityUpdate);
            console.log("processedRecord :", processedRecord);
            console.log("isPlanValidityUpdate :", isPlanValidityUpdate);
            expect(processedRecord.validityDate).to.be.equal(airtelDate);
            expect(isPlanValidityUpdate).to.be.equal(true);
        })
});