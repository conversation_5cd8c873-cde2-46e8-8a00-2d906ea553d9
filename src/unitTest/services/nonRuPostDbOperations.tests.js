/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert, expect } from "chai";
import sinon from 'sinon';
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import _ from 'lodash';
import MOMENT from 'moment';

import NonRuPostDbOperations from '../../services/nonPaytmBills/nonRuPostDbOperations';
import STARTUP_MOCK from '../__mocks__/startUp';
import utility from '../../lib';

chai.use(chaiAsPromised);
chai.use(sinonChai);

describe("Module: NonRuPostDbOperations test suite", function () {
    let serviceObj;
    let sandbox;

    const mockBillsKafkaRow = {
        debugKey: "test-debug-key",
        customerId: "123456789",
        rechargeNumber: "9876543210",
        operator: "airtel",
        service: "mobile",
        paytype: "prepaid",
        amount: 100,
        dueDate: "2024-01-15T00:00:00.000Z",
        status: 1,
        notificationStatus: 1,
        extra: "{}",
        customerOtherInfo: "{}",
        productId: "test-product-id",
        source: "smsParsing",
        dbEvent: "upsert",
        is_encrypted_done: false,
        toBeNotified: false,
        toBeNotifiedForRealTime: false,
        toBeSentToPublisherBillFetch: false,
        toBeSentToPublisherMultiPid: false,
        toBeNotifiedForCtAndPFCCE: false,
        toBeSentToPublisherBillPush: false
    };

    const mockCassandraCdcPublisher = {
        publishData: sinon.stub().callsFake((data, callback) => callback(null))
    };

    before(function (done) {
        STARTUP_MOCK.init(function(error, options) {
            if (error) {
                done(error);
                return;
            }
            
            // Add required publishers to options
            options.ctKafkaPublisher = { publishData: sinon.stub() };
            options.paytmFirstKafkaPublisher = { publishData: sinon.stub() };
            options.nonRubillFetchKafkaPublisher = { publishData: sinon.stub() };
            options.nonRubillFetchKafkaPublisherRealtime = { publishData: sinon.stub() };
            options.upmsPublisher = { publishData: sinon.stub() };
            options.nonRuPublisher = { publishData: sinon.stub() };
            options.cassandraCdcPublisher = mockCassandraCdcPublisher;
            
            serviceObj = new NonRuPostDbOperations(options);
            done();
        });
    });

    beforeEach(function() {
        sandbox = sinon.createSandbox();
    });

    afterEach(function() {
        sandbox.restore();
    });

    describe("Constructor", function() {
        it("should initialize with all required dependencies", function() {
            expect(serviceObj).to.be.instanceOf(NonRuPostDbOperations);
            expect(serviceObj.L).to.exist;
            expect(serviceObj.config).to.exist;
            expect(serviceObj.payloadPreparator).to.exist;
            expect(serviceObj.publishToKafka).to.exist;
            expect(serviceObj.commonLib).to.exist;
            expect(serviceObj.reminderUtils).to.exist;
            expect(serviceObj.BillPush).to.exist;
            expect(serviceObj.cryptr).to.exist;
            expect(serviceObj.ctKafkaPublisher).to.exist;
            expect(serviceObj.paytmFirstKafkaPublisher).to.exist;
            expect(serviceObj.nonRubillFetchKafkaPublisher).to.exist;
            expect(serviceObj.nonRubillFetchKafkaPublisherRealtime).to.exist;
            expect(serviceObj.upmsPublisher).to.exist;
            expect(serviceObj.nonRuPublisher).to.exist;
            expect(serviceObj.cassandraCdcPublisher).to.exist;
            expect(serviceObj.notify).to.exist;
        });
    });

    describe("execute", function() {
        it("should execute all operations successfully when all flags are true", async function() {
            const testData = {
                ...mockBillsKafkaRow,
                toBeNotified: true,
                toBeNotifiedForRealTime: true,
                toBeSentToPublisherBillFetch: true,
                toBeSentToPublisherMultiPid: true,
                toBeNotifiedForCtAndPFCCE: true,
                toBeSentToPublisherBillPush: true
            };

            const publishCassandraCDCventsStub = sandbox.stub(serviceObj, 'publishCassandraCDCvents');
            const handleBillFetchNotificationStub = sandbox.stub(serviceObj, '_handleBillFetchNotification');
            const handleRealTimeNotificationsStub = sandbox.stub(serviceObj, '_handleRealTimeNotifications');
            const handleNonRuBillFetchPublisherStub = sandbox.stub(serviceObj, '_handleNonRuBillFetchPublisher');
            const handleCtAndPFCCEventsStub = sandbox.stub(serviceObj, '_handleCtAndPFCCEvents');
            const publishToUPMSRegistrationStub = sandbox.stub(serviceObj, '_publishToUPMSRegistration');

            await serviceObj.execute(testData);

            expect(publishCassandraCDCventsStub).to.have.been.calledOnce;
            expect(handleBillFetchNotificationStub).to.have.been.calledOnce;
            expect(handleRealTimeNotificationsStub).to.have.been.calledOnce;
            expect(handleNonRuBillFetchPublisherStub).to.have.been.calledTwice;
            expect(handleCtAndPFCCEventsStub).to.have.been.calledOnce;
            expect(publishToUPMSRegistrationStub).to.have.been.calledOnce;
        });

        it("should execute only CDC publishing when no flags are set", async function() {
            const testData = { ...mockBillsKafkaRow };
            const publishCassandraCDCventsStub = sandbox.stub(serviceObj, 'publishCassandraCDCvents');

            await serviceObj.execute(testData);

            expect(publishCassandraCDCventsStub).to.have.been.calledOnce;
        });

        it("should handle errors and throw them", async function() {
            const testData = { ...mockBillsKafkaRow };
            const error = new Error("Test error");
            sandbox.stub(serviceObj, 'publishCassandraCDCvents').throws(error);

            try {
                await serviceObj.execute(testData);
                assert.fail("Should have thrown an error");
            } catch (err) {
                expect(err).to.equal(error);
            }
        });
    });

    describe("publishCassandraCDCvents", function() {
        it("should publish CDC events successfully", async function() {
            const testData = { ...mockBillsKafkaRow };
            const validateStub = sandbox.stub(serviceObj, '_validateCDCInputs');
            const prepareStub = sandbox.stub(serviceObj, '_prepareCDCData').returns({ topic: 'test-topic', source: 'test-source' });
            const getKeyStub = sandbox.stub(serviceObj, '_getCDCPartitionKey').returns('test-key');
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj.publishCassandraCDCvents(testData, mockCassandraCdcPublisher);

            expect(validateStub).to.have.been.calledOnce;
            expect(prepareStub).to.have.been.calledOnce;
            expect(getKeyStub).to.have.been.calledOnce;
            expect(publishStub).to.have.been.calledOnce;
        });

        it("should handle errors during CDC publishing", async function() {
            const testData = { ...mockBillsKafkaRow };
            const error = new Error("CDC publishing error");
            sandbox.stub(serviceObj, '_validateCDCInputs').throws(error);

            try {
                await serviceObj.publishCassandraCDCvents(testData, mockCassandraCdcPublisher);
                assert.fail("Should have thrown an error");
            } catch (err) {
                expect(err).to.equal(error);
            }
        });
    });

    describe("_validateCDCInputs", function() {
        it("should validate inputs successfully", function() {
            const result = serviceObj._validateCDCInputs(mockBillsKafkaRow, mockCassandraCdcPublisher);
            expect(result).to.be.true;
        });

        it("should throw error when billsKafkaRow is null", function() {
            try {
                serviceObj._validateCDCInputs(null, mockCassandraCdcPublisher);
                assert.fail("Should have thrown an error");
            } catch (error) {
                expect(error.message).to.include("billsKafkaRow parameter is required");
            }
        });

        it("should throw error when cassandraCdcPublisher is invalid", function() {
            try {
                serviceObj._validateCDCInputs(mockBillsKafkaRow, {});
                assert.fail("Should have thrown an error");
            } catch (error) {
                expect(error.message).to.include("Valid cassandraCdcPublisher is required");
            }
        });
    });

    describe("_prepareCDCData", function() {
        it("should prepare CDC data successfully", function() {
            const testData = { ...mockBillsKafkaRow };
            const preparePayloadStub = sandbox.stub(serviceObj.payloadPreparator, 'preparePayloadForCassandraCDC').returns(testData);
            const processRechargeStub = sandbox.stub(serviceObj, '_processRechargeNumberDecryption').returns(testData);

            const result = serviceObj._prepareCDCData(testData);

            expect(preparePayloadStub).to.have.been.calledOnce;
            expect(processRechargeStub).to.have.been.calledOnce;
            expect(result).to.deep.equal(testData);
        });
    });

    describe("_processRechargeNumberDecryption", function() {
        it("should decrypt mobile service recharge number", function() {
            const testData = {
                service: 'mobile',
                rechargeNumber: 'encrypted-number'
            };
            const decryptStub = sandbox.stub(serviceObj.cryptr, 'decrypt').returns('decrypted-number');

            const result = serviceObj._processRechargeNumberDecryption(testData);

            expect(decryptStub).to.have.been.calledOnce;
            expect(result.decryptedRechargeNumber).to.equal('decrypted-number');
        });

        it("should handle decryption errors gracefully", function() {
            const testData = {
                service: 'mobile',
                rechargeNumber: 'encrypted-number'
            };
            const error = new Error("Decryption failed");
            sandbox.stub(serviceObj.cryptr, 'decrypt').throws(error);

            const result = serviceObj._processRechargeNumberDecryption(testData);

            expect(result.decryptedRechargeNumber).to.equal('encrypted-number');
        });

        it("should use original recharge number for non-mobile services", function() {
            const testData = {
                service: 'electricity',
                rechargeNumber: 'original-number'
            };

            const result = serviceObj._processRechargeNumberDecryption(testData);

            expect(result.decryptedRechargeNumber).to.equal('original-number');
        });
    });

    describe("_publishToUPMSRegistration", function() {
        it("should publish to UPMS when conditions are met", function() {
            const testData = {
                ...mockBillsKafkaRow,
                service: 'electricity',
                operator: 'electricity',
                extra: '{"ambiguous": false}'
            };
            const pushStub = sandbox.stub(serviceObj.BillPush, 'pushToRegistrationProcess');

            serviceObj._publishToUPMSRegistration(testData);

            expect(pushStub).to.have.been.calledOnce;
        });

        it("should skip publishing when ambiguous", function() {
            const testData = {
                ...mockBillsKafkaRow,
                extra: '{"ambiguous": true}'
            };
            const pushStub = sandbox.stub(serviceObj.BillPush, 'pushToRegistrationProcess');

            serviceObj._publishToUPMSRegistration(testData);

            expect(pushStub).to.not.have.been.called;
        });

        it("should handle errors during UPMS publishing", function() {
            const testData = { 
                ...mockBillsKafkaRow,
                service: 'electricity',
                operator: 'electricity',
                extra: '{"ambiguous": false}'
            };
            const error = new Error("UPMS publishing error");
            sandbox.stub(serviceObj.BillPush, 'pushToRegistrationProcess').throws(error);

            expect(() => {
                serviceObj._publishToUPMSRegistration(testData);
            }).to.throw("UPMS publishing error");
        });
    });

    describe("_postProcessSpecificPayloadHandling", function() {
        it("should handle prepaid mobile with due date notification", async function() {
            const record = {
                paytype: 'prepaid',
                service: 'mobile',
                dueDate: MOMENT().add(5, 'days').format('YYYY-MM-DD')
            };
            const payload = { data: {} };

            const result = await serviceObj._postProcessSpecificPayloadHandling(record, payload);

            expect(result.notificationType).to.equal('BILLDUE');
        });

        it("should handle fastag recharge", async function() {
            const record = {
                paytype: 'prepaid',
                service: 'fastag recharge'
            };
            const payload = { data: {} };

            const result = await serviceObj._postProcessSpecificPayloadHandling(record, payload);

            expect(result.source).to.equal('nonRUbillGenPublisherRealtime');
        });

        it("should handle old bill found status", async function() {
            const record = {
                status: 5
            };
            const payload = { data: {} };

            const result = await serviceObj._postProcessSpecificPayloadHandling(record, payload);

            expect(result.notificationType).to.equal('OLD_BILL_NOTIFICATION');
            expect(result.data.status).to.equal(5);
        });
    });

    describe("_handleBillFetchNotification", function() {
        it("should handle bill fetch notification successfully", async function() {
            const testData = { ...mockBillsKafkaRow };
            const payload = { topic: 'test-topic', source: 'test-source', data: { customer_id: '123' } };
            const preparePayloadStub = sandbox.stub(serviceObj.payloadPreparator, 'prepareNonRuBillNotificationPayload').resolves(payload);
            const postProcessStub = sandbox.stub(serviceObj, '_postProcessSpecificPayloadHandling').resolves(payload);
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj._handleBillFetchNotification(testData);

            expect(preparePayloadStub).to.have.been.calledOnce;
            expect(postProcessStub).to.have.been.calledOnce;
            expect(publishStub).to.have.been.calledOnce;
        });

        it("should handle payload preparation failure", async function() {
            const testData = { ...mockBillsKafkaRow };
            sandbox.stub(serviceObj.payloadPreparator, 'prepareNonRuBillNotificationPayload').resolves(null);

            await serviceObj._handleBillFetchNotification(testData);
            // Should not throw error, just log
        });
    });

    describe("_handleNonRuBillFetchPublisher", function() {
        it("should handle non-RU bill fetch publisher successfully", async function() {
            const testData = { ...mockBillsKafkaRow };
            const payload = { topic: 'test-topic', source: 'test-source', customerId: '123' };
            const preparePayloadStub = sandbox.stub(serviceObj.payloadPreparator, 'prepareNonRuBillFetchPublisherPayload').resolves(payload);
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj._handleNonRuBillFetchPublisher(testData, false);

            expect(preparePayloadStub).to.have.been.calledOnce;
            expect(publishStub).to.have.been.calledOnce;
        });

        it("should handle payload preparation failure", async function() {
            const testData = { ...mockBillsKafkaRow };
            sandbox.stub(serviceObj.payloadPreparator, 'prepareNonRuBillFetchPublisherPayload').resolves(null);

            await serviceObj._handleNonRuBillFetchPublisher(testData, false);
            // Should not throw error, just log
        });
    });

    describe("_handleRealTimeNotifications", function() {
        it("should handle real-time notifications successfully", async function() {
            const testData = { ...mockBillsKafkaRow, isRealTimeDataExhausted: false };
            const payload = { topic: 'test-topic', source: 'test-source', customer_id: '123' };
            const preparePayloadStub = sandbox.stub(serviceObj.payloadPreparator, 'prepareNonRuBillNotificationPayload').resolves(payload);
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj._handleRealTimeNotifications(testData);

            expect(preparePayloadStub).to.have.been.calledOnce;
            expect(publishStub).to.have.been.calledOnce;
        });

        it("should handle data exhaust scenario", async function() {
            const testData = { ...mockBillsKafkaRow, isRealTimeDataExhausted: true };
            const publishDataExhaustStub = sandbox.stub(serviceObj, 'publishInBillFetchKafkaForDataExhaust');

            await serviceObj._handleRealTimeNotifications(testData);

            expect(publishDataExhaustStub).to.have.been.calledOnce;
        });
    });

    describe("publishInBillFetchKafkaForDataExhaust", function() {
        it("should publish when user is not notified at FE", async function() {
            const testData = { ...mockBillsKafkaRow, is_notified_at_fe: false };
            const payload = { topic: 'test-topic', source: 'test-source', data: { customer_id: '123' } };
            const preparePayloadStub = sandbox.stub(serviceObj.payloadPreparator, 'preparePayloadForDataExhaust').returns(payload);
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj.publishInBillFetchKafkaForDataExhaust(testData);

            expect(preparePayloadStub).to.have.been.calledOnce;
            expect(publishStub).to.have.been.calledOnce;
        });

        it("should skip publishing when user is already notified at FE", async function() {
            const testData = { ...mockBillsKafkaRow, is_notified_at_fe: true };
            const preparePayloadStub = sandbox.stub(serviceObj.payloadPreparator, 'preparePayloadForDataExhaust');

            await serviceObj.publishInBillFetchKafkaForDataExhaust(testData);

            expect(preparePayloadStub).to.not.have.been.called;
        });
    });

    describe("_handleCtAndPFCCEvents", function() {
        it("should handle CT and PFCCE events successfully", async function() {
            const testData = { ...mockBillsKafkaRow, service: 'financial services' };
            const eventName = 'test-event';
            const mappedData = { customer_id: '123', service: 'financial services' };
            
            sandbox.stub(serviceObj, '_determineEventName').returns(eventName);
            sandbox.stub(serviceObj.commonLib, 'validateCTEventCondition').callsFake((cb) => cb(null));
            sandbox.stub(serviceObj.commonLib, 'getRetailerData').callsFake((cb) => cb(null));
            sandbox.stub(serviceObj.commonLib, 'getCvrData').callsFake((cb) => cb(null));
            sandbox.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns(mappedData);
            sandbox.stub(serviceObj.commonLib, 'isCTEventBlocked').returns(false);
            sandbox.stub(serviceObj, '_publishCTEvents');
            sandbox.stub(serviceObj, '_publishPaytmFirstCCEvents');

            await serviceObj._handleCtAndPFCCEvents(testData);
            // Should complete without errors
        });

        it("should handle CT event blocking", async function() {
            const testData = { ...mockBillsKafkaRow };
            const eventName = 'test-event';
            const mappedData = { customer_id: '123' };
            
            sandbox.stub(serviceObj, '_determineEventName').returns(eventName);
            sandbox.stub(serviceObj.commonLib, 'validateCTEventCondition').callsFake((cb) => cb(null));
            sandbox.stub(serviceObj.commonLib, 'getRetailerData').callsFake((cb) => cb(null));
            sandbox.stub(serviceObj.commonLib, 'getCvrData').callsFake((cb) => cb(null));
            sandbox.stub(serviceObj.reminderUtils, 'createCTPipelinePayload').returns(mappedData);
            sandbox.stub(serviceObj.commonLib, 'isCTEventBlocked').returns(true);

            await serviceObj._handleCtAndPFCCEvents(testData);
            // Should complete without publishing CT events
        });
    });

    describe("_determineEventName", function() {
        it("should return default event name for regular bills", function() {
            const testData = { ...mockBillsKafkaRow };
            const result = serviceObj._determineEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return delete event name for delete operations", function() {
            const testData = { ...mockBillsKafkaRow, dbEvent: 'delete' };
            const result = serviceObj._determineEventName(testData);
            expect(result).to.include('Del');
        });

        it("should return mobile event name for mobile service", function() {
            const testData = { ...mockBillsKafkaRow, service: 'mobile' };
            const result = serviceObj._determineEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return loan event name for loan service", function() {
            const testData = { ...mockBillsKafkaRow, service: 'loan' };
            const result = serviceObj._determineEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return electricity event name for electricity service", function() {
            const testData = { ...mockBillsKafkaRow, service: 'electricity' };
            const result = serviceObj._determineEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return rent event name for rent payment service", function() {
            const testData = { ...mockBillsKafkaRow, service: 'rent payment' };
            const result = serviceObj._determineEventName(testData);
            expect(result).to.be.a('string');
        });
    });

    describe("_getMobileEventName", function() {
        it("should return postpaid event name for mobile postpaid", function() {
            const testData = { ...mockBillsKafkaRow, service: 'mobile', paytype: 'postpaid' };
            const result = serviceObj._getMobileEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return prepaid event name for mobile prepaid", function() {
            const testData = { ...mockBillsKafkaRow, service: 'mobile', paytype: 'prepaid' };
            const result = serviceObj._getMobileEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return plan recharged event name", function() {
            const testData = { ...mockBillsKafkaRow, service: 'mobile', smsparsedPlanRecharged: true };
            const result = serviceObj._getMobileEventName(testData);
            expect(result).to.equal('smsparsedPlanRecharged');
        });
    });

    describe("_getLoanEventName", function() {
        it("should return loan event name", function() {
            const testData = { ...mockBillsKafkaRow, service: 'loan' };
            const result = serviceObj._getLoanEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return loan deleted event name", function() {
            const testData = { ...mockBillsKafkaRow, service: 'loan', dbEvent: 'delete' };
            const result = serviceObj._getLoanEventName(testData);
            expect(result).to.include('deleted');
        });
    });

    describe("_getElectricityEventName", function() {
        it("should return electricity postpaid event name", function() {
            const testData = { ...mockBillsKafkaRow, service: 'electricity' };
            const result = serviceObj._getElectricityEventName(testData);
            expect(result).to.be.a('string');
        });

        it("should return electricity bill paid event name", function() {
            const testData = { ...mockBillsKafkaRow, service: 'electricity', source: 'postpaidSmsParsingBillPaid' };
            const result = serviceObj._getElectricityEventName(testData);
            expect(result).to.include('BillPaid');
        });
    });

    describe("_getRentEventName", function() {
        it("should return landlord event name for level 1", function() {
            const testData = { ...mockBillsKafkaRow, service: 'rent payment', extra: '{"level_2_category": 1}' };
            const result = serviceObj._getRentEventName(testData);
            expect(result).to.include('Landlord');
        });

        it("should return tenant event name for level 2", function() {
            const testData = { ...mockBillsKafkaRow, service: 'rent payment', extra: '{"level_2_category": 2}' };
            const result = serviceObj._getRentEventName(testData);
            expect(result).to.include('Tenant');
        });

        it("should return delete event name for delete operations", function() {
            const testData = { ...mockBillsKafkaRow, service: 'rent payment', dbEvent: 'delete' };
            const result = serviceObj._getRentEventName(testData);
            expect(result).to.include('Delete');
        });
    });

    describe("_getReminderNonRuEventName", function() {
        it("should return reminder non-RU event name", function() {
            const testData = { ...mockBillsKafkaRow, source: 'reminderNonRuBillFetch' };
            const result = serviceObj._getReminderNonRuEventName(testData);
            expect(result).to.be.a('string');
        });
    });

    describe("_getCDCPartitionKey", function() {
        it("should return partition key with customer ID and recharge number", function() {
            const testData = { customerId: '123', rechargeNumber: '456' };
            const result = serviceObj._getCDCPartitionKey(testData);
            expect(result).to.equal('123_456');
        });

        it("should return empty string when customer ID is missing", function() {
            const testData = { rechargeNumber: '456' };
            const result = serviceObj._getCDCPartitionKey(testData);
            expect(result).to.equal('');
        });

        it("should return empty string when recharge number is missing", function() {
            const testData = { customerId: '123' };
            const result = serviceObj._getCDCPartitionKey(testData);
            expect(result).to.equal('');
        });
    });

    describe("_publishCTEvents", function() {
        it("should publish CT events successfully", async function() {
            const mappedData = { customer_id: '123', eventName: 'test-event' };
            const clonedData = { customer_id: '123', eventName: 'test-event', customerOtherInfo: '{"partialBillfound": false}' };
            const billsKafkaRow = { ...mockBillsKafkaRow };
            const eventName = 'test-event';
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj._publishCTEvents(mappedData, clonedData, billsKafkaRow, eventName);

            expect(publishStub).to.have.been.calledOnce;
        });

        it("should handle partial bill found scenario", async function() {
            const mappedData = { customer_id: '123', eventName: 'test-event' };
            const clonedData = { customer_id: '123', eventName: 'test-event', customerOtherInfo: '{"partialBillfound": true}' };
            const billsKafkaRow = { ...mockBillsKafkaRow };
            const eventName = 'test-event';
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj._publishCTEvents(mappedData, clonedData, billsKafkaRow, eventName);

            expect(publishStub).to.have.been.calledOnce;
        });
    });

    describe("_publishPaytmFirstCCEvents", function() {
        it("should publish Paytm First CC events successfully", async function() {
            const mappedData = { customer_id: '123' };
            const clonedData = { customer_id: '123' };
            const billsKafkaRow = { ...mockBillsKafkaRow };
            const publishStub = sandbox.stub(serviceObj.publishToKafka, 'publishMessageToKafka');

            await serviceObj._publishPaytmFirstCCEvents(mappedData, clonedData, billsKafkaRow);

            expect(publishStub).to.have.been.calledOnce;
        });
    });

    describe("_promisifyCallback", function() {
        it("should resolve when callback succeeds", async function() {
            const fn = (callback) => callback(null);
            const result = await serviceObj._promisifyCallback(fn);
            expect(result).to.be.undefined;
        });

        it("should reject when callback fails", async function() {
            const error = new Error("Test error");
            const fn = (callback) => callback(error);
            
            try {
                await serviceObj._promisifyCallback(fn);
                assert.fail("Should have thrown an error");
            } catch (err) {
                expect(err).to.equal(error);
            }
        });
    });

    describe("_sendCTMetrics", function() {
        it("should send CT metrics successfully", function() {
            const billsKafkaRow = { ...mockBillsKafkaRow };
            const eventName = 'test-event';
            const customerOtherInfo = { partialBillfound: false };

            // Test that the method doesn't throw an error
            expect(() => {
                serviceObj._sendCTMetrics(billsKafkaRow, eventName, customerOtherInfo, false);
            }).to.not.throw();
        });

        it("should send CT metrics with partial bill found", function() {
            const billsKafkaRow = { ...mockBillsKafkaRow };
            const eventName = 'test-event';
            const customerOtherInfo = { partialBillfound: true };

            // Test that the method doesn't throw an error
            expect(() => {
                serviceObj._sendCTMetrics(billsKafkaRow, eventName, customerOtherInfo, false);
            }).to.not.throw();
        });
    });
}); 