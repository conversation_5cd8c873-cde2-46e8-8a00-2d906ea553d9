/*
  jshint
    esversion: 8
 */

'use strict';

import { describe, it, before } from 'mocha';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import _ from 'lodash';
import STARTUP_MOCK from '../__mocks__/startUp';
import DynamicSmsParsingRegexExecutor from '../../services/smsParsingBillPayment/dynamicSmsParsingRegexExecutor';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe('Checking Validity Of Recharge Number (PID Regex PASS)', () => {

    let serviceObj;

    before(async function () {  
        try{
            await new Promise((resolve, reject) => {
                STARTUP_MOCK.init(function(error, options){
                    if (error) {
                        return reject(error);
                    }
                    serviceObj = new DynamicSmsParsingRegexExecutor(options);
                    resolve();
                });
            });
        }catch(err){
            console.log(err);
        }
    });
  
    it('should make no correction when PID regex matches', async () => {
        let dummyRecord = {
            "operator": "BESCOM",
            "rechargeNumber": "1234567",
            "category": "ELECTRICITY",
            "productId": 33683541
        };

        const result = serviceObj.checkValidityOfRechargeNumberByRegex(dummyRecord);
        console.log("check result", result, dummyRecord);

        expect(result).to.equal(true);
        expect(dummyRecord["rechargeNumber"]).to.equal("1234567");

    });
});

describe('Checking Validity Of Recharge Number (PID Regex FAIL)', () => {

    let serviceObj;

    before(async function () {  
        try{
            await new Promise((resolve, reject) => {
                STARTUP_MOCK.init(function(error, options){
                    if (error) {
                        return reject(error);
                    }
                    serviceObj = new DynamicSmsParsingRegexExecutor(options);
                    resolve();
                });
            });
        }catch(err){
            console.log(err);
        }
    });
  
    it('should make correction of removing 2 zeros from front of recharge number', async () => {
        let dummyRecord = {
            "operator": "BESCOM",
            "rechargeNumber": "001234567",
            "category": "ELECTRICITY",
            "productId": 33683541
        };

        const result = serviceObj.checkValidityOfRechargeNumberByRegex(dummyRecord);
        console.log("check result", result, dummyRecord);

        expect(result).to.equal(true);
        expect(dummyRecord["rechargeNumber"]).to.equal("1234567");

    });

    it('should make correction of adding 1 zero at front of recharge number', async () => {
        let dummyRecord = {
            "operator": "BESCOM",
            "rechargeNumber": "123456",
            "category": "ELECTRICITY",
            "productId": 33683541
        };

        const result = serviceObj.checkValidityOfRechargeNumberByRegex(dummyRecord);
        console.log("check result", result, dummyRecord);

        expect(result).to.equal(true);
        expect(dummyRecord["rechargeNumber"]).to.equal("0123456");

    });

    it('should make no correction when no matching regex found', async () => {
        let dummyRecord = {
            "operator": "BESCOM",
            "rechargeNumber": "00123456789245",
            "category": "ELECTRICITY",
            "productId": 33683541
        };

        const result = serviceObj.checkValidityOfRechargeNumberByRegex(dummyRecord);
        console.log("check result", result, dummyRecord);

        expect(result).to.equal(false);
        expect(dummyRecord["rechargeNumber"]).to.equal("00123456789245");

    });
});