'use strict';
import { describe, it, before, afterEach, beforeEach} from 'mocha';
import sinon from 'sinon';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _, { subtract } from 'lodash';
import STARTUP_MOCK from '../__mocks__/startUp'
import WhatsappNotificationFallBackConsumer from '../../services/whatsappNotificationFallbackConsumer';
import nock from 'nock';
import assert from 'assert';
import utility from '../../lib';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: whatsappNotificationFallbackConsumer service test suite", function () {
    let whatsappNotificationFallbackConsumerObj, options, whatsappNotificationFallbackConfig;
    before(function () {
        STARTUP_MOCK.init(function(error, mock_options){
            options = mock_options;
            whatsappNotificationFallbackConfig = mock_options.config.WHATSAPP_NOTIFICATION_FALLBACK;
            whatsappNotificationFallbackConsumerObj = new WhatsappNotificationFallBackConsumer(options);
            done();
        });
    });

    let tinyUrlStub;
  
    beforeEach(() => {
        // Create a stub for the createShortUrl method
        const createShortUrlStub = sinon.stub().callsFake((callback, config, url) => {
        callback(null, 'http://tiny.url/abc123');
        });

        // Create a stub for the TinyUrl class constructor
        tinyUrlStub = sinon.stub(utility, 'TinyUrl').returns({
        createShortUrl: createShortUrlStub
        });
    });

    afterEach(() => {
        // Restore the stub after each test
        tinyUrlStub?.restore();
        sinon.restore();
    });

    it('_processBillsData | SUCCESS CASE dwhLogFlow is true', function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "templateName": "test_template",
            "type": "whatsapp",
            "event": "sent",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }

        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'preProcessNotificationFallbackData').resolves();
        let stub2 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isRuPayload').resolves();
        let stub3 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isEventWhitelisted').resolves();
        let stub4 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isDwhLogFlow').returns(true);
        let stub5 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'invokeDwhSyncFlow').resolves();
        let stub6 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'invokeSMSFlow').resolves();
        let stub7 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'publishLatencies').resolves();

        whatsappNotificationFallbackConsumerObj._processBillsData(notificationFallbackData, function(err, result){
            expect(err).to.be.null;
            expect(result).to.be.undefined;
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            done();
        });
    })

    it('_processBillsData | SUCCESS CASE dwhLogFlow is false', function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "templateName": "test_template",
            "type": "whatsapp",
            "event": "sent",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }

        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'preProcessNotificationFallbackData').resolves();
        let stub2 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isRuPayload').resolves();
        let stub3 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isEventWhitelisted').resolves();
        let stub4 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isDwhLogFlow').returns(false);
        let stub5 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'invokeDwhSyncFlow').resolves();
        let stub6 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'invokeSMSFlow').resolves();
        let stub7 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'publishLatencies').resolves();

        whatsappNotificationFallbackConsumerObj._processBillsData(notificationFallbackData, function(err, result){
            expect(err).to.be.null;
            expect(result).to.be.undefined;
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(1);
            expect(stub7).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            done();
        });
    })

    it('_processBillsData | FAILED CASE isRuPayload is not true', function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "templateName": "test_template",
            "type": "whatsapp",
            "event": "sent",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }

        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'preProcessNotificationFallbackData').resolves();
        let stub2 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isRuPayload').throws(new Error('isRuPayload failed'));
        let stub3 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isEventWhitelisted').resolves();
        let stub4 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'isDwhLogFlow').returns(false);
        let stub5 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'invokeDwhSyncFlow').resolves();
        let stub6 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'invokeSMSFlow').resolves();
        let stub7 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'publishLatencies').resolves();

        whatsappNotificationFallbackConsumerObj._processBillsData(notificationFallbackData, function(err, result){
            expect(err).to.be.null;
            expect(result).to.be.undefined;
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);
            expect(stub7).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            stub7.restore();
            done();
        });
    })

    it('preProcessNotificationFallbackData | SUCCESS CASE', function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "templateName": "test_template",
            "type": "whatsapp",
            "event": "sent",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": [{
                "code": "1234567890",
                "message": "test_message",
            }],
        }
        try{
            whatsappNotificationFallbackConsumerObj.preProcessNotificationFallbackData(notificationFallbackData);
            expect(notificationFallbackData.timestamp).to.be.equal(1740747756000);
            expect(notificationFallbackData.errors).to.be.equal(JSON.stringify([{
                "code": "1234567890",
                "message": "test_message",
            }]));
            done();
        }catch(err){
            done(err);
        }
    })

    it("invokeSMSFlow | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }

        whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer = {
            validateFallbackPayload: sinon.stub().returns([
                notificationFallbackData,
                "test_template",
                "1234567890",
            ]),
            updateNotification: sinon.stub().callsFake(function(callback){
                callback();
            })
        };

        let stub3 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'processFallBackPayload').resolves();

        whatsappNotificationFallbackConsumerObj.invokeSMSFlow(notificationFallbackData)
            .then(() => {
                expect(whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer.validateFallbackPayload).to.have.been.calledOnce;
                expect(whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer.updateNotification).to.have.been.calledOnce;
                expect(stub3).to.have.been.calledOnce;
                stub3.restore();
                done();
            })
            .catch(err => {
                done();
            });
    });

    it("invokeSMSFlow | FAILED CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }

        whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer = {
            validateFallbackPayload: sinon.stub().resolves(null),
            updateNotification: sinon.stub().callsFake(function(callback){
                callback();
            })
        };

        let stub3 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'processFallBackPayload').resolves();

        whatsappNotificationFallbackConsumerObj.invokeSMSFlow(notificationFallbackData)
            .then(() => {
                expect(whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer.validateFallbackPayload).to.have.been.calledOnce;
                expect(whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer.updateNotification).to.have.callCount(0);
                expect(stub3).to.have.callCount(0);
                done();
            })
            .catch(err => {
                done();
            })
    });

    it("publishLatencies | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }

        whatsappNotificationFallbackConsumerObj.smsParsingLagDashboard = {
            publishWhatsappNotificationFallbackLatencies: sinon.stub().resolves(),
        }

        whatsappNotificationFallbackConsumerObj.publishLatencies(notificationFallbackData, 'test_source')
        .then(() => {
            expect(whatsappNotificationFallbackConsumerObj.smsParsingLagDashboard.publishWhatsappNotificationFallbackLatencies).to.have.been.calledOnce;
            done();
        })
        .catch(err => {
            done();
        })
    })

    it("isRuPayload | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        let isRuPayload = whatsappNotificationFallbackConsumerObj.isRuPayload(notificationFallbackData);
        expect(isRuPayload).to.be.equal(true);
        done();
    })

    it("isRuPayload | FAILED CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        try{
            let isRuPayload = whatsappNotificationFallbackConsumerObj.isRuPayload(notificationFallbackData);
            expect(isRuPayload).to.be.equal(false);
            done();
        }catch(err){
            expect(err.message).to.be.equal('whatsappNotificationFallbackConsumer::isRuPayload Invalid template');
            done();
        }
    })

    it("handleDwhProducerResponse | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        whatsappNotificationFallbackConsumerObj.handleDwhProducerResponse(null, notificationFallbackData);
        done();
    })

    it("handleDwhProducerResponse | FAILED CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        whatsappNotificationFallbackConsumerObj.handleDwhProducerResponse(new Error('test_error'), notificationFallbackData);
        done();
    })

    it("invokeDwhSyncFlow | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        whatsappNotificationFallbackConsumerObj.dwhProducer = {
            publishData: sinon.stub().callsFake(function(notificationFallbackData,callback){
                callback();
            })
        }
        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'getDwhPayload').returns(notificationFallbackData);
        whatsappNotificationFallbackConsumerObj.invokeDwhSyncFlow(notificationFallbackData)
        .then(() => {
            expect(stub1).to.have.callCount(1);
            expect(whatsappNotificationFallbackConsumerObj.dwhProducer.publishData).to.have.been.calledOnce;
            stub1.restore();
            done();
        })
        .catch(assertionError => {
            console.log("promise rejected");
            done(assertionError);
        })
    })

    it("invokeDwhSyncFlow | FAILED CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        whatsappNotificationFallbackConsumerObj.dwhProducer = {
            publishData: sinon.stub().callsFake(function(notificationFallbackData,callback){
                callback(new Error('test_error'));
            })
        }
        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'getDwhPayload').returns(notificationFallbackData);
        whatsappNotificationFallbackConsumerObj.invokeDwhSyncFlow(notificationFallbackData)
        .then(() => {
            expect(stub1).to.have.callCount(1);
            expect(whatsappNotificationFallbackConsumerObj.dwhProducer.publishData).to.have.been.calledOnce;
            stub1.restore();
            done();
        })
        .catch(assertionError => {
            done(assertionError);
        })
    })

    it("getDwhPayload | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        let dwhPayload = whatsappNotificationFallbackConsumerObj.getDwhPayload(notificationFallbackData);
        expect(dwhPayload.created_at).to.be.equal(MOMENT().format("YYYY-MM-DD HH:mm:ss"));
        expect(dwhPayload.updated_at).to.be.equal(MOMENT().format("YYYY-MM-DD HH:mm:ss"));
        done();
    })

    it("isEventWhitelisted | SUCCESS CASE all errors are accepted", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": [{
                "code": "1234567890",
                "message": "test_message",
            }],
        }
        let isEventWhitelisted = whatsappNotificationFallbackConsumerObj.isEventWhitelisted(notificationFallbackData);
        expect(isEventWhitelisted).to.be.equal(true);
        done();
    })

    it("isEventWhitelisted | SUCCESS CASE only sent event is accepted", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "event": "sent",
            "errors": null,
        }
        let isEventWhitelisted = whatsappNotificationFallbackConsumerObj.isEventWhitelisted(notificationFallbackData);
        expect(isEventWhitelisted).to.be.equal(true);
        done();
    })

    it("isEventWhitelisted | FAILED CASE only delievered event is not accepted", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "event": "delivered",
            "errors": null,
        }
        try{
            let isEventWhitelisted = whatsappNotificationFallbackConsumerObj.isEventWhitelisted(notificationFallbackData);
            console.log("isEventWhitelisted", isEventWhitelisted);
            expect(isEventWhitelisted).to.be.equal(false);
            done();
        }catch(error){
            expect(error.message).to.be.equal('whatsappNotificationFallbackConsumer::isEventWhitelisted Invalid event delivered');
            done();
        }
    })
    
    it("isDwhLogFlow | FAILED CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": [{"code": "1234567890", "message": "test_message"}],
        }
        let isDwhLogFlow = whatsappNotificationFallbackConsumerObj.isDwhLogFlow(notificationFallbackData);
        expect(isDwhLogFlow).to.be.equal(false);
        done();
    })

    it("isDwhLogFlow | SUCCESS CASE", function(done){
        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
            "event": "clicked",
        }
        let isDwhLogFlow = whatsappNotificationFallbackConsumerObj.isDwhLogFlow(notificationFallbackData);
        expect(isDwhLogFlow).to.be.equal(true);
        done();
    })

    it("createTinyUrl | SUCCESS CASE", function(done){
        let dynamicPart = "test_dynamic_part";
        whatsappNotificationFallbackConsumerObj.createTinyUrl(dynamicPart)
        .then((tinyUrl) => {
            expect(tinyUrl).to.be.equal("http://tiny.url/abc123");
            done();
        })
        .catch(assertionError => {
            done(assertionError);
        })
    })

    it("createTinyUrl | FAILED CASE dynamic part is not provided", function(done){
        let dynamicPart = null;
        whatsappNotificationFallbackConsumerObj.createTinyUrl(dynamicPart)
        .then((tinyUrl) => {
            done();
        })
        .catch(error => {
            expect(error.message).to.be.equal('Static or dynamic part of url is null');
            done();
        })
    })

    it("processFallBackPayload | SUCCESS CASE", function(done){
        let notificationRecord = {
            "data": JSON.stringify({
                "dynamicParams": {},
                "notificationReceiver": {
                },
            }),
        }
        let templateName = "test_template";

        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer = {
            createAndPublishNotification: sinon.stub().resolves(),
        }
        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'createTinyUrl').resolves("http://tiny.url/abc123");
        whatsappNotificationFallbackConsumerObj.processFallBackPayload(notificationRecord, templateName, notificationFallbackData)
        .then(() => {
            console.log("notificationRecord", notificationRecord);
            expect(stub1).to.have.callCount(1);
            expect(whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer.createAndPublishNotification).to.have.been.calledOnce;
            stub1.restore();
            done();
        })
        .catch(assertionError => {
            done(assertionError);
        })
    })

    it("processFallBackPayload | FAILED CASE", function(done){
        let notificationRecord = {
            "data": JSON.stringify({
                "dynamicParams": {},
                "notificationReceiver": {
                    
                },
            }),
        }
        let templateName = "test_template";

        let notificationFallbackData = {
            "jobId": "1234567890",
            "templateId": "1234567890",
            "timestamp": 1740747756,
            "phoneNumber": "y2kcCMu6zDaLdsGXrwbFPg==",
            "errors": null,
        }
        whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer = {
            createAndPublishNotification: sinon.stub().resolves(),
        }
        let stub1 = sinon.stub(whatsappNotificationFallbackConsumerObj, 'createTinyUrl').rejects(new Error('test_error'));
        whatsappNotificationFallbackConsumerObj.processFallBackPayload(notificationRecord, templateName, notificationFallbackData)
        .then(() => {
            done();
        })
        .catch(error => {
            expect(error.message).to.be.equal('test_error');
            expect(stub1).to.have.callCount(1);
            expect(whatsappNotificationFallbackConsumerObj.notificationFallbackConsumer.createAndPublishNotification).to.have.callCount(0);
            stub1.restore();
            done();
        })
    })
})
