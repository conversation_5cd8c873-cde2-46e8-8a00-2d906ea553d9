'use strict';

import { describe, it, before} from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import STARTUP_MOCK from '../__mocks__/startUp'

import HistoricalRecords from '../../services/historicalRecords';
import _ from 'lodash';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe('Storing historical records service test suite', function () {
    let historicalRecordsInstance,
        options;
    const maxwellPayload = {
        "database":"digital_reminder",
        "table":"bills_airtel",
        "type":"insert",
        "ts":1604053927,
        "xid":7844,
        "commit":true,
        "data": {
            "id":11,
            "customer_id":1000742538,
            "recharge_number":1234567,
            "product_id":765467,
            "operator":"lic",
            "amount":320,
            "bill_date":"2020-03-10 13:48:47",
            "due_date":"2020-08-05 00:00:00",
            "bill_fetch_date":"2020-08-02 00:00:00",
            "next_bill_fetch_date":"2020-07-06 13:22:46",
            "gateway":"lic",
            "paytype":"abc",
            "service":"xyz",
            "circle":null,
            "customer_mobile":"12345678",
            "customer_email":"<EMAIL>",
            "retry_count":0,
            "status":4,
            "reason":null,
            "extra":null,
            "published_date":null,
            "created_at":"2020-10-30 10:32:07",
            "updated_at":"2020-10-30 10:32:07",
            "notification_status":1,
            "payment_date":null,
            "service_id":0,
            "customerOtherInfo":null,
            "is_automatic":1,
            "reference_id": null
        }
    };
    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            historicalRecordsInstance = new HistoricalRecords(options);
            done();
        });
    });
    
    // it('configureKafka | Configuration of Kafka publisher and subscriber', (done) => {
    //     console.log("\n\n\n\n----->>> Rajan");
    //     historicalRecordsInstance.configureKafka((error) => {
    //         console.log("\n\n\n\n----->>> Rajan 5555");

    //         expect(error).to.be.equal(null);
    //         done();
    //     });
    // });
    /*
    it('convertKafkaPayloadToRecord | To throw error on empty payload', (done) => {
        const kafkaPayload = {};
        expect(() =>
            historicalRecordsInstance.convertKafkaPayloadToRecord(kafkaPayload)
        ).to.throw(Error);
        done();
    });

    it('convertKafkaPayloadToRecord | To throw error if no tableName', (done) => {
        const payload = JSON.parse(JSON.stringify(maxwellPayload));
        payload.table = '';
        const kafkaPayload = { value: JSON.stringify(payload) };
        expect(() =>
            historicalRecordsInstance.convertKafkaPayloadToRecord(kafkaPayload)
        ).to.throw(Error);
        done();
    });

    it('convertKafkaPayloadToRecord | To throw error if tableName doesn\'t exist in the operatorTableRegistry', (done) => {
        const payload = JSON.parse(JSON.stringify(maxwellPayload));
        payload.table = 'bills_airtelprepaid';
        const kafkaPayload = { value: JSON.stringify(payload) };
        expect(() =>
            historicalRecordsInstance.convertKafkaPayloadToRecord(kafkaPayload)
        ).to.throw(Error);
        done();
    });

    it('convertKafkaPayloadToRecord | To return proper result', (done) => {
        const kafkaPayload = { value: JSON.stringify(maxwellPayload) };
        const result = historicalRecordsInstance.convertKafkaPayloadToRecord(kafkaPayload);
        expect(result).to.be.an('object');
        expect(result).to.have.property('customer_id').to.be.an('number');
        expect(result).to.have.property('recharge_number');
        expect(result).to.have.property('product_id');
        expect(result).to.have.property('operator');
        expect(result).to.have.property('amount');
        expect(result).to.have.property('due_date');
        expect(result).to.have.property('bill_date');
        expect(result).to.have.property('bill_fetch_date');
        expect(result).to.have.property('next_bill_fetch_date');
        expect(result).to.have.property('gateway');
        expect(result).to.have.property('paytype');
        expect(result).to.have.property('service');
        expect(result).to.have.property('circle');
        expect(result).to.have.property('customer_mobile');
        expect(result).to.have.property('customer_email');
        expect(result).to.have.property('retry_count');
        expect(result).to.have.property('status');
        expect(result).to.have.property('reason');
        expect(result).to.have.property('extra');
        expect(result).to.have.property('published_date');
        expect(result).to.have.property('user_data');
        expect(result).to.have.property('notification_status');
        expect(result).to.have.property('payment_date');
        expect(result).to.have.property('service_id');
        expect(result).to.have.property('customerOtherInfo');
        expect(result).to.have.property('is_automatic');
        expect(result).to.have.property('payment_channel');
        expect(result).to.have.property('reference_id');
        done();
    });
    */
});
