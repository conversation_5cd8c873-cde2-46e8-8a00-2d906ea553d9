/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before} from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import _ from 'lodash';
    import sinon from 'sinon';
    
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    import SERVICE from '../../services'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;

    describe("Kafka consumer validations", () => {
        let serviceObj;
        before((done) => {
            STARTUP_MOCK.init((error, options) => {
                serviceObj = new SERVICE.updateRecentsConsumer(options)
                done();
            })
        });

        it("testing initialize consumer | default values", () => {
            let cb = sinon.spy();
            serviceObj._initializeKafkaConsumer(cb);
            expect(cb).to.have.callCount(1);
            expect(cb).to.have.calledWith(null)
            
        })

        it("testing initialize consumer | fail at infraUtils create consumer", () => {
            let cb = sinon.spy();
            let stub = sinon.stub(serviceObj.infraUtils.kafka, 'consumer').callsFake('fake Error');
            serviceObj._initializeKafkaConsumer(cb);
            stub.restore();
            expect(cb).to.have.callCount(1);
            expect(cb).to.not.have.calledWith(null)
        })

        it("start service || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(serviceObj, '_initializeKafkaConsumer').yields(null);
            serviceObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        })

        it("processKafkaData || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj._processKafkaData(record);
            expect(processedRecords).to.be.equal(undefined)
        })

        it("processKafkaData || ensure JSON records are validated", () => {
            let record = [
                { 
                    value: "testing wrong JSON"
                }
            ]
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: () => {
                    return;
                },
                _resumeConsumer: () => {

                }
            };
            let processStub = sinon.stub(serviceObj, '_processRecentsData').yields(null)
            serviceObj._processKafkaData(record);
            processStub.restore();
            expect(processStub).to.not.have.been.called;
        })

        it("processKafkaData || ensure normal flow works", () => {
            let record = [
                { 
                    value: '{"order_id":1.3832694924E10,"paytype":"postpaid","paytype_label":"recharge","price":94}'
                }
            ]

            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: () => {
                    return;
                },
                _resumeConsumer: () => {
                    return;
                }
            };

            let processStub = sinon.stub(serviceObj, '_processRecentsData').yields(null)
            serviceObj._processKafkaData(record);
            processStub.restore();
            expect(processStub).to.have.been.calledWith(JSON.parse(record[0].value));
        })

        it("processKafkaData || ensure normal flow works for multiple records", () => {
            let record = [
                { 
                    value: '{"order_id":1.3832694924E10,"paytype":"postpaid","paytype_label":"recharge","price":94}'
                },
                {
                    value: '{"order_id":1.3832694924E10,"paytype":"postpaid","paytype_label":"recharge","price":94}'
                }
            ]

            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: () => {
                    return;
                },
                _resumeConsumer: () => {
                    return;
                }
            };

            let processStub = sinon.stub(serviceObj, '_processRecentsData').yields(null)
            serviceObj._processKafkaData(record);
            processStub.restore();
            expect(processStub).to.have.callCount(2);
        })
    })

    describe("Module: updateRecentsConsumer service test suite", function () {
        let serviceObj;
        let samplePayload = `{"_id":{"_data":"8260C05B16000000D429295A10046C57BB5FC7E14CC487B777145376E6EF46645F6964006460C05B169BE8DB3D82CD4D6F0004"},
        "operationType":"insert","clusterTime":{"$timestamp":{"t":1623218966,"i":212}},"fullDocument":{"_id":{"$oid":"60c05b169be8db3d82cd4d6f"},"customer_id":21814244,"operator":"jaipur vidyut vitran nigam ltd. (jvvnl)",
        "recharge_number":"8837531516","bills":[],"circle":"punjab","circle_label":"punjab","configuration":{"recharge_number":"8837531516","price":94,"price_new":94},"created_at":{"$date":1623218934974},"info":"{}",
        "label":null,"ope_logo_url":"https://assetscdn1.paytm.com/images/catalog/operators/1555325713574.png","operatorData":{},"operatorRecentData":{},"order_id":1.3832694924E10,"paytype":"postpaid",
        "paytype_label":"recharge","price":94,"product":{"category_id":17,"brand":"BSNL","ope_logo_url":"https://assetscdn1.paytm.com/images/catalog/operators/1555325713574.png","schedulable":0,"attributes":{}},
        "product_id":53,"service":"mobile","status":1,"updated_at":{"$date":1623218934974}},"ns":{"db":"inUsers","coll":"users"},"documentKey":{"_id":{"$oid":"60c05b169be8db3d82cd4d6f"}}}`
        
        let creditcardPayload = `{"_id":{"_data":"8260C05B16000000D429295A10046C57BB5FC7E14CC487B777145376E6EF46645F6964006460C05B169BE8DB3D82CD4D6F0004"},
        "operationType":"insert","clusterTime":{"$timestamp":{"t":1623218966,"i":212}},"fullDocument":{"_id":{"$oid":"60c05b169be8db3d82cd4d6f"},"customer_id":21814244,"operator":"jaipur vidyut vitran nigam ltd. (jvvnl)",
        "recharge_number":"8837531516","bills":[],"circle":"punjab","circle_label":"punjab","configuration":{"recharge_number":"8837531516","price":94,"price_new":94},"created_at":{"$date":1623218934974},"info":"{}",
        "label":null,"ope_logo_url":"https://assetscdn1.paytm.com/images/catalog/operators/1555325713574.png","operatorData":{},"operatorRecentData":{"creditCardId":1234},"order_id":1.3832694924E10,"paytype":"credit card",
        "paytype_label":"recharge","price":94,"product":{"category_id":17,"brand":"BSNL","ope_logo_url":"https://assetscdn1.paytm.com/images/catalog/operators/1555325713574.png","schedulable":0,"attributes":{}},
        "product_id":53,"service":"mobile","status":1,"updated_at":{"$date":1623218934974}},"ns":{"db":"inUsers","coll":"users"},"documentKey":{"_id":{"$oid":"60c05b169be8db3d82cd4d6f"}}}`
    
        let recordFromBillTable = 
        [
            {
                id: 6247875,
                customer_id: 21814244,
                recharge_number: '8837531516',
                product_id: 173500476,
                operator: 'jaipur vidyut vitran nigam ltd. (jvvnl)',
                amount: 23,
                bill_date: `2021-02-10T00:00:00.000Z`,
                due_date: `2021-04-22T00:00:00.000Z`,
                bill_fetch_date: `2021-04-17T06:31:24.000Z`,
                next_bill_fetch_date: `2021-05-22T00:00:00.000Z`,
                gateway: 'bharatbillpay',
                paytype: 'postpaid',
                service: 'electricity',
                circle: '',
                customer_mobile: '7891031015',
                customer_email: '',
                payment_channel: 'ANDROIDAPP 8.16.0',
                retry_count: 0,
                status: 11,
                reason: 'NULL',
                extra: 'NULL',
                published_date: `2021-04-17T06:31:23.000Z`,
                created_at: `2021-01-01T05:08:48.000Z`,
                updated_at: `2021-06-16T23:58:17.000Z`,
                user_data: '{}',
                notification_status: 1,
                payment_date: `2021-06-09T11:42:39.000Z`,
                service_id: 0,
                customerOtherInfo: '{"customerId":220858197,"lastCC":"10","currentBillAmount":2016,"currentMinBillAmount":200,"billDate":"2021-05-15 00:00:00","billDueDate":"2021-06-04 00:00:00","smsSenderID":"VM-SBICRD","billConsumeTimestamp":"2021-05-18 07:39:59","debugKey":"smsSenderID:VM-SBICRD_custId:220858197_lastCC:10_Id:4_MCN:4611 19XX XXXX 7210_operator:visa_sbi"}',
                is_automatic: 1,
                dl_last_updated: '2021-04-29',
                reference_id: '1234'
            }
        ]

        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.updateRecentsConsumer(options);
                done();
            });
        });

        it("updateRecentsConsumer::_prepareDataToInsert | Test transaction", (done) => {
            serviceObj._processRecentsData(samplePayload,done);
        });

        it("updateRecentsConsumer::_convertKafkaPayloadTorecord | Correct payload", () => {
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(JSON.parse(samplePayload));
            expect(recentsPayload).to.have.all.keys('customerID', 'rechargeNumber', 'operator', 'paytype', 'timestamp', 'amount', 'service', 'referenceId', 'panUniqueReference', 'tin', 'tokenisedCreditCard');
            expect(recentsPayload.referenceId).to.be.equal(null)
            expect(recentsPayload.customerID).to.be.equal(21814244)
            expect(recentsPayload.rechargeNumber).to.be.equal('8837531516')
            expect(recentsPayload.operator).to.be.equal('jaipur vidyut vitran nigam ltd. (jvvnl)')
            expect(recentsPayload.timestamp).to.be.equal('2021-06-09 11:38:54')
            expect(recentsPayload.paytype).to.be.equal('postpaid')
            expect(recentsPayload.amount).to.be.equal(94)
            expect(recentsPayload.service).to.be.equal('mobile')
        })

        it("updateRecentsConsumer::_convertKafkaPayloadTorecord | Correct credit card payload", () => {
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(JSON.parse(creditcardPayload));
            expect(recentsPayload).to.have.all.keys('customerID', 'rechargeNumber', 'operator', 'paytype', 'timestamp', 'amount', 'service', 'referenceId', 'panUniqueReference', 'tin', 'tokenisedCreditCard');
            expect(recentsPayload.referenceId).to.be.equal(1234)
            expect(recentsPayload.customerID).to.be.equal(21814244)
            expect(recentsPayload.rechargeNumber).to.be.equal('8837531516')
            expect(recentsPayload.operator).to.be.equal('jaipur vidyut vitran nigam ltd. (jvvnl)')
            expect(recentsPayload.timestamp).to.be.equal('2021-06-09 11:38:54')
            expect(recentsPayload.paytype).to.be.equal('credit card')
            expect(recentsPayload.amount).to.be.equal(94)
            expect(recentsPayload.service).to.be.equal('mobile')
        })

        it("updateRecentsConsumer::_convertKafkaPayloadTorecord | not an insert payload", () => {
            let incorrectPayload = JSON.parse(creditcardPayload);
            _.set(incorrectPayload, 'operationType', 'update');
            
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            expect(recentsPayload).to.be.equal(undefined)
        })

        it("updateRecentsConsumer::_validateRecentsData | correct payload", () => {
            let incorrectPayload = JSON.parse(creditcardPayload);
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);
            expect(validationResponse).to.be.equal(undefined)
        })

        it("updateRecentsConsumer::_validateRecentsData | missing recharge number", () => {
            let incorrectPayload = JSON.parse(creditcardPayload);
            _.set(incorrectPayload, 'fullDocument.recharge_number', '');

            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);
            expect(validationResponse).to.be.equal('Mandatory fields not present:: rech_num:::operator:jaipur vidyut vitran nigam ltd. (jvvnl)::custId:21814244 Missing params:: rechargeNumber')
        })

        it("updateRecentsConsumer::_validateRecentsData | missing customer ID", () => {
            let incorrectPayload = JSON.parse(samplePayload);
            _.set(incorrectPayload, 'fullDocument.customer_id', '');

            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);
            expect(validationResponse).to.be.equal('Mandatory fields not present:: rech_num:8837531516::operator:jaipur vidyut vitran nigam ltd. (jvvnl)::custId: Missing params:: customerID')
        })

        it("updateRecentsConsumer::_validateRecentsData | missing service", () => {
            let incorrectPayload = JSON.parse(samplePayload);
            _.set(incorrectPayload, 'fullDocument.service', '');

            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);
            expect(validationResponse).to.be.equal('Mandatory fields not present:: rech_num:8837531516::operator:jaipur vidyut vitran nigam ltd. (jvvnl)::custId:21814244 Missing params:: service')
        })

        it("updateRecentsConsumer::_validateRecentsData | missing paytype", () => {
            let incorrectPayload = JSON.parse(samplePayload);
            _.set(incorrectPayload, 'fullDocument.paytype', '');

            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);
            expect(validationResponse).to.be.equal('Mandatory fields not present:: rech_num:8837531516::operator:jaipur vidyut vitran nigam ltd. (jvvnl)::custId:21814244 Missing params:: paytype')
        })

        it("updateRecentsConsumer::_validateRecentsData | missing referenceID", () => {
            let incorrectPayload = JSON.parse(creditcardPayload);
            _.set(incorrectPayload, 'fullDocument.operatorRecentData.creditCardId', '');
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);

            expect(validationResponse).to.be.equal('Mandatory fields not present:: rech_num:8837531516::operator:jaipur vidyut vitran nigam ltd. (jvvnl)::custId:21814244 Missing params:: referenceId')
        })

        it("updateRecentsConsumer::_validateRecentsData | unapproved paytype", () => {
            let incorrectPayload = JSON.parse(creditcardPayload);
            _.set(incorrectPayload, 'fullDocument.paytype', 'prepaid');
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(incorrectPayload);
            let validationResponse = serviceObj._validateRecentsData(recentsPayload);

            expect(validationResponse).to.be.equal('Not an approved paytype')
        })

        it("updateRecentsConsumer::validateRecordsToUpdate | 0 amount in bills table", () => {
            let payload = JSON.parse(creditcardPayload);
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(payload);

            let zeroAmountRecord = _.cloneDeep(recordFromBillTable);
            _.set(zeroAmountRecord[0], 'amount', 0)

            let record = serviceObj.validateRecordsToUpdate(zeroAmountRecord, recentsPayload);
            expect(record).to.be.equal(undefined);
        })

        it("updateRecentsConsumer::validateRecordsToUpdate | stale date in bills table", () => {
            let payload = JSON.parse(creditcardPayload);
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(payload);

            let staleDateRecord = _.cloneDeep(recordFromBillTable);
            let oldDate = MOMENT('2019-12-12').format('YYYY-MM-DD HH:mm:ss')

            _.set(staleDateRecord[0], 'payment_date', oldDate)

            let record = serviceObj.validateRecordsToUpdate(staleDateRecord, recentsPayload);
            expect(record).to.be.equal(undefined);
        })

        it("updateRecentsConsumer::validateRecordsToUpdate | stale date but within buffer", () => {
            let payload = JSON.parse(creditcardPayload);
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(payload);
            let staleDateRecord = _.cloneDeep(recordFromBillTable);
            let oldDate = MOMENT.utc('2021-06-09T11:35:39.000Z').format('YYYY-MM-DD HH:mm:ss')

            _.set(staleDateRecord[0], 'payment_date', oldDate)

            let record = serviceObj.validateRecordsToUpdate(staleDateRecord, recentsPayload);
            expect(Object.keys(record[0]).length).to.be.equal(15);
        });

        it("updateRecentsConsumer::validateRecordsToUpdate | test for 2 records API calls", (done) => {
            let payload = JSON.parse(creditcardPayload);
            let update = sinon.stub(serviceObj.recentsLayer, 'update').callsFake(() => "mocking update call")

            let cb = sinon.spy();
            let recentsPayload = serviceObj.convertKafkaPayloadToRecord(payload);
            let staleDateRecord = _.cloneDeep(recordFromBillTable);
            let oldDate = MOMENT.utc('2021-06-09T11:35:39.000Z').format('YYYY-MM-DD HH:mm:ss')

            _.set(staleDateRecord[0], 'payment_date', oldDate)

            let record = serviceObj.validateRecordsToUpdate(staleDateRecord, recentsPayload);
            record.push(record[0]) // two elements, should call update twice
            serviceObj._updateRecents(cb, record);
            update.restore();
            
            expect(cb).to.have.callCount(1);
            expect(update).to.have.callCount(2);
            return done();
        });
    });