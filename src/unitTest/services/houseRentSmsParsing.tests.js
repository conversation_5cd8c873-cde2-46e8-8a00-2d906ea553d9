/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import utility from '../../lib/index'

import STARTUP_MOCK from '../__mocks__/startUp'
import houseRentSmsParsing from '../../services/smsParsingBillPayment/houseRentSmsParsing';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: Rent SMS Parsing suite :: validation record", function () {
  let serviceObj;

  before(function () {
    STARTUP_MOCK.init(function (error, options) {
      serviceObj = new houseRentSmsParsing(options);
      serviceObj.config = {
        'DYNAMIC_CONFIG': {
          'RENT_SMS_PARSING': {
            'all': {
              'PRODUCT_ID': '1234512'
            }
          }
        },
        'CVR_DATA': {
            '1234512': {
                'operator': 'biller_rent_mobile_houserent'
            }
        }
      };
      done();
    });
  });
  it("executeStrategy | Empty SMS record", () => {

    let stub1 = sinon.stub(serviceObj, "processRecord").returns(null);
    let record = null;
    serviceObj.executeStrategy((error) => {
      if (error) {
        expect(error).to.be.equal(null);
      }
    }, record);
    expect(stub1).to.have.callCount(0);
    stub1.restore();
  });

  it("executeStrategy |  valid SMS record", () => {
    let record = { "appCount": 1, "appVersion": "9.6.2", "netWorkType": "WIFI", "latitude": 0, "deviceDateTime": *************, "collector_timestamp": *************, "wakeUpTimeInterval": null, "osVersion": "28", "osType": "android", "model": "TA-1021", "msg_id": "82a066b1-ffe0-4b79-bc5e-3896b69c64ee", "brand": "Nokia", "user_agent": null, "cId": "237316907", "longitude": 0, "timestamp": null, "uploadFrequency": null, "clientId": "androidapp", "deviceId": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "mId": "", "eventType": "smsEvent", "uploadTime": *************, "true_client_ip": null, "realTime": null, "db_name": null, "newUser": null, "event_name": "sms", "batteryPercentage": 100, "smsUUID": "499a6de3-9f6b-4295-aab1-814d647ce0f4", "smsDateTime": *************, "smsBody": "Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India", "smsSenderID": "AX-UNIONB", "smsReceiver": "**********", "smsOperator": "Airtel", "smsRecSubId": 0, "lastCC": "8009", "amount": 4000, "competitor": "phonepe", "dueDate": "2023-11-10", "level_2_category": "1", "predicted_category": "rent" };
    let stub1 = sinon.stub(serviceObj, "processRecord").returns();
    let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
    serviceObj.executeStrategy((error) => {
      if (error) {
        expect(error).to.be.equal(null);
      }
    }, record);
    expect(stub1).to.have.callCount(1);
    expect(stub2).to.have.callCount(0);
    stub1.restore();
    stub2.restore();
  });

  it("processRecord |  validateAndProcessRecord", () => {
    let record = { "appCount": 1, "appVersion": "9.6.2", "netWorkType": "WIFI", "latitude": 0, "deviceDateTime": *************, "collector_timestamp": *************, "wakeUpTimeInterval": null, "osVersion": "28", "osType": "android", "model": "TA-1021", "msg_id": "82a066b1-ffe0-4b79-bc5e-3896b69c64ee", "brand": "Nokia", "user_agent": null, "cId": "237316907", "longitude": 0, "timestamp": null, "uploadFrequency": null, "clientId": "androidapp", "deviceId": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "mId": "", "eventType": "smsEvent", "uploadTime": *************, "true_client_ip": null, "realTime": null, "db_name": null, "newUser": null, "event_name": "sms", "batteryPercentage": 100, "smsUUID": "499a6de3-9f6b-4295-aab1-814d647ce0f4", "smsDateTime": *************, "smsBody": "Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India", "smsSenderID": "AX-UNIONB", "smsReceiver": "**********", "smsOperator": "Airtel", "smsRecSubId": 0, "lastCC": "8009", "amount": 4000, "competitor": "phonepe", "dueDate": "2023-11-10", "level_2_category": "1", "predicted_category": "rent" };
    serviceObj.validateAndProcessRecord((errorResponse, processedRecord) => {
      expect(errorResponse).to.be.equal(null);
      expect(processedRecord).to.not.equal(null);
      expect(processedRecord.dueDate).to.not.be.null;
      expect(processedRecord.amount).to.not.be.null;
    }, record);
  });
});

describe("Module: Rent SMS Parsing suite :: Record processing", function () {
  let serviceObj;

  let data, record;
  before(function () {
    STARTUP_MOCK.init(function (error, options) {
      serviceObj = new houseRentSmsParsing(options);
      serviceObj.config = {
        'DYNAMIC_CONFIG': {
          'RENT_SMS_PARSING': {
            'all': {
              'PRODUCT_ID': '1234512'
            }
          }
        },
        'CVR_DATA': {

        }
      };

      done();
    });
  });

  it("processRecords  | update Flow", (done) => {
    let record = { "appCount": 1, "appVersion": "9.6.2", "netWorkType": "WIFI", "latitude": 0, "deviceDateTime": *************, "collector_timestamp": *************, "wakeUpTimeInterval": null, "osVersion": "28", "osType": "android", "model": "TA-1021", "msg_id": "82a066b1-ffe0-4b79-bc5e-3896b69c64ee", "brand": "Nokia", "user_agent": null, "cId": "237316907", "longitude": 0, "timestamp": null, "uploadFrequency": null, "clientId": "androidapp", "deviceId": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "mId": "", "eventType": "smsEvent", "uploadTime": *************, "true_client_ip": null, "realTime": null, "db_name": null, "newUser": null, "event_name": "sms", "batteryPercentage": 100, "smsUUID": "499a6de3-9f6b-4295-aab1-814d647ce0f4", "smsDateTime": *************, "smsBody": "Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India", "smsSenderID": "AX-UNIONB", "smsReceiver": "**********", "smsOperator": "Airtel", "smsRecSubId": 0, "lastCC": "8009", "amount": 4000, "competitor": "phonepe", "dueDate": "2023-11-10", "level_2_category": "1", "predicted_category": "rent" };
    let processedRecord = {
      "operator": "biller_rent_mobile_houserent",
      "service": 'rent',
      "customerId": 1235,
      "rechargeNumber": "**********",
      "gateway": null,
      "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
      "billDate": MOMENT().endOf('day').format('YYYY-MM-DD'),
      "dueDate": "2023-09-29",
      "amount": record.amount,
      "status": 4,
      "paytype": 'postpaid',
      "cache": null,
      "service_id": 0,
      "customerMobile": "**********",
      "customerEmail": null,
      'paymentChannel': null,
      "retryCount": 0,
      "reason": null,
      "extra": "{\"competitor\":\"phonepe\",\"level_2_category\":\"1\",\"eventState\":\"bill_gen\",\"billSource\":\"sms_parsed\"}",
      "customer_type": null,
      "paymentDate": null,
      "user_data": null,
      "circle": null,
      "productId": "12312"
    };

    let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
      return done(null, processedRecord);
    });

    let stub2 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
      return done(null, 'update', processedRecord);
    });

    let stub4 = sinon.stub(serviceObj.smsParsingLagDashboard, 'publishDelaysMetrics').callsFake(function fakeFn(done) {
      return done(null, '');
    });

    serviceObj.processRecord(record, function (error) {
      expect(error).to.be.equal(undefined);
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);
      expect(stub4).to.have.callCount(1);

      stub1.restore();
      stub2.restore();
      stub4.restore();
      return done();
    });
  });


  it("processRecords  | error in validate function", (done) => {

    let record = { "appCount": 1, "appVersion": "9.6.2", "netWorkType": "WIFI", "latitude": 0, "deviceDateTime": *************, "collector_timestamp": *************, "wakeUpTimeInterval": null, "osVersion": "28", "osType": "android", "model": "TA-1021", "msg_id": "82a066b1-ffe0-4b79-bc5e-3896b69c64ee", "brand": "Nokia", "user_agent": null, "cId": "237316907", "longitude": 0, "timestamp": null, "uploadFrequency": null, "clientId": "androidapp", "deviceId": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "mId": "", "eventType": "smsEvent", "uploadTime": *************, "true_client_ip": null, "realTime": null, "db_name": null, "newUser": null, "event_name": "sms", "batteryPercentage": 100, "smsUUID": "499a6de3-9f6b-4295-aab1-814d647ce0f4", "smsDateTime": *************, "smsBody": "Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India", "smsSenderID": "AX-UNIONB", "smsReceiver": "**********", "smsOperator": "Airtel", "smsRecSubId": 0, "lastCC": "8009", "amount": 4000, "competitor": "phonepe", "dueDate": "2023-11-10", "level_2_category": "1", "predicted_category": "rent" };
    let processedRecord = {
      "operator": "biller_rent_mobile_houserent",
      "service": 'rent',
      "customerId": 1235,
      "rechargeNumber": "**********",
      "gateway": null,
      "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
      "billDate": MOMENT().endOf('day').format('YYYY-MM-DD'),
      "dueDate": "2023-09-29",
      "amount": record.amount,
      "status": 4,
      "paytype": 'postpaid',
      "cache": null,
      "service_id": 0,
      "customerMobile": "**********",
      "customerEmail": null,
      'paymentChannel': null,
      "retryCount": 0,
      "reason": null,
      "extra": "{\"competitor\":\"phonepe\",\"level_2_category\":\"1\",\"eventState\":\"bill_gen\",\"billSource\":\"sms_parsed\"}",
      "customer_type": null,
      "paymentDate": null,
      "user_data": null,
      "circle": null,
      "productId": "12312"
    };


    let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
      return done("error in validateAndProcessedRecord", processedRecord);
    });

    let stub2 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
      return done(null, 'update', processedRecord);
    });

    serviceObj.processRecord(record, function (error) {
      expect(error).to.be.equal(undefined);
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(0);

      stub1.restore();
      stub2.restore();
      return done();
    });
  });

  it("processRecords  | error in update cassandra function", (done) => {

    let record = { "appCount": 1, "appVersion": "9.6.2", "netWorkType": "WIFI", "latitude": 0, "deviceDateTime": *************, "collector_timestamp": *************, "wakeUpTimeInterval": null, "osVersion": "28", "osType": "android", "model": "TA-1021", "msg_id": "82a066b1-ffe0-4b79-bc5e-3896b69c64ee", "brand": "Nokia", "user_agent": null, "cId": "237316907", "longitude": 0, "timestamp": null, "uploadFrequency": null, "clientId": "androidapp", "deviceId": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "mId": "", "eventType": "smsEvent", "uploadTime": *************, "true_client_ip": null, "realTime": null, "db_name": null, "newUser": null, "event_name": "sms", "batteryPercentage": 100, "smsUUID": "499a6de3-9f6b-4295-aab1-814d647ce0f4", "smsDateTime": *************, "smsBody": "Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India", "smsSenderID": "AX-UNIONB", "smsReceiver": "**********", "smsOperator": "Airtel", "smsRecSubId": 0, "lastCC": "8009", "amount": 4000, "competitor": "phonepe", "dueDate": "2023-11-10", "level_2_category": "1", "predicted_category": "rent" };
    let processedRecord = {
      "operator": "biller_rent_mobile_houserent",
      "service": 'rent',
      "customerId": 1235,
      "rechargeNumber": "**********",
      "gateway": null,
      "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
      "billDate": MOMENT().endOf('day').format('YYYY-MM-DD'),
      "dueDate": "2023-09-29",
      "amount": record.amount,
      "status": 4,
      "paytype": 'postpaid',
      "cache": null,
      "service_id": 0,
      "customerMobile": "**********",
      "customerEmail": null,
      'paymentChannel': null,
      "retryCount": 0,
      "reason": null,
      "extra": "{\"competitor\":\"phonepe\",\"level_2_category\":\"1\",\"eventState\":\"bill_gen\",\"billSource\":\"sms_parsed\"}",
      "customer_type": null,
      "paymentDate": null,
      "user_data": null,
      "circle": null,
      "productId": "12312"
    };


    let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
      return done(null, processedRecord);
    });

    let stub2 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
      return done("Error in updateCassandra");
    });

    serviceObj.processRecord(record, function (error) {
      expect(error).to.be.equal(undefined);
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);

      stub1.restore();
      stub2.restore();

      return done();
    });
  });

  it("processRecords  | error in publishInKafka function", (done) => {

    let record = { "appCount": 1, "appVersion": "9.6.2", "netWorkType": "WIFI", "latitude": 0, "deviceDateTime": *************, "collector_timestamp": *************, "wakeUpTimeInterval": null, "osVersion": "28", "osType": "android", "model": "TA-1021", "msg_id": "82a066b1-ffe0-4b79-bc5e-3896b69c64ee", "brand": "Nokia", "user_agent": null, "cId": "237316907", "longitude": 0, "timestamp": null, "uploadFrequency": null, "clientId": "androidapp", "deviceId": null, "preference": [{ "prefCat": "permission", "prefKeys": "ocl.permission.creditcard.sms_read_consent", "prefSubCat": "sms consent" }], "mId": "", "eventType": "smsEvent", "uploadTime": *************, "true_client_ip": null, "realTime": null, "db_name": null, "newUser": null, "event_name": "sms", "batteryPercentage": 100, "smsUUID": "499a6de3-9f6b-4295-aab1-814d647ce0f4", "smsDateTime": *************, "smsBody": "Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India", "smsSenderID": "AX-UNIONB", "smsReceiver": "**********", "smsOperator": "Airtel", "smsRecSubId": 0, "lastCC": "8009", "amount": 4000, "competitor": "phonepe", "dueDate": "2023-11-10", "level_2_category": "1", "predicted_category": "rent" };
    let processedRecord = {
      "operator": "biller_rent_mobile_houserent",
      "service": 'rent',
      "customerId": 1235,
      "rechargeNumber": "**********",
      "gateway": null,
      "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
      "billDate": MOMENT().endOf('day').format('YYYY-MM-DD'),
      "dueDate": "2023-09-29",
      "amount": record.amount,
      "status": 4,
      "paytype": 'postpaid',
      "cache": null,
      "service_id": 0,
      "customerMobile": "**********",
      "customerEmail": null,
      'paymentChannel': null,
      "retryCount": 0,
      "reason": null,
      "extra": "{\"competitor\":\"phonepe\",\"level_2_category\":\"1\",\"eventState\":\"bill_gen\",\"billSource\":\"sms_parsed\"}",
      "customer_type": null,
      "paymentDate": null,
      "user_data": null,
      "circle": null,
      "productId": "12312"
    };

    let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
      return done(null, processedRecord);
    });

    let stub2 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
      return done(null);
    });

    serviceObj.processRecord(record, function (error) {
      expect(error).to.be.equal(undefined);
      expect(stub1).to.have.callCount(1);
      expect(stub2).to.have.callCount(1);

      stub1.restore();
      stub2.restore();
      return done();
    });
  });

});
