'use strict';
import { describe, it, before, afterEach, beforeEach} from 'mocha';
import sinon from 'sinon';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _, { subtract } from 'lodash';
import STARTUP_MOCK from '../__mocks__/startUp'
import NotificationFallBackConsumer from '../../services/notificationFallbackConsumer';
import nock from 'nock';
import assert from 'assert';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: notificationFallbackConsumer service test suite", function () {
    let fallbackConsumerObj, options, notificationConfig;
    before(function () {
        STARTUP_MOCK.init(function(error, mock_options){
            options = mock_options;
            notificationConfig = mock_options.config.NOTIFICATION;
            fallbackConsumerObj = new NotificationFallBackConsumer(options);
            done();
        });
    });

    it("should test method - createNotification | when notification is migrated to cassandra", (done) => {
        let record = {
            "recharge_number":"8248715328",
            "product_id":194,
            "send_at": "2024-05-02 19:30:00"
        }
        let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
        stub1.callsFake(function (sourceId, customerId) {
            return true;
        });
        let stub2 = sinon.stub(fallbackConsumerObj.notify, 'createNotificationInCassandra');
        stub2.callsFake(function (cb, notifier) {
            _.set(notifier, 'status', 6);
            return cb(null);
        });

        fallbackConsumerObj.createNotification((err, data, status) => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(err).to.be.equal(null);
            expect(status).to.be.equal(6);
            expect(record.retry_count).to.be.equal(0);
            expect(record.job_id).to.be.equal(null);
            expect(record.sent_at).to.be.equal(null);
            expect(record.error_msg).to.be.equal(null);
            stub1.restore();
            stub2.restore();
            done();
        }, record);
    });

    it("should test method - createNotification | when notification is NOT migrated to cassandra", (done) => {
        let record = {
            "recharge_number":"8248715328",
            "product_id":194,
            "send_at": "2024-05-02 19:30:00"
        }
        let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
        stub1.callsFake(function (sourceId, customerId) {
            return false;
        });
        let stub2 = sinon.stub(fallbackConsumerObj.notification, 'createNotification');
        stub2.callsFake(function (cb, params) {
            return cb(null, null, 1);
        });

        fallbackConsumerObj.createNotification((err, data, status) => {
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(err).to.be.equal(null);
            expect(status).to.be.equal(1);
            stub1.restore();
            stub2.restore();
            done();
        }, record);
    });

    describe("test method getNotificationsByJobId", function () {
        it("should test when notification record exists corresponding to job id", async () => {
            let notificationRecordKey = [{
                "recharge_number":"8248715328",
                "recipient":"1472461250",
                "type":"PUSH",
                "template_id":"27274",
                "source_id":"26",
                "category_id":1,
                "product_id":194,
                "send_at": "2024-05-02 19:30:00"
            }]
            let notificationRecord = {
                "rows": [{
                    "recharge_number":"8248715328",
                    "recipient":"1472461250",
                    "type":"PUSH",
                    "template_id":"27274",
                    "source_id":"26",
                    "category_id":1,
                    "product_id":194,
                    "send_at": "2024-05-02 19:30:00",
                    "sent_at": "2024-05-02 20:00:00",
                    "retry_count": 0,
                    "max_retry_count": 3,
                    "job_id": "xyz"
                }]
            }
            let jobId = "xyz";
            let stub1 = sinon.stub(fallbackConsumerObj.cassandraBills, 'getJobIdNotificationMappingRecord');
            stub1.callsFake(function (jobId, jobIdNotificationMappingTable) {
                return Promise.resolve(notificationRecordKey);
            });
            let stub2 = sinon.stub(fallbackConsumerObj.cassandraBills, 'getNotifications');
            stub2.callsFake(function (cb, tableName, whereCondition, queryParams) {
                return cb(null, notificationRecord);
            });

            let recordFetched = await fallbackConsumerObj.getNotificationsByJobId(jobId);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(recordFetched[0].recharge_number).to.be.equal("8248715328");
            expect(recordFetched[0].job_id).to.be.equal("xyz");
            expect(recordFetched[0].max_retry_count).to.be.equal(3);
            expect(recordFetched[0].recipient).to.be.equal("1472461250");
            stub1.restore();
            stub2.restore();
        });

        it("should test when notification record doesn't exist corresponding to job id", async () => {
            let notificationRecord = {
                "rows": [{
                    "recharge_number":"8248715328",
                    "recipient":"1472461250",
                    "type":"PUSH",
                    "template_id":"27274",
                    "source_id":"26",
                    "category_id":1,
                    "product_id":194,
                    "send_at": "2024-05-02 19:30:00",
                    "sent_at": "2024-05-02 20:00:00",
                    "retry_count": 0,
                    "max_retry_count": 3,
                    "job_id": "xyz"
                }]
            }
            let jobId = "xyz";
            let stub1 = sinon.stub(fallbackConsumerObj.cassandraBills, 'getJobIdNotificationMappingRecord');
            stub1.callsFake(function (jobId, jobIdNotificationMappingTable) {
                return Promise.resolve(null);
            });
            let stub2 = sinon.stub(fallbackConsumerObj.cassandraBills, 'getNotifications');
            stub2.callsFake(function (cb, tableName, whereCondition, queryParams) {
                return cb(null, notificationRecord);
            });

            let recordFetched = await fallbackConsumerObj.getNotificationsByJobId(jobId);
            expect(stub1).to.have.callCount(2);
            expect(stub2).to.have.callCount(0);
            expect(recordFetched).to.be.equal(null);
            stub1.restore();
            stub2.restore();
        });
    });

    describe("test method updateNotification", function () {
        it("should test when notification migrated to cassandra, status=RETRY_FALLBACK and mysql entry doesn't exist", (done) => {
            let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
            stub1.callsFake(function (sourceId, customerId) {
                return true;
            });
            let stub2 = sinon.stub(fallbackConsumerObj.cassandraBills, 'deleteNotification');
            stub2.callsFake(function (cb, whereCondition, params, sendAt) {
                let result = {
                    "rows": [{
                        "[applied]": true
                    }]
                }
                return cb(null, result);
            });
            let stub3 = sinon.stub(fallbackConsumerObj.notify, 'createNotificationInCassandra');
            stub3.callsFake(function (cb, record) {
                return cb(null);
            })
            let stub4 = sinon.stub(fallbackConsumerObj.notification, 'getNotificationsByJobId');
            stub4.callsFake(function (jobId, batchsize) {
                return Promise.resolve(null);
            });
            let stub5 = sinon.stub(fallbackConsumerObj.notification, 'createNotificationForRetry');
            stub5.callsFake(function (cb, record) {
                return cb(null, null, 0);
            });

            let record = {
                "recharge_number":"8248715328",
                "recipient":"1472461250",
                "type":"PUSH",
                "template_id":"27274",
                "source_id":"26",
                "category_id":1,
                "product_id":194,
                "send_at": "2024-05-02 19:30:00",
                "sent_at": "2024-05-02 20:00:00",
                "retry_count": 0,
                "max_retry_count": 3,
                "job_id": "xyz"
            }
            let fields = ['retry_count', 'status', 'send_at'];
            let params = [1, 9, "2024-05-02 20:00:00"];
            fallbackConsumerObj.updateNotification(() => {
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                expect(stub5).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                done();
            }, fields, params, record, 9);
        });

        it("should test when notification migrated to cassandra, status=RETRY_FALLBACK and mysql entry exists", (done) => {
            let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
            stub1.callsFake(function (sourceId, customerId) {
                return true;
            });
            let stub2 = sinon.stub(fallbackConsumerObj.cassandraBills, 'deleteNotification');
            stub2.callsFake(function (cb, whereCondition, params, sendAt) {
                let result = {
                    "rows": [{
                        "[applied]": true
                    }]
                }
                return cb(null, result);
            });
            let stub3 = sinon.stub(fallbackConsumerObj.notify, 'createNotificationInCassandra');
            stub3.callsFake(function (cb, record) {
                return cb(null);
            })
            let stub4 = sinon.stub(fallbackConsumerObj.notification, 'getNotificationsByJobId');
            stub4.callsFake(function (jobId, batchsize) {
                let notificationRecord = [{
                    "recharge_number":"8248715328",
                    "recipient":"1472461250",
                    "type":"PUSH",
                    "template_id":"27274",
                    "source_id":"26",
                    "category_id":1,
                    "product_id":194,
                    "send_at": "2024-05-02 19:30:00",
                    "sent_at": "2024-05-02 20:00:00",
                    "retry_count": 0,
                    "max_retry_count": 3,
                    "job_id": "xyz",
                    "id": "xyz11"    
                }]
                return Promise.resolve(notificationRecord);
            });
            let stub5 = sinon.stub(fallbackConsumerObj.notification, 'updateNotification');
            stub5.callsFake(function (cb, fields, whereCondition, params) {
                return cb(null, null);
            });

            let record = {
                "recharge_number":"8248715328",
                "recipient":"1472461250",
                "type":"PUSH",
                "template_id":"27274",
                "source_id":"26",
                "category_id":1,
                "product_id":194,
                "send_at": "2024-05-02 19:30:00",
                "sent_at": "2024-05-02 20:00:00",
                "retry_count": 0,
                "max_retry_count": 3,
                "job_id": "xyz"
            }
            let fields = ['retry_count', 'status', 'send_at'];
            let params = [1, 9, "2024-05-02 20:00:00"];
            fallbackConsumerObj.updateNotification(() => {
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                expect(stub5).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                done();
            }, fields, params, record, 9);
        });

        it("should test when notification migrated to cassandra, status=FAILED_FALLBACK and mysql entry exists", (done) => {
            let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
            stub1.callsFake(function (sourceId, customerId) {
                return true;
            });
            let stub2 = sinon.stub(fallbackConsumerObj.notify, 'updateNotificationInCassandra');
            stub2.callsFake(function (cb, fields, whereCondition, params, record){
                return cb(null);
            });
            let stub3 = sinon.stub(fallbackConsumerObj.notification, 'getNotificationsByJobId');
            stub3.callsFake(function (jobId, batchsize) {
                let notificationRecord = [{
                    "recharge_number":"8248715328",
                    "recipient":"1472461250",
                    "type":"PUSH",
                    "template_id":"27274",
                    "source_id":"26",
                    "category_id":1,
                    "product_id":194,
                    "send_at": "2024-05-02 19:30:00",
                    "sent_at": "2024-05-02 20:00:00",
                    "retry_count": 0,
                    "max_retry_count": 3,
                    "job_id": "xyz",
                    "id": "xyz11"    
                }]
                return Promise.resolve(notificationRecord);
            });
            let stub4 = sinon.stub(fallbackConsumerObj.notification, 'deleteNotification');
            stub4.callsFake(function (cb, whereCondition, params) {
                return cb(null);
            });

            let record = {
                "recharge_number":"8248715328",
                "recipient":"1472461250",
                "type":"PUSH",
                "template_id":"27274",
                "source_id":"26",
                "category_id":1,
                "product_id":194,
                "send_at": "2024-05-02 19:30:00",
                "sent_at": "2024-05-02 20:00:00",
                "retry_count": 0,
                "max_retry_count": 3,
                "job_id": "xyz"
            }
            let fields = ['retry_count', 'status', 'send_at'];
            let params = [1, 10, "2024-05-02 20:00:00"];

            fallbackConsumerObj.updateNotification(() => {
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                done();
            }, fields, params, record, 10);
        });

        it("should test when notification migrated to cassandra, status=FAILED_FALLBACK and mysql entry doesn't exist", (done) => {
            let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
            stub1.callsFake(function (sourceId, customerId) {
                return true;
            });
            let stub2 = sinon.stub(fallbackConsumerObj.notify, 'updateNotificationInCassandra');
            stub2.callsFake(function (cb, fields, whereCondition, params, record){
                return cb(null);
            });
            let stub3 = sinon.stub(fallbackConsumerObj.notification, 'getNotificationsByJobId');
            stub3.callsFake(function (jobId, batchsize) {
                return Promise.resolve(null);
            });
            let stub4 = sinon.stub(fallbackConsumerObj.notification, 'deleteNotification');
            stub4.callsFake(function (cb, whereCondition, params) {
                return cb(null);
            });

            let record = {
                "recharge_number":"8248715328",
                "recipient":"1472461250",
                "type":"PUSH",
                "template_id":"27274",
                "source_id":"26",
                "category_id":1,
                "product_id":194,
                "send_at": "2024-05-02 19:30:00",
                "sent_at": "2024-05-02 20:00:00",
                "retry_count": 0,
                "max_retry_count": 3,
                "job_id": "xyz"
            }
            let fields = ['retry_count', 'status', 'send_at'];
            let params = [1, 10, "2024-05-02 20:00:00"];

            fallbackConsumerObj.updateNotification(() => {
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                expect(stub4).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                done();
            }, fields, params, record, 10);
        });

        it("should test when notification not migrated to cassandra", (done) => {
            let stub1 = sinon.stub(fallbackConsumerObj.notify, 'isNotificationMigratedToCassandra');
            stub1.callsFake(function (sourceId, customerId) {
                return false;
            });

            let stub2 = sinon.stub(fallbackConsumerObj.notification, 'updateNotification');
            stub2.callsFake(function (cb, fields, whereCondition, params) {
                return cb(null, null);
            });

            let record = {
                "recharge_number":"8248715328",
                "recipient":"1472461250",
                "type":"PUSH",
                "template_id":"27274",
                "source_id":"26",
                "category_id":1,
                "product_id":194,
                "send_at": "2024-05-02 19:30:00",
                "sent_at": "2024-05-02 20:00:00",
                "retry_count": 0,
                "max_retry_count": 3,
                "job_id": "xyz",
                "id": "xyz11"    
            }

            let fields = ['retry_count', 'status', 'send_at'];
            let params = [1, 10, "2024-05-02 20:00:00"];

            fallbackConsumerObj.updateNotification(() => {
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                stub1.restore();
                done();
            }, fields, params, record, 10);
        })
    })
})



