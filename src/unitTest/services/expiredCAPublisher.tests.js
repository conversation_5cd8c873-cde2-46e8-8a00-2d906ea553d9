'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert } from "chai";
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import ExpiredCAPublisher from '../../services/expiredCAPublisher';
import L from 'lgr'
import config from '../../config'
import _ from 'lodash';
import helper from '../__mocks__';
import sinon from 'sinon';
import chaiAsPromised from "chai-as-promised";
import STARTUP_MOCK from '../__mocks__/startUp'
import KafkaConsumer from '../../lib/KafkaConsumer';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;


describe('ExpiredCAPublisher :: initializeVariable', () => {
    let expiredCAPublisher;
    let loggerStub;
    let configStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            error: sinon.stub(),
            verbose: sinon.stub()
        };

        configStub = {
            DYNAMIC_CONFIG: {
                GREYSCALE_CONFIG: {
                    RECENTS: {
                        BATCHSIZE: 5
                    },
                    NON_PAYTM_BILLS: {
                        BATCH_DELAY: 1000
                    }
                },
                SMART_FETCH_CONFIG: {
                    ACTIVE_PAYTM_USER: {
                        ALLOWED_SERVICES: ['electricity', 'water'],
                        SERVICE_TO_SERVICE_MAPPING: { 'electricity': 'electricity' },
                        SERVICE_WISE_THRESHOLD_TRANSACTIONS: { electricity: 3 },
                        SERVICE_WISE_THRESHOLD_DAYS: { electricity: 90 },
                        SERVICE_WISE_QUERY_LIMIT: 200
                    }
                },
                NON_PAYTM_CONFIG: {
                    SMART_FETCH_CONFIG: {
                        SERVICES: ['electricity'],
                        OPERATORS: ['OPERATOR1']
                    }
                }
            }
        };
    });

    it('should initialize variables with greyscale environment', () => {
        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: configStub,
            greyScaleEnv: true
        });

        expiredCAPublisher.initializeVariable();

        expect(expiredCAPublisher.kafkaBatchSize).to.equal(5);
        expect(expiredCAPublisher.kafkaResumeTimeout).to.equal(1000);
        expect(expiredCAPublisher.allowedServicesForSmartFetch).to.deep.equal(['electricity', 'water']);
        expect(expiredCAPublisher.serviceDependencyMapping).to.deep.equal({ 'electricity': 'electricity' });
        expect(expiredCAPublisher.smartFetchServiceConfigForNonRU).to.deep.equal(['electricity']);
        expect(expiredCAPublisher.smartFetchOperatorConfigForNonRU).to.deep.equal(['OPERATOR1']);
        expect(loggerStub.log.calledWith(
            'ExpiredCAPublisher :: initializeVariable : Re-initializing variable after interval'
        )).to.be.true;
    });

    it('should initialize with default values when config is empty', () => {
        const emptyConfig = { DYNAMIC_CONFIG: {} };
        
        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: emptyConfig,
            greyScaleEnv: false
        });

        expiredCAPublisher.initializeVariable();

        expect(expiredCAPublisher.kafkaBatchSize).to.equal(500);
        expect(expiredCAPublisher.kafkaResumeTimeout).to.equal(500);
        expect(expiredCAPublisher.allowedServicesForSmartFetch).to.deep.equal(['electricity']);
        expect(expiredCAPublisher.serviceDependencyMapping).to.deep.equal({});
        expect(expiredCAPublisher.serviceWiseNumberOfTransactions).to.deep.equal({});
        expect(expiredCAPublisher.serviceWiseNumberOfDays).to.deep.equal({});
        expect(expiredCAPublisher.smartFetchServiceConfigForNonRU).to.deep.equal([]);
        expect(expiredCAPublisher.smartFetchOperatorConfigForNonRU).to.deep.equal([]);
        expect(expiredCAPublisher.serviceWiseQueryLimit).to.equal(100);
    });
});

describe('ExpiredCAPublisher :: start', () => {
    let expiredCAPublisher;
    let loggerStub;
    let producerStub;
    let consumerStub;
    let utilityStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            error: sinon.stub(),
            verbose: sinon.stub()
        };

        utilityStub = {
            _sendMetricsToDD: sinon.stub()
        };

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {
                KAFKA: {
                    TOPICS: {
                        NON_PAYTM_RECORDS: {
                            HOSTS: 'kafka:9092'
                        }
                    }
                }
            },
            INFRAUTILS: {
                kafka: {
                    producer: sinon.stub().returns({
                        initProducer: sinon.stub()
                    })
                }
            }
        });

        expiredCAPublisher.utility = utilityStub;

        producerStub = sinon.stub(expiredCAPublisher, '_startProducer');
        consumerStub = sinon.stub(expiredCAPublisher, '_startConsumer');
    });

    afterEach(() => {
        // Restore all stubs
        if (producerStub && producerStub.restore) producerStub.restore();
        if (consumerStub && consumerStub.restore) consumerStub.restore();
        if (utilityStub && utilityStub._sendMetricsToDD.restore) utilityStub._sendMetricsToDD.restore();
        
        // Clean up sinon stubs
        sinon.restore();
    });

    it('should successfully start producer and consumer', (done) => {
        producerStub.yields(null); 
        consumerStub.yields(null); 

        expiredCAPublisher.start();

        // Assert
        setTimeout(() => {
            expect(producerStub).to.have.been.calledOnce;
            
            expect(consumerStub).to.have.been.calledOnce;
            expect(consumerStub).to.have.been.calledAfter(producerStub);
            
            expect(loggerStub.log).to.have.been.calledWith(
                "ExpiredCAPublisher :: start : Start Configuration: ExpiredCAPublisher is starting........"
            );
            
            expect(utilityStub._sendMetricsToDD).to.not.have.been.called;
            
            done();
        }, 0);
    });

    it('should handle producer startup failure', (done) => {
        const producerError = new Error('Producer failed to start');
        producerStub.yields(producerError);

        expiredCAPublisher.start();

        // Assert
        setTimeout(() => {
            expect(producerStub).to.have.been.calledOnce;
            expect(consumerStub).to.not.have.been.called;
            expect(loggerStub.error).to.have.been.calledWith(
                "ExpiredCAPublisher :: start : Failed to initialize expired ca publihser service."
            );
            
            done();
        }, 0);
    });

    it('should handle consumer startup failure', (done) => {
        producerStub.yields(null); 
        const consumerError = new Error('Consumer failed to start');
        consumerStub.yields(consumerError);

        expiredCAPublisher.start();

        // Assert
        setTimeout(() => {
            expect(producerStub).to.have.been.calledOnce;
            expect(consumerStub).to.have.been.calledOnce;
            expect(loggerStub.error).to.have.been.calledWith(
                "ExpiredCAPublisher :: start : Failed to initialize expired ca publihser service."
            );
            
            done();
        }, 0);
    });
});

describe('ExpiredCAPublisher :: _startProducer', () => {
    let expiredCAPublisher;
    let loggerStub;
    let utilityStub;
    let producerStub;
    let initProducerStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            critical: sinon.stub(),
            verbose: sinon.stub()
        };

        utilityStub = {
            _sendMetricsToDD: sinon.stub()
        };

        initProducerStub = sinon.stub();
        producerStub = sinon.stub().returns({
            initProducer: initProducerStub
        });

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {
                KAFKA: {
                    TOPICS: {
                        NON_PAYTM_RECORDS: {
                            HOSTS: 'kafka:9092'
                        }
                    }
                }
            },
            INFRAUTILS: {
                kafka: {
                    producer: producerStub
                }
            }
        });

        expiredCAPublisher.utility = utilityStub;
    });

    afterEach(() => {
        if (producerStub && producerStub.restore) producerStub.restore();
        if (initProducerStub && initProducerStub.restore) initProducerStub.restore();
        if (utilityStub && utilityStub._sendMetricsToDD.restore) utilityStub._sendMetricsToDD.restore();
        
        sinon.restore();
    });

    it('should successfully initialize kafka producer', (done) => {
        // Arrange
        initProducerStub.yields(null);

        // Act
        expiredCAPublisher._startProducer((error) => {
            // Assert
            expect(error).to.be.null;
            expect(producerStub).to.have.been.calledOnce;
            expect(producerStub).to.have.been.calledWith({
                kafkaHost: 'kafka:9092'
            });
            expect(initProducerStub).to.have.been.calledOnce;
            expect(initProducerStub).to.have.been.calledWith('high');
            expect(loggerStub.log).to.have.been.calledWith(
                "ExpiredCAPublisher :: _startProducer : expiredCAPublisher KAFKA PRODUCER STARTED...."
            );
            expect(utilityStub._sendMetricsToDD).to.not.have.been.called;
            done();
        });
    });

    it('should handle error during kafka producer initialization', (done) => {
        // Arrange
        const initError = new Error('Initialization error');
        initProducerStub.yields(initError);

        // Act
        expiredCAPublisher._startProducer((error) => {
            // Assert
            expect(error).to.equal(initError);
            expect(producerStub).to.have.been.calledOnce;
            expect(producerStub).to.have.been.calledWith({
                kafkaHost: 'kafka:9092'
            });
            expect(loggerStub.critical).to.have.been.calledWith(
                'ExpiredCAPublisher :: _startProducer : error in initialising expiredCAPublisher Producer :: ', initError
            );
            done();
        });
    });

});


describe('ExpiredCAPublisher :: _processKafkaData', () => {
    let expiredCAPublisher;
    let loggerStub;
    let utilityStub;
    let kafkaConsumerChecksStub;
    let processBillsDataStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            error: sinon.stub(),
            critical: sinon.stub(),
            verbose: sinon.stub()
        };

        utilityStub = {
            _sendMetricsToDD: sinon.stub()
        };

        kafkaConsumerChecksStub = {
            findOffsetDuplicates: sinon.stub()
        };

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {
                KAFKA: {
                    TOPICS: {
                        NON_PAYTM_RECORDS: {
                            HOSTS: 'kafka:9092'
                        }
                    }
                }
            }
        });

        expiredCAPublisher.utility = utilityStub;
        expiredCAPublisher.kafkaConsumerChecks = kafkaConsumerChecksStub;
        processBillsDataStub = sinon.stub(expiredCAPublisher, '_processBillsData');
        expiredCAPublisher.kafkaResumeTimeout = 0;
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should process valid kafka records successfully', (done) => {
        // Arrange
        const records = [
            {
                value: JSON.stringify({
                    customer_id: '123',
                    service: 'electricity',
                    operator: 'test-op'
                }),
                topic: 'test-topic',
                partition: 0,
                offset: 100,
                timestamp: Date.now()
            }
        ];
        const resolveOffset = sinon.stub().resolves();
        processBillsDataStub.yields(null);

        // Act
        expiredCAPublisher._processKafkaData(records, resolveOffset, 'test-topic', 0, (error) => {
            try {
                // Assert
                expect(processBillsDataStub).to.have.been.calledOnce;
                expect(kafkaConsumerChecksStub.findOffsetDuplicates).to.have.been.calledOnce;
                expect(resolveOffset).to.have.been.calledWith(100);
                expect(loggerStub.log).to.have.been.called;
                expect(error).to.be.undefined;
                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('should handle invalid JSON in kafka records', (done) => {
        // Arrange
        const records = [
            {
                value: 'invalid-json',
                topic: 'test-topic',
                partition: 0,
                offset: 100,
                timestamp: Date.now()
            }
        ];
        const resolveOffset = sinon.stub().resolves();

        // Act
        expiredCAPublisher._processKafkaData(records, resolveOffset, 'test-topic', 0, (error) => {
            try {
                // Assert
                expect(processBillsDataStub).to.not.have.been.called;
                
                const metricsCall = utilityStub._sendMetricsToDD.getCall(0);
                expect(error).to.be.undefined;
                done();
            } catch (err) {
                done(err);
            }
        });
    });
});

describe('ExpiredCAPublisher :: _processBillsData', () => {
    let expiredCAPublisher;
    let loggerStub;
    let utilityStub;
    let cassandraBillsStub;
    let updateKafkaPayloadStub;
    let checkAndGetServicePaymentDatesStub;
    let preparePayloadStub;
    // let nonruKafkaPublisherStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            error: sinon.stub(),
            critical: sinon.stub(),
            verbose: sinon.stub()
        };

        utilityStub = {
            _sendMetricsToDD: sinon.stub()
        };

        cassandraBillsStub = {
            readActivePaytmUsersNewByCId: sinon.stub(),
            readCustIdRNMappingByCustIdServices: sinon.stub()
        };

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {
                KAFKA: {
                    TOPICS: {
                        NON_PAYTM_RECORDS: {
                            HOSTS: 'kafka:9092'
                        }
                    }
                }
            }
        });
        expiredCAPublisher.utility = utilityStub;
        expiredCAPublisher.cassandraBills = cassandraBillsStub;      
    });

    afterEach(() => {
        sinon.restore();
    });
    
    it('should handle invalid kafka data', (done) => {
        // Arrange
        const billsKafkaRow = {
            customer_id: '123',
        };

        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(false);

        // Act
        expiredCAPublisher._processBillsData(billsKafkaRow, (error) => {
            try {
                // Assert
                expect(loggerStub.error).to.not.have.been.called;
                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('should handle empty service_wise_payment_dates', (done) => {
        const kafkaData = {
            customer_id: '123',
            service: 'electricity',
            operator: 'op1',
            recharge_number: '123',
            payment_date: '2024-01-01',
            is_active_expired_user: true,
            service_wise_payment_dates: {}  // Empty object
        };

        const dbData = [{
            service: 'electricity',
            payment_date_list: ['2024-01-01']
        }];

        cassandraBillsStub.readActivePaytmUsersNewByCId.resolves(dbData);
        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(true);
        cassandraBillsStub.readCustIdRNMappingByCustIdServices.resolves([]); 
        expiredCAPublisher._processBillsData(kafkaData, (error) => {
            try {
                
                expect(loggerStub.log).to.have.been.calledWith(
                    'ExpiredCAPublisher :: _processBillsData : reading cassandra again as service_wise_payment_dates is empty for customerId',
                    '123'
                );

                // Verify DB call
                expect(cassandraBillsStub.readActivePaytmUsersNewByCId).to.have.been.calledWith('123');
                expect(error).to.be.undefined;

                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('should handle DB error when reading active paytm users', (done) => {
        const kafkaData = {
            customer_id: '123',
            service: 'electricity'
        };

        const dbError = new Error('DB Error');
        cassandraBillsStub.readActivePaytmUsersNewByCId.rejects(dbError);
        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(true);
        expiredCAPublisher._processBillsData(kafkaData, (error) => {
            try {

                expect(loggerStub.critical).to.have.been.calledWith(
                    'ExpiredCAPublisher :: _processBillsData : select DB exception in reading active_paytm_users_new for ',
                    '123'
                );
                
                expect(error).to.be.undefined;
                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('should process bills data successfully with service_wise_payment_dates', (done) => {
        // Arrange
        const billsKafkaRow = {
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op',
            recharge_number: '9876543210',
            payment_date: '2024-03-20',
            is_active_expired_user: true,
            service_wise_payment_dates: {
                electricity: ['2024-03-20']
            }
        };

        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(true);
        cassandraBillsStub.readCustIdRNMappingByCustIdServices.resolves([{
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op'
        }]);

        // Act
        expiredCAPublisher._processBillsData(billsKafkaRow, (error) => {
            try {
                // Assert
                expect(error).to.be.undefined;
                expect(cassandraBillsStub.readCustIdRNMappingByCustIdServices).to.have.been.calledOnce;
                expect(cassandraBillsStub.readCustIdRNMappingByCustIdServices).to.have.been.calledWith(["123", ["electricity"], 100]);
                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('should handle DB error when reading cust_id_rn_mapping', (done) => {
        const kafkaData = {
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op',
            recharge_number: '9876543210',
            payment_date: '2024-01-01',
            is_active_expired_user: true,
            service_wise_payment_dates: {
                electricity: ['2024-01-01']  
            }
        };
        const dbError = new Error('DB Error');
        
        // Setup stubs
        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(true);
        cassandraBillsStub.readCustIdRNMappingByCustIdServices.rejects(dbError);

        expiredCAPublisher._processBillsData(kafkaData, (error) => {
            try {
                let params = ['123', ['electricity'], 100];
                // Verify DB call
                expect(cassandraBillsStub.readCustIdRNMappingByCustIdServices).to.have.been.calledWith(params);

                expect(loggerStub.critical).to.have.been.calledWith(
                    'ExpiredCAPublisher :: _processBillsData : select DB exception in reading cust_id_rn_mapping for ',
                    JSON.stringify(params),
                    dbError
                );

                expect(error).to.be.undefined;
                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('should handle when cust_id_rn_mapping data exceeds query limit', (done) => {
        const kafkaData = {
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op',
            recharge_number: '9876543210',
            payment_date: '2024-01-01',
            is_active_expired_user: true,
            service_wise_payment_dates: {
                electricity: ['2024-01-01']  
            }
        };

        // Create array of length equal to queryLimit (100)
        const queryLimit = 100;
        const dbRecords = Array(queryLimit).fill({
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op'
        });
        
        // Setup stubs
        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(true);
        cassandraBillsStub.readCustIdRNMappingByCustIdServices.resolves(dbRecords);

        expiredCAPublisher._processBillsData(kafkaData, (error) => {
            try {
                let params = ['123', ['electricity'], 100];
                
                // Verify DB call
                expect(cassandraBillsStub.readCustIdRNMappingByCustIdServices).to.have.been.calledWith(params);

                
                expect(loggerStub.log).to.have.been.calledWith(
                    'ExpiredCAPublisher :: _processBillsData : Skipping publishing as cust_id_rn_mapping data is more than',
                    queryLimit,
                    'for customer_id',
                    '123'
                );
                expect(error).to.be.undefined;
                done();
            } catch (err) {
                done(err);
            }
        });
    });


    it('should handle when cust_id_rn_mapping data is under query limit', (done) => {
        const kafkaData = {
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op',
            recharge_number: '9876543210',
            payment_date: '2024-01-01',
            is_active_expired_user: true,
            service_wise_payment_dates: {
                electricity: [
                    '2025-02-03',
                    '2025-02-08',
                    '2025-02-13'
                ]
            }
        };

        const dbRecords = [{
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op',
            status: 'DELETED'
        }];

        cassandraBillsStub.readCustIdRNMappingByCustIdServices.resolves(dbRecords);
        expiredCAPublisher.smartFetchServiceConfigForNonRU = ['electricity'];
        expiredCAPublisher.serviceWiseNumberOfDays = { electricity: 30 };
        expiredCAPublisher.serviceDependencyMapping = { 'electricity': 'electricity' };

        const publisherStub = sinon.stub();
        publisherStub.callsFake((data, publishCallback) => {
            console.log('Publisher called');
            publishCallback(null);
        });
        expiredCAPublisher.nonruKafkaPublisher = {
            publishData: publisherStub
        };
        
        expiredCAPublisher.config = {
            KAFKA: {
                SERVICES: {
                    NON_PAYTM_RECORDS: {
                        TOPIC: 'test-topic'
                    }
                }
            }
        };

        expiredCAPublisher._processBillsData(kafkaData, (error) => {
            console.log('Callback received', error);
            try {
                expect(cassandraBillsStub.readCustIdRNMappingByCustIdServices)
                    .to.have.been.calledWith(['123', ['electricity'], 100]);

                expect(loggerStub.log).to.have.been.calledWith(
                    'ExpiredCAPublisher :: _processBillsData : Found cust_id_rn_mapping data of size',
                    1,
                    'for customer_id',
                    '123'
                );
                expect(error).to.be.undefined;
                done();
            } catch (err) {
                done(err);
            }
        });
    });

    it('handle when allowedServicesForSmartFetch is empty', (done) => {
        const kafkaData = {
            customer_id: '123',
            service: 'electricity',
            operator: 'op1',
            recharge_number: '123',
            payment_date: '2024-01-01',
            is_active_expired_user: true,
            service_wise_payment_dates: {
                electricity: ['2024-03-20']
            }
        };

        expiredCAPublisher.allowedServicesForSmartFetch = [];  
        sinon.stub(expiredCAPublisher, 'validateKafkaData').returns(true);
        expiredCAPublisher._processBillsData(kafkaData, (error) => {
            try {
                
                expect(loggerStub.error).to.have.been.calledWith(
                    'ExpiredCAPublisher :: _processBillsData : No allowed services found for smart fetch!'
                );

                expect(error).to.be.undefined;

                done();
            } catch (err) {
                done(err);
            }
        });
    });

});

describe('ExpiredCAPublisher :: validateKafkaData', () => {
    let expiredCAPublisher;
    let loggerStub;
    let utilityStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            error: sinon.stub(),
            verbose: sinon.stub()
        };

        utilityStub = {
            _sendMetricsToDD: sinon.stub()
        };

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {}
        });

        expiredCAPublisher.utility = utilityStub;
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should return true for valid kafka data', () => {
        // Arrange
        const kafkaData = {
            customer_id: '123',
            service: 'electricity',
            operator: 'test-op',
            recharge_number: '9876543210',
            payment_date: '2024-03-20',
            is_active_expired_user: true
        };

        // Act
        const result = expiredCAPublisher.validateKafkaData(kafkaData);

        // Assert
        expect(result).to.be.true;
        expect(loggerStub.error).to.not.have.been.called;
        expect(utilityStub._sendMetricsToDD).to.not.have.been.called;
    });

    it('should return false for missing mandatory fields', () => {
        // Arrange
        const kafkaData = {
            customer_id: '123'
        };

        // Act
        const result = expiredCAPublisher.validateKafkaData(kafkaData);

        // Assert
        expect(result).to.be.true;
        expect(loggerStub.error).to.have.been.called;
    });
});

describe('ExpiredCAPublisher :: updateKafkaPayload', () => {
    let expiredCAPublisher;
    let loggerStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            verbose: sinon.stub()
        };

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {}
        });
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should update kafka payload with service_wise_payment_dates from active data', () => {
        // Arrange
        const kafkaData = {
            customer_id: '123',
            service: 'electricity'
        };

        const activeData = [
            {
                service: 'electricity',
                payment_date_list: ['2024-03-20', '2024-02-20']
            },
            {
                service: 'gas',
                payment_date_list: ['2024-03-15']
            }
        ];

        // Act
        expiredCAPublisher.updateKafkaPayload(kafkaData, activeData);

        // Assert
        expect(kafkaData.service_wise_payment_dates).to.deep.equal({
            electricity: ['2024-03-20', '2024-02-20'],
            gas: ['2024-03-15']
        });
    });

    it('should handle empty active data', () => {
        // Arrange
        const kafkaData = {
            customer_id: '123',
            service: 'electricity'
        };

        const activeData = [];

        // Act
        expiredCAPublisher.updateKafkaPayload(kafkaData, activeData);

        // Assert
        expect(kafkaData.service_wise_payment_dates).to.deep.equal({});
    });
});

describe('ExpiredCAPublisher :: checkAndGetServicePaymentDates', () => {
    let expiredCAPublisher;
    let loggerStub;
    let utilityStub;

    beforeEach(() => {
        loggerStub = {
            log: sinon.stub(),
            error: sinon.stub()
        };

        utilityStub = {
            _sendMetricsToDD: sinon.stub()
        };

        expiredCAPublisher = new ExpiredCAPublisher({
            L: loggerStub,
            config: {}
        });

        expiredCAPublisher.utility = utilityStub;
        expiredCAPublisher.smartFetchServiceConfigForNonRU = ['electricity'];
        expiredCAPublisher.smartFetchOperatorConfigForNonRU = ['operator1'];
        expiredCAPublisher.serviceDependencyMapping = { 'electricity': 'electricity' };
        expiredCAPublisher.serviceWiseNumberOfTransactions = { gas: 2 };
        expiredCAPublisher.serviceWiseNumberOfDays = { gas: 30 };
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should return null when status is not DELETED', () => {
        const result = expiredCAPublisher.checkAndGetServicePaymentDates(
            { status: 'ACTIVE', service: 'electricity', operator: 'operator1' },
            {}
        );
        expect(result).to.be.null;
        expect(loggerStub.log).to.have.been.calledWith(
            'ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as record status is not DELETED'
        );
    });

    it('should return null for duplicate record', () => {
        const result = expiredCAPublisher.checkAndGetServicePaymentDates(
            { status: 'DELETED', service: 'electricity', recharge_number: '123', operator: 'operator1' },
            { service: 'electricity', recharge_number: '123', operator: 'operator1' }
        );
        expect(result).to.be.null;
        expect(loggerStub.log).to.have.been.calledWith(
            'ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as record is same as payment data'
        );
    });

    it('should return null when service is not eligible', () => {
        const result = expiredCAPublisher.checkAndGetServicePaymentDates(
            { status: 'DELETED', service: 'water', recharge_number: '456', operator: 'operator3' },
            {}
        );
        expect(result).to.be.null;
        expect(loggerStub.log).to.have.been.calledWith(
            'ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as service or operator is not eligible for smart fetch'
        );
    });

    it('should return filtered payment dates when valid', () => {
        const dbRecord = {
            status: 'DELETED',
            service: 'electricity',
            recharge_number: '456',
            operator: 'operator2'
        };
        
        expiredCAPublisher.serviceWiseNumberOfDays = { electricity: 30 };

        const result = expiredCAPublisher.checkAndGetServicePaymentDates(
            dbRecord,
            {
                service_wise_payment_dates: {
                    electricity: [
                        MOMENT().subtract(15, 'days').format('YYYY-MM-DD'),
                        MOMENT().subtract(10, 'days').format('YYYY-MM-DD'),
                        MOMENT().subtract(5, 'days').format('YYYY-MM-DD')
                    ]
                }
            }
        );
        expect(result).to.deep.equal({
            electricity: [MOMENT().subtract(5, 'days').format('YYYY-MM-DD')]
        });

    });

    it('should return null when service_wise_payment_dates is empty ', () => {
        const dbRecord = {
            status: 'DELETED',
            service: 'electricity',
            recharge_number: '456',
            operator: 'operator2'
        };
        expiredCAPublisher.serviceWiseNumberOfDays = { electricity: 30 };
        const result = expiredCAPublisher.checkAndGetServicePaymentDates(
            dbRecord,
            {
                service_wise_payment_dates: {
                }
            }
        );
        expect(result).to.be.null;
        expect(loggerStub.log).to.have.been.calledWith(
            'ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as no valid configs found for checking active customer'
        );
    });
});

describe('ExpiredCAPublisher :: _startConsumer', function() {
    let expiredCAPublisher, options, kafkaConsumerStub;

    beforeEach(function() {
        options = {
            L: { log: sinon.stub(), error: sinon.stub(), critical: sinon.stub() },
            config: {
                KAFKA: {
                    TOPICS: {
                        ACTIVE_PAYTM_USERS: { HOSTS: 'localhost:9092' },
                        SERVICES: { ACTIVE_PAYTM_USERS: { TOPIC: 'active_paytm_users' } }
                    }
                },
                DYNAMIC_CONFIG: {
                    KAFKA_CONFIG: {
                        SESSION_TIMEOUT: 120000,
                        MAX_PROCESS_TIMEOUT: 180000
                    }
                }
            }
        };

        kafkaConsumerStub = sinon.createStubInstance(KafkaConsumer);
        sinon.stub(KafkaConsumer.prototype, 'initConsumer').callsFake(function(processKafkaData, callback) {
            callback(null);
        });

        expiredCAPublisher = new ExpiredCAPublisher(options);
    });

    afterEach(function() {
        sinon.restore();
    });

    it('should initialize the Kafka consumer successfully', function(done) {
        expiredCAPublisher._startConsumer((error) => {
            expect(error).to.be.null;
            expect(options.L.log.calledWith("ExpiredCAPublisher :: _startConsumer : ExpiredCAPublisher Consumer Configured")).to.be.true;
            done();
        });
    });

    it('should handle errors during Kafka consumer initialization', function(done) {
        KafkaConsumer.prototype.initConsumer.restore();
        sinon.stub(KafkaConsumer.prototype, 'initConsumer').callsFake(function(processKafkaData, callback) {
            callback(new Error('Initialization error'));
        });

        expiredCAPublisher._startConsumer((error) => {
            expect(error).to.not.be.null;
            expect(options.L.critical.calledWith("ExpiredCAPublisher :: _startConsumer : ExpiredCAPublisher Consumer cannot start, error: ", sinon.match.instanceOf(Error))).to.be.true;
            done();
        });
    });
});