/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    
    describe("Module: UpdateUserConsent Consumer suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.updateUserConsentConsumer(options);
                done();
            });
        });
    
    
        it("testing initialize consumer | default values", () => {
            let cb = sinon.spy();
            serviceObj.configureKafka(cb);
            expect(cb).to.have.callCount(1);
            expect(cb).to.have.calledWith(null)
            
        });
    
        it("start service || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(serviceObj, 'configureKafka').yields(null);
            serviceObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        });
    
        it("processKafkaData || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })

        // it("processKafkaData || ensure empty records are validated", () => {
        //     let record = [{}]
        //     let processedRecords = serviceObj.execSteps(record);
        //     expect(processedRecords).to.be.equal(undefined);
        // })
    

        // it("execSteps function | Valid Kafka record received", (done) => {
        //     let record = [{ value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": {"whatsapp": true} } } '}];
        //     serviceObj.processBatch(record, function (error){
        //         return done();
        //     });    
        // });


    });
    
    
    describe("Module: UpdateUserConsent Consumer suite :: validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SERVICE.updateUserConsentConsumer(options);
                done();
            });
        });
    
    
        it("validateRecord function | Invalid record", () => {
            let record = '';
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal('Invalid record');
        });

        it("validateRecord function | Invalid record parsing error", () => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" :  "consent": {"whatsapp": true} } } '};
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal('Kafka record Parsing Error');
            
        });

        it("validateRecord function | Missing customer_id Kafka record received", () => {
            let record = { value : '{"metaData" : { "consent": {"whatsapp": true} } } '};
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal('Missing customer_id');

            
        });

        it("validateRecord function | Invalid metaData.consent.whatsapp data:", () => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": {"whatsapp": 1} } } '};
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal('Invalid/ Missing metaData.consent.whatsapp data');            
        });

        it("validateRecord function | Missing metaData.consent.whatsapp data", () => {
            let record = { value : '{"customerInfo_customer_id": 1020305845 } '};
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal('Invalid/ Missing metaData.consent.whatsapp data');
            
        });

        it("validateRecord function | Valid Kafka record received", () => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": {"whatsapp": true} } } '};
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal(null);

            expect(validRecord.customer_id).to.be.equal(1020305845);
            expect(validRecord.preferenceValue).to.be.equal(1);
            
        });

        it("validateRecord function | Valid Kafka record received", () => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": {"whatsapp": true} } } '};
            let [error, validRecord] = serviceObj.validateRecord(record);
            expect(error).to.be.equal(null);

            expect(validRecord.customer_id).to.be.equal(1020305845);
            expect(validRecord.preferenceValue).to.be.equal(1);
            
        });


        it("processRecords function | InValid Kafka record received", (done) => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": 1 } } '};
            serviceObj.processRecords(function (error){
                return done();
            },record);    
        });

        /*it("processRecords function | Valid Kafka record received", (done) => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": {"whatsapp": true} } } '};
            serviceObj.processRecords(function (error){
                return done();
            },record);    
        });*/

        it("processBatch function | InValid Kafka record received", (done) => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": 1 } } '};
            serviceObj.processBatch(record, function (error){
                return done();
            });    
        });

        it("processBatch function | Valid Kafka record received", (done) => {
            let record = { value : '{"customerInfo_customer_id": 1020305845,"metaData" : { "consent": {"whatsapp": true} } } '};
            serviceObj.processBatch(record, function (error){
                return done();
            });    
        });
 
    });