/*
  jshint 
    esversion: 8
 */

    'use strict';
    import { describe, it, before, afterEach, beforeEach} from 'mocha';
    import sinon from 'sinon';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import _, { subtract } from 'lodash';
    import STARTUP_MOCK from '../__mocks__/startUp'
    import Notify from '../../services/notify';
    import nock from 'nock';
    import assert from 'assert';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    describe("Module: notification-send service test suite", function () {
        let notifyObj,options,notificationConfig;
        before(function () {
            STARTUP_MOCK.init(function(error, mock_options){
                options = mock_options;
                notificationConfig = mock_options.config.NOTIFICATION;
                notifyObj = new Notify(options);    
                done();
            });
        });
        it("checkNotificationInCassandra | valid payload | send_at at cassandra > last_ref_time", async () => {
            let row = {"id":628,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-22 09:52:20","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let skipNotification=false;
            let error=null;
            await notifyObj.checkNotificationInCassandra(row,cassandraKey)
            .then((result)=>{
                skipNotification=result;
            })
            .catch((err)=>{
                error=err;
            })
            expect(skipNotification).to.be.equal(true);
            expect(error).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("checkNotificationInCassandra | valid payload | send_at at cassandra < last_ref_time", async () => {
            let row = {"id":628,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-22 09:52:20","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let skipNotification=false;
            let error=null;
            await notifyObj.checkNotificationInCassandra(row,cassandraKey)
            .then((result)=>{
                skipNotification=result;
            })
            .catch((err)=>{
                error=err;
            })
            expect(skipNotification).to.be.equal(false);
            expect(error).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("checkNotificationInCassandra | valid payload | null data in cassandra", async () => {
            let row = {"id":628,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-22 09:52:20","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null);
            }); 
            let skipNotification=false;
            let error=null;
            await notifyObj.checkNotificationInCassandra(row,cassandraKey)
            .then((result)=>{
                skipNotification=result;
            })
            .catch((err)=>{
                error=err;
            })
            expect(skipNotification).to.be.equal(false);
            expect(error).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("checkNotificationInCassandra | valid payload | error from cassandra", async () => {
            let row = {"id":628,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-22 09:52:20","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback('Cassandra not working');
            }); 
            let skipNotification=false;
            let error=null;
            await notifyObj.checkNotificationInCassandra(row,cassandraKey)
            .then((result)=>{
                skipNotification=result;
            })
            .catch((err)=>{
                error=err;
            })
            expect(skipNotification).to.be.equal(false);
            expect(error).to.be.equal('Cassandra not working');
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("checkNotificationInCassandra | valid payload | invalid cassandraKey", async () => {
            let row = {"id":628,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-22 09:52:20","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback('Cassandra not working');
            }); 
            let skipNotification=false;
            let error=null;
            await notifyObj.checkNotificationInCassandra(row,cassandraKey)
            .then((result)=>{
                skipNotification=result;
            })
            .catch((err)=>{
                error=err;
            })
            expect(skipNotification).to.be.equal(false);
            expect(error).to.be.equal('cassandrakey incorrect');
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("checkPriority | valid payload | same prority", async () => {
            let notifier = {"source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null);
            }); 
            
            notifyObj.checkPriority(function(err,data){
                expect(data).to.be.equal(undefined);
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
            },notifier,cassandraKey)
        });
        it("checkPriority | valid payload | high prority in cassandra", async () => {
            let notifier = {"source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null);
            }); 
            
            notifyObj.checkPriority(function(err,data){
                // expect(data[0].send_at).to.be.equal(MOMENT().format('YYYY-MM-DD HH:mm:ss'))
                // expect(data[0].priority).to.be.equal(0)
                // expect(data[0].status).to.be.equal(1)
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
            },notifier,cassandraKey)
        });
        it("checkPriority | valid payload | same prority in cassandra", async () => {
            let notifier = {"source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().format('YYYY-MM-DD HH:mm:ss'),"priority":1,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null);
            }); 
            
            notifyObj.checkPriority(function(err,data){
                expect(data).to.be.equal(undefined)
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
            },notifier,cassandraKey)
        });
        it("checkPriority | valid payload | high prority, old send_at in cassandra", async () => {
            let notifier = {"source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null);
            }); 
            
            notifyObj.checkPriority(function(err,data){
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
            },notifier,cassandraKey)
        });
        it("checkPriority | valid payload | source id not added in config", async () => {
            let notifier = {"source_id":"24","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null, cassandraValue);
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null);
            }); 
            
            notifyObj.checkPriority(function(err,data){
                expect(data).to.be.equal(undefined)
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(0);
                expect(stub2).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
            },notifier,cassandraKey)
        });
        it("checkPriority | valid payload | error from cassandra", (done) => {
            let notifier = {"source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback('cassandra not working');
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null);
            }); 
            
            notifyObj.checkPriority(function(err,data){
                expect(data).to.be.equal(undefined)
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                done();
            },notifier,cassandraKey)
        });
        it("checkPriority | valid payload | error from cassandra", (done) => {
            let notifier = {"source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"max_retry_count":2,"retry_interval":30,"type":"PUSH","template_id":"27274","recipient":"**********","send_at":"2023-09-23 07:00:00","data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"rules":{"condition":"category_id=1 and source_id=26 and recharge_number='**********' and product_id=194 \n                                    and type='PUSH' and template_id=27274","actions":[{"status":"pending","action":"drop"},{"status":"sent","action":"drop"}]},"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null},"priority":1,"last_ref_time":"2023-09-21 20:03:42","cassandraKey":"**********_**********_194_26_1_27274_PUSH"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback('cassandra not working');
            }); 
            let stub2 = sinon.stub(notifyObj.notification, 'checkPriority').callsFake(function fakeFn(callback, notifier){
                return callback(null, {"id":1234});
            }); 
            
            notifyObj.checkPriority(function(err,data){
                expect(data.id).to.be.equal(1234)
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                done();
            },notifier,cassandraKey)
        });
        it("sendNotification | valid payload | cassandra cache found", async () => {
            let row = {"id":630,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-23 07:00:00","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************,"notify_acknowledgeTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH","last_ref_time":"2023-09-21 23:20:08"}
            let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
            let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}
            let ttl=1000;
            nock('https//notificationapi')
            .post('/') // Use the appropriate HTTP method and endpoint
            .reply(200, { result: {'jobId':1234} });

            let stub1 = sinon.stub(notifyObj, 'checkNotificationInCassandra').resolves(true);

            let stub2 = sinon.stub(notifyObj, 'paymentAlreadyDone').callsFake(function fakeFn(row, callback){
                return callback(null);
            }); 

            let stub3 = sinon.stub(notifyObj, 'processNotificationApiOpts').returns({"uri":"https//notificationapi"})

            let stub4 = sinon.stub(notifyObj, 'prepareResponse').callsFake(function fakeFn(callback){
                return callback(null, {});
            }); 

            let stub5 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
                return callback(null,cassandraValue);
            }); 

            let stub6 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function fakeFn(cassandraKey,cassandraValue, callback){
                return callback(null);
            }); 
            
            notifyObj.sendNotification(function(data){
                expect(data).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                expect(stub3).to.have.callCount(0);
                expect(stub4).to.have.callCount(0);
                expect(stub5).to.have.callCount(0);
                expect(stub6).to.have.callCount(0);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                stub6.restore();
            },row)
        });
        it("createNotificationInCassandra | test notification create in cassandra", (done) => {
            let notifier = {"recharge_number":"**********", "product_id":194};
            let createNotificationStub = sinon.stub(notifyObj.cassandraBills, 'createNotification');
            createNotificationStub.callsFake(function fakeFn(callback, record) {
                return callback(null, 0);
            })

            let publisherStub = sinon.stub(notifyObj, 'publishRecordInKafkaForDwhSync');
            publisherStub.callsFake(function (callback, record) {
                return callback(null);
            });

            notifyObj.createNotificationInCassandra((err) => {
                expect(notifier.status).to.be.equal(0);
                expect(createNotificationStub).to.have.callCount(1);
                expect(publisherStub).to.have.callCount(1);
                expect(err).to.be.equal(null);
                createNotificationStub.restore();
                publisherStub.restore();
                done();
            }, notifier);
        });
        describe("Test isNotificationMigratedToCassandra method", function() {
            it("when source id is null", () => {
                let sourceId = null;
                let customerId = 1234;
                let result = notifyObj.isNotificationMigratedToCassandra(sourceId, customerId);
                expect(result).to.be.equal(false);
            });
            it("when customer id is null", () => {
                let sourceId = 26;
                let customerId = null;
                let result = notifyObj.isNotificationMigratedToCassandra(sourceId, customerId);
                expect(result).to.be.equal(false);
            });
            it("when source is migrated to cassandra for 100% customers", () => {
                let sourceId = 26;
                let customerId = 1234;
                let result = notifyObj.isNotificationMigratedToCassandra(sourceId, customerId);
                expect(result).to.be.equal(true);
            });
            it("when source is migrated to cassandra for 1% customers and customerId is included", () => {
                let sourceId = 38;
                let customerId = 12301;
                let result = notifyObj.isNotificationMigratedToCassandra(sourceId, customerId);
                expect(result).to.be.equal(true);
            });
            it("when source is migrated to cassandra for 1% customers and customerId is not included", () => {
                let sourceId = 38;
                let customerId = 1234;
                let result = notifyObj.isNotificationMigratedToCassandra(sourceId, customerId);
                expect(result).to.be.equal(false);
            });
            it("when source is not migrated to cassandra", () => {
                let sourceId = 27;
                let customerId = 1234;
                let result = notifyObj.isNotificationMigratedToCassandra(sourceId, customerId);
                expect(result).to.be.equal(false);
            });
        });
        describe("Test notification create/send/update flows via cassandra", function() {
            it("should test method - createNotificationMysqlForRetry", (done) => {
                let record = {
                    "recharge_number": "**********",
                    "product_id": 194,
                    "data": {
                        "dynamicParams": {
                            "emoji1": "xyz1",
                            "emoji2": "xyz2"
                        }
                    }
                }
                let stub1 = sinon.stub(notifyObj.notification, 'createNotificationForRetry');
                stub1.callsFake(function (callback, record) {
                    return callback(null, null, null);
                });
                notifyObj.createNotificationMysqlForRetry((err) => {
                    expect(err).to.be.equal(null);
                    expect(stub1).to.have.callCount(1);
                    stub1.restore();
                    done();
                }, record);
            });
            it("should test method - updateNotificationInCassandra", (done) => {
                let record = {
                    "recharge_number":"**********",
                    "product_id":194,
                    "send_at": "2024-05-02 19:30:00"
                }
                let fields, whereCondition, params;
                let cassandraUpdateNotificationStub = sinon.stub(notifyObj.cassandraBills, 'updateNotification');
                cassandraUpdateNotificationStub.callsFake(function (cb, fields, whereCondition, params, sendAt) {
                    let result = {
                        "rows": [{"[applied]":true}]
                    }
                    return cb(null, result);
                });

                let publisherStub = sinon.stub(notifyObj, 'publishRecordInKafkaForDwhSync');
                publisherStub.callsFake(function (cb, record) {
                    return cb(null);
                });
                notifyObj.updateNotificationInCassandra((err) => {
                    expect(err).to.be.equal(null);
                    expect(cassandraUpdateNotificationStub).to.have.callCount(1);
                    expect(publisherStub).to.have.callCount(1);
                    cassandraUpdateNotificationStub.restore();
                    publisherStub.restore();
                    done();
                }, fields, whereCondition, params, record);
            });
            it("should test method - checkDuplicacyFromCassandraNotificationTable", (done) => {
                let record = {
                    "recharge_number":"**********",
                    "product_id":194,
                    "send_at": "2024-05-02 19:30:00"
                }
                let stub1 = sinon.stub(notifyObj.cassandraBills, 'getNotificationTableSuffix');
                stub1.callsFake(function (sendAt) {
                    return 2;
                });
                let stub2 = sinon.stub(notifyObj.cassandraBills, 'getNotifications');
                stub2.callsFake(function (cb, tableName, whereCondition, queryParams) {
                    let result = {
                        "rows": []
                    }
                    return cb(null, result);
                });
                notifyObj.checkDuplicacyFromCassandraNotificationTable((err, result) => {
                    expect(err).to.be.equal(null);
                    expect(stub1).to.have.callCount(1);
                    expect(stub2).to.have.callCount(1);
                    expect(result.length).to.be.equal(0);
                    stub1.restore();
                    stub2.restore();
                    done();
                }, record);
            });
        })

        it("getDataFromCassandraForDuplicacy | check via cassandraKey", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey
            }

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function (cassandraKey, cb) {
                return cb(null, null);
            });

            notifyObj.getDataFromCassandraForDuplicacy(record, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub1).to.have.calledWith(cassandraKey, sinon.match.func);
                stub1.restore();
                done();
            });
        });

        it("getDataFromCassandraForDuplicacy | check via notificationUniqueKey", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey,
                'source_id': '26',
            }

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function (cassandraKey, cb) {
                return cb(null, null);
            });

            notifyObj.getDataFromCassandraForDuplicacy(record, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub1).to.have.calledWith(notificationUniqueKey, sinon.match.func);
                stub1.restore();
                done();
            });
        });

        it("getDataFromCassandraForDuplicacy | check via cassandraKey set query", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey,
                'source_id': '26',
            }

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function (cassandraKey, cb) {
                return cb(null, null);
            });

            notifyObj.getDataFromCassandraForDuplicacy(record, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub1).to.have.calledWith(cassandraKey, sinon.match.func);
                stub1.restore();
                done();
            }, 'notify');
        });


        it("getDataFromCassandraForDuplicacy | check via notificationUniqueKey set query", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey,
                'source_id': '25',
            }

            let stub1 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function (cassandraKey, cb) {
                return cb(null, null);
            });

            notifyObj.getDataFromCassandraForDuplicacy(record, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub1).to.have.calledWith(notificationUniqueKey, sinon.match.func);
                stub1.restore();
                done();
            }, 'notify');
        });

        it("setDataFromCassandraForDuplicacy | save via cassandra key", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey,
                // 'source_id': '25',
            }
            let value = {
                "send_at": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                "priority": 0,
                "status": 1
            }

            let stub1 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function (cassandraKey, value, cb) {
                return cb(null, null);
            });

            notifyObj.setDataInCassandraForDuplicacy(record, value, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(1);
                expect(stub1).to.have.calledWith(cassandraKey, value, sinon.match.func);
                stub1.restore();
                done();
            });
        });

        it("setDataFromCassandraForDuplicacy | save via both keys", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey,
                'source_id': '25',
            }
            let value = {
                "send_at": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                "priority": 0,
                "status": 1
            }

            let stub1 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function (cassandraKey, value, cb) {
                return cb(null, null);
            });

            notifyObj.setDataInCassandraForDuplicacy(record, value, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(2);
                expect(stub1).to.have.calledWith(cassandraKey, value, sinon.match.func);
                expect(stub1).to.have.calledWith(notificationUniqueKey, value, sinon.match.func);
                stub1.restore();
                done();
            });
        });

        it("setDataFromCassandraForDuplicacy | save via cassandra key", (done) => {
            let notificationUniqueKey = '**********_**********_194_26_1_27274_PUSH';
            let cassandraKey = '**********_**********_27274';

            let record = {
                'notificationUniqueKey': notificationUniqueKey,
                'cassandraKey' : cassandraKey,
                'source_id': '25',
            }
            let value = {
                "send_at": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                "priority": 0,
                "status": 1
            }

            let stub1 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function (cassandraKey, value, cb) {
                return cb(null, null);
            });

            notifyObj.setDataInCassandraForDuplicacy(record, value, (err, result) => {
                expect(err).to.be.equal(null);
                expect(stub1).to.have.callCount(2);
                expect(stub1).to.have.calledWith(cassandraKey, value, sinon.match.func);
                expect(stub1).to.have.calledWith(notificationUniqueKey, value, sinon.match.func);
                stub1.restore();
                done();
            });
        });

        it('shouldNotificationBeBlocked | day is changed', (done) => {
            let notifier = {
                'time_interval' : 48*60,
                'send_at' : MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            };
            let data = {
                'send_at' : MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'),
                'status'  : 0
            }

            let result = notifyObj.shouldNotificationBeBlocked(notifier, data);
            expect(result).to.be.equal(false);
            return done();
        })

        it('shouldNotificationBeBlocked | day is not changed', (done) => {
            let notifier = {
                'time_interval' : 24*60,
                'send_at' : MOMENT().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            };
            let data = {
                'send_at' : MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                'status'  : 0
            }

            let result = notifyObj.shouldNotificationBeBlocked(data, notifier);
            expect(result).to.be.equal(true);
            return done();
        })

        it('shouldNotificationBeBlocked | day is not changed, service notify', (done) => {
            let notifier = {
                'time_interval' : 24*60,
                'send_at' : MOMENT().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            };
            let data = {
                'send_at' : MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                'status'  : 0
            }

            let result = notifyObj.shouldNotificationBeBlocked(data, notifier, 'notify');
            expect(result).to.be.equal(false);
            return done();
        })

        it('shouldNotificationBeBlocked | day is not changed, service notify status=1', (done) => {
            let notifier = {
                'time_interval' : 24*60,
                'send_at' : MOMENT().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            };
            let data = {
                'send_at' : MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                'status'  : 1
            }

            let result = notifyObj.shouldNotificationBeBlocked(data, notifier, 'notify');
            expect(result).to.be.equal(true);
            return done();
        })

        it('shouldNotificationBeBlocked | day is not changed, service notify other status', (done) => {
            let notifier = {
                'time_interval' : 24*60,
                'send_at' : MOMENT().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            };
            let data = {
                'send_at' : MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                'status'  : 2
            }

            let result = notifyObj.shouldNotificationBeBlocked(data, notifier, 'notify');
            expect(result).to.be.equal(false);
            return done();
        })
        it("prepareKafkaPayloadForDwhSync", (done)=>{
            let record = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
                source_id: '26',
                category_id: 'abc',
                recharge_number: **********,
                product_id: 194,
                job_id: 1234,
                send_at: '2023-09-23 07:00:00',
                sent_at: null,
                status: 0,
                error_msg: 'Notification failed',
                priority: 1,
                retry_count: '0',
                retry_interval: 30,
                max_retry_count: 2,
                created_at: '2023-09-23 07:00:00',
                updated_at: '2023-09-23 07:00:00',
                data: {
                    additional_data : {
                        timepoint: -1,
                        msg_type: 'push',
                        promocode: 'PROMO123',
                        notif_type: 'push',
                        bill_source: 'RU',
                        templateName: 'Bill Gen Electricity',
                        operator: 'Airtel',
                        amount: '123.45',
                        due_date: null,
                        service: 'Mobile',
                        recon_id: 'C1234=',
                        customer_id: '**********',
                    }
                }
            }
            let result = notifyObj.prepareKafkaPayloadForDwhSync(record);
            expect(typeof (result.template_id)).is.equal('string');
            expect(result.template_id).is.equal('27274');
            expect(typeof (result.recipient)).is.equal('string');
            expect(result.recipient).is.equal('**********');
            expect(typeof (result.source_id)).is.equal('string');
            expect(result.source_id).is.equal('26');
            expect(typeof (result.category_id)).is.equal('object');
            expect(result.category_id).is.equal(null);
            expect(typeof (result.recharge_number)).is.equal('string');
            expect(result.recharge_number).is.equal('**********');
            expect(typeof (result.product_id)).is.equal('string');
            expect(result.product_id).is.equal('194');
            expect(typeof (result.job_id)).is.equal('string');
            expect(result.job_id).is.equal('1234');
            expect(typeof (result.send_at)).is.equal('string');
            expect(result.send_at).is.equal('2023-09-23 07:00:00');
            expect(typeof (result.sent_at)).is.equal('object');
            expect(result.sent_at).is.equal(null);
            expect(typeof (result.status)).is.equal('number');
            expect(result.status).is.equal(0);
            expect(typeof (result.error_msg)).is.equal('string');
            expect(result.error_msg).is.equal('Notification failed');
            expect(typeof (result.priority)).is.equal('number');
            expect(result.priority).is.equal(1);
            expect(typeof (result.retry_count)).is.equal('number');
            expect(result.retry_count).is.equal(0);
            expect(typeof (result.retry_interval)).is.equal('number');
            expect(result.retry_interval).is.equal(30);
            expect(typeof (result.max_retry_count)).is.equal('number');
            expect(result.max_retry_count).is.equal(2);
            expect(typeof (result.created_at)).is.equal('string');
            expect(result.created_at).is.equal('2023-09-23 07:00:00');
            expect(typeof (result.updated_at)).is.equal('string');
            expect(result.updated_at).is.equal('2023-09-23 07:00:00');
            expect(typeof (result.additional_data.templateName)).is.equal('string');
            expect(result.additional_data.templateName).is.equal('Bill Gen Electricity');
            expect(typeof (result.additional_data.operator)).is.equal('string');
            expect(result.additional_data.operator).is.equal('Airtel');
            expect(typeof (result.additional_data.amount)).is.equal('number');
            expect(result.additional_data.amount).is.equal(123.45);
            expect(typeof (result.additional_data.raw_expiry_date)).is.equal('object');
            expect(result.additional_data.raw_expiry_date).is.equal(null);
            expect(typeof (result.additional_data.service)).is.equal('string');
            expect(result.additional_data.service).is.equal('Mobile');
            expect(typeof (result.additional_data.recon_id)).is.equal('string');
            expect(result.additional_data.recon_id).is.equal('C1234=');
            expect(typeof (result.additional_data.customer_id)).is.equal('string');
            expect(result.additional_data.customer_id).is.equal('**********');
            done();
        })

        it("additionalDuplicacyChecks| SUCCESS", (done) => {
            let notifier = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },
            action = 'create',
            idsWithPendingStatus = [];

            let stub1 = sinon.stub(notifyObj, 'performDuplicacyCheck').callsFake(function(notifier, checkType, callback){
                return callback(null, 'create');
            });

            notifyObj.additionalDuplicacyChecks(notifier, action, idsWithPendingStatus, function(err, result, idsWithPendingStatus){
                expect(err).to.be.equal(null);
                expect(result).to.be.equal('create');
                expect(idsWithPendingStatus.length).to.be.equal(0);
                expect(stub1).to.have.callCount(1);
                stub1.restore();
                done();
            });
        })

        it("additionalDuplicacyChecks| type not in list", (done) => {
            let notifier = {
                type: 'EMAIL',
                template_id: '27274',
                recipient: **********,
            },
            action = 'create',
            idsWithPendingStatus = [];

            let stub1 = sinon.stub(notifyObj, 'performDuplicacyCheck').callsFake(function(notifier, checkType, callback){
                return callback(null, 'create');
            });

            notifyObj.additionalDuplicacyChecks(notifier, action, idsWithPendingStatus, function(err, result, idsWithPendingStatus){
                expect(err).to.be.equal(null);
                expect(result).to.be.equal('create');
                expect(idsWithPendingStatus.length).to.be.equal(0);
                expect(stub1).to.have.callCount(0);
                stub1.restore();
                done();
            });
        })

        it("performDuplicacyCheck | SUCCESS", (done) => {
            let notifier = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },
            checkType = 'basic';
            let stub1 = sinon.stub(notifyObj, 'isWhitelistedService').returns(true);
            let stub2 = sinon.stub(notifyObj, 'checkNotificationsInBatches').returns(false);
            let stub3 = sinon.stub(notifyObj, 'extractParamFromDynamicParams').returns('electricity');

            notifyObj.performDuplicacyCheck(notifier, checkType, function(err, result){
                expect(err).to.be.equal(null);
                expect(result).to.be.equal('create');
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                done();
            });
        })

        it("performDuplicacyCheck | service not found", (done) => {
            let notifier = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },
            checkType = 'basic';
            let stub1 = sinon.stub(notifyObj, 'isWhitelistedService').returns(true);
            let stub2 = sinon.stub(notifyObj, 'checkNotificationsInBatches').returns(false);
            let stub3 = sinon.stub(notifyObj, 'extractParamFromDynamicParams').returns(null);

            notifyObj.performDuplicacyCheck(notifier, checkType, function(err, result){
                expect(err).to.be.equal('service not found');
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(0);
                expect(stub2).to.have.callCount(0);
                expect(stub3).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                done();
            });
        })

        it("performDuplicacyCheck | service not whitelisted", (done) => {
            let notifier = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },
            checkType = 'basic';
            let stub1 = sinon.stub(notifyObj, 'isWhitelistedService').returns(false);
            let stub2 = sinon.stub(notifyObj, 'checkNotificationsInBatches').returns(false);
            let stub3 = sinon.stub(notifyObj, 'extractParamFromDynamicParams').returns('electricity');

            notifyObj.performDuplicacyCheck(notifier, checkType, function(err, result){
                expect(err).to.be.equal('service not whitelisted');
                expect(result).to.be.equal(undefined);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(0);
                expect(stub3).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                done();
            });
        })

        it("performDuplicacyCheck | notification found", (done) => {
            let notifier = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },
            checkType = 'basic';
            let stub1 = sinon.stub(notifyObj, 'isWhitelistedService').returns(true);
            let stub2 = sinon.stub(notifyObj, 'checkNotificationsInBatches').returns(true);
            let stub3 = sinon.stub(notifyObj, 'extractParamFromDynamicParams').returns('electricity');

            notifyObj.performDuplicacyCheck(notifier, checkType, function(err, result){
                expect(err).to.be.equal(null);
                expect(result).to.be.equal('drop');
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                done();
            });
        })

        it("checkNotificationsInBatches | notification found", (done) => {
            let row = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },checkType = 'basic',
            service = 'electricity',
            buffer_time_interval = 24;

            let stub1 = sinon.stub(notifyObj, 'getBatchSizeAndTimeInterval').returns([10, 1, buffer_time_interval]);
            let stub2 = sinon.stub(notifyObj.cassandraBills, 'getNotificationsQuery').returns(['query', ['queryParams']]);
            let stub3 = sinon.stub(notifyObj.cassandraBills, 'getNotificationsFromCassandraTable').callsFake(function(self, query, queryParams, batchSize, service, row, checkType, time_interval, callback){
                return callback([]);
            });

            let promise = notifyObj.checkNotificationsInBatches(row, checkType);
            promise.then((result) => {
                expect(result).to.be.equal(true);
                expect(stub1).to.have.callCount(1);
                expect(stub2).to.have.callCount(1);
                expect(stub3).to.have.callCount(1);
                stub1.restore();
                stub2.restore();
                stub3.restore();
                done();
            })
        })

        it("filterResult | SUCCESS basic approach", (done) => {
            let row = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },checkType = 'basic',
            service = 'electricity';
            let result = [{
            }];
            let item = {
                template_id: '27274',
                type: 'PUSH',
                status: 'pending',
            }
            let stub1 = sinon.stub(notifyObj, 'extractParamFromStringifiedData').returns('airtel');
            let stub2 = sinon.stub(notifyObj, 'filterResultForBasicApproach').callsFake(function(item, row, filteredResult, params){
                return filteredResult;
            });
            let stub3 = sinon.stub(notifyObj, 'filterResultForAdvancedApproach').callsFake(function(item, row, filteredResult, params){
                return filteredResult;
            });

            let filteredResult = notifyObj.filterResult(result, row, checkType);
            expect(filteredResult.length).to.be.equal(0);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        })

        it("filterResult | SUCCESS advanced approach", (done) => {
            let row = {
                type: 'PUSH',
                template_id: '27274',
                recipient: **********,
            },checkType = 'advanced',
            service = 'electricity';
            let result = [{
            }];
            let item = {
                template_id: '27274',
                type: 'PUSH',
                status: 'pending',
            }
            let stub1 = sinon.stub(notifyObj, 'extractParamFromStringifiedData').returns('airtel');
            let stub2 = sinon.stub(notifyObj, 'filterResultForBasicApproach').callsFake(function(item, row, filteredResult, params){
                return filteredResult;
            });
            let stub3 = sinon.stub(notifyObj, 'filterResultForAdvancedApproach').callsFake(function(item, row, filteredResult, params){
                return filteredResult;
            });

            let filteredResult = notifyObj.filterResult(result, row, checkType);
            expect(filteredResult.length).to.be.equal(0);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        })

        it("filterResultForBasicApproach | SUCCESS", (done) => {
            let params={
                item_recharge_number: '0987654321',
                recharge_number: '0987654321',
                item_operator: 'airtel',
                operator: 'airtel',
                item_template_id: '27274',
                template_id: '27274',
                item_type: 'PUSH',
                type: 'PUSH',
                item_status: 1
            };
            let row={
                type: 'PUSH'
            }
            let item={
                type: 'PUSH'
            }
            let result = [];
            let stub1 = sinon.stub(notifyObj, 'existingNotificationTiming').returns(true);
            let filteredResult = notifyObj.filterResultForBasicApproach(item, row, result, params);
            expect(result.length).to.be.equal(1);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            done();
        })

        it("filterResultForBasicApproach | SUCCESS", (done) => {
            let params={
                item_recharge_number: '0987654321',
                recharge_number: '0987654321',
                item_operator: 'airtel',
                operator: 'vodafone',
                item_template_id: '27274',
                template_id: '27274',
                item_type: 'PUSH',
                type: 'PUSH',
                item_status: 1
            };
            let row={
                type: 'PUSH'
            }
            let item={
                type: 'PUSH'
            }
            let result = [];
            let stub1 = sinon.stub(notifyObj, 'existingNotificationTiming').returns(true);
            let filteredResult = notifyObj.filterResultForBasicApproach(item, row, result, params);
            expect(result.length).to.be.equal(0);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            done();
        })
        it("filterResultForBasicApproach | different operator", (done) => {
            let params={
                item_recharge_number: '0987654321',
                recharge_number: '0987654321',
                item_operator: 'airtel',
                operator: 'vodafone',
                item_template_id: '27274',
                template_id: '27274',
                item_type: 'PUSH',
                type: 'PUSH',
                item_status: 1
            };
            let row={
                type: 'PUSH'
            }
            let item={
                type: 'PUSH'
            }
            let result = [];
            let stub1 = sinon.stub(notifyObj, 'existingNotificationTiming').returns(true);
            let filteredResult = notifyObj.filterResultForBasicApproach(item, row, result, params);
            expect(result.length).to.be.equal(0);
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            done();
        })

        it("filterResultForAdvancedApproach | SUCCESS", (done) => {
            let params={
                item_recharge_number: '0987654321',
                recharge_number: '0987654321',
                item_operator: 'airtel',
                operator: 'airtel',
                item_template_id: '27274',
                template_id: '27274',
                item_type: 'PUSH',
                type: 'PUSH',
                item_status: 1
            };
            let row={
                type: 'PUSH'
            }
            let item={
                type: 'PUSH'
            }
            let result = [];

            let stub1 = sinon.stub(notifyObj, 'extractParamFromStringifiedData').returns(['2025-02-25T00:00:00', 'DUEDATE', 0]);
            let stub2 = sinon.stub(notifyObj, 'extractParamFromAdditionalData').returns(['2025-02-25T00:00:00', 'DUEDATE', 0]);
            let stub3 = sinon.stub(notifyObj, 'existingNotificationTiming').returns(true);

            let filteredResult = notifyObj.filterResultForAdvancedApproach(item, row, result, params);
            expect(result.length).to.be.equal(1);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        })

        it("filterResultForAdvancedApproach | prefix RN check", (done) => {
            let params={
                item_recharge_number: '987654321',
                recharge_number: '1287654321',
                item_operator: 'airtel',
                operator: 'airtel',
                item_template_id: '27274',
                template_id: '27274',
                item_type: 'PUSH',
                type: 'PUSH',
                item_status: 1
            };
            let row={
                type: 'PUSH'
            }
            let item={
                type: 'PUSH'
            }
            let result = [];

            let stub1 = sinon.stub(notifyObj, 'extractParamFromStringifiedData').returns(['2025-02-25T00:00:00', 'DUEDATE', 0]);
            let stub2 = sinon.stub(notifyObj, 'extractParamFromAdditionalData').returns(['2025-02-25T00:00:00', 'DUEDATE', 0]);
            let stub3 = sinon.stub(notifyObj, 'existingNotificationTiming').returns(true);

            let filteredResult = notifyObj.filterResultForAdvancedApproach(item, row, result, params);
            expect(result.length).to.be.equal(0);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        })

        it("existingNotificationTiming | SUCCESS", (done) => {
            let item = {
                sent_at: MOMENT().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                send_at: MOMENT().subtract(30, 'hours').format('YYYY-MM-DD HH:mm:ss')
            }
            let time_interval = 24;
            let result = notifyObj.existingNotificationTiming(item, time_interval);
            expect(result).to.be.equal(false);
            done();
        })

        it("existingNotificationTiming | FAILED", (done) => {
            let item = {
                // sent_at: MOMENT().subtract(1, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                send_at: MOMENT().subtract(30, 'hours').format('YYYY-MM-DD HH:mm:ss')
            }
            let time_interval = 24;
            let result = notifyObj.existingNotificationTiming(item, time_interval);
            expect(result).to.be.equal(false);
            done();
        })

        it("existingNotificationTiming | FAILED", (done) => {
            let item = {
                sent_at: MOMENT().subtract(29, 'hours').format('YYYY-MM-DD HH:mm:ss'),
                send_at: MOMENT().subtract(30, 'hours').format('YYYY-MM-DD HH:mm:ss')
            }
            let time_interval = 24;
            let result = notifyObj.existingNotificationTiming(item, time_interval);
            expect(result).to.be.equal(false);
            done();
        })

        it("checkDueDateAndTimepoint | SUCCESS", (done) => {
            let item_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let row_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let item_timepoint = 0;
            let row_timepoint = '0';
            let item_notif_type = 'DUEDATE';
            let row_notif_type = 'DUEDATE';
            let result = notifyObj.checkDueDateAndTimepoint(item_due_date, row_due_date, item_timepoint, row_timepoint, item_notif_type, row_notif_type);
            expect(result).to.be.equal(true);
            done();
        })

        it("checkDueDateAndTimepoint | FAIL1", (done) => {
            let item_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let row_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let item_timepoint = 1;
            let row_timepoint = '0';
            let item_notif_type = 'DUEDATE';
            let row_notif_type = 'DUEDATE';
            let result = notifyObj.checkDueDateAndTimepoint(item_due_date, row_due_date, item_timepoint, row_timepoint, item_notif_type, row_notif_type);
            expect(result).to.be.equal(false);
            done();
        })

        it("checkDueDateAndTimepoint | FAIL2", (done) => {
            let item_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let row_due_date = MOMENT().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
            let item_timepoint = 0;
            let row_timepoint = '0';
            let item_notif_type = 'DUEDATE';
            let row_notif_type = 'DUEDATE';
            let result = notifyObj.checkDueDateAndTimepoint(item_due_date, row_due_date, item_timepoint, row_timepoint, item_notif_type, row_notif_type);
            expect(result).to.be.equal(false);
            done();
        })

        it("checkDueDateAndTimepoint | SUCCESS2", (done) => {
            let item_due_date = MOMENT().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss');
            let row_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
            let item_timepoint = 0;
            let row_timepoint = '0';
            let item_notif_type = 'BILLGEN';
            let row_notif_type = 'DUEDATE';
            let result = notifyObj.checkDueDateAndTimepoint(item_due_date, row_due_date, item_timepoint, row_timepoint, item_notif_type, row_notif_type);
            expect(result).to.be.equal(true);
            done();
        })

        // it("checkDueDateAndTimepoint | FAILED", (done) => {
        //     let item_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
        //     let row_due_date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss');
        //     let item_timepoint = 'DUEDATE';
        //     let row_timepoint = 'DUEDATE';
            


        // it("sendNotification | valid payload | cassandra cache not found", async () => {
        //     let row = {"id":630,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-23 07:00:00","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************,"notify_acknowledgeTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH","last_ref_time":"2023-09-21 23:20:08"}
        //     let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
        //     let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}
        //     let ttl=1000;
        //     let error=null;
        //     let response= {"statusCode":200};
        //     let body={"jobId":12344};
        //     let uri='https//notificationapi';
        //     nock('https//notificationapi')
        //     .post('/') // Use the appropriate HTTP method and endpoint
        //     .reply(200, { result: {'jobId':1234} });

        //     let stub1 = sinon.stub(notifyObj, 'checkNotificationInCassandra').resolves(false);

        //     let stub2 = sinon.stub(notifyObj, 'paymentAlreadyDone').callsFake(function fakeFn(row, callback){
        //         return callback(null);
        //     }); 

        //     let stub3 = sinon.stub(notifyObj, 'processNotificationApiOpts').returns({"uri":"https//notificationapi"})

        //     let stub4 = sinon.stub(notifyObj, 'prepareResponse').callsFake(function fakeFn(callback,row, error, response, body, uri){
        //         return callback({"error_msg":null, "job_id":12345});
        //     }); 

        //     let stub5 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
        //         return callback(null,cassandraValue);
        //     }); 

        //     let stub6 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function fakeFn(cassandraKey,cassandraValue, callback){
        //         return callback(null);
        //     }); 
            
        //     notifyObj.sendNotification(function(data){
        //         expect(data.error_msg).to.be.equal(null)
        //         expect(data.job_id).to.be.equal(12345)
        //         expect(stub1).to.have.callCount(1);
        //         expect(stub2).to.have.callCount(1);
        //         expect(stub3).to.have.callCount(1);
        //         expect(stub4).to.have.callCount(1);
        //         expect(stub5).to.have.callCount(1);
        //         expect(stub6).to.have.callCount(1);
        //         stub1.restore();
        //         stub2.restore();
        //         stub3.restore();
        //         stub4.restore();
        //         stub5.restore();
        //         stub6.restore();
        //     },row)
        // });
        // it("sendNotification | valid payload | cassandra error", async () => {
        //     let row = {"id":630,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"26","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-23 07:00:00","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************,"notify_acknowledgeTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH","last_ref_time":"2023-09-21 23:20:08"}
        //     let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
        //     let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}
        //     let ttl=1000;
        //     let error=null;
        //     let response= {"statusCode":200};
        //     let body={"jobId":12344};
        //     let uri='https//notificationapi';
        //     nock('https//notificationapi')
        //     .post('/') // Use the appropriate HTTP method and endpoint
        //     .reply(200, { result: {'jobId':1234} });

        //     let stub1 = sinon.stub(notifyObj, 'checkNotificationInCassandra').rejects("cassandra not working");
        //     let stub7 = sinon.stub(notifyObj, 'checkNotificationInDB').resolves(false);

        //     let stub2 = sinon.stub(notifyObj, 'paymentAlreadyDone').callsFake(function fakeFn(row, callback){
        //         return callback(null);
        //     }); 

        //     let stub3 = sinon.stub(notifyObj, 'processNotificationApiOpts').returns({"uri":"https//notificationapi"})

        //     let stub4 = sinon.stub(notifyObj, 'prepareResponse').callsFake(function fakeFn(callback,row, error, response, body, uri){
        //         return callback({"error_msg":null, "job_id":12345});
        //     }); 

        //     let stub5 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
        //         return callback(null,cassandraValue);
        //     }); 

        //     let stub6 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function fakeFn(cassandraKey,cassandraValue, callback){
        //         return callback(null);
        //     }); 
            
        //     notifyObj.sendNotification(function(data){
        //         expect(data.error_msg).to.be.equal(null)
        //         expect(data.job_id).to.be.equal(12345)
        //         expect(stub1).to.have.callCount(1);
        //         expect(stub7).to.have.callCount(1);
        //         expect(stub2).to.have.callCount(1);
        //         expect(stub3).to.have.callCount(1);
        //         expect(stub4).to.have.callCount(1);
        //         expect(stub5).to.have.callCount(1);
        //         expect(stub6).to.have.callCount(1);
        //         stub1.restore();
        //         stub2.restore();
        //         stub3.restore();
        //         stub4.restore();
        //         stub5.restore();
        //         stub6.restore();
        //         stub7.restore();
        //     },row)
        // });
        // it("sendNotification | valid payload | config not present for cassandra cache", async () => {
        //     let row = {"id":630,"type":"PUSH","template_id":"27274","recipient":"**********","source_id":"24","category_id":1,"recharge_number":"**********","product_id":194,"data":{"template_type":"push","template_id":"27274","options":{"notificationOpts":{"recipients":"**********","channel_id":"both","deepLinkObj":{"extra":{"url":"paytmmp://mobile_postpaid?url=https://catalog.paytm.com/v1/mobile/getproductlist/21?$product_id=194$price=null$recharge_number=**********$utm_source=billReminderNotFound$utm_medium=null$utm_campaign=mobile_airtel_27274","url_type":"external"}},"noRich":false},"type":"async","data":{"amount":null,"dataConsumed":90,"recharge_number":"**********","operator":"Airtel","operator_label":"Airtel","brand":"Airtel","thumbnail":"https://assetscdn1.paytm.com/images/catalog/operators/*************.png","category_id":21,"service":"Mobile","customer_id":"**********","bank_name":null,"card_network":null,"paytype":"postpaid","time_interval":null,"refId":null,"rtspId":null,"due_date":"21st Sep 2023","short_operator_name":"Airtel"}}},"max_retry_count":2,"retry_count":0,"retry_interval":30,"priority":1,"send_at":"2023-09-23 07:00:00","status":0,"timestamps":{"billFetchReminder_acknowledgeTime":*************,"billFetchReminder_onBoardTime":null,"notify_onBoardTime":*************,"notify_acknowledgeTime":*************},"cassandraKey":"**********_**********_194_26_1_27274_PUSH","last_ref_time":"2023-09-21 23:20:08"}
        //     let cassandraKey= "**********_**********_194_26_1_27274_PUSH";
        //     let cassandraValue= {"send_at":MOMENT().subtract(2,'days').format('YYYY-MM-DD HH:mm:ss'),"priority":0,"status":1}
        //     let ttl=1000;
        //     let error=null;
        //     let response= {"statusCode":200};
        //     let body={"jobId":12344};
        //     let uri='https//notificationapi';
        //     nock('https//notificationapi')
        //     .post('/') // Use the appropriate HTTP method and endpoint
        //     .reply(200, { result: {'jobId':1234} });

        //     let stub1 = sinon.stub(notifyObj, 'checkNotificationInCassandra').rejects("cassandra not working");
        //     let stub7 = sinon.stub(notifyObj, 'checkNotificationInDB').resolves(false);

        //     let stub2 = sinon.stub(notifyObj, 'paymentAlreadyDone').callsFake(function fakeFn(row, callback){
        //         return callback(null);
        //     }); 

        //     let stub3 = sinon.stub(notifyObj, 'processNotificationApiOpts').returns({"uri":"https//notificationapi"})

        //     let stub4 = sinon.stub(notifyObj, 'prepareResponse').callsFake(function fakeFn(callback,row, error, response, body, uri){
        //         return callback({"error_msg":null, "job_id":12345});
        //     }); 

        //     let stub5 = sinon.stub(notifyObj, 'getDataFromCassandra').callsFake(function fakeFn(cassandraKey, callback){
        //         return callback(null,cassandraValue);
        //     }); 

        //     let stub6 = sinon.stub(notifyObj, 'setDataInCassandra').callsFake(function fakeFn(cassandraKey,cassandraValue, callback){
        //         return callback(null);
        //     }); 
            
        //     notifyObj.sendNotification(function(data){
        //         expect(data.error_msg).to.be.equal(null)
        //         expect(data.job_id).to.be.equal(12345)
        //         expect(stub1).to.have.callCount(0);
        //         expect(stub7).to.have.callCount(1);
        //         expect(stub2).to.have.callCount(1);
        //         expect(stub3).to.have.callCount(1);
        //         expect(stub4).to.have.callCount(1);
        //         expect(stub5).to.have.callCount(0);
        //         expect(stub6).to.have.callCount(0);
        //         stub1.restore();
        //         stub2.restore();
        //         stub3.restore();
        //         stub4.restore();
        //         stub5.restore();
        //         stub6.restore();
        //         stub7.restore();
        //     },row)
        // });
    })
        
   
    
   