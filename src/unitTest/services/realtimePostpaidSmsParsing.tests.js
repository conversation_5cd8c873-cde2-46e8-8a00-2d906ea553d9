/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    import utility from '../../lib/index'
    
    import SERVICE from '../../services'
    import STARTUP_MOCK from '../__mocks__/startUp'
    import realtimeSmsParsingPostpaid from '../../services/realtimeSmsParsing/postpaid';
    import ASYNC from 'async';
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    describe('start function', () => {
        let options, instance,serviceObj;
      
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                console.log("error ", error);
                console.log("options ", options);

                serviceObj = new realtimeSmsParsingPostpaid(options);
            });
        });
      
        it('should configure Kafka and connect to MongoDB successfully', (done) => {
            const connectMongoStub = sinon.stub(serviceObj.mongoDbInstance, 'connect').resolves();
            const asyncWaterfallStub = sinon.stub(serviceObj, 'configureKafka').callsFake((cb)=>{
                return cb(null);
            })
            // Call the start function
            try{
                serviceObj.start();
            }catch(err){
                  // Assertions
            expect(serviceObj.configureKafka.calledOnce).to.be.true;
            expect(connectMongoStub.calledOnce).to.be.true;
            expect(asyncWaterfallStub.calledOnce).to.be.true;
            }finally{
                connectMongoStub.restore();
                 asyncWaterfallStub.restore();
            }        
        
            // Restore the stubs
            return done();
          });
        
          it('should handle errors during Kafka configuration and MongoDB connection', async () => {
            // Mock the necessary functions for promisify
            const connectMongoStub = sinon.stub(serviceObj.mongoDbInstance, 'connect').resolves();

        
            const asyncWaterfallStub = sinon.stub(serviceObj, 'configureKafka').callsFake((cb)=>{
                return cb(null);
            })
        
            // Call the start function
            try{
                serviceObj.start()
            }catch(error){
            // Assertions
            expect(serviceObj.configureKafka.calledOnce).to.be.true;
            expect(connectMongoStub.calledOnce).to.be.true;
            expect(asyncWaterfallStub.calledOnce).to.be.true;
        
            // Restore the stubs
            connectMongoStub.restore();
            asyncWaterfallStub.restore();
            }
          });
      });

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new realtimeSmsParsingPostpaid(options);
                done();
            });
        });

        // it("testing configureKafka function | Error case from initProducer function",()=>{
        //     serviceObj.producer = new serviceObj.infraUtils.kafka.producer();
        //     let stub1 = sinon.stub(serviceObj.producer, "initProducer").callsFake(function fakeFn(callback){
        //         return callback("Error from initProducer");
        //     });
        //     serviceObj.configureKafka((error)=>{
        //         if (error) {
        //             expect(error).to.be.equal("Error from initProducer");
        //         }
        //     });
        //     stub1.restore();
        // });
        it("testing configureKafka function | Error case from initConsumer function",()=>{
            serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
                return callback("Error from initConsumer");
            });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal("Error from initConsumer");
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            //serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
            //     return callback(null);
            // });
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
        it("testing configureKafka function | success ",()=>{
            // serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
            // sinon.stub(serviceObj.consumer, "initConsumer").returns(null);
            serviceObj.configureKafka((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
        });
    
        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            serviceObj.consumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
                }
            };
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [{}
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [{}];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
        it("execSteps || greyScaleEnv || commitOffset function", () => {
            let record = [
            ];
            let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            let clock = sinon.useFakeTimers();
    
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb();
                },
                _resumeConsumer: () => {
    
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
        })
        it("execSteps || greyScaleEnv || Error in commitOffset function", () => {
            let record = [];
            let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
            let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
            sinon.stub(process, 'exit');
            let clock = sinon.useFakeTimers();
            serviceObj.kafkarealtimeSmsParsingConsumer = {
                _pauseConsumer : () => {
                    return;
                },
                commitOffset: (data, cb) => {
                    return cb("Error in commitOffset");
                },
                _resumeConsumer: () => {
                }
            };
            serviceObj.greyScaleEnv = true;
            
            let processedRecords = serviceObj.execSteps(record);
            clock.tick(40 * 60 * 1000)
            expect(processedRecords).to.be.equal(undefined);
            expect(stub1).to.have.callCount(0);
            expect(stub2).to.have.callCount(2);
            stub1.restore();
            stub2.restore();
            process.exit.restore();
        })
    
        it("start function || configureKafka error case", () => {
            sinon.stub(process, 'exit');
            let stub2 = sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback("configureKafka error response callback");
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);
                }
            });
            expect(stub2).to.have.callCount(1);
            process.exit.restore();
            stub2.restore();
        })
        it("start function || Error in configureKafka", () => {
            serviceObj.configureKafka = (callback)=>{
                return callback("Error in configureKafka");
            }

            sinon.stub(process, 'exit');     
            let clock = sinon.useFakeTimers();
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(undefined);            
                }
            });
            clock.tick(86400000) 
            process.exit.restore();
        })
        it("start function || configureKafka success case", () => {
            let stub2 =sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback){
                return callback();
            });        
            serviceObj.start((error)=>{
                if (error) {
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub2).to.have.callCount(1);
            stub2.restore();
        })

    
    });

    describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
        let serviceObj;
    
         let record;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new realtimeSmsParsingPostpaid(options);
                done();
            });
        });

        it("testing processBatch | execution count", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                },{
                    "topic":"dwh-ingest-SMS_PARSING_LENDING",
                    "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').callsFake(function fakeFn(callback){
                setTimeout(() => {
                    callback();
                }, 1)
            });
            // let stub2 = sinon.spy(serviceObj, 'processData');
            serviceObj.processBatch(data, function(error){
                if(error){
                    console.log("what is the eror", error);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });

        it("testing processBatch | correct payload", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | correct payload processData rejects", () => {
            let data =[{
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }]   
            let stub1 = sinon.stub(serviceObj, 'processData').rejects();
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(1);
            stub1.restore();
        });
        it("testing processBatch | incorrect payload", () => {
            let data ={
                "topic":"dwh-ingest-SMS_PARSING_LENDING",
                "value":"{\"data\":[{\"cId\":\"*********\",\"deviceDateTime\":*************,\"mId\":\"\",\"model\":\"TA-1021\",\"uploadTime\":*************,\"timestamp\":null,\"user_agent\":null,\"true_client_ip\":null,\"preference\":[{\"prefCat\":\"permission\",\"prefKeys\":\"ocl.permission.creditcard.sms_read_consent\",\"prefSubCat\":\"sms consent\"}],\"newUser\":null,\"realTime\":null,\"smsDateTime\":*************,\"smsSenderID\":\"VM-KOTAKB\",\"smsOperator\":\"JIO\",\"smsReceiver\":\"xxxxxxxxx6365\",\"smsUUID\":\"9f14ea09-b7ae-4278-b26e-762efefdd641\",\"predicted_category\":\"2\",\"level_2_category\":\"2.0\",\"fastag_class\":\"-1\",\"fastag_features\":{},\"mode_of_transaction\":{\"mode\":\"credit_card\",\"vendor\":\"KOTAK\",\"transaction_reference_no\":null},\"ccbp_category\":\"1\",\"account_no\":null,\"card_no\":\"2003\",\"amount\":10000,\"available_Balance\":null,\"date\":\"2021-03-15\",\"bankName\":\"KOTAK\"}],\"kafka_topic\":[\"SMS_PARSING_CC_BILLS_PAYMENT\"]}"
                }   
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
        it("testing processBatch | empty payload", () => {
            let data =[];  
            let stub1 = sinon.stub(serviceObj, 'processData').returns(null);
            serviceObj.processBatch(data, function(error){
                if(error){
                    expect(error).to.be.equal(null);
                }
            });
            expect(stub1).to.have.callCount(0);
            stub1.restore();
        });
    });

    describe('processData', function () {
        let serviceObj;
            before(function () {    
                STARTUP_MOCK.init(function(error, options){
                    serviceObj = new realtimeSmsParsingPostpaid(options);
                });
            });
        it('should handle a valid record with postpaid classifier', function (done) {
          const recordData = [
            {
              rtspClassId: 11, 
              rtspClassName: 'MOBILE_POSTPAID', 
              appVersion: '1.0.0', 
            },
          ];
      
          const record = {
            topic: 'TOPIC_NAME', 
            partition: 0, 
            value: JSON.stringify({
              data: recordData,
            }),
          };
      
          const asyncMapStub = sinon.stub(ASYNC, 'map').callsArgWith(2, null); 
          const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD');
      
          serviceObj.processData(record, () => {
            // Assertions
            expect(sendMetricsStub.calledTwice).to.be.true; 
            expect(asyncMapStub.calledOnce).to.be.true; 
      
            // Restore the stubs
            asyncMapStub.restore();
            sendMetricsStub.restore();
      
            done();
          });
        });
        it('should handle a valid record with postpaid classifier error case', function (done) {
            const recordData = [
              {
                rtspClassId: 11, 
                rtspClassName: 'MOBILE_POSTPAID', 
                appVersion: '1.0.0', 
              },
            ];
        
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0, 
              value: JSON.stringify({
                data: recordData,
              }),
            };
        
            const asyncMapStub = sinon.stub(ASYNC, 'map').callsArgWith(2, "error in async function"); 
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD');
        
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledThrice).to.be.true; 
              expect(asyncMapStub.calledOnce).to.be.true; 
        
              // Restore the stubs
              asyncMapStub.restore();
              sendMetricsStub.restore();
        
              done();
            });
          });
        it('should handle a valid record with postpaid electricity category', function (done) {
            const recordData = [
              {
                category: 'Electricity', 
                rechargeNumber2: 'postpaid', 
              },
            ];
        
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0, 
              value: JSON.stringify({
                data: recordData,
              }),
            };
        
            const asyncMapStub = sinon.stub(ASYNC, 'map').callsArgWith(2, null); 
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD');
        
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; 
              expect(asyncMapStub.calledOnce).to.be.true; 
        
              // Restore the stubs
              asyncMapStub.restore();
              sendMetricsStub.restore();
        
              done();
            });
        });
        it('should handle a record with missing "data" key', function (done) {
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0, 
              value: JSON.stringify({
                // No "data" key in the record
              }),
            };
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD'); 
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; 
              sendMetricsStub.restore();
              done(); 
            });
          });
          it('parsing error in json', function (done) {
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0, 
              value: "{data123",
            };
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD'); 
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; 
              sendMetricsStub.restore();
              done(); 
            });
          });
          it('records not array', function (done) {
            const recordData = 
              {
                rtspClassId: 11, 
                rtspClassName: 'MOBILE_POSTPAID', 
                appVersion: '1.0.0', 
              };
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0, 
              value: JSON.stringify({
                data: recordData,
              }),
            };
        
            const asyncMapStub = sinon.stub(ASYNC, 'map').callsArgWith(2, null); 
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD');
        
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; 
              expect(asyncMapStub.calledOnce).to.be.false; 
        
              // Restore the stubs
              asyncMapStub.restore();
              sendMetricsStub.restore();
        
              done();
            });
          });
          it('data length less than 1', function (done) {
            const recordData = [
            ];
        
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0, 
              value: JSON.stringify({
                data: recordData,
              }),
            };
        
            const asyncMapStub = sinon.stub(ASYNC, 'map').callsArgWith(2, null); 
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD');
        
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; 
              expect(asyncMapStub.calledOnce).to.be.false; 
        
              // Restore the stubs
              asyncMapStub.restore();
              sendMetricsStub.restore();
        
              done();
            });
          });
          it('should handle a valid record with postpaid classifier covering execute strategy mock', function (done) {
            // Mock the necessary functions and data
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0,
              value: JSON.stringify({
                data: [
                  {
                    rtspClassId: 'POSTPAID_CLASSIFIER_ID', 
                    rtspClassName: 'POSTPAID_CLASSIFIER_NAME', 
                    appVersion: 'APP_VERSION', 
                  },
                ],
              }),
            };
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD'); // Stub the _sendMetricsToDD function
            const executeStrategyStub = sinon.stub(serviceObj.postpaidSmsParsing, 'executeStrategy').callsFake((callback, smsData, self) => {
              return callback(null);
            });
        
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; // Ensure that _sendMetricsToDD is called twice
              expect(executeStrategyStub.calledOnce).to.be.true; // Ensure that executeStrategy is called once
              sendMetricsStub.restore();
              executeStrategyStub.restore();
        
              done(); // Call the done callback to indicate the end of the test
            });
        });
        it('should handle a valid record with postpaid classifier covering execute strategy mock error case', function (done) {
            // Mock the necessary functions and data
            const record = {
              topic: 'TOPIC_NAME', 
              partition: 0,
              value: JSON.stringify({
                data: [
                  {
                    rtspClassId: 'POSTPAID_CLASSIFIER_ID', 
                    rtspClassName: 'POSTPAID_CLASSIFIER_NAME', 
                    appVersion: 'APP_VERSION', 
                  },
                ],
              }),
            };
            const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD'); // Stub the _sendMetricsToDD function
            const executeStrategyStub = sinon.stub(serviceObj.postpaidSmsParsing, 'executeStrategy').callsFake((callback, smsData, self) => {
              return callback("error in executeStrategyStub");
            });
        
            serviceObj.processData(record, () => {
              // Assertions
              expect(sendMetricsStub.calledTwice).to.be.true; // Ensure that _sendMetricsToDD is called twice
              expect(executeStrategyStub.calledOnce).to.be.true; // Ensure that executeStrategy is called once
              sendMetricsStub.restore();
              executeStrategyStub.restore();
        
              done(); // Call the done callback to indicate the end of the test
            });
        });
    });

    describe('defaultStrategy', function () {
        let serviceObj;
            before(function () {    
                STARTUP_MOCK.init(function(error, options){
                    serviceObj = new realtimeSmsParsingPostpaid(options);
                });
            });
        // it('should log and send metrics for default strategy', function () {
        //   const logStub = sinon.stub(serviceObj.L, 'log'); // Stub the log function
        //   const sendMetricsStub = sinon.stub(utility, '_sendMetricsToDD'); // Stub the _sendMetricsToDD function
      
        //   const record = {
        //   };
      
        //   serviceObj.defaultStrategy(() => {
        //     // Assertions
        //     expect(logStub.calledOnce).to.be.true; // Ensure that the log function is called once
        //     expect(sendMetricsStub.calledOnce).to.be.true; // Ensure that _sendMetricsToDD is called once
        //     // Restore the stubs
        //     logStub.restore();
        //     sendMetricsStub.restore();
        //   });
        // });
      });

      describe('suspendOperations', function () {
        let serviceObj;
        before(function () {    
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new realtimeSmsParsingPostpaid(options);
            });
        });
        // it('should resolve promise when kafka consumer is stopped successfully', async function () {
        //   const logStub = sinon.stub(serviceObj.L, 'log');
        //   const kafkarealtimeSmsParsingConsumer = {
        //     close: sinon.stub().yields(null, 'Consumer Stopped!')
        //   };
          
        //   serviceObj.kafkarealtimeSmsParsingConsumer = kafkarealtimeSmsParsingConsumer;

        //   // Call the suspendOperations function
        //   try {
        //     await serviceObj.suspendOperations();
        //   } catch (error) {
        //     // Should not enter this block since there is no rejection expected
        //     expect.fail('Promise should be resolved');
        //   }
      
        //   // Assertions
        //   expect(kafkarealtimeSmsParsingConsumer.close.calledOnce).to.be.true;
        //   // Restore the stubs
        //   logStub.restore();
        // });
      
        it('should reject promise when there is an error stopping the kafka consumer', async function () {
            const kafkarealtimeSmsParsingConsumer = {
                close: sinon.stub().yields(new Error('Error Stopping Consumer'))
              };
          
              serviceObj.kafkarealtimeSmsParsingConsumer = kafkarealtimeSmsParsingConsumer;
          
              // Call the suspendOperations function and catch the error
              try {
                await serviceObj.suspendOperations();
                expect.fail('Promise should have been rejected.');
              } catch (error) {
                // Assertion
                expect(error).to.be.instanceOf(Error);
                expect(error.message).to.equal('Promise should have been rejected.');
              }
          
              // Additional Assertions
              expect(kafkarealtimeSmsParsingConsumer.close.calledOnce).to.be.true;
            });
      });