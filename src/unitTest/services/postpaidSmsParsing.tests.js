/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import utility from '../../lib/index'

import SERVICE from '../../services'
import STARTUP_MOCK from '../__mocks__/startUp'
import postpaidSmsParsing from '../../services/smsParsingBillPayment/postpaidSmsParsing';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("Module: postpaid SMS Parsing suite :: validation record", function () {
    let serviceObj;

    let data, record;
    before(function () {    
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
            done();
        });
    });

    

    it("initializeVariable : update variables if updated in config",()=>{
        let og = _.get(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{});
        _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],{airtel : 'bills_airtel2'});
        
        serviceObj.initializeVariable()
        assert.deepEqual(serviceObj.recent_bills_operators.airtel,'bills_airtel2')
        // /** Revert to original table */
        _.set(serviceObj.config, ['OPERATOR_TABLE_REGISTRY'],og);
        serviceObj.initializeVariable()
    })
      

    it("executeStrategy | Empty SMS record",()=>{

        let stub1 = sinon.stub(serviceObj, "processRecord").returns(null);
        let record = null;
        serviceObj.executeStrategy((error)=>{
            if (error) {
                expect(error).to.be.equal(null);
            }
        },record);
        expect(stub1).to.have.callCount(0);
        stub1.restore();
    });
    
    it("executeStrategy |  valid SMS record",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2023-01-15"}};
        let stub1 = sinon.stub(serviceObj, "processRecord").returns();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        serviceObj.executeStrategy((error)=>{
            if (error) {
                expect(error).to.be.equal(null);
            }
        },record);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(0);
        stub1.restore();
        stub2.restore();
    });
    
    it("processRecord |  validateAndProcessRecord | Invalid record ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('invalid_record');
        },record);
    });

    it("processRecord |  validateAndProcessRecord | Invalid record 2",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('invalid_record');
        },null);
    });
    // it("processRecord |  validateAndProcessRecord | smsDateTime length 10",()=>{
    //     let record = {"appCount":2,"isRuSmsParsing":true,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2023-01-15"}};
    //     serviceObj.validateAndProcessRecord((errorResponse)=>{
    //         expect(errorResponse).to.be.equal(null);
    //     },record);
    // });

    it("processRecord |  validateAndProcessRecord | smsDateTime length 10",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2023-01-15"}};
        serviceObj.validateAndProcessRecord((errorResponse)=>{
            expect(errorResponse).to.be.equal(null);
        },record);
    });
   
    it("processRecord |  validateAndProcessRecord | productId missing ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2023-01-15"}};
        
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal(null);
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(29.9);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.telecom_details.due_date).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });

    it("processRecord |  validateAndProcessRecord | customerId missing ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2023-01-15"}};
        _.set(serviceObj.config,['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', 'airtel', 'PRODUCT_ID'],341207918);
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('Mandatory Params customerId is Missing / Invalid');
            expect(processedRecord.customerId).to.be.equal(null);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(29.9);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.telecom_details.due_date).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });
   
    it("processRecord |  validateAndProcessRecord | rechargeNumber missing ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2023-01-15"}};
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('Mandatory Params rechargeNumber is Missing / Invalid');
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal(null);
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(29.9);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.telecom_details.due_date).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });

    it("processRecord |  validateAndProcessRecord | dueDate missing ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9"}};
       serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.dueDate).to.be.equal(null);
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(29.9);
            expect(processedRecord.dueDate).to.be.equal(null);
        },record);
    });
     
    it("processRecord |  validateAndProcessRecord | amount<=0 ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":'-2',"due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
          //  expect(errorResponse).to.be.equal(`Either amount < 0 || dueDate is invalid || dueDate in past rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`);
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(-2);
        },record);
    });
    
    it("processRecord |  validateAndProcessRecord | Amount lessthan minAmount ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":'9',"due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
          //  expect(errorResponse).to.be.equal(`Amount lessthan minAmount`);
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(9);
        },record);
    });

    it("processRecord |  validateAndProcessRecord | Amount greater than maxAmount ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":1000000,"due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
          //  expect(errorResponse).to.be.equal(`Amount greater than maxAmount`);
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(1000000);
        },record);
    }); 

    it("processRecord |  validateAndProcessRecord | dueDate in past ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":"2022-01-15"}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
       // expect(errorResponse).to.be.equal(`Either amount < 0 || dueDate is invalid || dueDate in past rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`);
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(29.9);
            expect(processedRecord.dueDate).to.be.equal(MOMENT(record.telecom_details.due_date).endOf('day').format('YYYY-MM-DD HH:mm:ss'));
        },record);
    });

    it("processRecord |  validateAndProcessRecord | success | valid record ",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":100,"due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
           // expect(errorResponse).to.be.equal(null);
            expect(processedRecord.customerId).to.be.equal(1235);
            expect(processedRecord.rechargeNumber).to.be.equal('1234567890');
            expect(processedRecord.operator).to.be.equal("airtel");
            expect(processedRecord.service).to.be.equal("mobile");
            expect(processedRecord.paytype).to.be.equal("postpaid");
            expect(processedRecord.productId).to.be.equal(341207918);
            expect(processedRecord.amount).to.be.equal(100);
        },record);
    });

    it("processRecord |  validateAndProcessRecord | rechargeNumber Invalid | contains alphanumeric character",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"12345678a90","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('rechargeNumber Invalid');
            expect(processedRecord.customerId).to.be.equal(undefined);
            expect(processedRecord.rechargeNumber).to.be.equal(undefined);
        },record);
    });

    it("processRecord |  validateAndProcessRecord | rechargeNumber Invalid | contains special character",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"12345678#90","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('rechargeNumber Invalid');
            expect(processedRecord.customerId).to.be.equal(undefined);
            expect(processedRecord.rechargeNumber).to.be.equal(undefined);
        },record);
    });

    it("processRecord |  validateAndProcessRecord | rechargeNumber Invalid | contains less than 10 digit",()=>{
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234590","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        serviceObj.validateAndProcessRecord((errorResponse,processedRecord)=>{
            expect(errorResponse).to.be.equal('rechargeNumber Invalid');
            expect(processedRecord.customerId).to.be.equal(undefined);
            expect(processedRecord.rechargeNumber).to.be.equal(undefined);
        },record);
    });

    it('processRecord | getNextBillFetchDate:: Get next_bill_fetch_date billDate based operator', () => {
        let record = {
            operator: 'airtel',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        serviceObj.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['airtel'];
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT().add(20, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    it('processRecord | getNextBillFetchDate:: Get next_bill_fetch_date dueDate based operator', () => {
        let record = {
            operator: 'power & electricity department - mizoram',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT(record.dueDate).add(20, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    it('processRecord | getNextBillFetchDate:: Get next_bill_fetch_date operator whose config is not present', () => {
        let record = {
            operator: 'qwert',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT(record.dueDate).add(20, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    it('processRecord | getNextBillFetchDate:: Get next_bill_fetch_date billDate based operator and billDate is absent', () => {
        let record = {
            operator: 'airtel',
            billDate: null,
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        serviceObj.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['airtel'];
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT().add(35, 'days').format('YYYY-MM-DD 00:00:00'));
    })

    it('processRecord | getNextBillFetchDate:: Get next_bill_fetch_date dueDate based operator and NBFD < now()', () => {
        let record = {
            operator: 'airtel',
            billDate: null,
            dueDate: MOMENT().add(-45,'days').format('YYYY-MM-DD')
        }
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT().add(1, 'days').format('YYYY-MM-DD HH:mm:ss'));  // case value other than mid-night time is stored in DB  
    })
    //when we have to add 30 days. we will be adding 1 month instead of adding days to maintain bill cycle
    it('processRecord | getNextBillFetchDate:: Get next_bill_fetch_date when NEXT_BILL_FETCH_DATES is -1', () => {
        let record = {
            operator: 'jio',
            billDate: MOMENT().format('YYYY-MM-DD'),
            dueDate: MOMENT().add(15,'days').format('YYYY-MM-DD')
        }
        serviceObj.config.SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS = ['jio'];
        let result = serviceObj.getNextBillFetchDate(record);
        expect(result).to.be.equal(MOMENT(record.billDate).add(1, 'months').format('YYYY-MM-DD HH:mm:ss'));  // case value other than mid-night time is stored in DB  
    })

});

describe('Async function ingestIncomingPayloads', () => {

    let serviceObj;

    before(async function () {  
        try{
            await new Promise((resolve, reject) => {
                STARTUP_MOCK.init(function(error, options){
                  if (error) {
                    return reject(error);
                  }
                  serviceObj = new postpaidSmsParsing(options);
                  resolve();
                });
              });
        }catch(err){
            console.log(error);
        }
    });
  
    it('should ingest payload successfully into the database', async () => {
      const fakeRecord = {
      };
      const fakeProcessedRecord = {
      };
      let table = "table";
      let stub1 = sinon.stub(serviceObj.bills, 'ingestRealtimePayloads').callsFake(({},table,callback)=> {
        return callback(null, 'Insertion successful');
    });
      
      const result = await serviceObj.ingestIncomingPayloads(null, fakeProcessedRecord, fakeRecord);
      console.log("check result", result);
      
      assert.isNull(result);
      expect(stub1).to.have.callCount(1);

      stub1.restore();
  
      });

    it('should handle payload ingestion error', async () => {
        const fakeRecord = {
            dueDate:"2023-07-24"
        };
        const fakeProcessedRecord = {
          
        };
        let table = "table";
        let stub1 = sinon.stub(serviceObj.bills, 'ingestRealtimePayloads').callsFake((dbRecord, table, callback) => {
          
          callback(new Error('DB insertion error'));
        });
    
        
        const result = await serviceObj.ingestIncomingPayloads(null, fakeProcessedRecord, fakeRecord);
    
        
        assert.isNull(result);
        expect(stub1).to.have.callCount(1);
    
        stub1.restore();
      });
  });


  describe('Async function getRecordsFromDb', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
    });
})
  
    it('should handle error from bills.getBillsOfSameRech', (done) => {
      
      const billsMock = {
        getBillsOfSameRech: sinon.stub().callsFake((callback) => {
          callback(new Error('DB query error'), null);
        }),
      };
      let fakeRecord={};
  
      serviceObj.bills = billsMock; 
  
        serviceObj.getRecordsFromDb((err, result) => {
       
        assert.instanceOf(err, Error);
        assert.isFalse(result); 
        done();
      }, fakeRecord);
  
      });
  
    it('should handle no data found', (done) => {
      const billsMock = {
        getBillsOfSameRech: sinon.stub().callsFake((callback) => {
          callback(null, []); 
        }),
      };
  
      let fakeRecord={};

      serviceObj.bills = billsMock; 
  
        serviceObj.getRecordsFromDb((err, result) => {
        assert.isNull(err); 
        assert.isFalse(result); 
        done();
      }, fakeRecord);
  
      });
  
    it('should handle data found', (done) => {
      
      const data = [
        {}
      ];
      const billsMock = {
        getBillsOfSameRech: sinon.stub().callsFake((callback) => {
          callback(null, data);
        }),
      };
      let fakeRecord={};

  
      serviceObj.bills = billsMock; 
  
        serviceObj.getRecordsFromDb((err, result) => {
        assert.isNull(err); 
        assert.isTrue(result); 
        
        done();
      }, fakeRecord);
  
      });
  
    
  });


  describe('Async function publishToAutomaticSync', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
      });
    })
  
    it('should publish data to AUTOMATIC_SYNC_TOPIC', (done) => {
      const fakeRecord = {
        "service":"mobile",
            "operator":"airtel",
            "rechargeNumber":"12345",
            "customerId":1234,
            "productId":124,
            "is_automatic":1,
            dbData:[
                {rechargeNumber: "12345",
            custId:12345}
            ]
      };
      
      const dbData = [
        {rechargeNumber: "12345",
        custId:12345}
      ];
  
      
      const mappedRow = {
        rechargeNumber: "12345",
        custId:12345,
        is_automatic:1
      };
      const commonLibMock = {
        mapBillsTableColumns: sinon.stub().returns(mappedRow),
      };
  
      // Mock parent.kafkaPublisher.publishData to call the callback with no error (success case)
      const kafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(null);
        }),
      };
  
      serviceObj.commonLib = commonLibMock; 
      serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };
        serviceObj.publishToAutomaticSync((error) => {
        // Check if the error is null, indicating successful publishing
        assert.isNull(error);
        done();
      }, fakeRecord);
  
      });
  
    it('should handle error during data publishing', (done) => {
        const fakeRecord = {
            "service":"mobile",
            "operator":"airtel",
            "rechargeNumber":"12345",
            "customerId":1234,
            "productId":124,
            dbData:[
                {rechargeNumber: "12345",
            custId:12345}
            ]
          };
          
          const dbData = [
            {rechargeNumber: "12345",
            custId:12345}
          ];
      
          
          const mappedRow = {
            rechargeNumber: "12345",
            custId:12345,
            is_automatic:1
          };
      const commonLibMock = {
        mapBillsTableColumns: sinon.stub().returns(mappedRow),
      };
  
      
      const kafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('Kafka publish error'));
        }),
      };
  
      serviceObj.commonLib = commonLibMock; 
      serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };
  
        serviceObj.publishToAutomaticSync((error) => {
       
        assert.isNull(error);
        done();
      }, fakeRecord);
  
      });

    it('should handle error during data publishing | is_automatic=0', (done) => {
        const fakeRecord = {
            "service":"mobile",
            "operator":"airtel",
            "rechargeNumber":"12345",
            "customerId":1234,
            "productId":124,
            dbData:[
                {rechargeNumber: "12345",
            custId:12345}
            ]
          };
          
          const dbData = [
            {rechargeNumber: "12345",
            custId:12345}
          ];
      
          
          const mappedRow = {
            rechargeNumber: "12345",
            custId:12345,
          };
      const commonLibMock = {
        mapBillsTableColumns: sinon.stub().returns(mappedRow),
      };
  
      
      const kafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('Kafka publish error'));
        }),
      };
  
      serviceObj.commonLib = commonLibMock; 
      serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };
  
        serviceObj.publishToAutomaticSync((error) => {
       
        assert.isNull(error);
        done();
      }, fakeRecord);
  
      });

    it('should handle error during data publishing | status=13', (done) => {
        const fakeRecord = {
            "service":"mobile",
            "operator":"airtel",
            "rechargeNumber":"12345",
            "customerId":1234,
            "productId":124,
            dbData:[
                {rechargeNumber: "12345",
            custId:12345,
        status:13}
            ]
          };
          
          const dbData = [
            {rechargeNumber: "12345",
            custId:12345}
          ];
      
          
          const mappedRow = {
            rechargeNumber: "12345",
            custId:12345,
          };
      const commonLibMock = {
        mapBillsTableColumns: sinon.stub().returns(mappedRow),
      };
  
      
      const kafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('Kafka publish error'));
        }),
      };
  
      serviceObj.commonLib = commonLibMock; 
      serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };
  
        serviceObj.publishToAutomaticSync((error) => {
       
        assert.isNull(error);
        done();
      }, fakeRecord);
  
      });

    it('should handle error during data publishing | incomplete details', (done) => {
        const fakeRecord = {
            "service":"mobile",
            "operator":"airtel",
            "rechargeNumber":"12345",
            "customerId":1234,
            dbData:[
                {rechargeNumber: "12345",
            custId:12345}
            ]
          };
          
          const dbData = [
            {rechargeNumber: "12345",
            custId:12345}
          ];
      
          
          const mappedRow = {
            rechargeNumber: "12345",
            custId:12345,
          };
      const commonLibMock = {
        mapBillsTableColumns: sinon.stub().returns(mappedRow),
      };
  
      
      const kafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('Kafka publish error'));
        }),
      };
  
      serviceObj.commonLib = commonLibMock; 
      serviceObj.parent = { kafkaPublisher: kafkaPublisherMock };
  
        serviceObj.publishToAutomaticSync((error) => {
       
        expect(error).to.be.equal('invalid inputs')
        done();
      }, fakeRecord);
    })
  
    
  });


  describe('Async function publishInKafka', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
      });
      });
  
    it('should publish data to all three Kafka topics', (done) => {
      const fakeRecord = {
        
        
      };
  
      // Mock publishToAutomaticSync, publishInBillFetchKafka, and publishCtEvents functions to call the callbacks without error
      const publishToAutomaticSyncMock = sinon.stub().callsFake((cb) => {
        cb(null);
      });
      const publishInBillFetchKafkaMock = sinon.stub().callsFake((cb) => {
        cb(null);
      });
      const publishCtEventsMock = sinon.stub().callsFake((cb) => {
        cb(null);
      });
  

      serviceObj.publishToAutomaticSync = publishToAutomaticSyncMock; 
      serviceObj.publishInBillFetchKafka = publishInBillFetchKafkaMock;
      serviceObj.publishCtEvents = publishCtEventsMock;
  
        serviceObj.publishInKafka((error) => {
        
        assert.isNull(error);
        done();
      }, fakeRecord);
  
      });
  
    it('should handle error during publishing', (done) => {
      const fakeRecord = {
        
        
      };
  
      
      const publishToAutomaticSyncMock = sinon.stub().callsFake((cb) => {
        cb(new Error('Automatic sync publish error'));
      });
      const publishInBillFetchKafkaMock = sinon.stub().callsFake((cb) => {
        cb(new Error('Bill fetch Kafka publish error'));
      });
      const publishCtEventsMock = sinon.stub().callsFake((cb) => {
        cb(new Error('CT events publish error'));
      });
  

      serviceObj.publishToAutomaticSync = publishToAutomaticSyncMock; 
      serviceObj.publishInBillFetchKafka = publishInBillFetchKafkaMock;
      serviceObj.publishCtEvents = publishCtEventsMock;
  
        serviceObj.publishInKafka((error) => {
       
        assert.instanceOf(error, Error);
        done();
      }, fakeRecord);
  
      });
  
    
  });


  describe('Async function publishCtEvents', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
      });
      });
  
    it('should publish data to CT_EVENTS_PUBLISHER topic', (done) => {
      const fakeRecord = {
        "service":"mobile",
        "operator":"airtel",
        "rechargeNumber":"12345",
        "customerId":1234,
        dbData:[
            {rechargeNumber: "12345",
        custId:12345}
        ]
      };
  
      const dbData = [
        {rechargeNumber: "12345",
        custId:12345}
      ];
  
      
      const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(null);
      });
  
      
      const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
        cb(null);
      });
  
      
      const ctKafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(null);
        }),
      };
  

      serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
      serviceObj.commonLib.getCvrData = getCvrDataMock;
      serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };

        serviceObj.publishCtEvents((error) => {
        
        assert.isNull(error);
        expect(getRetailerDataMock).to.have.been.calledOnce;
        expect(getCvrDataMock).to.have.been.calledOnce;
        expect(ctKafkaPublisherMock.publishData).to.have.been.calledOnce;
        done();
      }, fakeRecord);
  
      });
  
    it('status=13 not publishing', (done) => {
        const fakeRecord = {
          "service":"mobile",
          "operator":"airtel",
          "rechargeNumber":"12345",
          "customerId":1234,
          dbData:[
              {rechargeNumber: "12345",
          custId:12345,
        status:13}
          ]
        };
    
        const dbData = [
          {rechargeNumber: "12345",
          custId:12345}
        ];
    
        
        const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
          cb(null);
        });
    
        
        const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
          cb(null);
        });
    
        
        const ctKafkaPublisherMock = {
          publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
            callback(null);
          }),
        };
    
  
        serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
        serviceObj.commonLib.getCvrData = getCvrDataMock;
        serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };
  
            serviceObj.publishCtEvents((error) => {
          
        assert.isNull(error);
        expect(getRetailerDataMock).to.have.callCount(0);
        expect(getCvrDataMock).to.have.callCount(0);
        expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
          done();
        }, fakeRecord);
    
          });

      it('notification_status=0 not publishing', (done) => {
        const fakeRecord = {
          "service":"mobile",
          "operator":"airtel",
          "rechargeNumber":"12345",
          "customerId":1234,
          dbData:[
              {rechargeNumber: "12345",
          custId:12345,
        notification_status:0}
          ]
        };
    
        const dbData = [
          {rechargeNumber: "12345",
          custId:12345}
        ];
    
        
        const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
          cb(null);
        });
    
        
        const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
          cb(null);
        });
    
        
        const ctKafkaPublisherMock = {
          publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
            callback(null);
          }),
        };
    
  
        serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
        serviceObj.commonLib.getCvrData = getCvrDataMock;
        serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };
  
            serviceObj.publishCtEvents((error) => {
          
        assert.isNull(error);
        expect(getRetailerDataMock).to.have.callCount(0);
        expect(getCvrDataMock).to.have.callCount(0);
        expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
          done();
        }, fakeRecord);
    
          });

    it('should handle error during publishing', (done) => {
      const fakeRecord = {
        "service":"mobile",
        "operator":"airtel",
        "rechargeNumber":"12345",
        "customerId":1234,
        dbData:[
            {rechargeNumber: "12345",
        custId:12345}
        ]
      };
  
      const dbData = [
        {rechargeNumber: "12345",
        custId:12345}
      ];
  
      
      const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(new Error('Get retailer data error'));
      });
  
      
      const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
        cb(new Error('Get CVR data error'));
      });
  
     
      const ctKafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('CT Kafka publish error'));
        }),
      };
  

      serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
      serviceObj.commonLib.getCvrData = getCvrDataMock;
      serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };
  
        serviceObj.publishCtEvents((error) => {
       
        assert.instanceOf(error, Error);
        expect(getRetailerDataMock).to.have.callCount(1);
        expect(getCvrDataMock).to.have.callCount(0);
        expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
        done();
      }, fakeRecord);
  
      });

  it('should handle error during publishing cvrmock', (done) => {
    const fakeRecord = {
      "service":"mobile",
      "operator":"airtel",
      "rechargeNumber":"12345",
      "customerId":1234,
      dbData:[
          {rechargeNumber: "12345",
      custId:12345}
      ]
    };

    const dbData = [
      {rechargeNumber: "12345",
      custId:12345}
    ];

    
    const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(null);
      });

    
    const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
      cb(new Error('Get CVR data error'));
    });

   
    const ctKafkaPublisherMock = {
      publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
        callback(new Error('CT Kafka publish error'));
      }),
    };


    serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
    serviceObj.commonLib.getCvrData = getCvrDataMock;
    serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };

    serviceObj.publishCtEvents((error) => {
     
      assert.instanceOf(error, Error);
      expect(getRetailerDataMock).to.have.callCount(1);
      expect(getCvrDataMock).to.have.callCount(1);
      expect(ctKafkaPublisherMock.publishData).to.have.callCount(0);
      done();
    }, fakeRecord);

  });

  it('should handle error during publishing ct error', (done) => {
    const fakeRecord = {
      "service":"mobile",
      "operator":"airtel",
      "rechargeNumber":"12345",
      "customerId":1234,
      dbData:[
          {rechargeNumber: "12345",
      custId:12345}
      ]
    };

    const dbData = [
      {rechargeNumber: "12345",
      custId:12345}
    ];

    
    const getRetailerDataMock = sinon.stub().callsFake((cb, customerId, dataRow) => {
        cb(null);
      });

    
    const getCvrDataMock = sinon.stub().callsFake((cb, productId, dataRow) => {
      cb(null);
    });

   
    const ctKafkaPublisherMock = {
      publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
        callback(new Error('CT Kafka publish error'));
      }),
    };


    serviceObj.commonLib.getRetailerData = getRetailerDataMock; 
    serviceObj.commonLib.getCvrData = getCvrDataMock;
    serviceObj.parent = { ctKafkaPublisher: ctKafkaPublisherMock };

    serviceObj.publishCtEvents((error) => {
     
      assert.instanceOf(error, Error);
      expect(getRetailerDataMock).to.have.callCount(1);
      expect(getCvrDataMock).to.have.callCount(1);
      expect(ctKafkaPublisherMock.publishData).to.have.callCount(1);
      done();
    }, fakeRecord);

  });
});

describe('Async function publishInBillFetchKafka', () => {
    let serviceObj;
  
    before(() => {
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
      });
     });
  
    it('should publish data to REMINDER_BILL_FETCH_REALTIME topic', (done) => {
      const fakeProcessedRecord = {
          "isRuSmsParsing":true,
          "isDwhSmsParsing":true,
          "dbData":[
            {"notification_status":1,
          "is_automatic":0}
          ]
         };
  
      const billFetchKafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(null);
        }),
      };
  
      serviceObj.parent = { billFetchKafkaPublisher: billFetchKafkaPublisherMock };

      
      serviceObj.publishInBillFetchKafka((error) => {
        
        assert.isNull(error);
  
        expect(billFetchKafkaPublisherMock.publishData).to.have.been.calledOnce;
  
        done();
      }, fakeProcessedRecord);
  
      });
  
    it('should handle error during publishing', (done) => {
      const fakeProcessedRecord = {
        "isRuSmsParsing":true,
        "isDwhSmsParsing":true,
        "dbData":[
          {"notification_status":1,
        "is_automatic":0}
        ]
      };
  
      
      const billFetchKafkaPublisherMock = {
        publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
          callback(new Error('Kafka publish error'));
        }),
      };
  
      serviceObj.parent = { billFetchKafkaPublisher: billFetchKafkaPublisherMock };
  
      
      serviceObj.publishInBillFetchKafka((error) => {
       
        assert.isNull(error);
  
        expect(billFetchKafkaPublisherMock.publishData).to.have.been.calledOnce;
  
        done();
      }, fakeProcessedRecord);
  
      });
  
    it('not publishing if isRuSmsParsing is false', (done) => {
        const fakeProcessedRecord = {
          "isRuSmsParsing":false
        };
    
        
        const billFetchKafkaPublisherMock = {
          publishData: sinon.stub().callsFake((kafkaMessages, callback) => {
            callback(new Error('Kafka publish error'));
          }),
        };
    
        serviceObj.parent = { billFetchKafkaPublisher: billFetchKafkaPublisherMock };
    
        
        serviceObj.publishInBillFetchKafka((error) => {
         
          assert.isNull(error);
    
            expect(billFetchKafkaPublisherMock.publishData).to.have.callCount(0);
    
          done();
        }, fakeProcessedRecord);
    
          });
    
  });

describe("Module: postpaid SMS Parsing suite :: Record processing", function () {
    let serviceObj;

    let data, record;
    before(function () {    
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new postpaidSmsParsing(options);
            done();
        });
    });
    
    // it("processRecords  | update Flow", (done) => {

    //     let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
    //     let processedRecord = { "operator": "airtel" ,
    //     "service": 'mobile',
    //     "customerId": 1235,
    //     "rechargeNumber": "1234567890",
    //     "gateway": null,
    //     "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    //     "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    //     "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
    //     "amount": record.amount,
    //     "status": 4,
    //     "paytype": 'postpaid',
    //     "cache": null,
    //     "service_id": 0,
    //     "customerMobile":"1234567890" ,
    //     "customerEmail": _.get(record, 'smsReceiverEmail', null),
    //     'paymentChannel': null,
    //     "retryCount": 0,
    //     "reason": null,
    //     "extra": null,
    //     "customer_type":null, 
    //     "paymentDate": null,
    //     "user_data":null,
    //     "circle":'all circles',
    //     "productId": 11,
    //     "tableName" : 'bills_airtel',
    //     "recordFoundOfSameCustId":true
    //     };

    //     let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
    //         return done(null, processedRecord);
    //     });

    //     let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
    //         return done(null, 'update', processedRecord);
    //     });

    //     let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
    //         return done(null, 'update', processedRecord);
    //     });

    //     let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
    //         return done(null, processedRecord);
    //     });

    //     let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
    //         return done(null, processedRecord);
    //     });

    //     // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
    //     //     return done(null, processedRecord);
    //     // });


    //     serviceObj.processRecord(record, function (error) {
    //         expect(error).to.be.equal(undefined);
    //         expect(stub1).to.have.callCount(1);
    //         expect(stub2).to.have.callCount(1);
    //         expect(stub3).to.have.callCount(0);
    //         // expect(stub4).to.have.callCount(1);
    //         expect(stub5).to.have.callCount(1);
    //         // expect(stub6).to.have.callCount(1);

    //         stub1.restore();
    //         stub2.restore();
    //         stub3.restore();
    //         stub4.restore();
    //         stub5.restore();
    //         // stub6.restore();

    //         return done();
    //     });
    // });

    it("processRecords  | error in validate function", (done) => {

        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done("error in validateAndProcessedRecord", processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
        //     return done(null, processedRecord);
        // });


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            // expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            // stub6.restore();

            return done();
        });
    });

    it("processRecords  | error in getForward action function", (done) => {

        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done("error in getForwardActionFlow");
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
        //     return done(null, processedRecord);
        // });


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            // expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            // stub6.restore();

            return done();
        });
    });

    it("processRecords  | error in update cassandra function", (done) => {

        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, "findAndUpdateToCassandra", processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done("Error in updateCassandra");
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
        //     return done(null, processedRecord);
        // });


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            // expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            // stub6.restore();

            return done();
        });
    });

    it("processRecords  | error in publishInKafka function", (done) => {

        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, "update", processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null);
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done("error in publishInKafka");
        });

        // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
        //     return done(null, processedRecord);
        // });


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            // expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            // stub6.restore();

            return done();
        });
    });

    it("processRecords  | error in update dbrecord function", (done) => {

        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, "update", processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null);
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done("Error in updatedbRecord");
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        // let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
        //     return done(null, processedRecord);
        // });


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(0);
            // expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            // stub6.restore();

            return done();
        });
    });

    it.skip("processRecords  | isRuSmsParsing:true Flow", (done) => {

        let record = {"appCount":2,"isRuSmsParsing":true,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub6 = sinon.stub(serviceObj, 'ingestIncomingPayloads').resolves(null);


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();

            return done();
        });
    });
    
    it.skip("processRecords | findAndUpdateToCassandra flow | recordNotFound", (done) => {
        
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel'
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, 'findAndUpdateToCassandra', processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null, 'findAndUpdateToCassandra', processedRecord);
        });
          
        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            return done();
        });
    });

    it.skip("processRecords  | isRuSmsParsing:true error in ingestions Flow", (done) => {

        let record = {"appCount":2,"isRuSmsParsing":true,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":true
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null, 'update', processedRecord);
        });

        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub6 = sinon.stub(serviceObj, 'ingestIncomingPayloads').rejects("error in ingestIncomingPayloads");


        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(0);
            expect(stub4).to.have.callCount(1);
            expect(stub5).to.have.callCount(1);
            expect(stub6).to.have.callCount(1);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();

            return done();
        });
    });

    it.skip("processRecords | Cassandra insert | record Not Found Of SameCustId", (done) => {
        
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":false
        };

        let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub2 = sinon.stub(serviceObj, 'getForwardActionFlow').callsFake(function fakeFn(done) {
            return done(null, 'skip_update', processedRecord);
        });

        let stub3 = sinon.stub(serviceObj, 'updateCassandra').callsFake(function fakeFn(done) {
            return done(null, 'skip_update', processedRecord);
        });
          
        let stub4 = sinon.stub(serviceObj, 'updateDbRecord').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub5 = sinon.stub(serviceObj, 'publishInKafka').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        let stub6 = sinon.stub(serviceObj, 'publishCtEvents').callsFake(function fakeFn(done) {
            return done(null, processedRecord);
        });

        serviceObj.processRecord(record, function (error) {
            expect(error).to.be.equal(undefined);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            expect(stub3).to.have.callCount(1);
            expect(stub4).to.have.callCount(0);
            expect(stub5).to.have.callCount(0);
            expect(stub6).to.have.callCount(0);

            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            stub6.restore();
            return done();
        });
    });

    it.skip("getForwardActionFlow | record Not Found in DB | action : findAndUpdateToCassandra", (done) => {
        
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":false
        };


        let stub1 = sinon.stub(serviceObj, 'getRecordsFromDb').callsFake(function fakeFn(done) {
            return done(null, false);
        });

        serviceObj.getForwardActionFlow((errorResponse,action)=>{
            expect(errorResponse).to.be.equal(null);
            expect(action).to.be.equal('findAndUpdateToCassandra');
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return done();
        },processedRecord);

    });

    it.skip("getForwardActionFlow | record Found in DB | action : update", (done) => {
        
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":MOMENT().add(1, 'months').format('YYYY-MM-DD')}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": '2023-03-11',
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":false,
        "noOfFoundRecord":1,
        "dbData":[{'due_date': '2022-03-12'}]
        };


        let stub1 = sinon.stub(serviceObj, 'getRecordsFromDb').callsFake(function fakeFn(done) {
            return done(null, true);
        });

        serviceObj.getForwardActionFlow((errorResponse,action)=>{
            expect(errorResponse).to.be.equal(null);
            expect(action).to.be.equal('update');
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return done();
        },processedRecord);

    });

    it.skip("getForwardActionFlow | record Found in DB| smsDueDate<= dbDueDate | action : skip_update", (done) => {
        
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":false,
        "noOfFoundRecord":1,
        "dbData":[{'due_date': '2060-03-12'}]
        };


        let stub1 = sinon.stub(serviceObj, 'getRecordsFromDb').callsFake(function fakeFn(done) {
            return done(null, true);
        });

        serviceObj.getForwardActionFlow((errorResponse,action)=>{
            expect(errorResponse).to.be.equal('smsDueDate less than dbDueDate');
            // expect(action).to.be.equal('skip_update');
            expect(stub1).to.have.callCount(1);
            stub1.restore();
            return done();
        },processedRecord);

    });

    // it("updateDbRecord | No activeRecordsInDB", (done) => {
        
    //     let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
    //     let processedRecord = { "operator": "airtel" ,
    //     "service": 'mobile',
    //     "customerId": 1235,
    //     "rechargeNumber": "1234567890",
    //     "gateway": null,
    //     "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
    //     "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
    //     "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
    //     "amount": record.amount,
    //     "status": 4,
    //     "paytype": 'postpaid',
    //     "cache": null,
    //     "service_id": 0,
    //     "customerMobile":"1234567890" ,
    //     "customerEmail": _.get(record, 'smsReceiverEmail', null),
    //     'paymentChannel': null,
    //     "retryCount": 0,
    //     "reason": null,
    //     "extra": null,
    //     "customer_type":null, 
    //     "paymentDate": null,
    //     "user_data":null,
    //     "circle":'all circles',
    //     "productId": 11,
    //     "tableName" : 'bills_airtel',
    //     "recordFoundOfSameCustId":false,
    //     "noOfFoundRecord":1,
    //     "dbData":[{'due_date': '2060-03-12'}],
    //     "activeRecordsInDB":0
    //     };


    //     let stub1 = sinon.stub(serviceObj.bills, 'updateBillForSameRechargeNum').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });
    //     let stub2 = sinon.stub(serviceObj, 'updateBillsInRecent').callsFake(function fakeFn(done) {
    //         return done(null);
    //     });

    //     serviceObj.updateDbRecord((errorResponse)=>{
    //         expect(errorResponse).to.be.equal(null);
    //         expect(stub1).to.have.callCount(0);
    //         expect(stub2).to.have.callCount(0);
    //         stub1.restore();
    //         stub2.restore();
    //         return done();
    //     },processedRecord);

    // });

    it.skip("updateDbRecord | updated successfully", (done) => {
        
        let record = {"appCount":2,"appVersion":"10.20.0","batteryPercentage":88,"brand":"OPPO","cId":"1235","clientId":"androidapp","db_name":"sms_parsed_data","deviceDateTime":1671600355297,"eventType":"smsEvent","event_name":"sms","uploadTime":1671600355297,"collector_timestamp":1671600356146,"timestamp":"Wed Dec 21 05:25:56 UTC 2022","true_client_ip":"2001:df5:2380:77bd:c8a:79d8:30fc:b024","smsBody":"Pack Valid till 28 Jan 2023. Remaining SMS:899 Bal:Rs.29.90.","smsDateTime":1671354044679,"smsOperator":"airtel","smsReceiver":"1234567890","smsSenderID":"AT-AIRTEL","smsUUID":"e8118e9a-dc27-4891-b96e-1b86aab27ba9","predicted_category":"telecom","level_2_category":5,"telecom_details":{"operator":"AIRTEL","due_amount":"29.9","due_date":String(MOMENT().add(1, 'months').format('YYYY-MM-DD'))}};
        let processedRecord = { "operator": "airtel" ,
        "service": 'mobile',
        "customerId": 1235,
        "rechargeNumber": "1234567890",
        "gateway": null,
        "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        "billDate":  MOMENT().endOf('day').format('YYYY-MM-DD'),
        "dueDate": record.telecom_details.due_date ? (record.telecom_details.due_date) : null,
        "amount": record.amount,
        "status": 4,
        "paytype": 'postpaid',
        "cache": null,
        "service_id": 0,
        "customerMobile":"1234567890" ,
        "customerEmail": _.get(record, 'smsReceiverEmail', null),
        'paymentChannel': null,
        "retryCount": 0,
        "reason": null,
        "extra": null,
        "customer_type":null, 
        "paymentDate": null,
        "user_data":null,
        "circle":'all circles',
        "productId": 11,
        "tableName" : 'bills_airtel',
        "recordFoundOfSameCustId":false,
        "noOfFoundRecord":1,
        "dbData":[{'due_date': '2060-03-12'}],
        "activeRecordsInDB":2
        };


        let stub1 = sinon.stub(serviceObj.bills, 'updateBillForSameRechargeNumPostpaid').callsFake(function fakeFn(done) {
            return done(null);
        });
        let stub2 = sinon.stub(serviceObj, 'updateBillsInRecent').callsFake(function fakeFn(done) {
            return done(null);
        });


        serviceObj.updateDbRecord((errorResponse)=>{
            expect(errorResponse).to.be.equal(null);
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(1);
            stub1.restore();
            stub2.restore();
            return done();
        },processedRecord);

    });
   
    
});