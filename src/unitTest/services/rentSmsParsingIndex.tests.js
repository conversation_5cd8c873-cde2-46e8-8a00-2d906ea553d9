/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import sinon from 'sinon';
import _ from 'lodash'
import utility from '../../lib/index'

import STARTUP_MOCK from '../__mocks__/startUp'

import RentSMSParsingBillPayment from '../../services/smsParsingBillPayment/rentSmsParsingIndex';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer validations", function () {
    let serviceObj;

    before(function () {
        STARTUP_MOCK.init(function (error, options) {
            serviceObj = new RentSMSParsingBillPayment(options);
            done();
        });
    });
    // it("testing configureKafka function | Error case from initProducer function", () => {
    //     serviceObj.producer = new serviceObj.infraUtils.kafka.producer();
    //     let stub1 = sinon.stub(serviceObj.producer, "initProducer").callsFake(function fakeFn(callback) {
    //         return callback("Error from initProducer");
    //     });
    //     serviceObj.configureKafka((error) => {
    //         if (error) {
    //             expect(error).to.be.equal("Error from initProducer");
    //         }
    //     });
    //     stub1.restore();
    // });
    it("testing configureKafka function | Error case from initConsumer function", () => {
        serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
        sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback) {
            return callback("Error from initConsumer");
        });
        serviceObj.configureKafka((error) => {
            if (error) {
                expect(error).to.be.equal("Error from initConsumer");
            }
        });
    });
    it("testing configureKafka function | success ", () => {
        //serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
        // sinon.stub(serviceObj.consumer, "initConsumer").callsFake(function fakeFn(callback){
        //     return callback(null);
        // });
        serviceObj.configureKafka((error) => {
            if (error) {
                expect(error).to.be.equal(null);
            }
        });
    });
    it("testing configureKafka function | success ", () => {
        // serviceObj.consumer = new serviceObj.infraUtils.kafka.consumer();
        // sinon.stub(serviceObj.consumer, "initConsumer").returns(null);
        serviceObj.configureKafka((error) => {
            if (error) {
                expect(error).to.be.equal(null);
            }
        });
    });

    it("execSteps || ensure empty records are validated", () => {
        let record = {}
        serviceObj.consumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb();
            },
            _resumeConsumer: () => {
            }
        };
        let processedRecords = serviceObj.execSteps(record);
        expect(processedRecords).to.be.equal(undefined)
    })
    it.skip("execSteps || Error in commitOffset function", () => {
        let record = [{}];
        let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        sinon.stub(process, 'exit');
        let clock = sinon.useFakeTimers();
        serviceObj.kafkasmsParsingBillPaymentConsumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb("Error in commitOffset");
            },
            _resumeConsumer: () => {
            }
        };
        serviceObj.greyScaleEnv = true;

        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
        process.exit.restore();
    })
    it.skip("execSteps || greyScaleEnv || commitOffset function", () => {
        let record = [{}
        ];
        let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        let clock = sinon.useFakeTimers();

        serviceObj.kafkasmsParsingBillPaymentConsumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb();
            },
            _resumeConsumer: () => {

            }
        };
        serviceObj.greyScaleEnv = true;

        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    })
    it.skip("execSteps || greyScaleEnv || commitOffset function", () => {
        let record = [{}
        ];
        let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        let clock = sinon.useFakeTimers();

        serviceObj.kafkasmsParsingBillPaymentConsumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb();
            },
            _resumeConsumer: () => {
            }
        };
        serviceObj.greyScaleEnv = true;

        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    })
    it.skip("execSteps || greyScaleEnv || Error in commitOffset function", () => {
        let record = [{}];
        let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        sinon.stub(process, 'exit');
        let clock = sinon.useFakeTimers();
        serviceObj.kafkasmsParsingBillPaymentConsumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb("Error in commitOffset");
            },
            _resumeConsumer: () => {
            }
        };
        serviceObj.greyScaleEnv = true;

        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
        process.exit.restore();
    })
    it.skip("execSteps || greyScaleEnv || commitOffset function", () => {
        let record = [
        ];
        let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        let clock = sinon.useFakeTimers();

        serviceObj.kafkasmsParsingBillPaymentConsumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb();
            },
            _resumeConsumer: () => {

            }
        };
        serviceObj.greyScaleEnv = true;

        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(0);
        expect(stub2).to.have.callCount(2);
        stub1.restore();
        stub2.restore();
    })
    it.skip("execSteps || greyScaleEnv || Error in commitOffset function", () => {
        let record = [];
        let stub1 = sinon.stub(serviceObj, "processBatch").rejects();
        let stub2 = sinon.stub(utility, "_sendMetricsToDD").returns(null);
        sinon.stub(process, 'exit');
        let clock = sinon.useFakeTimers();
        serviceObj.kafkasmsParsingBillPaymentConsumer = {
            _pauseConsumer: () => {
                return;
            },
            commitOffset: (data, cb) => {
                return cb("Error in commitOffset");
            },
            _resumeConsumer: () => {
            }
        };
        serviceObj.greyScaleEnv = true;

        let processedRecords = serviceObj.execSteps(record);
        clock.tick(40 * 60 * 1000)
        expect(processedRecords).to.be.equal(undefined);
        expect(stub1).to.have.callCount(0);
        expect(stub2).to.have.callCount(3);
        stub1.restore();
        stub2.restore();
        process.exit.restore();
    })
    it("start function || configureKafka error case", () => {
        let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
        sinon.stub(process, 'exit');
        let stub2 = sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback) {
            return callback("configureKafka error response callback");
        });
        serviceObj.start((error) => {
            if (error) {
                expect(error).to.be.equal(undefined);
            }
        });
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(1);
        process.exit.restore();
        stub1.restore();
        stub2.restore();
    })
    it("start function || Error in configureKafka", () => {
        let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
        serviceObj.configureKafka = (callback) => {
            return callback("Error in configureKafka");
        }

        sinon.stub(process, 'exit');
        let clock = sinon.useFakeTimers();
        serviceObj.start((error) => {
            if (error) {
                expect(error).to.be.equal(undefined);
            }
        });
        expect(stub1).to.have.callCount(1);
        clock.tick(86400000)
        process.exit.restore();
        stub1.restore();
    })
    it("start function || configureKafka success case", () => {
        let stub1 = sinon.stub(serviceObj.smsParsingSyncCCBillLib, "refreshDCATCacheData").returns(null);
        let stub2 = sinon.stub(serviceObj, "configureKafka").callsFake(function fakeFn(callback) {
            return callback();
        });
        serviceObj.start((error) => {
            if (error) {
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(1);
        expect(stub2).to.have.callCount(1);
        stub1.restore();
        stub2.restore();
    })
});

describe("Module: SMS Parsing Bill Payment suite :: Kafka consumer", function () {
    let serviceObj;

    let record;
    before(function () {
        STARTUP_MOCK.init(function (error, options) {
            serviceObj = new RentSMSParsingBillPayment(options);
            done();
        });
    });

    it("testing processBatch | execution count", () => {
        let data = [
            {
                "topic": "SMS_PARSING_RENT",
                "value": "{\n\t\"data\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"1\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
                "offset": 1,
                "partition": 0,
                "highWaterOffset": 2,
                "key": null,
                "timestamp": "2023-11-23T02:56:47.903Z"
            }
        ];
        let stub1 = sinon.stub(serviceObj, 'processData').callsFake(function fakeFn(callback) {
            setTimeout(() => {
                callback();
            }, 1)
        });
        serviceObj.processBatch(data, function (error) {
            if (error) {
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(1);
        stub1.restore();
    });

    it("testing processData | correct payload for level_2_category = 1", () => {
        let data = {
            "topic": "SMS_PARSING_RENT",
            "value": "{\n\t\"data\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"1\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
            "offset": 1,
            "partition": 0,
            "highWaterOffset": 2,
            "key": null,
            "timestamp": "2023-11-23T02:56:47.903Z"
        }; 
        let stub1 = sinon.stub(serviceObj.houseRentSmsParsing, 'executeStrategy').returns(null);
        serviceObj.processData(data,function(error){
            if(error){
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(1);
        stub1.restore();
    });

    it("testing processData | correct payload for level_2_category = 2", () => {
        let data = {
            "topic": "SMS_PARSING_RENT",
            "value": "{\n\t\"data\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"2\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
            "offset": 1,
            "partition": 0,
            "highWaterOffset": 2,
            "key": null,
            "timestamp": "2023-11-23T02:56:47.903Z"
        }; 
        let stub1 = sinon.stub(serviceObj.houseRentSmsParsing, 'executeStrategy').returns(null);
        serviceObj.processData(data,function(error){
            if(error){
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(1);
        stub1.restore();
    });
    
    it("testing processData | incorrect payload level_2_category other than 1 or 2", () => {
        let data = {
            "topic": "SMS_PARSING_RENT",
            "value": "{\n\t\"data\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"0\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
            "offset": 1,
            "partition": 0,
            "highWaterOffset": 2,
            "key": null,
            "timestamp": "2023-11-23T02:56:47.903Z"
        }; 
        let stub1 = sinon.stub(serviceObj.houseRentSmsParsing, 'executeStrategy').returns(null);
        serviceObj.processData(data, function(error){
            if(error){
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(0);
        stub1.restore();
    });

    it("testing processData | inccorrect payload with level_2_category 2", () => {
        let data = {
            "topic": "SMS_PARSING_RENT",
            "value": "{\n\t\"data2\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"2\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
            "offset": 1,
            "partition": 0,
            "highWaterOffset": 2,
            "key": null,
            "timestamp": "2023-11-23T02:56:47.903Z"
        };   
        let stub1 = sinon.stub(serviceObj.houseRentSmsParsing, 'executeStrategy').returns(null);
        serviceObj.processData(data, function(error){
            if(error){
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(0);
        stub1.restore();
    });

    it("testing processData | inccorrect payload with level_2_category 1", () => {
        let data = {
            "topic": "SMS_PARSING_RENT",
            "value": "{\n\t\"data2\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"1\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
            "offset": 1,
            "partition": 0,
            "highWaterOffset": 2,
            "key": null,
            "timestamp": "2023-11-23T02:56:47.903Z"
        };   
        let stub1 = sinon.stub(serviceObj.houseRentSmsParsing, 'executeStrategy').returns(null);
        serviceObj.processData(data, function(error){
            if(error){
                expect(error).to.be.equal(null);
            }
        });
        expect(stub1).to.have.callCount(0);
        stub1.restore();
    });

    it("testing defaultStrategy | incorrect payload", () => {
        let data = {
            "topic": "SMS_PARSING_RENT",
            "value": "{\n\t\"data2\": [\n\t\t{\n\t\t\t\"appCount\": 1,\n\t\t\t\"appVersion\": \"9.6.2\",\n\t\t\t\"netWorkType\": \"WIFI\",\n\t\t\t\"latitude\": 0,\n\t\t\t\"deviceDateTime\": *************,\n\t\t\t\"collector_timestamp\": 1698749860880,\n\t\t\t\"wakeUpTimeInterval\": null,\n\t\t\t\"osVersion\": \"28\",\n\t\t\t\"osType\": \"android\",\n\t\t\t\"model\": \"TA-1021\",\n\t\t\t\"msg_id\": \"82a066b1-ffe0-4b79-bc5e-3896b69c64ee\",\n\t\t\t\"brand\": \"Nokia\",\n\t\t\t\"user_agent\": null,\n\t\t\t\"cId\": \"227316904\",\n\t\t\t\"longitude\": 0,\n\t\t\t\"timestamp\": null,\n\t\t\t\"uploadFrequency\": null,\n\t\t\t\"clientId\": \"androidapp\",\n\t\t\t\"deviceId\": null,\n\t\t\t\"preference\": [\n\t\t\t\t{\n\t\t\t\t\t\"prefCat\": \"permission\",\n\t\t\t\t\t\"prefKeys\": \"ocl.permission.creditcard.sms_read_consent\",\n\t\t\t\t\t\"prefSubCat\": \"sms consent\"\n\t\t\t\t}\n\t\t\t],\n\t\t\t\"mId\": \"\",\n\t\t\t\"eventType\": \"smsEvent\",\n\t\t\t\"uploadTime\": *************,\n\t\t\t\"true_client_ip\": null,\n\t\t\t\"realTime\": null,\n\t\t\t\"db_name\": null,\n\t\t\t\"newUser\": null,\n\t\t\t\"event_name\": \"sms\",\n\t\t\t\"batteryPercentage\": 100,\n\t\t\t\"smsUUID\": \"499a6de3-9f6b-4295-aab1-814d647ce0f4\",\n\t\t\t\"smsDateTime\": *************,\n\t\t\t\"smsBody\": \"Rs. 33660 was spent on your Credit Card ending 8009 at PHONEPE RENTAL 08042 on 07-OCT-23 01:45:56 . Avail Limit is 1340 - Union Bank of India\",\n\t\t\t\"smsSenderID\": \"AX-UNIONB\",\n\t\t\t\"smsReceiver\": \"**********\",\n\t\t\t\"smsOperator\": \"Airtel\",\n\t\t\t\"smsRecSubId\": 0,\n\t\t\t\"lastCC\": \"8009\",\n\t\t\t\"amount\": 7000,\n\t\t\t\"competitor\": \"phonepe\",\n\t\t\t\"dueDate\": \"2023-09-29\",\n\t\t\t\"level_2_category\": \"1\",\n\t\t\t\"predicted_category\": \"rent\"\n\t\t}\n\t],\n\t\"kafka_topic\": [\n\t\t\"SMS_PARSING_RENT\"\n\t]\n}",
            "offset": 1,
            "partition": 0,
            "highWaterOffset": 2,
            "key": null,
            "timestamp": "2023-11-23T02:56:47.903Z"
        };   
        let result = serviceObj.defaultStrategy(function(error){
            if(error){
                expect(error).to.be.equal(null);
            }
        },data);
        expect(result).to.equal(undefined);
    });

});
