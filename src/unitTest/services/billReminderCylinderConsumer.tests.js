/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    
    
    import chai from "chai";
    import MOMENT from 'moment';
    import sinonChai from "sinon-chai";
    import BillReminderCylinderConsumer from '../../services/billReminderCylinderConsumer';
    import L from 'lgr'
    import config from '../../config'
    import _ from 'lodash';
    import helper from '../__mocks__';
    import sinon from 'sinon';
    import chaiAsPromised from "chai-as-promised";
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    describe("Module publisher:: Bill Reminder Cylinder Consumer :: Kafka consumer validations", function () {
        let billReminderCylinderObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){                
                billReminderCylinderObj = new BillReminderCylinderConsumer(options);
                // done();
            });
        });
    
    
        it("testing initialize consumer | default values", () => {
            let cb = sinon.spy();
            billReminderCylinderObj.configureKafka(cb);
            expect(cb).to.have.callCount(1);
            expect(cb).to.have.calledWith(null)
            
        });
    
    
        it("start service || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(billReminderCylinderObj, 'configureKafka').yields(null);
            billReminderCylinderObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        });
    
        it("processKafkaData || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = billReminderCylinderObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
    
    });
        
    describe("Module publisher:: Bill Reminder Cylinder Consumer :: test suite", function () {
        let billReminderCylinderObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                billReminderCylinderObj = new BillReminderCylinderConsumer(options);
                done();
            });
        });
    
        it('convertKafkaPayloadToRecord | To throw error on empty payload', (done) => {
            const kafkaPayload = {};
            expect(() =>
                billReminderCylinderObj.convertKafkaPayloadToRecord(kafkaPayload)
            ).to.throw(Error);
            done();
        });
                
        /** helper function test ases */
        it("parseAmount | Valid Record | Integer - Rs.590", () => {
            expect(billReminderCylinderObj.parseAmount("Rs.590")).to.be.equal(590);
        });
        it("parseAmount | Valid Record | Float - Rs.590.78", () => {
            expect(billReminderCylinderObj.parseAmount("Rs.590.78")).to.be.equal(590.78);
        });
        it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....-1223.34", () => {
            expect(billReminderCylinderObj.parseAmount("Rs   ....-1223.34")).to.be.equal(-1223.34);
        });
        it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....+1223.34", () => {
            expect(billReminderCylinderObj.parseAmount("Rs   ....+1223.34")).to.be.equal(1223.34);
        });
        it("parseAmount | Valid Record | zero amount -> Rs.0", () => {
            expect(billReminderCylinderObj.parseAmount("Rs.0")).to.be.equal(0);
        });
        it("parseAmount | Valid Record | without Rs. string", () => {
            expect(billReminderCylinderObj.parseAmount("590")).to.be.equal(590);
        });
        it("parseAmount | Valid Record | without Rs. string", () => {
            expect(billReminderCylinderObj.parseAmount("-590")).to.be.equal(2);
        });
        it("parseAmount | Valid Record | as Number", () => {
            expect(billReminderCylinderObj.parseAmount(590)).to.be.equal(590);
        });
        it("parseAmount | Valid Record | as Number", () => {
            expect(billReminderCylinderObj.parseAmount(590.67)).to.be.equal(590.67);
        });
        it("parseAmount | InValid Record | as null", () => {
            expect(billReminderCylinderObj.parseAmount(null)).to.be.equal(2);
        });
        it("parseAmount | InValid Record | as normal string", () => {
            expect(billReminderCylinderObj.parseAmount("amount")).to.be.equal(null);
        });
    
    });