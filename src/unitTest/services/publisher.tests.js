/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before } from 'mocha';
    import chai, { assert } from "chai";
    import sinon from 'sinon';
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import nock from 'nock'
    
    import MOMENT from 'moment'

    import PU<PERSON>ISHER from '../../services/publisher'
    
    import STARTUP_MOCK from '../__mocks__/startUp'
import _, { sortedUniqBy } from 'lodash';

    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    let token = 'dummy-token-for-ut';
    
    describe("Module: Publisher test suite", function () {
        let serviceObj;
        
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new PUBLISHER(options);
    
                done();
            });
        });
    
        it("initializeVariable : update  variable if updated in config", () => {
            let og = _.get(serviceObj.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
            _.set(serviceObj.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment2');
            serviceObj.initializeVariable()
            assert.deepEqual(serviceObj.includedOperator,['rent payment2'])
            /** Revert to original table */
            _.set(serviceObj.config,['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'],og)
        });

        it("markUnusedToOtherCustomerId | success when we have to publish", () => {
            let table="bills_jio";
            let cb = sinon.spy();
            let recordData = {"product_id":341211198,"customer_id":289829,"operator":"tuition fees","service":"tuition fees","productInfo_verticalId":76,"userData_amount":5000,"recharge_number":"17660703"};
            let records=[recordData];
            let stub1 = sinon.stub(serviceObj.dbInstance, 'exec').callsFake(function fakeFn(callback){
                return callback(null,records);
            });
            let stub2 = sinon.stub(serviceObj, 'checkIfRecordHasToBePublished').returns(false);
            serviceObj.markingUnusedToOtherCustomerId(recordData,table,cb);
            expect(stub1).to.have.callCount(2)
            expect(stub2).to.have.callCount(1)
            stub1.restore();
            stub2.restore();
        });

        it("markUnusedToOtherCustomerId | success when we dont have to publish", () => {
            let table="bills_jio";
            let cb = sinon.spy();
            let recordData = {"product_id":341211198,"customer_id":289829,"operator":"tuition fees","service":"tuition fees","productInfo_verticalId":76,"userData_amount":5000,"recharge_number":"17660703"};
            let records=[recordData];
            let stub1 = sinon.stub(serviceObj.dbInstance, 'exec').callsFake(function fakeFn(callback){
                return callback(null,records);
            });
            let stub2 = sinon.stub(serviceObj, 'checkIfRecordHasToBePublished').returns(true);
            serviceObj.markingUnusedToOtherCustomerId(recordData,table,cb);
            expect(stub1).to.have.callCount(1)
            expect(stub2).to.have.callCount(1)
            stub1.restore();
            stub2.restore();
        });

        // it.skip("_execSteps | success", (done) => {
        //     let table="bills_jio";
        //     let recordData = {"product_id":341211198,"customer_id":289829,"operator":"tuition fees","service":"tuition fees","productInfo_verticalId":76,"userData_amount":5000,"recharge_number":"17660703"};
        //     let records=[recordData];
        //     serviceObj.priorityBuckets = {
        //         0: 'platinum',
        //         1: 'gold',
        //         2: 'silver',
        //         3: 'bronze',
        //         4: 'noBucket'
        //     }

        //     let stub1 = sinon.stub(serviceObj.customerBucketsHandler, 'getPriorityBucketsList').callsFake(function fakeFn(records,parent){
        //         parent.priorityBucketsList = ["platinum","gold","silver","bronze","diamond"];
        //     });

        //     let stub2 = sinon.stub(serviceObj, '_processFreshRecords').callsFake(function fakeFn(records,cb){
        //         return cb(null,records);
        //     });

        //     let stub3 = sinon.stub(serviceObj, 'handleCaseWhenPriorityBucketsAreSet').callsFake(function fakeFn(records, cb){
        //         return cb(null,records);
        //     });

        //     serviceObj._execSteps(table,()=>{
        //         expect(stub1).to.have.callCount(1)
        //         expect(stub2).to.have.callCount(1)
        //         expect(stub3).to.have.callCount(1)
        //         stub1.restore();
        //         stub2.restore();
        //         stub3.restore();
        //         done();
        //     });
        // })

        it.skip("handleCaseWhenPriorityBucketsAreSet | success priorityResetTimer is completed but still bill fetch will done for platinum bucket", (done) => {
            serviceObj.priorityResetTimerCompleted = true;
            serviceObj.dbBatchSize = 1;
            serviceObj.currentPriorityBucket = 0;
            serviceObj.priorityBuckets = {
                0: 'platinum',
                1: 'gold',
                2: 'silver',
                3: 'bronze',
                4: 'noBucket'
            }
            serviceObj.priorityBucketsList = ["platinum","gold","silver","bronze","diamond"];
            let recordsCountInLastBatch = 1;
            let params = {
                "table": "bills_jio",
                "priorityBucket": "platinum",
                "recordsCountInLastBatch": recordsCountInLastBatch
            }
            let totalFreshRecordsCount = 1;
            let __keepFetchingUntilDone = function(){
                serviceObj.currentPriorityBucket = 4;
                serviceObj.priorityResetTimerCompleted = false;
                return serviceObj.handleCaseWhenPriorityBucketsAreSet(params, 0, 0, __keepFetchingUntilDone, (error, result) => {
                    done();
                });
            }

            let stub1 = sinon.stub(serviceObj, '_processFreshRecords').callsFake(function fakeFn(params, __keepFetchingUntilDone){
                return __keepFetchingUntilDone(null,[]);
            })

            let stub2 = sinon.stub(serviceObj.customerBucketsHandler, 'resetPriorityBucket').callsFake(function fakeFn(serviceObj){
                serviceObj.currentPriorityBucket = "platinum";
            });

            let stub3 = sinon.stub(serviceObj.customerBucketsHandler, 'shiftPriorityBucket').callsFake(function fakeFn(serviceObj){
                serviceObj.currentPriorityBucket = "gold";
            });

            serviceObj.handleCaseWhenPriorityBucketsAreSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, (error, result) => {
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(0)
                expect(stub3).to.have.callCount(0)
                stub1.restore();
                stub1.restore();
                stub2.restore();
                stub3.restore();
                done();
            })
        })

        it.skip("handleCaseWhenPriorityBucketsAreSet | success priorityResetTimer is completed and priority is reseted", (done) => {
            serviceObj.priorityResetTimerCompleted = true;
            serviceObj.dbBatchSize = 1;
            serviceObj.currentPriorityBucket = 1;
            serviceObj.priorityBuckets = {
                0: 'platinum',
                1: 'gold',
                2: 'silver',
                3: 'bronze',
                4: 'noBucket'
            }
            serviceObj.priorityBucketsList = ["platinum","gold","silver","bronze","diamond"];
            let recordsCountInLastBatch = 1;
            let params = {
                "table": "bills_jio",
                "priorityBucket": "platinum",
                "recordsCountInLastBatch": recordsCountInLastBatch
            }
            let totalFreshRecordsCount = 1;
            let __keepFetchingUntilDone = function(){
                serviceObj.currentPriorityBucket = 4;
                serviceObj.priorityResetTimerCompleted = false;
                return serviceObj.handleCaseWhenPriorityBucketsAreSet(params, 0, 0, __keepFetchingUntilDone, (error, result) => {
                    done();
                });
            }

            let stub1 = sinon.stub(serviceObj, '_processFreshRecords').callsFake(function fakeFn(params, __keepFetchingUntilDone){
                return __keepFetchingUntilDone(null,[]);
            })

            let stub2 = sinon.stub(serviceObj.customerBucketsHandler, 'resetPriorityBucket').callsFake(function fakeFn(serviceObj){
                serviceObj.currentPriorityBucket = "platinum";
            });

            let stub3 = sinon.stub(serviceObj.customerBucketsHandler, 'shiftPriorityBucket').callsFake(function fakeFn(serviceObj){
                serviceObj.currentPriorityBucket = "gold";
            });

            let stub4 = sinon.stub(serviceObj.customerBucketsHandler, 'getCurrentPriorityBucket').callsFake(function fakeFn(serviceObj){
                return  "noBucket";
            });

            let stub5 = sinon.stub(serviceObj.customerBucketsHandler, 'getLastPriorityBucket').callsFake(function fakeFn(serviceObj){
                return  "noBucket";
            })

            serviceObj.handleCaseWhenPriorityBucketsAreSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, (error, result) => {
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(1)
                stub1.restore();
                stub1.restore();
                stub2.restore();
                stub3.restore();
                stub4.restore();
                stub5.restore();
                done();
            })
        })

        it("handleCaseWhenPriorityBucketsAreSet | recieved less than batch size, priority is shifted", (done) => {
            serviceObj.priorityResetTimerCompleted = false;
            serviceObj.dbBatchSize = 1;
            serviceObj.currentPriorityBucket = 1;
            serviceObj.priorityBuckets = {
                0: 'platinum',
                1: 'gold',
                2: 'silver',
                3: 'bronze',
                4: 'noBucket'
            }
            serviceObj.priorityBucketsList = ["platinum","gold","silver","bronze","diamond"];
            let recordsCountInLastBatch = 0;
            let params = {
                "table": "bills_jio",
                "priorityBucket": "platinum",
                "recordsCountInLastBatch": recordsCountInLastBatch
            }
            let totalFreshRecordsCount = 1;
            let __keepFetchingUntilDone = function(){
                serviceObj.currentPriorityBucket = 4;
                serviceObj.priorityResetTimerCompleted = false;
                return serviceObj.handleCaseWhenPriorityBucketsAreSet(params, 0, 0, __keepFetchingUntilDone, (error, result) => {
                    done();
                });
            }

            let stub1 = sinon.stub(serviceObj, '_processFreshRecords').callsFake(function fakeFn(params, __keepFetchingUntilDone){
                return __keepFetchingUntilDone(null,[]);
            })

            let stub2 = sinon.stub(serviceObj.customerBucketsHandler, 'resetPriorityBucket').callsFake(function fakeFn(serviceObj){
                serviceObj.currentPriorityBucket = "platinum";
            });

            let stub3 = sinon.stub(serviceObj.customerBucketsHandler, 'shiftPriorityBucket').callsFake(function fakeFn(serviceObj){
                serviceObj.currentPriorityBucket = "gold";
            });

            // let stub4 = sinon.stub(serviceObj.customerBucketsHandler, 'getCurrentPriorityBucket').callsFake(function fakeFn(serviceObj){
            //     return  "noBucket";
            // });

            let stub5 = sinon.stub(serviceObj.customerBucketsHandler, 'getLastPriorityBucket').callsFake(function fakeFn(serviceObj){
                return  "noBucket";
            })

            serviceObj.handleCaseWhenPriorityBucketsAreSet(params, recordsCountInLastBatch, totalFreshRecordsCount, __keepFetchingUntilDone, (error, result) => {
                expect(stub1).to.have.callCount(1)
                expect(stub2).to.have.callCount(1)
                expect(stub3).to.have.callCount(1)
                // expect(stub4).to.have.callCount(1)
                expect(stub5).to.have.callCount(1)
                stub1.restore();
                stub2.restore();
                stub3.restore();
                // stub4.restore();
                stub5.restore();
                done();
            })
        })
    });