"use strict";

import { describe, it, before, beforeEach, afterEach } from "mocha";
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import moment from "moment";
import _ from "lodash";

import ActivePaytmUsersConsumer from "../../services/activePaytmUsersConsumer";
import KafkaConsumer from "../../lib/KafkaConsumer";
import KafkaConsumerChecks from "../../lib/kafkaConsumerChecks";
import cassandraBills from "../../models/cassandraBills";
import utility from "../../lib";
import Q from "q";
import ASYNC from "async";

chai.use(chaiAsPromised);
chai.use(sinonChai);
const { expect } = chai;

// --- Fake config & dependencies ---
const fakeConfig = {
  DYNAMIC_CONFIG: {
    GREYSCALE_CONFIG: {
      RECENTS: { BATCHSIZE: 2 },
      NON_PAYTM_BILLS: { BATCH_DELAY: 5 * 60 * 1000 },
    },
    ACTIVE_PAYTM_USERS_CONFIG: {
      COMMON: { KAFKA_BATCHSIZE: 500 },
    },
    SMART_FETCH_CONFIG: {
      ACTIVE_PAYTM_USER: {
        PAYMENT_DATE_LIST_SIZE: 10,
        SERVICES_WISE_PAYMENT_DATE_LIST_SIZE: 3,
      },
    },
    KAFKA_CONFIG: {
      SESSION_TIMEOUT: 120000,
      MAX_PROCESS_TIMEOUT: 180000,
    },
  },
  COMMON: { INTERVAL_FOR_REINITIALIZE: 15 },
  KAFKA: {
    TOPICS: {
      ACTIVE_PAYTM_USERS: { HOSTS: "localhost:9092" },
      RECHARGE: { HOSTS: "localhost:9092" },
    },
    SERVICES: {
      RECENTBILL: {
        RECHARGE_CONSUMER_TOPICS: ["topic1", "topic2"],
      },
      ACTIVE_PAYTM_USERS: { TOPIC: "active_paytm_topic" },
    },
  },
};

const fakeLogger = {
  log: () => {},
  error: () => {},
  critical: () => {},
  info: () => {},
};

const fakeCassandraDbClient = {};

const fakeInfraUtils = {
  kafka: {
    producer: function (options) {
      this.options = options;
      this.initProducer = function (priority, cb) {
        cb(null);
      };
    },
  },
};

const fakeOptions = {
  L: fakeLogger,
  config: fakeConfig,
  cassandraDbClient: fakeCassandraDbClient,
  INFRAUTILS: fakeInfraUtils,
  greyScaleEnv: false,
};

// Stub out the Datadog metrics call globally
sinon.stub(utility, "_sendMetricsToDD").callsFake(() => {});

// Fake classes
class FakeKafkaConsumerChecks extends KafkaConsumerChecks {
  constructor(opts) {
    super(opts);
    this.findOffsetDuplicates = sinon.stub();
  }
}
class FakeCassandraBills extends cassandraBills {
  constructor(opts) {
    super(opts);
    this.writeInActivePaytmUsers = sinon.stub();
    this.readActivePaytmUsersNewByCId = sinon.stub();
    this.writeInActivePaytmUsersNew = sinon.stub();
  }
}

describe("ActivePaytmUsersConsumer Comprehensive Test Suite", function () {
  let serviceObj;
  let sandbox;

  before(() => {
    // Create the service object once
    serviceObj = new ActivePaytmUsersConsumer(fakeOptions);
    serviceObj.kafkaConsumerChecks = new FakeKafkaConsumerChecks(fakeOptions);
    serviceObj.cassandraBills = new FakeCassandraBills(fakeOptions);
  });

  beforeEach(() => {
    // Create a fresh sandbox before each test, stub logger methods only once here
    sandbox = sinon.createSandbox();

    // Stub logger methods (only once per test lifecycle!)
    sandbox.stub(fakeLogger, "log");
    sandbox.stub(fakeLogger, "error");
    sandbox.stub(fakeLogger, "critical");
    sandbox.stub(fakeLogger, "info");
  });

  afterEach(() => {
    // Restore stubs
    sandbox.restore();
  });

  // ---------------------
  // Constructor / init
  // ---------------------
  it("should initialize instance variables in constructor", function () {
    expect(serviceObj.L).to.equal(fakeLogger);
    expect(serviceObj.config).to.equal(fakeConfig);
    expect(serviceObj.client).to.equal(fakeCassandraDbClient);
    expect(serviceObj.infraUtils).to.equal(fakeInfraUtils);
    expect(serviceObj.kafkaBatchSize).to.equal(500);
    expect(serviceObj.kafkaResumeTimeout).to.equal(500);
    expect(serviceObj.allowedNumberOfPaymentDates).to.equal(10);
    expect(serviceObj.allowedNumberOfServicesForPaymentDates).to.equal(3);
  });

  it("should update variables in initializeVariable", function () {
    serviceObj.initializeVariable();
    expect(fakeLogger.log).to.have.been.calledWith(
      "initializeVariable :: activePaytmUsersConsumer",
      "Re-initializing variable after interval"
    );
    expect(serviceObj.kafkaBatchSize).to.equal(500);
    expect(serviceObj.kafkaResumeTimeout).to.equal(500);
  });

  // ---------------------
  // start()
  // ---------------------
  it("should start service with successful producer and consumer initialization", function (done) {
    sandbox.stub(serviceObj, "_startProducer").callsFake((cb) => cb(null));
    sandbox.stub(serviceObj, "_initializeActivePaytmUsersConsumer").callsFake((cb) => cb(null));

    serviceObj.start();
    expect(fakeLogger.log).to.have.been.calledWith(
      "Start Configuration: ActivePaytmUsersConsumer is starting........"
    );
    // Waterfall calls are async
    setImmediate(() => {
      done();
    });
  });

  it("should log error when start fails due to consumer init error", function (done) {
    sandbox.stub(serviceObj, "_startProducer").callsFake((cb) => cb(null));
    sandbox.stub(serviceObj, "_initializeActivePaytmUsersConsumer").callsFake((cb) => cb(new Error("fail")));

    serviceObj.start();
    setImmediate(() => {
      expect(fakeLogger.error).to.have.been.calledWith(
        "ActivePaytmUsersConsumer",
        "Failed to initialize active paytm users consumer service."
      );
      done();
    });
  });

  // ---------------------
  // _startProducer()
  // ---------------------
  it("should initialize Kafka producer successfully", function (done) {
    // Force it to succeed
    sandbox.stub(fakeInfraUtils.kafka, "producer").callsFake(function () {
      this.initProducer = (priority, cb) => cb(null);
    });
    serviceObj._startProducer((err) => {
      expect(err).to.be.null;
      expect(fakeLogger.log).to.have.been.calledWith(
        "_startProducer : activePaytmUsersKafka KAFKA PRODUCER STARTED...."
      );
      done();
    });
  });

  it("should handle error during Kafka producer initialization", function (done) {
    sandbox.stub(fakeInfraUtils.kafka, "producer").callsFake(function () {
      this.initProducer = (priority, cb) => cb(new Error("producer error"));
    });
    serviceObj._startProducer((err) => {
      expect(err).to.exist;
      expect(fakeLogger.critical).to.have.been.called;
      done();
    });
  });

  // ---------------------
  // _initializeActivePaytmUsersConsumer()
  // ---------------------
  it("should initialize Kafka consumer successfully", function (done) {
    sandbox.stub(KafkaConsumer.prototype, "initConsumer").callsFake((fn, cb) => cb(null));

    serviceObj._initializeActivePaytmUsersConsumer((err) => {
      expect(err).to.be.null;
      expect(fakeLogger.log).to.have.been.calledWithMatch(
        "_initializeActivePaytmUsersConsumer : ActivePaytmUsers Consumer Configured"
      );
      done();
    });
  });

  it("should handle error during Kafka consumer initialization", function (done) {
    sandbox.stub(KafkaConsumer.prototype, "initConsumer").callsFake((fn, cb) => cb(new Error("consumer error")));

    serviceObj._initializeActivePaytmUsersConsumer((err) => {
      expect(err).to.exist;
      expect(fakeLogger.critical).to.have.been.called;
      done();
    });
  });

  it("should catch exception in _initializeActivePaytmUsersConsumer", function (done) {
    sandbox.stub(KafkaConsumer.prototype, "initConsumer").throws(new Error("exception"));

    serviceObj._initializeActivePaytmUsersConsumer((err) => {
      expect(err).to.exist;
      done();
    });
  });

  // ---------------------
  // _processKafkaData()
  // ---------------------
  it("should call cb immediately if records is not an array", function (done) {
    serviceObj._processKafkaData(null, sinon.stub(), "topic", 0, () => {
      expect(fakeLogger.critical).to.have.been.calledWith(
        "activePaytmUsersConsumer::_processKafkaData error while reading kafka"
      );
      done();
    });
  });

  it("should process valid kafka records", function (done) {
    const validData = {
      inStatusMap_responseCode: "00",
      customerInfo_customer_id: "cust1",
      productInfo_service: "svc1",
      userData_recharge_number: "1234567890",
      productInfo_operator: "op1",
    };
    const fakeRecord = {
      value: JSON.stringify(validData),
      topic: "t",
      partition: 0,
      offset: 10,
      timestamp: Date.now(),
    };

    sandbox.stub(serviceObj, "_processBillsData").callsFake((data, cb) => cb(null));
    const fakeResolveOffset = sinon.stub().resolves();

    serviceObj._processKafkaData([fakeRecord], fakeResolveOffset, "t", 0, () => {
      expect(fakeLogger.log).to.have.been.called;
      expect(fakeResolveOffset).to.have.been.calledWith(10);
      done();
    });
  });

  it("should handle invalid JSON in _processKafkaData", function (done) {
    const badRecord = {
      value: "invalid json",
      topic: "t",
      partition: 0,
      offset: 1,
      timestamp: Date.now(),
    };

    serviceObj._processKafkaData([badRecord], sinon.stub().resolves(), "t", 0, () => {
      expect(fakeLogger.error).to.have.been.called;
      done();
    });
  });

  it("should handle error in _processKafkaData when _processBillsData returns error", function (done) {
    const validData = {
      inStatusMap_responseCode: "00",
      customerInfo_customer_id: "cust_err",
      productInfo_service: "svc_err",
      userData_recharge_number: "123",
      productInfo_operator: "op_err",
    };
    const record = {
      value: JSON.stringify(validData),
      topic: "t",
      partition: 0,
      offset: 20,
      timestamp: Date.now(),
    };

    sandbox.stub(serviceObj, "_processBillsData").callsFake((data, cb) => cb(new Error("processing error")));
    const fakeResolveOffset = sinon.stub().resolves();
    const doneSpy = sinon.spy();

    // We'll need to wait for the setTimeout to fire
    serviceObj._processKafkaData([record], fakeResolveOffset, "t", 0, doneSpy);

    // The code sets a setTimeout with kafkaResumeTimeout (default 500ms).
    setTimeout(() => {
      expect(doneSpy.callCount).to.equal(1);
      done();
    }, serviceObj.kafkaResumeTimeout + 50);
  });

  it("should handle _processKafkaData with record missing value property", function (done) {
    const incompleteRecord = {
      topic: "t",
      partition: 0,
      offset: 5,
      timestamp: Date.now(),
    };

    serviceObj._processKafkaData([incompleteRecord], sinon.stub().resolves(), "t", 0, () => {
      expect(fakeLogger.error).to.have.been.called;
      done();
    });
  });

  // ---------------------
  // _processBillsData()
  // ---------------------
  it("should process bills data when payload is valid", function (done) {
    sandbox.stub(serviceObj, "validateKafkaPayload").returns(true);

    const stubOld = sandbox.stub(serviceObj, "writeInOldTable").callsFake((cb, rec) => cb(null));
    const stubNew = sandbox.stub(serviceObj, "writeInNewTable").callsFake((cb, rec) => cb(null, {}));
    const stubPub = sandbox
      .stub(serviceObj, "publishInActivePaytmUsersKafka")
      .callsFake((cb, rec, swp) => cb(null));

    const validPayload = {
      inStatusMap_responseCode: "00",
      customerInfo_customer_id: "cust1",
      productInfo_service: "svc1",
      userData_recharge_number: "1234567890",
      productInfo_operator: "op1",
    };

    serviceObj._processBillsData(validPayload, () => {
      expect(stubOld).to.have.been.called;
      expect(stubNew).to.have.been.called;
      expect(stubPub).to.have.been.called;
      done();
    });
  });

  it("should do nothing if payload is invalid in _processBillsData", function (done) {
    sandbox.stub(serviceObj, "validateKafkaPayload").returns(false);
    const invalidPayload = {};
    const finalSpy = sinon.spy();
    serviceObj._processBillsData(invalidPayload, finalSpy);

    // Should call final callback immediately
    expect(finalSpy).to.have.been.called;
    done();
  });

  // ---------------------
  // validateKafkaPayload()
  // ---------------------
  it("should return true if response code is '00', ignoring empty fields due to forEach bug", function () {
    const payload = {
      inStatusMap_responseCode: "00",
      customerInfo_customer_id: "",
      productInfo_service: "svc1",
      userData_recharge_number: "num",
      productInfo_operator: "op",
    };
    expect(serviceObj.validateKafkaPayload(payload)).to.be.true;
  });

  it("should return false when response code is not '00'", function () {
    const payload = { inStatusMap_responseCode: "99" };
    expect(serviceObj.validateKafkaPayload(payload)).to.be.false;
    expect(fakeLogger.error).to.have.been.called;
  });

  // ---------------------
  // writeInOldTable()
  // ---------------------
  it("should write data in old table successfully", function (done) {
    const record = { service: "svc1" };
    serviceObj.cassandraBills.writeInActivePaytmUsers.resolves();

    serviceObj.writeInOldTable((err) => {
      expect(err).to.be.null;
      done();
    }, record);
  });

  it("should handle error in writeInOldTable", function (done) {
    const record = { service: "svc1" };
    serviceObj.cassandraBills.writeInActivePaytmUsers.rejects(new Error("DB error"));

    serviceObj.writeInOldTable((err) => {
      expect(err).to.include("activePaytmUsersConsumer::writeInOldTable");
      done();
    }, record);
  });

  // ---------------------
  // writeInNewTable()
  // ---------------------
  it("should handle writeInNewTable when read returns empty data", function (done) {
    const record = {
      customer_id: "cust1",
      service: "svc1",
      payment_date: moment().format("YYYY-MM-DD HH:mm:ss"),
      source: "userRecharge",
    };
    serviceObj.cassandraBills.readActivePaytmUsersNewByCId.resolves([]);
    serviceObj.cassandraBills.writeInActivePaytmUsersNew.resolves();

    serviceObj.writeInNewTable((err, serviceDates) => {
      expect(err).to.be.null;
      expect(serviceDates).to.have.property("svc1");
      done();
    }, record);
  });

  it("should handle writeInNewTable with existing matching service and valid payment_date_list", function (done) {
    const record = {
      customer_id: "cust1",
      service: "svc1",
      payment_date: "2025-01-01 10:00:00",
      source: "userRecharge",
    };
    const existingData = [
      {
        service: "svc1",
        payment_date_list: ["2025-01-01 09:00:00"],
        created_source: "oldSource",
        created_at: "2025-01-01 08:00:00",
        customer_id: "cust1",
      },
    ];

    serviceObj.cassandraBills.readActivePaytmUsersNewByCId.resolves(existingData);
    serviceObj.cassandraBills.writeInActivePaytmUsersNew.resolves();

    serviceObj.writeInNewTable((err, serviceDates) => {
      expect(err).to.be.null;
      expect(serviceDates["svc1"]).to.include("2025-01-01 10:00:00");
      done();
    }, record);
  });

  it("should handle writeInNewTable when payment_date_list is not an array", function (done) {
    const record = {
      customer_id: "cust1",
      service: "svc1",
      payment_date: "2025-01-01 10:00:00",
      source: "userRecharge",
    };
    const existingData = [
      {
        service: "svc1",
        payment_date_list: "not-an-array",
        created_source: "oldSource",
        created_at: "2025-01-01 08:00:00",
        customer_id: "cust1",
      },
    ];

    serviceObj.cassandraBills.readActivePaytmUsersNewByCId.resolves(existingData);
    serviceObj.cassandraBills.writeInActivePaytmUsersNew.resolves();

    serviceObj.writeInNewTable((err, serviceDates) => {
      expect(err).to.be.null;
      expect(serviceDates["svc1"]).to.include("2025-01-01 10:00:00");
      done();
    }, record);
  });

  it("should handle error in writeInNewTable when read fails", function (done) {
    const record = {
      customer_id: "cust_error",
      service: "svc_err",
      payment_date: "2025-01-01 12:00:00",
      source: "userRecharge",
    };
    serviceObj.cassandraBills.readActivePaytmUsersNewByCId.rejects(new Error("read error"));

    serviceObj.writeInNewTable((err) => {
      expect(err).to.include("activePaytmUsersConsumer::writeInNewTable::select DB exception!");
      done();
    }, record);
  });

  it("should propagate error from writeInNewTable when write fails", function (done) {
    const record = {
      customer_id: "cust1",
      service: "svc1",
      payment_date: "2025-01-01 10:00:00",
      source: "userRecharge",
    };
    serviceObj.cassandraBills.readActivePaytmUsersNewByCId.resolves([]);
    serviceObj.cassandraBills.writeInActivePaytmUsersNew.rejects(new Error("write error"));

    serviceObj.writeInNewTable((err) => {
      expect(err).to.exist;
      done();
    }, record);
  });

  // ---------------------
  // publishInActivePaytmUsersKafka()
  // ---------------------
  it("should publish data to Kafka successfully", function (done) {
    const record = {
      customer_id: "cust1",
      service: "svc1",
      operator: "op",
      recharge_number: "num",
      payment_date: "2025-01-01 10:00:00",
    };
    const serviceDates = { svc1: ["2025-01-01 10:00:00"] };

    serviceObj.activePaytmUsersKafkaPublisher = {
      publishData: (payload, cb) => cb(null),
    };

    serviceObj.publishInActivePaytmUsersKafka((err) => {
      expect(err).to.be.undefined;
      expect(fakeLogger.log).to.have.been.called;
      done();
    }, record, serviceDates);
  });

  it("should handle error during Kafka publish", function (done) {
    const record = {
      customer_id: "cust1",
      service: "svc1",
      operator: "op",
      recharge_number: "num",
      payment_date: "2025-01-01 10:00:00",
    };
    const serviceDates = {};

    serviceObj.activePaytmUsersKafkaPublisher = {
      publishData: (payload, cb) => cb(new Error("publish error")),
    };

    serviceObj.publishInActivePaytmUsersKafka((err) => {
      expect(err).to.exist;
      expect(fakeLogger.critical).to.have.been.called;
      done();
    }, record, serviceDates);
  });

  it("should not include service_wise_payment_dates if too many services", function (done) {
    const record = {
      customer_id: "cust_pub2",
      service: "svc_pub2",
      operator: "op_pub2",
      recharge_number: "num_pub2",
      payment_date: "2025-01-01 14:00:00",
    };
    const serviceDates = {
      svc1: ["d1"],
      svc2: ["d2"],
      svc3: ["d3"],
      svc4: ["d4"], // total 4 > allowed 3
    };
    let publishedPayload;

    serviceObj.activePaytmUsersKafkaPublisher = {
      publishData: (payload, cb) => {
        publishedPayload = JSON.parse(payload[0].messages);
        cb(null);
      },
    };

    serviceObj.publishInActivePaytmUsersKafka((err) => {
      expect(err).to.be.undefined;
      expect(publishedPayload).to.not.have.property("service_wise_payment_dates");
      done();
    }, record, serviceDates);
  });

  // ---------------------
  // suspendOperations()
  // ---------------------
  it("should suspend operations successfully", function (done) {
    serviceObj.consumer = {
      close: (cb) => {
        cb(null, "closed");
      },
    };

    serviceObj.suspendOperations()
      .then(() => {
        // Check we logged 'kafka consumer shutdown successful'
        const logs = fakeLogger.log.getCalls().map(call => call.args.join(" "));
        const found = logs.some(str => str.includes("kafka consumer shutdown successful"));
        expect(found).to.be.true;
        done();
      })
      .catch(done);
  });

  // ---------------------
  // setInterval callback
  // ---------------------
  it("should call initializeVariable on setInterval", function () {
    // Instead of using the same serviceObj created outside, re-instantiate here
    // with fake timers active
    const clock = sinon.useFakeTimers();

    const localConsumer = new ActivePaytmUsersConsumer(fakeOptions);
    const initSpy = sinon.spy(localConsumer, "initializeVariable");

    // Advance time beyond 15 minutes
    clock.tick((fakeConfig.COMMON.INTERVAL_FOR_REINITIALIZE * 60 * 1000) + 100);

    expect(initSpy.callCount).to.be.greaterThan(0);

    clock.restore();
  });
});
