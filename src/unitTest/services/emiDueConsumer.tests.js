/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';


import chai, { assert } from "chai";
import MOMENT from 'moment';
import sinonChai from "sinon-chai";
import EmiDueConsumer from '../../services/emiDueConsumer';
import L from 'lgr'
import config from '../../config'
import _ from 'lodash';
import helper from '../__mocks__';
import sinon from 'sinon';
import chaiAsPromised from "chai-as-promised";
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module publisher:: EMI Due Consumer :: Kafka consumer validations", function () {
    let emiDueConsumerObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            // billsSubscriberObj = new EmiDueCommonConsumer(options);
            emiDueConsumerObj = new EmiDueConsumer(options);
            done();
        });
    });
    


    it("initializeVariable : update table name  in config", () => {
        _.set(emiDueConsumerObj.config,['OPERATOR_TABLE_REGISTRY', emiDueConsumerObj.operator],'newTable')
        emiDueConsumerObj.initializeVariable()
        assert.deepEqual(emiDueConsumerObj.tableName,'newTable')
    });

    it("testing initialize consumer | default values", () => {
        let cb = sinon.spy();
        emiDueConsumerObj.configureKafka(cb);
        expect(cb).to.have.callCount(1);
        expect(cb).to.have.calledWith(null)
        
    });


    it("start service || ensure service initialises consumer", () => {
        let initializeStub = sinon.stub(emiDueConsumerObj, 'configureKafka').yields(null);
        emiDueConsumerObj.start();
        expect(initializeStub).to.have.been.calledOnce;
    });

    it("processKafkaData || ensure empty records are validated", () => {
        let record = {}
        let processedRecords = emiDueConsumerObj.execSteps(record);
        expect(processedRecords).to.be.equal(undefined)
    })

});


describe("Module publisher:: EMI Due Common Consumer :: test suite", function () {
    let emiDueConsumerObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            emiDueConsumerObj = new EmiDueConsumer(options);
            done();
        });
    });

    it("validateAndProcessRecord function | invalid record", () => {
        let record = '';
        let [error, validRecord] =emiDueConsumerObj.validateAndProcessRecord(record);
        expect(error).to.be.equal('Invalid record');
    });

    it("validateAndProcessRecord function | Invalid Kafka record received", () => {
        let record = '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071"}';
        let [error, validRecord] =emiDueConsumerObj.validateAndProcessRecord(record);
        expect(error).to.be.equal('Invalid Kafka record received');
    });

    it("validateAndProcessRecord function | Kafka record Parsing Error", () => {
        let record = { "value" : [{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071"}] };
        let [error, validRecord] =emiDueConsumerObj.validateAndProcessRecord(record);    
        expect(error).to.be.equal('Kafka record Parsing Error');
    });

    it("processRecords function | validateAndProcessRecord function | invalid record", (done) => {
        let record = '';    
        emiDueConsumerObj.processRecords((error) => {
            return done();
        }, record);
    });

    it("processRecords function | validateAndProcessRecord function | Invalid Kafka record received", (done) => {
        let record = '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Hero FinCorp"}';
        emiDueConsumerObj.processRecords((error) => {
            return done();
        }, record);
    });


    
    /**
    it("checkOperatorEligibility function | Flow execution", () => {
        let record = {"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Hero FinCorp"} ;
        let [error, validRecord] = emiDueConsumerObj.checkOperatorEligibility(record);    
        expect(error).to.be.equal(null);
    }); */

    it("validateAndProcessRecord function | Mandatory Params amount is Missing / Invalid", () => {        
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);    
        expect(error).to.be.equal('Mandatory Params amount,amount is Missing / Invalid');
    });

    it("validateAndProcessRecord function | Mandatory Params customerId is Missing / Invalid", () => {
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);    
        expect(error).to.be.equal('Mandatory Params customerId is Missing / Invalid');
    });


    it("validateAndProcessRecord function | Mandatory Params rechargeNumber is Missing / Invalid", () => {
        let record = {"value" : '{"loan_no": "12331231", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        //let record = {"value" : '{"loan_no": "12331231", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);    
        expect(error).to.be.equal('Mandatory Params rechargeNumber is Missing / Invalid');
    });

    it("validateAndProcessRecord function | Flow execution", () => {
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);    
        expect(error).to.be.equal(null);
    });

    it("getDbRecordToUpdate function", () => {
        let record = { "productId" : "105826600" ,"rechargeNumber": "055602210545639","customerMobile": "9822468899","customerEmail": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","amount": "15561","emi_due": "50","customerId": "1147438071","operator": "Hero FinCorp"} ;
        emiDueConsumerObj.operator = "Hero FinCorp";
        let updatedDbRecord = emiDueConsumerObj.getDbRecordToUpdate(record);   

        expect(updatedDbRecord.customerId).to.be.equal(record.customerId);
        expect(updatedDbRecord.rechargeNumber).to.be.equal(record.rechargeNumber);
        expect(updatedDbRecord.customerMobile).to.be.equal(record.customerMobile);
        expect(updatedDbRecord.customerEmail).to.be.equal(record.customerEmail);
    });


    it("processBatch function", (done) => {
        let records = [
            { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Hero FinCorp"}' },
            { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Hero FinCorp"}' },    
            { "value" : '{"record_type": "New","application_id": "3152750","loan_agreement_no": "055602210545639","customername": "SWAPNIL DILIP JADHAV","branch": null,"branch_state_name": "ROMG","customer_add_mobile": "9822468899","cust_mailing_add_email": null,"pin_code": "NA","productflag": "PL - SAL","loan_amount": "528638","loan_tenure": "NA","loan_start_date": "26-Aug-19","installment_frequency": "MONTHLY","emi_amount": "15561","installment_date": "4-Jun-21","emi_due": "14625.84","opening_bucket": "1","dpd": "27","cust_id": "1147438071","operator": "housing","reminder_operator" : "Hero FinCorp"}' }
        ];
        emiDueConsumerObj.processBatch(records, () => {
            return done();
        });
    });



    /** helper function test ases */
    it("parseAmount | Valid Record | Integer - Rs.590", () => {
        expect(emiDueConsumerObj.parseAmount("Rs.590")).to.be.equal(590);
    });
    it("parseAmount | Valid Record | Float - Rs.590.78", () => {
        expect(emiDueConsumerObj.parseAmount("Rs.590.78")).to.be.equal(590.78);
    });
    it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....-1223.34", () => {
        expect(emiDueConsumerObj.parseAmount("Rs   ....-1223.34")).to.be.equal(-1223.34);
    });
    it("parseAmount | Valid Record | Amount negative with whitespaces with multiple dots -> Rs   ....+1223.34", () => {
        expect(emiDueConsumerObj.parseAmount("Rs   ....+1223.34")).to.be.equal(1223.34);
    });
    it("parseAmount | Valid Record | zero amount -> Rs.0", () => {
        expect(emiDueConsumerObj.parseAmount("Rs.0")).to.be.equal(0);
    });
    it("parseAmount | Valid Record | without Rs. string", () => {
        expect(emiDueConsumerObj.parseAmount("590")).to.be.equal(590);
    });
    it("parseAmount | Valid Record | without Rs. string", () => {
        expect(emiDueConsumerObj.parseAmount("-590")).to.be.equal(-590);
    });
    it("parseAmount | Valid Record | as Number", () => {
        expect(emiDueConsumerObj.parseAmount(590)).to.be.equal(590);
    });
    it("parseAmount | Valid Record | as Number", () => {
        expect(emiDueConsumerObj.parseAmount(590.67)).to.be.equal(590.67);
    });
    it("parseAmount | InValid Record | as null", () => {
        expect(emiDueConsumerObj.parseAmount(null)).to.be.equal(null);
    });
    it("parseAmount | InValid Record | as normal string", () => {
        expect(emiDueConsumerObj.parseAmount("amount")).to.be.equal(null);
    });

});

describe("Module publisher:: EMI Due Common Consumer :: CT tests", function () {
    let emiDueConsumerObj;

    let data, record;
    before(function () {
        STARTUP_MOCK.init(function(error, options){
            emiDueConsumerObj = new EmiDueConsumer(options);
            done();
        });
    });

    it("publishCtEvents function | Flow execution", (done) => {
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        emiDueConsumerObj.tableName = 'bills_hero'
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record); 
        let cb = sinon.spy();
        let stub = sinon.stub(emiDueConsumerObj, 'publishCtEvents');
        let stub2 = sinon.stub(emiDueConsumerObj, 'sendNotification').yields(null);
        emiDueConsumerObj.processRecords(cb, record)

        stub.restore();
        stub2.restore();
        expect(stub).to.have.callCount(1)
        return done();
    });

    it("publishCtEvents function | check function calls", (done) => {
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        emiDueConsumerObj.tableName = 'bills_hero'
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);
        let cb = sinon.spy();

        emiDueConsumerObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(emiDueConsumerObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(emiDueConsumerObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(emiDueConsumerObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        emiDueConsumerObj.publishCtEvents(cb, processedRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(1)
        expect(cb).to.have.been.calledWith(null)
        return done();
    })

    it("publishCtEvents function | no retailerStatus", (done) => {
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        emiDueConsumerObj.tableName = 'bills_hero'
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);
        let cb = sinon.spy();

        emiDueConsumerObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(emiDueConsumerObj.commonLib, 'getRetailerData').yields("error")
        let stub2 = sinon.stub(emiDueConsumerObj.commonLib, 'getCvrData').yields(null, 'abc.jpg')
        let stub3 = sinon.stub(emiDueConsumerObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        emiDueConsumerObj.publishCtEvents(cb, processedRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(0)
        expect(stub3).to.have.callCount(0)
        return done();
    })

    it("publishCtEvents function | no thumbnail", (done) => {
        let record = {"value" : '{"loan_no": "12331231", "application_id": "12312312", "customername": "bablu  thakur", "bkt": "2", "branch": "BHILWARA", "State_Name": "Rajasthan-2", "Product": "TW", "emi_amount": "2142", "emi_due": "4426", "customer_fathername": "Mr suganth  ...", "dpd": "57", "Cm_Data_of_Birth": "05/Mar/94", "customer_address": "bsl mell ke piche ram nagar  ward no  48 bhilwara", "Mobile_No": "99999999999", "Email_Address": "<EMAIL>", "Pan_No": null, "cust_id" : "123412312" }' };
        emiDueConsumerObj.operator = "Hero FinCorp";
        emiDueConsumerObj.tableName = 'bills_hero'
        let [error, processedRecord] = emiDueConsumerObj.validateAndProcessRecord(record);
        let cb = sinon.spy();

        emiDueConsumerObj.ctKafkaPublisher = {
            publishData : () => {
                return cb(null, data)
            }
        }
        let stub1 = sinon.stub(emiDueConsumerObj.commonLib, 'getRetailerData').yields(null, 1)
        let stub2 = sinon.stub(emiDueConsumerObj.commonLib, 'getCvrData').yields("error")
        let stub3 = sinon.stub(emiDueConsumerObj.reminderUtils, 'createCTPipelinePayload',).returns({});

        emiDueConsumerObj.publishCtEvents(cb, processedRecord);
        stub1.restore();
        stub2.restore();
        stub3.restore();

        expect(stub1).to.have.callCount(1)
        expect(stub2).to.have.callCount(1)
        expect(stub3).to.have.callCount(0)
        return done();
    })
})