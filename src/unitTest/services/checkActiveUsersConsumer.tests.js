'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment';
import _ from 'lodash';
import sinon from 'sinon';

import STARTUP_MOCK from '../__mocks__/startUp';
import CheckActiveUsersConsumer from '../../services/checkActiveUsersConsumer';
import utility from '../../lib';

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

describe("Module: Check Active Users Consumer Suite", function () {
    let serviceObj;
    let metricsStub;
    let sandbox;


    before(function (done) {
        // Create a new sandbox for each test suite
        sandbox = sinon.createSandbox();
        
        // Only create stub if it doesn't exist already
        if (!utility._sendMetricsToDD.restore) {
            metricsStub = sandbox.stub(utility, '_sendMetricsToDD');
        } else {
            metricsStub = utility._sendMetricsToDD;
        }
        
        STARTUP_MOCK.init((error, options) => {
            if (error) {
                sandbox.restore();
                return done(error);
            }
            try {
                serviceObj = new CheckActiveUsersConsumer(options);
                done();
            } catch (err) {
                sandbox.restore();
                done(err);
            }
        });
    });

    after(function() {
        // Restore all stubs created in the sandbox
        sandbox.restore();
    });

    afterEach(function() {
        if (metricsStub && typeof metricsStub.reset === 'function') {
            metricsStub.reset();
        }
    });

    beforeEach(function() {
        // Common dynamic config setup for all tests
        serviceObj.config.DYNAMIC_CONFIG = {
            SMART_FETCH_CONFIG: {
                ACTIVE_PAYTM_USER: {
                    SERVICE_TO_SERVICE_MAPPING: {
                        'electricity': 'loan',
                        'mobile': 'mobile'
                    },
                    SERVICE_WISE_THRESHOLD_DAYS: { 
                        'electricity': 60,
                        'mobile': 30 
                    },
                    SERVICE_WISE_THRESHOLD_TRANSACTIONS: { 
                        'electricity': 2,
                        'mobile': 1
                    },
                    COMMON_THRESHOLD_DAYS: 30
                }
            }
        };
    });

    it("_fetchUserPaymentDates | successfully fetches payment dates", (done) => {
        const customerId = "test123";
        const service = "electricity";
        const mockRows = [{
            latest_payment_date: Date.now(),
            payment_date_list: [Date.now(), Date.now() - 86400000]
        }];

        // Mock the cassandraBills method
        serviceObj.cassandraBills.getPaymentDatesFromActivePaytmUsers = (id, mappedService, cb) => {
            expect(id).to.equal(customerId);
            expect(mappedService).to.equal("loan");
            cb(null, { rows: mockRows });
        };

        serviceObj._fetchUserPaymentDates(customerId, service, (error, result, mappedService) => {
            expect(error).to.be.null;
            expect(result).to.deep.equal(mockRows);
            expect(mappedService).to.equal("loan");
            done();
        });
    });

    it("_fetchUserPaymentDates | error when service mapping not found", (done) => {
        const customerId = "test123";
        const service = "invalid_service";

        serviceObj._fetchUserPaymentDates(customerId, service, (error) => {
            expect(error).to.be.instanceOf(Error);
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:NO_SERVICE_MAPPING_FOUND'
            ]);
            done();
        });
    });

    it("_fetchUserPaymentDates | error when DB query fails", (done) => {
        const customerId = "test123";
        const service = "electricity";

        // Mock DB error
        serviceObj.cassandraBills.getPaymentDatesFromActivePaytmUsers = (id, mappedService, cb) => {
            cb(new Error("DB Error"));
        };

        serviceObj._fetchUserPaymentDates(customerId, service, (error) => {
            expect(error).to.be.instanceOf(Error);
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:DB_QUERY_ERROR'
            ]);
            done();
        });
    });

    it("_fetchUserPaymentDates | error when no rows found", (done) => {
        const customerId = "test123";
        const service = "electricity";

        // Mock empty result set
        serviceObj.cassandraBills.getPaymentDatesFromActivePaytmUsers = (id, mappedService, cb) => {
            expect(id).to.equal(customerId);
            expect(mappedService).to.equal("loan");
            cb(null, { rows: [] });
        };

        serviceObj._fetchUserPaymentDates(customerId, service, (error) => {
            expect(error).to.be.instanceOf(Error);
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:NO_ROWS_FOUND'
            ]);
            done();
        });
    });

    it("_checkActiveUser | user is active with sufficient recent transactions", (done) => {
        const now = MOMENT();
        const resultRow = [{
            latest_payment_date: now.clone().subtract(15, 'days').valueOf(),
            payment_date_list: [
                now.clone().subtract(20, 'days').valueOf(),
                now.clone().subtract(30, 'days').valueOf(),
                now.clone().subtract(40, 'days').valueOf()
            ]
        }];
        const service = 'electricity';
        const mapped_service = 'loan';

        serviceObj._checkActiveUser(resultRow, service, mapped_service, (error, isActive, latestPaymentDate, servicePaymentList) => {
            expect(error).to.be.null;
            expect(isActive).to.be.true;
            expect(servicePaymentList).to.have.property(mapped_service);
            expect(servicePaymentList[mapped_service]).to.have.lengthOf(2);
            expect(latestPaymentDate).to.equal(resultRow[0].latest_payment_date);
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ACTIVE',
                'TYPE:IS_ACTIVE_USER'
            ]);
            done();
        });
    });

    it("_checkActiveUser | error when service wise threshold daya config is missing", (done) => {
        const now = MOMENT();
        const resultRow = [{
            latest_payment_date: now.valueOf(),
            payment_date_list: []
        }];
        const service = 'electricity';
        const mapped_service = 'loan';

        delete serviceObj.config.DYNAMIC_CONFIG.SMART_FETCH_CONFIG.ACTIVE_PAYTM_USER.SERVICE_WISE_THRESHOLD_DAYS;

        serviceObj._checkActiveUser(resultRow, service, mapped_service, (error, isActive, latestPaymentDate, servicePaymentList) => {
            expect(error).to.be.instanceOf(Error);
            expect(isActive).to.be.undefined;
            expect(latestPaymentDate).to.be.undefined;
            expect(servicePaymentList).to.be.undefined;
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:NO_THRESHOLD_DAYS_OR_TRANSACTIONS_CONFIG'
            ]);
            done();
        });
    });

    it("_checkActiveUser | error when service wise threshold transactions config is missing", (done) => {
        const now = MOMENT();
        const resultRow = [{
            latest_payment_date: now.valueOf(),
            payment_date_list: []
        }];
        const service = 'electricity';
        const mapped_service = 'loan';

        delete serviceObj.config.DYNAMIC_CONFIG.SMART_FETCH_CONFIG.ACTIVE_PAYTM_USER.SERVICE_WISE_THRESHOLD_TRANSACTIONS;

        serviceObj._checkActiveUser(resultRow, service, mapped_service, (error, isActive, latestPaymentDate, servicePaymentList) => {
            expect(error).to.be.instanceOf(Error);
            expect(isActive).to.be.undefined;
            expect(latestPaymentDate).to.be.undefined;
            expect(servicePaymentList).to.be.undefined;
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:NO_THRESHOLD_DAYS_OR_TRANSACTIONS_CONFIG'
            ]);
            done();
        });
    });

    it("_checkActiveUser | error when common threshold days config is missing", (done) => {
        const now = MOMENT();
        const resultRow = [{
            latest_payment_date: now.valueOf(),
            payment_date_list: []
        }];
        const service = 'electricity';
        const mapped_service = 'loan';

        delete serviceObj.config.DYNAMIC_CONFIG.SMART_FETCH_CONFIG.ACTIVE_PAYTM_USER.COMMON_THRESHOLD_DAYS;

        serviceObj._checkActiveUser(resultRow, service, mapped_service, (error, isActive, latestPaymentDate, servicePaymentList) => {
            expect(error).to.be.instanceOf(Error);
            expect(isActive).to.be.undefined;
            expect(latestPaymentDate).to.be.undefined;
            expect(servicePaymentList).to.be.undefined;
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:NO_THRESHOLD_DAYS_OR_TRANSACTIONS_CONFIG'
            ]);
            done();
        });
    });

    it("_checkActiveUser | user is inactive due to insufficient recent transactions", (done) => {
        const now = MOMENT();
        const resultRow = [{
            latest_payment_date: now.clone().subtract(15, 'days').valueOf(),
            payment_date_list: [
                now.clone().subtract(70, 'days').valueOf(),
                now.clone().subtract(80, 'days').valueOf()
            ]
        }];
        const service = 'electricity';
        const mapped_service = 'loan';

        serviceObj._checkActiveUser(resultRow, service, mapped_service, (error, isActive, latestPaymentDate, servicePaymentList) => {
            expect(error).to.be.null;
            expect(isActive).to.be.false;
            expect(latestPaymentDate).to.equal(resultRow[0].latest_payment_date);
            // Service payment list should be empty since no transactions meet the criteria
            expect(servicePaymentList).to.deep.equal({});
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:INACTIVE',
                'TYPE:IS_ACTIVE_USER'
            ]);
            done();
        });
    });

    it("_checkActiveUser | user is inactive due to old latest payment date", (done) => {
        const now = MOMENT();
        const resultRow = [{
            latest_payment_date: now.clone().subtract(35, 'days').valueOf(),
            payment_date_list: [
                now.clone().subtract(20, 'days').valueOf(),
                now.clone().subtract(30, 'days').valueOf()
            ]
        }];
        const service = 'electricity';
        const mapped_service = 'loan';

        serviceObj._checkActiveUser(resultRow, service, mapped_service, (error, isActive, latestPaymentDate, servicePaymentList) => {
            expect(error).to.be.null;
            expect(isActive).to.be.false;
            expect(latestPaymentDate).to.equal(resultRow[0].latest_payment_date);
            // Service payment list should be empty since user is inactive
            expect(servicePaymentList).to.deep.equal({});
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:INACTIVE',
                'TYPE:IS_ACTIVE_USER'
            ]);
            done();
        });
    });

    it("_publishToNonRuPipeline | successfully publishes to kafka", (done) => {
        const billsKafkaRow = {
            customerId: "test123",
            service: "electricity"
        };
        const latestPaymentDate = Date.now();
        const servicePaymentList = {
            loan: [Date.now(), Date.now() - 86400000]
        };

        // Mock the kafka publisher
        serviceObj.nonPaytmKafkaPublisher = {
            publishData: function(messages, callback) {
                const publishedData = JSON.parse(messages[0].messages);
                expect(publishedData.is_active_expired_user).to.be.true;
                expect(publishedData.latest_payment_date).to.equal(latestPaymentDate);
                expect(publishedData.service_payment_date_list).to.deep.equal(servicePaymentList);
                callback(null);
            }
        };

        serviceObj._publishToNonRuPipeline(billsKafkaRow, latestPaymentDate, servicePaymentList, (error) => {
            expect(error).to.be.null;
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                "STATUS:SUCCESS",
                "TYPE:NON_PAYTM_EVENTS",
                "TOPIC:NON_PAYTM_RECORDS_DWH"
            ]);
            done();
        });
    });

    it("_publishToNonRuPipeline | handles kafka publish error", (done) => {
        const billsKafkaRow = {
            customerId: "test123",
            service: "electricity"
        };
        const latestPaymentDate = Date.now();
        const servicePaymentList = {
            loan: [Date.now(), Date.now() - 86400000]
        };

        // Mock kafka publish error
        serviceObj.nonPaytmKafkaPublisher.publishData = (messages, cb) => {
            cb(new Error("Kafka publish error"));
        };

        serviceObj._publishToNonRuPipeline(billsKafkaRow, latestPaymentDate, servicePaymentList, (error) => {
            expect(error).to.be.instanceOf(Error);
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:NON_PAYTM_EVENTS',
                'TOPIC:NON_PAYTM_RECORDS_DWH'
            ]);
            done();
        });
    });

    it("_initializeNonPaytmPublisher | successfully initializes publisher", (done) => {
        const mockProducer = function(config) {
            expect(config.kafkaHost).to.equal(serviceObj.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS);
            return {
                initProducer: function(acks, callback) {
                    expect(acks).to.equal('high');
                    callback(null);
                }
            };
        };

        serviceObj.infraUtils.kafka.producer = mockProducer;

        serviceObj._initializeNonPaytmPublisher((error) => {
            expect(error).to.be.null;
            done();
        });
    });

    it("_initializeNonPaytmPublisher | handles initialization error", (done) => {
        const mockProducer = function() {
            return {
                initProducer: function(acks, callback) {
                    callback(new Error("Kafka producer init error"));
                }
            };
        };

        serviceObj.infraUtils.kafka.producer = mockProducer;

        serviceObj._initializeNonPaytmPublisher((error) => {
            expect(error).to.be.instanceOf(Error);
            expect(metricsStub).to.have.been.calledWith(1, [
                "REQUEST_TYPE:CHECK_ACTIVE_USERS",
                'STATUS:ERROR',
                'TYPE:KAFKA_PUBLISHER',
                'TOPIC:NON_PAYTM_RECORDS_DWH'
            ]);
            done();
        });
    });

    it("_processKafkaData | successfully processes valid records", (done) => {
        const records = [
            { value: JSON.stringify({ customerId: "test1", service: "mobile" }), offset: 1 },
            { value: JSON.stringify({ customerId: "test2", service: "electricity" }), offset: 2 }
        ];
        const resolveOffset = sinon.stub().resolves();
        const topic = "test-topic";
        const partition = 0;

        // Stub _processBillsData to avoid actual processing
        const processBillsStub = sinon.stub(serviceObj, '_processBillsData').callsFake((data, cb) => cb(null));

        serviceObj._processKafkaData(records, resolveOffset, topic, partition, (error) => {
            expect(error).to.be.undefined;
            expect(processBillsStub.callCount).to.equal(2);
            expect(resolveOffset).to.have.been.calledWith(2);
            processBillsStub.restore();
            done();
        });
    });

    it("_processBillsData | successfully processes active user data", (done) => {
        const billsKafkaRow = {
            customerId: "test123",
            service: "electricity"
        };

        // Stub dependent methods
        const fetchStub = sinon.stub(serviceObj, '_fetchUserPaymentDates').callsFake((id, service, cb) => {
            cb(null, [{ latest_payment_date: Date.now() }], 'loan');
        });

        const checkActiveStub = sinon.stub(serviceObj, '_checkActiveUser').callsFake((result, service, mapped_service, cb) => {
            cb(null, true, Date.now(), { loan: [Date.now()] });
        });

        const publishStub = sinon.stub(serviceObj, '_publishToNonRuPipeline').callsFake((data, date, list, cb) => {
            cb(null);
        });

        serviceObj._processBillsData(billsKafkaRow, (error) => {
            expect(error).to.be.undefined;
            expect(fetchStub.calledOnce).to.be.true;
            expect(checkActiveStub.calledOnce).to.be.true;
            expect(publishStub.calledOnce).to.be.true;
            
            fetchStub.restore();
            checkActiveStub.restore();
            publishStub.restore();
            done();
        });
    });

    it("suspendOperations | successfully suspends operations", (done) => {
        const closeStub = sinon.stub().callsFake((callback) => {
            callback(null, "Success");
        });

        serviceObj.consumer = { close: closeStub };

        serviceObj.suspendOperations()
            .then(() => {
                expect(closeStub.calledOnce).to.be.true;
                done();
            })
            .catch(done);
    });

    it("suspendOperations | handles error during suspension", (done) => {
        const errorMessage = "Kafka close error";
        const closeStub = sinon.stub().callsFake((callback) => {
            callback(new Error(errorMessage));
        });

        serviceObj.consumer = { close: closeStub };

        serviceObj.suspendOperations()
            .then(() => {
                expect(closeStub.calledOnce).to.be.true;
                done();
            })
            .catch(() => {
                done(new Error("Should not have rejected"));
            });
    });
}); 