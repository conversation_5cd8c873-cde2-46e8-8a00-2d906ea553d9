'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment';
    import sinon from 'sinon';
    import _ from 'lodash';

    import STARTUP_MOCK from '../__mocks__/startUp';
    import SmsParsingFasTag from '../../services/fasTagSmsParsing';

    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;

    describe("Module: SMS Parsing FasTag Consumer suite :: Kafka consumer validations", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SmsParsingFasTag(options);
                done();
            });
        });
    
    
        // it("testing initialize consumer | default values", () => {
        //     let cb = sinon.spy();
        //     serviceObj.configureKafka(cb);
        //     expect(cb).to.have.callCount(1);
        //     expect(cb).to.have.calledWith(null);
            
        // });
    
        it("start service || ensure service initialises consumer", () => {
            let initializeStub = sinon.stub(serviceObj, 'configureKafka').yields(null);
            serviceObj.start();
            expect(initializeStub).to.have.been.calledOnce;
        });
    
        it("processKafkaData || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })
    });

    describe("Module: Sms Parsing FasTag Consumer Site", function(){
        let serviceObj;

        before(function(done){
            STARTUP_MOCK.init(function(error,options){
                serviceObj = new SmsParsingFasTag(options);
                done();
            })
        })

        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
        });

        it("execSteps || ensure empty records are validated", () => {
            let record = {}
            let processedRecords = serviceObj.execSteps(record);
            expect(processedRecords).to.be.equal(undefined)
        })

        // it("execSteps || greyScaleEnv || Error in resolveOffset function", () => {
        //     let record = [
        //     ];
        //     let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
        //     sinon.stub(process, 'exit');
        //     let clock = sinon.useFakeTimers();
        //     serviceObj.consumer = {
        //         pause : () => {
        //             return;
        //         },
        //         resolveOffset: (data, cb) => {
        //             return cb("Error in resolveOffset");
        //         },
        //         resume: () => {
        //         }
        //     };
        //     serviceObj.greyScaleEnv = true;
            
        //     let processedRecords = serviceObj.execSteps(record);
        //     clock.tick(40 * 60 * 1000)
        //     expect(processedRecords).to.be.equal(undefined);
        //     expect(stub1).to.have.callCount(0);
        //     stub1.restore();
        //     process.exit.restore();
        // })

        // it("execSteps || greyScaleEnv || resolveOffset function", () => {
        //     let record = [
        //     ];
        //     let stub1 = sinon.stub(serviceObj, "processBatch").resolves();
        //     let clock = sinon.useFakeTimers();
    
        //     serviceObj.consumer = {
        //         pause : () => {
        //             return;
        //         },
        //         resolveOffset: (data, cb) => {
        //             return cb();
        //         },
        //         resume: () => {
    
        //         }
        //     };
        //     serviceObj.greyScaleEnv = true;
            
        //     let processedRecords = serviceObj.execSteps(record);
        //     clock.tick(40 * 60 * 1000)
        //     expect(processedRecords).to.be.equal(undefined);
        //     expect(stub1).to.have.callCount(0);
        //     stub1.restore();
        // })

        // it("execSteps || processBatch function", () => {
        //     let data = {"abs":"123"}
        //     let payLoad = {
        //         value: JSON.stringify(data)
        //     };
        //     let record = [
        //         {...payLoad}
        //     ];
        //     let clock = sinon.useFakeTimers();
        //     let stub1 = sinon.stub(serviceObj, 'processBatch').yields();
            
        //     serviceObj.consumer = {
        //         pause : () => {
        //             return;
        //         },
        //         resolveOffset: (data, cb) => {
        //             return cb();
        //         },
        //         resume: () => {
        //         }
        //     };
        //     let processedRecords = serviceObj.execSteps(record);
        //     clock.tick(40 * 60 * 1000)
        //     expect(processedRecords).to.be.equal(undefined);
        //     expect(stub1).to.have.callCount(1);
        //     stub1.restore();
        // })

        // it("processBatch function | InValid Kafka record received", (done) => {
        //     let record = { value : '{"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"4","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}'};
        //     serviceObj.processBatch(record, function (error){
        //         return done();
        //     });    
        // });

        it("processBatch function | Success case execution",  ()=>{
            let data = { value : '{"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}'};
            let payLoad = {
                value: JSON.stringify(data)
            };
            let records = [
                { ...payLoad },
                { ...payLoad }
            ];
            serviceObj.processBatch(records,(data)=>{
                expect(data).to.be.equal(undefined);    
            });
        });

        it("processRecords | Invalid Kafka record received. data key is missing", (done) => {
            let recordData = { value: JSON.stringify({"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) };
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                return done();
            });
        });
    
        it("processRecords | Invalid Kafka record received | parsing Error", (done) => {
            let recordData = { value: {}  };
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                return done();
            });
        });
    
        it("processRecords | Valid Kafka record received", (done) => {
            let recordData = {
                value : JSON.stringify({
                    data : [
                        {"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}},
                        {"cId": "1234345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}
                    ]
                })
            }
            serviceObj.processData(recordData, function (error) {
                expect(error).to.be.equal(undefined);
                return done();
            });
        });

        it("processRecords | customerId not passed", (done) => {
            let recordData = {"cId": "","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}};
            serviceObj.processRecords(function (error) {
                expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
                return done();
            }, recordData);
        });

        it("processRecords | valid record | unexpected error" , (done) => {
            let recordData = {"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12","fastag_model_version":"v2.0"}};
            let record = {}

            let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(cb) {
                return cb(null,record);
            });
    
            let stub2 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
                return done(null);
            });
    
            serviceObj.processRecords(function (error) {
                expect(`${error}`).to.be.equal("TypeError: cb is not a function");
            }, recordData);
    
            expect(stub1).to.have.callCount(1);
            expect(stub2).to.have.callCount(0);

            stub1.restore();
            stub2.restore();
    
            done();
        });

        // it("processRecords | valid record | unexpected error" , (done) => {
        //     let recordData = {"cId":"12345","eventType":"smsEvent","event_name":"sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}};
        //     let record = {}

        //     let stub1 = sinon.stub(serviceObj, 'validateAndProcessRecord').callsFake(function(recordDate,cb) {
        //         return cb(null,record);
        //     });
    
        //     let stub2 = sinon.stub(serviceObj, 'publishCtAndPFCCEvents').callsFake(function fakeFn(done) {
        //         return done("Error while publishing");
        //     });
        //     let stub3 = sinon.stub(serviceObj, 'isValidClassForFastagLowBalance').callsFake(function fakeFn(done) {
        //         return true;
        //     });
    
        //     serviceObj.processRecords(function (error) {
        //         expect(error).to.be.equal("Error while publishing");
        //     }, recordData);
    
        //     expect(stub1).to.have.callCount(1);
        //     expect(stub2).to.have.callCount(1);
        //     expect(stub3).to.have.callCount(1);

        //     stub1.restore();
        //     stub2.restore();
        //     stub3.restore();
    
        //     done();
        // });

        it("validateAndProcessRecord | Invalid Arg | record not passed", () => {
            let recordData = null;
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Invalid record");
            });
        });

        it("validateAndProcessRecord | customerId not passed", () => {
            let recordData = JSON.parse(JSON.stringify({"cId": "","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
            });
        });

        it("validateAndProcessRecord | customerId - As Number Valid Case", () => {
            let recordData = JSON.parse(JSON.stringify({"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12","fastag_model_version":"v1"}}) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                console.log("printing the error",error)
                expect(error).to.be.equal(null);
            });
        });

        it("validateAndProcessRecord | Invalid customerId - alpha numeric", () => {
            let recordData = JSON.parse(JSON.stringify({"cId": "alphanumeric","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) );
            serviceObj.validateAndProcessRecord(recordData, function(error, data){
                expect(error).to.be.equal("Mandatory Params customerId is Missing / Invalid");
            });
        });
    })

    describe("Module publisher:: SMS PARSING Consumer :: CT tests", function () {
        let serviceObj;
    
        let data, record;
        before(function () {
            STARTUP_MOCK.init(function(error, options){
                serviceObj = new SmsParsingFasTag(options);
                done(error);
            });
        });
    
        // it("publishCtAndPFCCEvents function | check function calls", (done) => {
        //     let recordData = JSON.parse(JSON.stringify({"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) );
        //     serviceObj.validateAndProcessRecord(recordData, function(error,data){
        //         const record=data;
        //         let cb = sinon.spy();
    
        //         serviceObj.ctKafkaPublisher = {
        //             publishData : () => {
        //                 return cb(null, data)
        //             }
        //         }
        //         serviceObj.paytmFirstKafkaPublisher = {
        //             publishData : () => {
        //                 return cb(null,data)
        //             }
        //         }
        //         let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        //         let stub2 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
        
        //         serviceObj.publishCtAndPFCCEvents(cb, record, record);
        //         stub1.restore();
        //         stub2.restore();
        
        //         expect(stub1).to.have.callCount(1)
        //         expect(stub2).to.have.callCount(1)
        //         expect(cb).to.have.been.calledWith(null)
        //         return done();
        //     })
        // });
        // it("publishCtAndPFCCEvents function | check function calls", (done) => {
        //     let recordData = JSON.parse(JSON.stringify({"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) ),
        //         record=data;
    
        //         serviceObj.paytmFirstKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
        //         serviceObj.paytmFirstKafkaPublisher.publishData = function(data,cb){};
        //         serviceObj.ctKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
        //         serviceObj.ctKafkaPublisher.publishData = function(data,cb){};
    
        //         let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
        //         let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
        //         let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, 'publishData').callsFake(function(recordData,cb){
        //             return cb(null);
        //         });
        //         let stub5 = sinon.stub(serviceObj.paytmFirstKafkaPublisher, 'publishData',).callsFake(function(recordData,cb){
        //             return cb(null);
        //         });
    
        //         serviceObj.publishCtAndPFCCEvents(function(error,result){
        //         expect(stub1).to.have.callCount(1)
        //         expect(stub3).to.have.callCount(1)
        //         stub1.restore();
        //         stub3.restore();
        //         return done();
        //         }, recordData,record);
                
        // });
    //     it("publishCtAndPFCCEvents function | check function calls", (done) => {
    //         let recordData = JSON.parse(JSON.stringify({"cId": "122345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) ),
    //             record=data;
    
    //             serviceObj.paytmFirstKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //             serviceObj.paytmFirstKafkaPublisher.publishData = function(data,cb){};
    //             serviceObj.ctKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //             serviceObj.ctKafkaPublisher.publishData = function(data,cb){};
    
    //             let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
    //             let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    //             let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, 'publishData').callsFake(function(recordData,cb){
    //                 return cb("error");
    //             });
    //             let stub5 = sinon.stub(serviceObj.paytmFirstKafkaPublisher, 'publishData',).callsFake(function(recordData,cb){
    //                 return cb("error");
    //             });
    
    //             serviceObj.publishCtAndPFCCEvents(function(error,result){
    //             expect(stub1).to.have.callCount(1)
    //             expect(stub3).to.have.callCount(1)
    //             stub1.restore();
    //             stub3.restore();
    //             return done();
    //             }, recordData, record);
                
    // });
    // it("publishCtAndPFCCEvents function | check function calls", (done) => {
    //     let recordData = JSON.parse(JSON.stringify({ "smsSenderID": "AD-HDFCBK", "smsDateTime": 1574395093059, "dueDate": "2022-12-11", "billDate": "2020-11-11", "dueAmt": "590", "totalAmt": "11711", "lastCC": "2808", "mId": "", "cId": "1000955756", "isReliable" : "1" }));
    //         record={"notification_status":0};
    
    //         serviceObj.paytmFirstKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //         serviceObj.paytmFirstKafkaPublisher.publishData = function(data,cb){};
    //         serviceObj.ctKafkaPublisher = new serviceObj.infraUtils.kafka.producer(); 
    //         serviceObj.ctKafkaPublisher.publishData = function(data,cb){};
    
    //         let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields(null, 1)
    //         let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
    //         let stub4 = sinon.stub(serviceObj.ctKafkaPublisher, 'publishData').callsFake(function(recordData,cb){
    //             return cb("error");
    //         });
    //         let stub5 = sinon.stub(serviceObj.paytmFirstKafkaPublisher, 'publishData',).callsFake(function(recordData,cb){
    //             return cb("error");
    //         });
    
    //         serviceObj.publishCtAndPFCCEvents(function(error,result){
    //         expect(stub1).to.have.callCount(1)
    //         expect(stub3).to.have.callCount(1)
    //         stub1.restore();
    //         stub3.restore();
    //         return done();
    //         }, record, record);
            
    //     });         
        // it("publishCtAndPFCCEvents function | no retailerStatus", (done) => {
        //     let recordData = JSON.parse(JSON.stringify({"cId": "12345","eventType": "smsEvent","event_name": "sms","smsDateTime":*************,"smsOperator":"Jio","smsReceiver":"************","smsSenderID":"JX-ICICIB","smsUUID":"c8cd2c6f-30ba-409e-9d59-3b940bdc95a1","level_2_category":"3.3","fastag_class":"5","fastag_features":{"vehicle_number":"od07an9952 ","bank_name":"ICICI","date_of_message":"2023-06-12"}}) );
        //     serviceObj.validateAndProcessRecord(recordData, function(error, data){
        //         record = data
        //         let cb = sinon.spy();
    
        //     serviceObj.ctKafkaPublisher = {
        //         publishData : () => {
        //             return cb(null, data)
        //         }
        //     }
        //     serviceObj.paytmFirstKafkaPublisher = {
        //         publishData : () => {
        //             return cb(null, data)
        //         }
        //     }
        //     let stub1 = sinon.stub(serviceObj.commonLib, 'getRetailerData').yields("error")
        //     let stub3 = sinon.stub(serviceObj.reminderUtils, 'createCTPipelinePayload',).returns({});
        //     let stub4 = sinon.stub(serviceObj, 'paytmFirstKafkaPublisher').returns(null);
    
        //     serviceObj.publishCtAndPFCCEvents(cb, recordData,record);
        //     stub1.restore();
        //     stub3.restore();
        //     stub4.restore();
    
        //     expect(stub1).to.have.callCount(1)
        //     expect(stub3).to.have.callCount(0)
        //     expect(stub4).to.have.callCount(0)
        //     return done();
        //     });
        // })
    })
