'use strict';
import { describe, it, before, afterEach, beforeEach } from 'mocha';
import sinon from 'sinon';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import billsLib from '../../lib/bills';

chai.use(chaiAsPromised);
chai.use(sinonChai);
const { expect } = chai;
import LoanUtils from '../../utils/loanUtil';

describe("LoanUtils :: test cases :: ", function () {
    let loanUtils, sandbox;
    let mockL, mockConfig;
    beforeEach(function () {
        sandbox = sinon.createSandbox();
        
        mockL = {
            log: sandbox.stub(),
            error: sandbox.stub(),
            verbose: sandbox.stub()
        };

        mockConfig = {};
        
        loanUtils = new LoanUtils({
            L: mockL,
            config: mockConfig
        });
    });

    afterEach(function () {
        sandbox.restore();
    });

    it('should update params with dummy recharge number', () => {
        // Arrange
        const params = {};
        sinon.stub(loanUtils.billsLib, 'generateDummyRechargeNumber').returns('1234_JIO');

        // Act
        loanUtils.updateRNtoDummy(params);

        // Assert
        expect(params.rechargeNumber).to.equal('1234_JIO');
        expect(loanUtils.billsLib.generateDummyRechargeNumber).to.have.been.calledWith(params);
    });
});
