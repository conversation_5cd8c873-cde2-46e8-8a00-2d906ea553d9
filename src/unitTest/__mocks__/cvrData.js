

function getRawCvrData() {
    return CVR_DATA;
}

function getCvrDataWithPidMapping() {
    let pidMappedCvrData = {};
    CVR_DATA.forEach(function(cvrRow){
        pidMappedCvrData[cvrRow.product_id] = cvrRow;   
    });
    return pidMappedCvrData;
}

var CVR_DATA = [{
    id: 649017,
    product_id: 1200763862,
    vertical_id: 17,
    category_id: 46007,
    brand: "Hillside School",
    service: "mobile",
    paytype: "credit card",
    operator: "airtel",
    circle: "D",
    producttype: "",
    service_label: "Education",
    paytype_label: "Fee Payment",
    operator_label: "Hillside School",
    circle_label: "",
    producttype_label: "",
    info: JSON.stringify({ "dimensions": "course", "mop": 0, "created_by": "admin", "email": "<EMAIL>", "discoverability": [1], "productStandardName": 0 }),
    updated_info: "",
    min_amount: 0,
    max_amount: 99999,
    status: 1,
    message: "Convenience fee is charged on fee amount & it varies based on payment mode selected",
    prefetch: 1,
    attributes: JSON.stringify({ "course": "N/A", "prefetch": "0", "location": "N/A", "input_field-title-1": "StudentName", "input_field-config_key-1": "recharge_number", "input_field-title-2": "EmailId", "input_field-config_key-2": "recharge_number_2", "service": "Education", "input_field-title-3": "Contact", "input_field-config_key-3": "recharge_number_3", "input_field-regex-1": "^[a-zA-Z.]+$", "input_field-regex-2": "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[a-zA-Z]{2,}$", "operator_label": "GalgotiasUniversity", "operator": "GalgotiasUniversity", "service_label": "Education", "paytype_label": "NewRegistration", "paytype": "NewRegistration", "input_field-title-4": "College", "input_field-config_key-4": "recharge_number_4", "input_field-regex-3": "^(+91|0)?[1-9][0-9]{9}$", "input_field-regex-4": "^[a-zA-Z.]+$", "state": "UttarPradesh", "city": "GreaterNoida", "fee_type": "N/A", "school": "GalgotiasUniversity", "area": "GalgotiasUnifestPass", "fee_name": "noida-total fee" }),
    thumbnail: "https://assetscdn1.paytm.com/images/catalog/brand/Hillside School.jpg",
    created_at: "2019-01-04 09:00:08",
    updated_at: "2021-02-02 18:44:31",
    merchant_id: 267204,
    schedulable: 0
},{
    id: 649017,
    product_id: 341207918,
    vertical_id: 17,
    category_id: 46007,
    brand: "Hillside School",
    service: "mobile",
    paytype: "postpaid",
    operator: "airtel",
    circle: "D",
    producttype: "",
    service_label: "Education",
    paytype_label: "Fee Payment",
    operator_label: "Hillside School",
    circle_label: "",
    producttype_label: "",
    info: JSON.stringify({ "dimensions": "course", "mop": 0, "created_by": "admin", "email": "<EMAIL>", "discoverability": [1], "productStandardName": 0 }),
    updated_info: "",
    min_amount: 0,
    max_amount: 99999,
    status: 1,
    message: "Convenience fee is charged on fee amount & it varies based on payment mode selected",
    prefetch: 1,
    attributes: JSON.stringify({ "course": "N/A", "prefetch": "0", "location": "N/A", "input_field-title-1": "StudentName", "input_field-config_key-1": "recharge_number", "input_field-title-2": "EmailId", "input_field-config_key-2": "recharge_number_2", "service": "Education", "input_field-title-3": "Contact", "input_field-config_key-3": "recharge_number_3", "input_field-regex-1": "^[a-zA-Z.]+$", "input_field-regex-2": "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[a-zA-Z]{2,}$", "operator_label": "GalgotiasUniversity", "operator": "GalgotiasUniversity", "service_label": "Education", "paytype_label": "NewRegistration", "paytype": "NewRegistration", "input_field-title-4": "College", "input_field-config_key-4": "recharge_number_4", "input_field-regex-3": "^(+91|0)?[1-9][0-9]{9}$", "input_field-regex-4": "^[a-zA-Z.]+$", "state": "UttarPradesh", "city": "GreaterNoida", "fee_type": "N/A", "school": "GalgotiasUniversity", "area": "GalgotiasUnifestPass", "fee_name": "noida-total fee" }),
    thumbnail: "https://assetscdn1.paytm.com/images/catalog/brand/Hillside School.jpg",
    created_at: "2019-01-04 09:00:08",
    updated_at: "2021-02-02 18:44:31",
    merchant_id: 267204,
    schedulable: 0
},
{
    id: 972,
    product_id: 972,
    vertical_id: 4,
    category_id: 18,
    brand: "airteltv",
    service: "DTH",
    paytype: "prepaid",
    operator: "airteltv",
    circle: "",
    producttype: "",
    service_label: "DTH",
    paytype_label: "Recharge",
    operator_label: "Airtel Digital TV",
    circle_label: "",
    producttype_label: "",
    info: JSON.stringify({"displayID": "Customer ID"}),
    updated_info: "",
    min_amount: 50,
    max_amount: 20000,
    status: 1,
    message: "Convenience fee is charged on fee amount & it varies based on payment mode selected",
    prefetch: 0,
    attributes: JSON.stringify({ "course": "N/A", "prefetch": "0", "location": "N/A", "input_field-title-1": "StudentName", "input_field-config_key-1": "recharge_number", "input_field-title-2": "EmailId", "input_field-config_key-2": "recharge_number_2", "service": "Education", "input_field-title-3": "Contact", "input_field-config_key-3": "recharge_number_3", "input_field-regex-1": "^[a-zA-Z.]+$", "input_field-regex-2": "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[a-zA-Z]{2,}$", "operator_label": "GalgotiasUniversity", "operator": "GalgotiasUniversity", "service_label": "Education", "paytype_label": "NewRegistration", "paytype": "NewRegistration", "input_field-title-4": "College", "input_field-config_key-4": "recharge_number_4", "input_field-regex-3": "^(+91|0)?[1-9][0-9]{9}$", "input_field-regex-4": "^[a-zA-Z.]+$", "state": "UttarPradesh", "city": "GreaterNoida", "fee_type": "N/A", "school": "GalgotiasUniversity", "area": "GalgotiasUnifestPass", "fee_name": "noida-total fee" }),
    thumbnail: "https://assetscdn1.paytm.com/images/catalog/operators/1547709645126.png",
    created_at: "2019-01-04 09:00:08",
    updated_at: "2021-02-02 18:44:31",
    merchant_id: 2,
    schedulable: 0
},
{
    id: 12345677,
    product_id: 12345677,
    vertical_id: 4,
    category_id: 18,
    brand: "airteltv",
    service: "UPI P2P",
    paytype: "prepaid",
    operator: "UPI P2P",
    circle: "",
    producttype: "",
    service_label: "DTH",
    paytype_label: "Recharge",
    operator_label: "Airtel Digital TV",
    circle_label: "",
    producttype_label: "",
    info: JSON.stringify({"displayID": "Customer ID"}),
    updated_info: "",
    min_amount: 50,
    max_amount: 20000,
    status: 1,
    message: "Convenience fee is charged on fee amount & it varies based on payment mode selected",
    prefetch: 0,
    attributes: JSON.stringify({ "course": "N/A", "prefetch": "0", "location": "N/A", "input_field-title-1": "StudentName", "input_field-config_key-1": "recharge_number", "input_field-title-2": "EmailId", "input_field-config_key-2": "recharge_number_2", "service": "Education", "input_field-title-3": "Contact", "input_field-config_key-3": "recharge_number_3", "input_field-regex-1": "^[a-zA-Z.]+$", "input_field-regex-2": "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+.[a-zA-Z]{2,}$", "operator_label": "GalgotiasUniversity", "operator": "GalgotiasUniversity", "service_label": "Education", "paytype_label": "NewRegistration", "paytype": "NewRegistration", "input_field-title-4": "College", "input_field-config_key-4": "recharge_number_4", "input_field-regex-3": "^(+91|0)?[1-9][0-9]{9}$", "input_field-regex-4": "^[a-zA-Z.]+$", "state": "UttarPradesh", "city": "GreaterNoida", "fee_type": "N/A", "school": "GalgotiasUniversity", "area": "GalgotiasUnifestPass", "fee_name": "noida-total fee" }),
    thumbnail: "https://assetscdn1.paytm.com/images/catalog/operators/1547709645126.png",
    created_at: "2019-01-04 09:00:08",
    updated_at: "2021-02-02 18:44:31",
    merchant_id: 2,
    schedulable: 0
},
{
    id: 65727,
    product_id: 33683541,
    vertical_id: 76,
    category_id: 26,
    brand: "Bangalore Electricity Supply Company Ltd. (BESCOM)",
    service: "Electricity",
    paytype: "postpaid",
    operator: "BESCOM",
    circle: "",
    producttype: "",
    service_label: "Electricity",
    paytype_label: "Bill Payment",
    operator_label: "BESCOM",
    circle_label: "",
    producttype_label: "",
    info: JSON.stringify({}),
    updated_info: "",
    min_amount: 1,
    max_amount: 4000000,
    status: 1,
    message: "Convenience fee is charged on fee amount & it varies based on payment mode selected",
    prefetch: 1,
    attributes: JSON.stringify({"service":"Electricity","paytype":"postpaid","operator":"BESCOM","service_label":"Electricity","paytype_label":"Bill Payment","operator_label":"BESCOM","min_amount":"1","max_amount":"4000000","prefetch":"1","input_field-title-1":"Consumer Id / Account Id","input_field-config_key-1":"recharge_number","input_field-regex-1":"^([0-9]{10}|[0-9]{7})$","district":"N/A","sub_district":"N/A","sub_division":"N/A","state":"Karnataka","board":"Bangalore Electricity Supply Company Ltd. (BESCOM)","input_field-sample_bill-1":"https://paytmofferlive.wpengine.com/wp-content/uploads/2017/03/Bescom-New-Bill.jpg","disclaimer":"Your service provider will take two working days to consider bill paid in their accounts.","nickname_label":"Name your bill","nickname_example":"Ex. Home Electricity","payment_schedule_label":"When do you want us to pay your bills?","payment_schedule_option_1_label":"On day of bill generation","payment_schedule_option_1_value":"-1","payment_schedule_option_2_label":"3 days before bill due date","payment_schedule_option_2_value":"3","card_opt_in":"true","input_selection":"N/A","input_field-right_button_url-1":"https://paytmofferlive.wpengine.com/wp-content/uploads/2017/03/Bescom-New-Bill.jpg","input_field-right_button_label-1":"View Sample Bill","product_timeout":"50"}),
    thumbnail: "https://assetscdn1.paytm.com/images/catalog/operators/*************.png",
    created_at: "2016-05-12 18:07:40",
    updated_at: "2022-08-03 07:00:20",
    merchant_id: 184408,
    schedulable: 1
}
];

export default {
    getRawCvrData,
    getCvrDataWithPidMapping
}