

class Producer{
    constructor (options){
    }

    initProducer(type, cb){
        return cb();
    }
}

class Consumer{
    constructor (options){
    }

    initConsumer( processMsgFn, cb){
        return cb(null);
    }
    _pauseConsumer() {
        return;
    }

    _resumeConsumer() {
        return;
    }
    
}

class cache{
    constructor(options){
    }
    connect(){
        return;
    }
    getData(){
        return;
    }
    updateData(){
        return;
    }
}

let kafka = {
    producer : Producer,
    consumer : Consumer,
};

export default {
    kafka,
    cache
};