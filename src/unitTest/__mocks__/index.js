import MOMENT from 'moment';

module.exports = {
    getDBRecord: (params) => {
        let a =  {
            id: 1,
            is_automatic: 0,
            customer_id: 1,
            recharge_number: params.rech_num || '1111 XXXX XXXX 2222',
            product_id: 123,
            operator: params.operator || 'paytmfirstcc',
            amount: 100,
            due_date: params.due_date,
            bill_date: params.bill_date,
            bill_fetch_date: MOMENT(),
            next_bill_fetch_date: params.nbfd || MOMENT(),
            gateway: 'paytmfirst',
            paytype: params.paytype || 'credit card',
            service: params.service || 'financial services',
            circle: '',
            customer_mobile: '9876543210',
            customer_email: '<EMAIL>',
            status: 0,
            userData: '',
            created_at: MOMENT(),
            updated_at: MOMENT(),
            extra: '',
            customerOtherInfo: '',
            payment_date: MOMENT(),
            notificationStatus: 1,
            serviceId: 1
        };
        return a;
    },

    getFFRResponse : (params) => {
        let a =  {
            catalogProductID: 32415,
            userData: {
                recharge_number: params.rech_num || '1111 XXXX XXXX 2222',
    
            },
            productInfo: {
                operator: params.operator || 'paytmfirstcc',
                service: params.service || 'financial services',
            },
            customerDataResponse:{
                currentBillAmount:4000,
                currentMinBillAmount:200,
                billDueDate: params.due_date,
                // nextBillFetchDate: MOMENT().add(1,'days').format('YYYY-MM-DD HH:MM:SS'),
                billDate: params.bill_date || MOMENT().format('YYYY-MM-DD'),
                invalid:0
            },
            validationGwResponse:{
                connectionError:  false,
                deducedStatus: true
            },
            customerInfo: {
                customer_id: 1,
                customer_mobile: 9876543210
            }
        };
        return a;
    },

    mockNotificationCallbackdata : () => {
        const callbackReq = {
          "status": "FAIL",
          "message": "javax.script.ScriptException: ReferenceError: \"tracking_url\" is not defined in <function> at line number 4",
          "job_id": "01d6fa36-4cbd-4ff3-84a1-8aee6e91b034"
      }
        return callbackReq;
    }

}