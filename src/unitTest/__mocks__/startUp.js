import L from 'lgr'

import REMIN<PERSON>R_CONFIG_LOADER from '../../config'

import SQL_MOCK from './sqlLayer'
import CVR_DATA from './cvrData'
import ACTIVE_PID_MOCK from './activePid'
import DYNA<PERSON>C_CONFIG_DATA from './dynamicConfigData'
import LOCALISATION_CLIENT from './localisationClient'
import INFRA_UTILS_MOCK from './infraUtils'
import DigitalReminderConfigLib_MOCK from './digitalReminderConfig'

function init(cb){

    // settung vault = 0 to avoid getting secrets from vault 
    process.env.VAULT = 0;

    REMINDER_CONFIG_LOADER()
    .then(function(config){
        let dbInstance = SQL_MOCK.mockedSql();
        
        // mocking infra-utils 
        let INFRAUTILS = INFRA_UTILS_MOCK;
    
        // setting cvr data
        config["CVR_DATA"] = CVR_DATA.getCvrDataWithPidMapping();
    
        // set dynamic config data
        config.DYNAMIC_CONFIG =  DYNAMIC_CONFIG_DATA.getMockedDynamicConfigData();

        let activePidLib = ACTIVE_PID_MOCK;

        // greyScaleEnv 
        let greyScaleEnv = false;

       let options =  {
            L,
            //rechargeConfig,
            config,
            dbInstance,
            // esInstance,
            // mongoDbInstance,
            INFRAUTILS,
            LOCALISATION_CLIENT,
            activePidLib,
            greyScaleEnv
        };
        DigitalReminderConfigLib_MOCK.updateParsedConfig(options.config);
        return cb(null,options);
    });
}

export default {
    init
}