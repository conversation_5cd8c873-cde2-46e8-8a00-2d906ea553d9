
function getMockedDynamicConfigData(){
    return DYNAMIC_CONFIG;
}

var DYNAMIC_CONFIG = {
    'OPERATOR_CONFIG' : {
        "tata power delhi distribution limited" : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        "airtel" : {
            "ENABLE_VALIDATION_SYNC" : 1
        },
        "jio" : {
            "ENABLE_VALIDATION_SYNC" : 1,
            "NEXT_BILL_FETCH_DATES" : -1,
            "CONNECTION_ERROR_SLOT_FROM": '00:00:00',
            "CONNECTION_ERROR_SLOT_TO": '23:59:59',
            "CONNECTION_ERROR_RESCHEDULE_TIME": '09:00:00',
            "VALIDATION_FAILED_SLOT_FROM":  '00:00:00',
            "VALIDATION_FAILED_SLOT_TO":  '23:59:59',
            "VALIDATION_FAILED_RESCHEDULE_TIME":  '10:00:00',
            "NO_BILL_SLOT_FROM":  '00:00:00',
            "NO_BILL_SLOT_TO":  '23:59:59',
            "NO_BILL_RESCHEDULE_TIME":  '16:00:00',
            "WRONG_DUE_DATE_SLOT_FROM":  '00:00:00',
            "WRONG_DUE_DATE_SLOT_TO":  '23:59:59',
            "WRONG_DUE_DATE_RESCHEDULE_TIME":  '11:00:00',
            "NOT_IN_USE_SLOT_FROM":  '00:00:00',
            "NOT_IN_USE_SLOT_TO":  '23:59:59',
            "NOT_IN_USE_RESCHEDULE_TIME":  '12:00:00',
            "EARLY_BILL_FETCH_SLOT_FROM":  '00:00:00',
            "EARLY_BILL_FETCH_SLOT_TO":  '23:59:59',
            "EARLY_BILL_FETCH_RESCHEDULE_TIME":  '13:00:00',
            "OLD_BILL_FOUND_SLOT_FROM":  '00:00:00',
            "OLD_BILL_FOUND_SLOT_TO":  '23:59:59',
            "OLD_BILL_FOUND_RESCHEDULE_TIME":  '14:00:00',
            
        },
        "kerala state electricity board ltd (kseb ltd)" : {
            "NO_BILL_SLOT_FROM":  '00:00:00',
            "NO_BILL_SLOT_TO":  '23:59:59',
            "NO_BILL_RESCHEDULE_TIME":  '16:00:00',
            "NEXT_BILL_FETCH_DATES" : -1,
        },

        "neft_sbi" : {
            "paytypes":['credit card']
        },

        'Hero FinCorp' : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        'Aditya Birla Finance Limited' : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        'Fullerton India credit company limited' : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        'Fullerton India Housing Finance Limited' : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        'Clix' : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        'StashFin-CAAS' : {
            "ALLOW_NULL_DUE_DATE" : 1
        },
        'L&T Finance Limited-CAAS' : {
            "ALLOW_NULL_DUE_DATE" : 1
        }
    },
    "SUBSCRIBER_CONFIG" : {
        "airtel" : {
            "BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS" : 1,
            "NEXT_RETRY_FREQUENCY" : 1,
            "NEXT_BILL_FETCH_DATES" : 30
        },
    },
    "NOTIFICATION_CONFIG" : {
        "BLACKLIST_BILLGEN_NOTIFICATION_OPERATOR" : {
            'airteltv' : 1
        },
        "BLACKLIST_DUEDATE_NOTIFICATION_OPERATOR" : {
            "airtel" : 1
        },
        "NOTIFICATION_CASSANDRA":{
            "FETCH_PRIORITY_ENABLE_FOR_SOURCE_IDS":['26'],
            "FETCH_DUPLICACY_ENABLE_FOR_SOURCE_IDS":['26'],
            "CREATE_CACHE_ENABLE_FOR_SOURCE_IDS":['26'],
            "NOTIFICATION_MIGRATED_FOR_SOURCE_IDS":['26', '38'],
            "ENABLE_ONE_PC_CUSTOMERS_FOR_SOURCE_IDS":['38'],
            "WRITE_NOTIFICATION_VIA_UNIQUE_KEY_ENABLED": ['25'],
            "FETCH_NOTIFICATION_VIA_UNIQUE_KEY_ENABLED" : ['26'],
            "DUPLICACY_CHECK_WITH_BASIC_APPROACH" : ['WHATSAPP'],
            "DUPLICACY_CHECK_WITH_ADVANCED_APPROACH" : ['PUSH'],
            "BASIC_APPROACH_DUPLICACY_CHECK_WHITELISTED_SERVICES" : ['electricity'],
            "ENABLE_ONE_PC_CUSTOMERS_FOR_SOURCE_IDS":['38']
        },
        "EMOJI_JAVA_ESCAPE_CODES": {
            "emoji1": "\\ud83d\\udca1",
            "emoji2": "\\u21e2"
        }
    },
    "BILL_PAYMENT_CONFIG" : {
        "ALLOWED_BANKS" : {
            "BANK_LIST" : ['SBI','KOTAK']
        }
    },
    "NOTIFICATION_EXCEPTIONS" : {
        "BLOCK_CUSTOMER_NOTIFICATIONS" : {
            "CUSTOMER_ID" : ['1212']
        }
    },
    "CC_BILLS_CONFIG": {
        "DWH_BANK_NAME_MAPPING":{
            "scb":"SC"
        },
        "NO_MCN_BANKS":{
            "BANK_LIST":['SC']
        },
        "NO_MINDUE_AMOUNT_BANKS":{
            "BANK_LIST":['SC']
        }
    },
    "FASTAG_CONFIG":{
        "FASTAG_CLASS_CONFIG":{
            "CLASS_LIST":['5v1']
        }
    },
    "POSTPAID_SMS_PARSING":{
        "airtel":{
            "PRODUCT_ID": *********
        },
        "jio":{
            "PRODUCT_ID": *********
        }
    },
    "NON_RU_CONFIG":{
        "NON_RU_SERVICES":{
            "ONBOARDED_SERVICES": ['upi p2p']
        }
    },
    "PREPAID_TABLES":{
        "TABLES":{
            "EXTRA": ['plan_validity']
        }
    },
    "REALTIME_SMS_PARSING":{
        "PREPAID_CLASSIFIERS":{
            "DATA_PACK_IDS":["1","2"],
            "VALIDITY_EXPIRY_IDS":["5","6"],
            "RECHARGE_DONE_IDS":["8"],
            "CT_EVENT_IDS":["3","4","7","9","10"]
        }
    },
    "DWH_ELECTRICITY_SMS_PARSING_CONFIG":{
        "DWH_OPERATOR_MAPPING":{
            "MSEDCL":"msedcl"
        },
        "OPERATOR_PRODUCT_ID_MAPPING":{
            "PID_MAP":{
                "tneb":1234567,
                "msedcl":12345678
            }
        }
    },
    "CC_PUBLISHER_CONFIG":{
        "SOURCE_DISABLED_FOR_NOTIFY":{
            "PG":1,
            "CIR":0

        }
    },
    "PREPAIDSMSPARSING":{
        "vodafone idea":{
            "DEFAULT_AMOUNT":299
        },
        "jio":{
            "DEFAULT_AMOUNT":299
        },
        "airtel":{
            "DEFAULT_AMOUNT":299
        }
    },
    "SMS_PARSING_RNO_CORRECTION_CONFIG":{
        "BESCOM_ELECTRICITY": {
            "REGEX_ACTIONS": [
                {
                    "regEx": "^(.{6})$",
                    "actions": [
                        {
                            "action": "appendAtFront",
                            "value": "0"
                        }
                    ]
                },
                {
                    "regEx": "^(00.{7})$",
                    "actions": [
                        {
                            "action": "removeFromFront",
                            "count": 2
                        }
                    ]
                }
            ]
        }
    },
    "NOTIFY_REJECTED_BILLS":{
        "ENABLED_SERVICES_PAYTYPES":{
            "SERVICE_PAYTYPE_LIST": ['FINANCIAL_SERVICES_CREDIT_CARD', 'MOBILE_PREPAID', 'ELECTRICITY_POSTPAID', 'ELECTRICITY_PREPAID', 'ELECTRICITY_AUTOPAY', 'MOBILE_AUTOPAY']
        },
        "CATEGORY_ID_MAP":{
            "FINANCIAL_SERVICES_CREDIT_CARD": 156705,
            "ELECTRICITY_POSTPAID":26,
            "ELECTRICITY_PREPAID": 15,
            "ELECTRICITY_AUTOPAY": 20,
            "MOBILE_AUTOPAY":30
        },
        "PERCENTAGE_LIVE_CUSTOMERS":{
            "PERCENTAGE" : 50
        },
        "ENABLED_SOURCES":{
            "ELECTRICITY_PREPAID": ["SMS_PARSING_DWH"],
            "ELECTRICITY_AUTOPAY": ["SMS_PARSING_DWH"],
            "MOBILE_AUTOPAY": ["SMS_PARSING_DWH"],
        },
        "ENABLED_ERROR_MESSAGES": {
            "ELECTRICITY_AUTOPAY": ['cannot process data', 'data format issue'],
            "MOBILE_AUTOPAY": ['cannot process data'],
        },
        "ERROR_MESSAGE_TO_ERROR_CODE_MAPPING":{
            'cannot process data': 'ERR_001'
        },
        "REJECTED_BILL_TEMPLATES":{
            "REJECTED_BILL_MOBILE_AUTOPAY_ERR_001_PUSH" : 1234,
            "REJECTED_BILL_MOBILE_AUTOPAY_ERR_001_EMAIL" : 2345,
            "REJECTED_BILL_MOBILE_AUTOPAY_ERR_001_CHAT" : 3456,
            "REJECTED_BILL_MOBILE_AUTOPAY_ERR_001_SMS" : 4567,
            "REJECTED_BILL_MOBILE_AUTOPAY_ERR_001_WHATSAPP" : 5678,
            "REJECTED_BILL_MOBILE_AUTOPAY_PUSH" : 9876,
            "REJECTED_BILL_MOBILE_AUTOPAY_EMAIL" : 8765,
            "REJECTED_BILL_MOBILE_AUTOPAY_CHAT" : 7654,
            "REJECTED_BILL_MOBILE_AUTOPAY_SMS" : 6543,
            "REJECTED_BILL_MOBILE_AUTOPAY_WHATSAPP" : 5432,
        },
        "MANDATORY_PARAMS_FOR_ERROR_CODE":{
            "ERR_001": ['customer_id', 'recharge_number', 'operator', 'service', 'paytype'],
            "ERR_002": ['customer_id', 'recharge_number', 'operator', 'service', 'paytype', 'due_date']
        }
    },
    "USER_SCORE_INGESTOR":{
        "USER_SCORE_BUCKET_CONFIG":{
            "mobile_prepaid":{
                "scores": [10,30,60,80], 
                "buckets": ['bronze','silver','gold','platinum'], 
                "nbfdHours":[0,-24,-48,-72],
                "thresholdScore":0
            },
            "electricity_postpaid":{
                "scores": [10,30,60,80], 
                "buckets": ['bronze','silver','gold','platinum'], 
                "nbfdHours":[0,-24,-48,-72],
                "thresholdScore":0
            },
            "electricity_prepaid":{
                "scores": [10,30,60,80], 
                "buckets": ['bronze','silver','gold','platinum'], 
                "nbfdHours":[0,-24,-48,-72],
                "thresholdScore":0
            },
            "mobile_postpaid":{
                "scores": [10,30,60,80], 
                "buckets": ['bronze','silver','gold','platinum'], 
                "nbfdHours":[0,-24,-48,-72],
                "thresholdScore":0
            },
        },
        "OPERATOR_WHITE_LIST" : {
            "OPERATOR_LIST" : ['airtel', 'jio'],
            "SERVICE_LIST" : ['electricity']
        },
    },
    "TEMPLATE_CONFIG":{
        "1234":{
            "MAPPED_TEMPLATE_ID": "1234567890"
        }
    },
    "WHATSAPP_NOTIFICATION_FALLBACK":{
        "WHITELISTED_EVENTS":{
            "EVENTS": ['sent']
        },
        "WHITELISTED_ERRORS_FOR_SMS_INVOKE":{
            "ERRORS": ['ALL']
        },
        "WHITELISTED_EVENTS_FOR_USER_RESPONSE":{
            "EVENTS": ['clicked']
        },
        "STATIC_SMS_URL":{
            "URL": "https://m.paytm.me/rentcon?rcon=2"
        },
        "COMMON":{
            "TYPE": "SMS",
            "CATEGORY_ID": 1,
            "SOURCE_ID": 42
        }
    },
    "DUPLICATE_CA_NUMBER_OPERATOR":{
        "airtel":{
            "PREFIX": "0"
        }
    }
};

export default {
    getMockedDynamicConfigData
}; 