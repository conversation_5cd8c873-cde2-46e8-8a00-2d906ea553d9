

function mockedSql(){
    return {
        exec : function (callback, dbName, query, queryParams){
            let response = prepareResponse(dbName, query, queryParams);
            console.log("__mock__::sql:exec",dbName, query, queryParams, response);
            return callback(null, response);
        },
        format : function (query, queryParams){
            return ''; // dummy
        }
    };
}

function prepareResponse(dbName, query, queryParams) {
    //
    let keys = Object.keys(MOCK_SQL_CONFIG);
    let queryStr = query ? query : '';
    let queryParamsStr = queryParams ? queryParams.join('|') : '';

    for(let key of keys){
        let arr = key.split('|');
        let queryType = query ? query.split(' ')[0]: '';
        // check for priority 1
        if(arr && arr.length == 3){
            if((queryStr.includes(arr[0]) || queryParamsStr.includes(arr[0])) && 
                arr[1] == queryType && 
                arr[2] == dbName
            ) {
                return MOCK_SQL_CONFIG[key];
            }
        }

        // check for priority 2
        if(arr && arr.length == 2){
            if((queryStr.includes(arr[0]) || queryParamsStr.includes(arr[0])) && 
                arr[1] == queryType
            ) {
                return MOCK_SQL_CONFIG[key];
            }
        }

        // check for priority 3
        if(arr && arr.length == 1){
            if(queryStr.includes(arr[0]) || queryParamsStr.includes(arr[0])
            ) {
                return MOCK_SQL_CONFIG[key];
            }
        }

        // check for priority 4
        if(arr && arr.length == 2){
            if( 
                arr[0] == queryType && 
                arr[1] == dbName
            ) {
                return MOCK_SQL_CONFIG[key];
            }
        }

        // check for priority 5
        if(arr && arr.length == 1){
            if( 
                arr[0] == queryType
            ) {
                return MOCK_SQL_CONFIG[key];
            }
        }
    }

    return MOCK_SQL_CONFIG["DEFAULT"];
}

/*
    pattern of config : grep parameter + query type + DB type... 
    Exa: testRn1_UPDATE_DIGITAL_REMINER_MASTER
    Config can be anything like below..
    1. Grep text: Test which can exists in query or queryParams. exa. test1, rnTest2
    2. query type : SELECT, UPDATE, DELETE, INSERT
    3. DB : DIGITAL_REMINER_MASTER, DIGITAL_REMINER_SLAVE,...

    Priority Order: 
    1. {Grep text}|{query type}|{db}    -> testRn1_UPDATE_DIGITAL_REMINER_MASTER
    2. {Grep text}|{query type}         -> testRn1_UPDATE
    3. {Grep text}                      -> testRn1
    4. {query type}|{db}                -> UPDATE_DIGITAL_REMINER_MASTER,SELECT_DIGITAL_REMINER_MASTER
    5. {query type}                     -> UPDATE
    6. DEFAULT

    Note: Earlier key will be given preference so take care of sequence while putting keys. More specific keys should be come earlier in array
    i.e, rn_12 should come before rn_1 -> since we are checking for subscting in query and queryParams
*/
//
var MOCK_SQL_CONFIG = {
    "SELECT * from catalog_vertical_recharge|SELECT|FS_RECHARGE_SLAVE1" : [
        {
            id: 202,
            product_id: 202,
            vertical_id: 4,
            category_id: 21,
            brand: 'Airtel',
            service: 'Mobile',
            paytype: 'postpaid',
            operator: 'Airtel',
            circle: 'Jammu Kashmir',
            attributes: `{"ref_id":"BBPS Reference Number","show_browse_plan":"0","reminder_flag":"1"}`
        },
        {
            id: 234,
            product_id: 234,
            vertical_id: 4,
            category_id: 21,
            brand: 'Vodafone',
            service: 'Mobile',
            paytype: 'postpaid',
            operator: 'Vodafone',
            circle: 'Assam',
            attributes: `{"voda_fetchbill":"TRUE","remindable":"2","automatic_type":"postpaid","schedulable":"1"}`
        },
        {
            id: 235,
            product_id: 235,
            vertical_id: 4,
            category_id: 21,
            brand: 'Vodafone',
            service: 'Mobile',
            paytype: 'postpaid',
            operator: 'Vodafone',
            circle: 'Assam',
            attributes: `{ } `
        },
        {
            id: 236,
            product_id: 235,
            vertical_id: 4,
            category_id: 21,
            brand: 'Vodafone',
            service: 'Mobile',
            paytype: 'postpaid',
            operator: 'Vodafone',
            circle: 'Assam'
            // attributes: ``
        },
        {
            id: 237,
            product_id: 235,
            vertical_id: 4,
            category_id: 21,
            brand: 'Vodafone',
            service: 'Mobile',
            paytype: 'postpaid',
            operator: 'Vodafone',
            circle: 'Assam',
            attributes: ''
        },
        {
            id: 237,
            product_id: 235,
            vertical_id: 4,
            category_id: 21,
            brand: 'Vodafone',
            service: 'Mobile',
            paytype: 'postpaid',
            operator: 'Vodafone',
            circle: 'Assam',
            attributes: '{' //invalid json
        }
    ],
    "987654318888888800" : [{ "recharge_number" : "56XX XXXX 8767" },{ "recharge_number" : "72XX XXXX 8767" }],
    "987654318881": [{"recharge_number" : "56XX XXXX 8767", "reference_id" : "20191229359104e7b3ef518642698b61c7403ed4b506c", "id":123, "customer_id": 12345},{"recharge_number" : "56XX XXXX 8767", "reference_id" : "357ca6eb-f614-482e-97d8-377bdfd75115", "id": 124, "customer_id": 12345}],
    "667771" : [{ "recharge_number" : "56XX XXXX 8767" }],
    "98786756" : [{ "recharge_number" : "rn_which_do_not_have_matching_mcn_in_db" }],
    "recents_airtel_rn_001" : [{ "recharge_number" : "recents_airtel_rn_001" }],
    "DEFAULT" : {}
}

export default {
    mockedSql
}