import _ from 'lodash'

function updateParsedConfig(config){
    let parsedConfig = {

        "KAFKA" : {
            "SERVICES" : {
                RECENTBILL: {
                    RECHARGE_CONSUMER_TOPICS: ['RECHARGE_ORDER', 'ED<PERSON><PERSON><PERSON>_ORDER',
                        'B<PERSON><PERSON>_INSURANCE_ORDER', 'BFSI_METRO_ORDER',
                        'UTILITY_ELECTRICITY_ORDER', 'BFSI_GOLD_ORDER',
                        'B<PERSON>I_TOLL_ORDER', 'BFSI_GOOGLEPLAY_ORDER',
                        'UTILITY_CHALLAN_ORDER', 'DONATION_ORDER',
                        'TAPTOPAY_ORDER', 'DEFAULT_ORDER']
                },
                NOTIFICATION: {
                    CONSUMER_TOPICS: ['EDUCATION_ORDER']
                },
                RECHARGE_NUDGE_CONSUMERS: {
                    VALIDATION_TOPICS: ['UTILITY_ELECTRICITY_VALIDATION'],
                    RECHARGE_TOPICS: ['UTILITY_ELECTRICITY_ORDER', 'DEFAULT_ORDER'],
                    REC<PERSON>R<PERSON>_CC_TOPICS: ['BFSI_INSURANCE_ORDER'],
                },
                REMINDER_SYNC: {
                    REMINDER_SYNC_TOPICS: ['REMINDER_SYNC']
                },
                AUTOMATIC_SYNC: {
                    AUTOMATIC_SYNC_TOPIC: 'AUTOMATIC_SYNC'
                },
                AUTOMATIC_SYNC_RECENT: {
                    AUTOMATIC_SYNC_RECENT_TOPIC: 'AUTOMATIC_SUBS_DATA'
                },
                REMINDER_BILLFETCH_PIPELINE: {
                    REMINDER_BILL_FETCH: "REMINDER_BILL_FETCH",
                },
                REMINDER_BILLFETCH_PIPELINE_REALTIME: {
                    REMINDER_BILL_FETCH_REALTIME: "REMINDER_BILL_FETCH_REALTIME",
                },
                NONRU_NOTIFICATION_PIPELINE_REALTIME: {
                    NONRU_NOTIFICATION_REALTIME: "NONRU_REMINDER_BILL_FETCH_REALTIME",
                },
                VALIDATION_SYNC: {
                    VALIDATION_TOPICS:['UTILITY_ELECTRICITY_VALIDATION','DEFAULT_VALIDATION','BFSI_INSURANCE_VALIDATION','RECHARGE_VALIDATION','MNP_VALIDATION']
                },
                SMS_PARSING_CC: {
                    SMS_PARSING_CC_TOPIC: "dwh-ingest-SMS_PARSING_CC_BILLS",
                },
                SMS_PARSING_CC_RU_REALTIME: {
                    SMS_PARSING_CC_RU_REALTIME_TOPIC: "ru_sms_reminder",
                },
                SMS_PARSING_CC_DWH_REALTIME: {
                    SMS_PARSING_CC_DWH_REALTIME_TOPIC: "REALTIME_SMS_PARSER_CC_BILLS",
                },
                SMS_PARSING_FASTAG : {
                    SMS_PARSING_FASTAG_TOPIC : "dwh-ingest-SMS_PARSING_FASTAG",
                },
                EMI_DUE_DETAILS: {
                    EMI_DUE_DETAILS_TOPIC : 'EMI_DUE_DETAILS'
                },
                EMI_DUE_COMMON_DETAILS: {
                    EMI_DUE_COMMON_DETAILS_TOPIC : 'EMI_DUE_COMMON_DETAILS'
                },
                PLAN_VALIDITY_SYNC_DB: {
                    TOPIC: "SYNC_DB_ORDER"
                },
                REMINDER_MAXWELL: {
                    TOPIC: "REMINDER_MAXWELL",
                },
                PLAN_VALIDITY_MAXWELL: {
                    TOPIC: "PLAN_VALIDITY_MAXWELL",
                },
                REMINDER_CYLINDER: {
                    TOPIC: "ivrs",
                },
                CT_EVENTS_PUBLISHER: {
                    TOPIC: "cdo-ru-reminders-reminderEvents"
                },
                NON_PAYTM_RECORDS_CONSUMER: {
                     TOPIC: ["NON_PAYTM_RECORDS", "NON_PAYTM_RECORDS_DWH"]
                },
                UPDATE_RECENTS: {
                    TOPIC: "mongo.inUsers.users"
                },
                NON_PAYTM_RECORDS: {
                    TOPIC: "NON_PAYTM_RECORDS"
                },
                NON_PAYTM_RECORDS_DWH:{
                    TOPIC: "NON_PAYTM_RECORDS_DWH"
                },
                SMS_PARSING_BILL_PAYMENT: {
                  TOPIC: "SMS_PARSING_TELECOM"
                },
                SMS_PARSING_BILL_PAYMENT_DWH_REALTIME: {
                    TOPIC: "REALTIME_SMS_PARSER_TELECOM"
                },
                CC_SMS_PARSING_BILL_PAYMENT: {
                    TOPIC: "dwh-ingest-SMS_PARSING_CC_BILLS_PAYMENT"
                },
                ELECTRICITY_SMS_PARSING_BILL_PAYMENT:{
                    TOPIC : "SMS_PARSER_ELECTRICITY"
                },
                ELECTRICITY_SMS_PARSING_BILL_PAYMENT_DWH_REALTIME:{
                    TOPIC : "REALTIME_SMS_PARSER_ELECTRICITY"
                },
                RENT_SMS_PARSING_BILL_PAYMENT:{
                    TOPIC : "SMS_PARSING_RENT"
                },
                PAYTM_FIRST_CC_EVENTS_PUBLISHER : {
                    TOPIC : "lending-cc-ccbp-event"
                },
                FAILED_SMS_PARSING_PUBLISHER : {
                    TOPIC : "FAILED_SMS_PARSING_REGEX_BACKUP"
                },
                SMS_PARSING_LOAN_EMI: {
                    TOPIC : "SMS_PARSING_LOANEMI"
                },
                OPERATOR_HEALTH: {
                    TOPIC : "OPERATOR_HEALTH"
                },
                PAYTM_POSTPAID:{
                    TOPIC : "PAYTM_POSTPAID_BILLS"
                },
                CASSANDRA_CDC: {
                    TOPIC : "CDC_RECOVERY"
                },
                REALTIME_SMS_PARSING_PREPAID: {
                    TOPIC : "ru_sms_parsing_prepaid"
                },
                REALTIME_SMS_PARSING_POSTPAID: {
                    TOPIC : "ru_sms_parsing_postpaid"
                },
                NONRU_BILL_FETCH:{
                    TOPIC : "NONRU_REMINDER_BILL_FETCH"
                },
                NOTIFICATION_FALLBACK: {
                    TOPIC : ["push_delivery_topic", "delivery_status_topic"],
                    WHATSAPP_TOPIC : "whatsapp-status-reporter"
                },
                AIRTEL_BILL_FETCH: {
                    TOPIC : "AIRTEL_PREPAID_RECORDS"
                },
                PUBLISHER_NON_RU:{
                    TOPIC:"PUBLISHER_BILL_FETCH"
                }, 
                NOTIFICATION_REJECTS: {
                    TOPIC : "REMINDER_NOTIFICATION_REJECT_DATA"
                },
                BILL_FETCH_ANALYTICS:{
                    TOPIC: "bills_analytics_data"
                },
                CUSTOM_NOTIFICATIONS_PIPELINE:{
                    CUSTOM_NOTIFICATIONS:"CUSTOM_NOTIFICATIONS"
    
                },
                CASSANDRA_NOTIFICATION_DWH: {
                    TOPIC : "REMINDER_NOTIFICATION_DATA_NONRU"
                },
                PUBLISHER_NON_RU_MULTIPLE_PID: {
                    TOPIC: "MULTIPLE_PID_TOPIC"
                },
                NOTIFICATIONS_DLQ: {
                    TOPIC: "NOTIFICATIONS_DLQ"
                }
            }
        },
        "OPERATOR_TEMPLATE_MAPPING" : {
            "tata power delhi distribution limited": {
                "DUEDATE_PUSH":7143,
                "DUEDATE_CHAT":7588,
                "DUEDATE_SMS":7137,
                "DUEDATE_EMAIL":6604,
                "DUEDATE_PUSH_NOAMOUNT":9013,
                "DUEDATE_CHAT_NOAMOUNT":9014,
                "DUEDATE_SMS_NOAMOUNT":9015,
                "DUEDATE_EMAIL_NOAMOUNT":9016,
                "BILLGEN_PUSH":7186,
                "BILLGEN_CHAT":7581,
                "BILLGEN_SMS":7187,
                "BILLGEN_EMAIL":6555,
                "BILLGEN_PUSH_NOAMOUNT":9017,
                "BILLGEN_CHAT_NOAMOUNT":9018,
                "BILLGEN_SMS_NOAMOUNT":9019,
                "BILLGEN_EMAIL_NOAMOUNT":9020,
                "BILLGEN_PUSH_NOAMOUNT_NODUEDATE":9021,
                "BILLGEN_CHAT_NOAMOUNT_NODUEDATE":9022,
                "BILLGEN_SMS_NOAMOUNT_NODUEDATE":9023,
                "BILLGEN_EMAIL_NOAMOUNT_NODUEDATE":9024,
                "BILLGEN_PUSH_NODUEDATE":9025,
                "BILLGEN_CHAT_NODUEDATE":9026,
                "BILLGEN_SMS_NODUEDATE":9027,
                "BILLGEN_EMAIL_NODUEDATE":9028
            }
        },
        "RECENT_BILL_CONFIG" : {
            "CUSTOMER_IDS": [
                "11292619",
                "11613001",
                "19102731",
                "20971466",
                "26078282",
                "28570735",
                "136554028",
                "152942826",
                "198165771",
            ],
            "OPERATORS": {
                "tata power delhi distribution limited" : {
                    "daysToNextDueDate" : 10
                },
                "vodafone" : {
                    "daysToNextDueDate" : 10
                },
                "kerala state electricity board ltd (kseb ltd)" : {
                    "INVALID_BILLDATE_DUEDATE" : 1
                },
                "neft_sbi" : {
                    "paytypes": ['credit card']
                },
                "neft_sbibank" : {
                    "paytypes": ['credit card']
                },
                "lic": {
                    "paytypes": ['postpaid']
                },
                "rent payment": {
                    "paytypes" : ['postpaid']
                }
            },
            "COMMON" : {
                "EXCLUDE_CHANNEL_ID": ['DUMMY_CHANNEL']
            },
        },
        "OPERATOR_NOT_IN_USE_CONFIG": {
            "tata power delhi distribution limited" : 2,
            "vodafone" : 5
        },
        "OPERATOR_TABLE_REGISTRY" : {
            "tata power delhi distribution limited": "bills_tatapower",
            "airtel": "bills_airtel",
            "StashFin-CAAS": "bills_table",
            "neft_sbi": "bills_creditcard",
            "lic": "bills_lic",
            "rent payment": "bills_rentpayment",
            "bsnl": "bills_bsnl",
            "paytm postpaid": "bills_paytmpostpaid",
            "tneb":"bills_tneb"
        },
        "SUBSCRIBER_CONFIG" : {
            "DURATION" : {
                "EARLIEST_BILL_FETCH_DELAY" : 1
            },
            "UNIT" : {
                "EARLIEST_BILL_FETCH_DELAY" : "month"
            }
        },
        "PUBLISHER_CONFIG" : {

        },
        'CVR_DATA' :{
            '1234': {
                'thumbnail': 'testThumbnail.jpg'
            },
            '1201342837': {
                'operator': 'operator'
            }
        },
        "NOTIFICATION" : {
            "BlackListOperator": [
                'dps_dps noida', 'dps_dps greater noida', 'manappuram', 'icici prudential life insurance',
                'indiafirst life insurance', 'matrix postpaid','tikona broadband', 'connect broadband', 'rent payment',
                'indane' , 'bharatgas' , 'hp gas' , 'lic' , 'manipur state power distribution company limited (mspdcl)'
            ],
            "TEMPLATE_UTM" : {
                notFound: {
                    "utm_source": "billReminderNotFound",
                    "utm_medium": "null",
                    "utm_campaign": "null"
                },
                4597: {
                    "utm_source": "billGenReminder",
                    "utm_medium": "email",
                    "utm_campaign": "4597"
                },
                4598: {
                    "utm_source": "billGenReminder",
                    "utm_medium": "sms",
                    "utm_campaign": "4598"
                },
                4868: {
                    "utm_source": "billGenReminder",
                    "utm_medium": "push",
                    "utm_campaign": "4868"
                    
                },
                4600: {
                    "utm_source": "billDueDateReminder",
                    "utm_medium": "sms",
                    "utm_campaign": "4600"
                },
                7126: {
                    "utm_source": "billGenReminder",
                    "utm_medium": "push",
                    "utm_campaign": "7126"
                },
                8169: {
                    "utm_source": 'billDueDateReminder', 
                    "utm_medium": "sms",
                    "utm_campaign": "8169"  
                }
            },
            "TEMPLATE_ID_BY_SERVICE" : {
                "BR_MOBILE_BILLGEN_SMS": 7127,
                "BR_MOBILE_BILLGEN_PUSH": 7126,
                "BR_MOBILE_BILLGEN_CHAT": 7573,

                "BR_MOBILE_DUEDATE_SMS": 7098,
                "BR_MOBILE_DUEDATE_PUSH": 7099,
                "BR_MOBILE_DUEDATE_CHAT": 7580,

                "BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT": 9001,
                "BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT": 9002,
                "BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT": 9003,
                "BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT": 9038,

                "BR_MOBILE_BILLGEN_GENERIC_SMS_NOAMOUNT_NODUEDATE": 9004,
                "BR_MOBILE_BILLGEN_GENERIC_PUSH_NOAMOUNT_NODUEDATE": 9005,
                "BR_MOBILE_BILLGEN_GENERIC_CHAT_NOAMOUNT_NODUEDATE": 9006,
                "BR_MOBILE_BILLGEN_GENERIC_EMAIL_NOAMOUNT_NODUEDATE": 9039,

                "BR_MOBILE_BILLGEN_GENERIC_SMS_NODUEDATE": 9007,
                "BR_MOBILE_BILLGEN_GENERIC_PUSH_NODUEDATE": 9008,
                "BR_MOBILE_BILLGEN_GENERIC_CHAT_NODUEDATE": 9009,
                "BR_MOBILE_BILLGEN_GENERIC_EMAIL_NODUEDATE": 9040,

                "BR_MOBILE_DUEDATE_SMS_NOAMOUNT": 9010,
                "BR_MOBILE_DUEDATE_PUSH_NOAMOUNT": 9011,
                "BR_MOBILE_DUEDATE_CHAT_NOAMOUNT": 9012,
                "BR_MOBILE_DUEDATE_EMAIL_NOAMOUNT":9030,

                "BR_MOBILE_BILLGEN_GENERIC_SMS": 8167,
                "BR_MOBILE_BILLGEN_GENERIC_PUSH": 8168,
                "BR_MOBILE_BILLGEN_GENERIC_CHAT": 8171,
                "BR_MOBILE_BILLGEN_GENERIC_EMAIL": null,


                "BR_MOBILE_DUEDATE_GENERIC_SMS": 8169,
                "BR_MOBILE_DUEDATE_GENERIC_PUSH": 8170,
                "BR_MOBILE_DUEDATE_GENERIC_CHAT": 8172,
                "BR_MOBILE_DUEDATE_GENERIC_EMAIL": null,

                "BR_FINANCIAL SERVICES_7_DUEDATE_SMS": null,
                "BR_FINANCIAL SERVICES_7_DUEDATE_PUSH": 8621,
                "BR_FINANCIAL SERVICES_7_DUEDATE_CHAT": 8628,
                "BR_FINANCIAL SERVICES_7_DUEDATE_EMAIL": null,

                "BR_FINANCIAL SERVICES_7_DUEDATE_SMS_NOAMOUNT": 9035,
                "BR_FINANCIAL SERVICES_7_DUEDATE_PUSH_NOAMOUNT": 9036,
                "BR_FINANCIAL SERVICES_7_DUEDATE_CHAT_NOAMOUNT": 9036,
                "BR_FINANCIAL SERVICES_7_DUEDATE_EMAIL_NOAMOUNT": 9037,

                "BR_FINANCIAL SERVICES_1_DUEDATE_SMS": null,
                "BR_FINANCIAL SERVICES_1_DUEDATE_PUSH": 7043,
                "BR_FINANCIAL SERVICES_1_DUEDATE_CHAT": 8627,
                "BR_FINANCIAL SERVICES_1_DUEDATE_EMAIL": null,

                "BR_FINANCIAL SERVICES_-1_DUEDATE_SMS": null,
                "BR_FINANCIAL SERVICES_-1_DUEDATE_PUSH": 8623,
                "BR_FINANCIAL SERVICES_-1_DUEDATE_CHAT": 8630,
                "BR_FINANCIAL SERVICES_-1_DUEDATE_EMAIL": null,

                "BR_FINANCIAL SERVICES_-1_DUEDATE_SMS_NOAMOUNT": 9031,
                "BR_FINANCIAL SERVICES_-1_DUEDATE_PUSH_NOAMOUNT": 9032,
                "BR_FINANCIAL SERVICES_-1_DUEDATE_CHAT_NOAMOUNT": 9033,
                "BR_FINANCIAL SERVICES_-1_DUEDATE_EMAIL_NOAMOUNT": 9034,

                "BR_FINANCIAL SERVICES_7_BILLGEN_SMS_CIR": 9034,
                "BR_FINANCIAL SERVICES_7_BILLGEN_PUSH_CIR": 9035,
                "BR_FINANCIAL SERVICES_7_BILLGEN_CHAT_CIR": 9036,


                "BR_FINANCIAL SERVICES_7_BILLGEN_PUSH": 9037,
                "BR_FINANCIAL SERVICES_BILLGEN_SMS_CIR": 9038,
            }
        },
        "MONGO": {
          //RECENT_UPDATE_URL: 'http://localhost:4000/v1/recentUpdate/admin',
        //RECENT_FETCH_URL : 'http://localhost:4000/v1/recentfetch/admin',
        RECENT_FETCH_URL : 'https://run.mocky.io/v3/bd7ea8a1-c8ec-4f52-83ea-2b9dc95a6799',
        REMOVE_RECENTS : 'https://run.mocky.io/v3/6d46cb5e-ff57-420d-bef6-52cdaf6179de',
        RECENT_UPDATE_URL: 'https://run.mocky.io/v3/5e3d1d08-79cb-4c1b-b61b-0bf6ee0edbdb',
        //RECENT_FETCH_URL : 'https://run.mocky.io/v3/886d25e7-b8cf-4f14-a9c6-54ef8fa9bd83',
        //RECENT_FETCH_URL : 'https://run.mocky.io/v3/bcd70131-d791-43b3-941f-d2e5304b52d2',
        MASTER: {
              
                host          : 'localhost',
                replicaHost   : 'localhost',
                replicaHostStr: 'localhost:27017',
                replicas      : ['localhost'],
                options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port          : 27017
            },
            SLAVE: {
             
                host          : 'localhost',
                replicaHost   : 'localhost',
                replicaHostStr: 'localhost:27017',
                replicas      : ['localhost'],
                options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port          : 27017
            },
            HIDDEN_SLAVE: {
                username      : '123',
                password      : '321',
                host          : 'localhost',
                replicaHost   : 'localhost',
                replicaHostStr: 'localhost:27017',
                replicas      : ['localhost'],
                options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
                db            : 'inUsers',
                poolSize      : 5,
                port          : 27017
            }
        },
        "VIL_SYNC_DB": {
            MONGO_DB: {
            RETRY_COUNT: 5,
            FETCH_LIMIT: 50,
            TPS: 3,
            FAILURE_RETRY_INTERVAL: 15 * 60 * 1000,
            TPS_SWITCH_HOUR: '0:5',
            SWITCH_FETCH_LIMIT: 5,
            SWITCH_TPS: 1,
        },

        MYSQL: {
            FROM_ID: 0,
            FETCH_LIMIT: 100,
            SKIP_LIMIT: 0,
        },
        OLD_RECORD : {
            UPDATION_LIMIT: 2,
            SLEEP_TIME: 900,
        },
        KAFKA: {
            PUBLISHING_RATE: 1,
            SWITCH_PUBLISHING_RATE: 2,
        }
        }
    };

    for (const key in parsedConfig) {
        _.extend(config[key], parsedConfig[key]);   
    }
}



export default {
    updateParsedConfig
}; 