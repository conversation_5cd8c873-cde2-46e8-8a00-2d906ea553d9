'use strict';

import { describe, it, before, afterEach, beforeEach} from 'mocha';
import sinon from 'sinon';
import chai, { assert } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
import MOMENT from 'moment'
import _ from 'lodash';

import BILLGENPUBLISHER from '../../crons/billGenPublisher'
import STARTUP_MOCK from '../__mocks__/startUp'
import ASYNC from 'async'


chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("billGenPublisher ::  test cases :: ", function () {
    let serviceObj;

    let data, record;
    before(function (done) { // <-- Add done callback here
        STARTUP_MOCK.init(function(error, options){
            serviceObj = new BILLGENPUBLISHER(options);
            done(error); // Call done with the error argument
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });

    // it('should configure the Kafka publisher and call the callback with no error', (done) => {
    //     // Mock dependencies, you can use sinon.stub() for this purpose
    //     const fakeKafkaPublisher = {
    //       initProducer: sinon.stub().callsArgWith(1, null),
    //     };
  
    //     // Stub infraUtils.kafka.producer constructor to return the fake Kafka publisher
    //     sinon.stub(serviceObj.infraUtils.kafka, 'producer').returns(fakeKafkaPublisher);
  
    //     // Stub the logger (self.L.log) to prevent actual logging during testing
    //     sinon.stub(serviceObj.L, 'log');
  
    //     // Call the function to be tested
    //     serviceObj.configureKafkaPublisher((error) => {
    //       // Assertions
    //       assert.strictEqual(error, null, 'No error should be passed to the callback');
    //       assert.strictEqual(serviceObj.kafkaPublisher, fakeKafkaPublisher, 'Kafka publisher should be set');
  
    //       // Check if the constructor of Kafka publisher was called with the correct arguments
    //       assert.strictEqual(serviceObj.infraUtils.kafka.producer.callCount, 1, 'infraUtils.kafka.producer should be called once');
    //       // Extract the argument object from the array before comparing
    //         const kafkaProducerArgs = serviceObj.infraUtils.kafka.producer.firstCall.args[0];

    //         assert.deepStrictEqual(kafkaProducerArgs, { kafkaHost: 'localhost:9092' }, 'The correct Kafka configuration should be passed');
    
    //         // Check if the initProducer function was called with the correct argument
    //         assert.strictEqual(fakeKafkaPublisher.initProducer.callCount, 1, 'initProducer should be called once');
    
    //         // Check if the logger function was called with the correct arguments
    //         assert.strictEqual(serviceObj.L.log.callCount, 1, 'Logger should be called once');
    //         assert.deepStrictEqual(serviceObj.L.log.firstCall.args, ['notify :: configureKafkaPublisher', 'publisher Configured']);
    
    //         // Restore the mocked functions
    //         serviceObj.infraUtils.kafka.producer.restore();
    //         serviceObj.L.log.restore();
    
    //         // Call done() to finish the test
    //         done();
    //     });
    // });

    // it('should process all tables and call the callback when done', (done) => {
    //     // Mock dependencies, you can use sinon.stub() for this purpose
    //     const fakeOperatorList = {
    //       op1: 'jio',
    //       op2: 'airtel',
    //     };
  
    //     const fakeOperatorTableMap = {
    //       op1: 'bills_jio',
    //       op2: 'bills_airtel',
    //     };
  
    //     const fakeOffsetIdMap = {
    //       Table1: {},
    //       Table2: {},
    //     };

    //     // Custom assertion function to check if the argument is a function
    //     const isFunction = (func) => typeof func === 'function';
  
    //     sinon.stub(serviceObj, 'operatorList').value(fakeOperatorList);
    //     sinon.stub(serviceObj, 'operatorTableMap').value(fakeOperatorTableMap);
    //     sinon.stub(serviceObj, 'offsetIdMap').value(fakeOffsetIdMap);
  
    //     // Mock the notificationLibrary.getPossibleBillGenDates function
    //     sinon.stub(serviceObj.notificationLibrary, 'getPossibleBillGenDates')
    //       .returns(['interval1', 'interval2']);
  
    //     // Mock the processBills function
    //     sinon.stub(serviceObj, 'processBills').callsFake((table, billGenDateIntervals, callback) => {
    //       // Do any necessary checks or assertions here
    //       // You can also simulate asynchronous behavior with setTimeout if needed
    //       callback();
    //     });
  
    //     // Mock the logger function (self.L.log) to prevent actual logging during testing
    //     sinon.stub(serviceObj.L, 'log');
  
    //     // Call the function to be tested
    //     serviceObj.processOperators((err) => {
    //       // Assertions
    //       assert.strictEqual(err, null, 'No error should be passed to the callback');
  
    //       // Check that all expected function calls were made
    //       assert.strictEqual(serviceObj.processBills.callCount, 2, 'processBills should be called twice');
    //       assert.strictEqual(serviceObj.notificationLibrary.getPossibleBillGenDates.callCount, 2, 'getPossibleBillGenDates should be called twice');
    //       assert.strictEqual(serviceObj.L.log.callCount, 3, 'Logger should be called three times');
  
    //     // Check the arguments passed to processBills
    //     assert.strictEqual(serviceObj.processBills.firstCall.args[0], 'bills_jio');
    //     assert.strictEqual(serviceObj.processBills.secondCall.args[0], 'bills_airtel');

    //     assert.deepStrictEqual(serviceObj.processBills.firstCall.args[1], ['interval1', 'interval2']);
    //     assert.deepStrictEqual(serviceObj.processBills.secondCall.args[1], ['interval1', 'interval2']);

    //     // Check if the third argument is a function using our custom assertion function
    //     assert.ok(isFunction(serviceObj.processBills.firstCall.args[2]));
    //     assert.ok(isFunction(serviceObj.processBills.secondCall.args[2]));
    //       // Restore the mocked functions
    //       serviceObj.notificationLibrary.getPossibleBillGenDates.restore();
    //       serviceObj.processBills.restore();
    //       serviceObj.L.log.restore();
  
    //       // Call done() to finish the test
    //       done();
    //     });
    //   });  

    it('processBills :: should call done with no error when billGenDateIntervals is empty', function(done) {
        const tableName = 'bills_airtel';
        const billGenDateIntervals = [];
        serviceObj.processBills(tableName, billGenDateIntervals, function(error) {
            assert.equal(error, null);
            done();
        });
    });

    // it('processBills :: should call processBillsForEachBillGenDate for each interval', function(done) {
    //     const tableName = 'bills_airtel';
    //     const billGenDateIntervals = [1, 2, 3];

    //     const stub1 = sinon.stub(serviceObj, 'processBillsForEachBillGenDate').callsFake((tableName, arg1, cb) => {
    //         cb(); 
    //       });

    //     serviceObj.processBills(tableName, billGenDateIntervals, function(error) {
    //         // Check that processBillsForEachBillGenDate was called for each interval
    //         assert.equal(stub1.callCount, billGenDateIntervals.length);
    //         // Check that done() was called with no error
    //         assert.equal(error, null);
    //         stub1.restore();
    //         done();
    //     });
    // });

    // it('Process Bills | should call processBillsForEachBillGenDate for each date interval by mocking for each series behaviour', (done) => {
    //   const processBillsForEachBillGenDateStub = sinon.stub(serviceObj, 'processBillsForEachBillGenDate').callsFake((tableName, arg1, cb) => {
    //     cb();
    //   })
  
    //   const forEachOfSeriesStub = sinon.stub(ASYNC, 'forEachOfSeries').callsFake((array, iteratorFn, callback) => {
    //     for (let i = 0; i < array.length; i++) {
    //       iteratorFn(array[i], i, () => {
    //         // Do nothing here since we don't need to simulate any async behavior
    //       });
    //     }
    //     callback();
    //   });
   
    //   const tableName = 'yourTableName';
    //   const billGenDateIntervals = [1, 2];
  
    //   serviceObj.processBills(tableName, billGenDateIntervals, (err) => {
    //     processBillsForEachBillGenDateStub.restore();
    //     forEachOfSeriesStub.restore();
    //     if (err) {
    //       return done(err);
    //     }
    //     sinon.assert.callCount(processBillsForEachBillGenDateStub, billGenDateIntervals.length);
    //     done();
    //   });
    // });

    // it('processBillsForEachBillGenDate :: should call done when there are no bills to process', function(done) {
    //     const stub2 = sinon.stub(serviceObj, 'processBatch').callsFake((arr, cb) => {
    //         cb();
    //     })
    //     const tableName = 'bills_airtel';
    //     const billGenDateInterval = 1;
    //     serviceObj.offsetIdMap[tableName] = serviceObj.offsetIdMap[tableName] || {};
    //     serviceObj.offsetIdMap[tableName][billGenDateInterval] = 0;
    //     const x = 4;
    //     serviceObj.x = 4;
    //     const stub1 = sinon.stub(serviceObj.bills, "getBillsToNotifyBillGen").callsFake((cb, val1, val2, val3) =>{
    //         if(serviceObj.x > 0){
    //             serviceObj.x -= 1
    //             return cb(null, [{
    //                 "id" : 42,
    //                 "customer_id" : 2089665,
    //                 "recharge_number" : "9870653325",
    //                 "product_id" : 212,
    //                 "operator" : "airtel",
    //                 "amount" : -618.00,
    //                 "bill_date" : "2023-08-04 00:00:00",
    //                 "due_date" : "2023-08-19 00:00:00",
    //                 "bill_fetch_date" : "2023-08-01 09:42:00",
    //                 "next_bill_fetch_date" : "2023-09-19 01:00:00",
    //                 "gateway" : "airtellapu",
    //                 "paytype" : "postpaid",
    //                 "service" : "mobile",
    //                 "circle" : "mumbai",
    //                 "customer_mobile" : "9870653325",
    //                 "customer_email" : "<EMAIL>",
    //                 "payment_channel" : "ANDROIDAPP 8.4.0",
    //                 "retry_count" : 0,
    //                 "status" : 11,
    //                 "reason" : "",
    //                 "extra" : "",
    //                 "published_date" : "2021-06-19 11:50:00",
    //                 "created_at" : "2024-01-18 12:48:00",
    //                 "updated_at" : "2023-08-03 16:53:10",
    //                 "user_data" : "{}",
    //                 "notification_status" : 1,
    //                 "payment_date" : "2010-10-19 09:42:00",
    //                 "service_id" : 0,
    //                 "customerOtherInfo" : "",
    //                 "is_automatic" : 0
                
    //             }])
    //         }
    //         else    
    //             return cb(null,[])
            
    //     })
    //     serviceObj.processBillsForEachBillGenDate(tableName, billGenDateInterval, function() {
    //         // Check that done() was called
    //         expect(stub1).to.have.callCount(5);
    //         expect(stub2).to.have.callCount(4);
    //         stub1.restore();
    //         stub2.restore();
    //         done();
    //     });
    // });

    // it('processBillsForEachBillGenDate :: should call done when there are no bills to process grayscale', function(done) {
    //     const stub2 = sinon.stub(serviceObj, 'processBatch').callsFake((arr, cb) => {
    //         cb();
    //     })
    //     let cb = sinon.spy();
    //     const tableName = 'bills_airtel';
    //     const billGenDateInterval = 1;
    //     serviceObj.offsetIdMap[tableName] = serviceObj.offsetIdMap[tableName] || {};
    //     serviceObj.offsetIdMap[tableName][billGenDateInterval] = 0;
    //     serviceObj.greyScaleEnv = true
    //     serviceObj.x = 1
    //     const stub1 = sinon.stub(serviceObj.bills, "getBillsToNotifyBillGen").callsFake((cb, val1, val2, val3) =>{
    //         if(serviceObj.x > 0){
    //             serviceObj.x -= 1
    //             return cb(null, [{
    //                 "id" : 42,
    //                 "customer_id" : 2089665,
    //                 "recharge_number" : "9870653325",
    //                 "product_id" : 212,
    //                 "operator" : "airtel",
    //                 "amount" : -618.00,
    //                 "bill_date" : "2023-08-04 00:00:00",
    //                 "due_date" : "2023-08-19 00:00:00",
    //                 "bill_fetch_date" : "2023-08-01 09:42:00",
    //                 "next_bill_fetch_date" : "2023-09-19 01:00:00",
    //                 "gateway" : "airtellapu",
    //                 "paytype" : "postpaid",
    //                 "service" : "mobile",
    //                 "circle" : "mumbai",
    //                 "customer_mobile" : "9870653325",
    //                 "customer_email" : "<EMAIL>",
    //                 "payment_channel" : "ANDROIDAPP 8.4.0",
    //                 "retry_count" : 0,
    //                 "status" : 11,
    //                 "reason" : "",
    //                 "extra" : "",
    //                 "published_date" : "2021-06-19 11:50:00",
    //                 "created_at" : "2024-01-18 12:48:00",
    //                 "updated_at" : "2023-08-03 16:53:10",
    //                 "user_data" : "{}",
    //                 "notification_status" : 1,
    //                 "payment_date" : "2010-10-19 09:42:00",
    //                 "service_id" : 0,
    //                 "customerOtherInfo" : "",
    //                 "is_automatic" : 0
                
    //             }])
    //         }
    //         else    
    //             return cb(null,[])
    //     })
    //     serviceObj.processBillsForEachBillGenDate(tableName, billGenDateInterval, function() {
    //         // Check that done() was called
    //         expect(stub1).to.have.callCount(1);
    //         expect(stub2).to.have.callCount(1);
    //         stub1.restore();
    //         stub2.restore();
    //         done();
    //     });
    // });
    
    // it('processBatch :: call with customerId with 2 records', function(done){
    //     let records = [{"id":42,"customer_id":2089665,"recharge_number":"9870653325","product_id":212,"operator":"airtel","amount":-618,"bill_date":"2023-08-04 00:00:00","due_date":"2023-08-19 00:00:00","bill_fetch_date":"2023-08-01 09:42:00","next_bill_fetch_date":"2023-09-19 01:00:00","gateway":"airtellapu","paytype":"postpaid","service":"mobile","circle":"mumbai","customer_mobile":"9870653325","customer_email":"<EMAIL>","payment_channel":"ANDROIDAPP 8.4.0","retry_count":0,"status":11,"reason":"","extra":"","published_date":"2021-06-19 11:50:00","created_at":"2024-01-18 12:48:00","updated_at":"2023-08-03 16:53:10","user_data":"{}","notification_status":1,"payment_date":"2010-10-19 09:42:00","service_id":0,"customerOtherInfo":"","is_automatic":0},{"id":42,"customer_id":2089665,"recharge_number":"9870653325","product_id":212,"operator":"airtel","amount":-618,"bill_date":"2023-08-04 00:00:00","due_date":"2023-08-19 00:00:00","bill_fetch_date":"2023-08-01 09:42:00","next_bill_fetch_date":"2023-09-19 01:00:00","gateway":"airtellapu","paytype":"postpaid","service":"mobile","circle":"mumbai","customer_mobile":"9870653325","customer_email":"<EMAIL>","payment_channel":"ANDROIDAPP 8.4.0","retry_count":0,"status":11,"reason":"","extra":"","published_date":"2021-06-19 11:50:00","created_at":"2024-01-18 12:48:00","updated_at":"2023-08-03 16:53:10","user_data":"{}","notification_status":1,"payment_date":"2010-10-19 09:42:00","service_id":0,"customerOtherInfo":"","is_automatic":0}]
    //     const stub1 = sinon.stub(serviceObj, 'publishData').callsFake((records, cb) => {
    //         cb();
    //     })
    //     serviceObj.processBatch(records,function(){
    //         assert.strictEqual(stub1.callCount, 2, 'publishData should be called twice');

    //         // Check if publishData is called with the correct arguments
    //         assert.deepStrictEqual(stub1.firstCall.args[0],records[0]);
    //         assert.deepStrictEqual(stub1.secondCall.args[0],records[1]);

    //         stub1.restore();
    //         done();
    //     })
    // })

    // it('processBatch :: call with customerId with 0 records', function(done){
    //     let records = [];
    //     const stub1 = sinon.stub(serviceObj, 'publishData').callsFake((records, cb) => {
    //         cb();
    //     })
    //     serviceObj.processBatch(records,function(){
    //         assert.strictEqual(stub1.callCount, 0, 'publishData should be called twice');
    //         stub1.restore();
    //         done();
    //     })
    // })

    // it('publishData :: should call done() if record validation fails', function (done1) {
    //     let record = {'cid': 1234};
    //     // Mock the dependencies
    //     const self = {
    //         validateRecord: sinon.stub().returns(false),
    //         prepareDataToPublish: sinon.stub(),
    //         greyScaleEnv: false,
    //         kafkaPublisher: {
    //         publishData: sinon.stub()
    //         },
    //         config: {
    //         KAFKA: {
    //             SERVICES: {
    //             REMINDER_BILLFETCH_PIPELINE: {
    //                 REMINDER_BILL_FETCH: null
    //             }
    //             }
    //         }
    //         },
    //         L: {
    //         critical: sinon.stub(),
    //         log: sinon.stub()
    //         }
    //     };
    //     const done = sinon.stub();
    
    //     // Call the function
    //     serviceObj.publishData.call(self, {}, done);
    
    //     // Assertions
    //     assert.isTrue(self.validateRecord.calledOnce);
    //     assert.isTrue(done.calledOnce);
    //     assert.isTrue(self.kafkaPublisher.publishData.notCalled);

    //     done1();
    // });

    // it('publishData :: should publish data to Kafka with correct parameters if validation passes', function (done1) {
    //     // Mock the dependencies
    //     const record = {"id":42,"customer_id":2089665,"recharge_number":"9870653325","product_id":212,"operator":"airtel","amount":-618,"bill_date":"2023-08-04 00:00:00","due_date":"2023-08-19 00:00:00","bill_fetch_date":"2023-08-01 09:42:00","next_bill_fetch_date":"2023-09-19 01:00:00","gateway":"airtellapu","paytype":"postpaid","service":"mobile","circle":"mumbai","customer_mobile":"9870653325","customer_email":"<EMAIL>","payment_channel":"ANDROIDAPP 8.4.0","retry_count":0,"status":11,"reason":"","extra":"","published_date":"2021-06-19 11:50:00","created_at":"2024-01-18 12:48:00","updated_at":"2023-08-03 16:53:10","user_data":"{}","notification_status":1,"payment_date":"2010-10-19 09:42:00","service_id":0,"customerOtherInfo":"","is_automatic":0}; // Replace with your test record
    //     const payload = {
    //         "source": "reminderBillGenPublisher",
    //         "notificationType": "BILLGEN",
    //         "machineId": "mymachineId",
    //         "data": record
    //         };
    //     const self = {
    //       validateRecord: sinon.stub().returns(true),
    //       prepareDataToPublish: sinon.stub().returns(payload),
    //       greyScaleEnv: false,
    //       kafkaPublisher: {
    //         publishData: sinon.stub().callsArg(1) // Simulate successful publishing
    //       },
    //       config: {
    //         KAFKA: {
    //           SERVICES: {
    //             REMINDER_BILLFETCH_PIPELINE: {
    //               REMINDER_BILL_FETCH: 'REMINDER_BILL_FETCH' // Replace with your topic
    //             }
    //           }
    //         }
    //       },
    //       L: {
    //         critical: sinon.stub(),
    //         log: sinon.stub()
    //       }
    //     };
    //     const done = sinon.stub();
    
    //     // Call the function
    //     serviceObj.publishData.call(self, record, done);
    
    //     // Assertions
    //     assert.isTrue(self.validateRecord.calledOnce);
    //     assert.isTrue(self.prepareDataToPublish.calledOnceWith(record));
    //     assert.isTrue(self.kafkaPublisher.publishData.calledOnce);
    //     assert.deepEqual(self.kafkaPublisher.publishData.getCall(0).args[0], [{
    //       topic: 'REMINDER_BILL_FETCH',
    //       messages: JSON.stringify(payload)
    //     }]);
    //     assert.isTrue(done.calledOnce);
    //     assert.isTrue(self.L.log.calledOnce);
    //     assert.isTrue(self.L.critical.notCalled);

    //     done1();
    // });

    // it('publishData :: should set skipNotification to 1 when greyScaleEnv is true', function () {
    //     // Mock the dependencies
    //     const record = {"id":42,"customer_id":2089665,"recharge_number":"9870653325","product_id":212,"operator":"airtel","amount":-618,"bill_date":"2023-08-04 00:00:00","due_date":"2023-08-19 00:00:00","bill_fetch_date":"2023-08-01 09:42:00","next_bill_fetch_date":"2023-09-19 01:00:00","gateway":"airtellapu","paytype":"postpaid","service":"mobile","circle":"mumbai","customer_mobile":"9870653325","customer_email":"<EMAIL>","payment_channel":"ANDROIDAPP 8.4.0","retry_count":0,"status":11,"reason":"","extra":"","published_date":"2021-06-19 11:50:00","created_at":"2024-01-18 12:48:00","updated_at":"2023-08-03 16:53:10","user_data":"{}","notification_status":1,"payment_date":"2010-10-19 09:42:00","service_id":0,"customerOtherInfo":"","is_automatic":0}; // Replace with your test record
    //     const payload = {
    //         "source": "reminderBillGenPublisher",
    //         "notificationType": "BILLGEN",
    //         "machineId": "mymachineId",
    //         "data": record
    //     };
    //     const self = {
    //       validateRecord: sinon.stub().returns(true),
    //       prepareDataToPublish: sinon.stub().returns(payload),
    //       greyScaleEnv: true, // Set greyScaleEnv to true
    //       kafkaPublisher: {
    //         publishData: sinon.stub().callsArg(1) // Simulate successful publishing
    //       },
    //       config: {
    //         KAFKA: {
    //           SERVICES: {
    //             REMINDER_BILLFETCH_PIPELINE: {
    //               REMINDER_BILL_FETCH: 'REMINDER_BILL_FETCH' // Replace with your topic
    //             }
    //           }
    //         }
    //       },
    //       L: {
    //         critical: sinon.stub(),
    //         log: sinon.stub()
    //       }
    //     };
    //     const done = sinon.stub();
    
    //     // Call the function
    //     serviceObj.publishData.call(self, record, done);
    
    //     // Assertions
    //     assert.isTrue(self.validateRecord.calledOnce);
    //     assert.isTrue(self.prepareDataToPublish.calledOnceWith(record));
    //     assert.isTrue(self.kafkaPublisher.publishData.calledOnce);
    //     assert.deepEqual(self.kafkaPublisher.publishData.getCall(0).args[0], [{
    //       topic: 'REMINDER_BILL_FETCH',
    //       messages: JSON.stringify(payload)
    //     }]);
    //     assert.isTrue(payload.data.skipNotification === 1);
    //     assert.isTrue(done.calledOnce);
    //     assert.isTrue(self.L.log.calledOnce);
    //     assert.isTrue(self.L.critical.notCalled);
    // });

    // it('validateRecord :: should return false if the operator is not in the operator list', function () {
    //     const self = {
    //       operatorList: {},
    //       config: {
    //         COMMON: {
    //           MIN_NOTIFY_AMOUNT: 0,
    //           notification_status: {
    //             DISABLED: 0
    //           }
    //         }
    //       }
    //     };
    //     const record = {
    //       operator: 'jio',
    //       amount: 10,
    //       notification_status: 1
    //     };
    
    //     // Call the function
    //     const result = serviceObj.validateRecord.call(self, record);
    
    //     // Assertions
    //     assert.isFalse(result);
    // });
    
    // it('validateRecord :: should return false if the amount is less than or equal to the minimum notify amount', function () {
    //     const self = {
    //       operatorList: {
    //         jio : true
    //       },
    //       config: {
    //         COMMON: {
    //           MIN_NOTIFY_AMOUNT: 0,
    //           notification_status: {
    //             DISABLED: 0
    //           }
    //         }
    //       }
    //     };
    //     const record = {
    //       operator: 'jio',
    //       amount: 5,
    //       notification_status: 1
    //     };
    
    //     // Call the function
    //     const result = serviceObj.validateRecord.call(self, record);
    
    //     // Assertions
    //     assert.isFalse(result);
    // });
    
    // it('validateRecord :: should return false if the notification status is disabled', function () {
    //     const self = {
    //       operatorList: {
    //         jio : true
    //       },
    //       config: {
    //         COMMON: {
    //           MIN_NOTIFY_AMOUNT: 0,
    //           notification_status: {
    //             DISABLED: 0
    //           }
    //         }
    //       }
    //     };
    //     const record = {
    //       operator: 'jio',
    //       amount: 10,
    //       notification_status: 0
    //     };
    
    //     // Call the function
    //     const result = serviceObj.validateRecord.call(self, record);
    
    //     // Assertions
    //     assert.isFalse(result);
    // });
    
    //   it('validateRecord :: should return false if the bill_fetch_date is not valid or in the future', function () {
    //     const self = {
    //       operatorList: {
    //         jio : true
    //       },
    //       config: {
    //         COMMON: {
    //           MIN_NOTIFY_AMOUNT: 0,
    //           notification_status: {
    //             DISABLED: 0
    //           }
    //         }
    //       }
    //     };
    //     const record1 = {
    //       operator: 'jio',
    //       amount: 10,
    //       notification_status: 1,
    //       bill_fetch_date: '2035-01-01' // future
    //     };
    //     const record2 = {
    //       operator: 'airtel',
    //       amount: 10,
    //       notification_status: 1,
    //       bill_fetch_date: 'invalid-date' // Invalid date
    //     };
    
    //     // Call the function
    //     const result1 = serviceObj.validateRecord.call(self, record1);
    //     const result2 = serviceObj.validateRecord.call(self, record2);
    
    //     // Assertions
    //     assert.isFalse(result1);
    //     assert.isFalse(result2);
    // });
    
    // it('validateRecord :: should return true if all conditions are met', function () {
    //     const self = {
    //       operatorList: {
    //         jio : true
    //       },
    //       config: {
    //         COMMON: {
    //           MIN_NOTIFY_AMOUNT: 0,
    //           notification_status: {
    //             DISABLED: 0
    //           }
    //         }
    //       }
    //     };
    //     const record = {
    //       operator: 'jio',
    //       amount: 10,
    //       notification_status: 1,
    //       bill_fetch_date: '2023-08-01'
    //     };
    
    //     // Call the function
    //     const result = serviceObj.validateRecord.call(self, record);
    
    //     // Assertions
    //     assert.isTrue(result);
    // });

});

