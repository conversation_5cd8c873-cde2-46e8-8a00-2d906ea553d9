/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import nock from 'nock'

import BILLS from '../../models/bills'
import STARTUP_MOCK from '../__mocks__/startUp'


// src/unitTest/lib/recentBills.tests.js
// src/services/billSubscriber.js

chai.use(chaiAsPromised);
chai.use(sinonChai);
import {createHash} from 'crypto'

const { expect } = chai;

let server;

describe("createBill", () => {
    let serviceObj;
    before((done) => {
        STARTUP_MOCK.init((error, options) => {
            serviceObj = new BILLS(options)
            done();
        })
    });
    it('createBill success', (done) => {
        let tableName = 'bills_creditcard';
        let params = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "recharge_number": "1234567890",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success"
        };

        let stub1 = sinon.stub(serviceObj, 'formatParamsAndexecuteCreateBillQuery').callsFake((cb, tableName, params) => {
            cb(null, params);
        });

        let fromRecents = true;
        serviceObj.createBill((err, data) => {
            expect(data).to.deep.equal(params);
            expect(stub1).to.have.been.calledOnce;
            stub1.restore();
            done();
        }, tableName, params, fromRecents);
    });

    it('createBill error', (done) => {
        let tableName = 'bills_creditcard';
        let params = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "recharge_number": "1234567890",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success"
        };

        let stub1 = sinon.stub(serviceObj, 'formatParamsAndexecuteCreateBillQuery').callsFake((cb, tableName, params) => {
            cb('error in formatParamsAndexecuteCreateBillQuery');
        });

        let fromRecents = true;
        serviceObj.createBill((err, data) => {
            expect(err).to.equal('error in formatParamsAndexecuteCreateBillQuery');
            expect(stub1).to.have.been.calledOnce;
            stub1.restore();
            done();
        }, tableName, params, fromRecents);
    });

    it('formatParamsAndexecuteCreateBillQuery isWhitelistedForCC false', (done) => {
        let tableName = 'bills_creditcard';
        let params = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "recharge_number": "1234567890",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success"
        };

        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper,'isWhitelistedForCC').returns(false);
        let stub2 = sinon.stub(serviceObj, 'executeOlderCreateBillQuery').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        let stub3 = sinon.stub(serviceObj, 'formatCCBillParamsAndExecute').callsFake((cb, tableName, params) => {
            cb(null, params);
        });

        let fromRecents = true;
        serviceObj.formatParamsAndexecuteCreateBillQuery((err, data) => {
            expect(data).to.deep.equal(params);
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params, fromRecents);
    });

    it('formatParamsAndexecuteCreateBillQuery isWhitelistedForCC true', (done) => {
        let tableName = 'bills_creditcard';
        let params = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "recharge_number": "1234567890",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success"
        };

        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper,'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj, 'executeOlderCreateBillQuery').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        let stub3 = sinon.stub(serviceObj, 'formatCCBillParamsAndExecute').callsFake((cb, tableName, params) => {
            cb(null, params);
        });

        let fromRecents = true;
        serviceObj.formatParamsAndexecuteCreateBillQuery((err, data) => {
            expect(data).to.deep.equal(params);
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params, fromRecents);
    });

    it('formatCCBillParamsAndExecute success', (done) => {
        let tableName = 'bills_creditcard';
        let params = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "rechargeNumber": "XXXX XXXX XXXX 1234",
            "referenceId": "123",
            "customerId": "12345567",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success",
            "transactionHistory" : [
                {
                    "id": "123",
                    "amount": 100,
                    "operator": "emidue",
                    "recharge_number": "XXXX XXXX XXXX 1234",
                    "reference_id": "12345",
                    "customer_id": "12345567",
                    "recharge_date": "2024-01-01",
                    "due_date": "2024-01-01",
                    "status": "success"
                },
                {
                    "id": "123",
                    "amount": 100,
                    "operator": "emidue",
                    "recharge_number": "XXXX XXXX XXXX 1234",
                    "reference_id": "123",
                    "customer_id": "12345567",
                    "recharge_date": "2024-01-01",
                    "due_date": "2024-01-01",
                    "status": "success",
                    "is_encrypted": 0
                }
            ]
        };

        let stub1 = sinon.stub(serviceObj, 'copyAdditionalParamsFromHistoricalRecord').returns(params);
        let stub2 = sinon.stub(serviceObj, 'additionalHandlingForRecentBills').returns(params);
        let stub3 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub4 = sinon.stub(serviceObj, 'executeCreateBillQuery').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        let stub5 = sinon.stub(serviceObj, 'deleteDuplicateCards').callsFake((cb, tableName, params) => {
            cb(null, params);
        });

        let fromRecents = true;
        serviceObj.formatCCBillParamsAndExecute((err, data) => {
            expect(data).to.deep.equal({ id: '123' });
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            expect(stub4).to.have.been.calledOnce;
            expect(stub5).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            done();
        }, tableName, params);
    });
    it('formatCCBillParamsAndExecute success with no transHistory', (done) => {
        let tableName = 'bills_creditcard';
        let params = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "rechargeNumber": "XXXX XXXX XXXX 1234",
            "referenceId": "123",
            "customerId": "12345567",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success",
            "transactionHistory" : [
                {
                    "id": "123",
                    "amount": 100,
                    "operator": "emidue",
                    "recharge_number": "XXXX XXXX XXXX 1234",
                    "reference_id": "12345",
                    "customer_id": "12345567",
                    "recharge_date": "2024-01-01",
                    "due_date": "2024-01-01",
                    "status": "success"
                },
                {
                    "id": "123",
                    "amount": 100,
                    "operator": "emidue",
                    "recharge_number": "XXXX XXXX XXXX 1235",
                    "reference_id": "123",
                    "customer_id": "12345567",
                    "recharge_date": "2024-01-01",
                    "due_date": "2024-01-01",
                    "status": "success",
                    "is_encrypted": 0
                }
            ]
        };

        let stub1 = sinon.stub(serviceObj, 'copyAdditionalParamsFromHistoricalRecord').returns(params);
        let stub2 = sinon.stub(serviceObj, 'additionalHandlingForRecentBills').returns(params);
        let stub3 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub4 = sinon.stub(serviceObj, 'executeCreateBillQuery').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        let stub5 = sinon.stub(serviceObj, 'deleteDuplicateCards').callsFake((cb, tableName, params) => {
            cb(null, params);
        });

        let fromRecents = true;
        serviceObj.formatCCBillParamsAndExecute((err, data) => {
            expect(data).to.deep.equal(null);
            expect(stub1).to.not.have.been.called;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            expect(stub4).to.have.been.calledOnce;
            expect(stub5).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            stub5.restore();
            done();
        }, tableName, params);
    });
    it("updateBillsByCCPublisherWithEncDecHandling whitelisting done is_encyrpted true", (done)=>{
        let tableName = "bills_creditcard";
        let currentRecord = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "rechargeNumber": "XXXX XXXX XXXX 1234",
            "referenceId": "123",
            "customerId": "12345567",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success",
            "is_encrypted" : 1
        }
        let params = {
            "amount": 100,
            "status": 4
        }
        let string = "abc"
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj, 'updateBillByPublisher').callsFake((cb, tableName, params, currentRecord) => {
            cb(null, params);
        })
        let stub3 = sinon.stub(serviceObj, 'createAndDeleteCCBillsByPublisher').callsFake((cb, tableName, params, currentRecord) => {
            cb(null, params);
        });
        let stub4 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'encryptData').returns(string);
        serviceObj.updateBillsByCCPublisherWithEncDecHandling((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName,currentRecord, params)
    })
    it("updateBillsByCCPublisherWithEncDecHandling whitelisting done is_encyrpted false", (done)=>{
        let tableName = "bills_creditcard";
        let currentRecord = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "rechargeNumber": "XXXX XXXX XXXX 1234",
            "referenceId": "123",
            "customerId": "12345567",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success",
            "is_encrypted" : 0
        }
        let params = {
            "amount": 100,
            "status": 4
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj, 'updateBillByPublisher').callsFake((cb, tableName, params, currentRecord) => {
            cb(null, params);
        })
        let stub3 = sinon.stub(serviceObj, 'createAndDeleteCCBillsByPublisher').callsFake((cb, tableName, params, currentRecord) => {
            cb(null, params);
        });
        let stub4 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'encryptData').returns("abc");
        serviceObj.updateBillsByCCPublisherWithEncDecHandling((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName,currentRecord, params)
    })
    it("updateBillsByCCPublisherWithEncDecHandling whitelisting not done", (done)=>{
        let tableName = "bills_creditcard";
        let currentRecord = {
            "id": "123",
            "amount": 100,
            "operator": "emidue",
            "rechargeNumber": "XXXX XXXX XXXX 1234",
            "referenceId": "123",
            "customerId": "12345567",
            "recharge_date": "2024-01-01",
            "due_date": "2024-01-01",
            "status": "success",
            "is_encrypted" : 0
        }
        let params = {
            "amount": 100,
            "status": 4
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(false);
        let stub2 = sinon.stub(serviceObj, 'updateBillByPublisher').callsFake((cb, tableName, params, currentRecord) => {
            cb(null, params);
        })
        let stub3 = sinon.stub(serviceObj, 'createAndDeleteCCBillsByPublisher').callsFake((cb, tableName, params, currentRecord) => {
            cb(null, params);
        });
        serviceObj.updateBillsByCCPublisherWithEncDecHandling((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName,currentRecord, params)
    })
    it("createAndDeleteCCBillsByPublisher", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub2 = sinon.stub(serviceObj, 'createCCBillByPublisher').callsFake((cb, tableName, params) => {
            cb(null, 12345);
        })
        let stub3 = sinon.stub(serviceObj, 'deleteDuplicateCards').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        serviceObj.createAndDeleteCCBillsByPublisher((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("createAndDeleteCCBillsByPublisher error" , (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub2 = sinon.stub(serviceObj, 'createCCBillByPublisher').callsFake((cb, tableName, params) => {
            cb('error in createCCBillByPublisher');
        })
        let stub3 = sinon.stub(serviceObj, 'deleteDuplicateCards').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        serviceObj.createAndDeleteCCBillsByPublisher((err, data)=>{
            expect(err).to.equal('error in createCCBillByPublisher');
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("updateBillForSameRNandCID: success whitelisting done", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'encryptData').returns("abc");
        let stub3 = sinon.stub(serviceObj, 'updateBillForSameRNandCIDExisting').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        let stub4 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'encryptJson').returns({});
        serviceObj.updateBillForSameRNandCID((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
    it("updateBillForSameRNandCID: success whitelisting not  done", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(false);
        let stub3 = sinon.stub(serviceObj, 'updateBillForSameRNandCIDExisting').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        serviceObj.updateBillForSameRNandCID((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("updateCCBillByCustomerId whitelisting done and is_encrypted true", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234,
            "is_encrypted": 1
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'updateExistingCCBillByCustomerId').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        let stub4 = sinon.stub(serviceObj, 'createAndDeleteCCBill').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        serviceObj.updateCCBillByCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            expect(stub4).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
    it("updateCCBillByCustomerId whitelisting done and is_encrypted false", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234,
            "is_encrypted": 0
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'updateExistingCCBillByCustomerId').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        let stub4 = sinon.stub(serviceObj, 'createAndDeleteCCBill').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        serviceObj.updateCCBillByCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.not.have.been.called;
            expect(stub4).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
    it("updateCCBillByCustomerId whitelisting not done", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234,
            "is_encrypted": 0
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(false);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'updateExistingCCBillByCustomerId').callsFake((cb, tableName, params) => {
            cb(null, params);
        })
        let stub4 = sinon.stub(serviceObj, 'createAndDeleteCCBill').callsFake((cb, tableName, params) => {
            cb(null, params);
        });
        serviceObj.updateCCBillByCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.have.been.calledOnce;
            expect(stub4).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
    it("createAndDeleteCCBill success", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub2 = sinon.stub(serviceObj, 'createCCBillForCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb(null);
        })
        let stub3 = sinon.stub(serviceObj, 'deleteDuplicateCards').callsFake((cb, tableName, params) => {
            cb(null);
        });
        serviceObj.createAndDeleteCCBill((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("createAndDeleteCCBill fail", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub2 = sinon.stub(serviceObj, 'createCCBillForCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb('errror in createCCBillForCustomerIdWithEncDecHandling');
        })
        let stub3 = sinon.stub(serviceObj, 'deleteDuplicateCards').callsFake((cb, tableName, params) => {
            cb(null);
        });
        serviceObj.createAndDeleteCCBill((err, data)=>{
            expect(err).to.equal('errror in createCCBillForCustomerIdWithEncDecHandling');
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("createCCBillForCustomerId whitelisting done", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'createCCBillForCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb(null);
        })
        serviceObj.createCCBillForCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("createCCBillForCustomerId whitelisting not done", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(false);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'createCCBillForCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb(null);
        })
        serviceObj.createCCBillForCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            done();
        }, tableName, params)
    })
    it("updateCCBillPaidByCustomerId whitelisting done and is_encrypted true", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234,
            "is_encrypted": 1
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'updateCCBillPaidByCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb(null);
        })
        let stub4 = sinon.stub(serviceObj, 'createAndDeleteCCBill').callsFake((cb, tableName, params) => {
            cb(null);
        })
        serviceObj.updateCCBillPaidByCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.have.been.calledOnce;
            expect(stub3).to.have.been.calledOnce;
            expect(stub4).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
    it("updateCCBillPaidByCustomerId whitelisting done and is_encrypted false", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234,
            "is_encrypted": 0
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(true);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'updateCCBillPaidByCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb(null);
        })
        let stub4 = sinon.stub(serviceObj, 'createAndDeleteCCBill').callsFake((cb, tableName, params) => {
            cb(null);
        })
        serviceObj.updateCCBillPaidByCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.not.have.been.called;
            expect(stub4).to.have.been.calledOnce;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
    it("updateCCBillPaidByCustomerId whitelisting not done", (done)=>{
        let tableName = "bills_creditcard";
        let params = {
            "amount": 100,
            "status": 4,
            "id": 1234,
            "is_encrypted": 0
        }
        let stub1 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'isWhitelistedForCC').returns(false);
        let stub2 = sinon.stub(serviceObj.EncryptionDecryptioinHelper, 'getEncryptedParamsFromGenericParams').returns(params);
        let stub3 = sinon.stub(serviceObj, 'updateCCBillPaidByCustomerIdWithEncDecHandling').callsFake((cb, tableName, params) => {
            cb(null);
        })
        let stub4 = sinon.stub(serviceObj, 'createAndDeleteCCBill').callsFake((cb, tableName, params) => {
            cb(null);
        })
        serviceObj.updateCCBillPaidByCustomerId((err, data)=>{
            expect(stub1).to.have.been.calledOnce;
            expect(stub2).to.not.have.been.called;
            expect(stub3).to.have.been.calledOnce;
            expect(stub4).to.not.have.been.called;
            stub1.restore();
            stub2.restore();
            stub3.restore();
            stub4.restore();
            done();
        }, tableName, params)
    })
})

/**
 * case let say we on-board new operator in emidue consumer
 * Will it be on-boarded in recent service flow?
 */