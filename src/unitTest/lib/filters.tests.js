/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    import nock from 'nock'
    
    import filters         from '../../lib/filters'
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    let server;
    
    describe("Library: filters library test suite", function () {
    
    
        beforeEach(function () {
            server = sinon.fakeServer.create();
        });
    
        afterEach(function () {
            server.restore();
        });
        
        it("getFilteredAmount | Valid ammount | input ammount : '15' ", (done)=>{
            let inputAmount = 15;
            let parseAmountobj=filters.getFilteredAmount(inputAmount);

            expect(parseAmountobj).to.equal(inputAmount);

            return done();
        })
        it("getFilteredAmount | Valid ammount | input ammount : '166.21' ", (done)=>{
            let inputAmount = 166.21;
            let parseAmountobj=filters.getFilteredAmount(inputAmount);

            expect(parseAmountobj).to.equal(inputAmount);

            return done();
        })

        it("getFilteredAmount | invalid ammount | input ammount : 'Age 21' ", (done)=>{
            let inputAmount = "Age 21";
            let parseAmountobj=filters.getFilteredAmount(inputAmount);

            expect(parseAmountobj).to.equal(0);

            return done();
        })
        it("getFilteredAmount | invalid ammount | input ammount : 'ABC' ", (done)=>{
            let inputAmount = "ABC";
            let parseAmountobj=filters.getFilteredAmount(inputAmount);

            expect(parseAmountobj).to.equal(0);

            return done();
        })

        it("Date parsing | getFilteredDate | valid date | input date format 'DD-MM-YYYY' | input date : '21-12-2021'", (done) => {
            let inputDate = "21-12-2021";
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(true);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });


        it("Date parsing | getFilteredDate | valid date | input date format 'MMM D, YYYY' | input date : 'Jan 4, 2021'", (done) => {
            let inputDate = "Jan 4, 2021"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(true);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'MMM D, YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });



        it("Date parsing | getFilteredDate | valid date | input date format 'MMM D, YYYY' | input date : 'Dec 31, 2021'", (done) => {
            let inputDate = "Dec 31, 2021"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(true);
            
            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'MMM D, YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });


        it("Date parsing | getFilteredDate | invalid date | input date format 'MMM D, YYYY' | input date : 'Jan 32, 2021'", (done) => {
            let inputDate = "Jan 32, 2021"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(false);
            
            return done();
        });



        it("Date parsing | getFilteredDate | Valid date | input date format 'MMM D, YYYY' | input date : 'Jan 02, 2021'", (done) => {
            let inputDate = "Jan 02, 2021"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(true);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'MMM D, YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });


        it("Date parsing | 29-09-1905 ", (done) => {
            let inputDate = "29-09-1905"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(false);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });


        it("Date parsing | 29-09-2005 ", (done) => {
            let inputDate = "29-09-2005"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(true);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });

        it("Date parsing | 29-09-2025 ", (done) => {
            let inputDate = "29-09-2025"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(true);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });


        it("Date parsing | 35-01-2022 ", (done) => {
            let inputDate = "35-01-2022"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(false);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });



        it("Date parsing | 23-13-2022 ", (done) => {
            let inputDate = "23-13-2022"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(false);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });

        it("Date parsing | 30-02-2021 ", (done) => {
            let inputDate = "30-02-2021"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(false);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });

        it("Date parsing | 31-09-2021 ", (done) => {
            let inputDate = "31-09-2021"; 
            let parsedDateObj = filters.getFilteredDate(inputDate);
            expect(parsedDateObj.isDateFmtValid).to.be.equal(false);

            if (parsedDateObj.isDateFmtValid) {
                expect(parsedDateObj.value.diff( MOMENT(inputDate,'DD-MM-YYYY') , 'seconds')).to.be.equal(0);
            }

            return done();
        });


    });