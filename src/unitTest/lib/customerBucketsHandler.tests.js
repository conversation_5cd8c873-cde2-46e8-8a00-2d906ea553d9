

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai, { assert, expect } from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import CUSTOMER_BUCKETS_HANDLER from '../../lib/customerBucketsHandler';
import STARTUP_MOCK from '../__mocks__/startUp'
import fs from 'fs'
import { Readable } from 'stream';
import { deepEqual } from 'assert';


describe("Library: customerBucketsHandler library test suite", function(){
    let serviceInstance;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            serviceInstance = new CUSTOMER_BUCKETS_HANDLER(options)
            done()
        });
    });

    it("getServicePaytypeFromRecords : should return service_paytype ",()=>{
        let records = [{service:"electricity",paytype:"postpaid",product_id:"123"}]
        let parent = {operator:"test"}
        let servicePaytype = serviceInstance.getServicePaytypeFromRecords(records,parent)
        assert(servicePaytype == "electricity_postpaid")
    })

    it("getPriorityBucketsList : should return priorityBucketsList ",()=>{
        let records = [{service:"electricity",paytype:"postpaid",product_id:"123"}]
        let parent = {operator:"test"}
        serviceInstance.getPriorityBucketsList(records,parent)
        assert(parent.priorityBucketsList.length == 5)
    })

    it("canPrioritiesBeSet : should return true ",()=>{
        let records = [{service:"electricity",paytype:"postpaid",product_id:"123"}]
        let parent = {operator:"airtel"}
        let canPrioritiesBeSet = serviceInstance.canPrioritiesBeSet(records,parent)
        assert(canPrioritiesBeSet == true)
    })

    it("canPrioritiesBeSet : should return false ",()=>{
        let records = [{service:"electricity",paytype:"postpaid",product_id:"123"}]
        let parent = {operator:"vodafone"}
        let canPrioritiesBeSet = serviceInstance.canPrioritiesBeSet(records,parent)
        assert(canPrioritiesBeSet == false)
    })

    it("setPriorityBucketsByServicePaytype : should set priorityBuckets ",()=>{
        let records = [{service:"electricity",paytype:"postpaid",product_id:"123"}]
        let parent = {operator:"airtel"}
        serviceInstance.setPriorityBucketsByServicePaytype(records,parent)
        assert(parent.priorityBuckets[0] == "platinum")
        assert(parent.priorityBuckets[1] == "gold")
        assert(parent.priorityBuckets[2] == "silver")
        assert(parent.priorityBuckets[3] == "bronze")
        assert(parent.priorityBuckets[4] == "noBucket")
    })

    it("startPriorityResetTimer : should start timer ",()=>{
        let parent = {"operator":"airtel", "priorityResetTime":10}
        serviceInstance.startPriorityResetTimer(parent)
        assert(parent.priorityResetTimerCompleted != null)
        assert(parent.timer !=null)
    })

    it("restartPriorityResetTimer : should restart timer ",()=>{
        let parent = {"operator":"airtel", "priorityResetTime":10}
        serviceInstance.restartPriorityResetTimer(parent)
        assert(parent.priorityResetTimerCompleted != null)
        assert(parent.timer !=null)
    })

    it("getCurrentPriorityBucket : should return current priority bucket ",()=>{
        let parent = {priorityBuckets: {
            0:"platinum",
            1:"gold",
            2:"silver",
            3:"bronze",
            4:"noBucket"}, 
            operator:"airtel",
            priorityResetTime:10}
        let currentPriorityBucket = serviceInstance.getCurrentPriorityBucket(parent)
        assert(currentPriorityBucket == "platinum")
    })

    it("resetPriorityBucket : should reset priority bucket ",()=>{
        let parent = {priorityBuckets: {
            0:"platinum",
            1:"gold",
            2:"silver",
            3:"bronze",
            4:"noBucket"}, 
            operator:"airtel",
            priorityResetTime:10}
        serviceInstance.resetPriorityBucket(parent)
        assert(parent.currentPriorityBucket == 0)
    })

    it("shiftPriorityBucket : should shift priority bucket ",()=>{
        let parent = {priorityBuckets: {
            0:"platinum",
            1:"gold",
            2:"silver",
            3:"bronze",
            4:"noBucket"}, 
            operator:"airtel",
            currentPriorityBucket:2,
            priorityBucketsList:["platinum","gold","silver","bronze","noBucket"],
            priorityResetTime:10
        }
        serviceInstance.shiftPriorityBucket(parent)
        assert(parent.currentPriorityBucket == 3)
    })

    it("getLastPriorityBucket : should return last priority bucket ",()=>{
        let parent = {priorityBuckets: {
            0:"platinum",
            1:"gold",
            2:"silver",
            3:"bronze",
            4:"noBucket"}, 
            operator:"airtel",
            priorityResetTime:10,
            priorityBucketsList:["platinum","gold","silver","bronze","noBucket"]}
        let lastPriorityBucket = serviceInstance.getLastPriorityBucket(parent)
        assert(lastPriorityBucket == "noBucket")
    })
})