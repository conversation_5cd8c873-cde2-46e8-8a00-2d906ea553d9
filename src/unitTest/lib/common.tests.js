/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    // import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'
    import nock from 'nock'
    
    import utility from '../../lib'
    import STARTUP_MOCK from '../__mocks__/startUp'
    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);
    
    const { expect } = chai;
    
    describe("Library: Common library test suite", function () {
        let libraryObj;
    
        before(function (done) {
            STARTUP_MOCK.init(function(error, options){
                libraryObj = new utility.commonLib(options);
                done();
            });
        });
        
        it("Common::getRetailerData | Invalid customerId", (done) => {
            let customerId = null
            libraryObj.getRetailerData((error, status) => {
                expect(error).to.be.equal('common::getRetailerData, invalid customerId');
                expect(status).to.be.equal(undefined)
            }, customerId);
            return done();
        });
        
        it("Common::getCvrData status | Invalid productId", (done) => {
            let productId = null
            let stub = sinon.stub(libraryObj.catalogVerticalRecharge, 'getSpecificDataFromCvr').yields('fakeError')
            libraryObj.getCvrData((error, thumbnail) => {
                stub.restore();
                expect(error).to.be.equal('common::getCvrData, thumbnail not found for null');
                expect(thumbnail).to.be.equal(undefined)
            }, productId);
            
            return done();
        });

        it("Common::getCvrData status | Valid productId", (done) => {
            let productId = '1234'
            libraryObj.getCvrData((error) => {
                expect(error).to.be.null;
            }, productId);
            
            return done();
        });

        it("mapBillsTableColumns | DB keys mapping with kafka payload", () => {
            let dbPayload = {
                "customer_id": "1147438071",
                "recharge_number": "055602210545639",
                "product_id": "105826600",
                "operator": "Hero FinCorp",
                "amount": "15561",
                "due_date": null,
                "bill_date": "2021-08-12 13:31:50",
                "bill_fetch_date": "2021-08-12 13:31:50",
                "service_id": 0,
                "paytype": null,
                "service": null,
                "circle": null,
                "gateway": null,
                "customer_mobile": "9822468899",
                "customer_email": null,
                "next_bill_fetch_date": "2021-08-27 13:31:50",
                "paymentChannel": null,
                "retry_count": 0,
                "status": 4,
                "reason": "",
                "extra": "",
                "published_date": "2021-08-12 13:31:50",
                "user_data": "{\"recharge_number_2\":null}",
                "notification_status": 1,
                "payment_channel" : null,
                "payment_date": null,
                "is_automatic": 0,
                "updateAllCustIdRecords": false,
                "time_interval": null
            };
            
            let kafkaPayload = libraryObj.mapBillsTableColumns(dbPayload);
    
            expect(kafkaPayload.customerId).to.be.equal(dbPayload.customer_id);
            expect(kafkaPayload.rechargeNumber).to.be.equal(dbPayload.recharge_number);
            expect(kafkaPayload.productId).to.be.equal(dbPayload.product_id);
            expect(kafkaPayload.billDate).to.be.equal(dbPayload.bill_date);
            expect(kafkaPayload.dueDate).to.be.equal(dbPayload.due_date);
            expect(kafkaPayload.billFetchDate).to.be.equal(dbPayload.bill_fetch_date);
            expect(kafkaPayload.nextBillFetchDate).to.be.equal(dbPayload.next_bill_fetch_date);
            expect(kafkaPayload.customerMobile).to.be.equal(dbPayload.customer_mobile);
            expect(kafkaPayload.customerEmail).to.be.equal(dbPayload.customer_email);
            expect(kafkaPayload.paymentChannel).to.be.equal(dbPayload.payment_channel);
            expect(kafkaPayload.retryCount).to.be.equal(dbPayload.retry_count);
            expect(kafkaPayload.userData).to.be.equal(dbPayload.user_data);
            expect(kafkaPayload.paymentDate).to.be.equal(dbPayload.payment_date);
       
        });
    
        it("mapBillsTableColumns | ", () => {
            let dbPayload = {
                "customer_id": "1147438071",
                "rechargeNumber": "055602210545639",
                "product_id": "105826600",
                "operator": "Hero FinCorp",
                "amount": "15561",
                "due_date": null,
                "bill_date": "2021-08-12 13:31:50",
                "bill_fetch_date": "2021-08-12 13:31:50",
                "service_id": 0,
                "paytype": null,
                "service": null,
                "circle": null,
                "gateway": null,
                "customer_mobile": "9822468899",
                "customer_email": null,
                "nextBillFetchDate": "2021-08-27 13:31:50",
                "paymentChannel": null,
                "retry_count": 0,
                "status": 4,
                "reason": "",
                "extra": "",
                "published_date": "2021-08-12 13:31:50",
                "user_data": "{\"recharge_number_2\":null}",
                "notification_status": 1,
                "payment_date": null,
                "is_automatic": 0,
                "updateAllCustIdRecords": false,
                "time_interval": null
            };
            
            let kafkaPayload = libraryObj.mapBillsTableColumns(dbPayload);
    
            expect(kafkaPayload.customerId).to.be.equal(dbPayload.customer_id);
            expect(kafkaPayload.rechargeNumber).to.be.equal(dbPayload.rechargeNumber);
            expect(kafkaPayload.productId).to.be.equal(dbPayload.product_id);
            expect(kafkaPayload.billDate).to.be.equal(dbPayload.bill_date);
            expect(kafkaPayload.dueDate).to.be.equal(dbPayload.due_date);
            expect(kafkaPayload.billFetchDate).to.be.equal(dbPayload.bill_fetch_date);
            expect(kafkaPayload.nextBillFetchDate).to.be.equal(dbPayload.nextBillFetchDate);
            expect(kafkaPayload.customerMobile).to.be.equal(dbPayload.customer_mobile);
            expect(kafkaPayload.customerEmail).to.be.equal(dbPayload.customer_email);
            expect(kafkaPayload.paymentChannel).to.be.equal(dbPayload.paymentChannel);
            expect(kafkaPayload.retryCount).to.be.equal(dbPayload.retry_count);
            expect(kafkaPayload.userData).to.be.equal(dbPayload.user_data);
            expect(kafkaPayload.paymentDate).to.be.equal(dbPayload.payment_date);
        });
    
    });
