/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import nock from 'nock'

import SMSParsingSyncCCLibrary from '../../lib/smsParsingSyncCCBills'
import STARTUP_MOCK from '../__mocks__/startUp'
import DCAT_API_MOCK_Data from './DCAT_getCategoryAPIResp'

chai.use(chaiAsPromised);
chai.use(sinonChai);


const { expect } = chai;

let server;

describe("Library: SMS ParsingSync CC Bills library test suite", function () {
    let libraryObj;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            libraryObj = new SMSParsingSyncCCLibrary(options);
            done();
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });
   
    it("PG API | add spaces in masked CC", (done) => {
        let maskedCardNumber = "5165"
        let maskedCardNumberWithSpaces = libraryObj.createMaskedCC(maskedCardNumber);
        expect(maskedCardNumberWithSpaces).to.be.equal("XXXX XXXX XXXX 5165");
        return done();
    });

    it("PG API | add spaces in masked CC", (done) => {
        let maskedCardNumber = "6929"
        let maskedCardNumberWithSpaces = libraryObj.createMaskedCC(maskedCardNumber);
        expect(maskedCardNumberWithSpaces).to.be.equal("XXXX XXXX XXXX 6929");
        return done();
    });

    it("PG API | add spaces in masked CC", (done) => {
        let maskedCardNumber = "1000"
        let maskedCardNumberWithSpaces = libraryObj.createMaskedCC(maskedCardNumber);
        expect(maskedCardNumberWithSpaces).to.be.equal("XXXX XXXX XXXX 1000");
        return done();
    });


    it("getDcatCacheParams | SUCCESS | MATCHING_PID_FOUND", (done) => {
        let categoryData = DCAT_API_MOCK_Data.SUCCESS_RESPONSE;
        let {status : status , type : type , data : data } = libraryObj.getDcatCacheParams(categoryData);
        expect(status).to.be.equal("SUCCESS");
        expect(type).to.be.equal("MATCHING_PID_FOUND");
        return done();
    });

    it("getDcatCacheParams | ERROR | DUPLICATE_UNIQUE_KEY", (done) => {
        let categoryData = DCAT_API_MOCK_Data.DUPLICATE_UNIQUE_KEY_RESPONSE;
        let {status : status , type : type , data : data } = libraryObj.getDcatCacheParams(categoryData);
        expect(status).to.be.equal("ERROR");
        expect(type).to.be.equal("DUPLICATE_UNIQUE_KEY");
        expect(data).to.be.equal("0_inds_visa");
        return done();
    });


    it("getDcatCacheParams | ERROR | ERROR_WHILE_CHACHING_DCAT_CATEGORY_DATA", (done) => {
        let categoryData = DCAT_API_MOCK_Data;
        let {status : status , type : type , data : data } = libraryObj.getDcatCacheParams(categoryData);
        expect(status).to.be.equal("ERROR");
        expect(type).to.be.equal("ERROR_WHILE_CHACHING_DCAT_CATEGORY_DATA");
        // expect(data).to.be.equal("0_inds_visa");
        return done();
    });

    it("getDcatCacheParams | ERROR | DIFFERENT_PAYTYPE", (done) => {
        let categoryData = DCAT_API_MOCK_Data.DIFFERENT_PAYTYPE_RESPONSE;
        let {status : status , type : type , data : data } = libraryObj.getDcatCacheParams(categoryData);
        expect(status).to.be.equal("ERROR");
        expect(type).to.be.equal("DIFFERENT_PAYTYPE");
        expect(data).to.be.equal("Credit card xxxxxx");
        return done();
    });

    it("getDcatCacheParams | ERROR | DIFFERENT_SERVICE_CATEGORY", (done) => {
        let categoryData = DCAT_API_MOCK_Data.DIFFERENT_SERVICE_CATEGORY_RESPONSE;
        let {status : status , type : type , data : data } = libraryObj.getDcatCacheParams(categoryData);
        expect(status).to.be.equal("ERROR");
        expect(type).to.be.equal("DIFFERENT_SERVICE_CATEGORY");
        expect(data).to.be.equal("Financial Services XXXXXX");
        return done();
    });


    it("getDcatCacheParams | ERROR | DIFFERENT_BANK_CODE", (done) => {
        let categoryData = DCAT_API_MOCK_Data.DIFFERENT_BANK_CODE_RESPONSE;
        let {status : status , type : type , data : data } = libraryObj.getDcatCacheParams(categoryData);
        expect(status).to.be.equal("ERROR");
        expect(type).to.be.equal("DIFFERENT_BANK_CODE");
        expect(data).to.be.equal("OPERATOR_LABEL_DETAIL_BANKCODE_INDS_bankCode_INDS XXXXX");
        return done();
    });

    it("isPaytmFirstCCInSagaCCDetails | displayBankName | CC is Paytm First", (done) => {
        let pgSavedCCData = {'product': {
            "cardId": **********,
            "maskedCardNumber": "435376XXXXXX4274",
            "cardType": "CC",
            "cardExpiry": "122021",
            "cardScheme": "VISA",
            "bankName": "Paytm First",
            "displayName" : "Paytm First",
            "cardIndexNumber": "2019120572110452d85d1bce780afc8ac6ceb3c342e02"
        }};

        let paytmFirstCCFlag = libraryObj.isPaytmFirstCCInSagaCCDetails(pgSavedCCData);
        expect(paytmFirstCCFlag).to.be.equal(true);
        return done();
    });

    it("isPaytmFirstCCInSagaCCDetails | bin number | CC is Paytm First", (done) => {
        let pgSavedCCData = {
            "cardId": **********,
            "maskedCardNumber": "438106XXXXXX4274",
            "cardType": "CC",
            "cardExpiry": "122021",
            "cardScheme": "VISA",
            "bankName": "CITI",
            "cardIndexNumber": "2019120572110452d85d1bce780afc8ac6ceb3c342e02"
        };

        let paytmFirstCCFlag = libraryObj.isPaytmFirstCCInSagaCCDetails(pgSavedCCData);
        expect(paytmFirstCCFlag).to.be.equal(true);
        return done();
    });

    it("isPaytmFirstCCInSagaCCDetails | CC is not Paytm First", (done) => {
        let pgSavedCCData = {
            "cardId": **********,
            "maskedCardNumber": "435376XXXXXX4274",
            "cardType": "CC",
            "cardExpiry": "122021",
            "cardScheme": "VISA",
            "bankName": "CITI",
            "cardIndexNumber": "2019120572110452d85d1bce780afc8ac6ceb3c342e02"
        };

        let paytmFirstCCFlag = libraryObj.isPaytmFirstCCInSagaCCDetails(pgSavedCCData);
        expect(paytmFirstCCFlag).to.be.equal(false);
        return done();
    });

    it("getUniqueKeyForSavedCardsData | Paytm First CC | case-sensitive | uniqueKey", (done) => {
        let ccDataObj = {
            "isPaytmFirstCard" : "1",
            "cardScheme": "VISA",
            "bankName": "HDFC",
        };
        let uniqueKey = libraryObj.getUniqueKeyForSavedCardsData(ccDataObj);
        expect(uniqueKey).to.be.equal("1_hdfc_visa");
        return done();
    });

    it("getUniqueKeyForSavedCardsData | Paytm First CC | case-sensitive | uniqueKey", (done) => {
        let ccDataObj = {
            "isPaytmFirstCard" : "1",
            "cardScheme": "visa",
            "bankName": "hdfc",
        };

        let uniqueKey = libraryObj.getUniqueKeyForSavedCardsData(ccDataObj);
        expect(uniqueKey).to.be.equal("1_hdfc_visa");
        return done();
    });


    it("getUniqueKeyForSavedCardsData | Paytm First CC according to bin | uniqueKey", (done) => {
        let ccDataObj = {
            "isPaytmFirstCard" : "1",
            "cardScheme": "VISA",
            "bankName": "CITI",
        };

        let uniqueKey = libraryObj.getUniqueKeyForSavedCardsData(ccDataObj);
        expect(uniqueKey).to.be.equal("1_citi_visa");
        return done();
    });

    it("getUniqueKeyForSavedCardsData | Non Paytm First CC | uniqueKey", (done) => {
        let ccDataObj = {
            "isPaytmFirstCard" : "0",
            "cardScheme": "VISA",
            "bankName": "CITI",
        };

        let uniqueKey = libraryObj.getUniqueKeyForSavedCardsData(ccDataObj);
        expect(uniqueKey).to.be.equal("0_citi_visa");
        return done();
    });

    it("getFinancialServicesPID | dcatCategoryCacheData is not initialized. ", (done) => {   
        let pgSavedCCDataUniqueKey = "0_hdfc_visa";
        libraryObj.getFinancialServicesPID((error)=>{
            expect(error).to.be.equal(`dcatCategoryCacheData is not initialized.`);
            return done();
        },pgSavedCCDataUniqueKey);
    });


    it("getPIDFromDcatCategoryCacheData | product id match", (done) => { 
        libraryObj.dcatCategoryCacheData = {
            '0_uco_visa' : *********,
            '0_ubi_visa': *********,
            '0_uni_visa': *********,
            '0_vjya_visa': *********,
            '0_citibank_visa': *********
        };
        let pgSavedCCDataUniqueKey = "0_citibank_visa";
        let product_id = libraryObj.getPIDFromDcatCategoryCacheData(pgSavedCCDataUniqueKey);
        expect(product_id).to.be.equal(*********);
        return done();
    });

    it("getPIDFromDcatCategoryCacheData | product id not found", (done) => { 
        libraryObj.dcatCategoryCacheData = {
            '0_uco_visa' : *********,
            '0_ubi_visa': *********,
            '0_uni_visa': *********,
            '0_vjya_visa': *********,
            '0_citibank_visa': *********
        };
        let pgSavedCCDataUniqueKey = "0_hdfc_visa";
        let product_id = libraryObj.getPIDFromDcatCategoryCacheData(pgSavedCCDataUniqueKey);
        expect(product_id).to.be.equal(null);
        return done();
    });


    it("getFinancialServicesPID | successful Product id retrieval ", (done) => {   
        libraryObj.dcatCategoryCacheData = {
            '0_uco_visa' : *********,
            '0_ubi_visa': *********,
            '0_uni_visa': *********,
            '0_vjya_visa': *********,
            '0_citibank_visa': *********
        };

        let pgSavedCCDataUniqueKey = "0_citibank_visa";
        libraryObj.getFinancialServicesPID((error, productId)=>{
            expect(error).to.be.equal(null);
            expect(productId).to.be.equal(*********);
            return done();
        },pgSavedCCDataUniqueKey);
    });


    it("getFinancialServicesPID | Product id not found ", (done) => {   
        libraryObj.dcatCategoryCacheData = {
            '0_uco_visa' : *********,
            '0_ubi_visa': *********,
            '0_uni_visa': *********,
            '0_vjya_visa': *********,
            '0_citibank_visa': *********
        };

        let pgSavedCCDataUniqueKey = "0_hdfc_visa";
        libraryObj.getFinancialServicesPID((error, productId)=>{
            expect(error).to.be.equal(`Product Id not found for unique key ${pgSavedCCDataUniqueKey}`);
            expect(productId).to.be.equal(undefined);
            return done();
        },pgSavedCCDataUniqueKey);
    });    

    it("PG savedCardData Processing  | MATCHING_MCN", (done) => {
        let processedRecord = {"lastCC":"0792","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
        let pgSavedCardsData = {
          "recents": [
            {
              "product": {
                "productId": *********,
                "operator": "neft_Citibank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "CITI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_Citibank",
              "type": "savedCard",
              "priority": 3,
              "cta": [
                {
                  "type": "button"
                },
                {
                  "type": "childCta",
                  "cta": [
                    {
                      "type": "deleteCard"
                    }
                  ]
                }
              ],
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "cin": "20210228508007a1985f4af408d279275cb0590f44486"
              },
              "recharge_number": "XXXX XXXX XXXX 0792",
              "recharge_number_for_display": "XXXX  XXXX  XXXX  0792"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "cta": [
                {
                  "type": "button"
                },
                {
                  "type": "childCta",
                  "cta": [
                    {
                      "type": "deleteCard"
                    }
                  ]
                }
              ],
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "cin": "2021030728490fed1be0f0d636ba42f061f008b2a5660"
              },
              "recharge_number": "XXXX XXXX XXXX 2013",
              "recharge_number_for_display": "XXXX  XXXX  XXXX  2013"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            }
          ]
        };
        libraryObj.getProcessedSagaSavedCardsData((data) => {
            expect(data.type).to.be.equal("MATCHING_MCN");
            return done();

        },processedRecord, pgSavedCardsData);
        
    });

    it.skip("PG savedCardData Processing  | API_STATUS_NOT_SUCCESS", (done) => {
        let processedRecord = {"lastCC":"4274","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
        let pgSavedCardsData = {
            "head": {
                "responseTimestamp": "*************",
                "version": null,
                "clientId": null,
                "signature": null
            },
            "body": {
                "responseStatus": "ERROR",
                "codeDetail": "Success",
                "response": [ { 
                    "savedCardId":"618b6273d83f4545dc117294",
                    "cardScheme":"VISA",
                    "expiryDate":"082022",
                    "issuingBankName":"ICICI Bank",
                    "issuingBankCardVariant":"Regalia",
                    "cardSuffix":"4274",
                    "cardType":"CC",
                    "displayName":"ICICI Bank",
                    "isCardCoft": true,
                    "panUniqueReference":"V0010013021295362620166880000",
                    "tokenBin":"*********",
                    "tokenStatus":"ACTIVE"
           
                 },
                 {
           
                  "savedCardId": "************* c7ef9cd6628a76c1937bf1393d8a414b",       
                  "cardScheme": "VISA",
                  "expiryDate": "102022",
                  "issuingBankName": "Union Bank of India",
           
                    "issuingBankCardVariant":"Regalia",
           
                   "cardSuffix": "0456",
                   "cardType": "DC",
                   "displayName": "Union Bank Debit Card",
                   "isCardCoft": false,
                   "cardFirstSixDigits": "452055",
                   "issuerCode": "UNI"
           
                 }]
            }
        };

        libraryObj.getProcessedPGSavedCardsData((data) => {
            expect(data.status).to.be.equal("ERROR");
            expect(data.type).to.be.equal("API_STATUS_NOT_SUCCESS");
            return done();

        },processedRecord, pgSavedCardsData);
    });

    it("PG savedCardData Processing  | SAVED_CARD_DETAILS_NOT_EXISTS", (done) => {
        let processedRecord = {"lastCC":"4174","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
            let pgSavedCardsData = {
            };
            
        libraryObj.getProcessedSagaSavedCardsData((data) => {
            expect(data.status).to.be.equal("ERROR");
            expect(data.type).to.be.equal("SAVED_CARD_DETAILS_NOT_EXISTS");
            return done();
        },processedRecord, pgSavedCardsData);
    });

    it("PG savedCardData Processing  | NO_SAVE_CARDS_FOUND", (done) => {
        let processedRecord = {"lastCC":"4274","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
        let pgSavedCardsData = {
            "head": {
                "responseTimestamp": "*************",
                "version": null,
                "clientId": null,
                "signature": null
            },
                "recents": []

        };

        libraryObj.getProcessedSagaSavedCardsData((data) => {
            expect(data.status).to.be.equal("ERROR");
            expect(data.type).to.be.equal("NO_SAVE_CARDS_FOUND");
            return done();

        },processedRecord, pgSavedCardsData);
        
    });

    it("PG savedCardData Processing  | MAX_SAVE_CARDS_LIMIT_EXCEED", (done) => {
        let processedRecord = {"lastCC":"8767","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
        let pgSavedCardsData = JSON.stringify({
          "recents": [ {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            },
            {
              "product": {
                "productId": *********,
                "operator": "neft_ICICIBank",
                "categoryId": 156705,
                "service": "financial services",
                "cardType": "CC",
                "bankName": "ICICI",
                "cardNetwork": "VISA"
              },
              "operator": "neft_ICICIBank",
              "type": "savedCard",
              "priority": 3,
              "bills": [
                {
                  "billState": "NO_BILL"
                }
              ],
              "additional_info": {
                "panUniqueReference": "**********",
                "tin": "**********",
                "isCardCoft": true
              },
              "recharge_number": "4315 81XX XXXX 8767",
              "recharge_number_for_display": "4315  81XX  XXXX  8767"
            }
          ]
        });
        libraryObj.getProcessedSagaSavedCardsData((data) => {
            expect(data.status).to.be.equal("ERROR");
            expect(data.type).to.be.equal("MAX_SAVE_CARDS_LIMIT_EXCEED");
            return done();
        },processedRecord, pgSavedCardsData);
    });

    it("PG savedCardData Processing  | MULTIPLE_MATCHING_MCN", (done) => {
        let processedRecord = {"lastCC":"8767","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
        let pgSavedCardsData = {
            "recents": [ {
                "product": {
                  "productId": *********,
                  "operator": "neft_ICICIBank",
                  "categoryId": 156705,
                  "service": "financial services",
                  "cardType": "CC",
                  "bankName": "ICICI",
                  "cardNetwork": "VISA"
                },
                "operator": "neft_ICICIBank",
                "type": "savedCard",
                "priority": 3,
                "bills": [
                  {
                    "billState": "NO_BILL"
                  }
                ],
                "additional_info": {
                  "panUniqueReference": "**********",
                  "tin": "**********",
                  "isCardCoft": true
                },
                "recharge_number": "4315 81XX XXXX 8767",
                "recharge_number_for_display": "4315  81XX  XXXX  8767"
              },
              {
                "product": {
                  "productId": *********,
                  "operator": "neft_ICICIBank",
                  "categoryId": 156705,
                  "service": "financial services",
                  "cardType": "CC",
                  "bankName": "ICICI",
                  "cardNetwork": "VISA"
                },
                "operator": "neft_ICICIBank",
                "type": "savedCard",
                "priority": 3,
                "bills": [
                  {
                    "billState": "NO_BILL"
                  }
                ],
                "additional_info": {
                  "panUniqueReference": "**********",
                  "tin": "**********",
                  "isCardCoft": true
                },
                "recharge_number": "4315 81XX XXXX 8767",
                "recharge_number_for_display": "4315  81XX  XXXX  8767"
              }
            ]
        };
        libraryObj.getProcessedSagaSavedCardsData((data) => {
            expect(data.status).to.be.equal("ERROR");
            expect(data.type).to.be.equal("MULTIPLE_MATCHING_MCN");
            return done();
        },processedRecord, pgSavedCardsData);
        
    });

    it("PG savedCardData Processing  | PROCESSING_FAILURE", (done) => {
        let processedRecord = {"lastCC1":"4274","dueAmt":200,"totalAmt":1856,"dueDate":"2021-06-10","bankName":"SBI","billDate":"2021-05-28","cId":"********","isReliable":"1"}
        let pgSavedCardsData = {
            "head": {
                "responseTimestamp": "*************",
                "version": null,
                "clientId": null,
                "signature": null
            }
        };
        libraryObj.getProcessedSagaSavedCardsData((data) => {
            expect(data.status).to.be.equal("ERROR");
            expect(data.type).to.be.equal("PROCESSING_FAILURE");
            return done();
        },processedRecord, pgSavedCardsData);
    });
    
});