/*
jshint 
esversion: 8
*/

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import MOMENT from 'moment'
import nock from 'nock'

import utility from '../../lib'

// src/unitTest/lib/recentBills.tests.js
// src/services/billSubscriber.js

chai.use(chaiAsPromised);
chai.use(sinonChai);
import {createHash} from 'crypto'

const { expect } = chai;

let server;

describe("generate ID",()=>{
    it("when everything is present",()=>{
        console.log(utility)
        let res = utility.generateReconID("abc","xyz",10,MOMENT('2024-01-01'),MOMENT('2024-01-01'))
        let exp = createHash('sha1').update('abc_xyz_10_1704047400000_1704047400000').digest('base64')
        chai.assert.deepEqual(res , exp)
    })

    it("should not include operator if its not prsent",()=>{
        console.log(utility)
        let res = utility.generateReconID("abc","",10,MOMENT('2024-01-01'),MOMENT('2024-01-01'))
        let exp = createHash('sha1').update('abc_10_1704047400000_1704047400000').digest('base64')
        chai.assert.deepEqual(res , exp)
    })

    it("should include amount even if its 0 , 0.00 or 0 as string",()=>{
        console.log(utility)
        let res = utility.generateReconID("abc","",0,MOMENT('2024-01-01'),MOMENT('2024-01-01'))
        let exp = createHash('sha1').update('abc_0_1704047400000_1704047400000').digest('base64')
        chai.assert.deepEqual(res , exp)
        res = utility.generateReconID("abc","",0.0,MOMENT('2024-01-01'),MOMENT('2024-01-01'))
        chai.assert.deepEqual(res , exp)

    })

    it("should not include dates if garbage or null",()=>{
        console.log(utility)
        let res = utility.generateReconID("abc","xyz",0,"xxx",MOMENT('2024-01-01'))
        let exp = createHash('sha1').update('abc_xyz_0_1704047400000').digest('base64')
        chai.assert.deepEqual(res , exp)
        res = utility.generateReconID("abc","xyz",0.0,null,MOMENT('2024-01-01'))
        chai.assert.deepEqual(res , exp)
        
    })

    it("should include dates only even if time is present",()=>{
        console.log(utility)
        let res = utility.generateReconID("abc","xyz",0,"xxx",MOMENT('2024-01-01 22:30:00'))
        let exp = createHash('sha1').update('abc_xyz_0_1704047400000').digest('base64')
        chai.assert.deepEqual(res , exp)
        res = utility.generateReconID("abc","xyz",0.0,null,MOMENT('2024-01-01'))
        chai.assert.deepEqual(res , exp)
        
    })
})
/**
 * case let say we on-board new operator in emidue consumer
 * Will it be on-boarded in recent service flow?
 */