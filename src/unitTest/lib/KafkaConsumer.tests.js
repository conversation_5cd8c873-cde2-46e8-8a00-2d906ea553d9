

const lgr = require("lgr");
// var DTLOG = require("../lib/dtlog");
// let KafkaConsumer = require("../../lib/KafkaConsumer");
import { describe, it, before, beforeEach } from 'mocha';
import STARTUP_MOCK from '../__mocks__/startUp'
import chai, { assert , expect } from "chai";
import SINON from 'sinon';
import sinon from 'sinon';
import digitalReminderConfig from '../__mocks__/digitalReminderConfig';
import proxyquire from 'proxyquire'
let mockKafka = {};
let mockAdminObj = {
  connect: sinon.stub().yields(),
  partitionCountOfTopics: sinon.stub(),
  consumersCountOfTopicsInGroup: sinon.stub(),
  close: sinon.stub()
};
const KafkaConsumer = proxyquire('../../lib/KafkaConsumer', { 'kafkajs': mockKafka , 'infra-utils/dest/kafka/admin': function(){
  let self = this;
  Object.keys(mockAdminObj).forEach(key=>{
    self[key] = mockAdminObj[key]
  })
}});
// mockAdminObj = 
// const admin = proxyquire('../../lib/KafkaConsumer',{})
// console.log(admin)

let runStub = sinon.stub()
mockKafka.Kafka = function(){
  this.connect = sinon.stub()
  this.consumer  =sinon.stub().returns({
      run : runStub,
      subscribe : sinon.stub(),
      pause : sinon.stub(),
      resume : sinon.stub(),
      connect : sinon.stub().resolves(),
      disconnect : sinon.stub().resolves()
  }) 
  }

const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
STARTUP_MOCK
let KafkaConsumerObj;

describe("dynamicConfig : ", function () {


  beforeEach(function () {
    //  stub = SINON.stub(KafkaConsumer.digitalReminderConfigLoader,'getDynamicConfig')
    KafkaConsumerObj = new KafkaConsumer({
      topics : ["A","B"],
      kafkaHost : "localhost:9092",
      groupId:"!23",
      id:"123"
    })
  });

  // afterEach(function () {
  //   stub.restore()
  // })



  it("Throw errror if Mandatory Params Missing",()=>{

    try{
      KafkaConsumerObj = new KafkaConsumer({
        topics : ["A","B"],
        // kafkaHost : "localhost:9092",
        groupId:"!23",
        id:"123"
      })
    }catch(err){
      assert.ok(err)
    }

    try{
      KafkaConsumerObj = new KafkaConsumer({
        topics : ["A","B"],
        // kafkaHost : "localhost:9092",
        groupId:"!23",
        id:"123"
      })
    }catch(err){
      assert.ok(err)
    }

    try{
      KafkaConsumerObj = new KafkaConsumer({
        topics : ["A","B"],
        kafkaHost : "localhost:9092",
        // groupId:"!23",
        id:"123"
      })
    }catch(err){
      assert.ok(err)
    }

    try{
      KafkaConsumerObj = new KafkaConsumer({
        topics : ["A","B"],
        kafkaHost : "localhost:9092",
        groupId:"!23",
        // id:"123"
      })
    }catch(err){
      assert.ok(err)
    }

    try{
      KafkaConsumerObj = new KafkaConsumer({
        // topics : ["A","B"],
        kafkaHost : "localhost:9092",
        groupId:"!23",
        id:"123"
      })
    }catch(err){
      assert.ok(err)
    }

    
   
  })

  it("initConsumer",(done)=>{
    KafkaConsumerObj = new KafkaConsumer({
      topics : ["A","B"],
      kafkaHost : "localhost:9092",
      groupId:"!23",
      id:"123"
    })

    let sandbox = sinon.createSandbox()
    let x = sandbox.stub(KafkaConsumerObj,'_validateCreateConsumer')
    x.yields()
    // let y = sandbox.stub()

    KafkaConsumerObj.initConsumer(()=>{},(err)=>{
      assert.deepEqual(err , null)
      done()
    })
  })

  it("Commit offsets after processing complete", (done)=>{
    let stubb = sinon.stub()
    runStub = sinon.stub().callsFake((obj)=>{
      obj.eachBatch({batch:{
          topic:"XYZ",
          partition:1,
          messages:[]
      },commitOffsetsIfNecessary : stubb})
  })
    KafkaConsumerObj = new KafkaConsumer({
      topics : ["A","B"],
      kafkaHost : "localhost:9092",
      groupId:"!23",
      id:"123"
    })

    let sandbox = sinon.createSandbox()
    let x = sandbox.stub(KafkaConsumerObj,'_validateCreateConsumer')
    x.yields()
    let y = sandbox.stub()
    y.yields()

    KafkaConsumerObj.initConsumer(y,()=>{})
    setTimeout(()=>{
      assert(stubb.calledOnce)
      sandbox.restore()
      done()
    },20)
   
 
  })

  it("close consumer",()=>{
    KafkaConsumerObj = new KafkaConsumer({
      topics : ["A","B"],
      kafkaHost : "localhost:9092",
      groupId:"!23",
      id:"123"
    })

    let sandbox = sinon.createSandbox()
    let x = sandbox.stub(KafkaConsumerObj,'_validateCreateConsumer')
    x.yields()

    KafkaConsumerObj.initConsumer(()=>{},()=>{})
    KafkaConsumerObj.close()
  })

  it("pauseConsumer",()=>{
    KafkaConsumerObj = new KafkaConsumer({
      topics : ["A","B"],
      kafkaHost : "localhost:9092",
      groupId:"!23",
      id:"123"
    })

    let sandbox = sinon.createSandbox()
    let x = sandbox.stub(KafkaConsumerObj,'_validateCreateConsumer')
    x.yields()

    KafkaConsumerObj.initConsumer(()=>{},()=>{})
    KafkaConsumerObj._pauseConsumer()
  })

  it("resumeConsumer",()=>{
    KafkaConsumerObj = new KafkaConsumer({
      topics : ["A","B"],
      kafkaHost : "localhost:9092",
      groupId:"!23",
      id:"123"
    })

    let sandbox = sinon.createSandbox()
    let x = sandbox.stub(KafkaConsumerObj,'_validateCreateConsumer')
    x.yields()

    KafkaConsumerObj.initConsumer(()=>{},()=>{})
    KafkaConsumerObj._resumeConsumer()
  })

  describe('_validateCreateConsumer', () => {
    // let mockAdminObj;
    let instanceUnderTest;

    beforeEach(() => {
      KafkaConsumerObj = new KafkaConsumer({
        topics : ["A","B"],
        kafkaHost : "localhost:9092",
        groupId:"!23",
        id:"123"
      })
        // mockAdminObj = {
        //     connect: sinon.stub(),
        //     partitionCountOfTopics: sinon.stub(),
        //     consumersCountOfTopicsInGroup: sinon.stub(),
        //     close: sinon.stub()
        // };
        instanceUnderTest = {
            options: {
                kafkaHost: 'mockKafkaHost',
                groupId: 'mockGroupId'
            },
            topics: ['topic1', 'topic2'],
            _validateCreateConsumer: KafkaConsumerObj._validateCreateConsumer.bind({ options: { kafkaHost: 'mockKafkaHost:9092' }, topics: ['topic1', 'topic2'] })
        };
    });

    it('should validate consumer creation', (done) => {
        const partitionCountOfTopicsCallback = sinon.stub();
        const consumersCountOfTopicsInGroupCallback = sinon.stub();

        mockAdminObj.connect.yields();
        mockAdminObj.partitionCountOfTopics.callsArgWith(1, null, { 'topic1': 4, 'topic2': 4 });
        mockAdminObj.consumersCountOfTopicsInGroup.callsArgWith(2, null, { 'topic1': 2, 'topic2': 2 });
        
        instanceUnderTest._validateCreateConsumer((err) => {
            expect(err).to.be.null;
            expect(mockAdminObj.close.calledOnce).to.be.true;
            done();
        });
      
    });

    // it('should handle error during validation', (done) => {
    //     const error = new Error('Test error');

    //     mockAdminObj.connect.callsArgWith(0, error);

    //     instanceUnderTest._validateCreateConsumer((err) => {
    //         expect(err).to.equal(error);
    //         expect(mockAdminObj.close.calledOnce).to.be.true;
    //         done();
    //     });

    //     expect(mockAdminObj.connect.calledOnce).to.be.true;
    //     expect(mockAdminObj.partitionCountOfTopics.called).to.be.false;
    //     expect(mockAdminObj.consumersCountOfTopicsInGroup.called).to.be.false;
    // });
});

  
})