/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import nock from 'nock'

import DigitalCatalog from '../../lib/digitalCatalog'
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("Library: Payment Gateway library test suite", function () {
    let libraryObj;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            libraryObj = new DigitalCatalog(options);
            done();
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });
    
    it("PG API | _capitalize | ", (done) => {
        let inputString = "uttar pradesh power corporation ltd. (uppcl)";
        let capitalizeStr = libraryObj._capitalize(inputString);
        expect(capitalizeStr).to.be.equal("Uttar Pradesh Power Corporation Ltd. (uppcl)");
        return done();
    });
});