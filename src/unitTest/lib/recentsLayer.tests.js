
// import request from'request';
// import sinon from 'sinon';
// import RecentsLayer from '../../lib/recentsLayer';
// import chai from "chai";

// const { expect } = chai;
// import { describe, it, before } from 'mocha';

// describe('recentsLayer API::', () => {
//     let recentsLayer;
//     before(function () {
//          recentsLayer = new RecentsLayer({
//              L : require('lgr'),
//              config : require('../../config')
//          });
//     });

//     it('Successfully updated!', (done) => {
//         let requestMock = sinon.mock(request);

//         let queryParam = { customer_id: 1, recharge_number: 1, operator: "airtel" };
//         let fieldName = 'bills';
//         let fieldValue = { bill_date: "2020-08-20" };
//         let source = "publisher";


//         recentsLayer.update(() => {
//             console.log('heeey');

//             requestMock.verify();
//             requestMock.restore();
//             done();
//         },queryParam,fieldName,fieldValue,source);
//     });
// });