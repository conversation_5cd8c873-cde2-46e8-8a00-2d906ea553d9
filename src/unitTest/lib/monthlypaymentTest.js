/*
  jshint 
    esversion: 8
 */

    'use strict';

    import { describe, it, before, beforeEach, afterEach } from 'mocha';
    import chai from "chai";
    import sinonChai from "sinon-chai";
    import chaiAsPromised from "chai-as-promised";
    import MOMENT from 'moment'
    import sinon from 'sinon';
    import _ from 'lodash'

    import MonthlyPaymentFlow from "../../lib/reminderFlowManager/monthlypayment"
    import STARTUP_MOCK from '../__mocks__/startUp'

    
    chai.use(chaiAsPromised);
    chai.use(sinonChai);

    const { expect } = chai;
    
    let server;

    describe("Library: monthlypayment library test suite", function(){
        let monthlyPaymentFlow;

        before(function () {
            STARTUP_MOCK.init(function(error, options){
                monthlyPaymentFlow = new MonthlyPaymentFlow(options);
                done();
            });
        });

        beforeEach(function () {
            server = sinon.fakeServer.create();
        });

        afterEach(function () {
            server.restore();
        });

        it("getHeauristicDueDateBasedOnTxn | validAmount ",()=>{
            let inputAmount=900;
            let belowThresholdAddTimeDuration = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'BELOW_TIME_DURATION'], 1),
                belowThresholdAddTimeUnit = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'BELOW_TIME_UNIT'], 'days');
            let outDate = MOMENT().add(belowThresholdAddTimeDuration, belowThresholdAddTimeUnit).format('YYYY-MM-DD');
            let deduceAmountObje = monthlyPaymentFlow.getHeauristicDueDateBasedOnTxn(inputAmount);
            console.log("printing deduceAmountObje",deduceAmountObje);
            console.log("printing expected",outDate);
            expect(deduceAmountObje).to.equal(outDate);
        })

        it("getHeauristicDueDateBasedOnTxn | validAmount ",()=>{
            let inputAmount=2000;
            let aboveThresholdAddTimeDuration = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'ABOVE_TIME_DURATION'], 1),
                aboveThresholdAddTimeUnit = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'ABOVE_TIME_UNIT'], 'months');
            let outDate = MOMENT().add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD');
            let deduceAmountObje = monthlyPaymentFlow.getHeauristicDueDateBasedOnTxn(inputAmount);
            console.log("printing deduceAmountObje",deduceAmountObje);
            console.log("printing expected",outDate);
            expect(deduceAmountObje).to.equal(outDate);
        })
    })