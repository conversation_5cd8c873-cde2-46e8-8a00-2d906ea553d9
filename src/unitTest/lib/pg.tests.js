/*
  jshint 
    esversion: 8
 */

'use strict';

import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinonChai from "sinon-chai";
import chaiAsPromised from "chai-as-promised";
// import MOMENT from 'moment'
import sinon from 'sinon';
import _ from 'lodash'
import nock from 'nock'

import PGLibrary from '../../lib/pg'
import STARTUP_MOCK from '../__mocks__/startUp'

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;

let server;

describe("Library: Payment Gateway library test suite", function () {
    let libraryObj;

    before(function (done) {
        STARTUP_MOCK.init(function(error, options){
            libraryObj = new PGLibrary(options);
            done();
        });
    });

    beforeEach(function () {
        server = sinon.fakeServer.create();
    });

    afterEach(function () {
        server.restore();
    });
    
    it("PG API | JWT auth. check passed", (done) => {
        let requestData = { "custId" :  "11065108" };
        let jwtToken = libraryObj.getJwtToken(requestData);
        expect(typeof jwtToken ).to.be.equal('string');
        return done();
    });
});