

const lgr = require("lgr");
// var DTLOG = require("../lib/dtlog");
let DigitalReminderConfigLib = require("../../lib/digitalReminderConfig");
import { describe, it, before, beforeEach } from 'mocha';
import STARTUP_MOCK from '../__mocks__/startUp'
import chai, { assert } from "chai";
import SINON from 'sinon';
import Sinon from 'sinon';
import digitalReminderConfig from '../__mocks__/digitalReminderConfig';

let stub;
let otm = [{
    "id" : 1,
    "name" : "OPERATOR_TEMPLATE_MAPPING",
    "node" : "Operator1",
    "key_name" : "BILLGEN_CHAT",
    "value" : "8667",
    "type" : "number",
    "created_at" : "2023-03-27 15:36:02",
    "updated_at" : "2023-03-27 15:36:02",
    "status" : 1,
    "updated_by" : null
},
{
    "id" : 2,
    "name" : "OPERATOR_TEMPLATE_MAPPING",
    "node" : "Operator1",
    "key_name" : "BILLGEN_EMAIL",
    "value" : "8668",
    "type" : "number",
    "created_at" : "2023-03-27 15:36:02",
    "updated_at" : "2023-03-27 15:36:02",
    "status" : 1,
    "updated_by" : null
},
{
    "id" : 1,
    "name" : "OPERATOR_TEMPLATE_MAPPING",
    "node" : "Operator1",
    "key_name" : "BILLGEN_CHAT",
    "value" : "8667",
    "type" : "number",
    "created_at" : "2023-03-27 15:36:02",
    "updated_at" : "2023-03-27 15:36:02",
    "status" : 1,
    "updated_by" : null
},
{
    "id" : 2,
    "name" : "OPERATOR_TEMPLATE_MAPPING",
    "node" : "Operator2",
    "key_name" : "BILLGEN_EMAIL",
    "value" : "8668",
    "type" : "number",
    "created_at" : "2023-03-27 15:36:02",
    "updated_at" : "2023-03-27 15:36:02",
    "status" : 1,
    "updated_by" : null
}]

let otr = [
    {
        "id" : 1,
        "name" : "OPERATOR_TABLE_REGISTRY",
        "node" : "airtel",
        "key_name" : "TABLE_NAME",
        "value" : "bills_airtel",
        "type" : "string",
        "created_at" : "2023-03-27 15:36:02",
        "updated_at" : "2023-03-27 15:36:02",
        "status" : 1,
        "updated_by" : null
    },
    {
        "id" : 1,
        "name" : "OPERATOR_TABLE_REGISTRY",
        "node" : "jio",
        "key_name" : "TABLE_NAME",
        "value" : "bills_airtel",
        "type" : "string",
        "created_at" : "2023-03-27 15:36:02",
        "updated_at" : "2023-03-27 15:36:02",
        "status" : 1,
        "updated_by" : null
    }]
const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
STARTUP_MOCK
describe("dynamicConfig : ", function () {
  before(function () {
    console.log(DigitalReminderConfigLib.default.DigitalReminderConfigLib)

    STARTUP_MOCK.init(function(error, options){
        DigitalReminderConfigLib= new DigitalReminderConfigLib.default.DigitalReminderConfigLib(options);
        done();
    });

  });

  beforeEach(function () {
     stub = SINON.stub(DigitalReminderConfigLib.digitalReminderConfigLoader,'getDynamicConfig')
  });

  afterEach(function () {
    stub.restore()
  })



  it("parseRecentConfigs : should parse in the correct way",()=>{
    let res = DigitalReminderConfigLib.parseRecentConfigs({
      airtel: {
        firstBillDelay: 16,
      },
      COMMON: {
        EXCLUDE_CHANNEL_ID: 1,
      },
    });
    assert.deepEqual(res, {
      OPERATORS: {
        airtel: {
          firstBillDelay: 16,
        },
      },
      COMMON: {
        EXCLUDE_CHANNEL_ID: 1,
      },
    });
  })

  it("parseOperatorTableRegistryConfigs : should parse in the correct way",()=>{
    let res = DigitalReminderConfigLib.parseOperatorTableRegistryConfigs({
      airtel: {
        TABLE_NAME: 'airtel',
      }
    });
    assert.deepEqual(res, {airtel : 'airtel'});
  })


  it("parseOperatorTemplateMappingConfig : should parse in the correct way",()=>{
    let res = DigitalReminderConfigLib.parseOperatorTemplateMappingConfig({
      airtel: {
        BILLGEN_CHAT : 8667,
        BILLGEN_EMAIL : 8668
      }
    });
    assert.deepEqual(res, {airtel: {
        BILLGEN_CHAT : 8667,
        BILLGEN_EMAIL : 8668
      }});
  })

  it("parseOperatorNotInUseConfig : should parse in the correct way",()=>{
    let res = DigitalReminderConfigLib.parseOperatorNotInUseConfig({
      airtel: {
        NOTIFICATION_EXPIRY_PERIOD : 180
      }
    });
    assert.deepEqual(res, {airtel: 180});
  })

  it("parseSubscriberConfig : should parse in the correct way",()=>{
    let res = DigitalReminderConfigLib.parseSubscriberConfig({
      airtel: {
        NEXT_BILL_FETCH_DATES : 30
      }
    });
    assert.deepEqual(res, {
        NEXT_BILL_FETCH_DATES: {
            airtel : 30
    },
    NEXT_RETRY_FREQUENCY: {
    },
    BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS: [],
    EARLIEST_BILL_FETCH_DELAY : {
    }
    });
  })

  it("parseNotificationConfig : should only parse TEMPLATE_UTM , TEMPLATE_ID_BY_SERVICE or BlackListOperator",()=>{
    let config =  {
        BlackListOperator : {
            airtel : 1
        },
        TEMPLATE_ID_BY_SERVICE : {
            BR_APARTMENTS_DUEDATE_CHAT : 800,
            BR_APARTMENTS_DUEDATE_PUSH : 252
        },
        TEMP : 123
    }
    let res = DigitalReminderConfigLib.parseNotificationConfig(
      config);
    assert.deepEqual(res, {
        TEMPLATE_ID_BY_SERVICE : {
            BR_APARTMENTS_DUEDATE_CHAT : 800,
            BR_APARTMENTS_DUEDATE_PUSH : 252
        },
        BlackListOperator : ['airtel'],
        TEMPLATE_UTM :{}
    });
  })

  it("parseAirtelPublisherConfig : should only put SNOOZE TIME property in config",()=>{
    let res = DigitalReminderConfigLib.parseAirtelPublisherConfig({
      SNOOZE_TIME: {
        DAYS : 30,
        MONTHS : 10
      }
    });
    assert.deepEqual(res, {
        SNOOZE_TIME :{DAYS: 30}
    });
  })

  it("parseOperatorGatewayRegistryConfig : should parse correct way ",()=>{
    let res = DigitalReminderConfigLib.parseOperatorGatewayRegistryConfig({
      airtel : {
        GATEWAY_NAME : 'airtel2'
      }
    });
    assert.deepEqual(res, {
        airtel : 'airtel2'
    });
  })

  it("deleteMatchingProperties : should delete all the properties recursively from target which are present in source",()=>{

    let target = {
        A : {
            X : 1 , Y :2
        },
        B : {
            X :1
        },
        C : {
            X:1
        }
    }

    let source ={
        A : {
            X:1
        },
        B : {
            X:1
        }
    }

    let expResult = {
        A : {
            Y :2
        },
        C :{
            X:1
        }
    }
    let res = DigitalReminderConfigLib.deleteMatchingProperties(
      source , target
    );
    assert.deepEqual(target, expResult)

    source = {
      x : {
        y : [1, 5, 7]
      }, 
      a : {
        b : [10, 30, 70]
      }
    }

    target = {
      x : {
        y : [2, 4, 5, 6, 7]
      },
      a : {
        b : [20, 30, 50, 60, 70]
      }
    }

    expResult = {
      x : {
        y : [2, 4, 6]
      },
      a : {
        b : [20, 50, 60]
      }
    }

    res = DigitalReminderConfigLib.deleteMatchingProperties(
      source , target
    );
    assert.deepEqual(target, expResult)
  })

    it("getParsedConfig : delete from central config if status 0  in db: OPERATOR_TEMPLATE_MAPPING",async function(){
        let c  = {}
        stub.yields(null , otm)

        DigitalReminderConfigLib.getParsedConfig(function(error , data){
        })
        let dbTemp = JSON.parse(JSON.stringify(otm))
        await delay(100)
        
        /** Making status of both the templates 0 */
        dbTemp[0].status=0;
        dbTemp[1].status=0;
        stub.yields(null , dbTemp)
        let completed = 0;
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
            console.log(DigitalReminderConfigLib.config['OPERATOR_TEMPLATE_MAPPING'])
            assert(DigitalReminderConfigLib.config['OPERATOR_TEMPLATE_MAPPING']['Operator2']!=null,"Dont delete Operator2")
            assert(DigitalReminderConfigLib.config['OPERATOR_TEMPLATE_MAPPING']['Operator1']==null,"Delete")
            completed =1 ;
        })

        assert(completed == 1)
        await delay(100)
    })

    it("getParsedConfig : delete from central config if deleted from DB : OPERATOR_TEMPLATE_MAPPING",async function(){
        let c  = {}
        stub.yields(null , otm)
    
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
        })
        let dbTemp = JSON.parse(JSON.stringify(otm))

        await delay(100)
       
        /** Making status of only 1 the templates 0 */
        dbTemp[0].status=0;
        // db[1].status=0;
        stub.yields(null , dbTemp)
        let completed = 0;
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
            console.log(DigitalReminderConfigLib.config['OPERATOR_TEMPLATE_MAPPING'])
            assert(DigitalReminderConfigLib.config['OPERATOR_TEMPLATE_MAPPING']['Operator1']["BILLGEN_EMAIL"]!=null,"Dont delete Operator2")
            assert(DigitalReminderConfigLib.config['OPERATOR_TEMPLATE_MAPPING']['Operator1']["BILLGEN_CHAT"]==null,"Delete")
            completed =1 ;
        })

        assert(completed == 1)
        await delay(100)
    })

    it("getParsedConfig : delete from central config if status 0  in db: OPERATOR_TABLE_REGISTRY",async function(){
        let c  = {}
        stub.yields(null , otr)
    
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
            
        })
        let dbTemp = JSON.parse(JSON.stringify(otr))
        await delay(100)
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['airtel']!=null,"Dont delete Operator2")
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['jio']!=null,"Delete")
       
        /** Making status of  one of the operator 0 -airtel */
        dbTemp[0].status=0;
        stub.yields(null , dbTemp)
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
        })
        await delay(100)
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['airtel']==null,"Dont delete Operator2")
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['jio']!=null,"Delete")
    })

    it("getParsedConfig : delete from central config if status 0  in db: OPERATOR_TABLE_REGISTRY && PREPAID_TABLE_REGISTRY",async function(){
        let c  = {}
        stub.yields(null , otr)
    
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
            
        })
        let dbTemp = JSON.parse(JSON.stringify(otr))
        await delay(100)
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['airtel']!=null,"Dont delete Operator2")
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['jio']!=null,"Delete")
       
        /** Making status of  one of the operator 0 -airtel */
        dbTemp[0].status=0;
        stub.yields(null , [...dbTemp ,{...dbTemp[0], name :"PREPAID_TABLE_REGISTRY"},,{...dbTemp[1], name :"PREPAID_TABLE_REGISTRY"}])
        DigitalReminderConfigLib.getParsedConfig(function(error , data){
        })
        await delay(100)
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['airtel']==null,"Dont delete Operator2")
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['jio']!=null,"Delete")
       
    })

    it("parseBasedOnConfig : convert the parsed config into proper form and add it to existing config",async function(){
        
        let res =DigitalReminderConfigLib.parseBasedOnConfig({
            'RECENT_BILL_CONFIG':{
                airtel: {
                    firstBillDelay: 16,
                  },
                  COMMON: {
                    EXCLUDE_CHANNEL_ID: 1,
                  },
            },
            'OPERATOR_TABLE_REGISTRY':{},
            'PREPAID_TABLE_REGISTRY':{},
            'OPERATOR_TEMPLATE_MAPPING':{},
            'OPERATOR_NOT_IN_USE_CONFIG':{},
            'SUBSCRIBER_CONFIG':{},
            'PUBLISHER_CONFIG':{},
            'NOTIFICATION':{},
            'OPERATOR_GATEWAY_REGISTRY':{},
            'AIRTEL_PUBLISHER_CONFIG':{}

        })

        assert.containsAllDeepKeys(DigitalReminderConfigLib.config['RECENT_BILL_CONFIG'],{
            OPERATORS: {
              airtel: {
                firstBillDelay: 16,
              },
            },
            COMMON: {
              EXCLUDE_CHANNEL_ID: 1,
            },
          })

        
       
    })

    it("parseBasedOnConfig : convert the parsed config into proper form and delete it from existing config",async function(){
        
        let res =DigitalReminderConfigLib.parseBasedOnConfig({
            'RECENT_BILL_CONFIG':{
                airtel: {
                    firstBillDelay: 16,
                  },
                  COMMON: {
                    EXCLUDE_CHANNEL_ID: 1,
                  },
            },
            'OPERATOR_TABLE_REGISTRY':{
                airtel : {
                    TABLE_NAME : "bills_airtel"
                }
            },
            'PREPAID_TABLE_REGISTRY':{},
            'OPERATOR_TEMPLATE_MAPPING':{},
            'OPERATOR_NOT_IN_USE_CONFIG':{},
            'SUBSCRIBER_CONFIG':{},
            'PUBLISHER_CONFIG':{},
            'NOTIFICATION':{},
            'OPERATOR_GATEWAY_REGISTRY':{},
            'AIRTEL_PUBLISHER_CONFIG':{}

        },true)
       
        assert(DigitalReminderConfigLib.config['OPERATOR_TABLE_REGISTRY']['airtel'] == null,"Delete airtel from current config")

        
       
    })

})
