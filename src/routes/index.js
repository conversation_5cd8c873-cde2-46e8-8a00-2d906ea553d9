/*
    Router class to define actions against urls
 */
import { body, header, query, validationResult} from 'express-validator';
import _ from 'lodash'
export default class {

    constructor(app, { controller }) {
        this.app = app
        this.controller = controller
        this.bindRoutes = this.bindRoutes.bind(this);
    }

    bindRoutes() {
        let self = this;

        self.app.get('/_status', function (req, res) {
            res.json({
                program: "digital-reminder",
                version: "1.0.0",
                timestamp: new Date().getTime(),
                status: "success",
                code: 200,
                message: "OK",
                dependentservices: {},
                data: {
                    message: "The service is healthy"
                }
            })
        });

        self.app.get('/v1/getBill', function (req, res) {
            self.controller.getBill(req, res);
        });

        self.app.get('/v2/getBill', function (req, res) {
            self.controller.getBillV2(req, res);
        });

        self.app.get('/v1/userConsentStatus',
            header('X-USER-ID').notEmpty(),
            function (req, res) {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('GET /v1/userConsentStatus',`Parameter validation failure with error:${errors.array() && JSON.stringify(errors.array())}`);
                    let result = {
                        response : null,
                        statusInfo : {
                            status : "FAILURE",
                            statusMessage : "Invalid Request Header/Parameters"
                        }
                    };
                    return res.status(422).json({ "status": 422, "data": result });
                }
                self.controller.getUserConsentStatus(req, res);
        });

        self.app.put('/v1/userConsentStatus', 
            header('X-USER-ID').notEmpty(),
            header('deviceId').optional(),
            header('appVersion').optional(),
            header('clientId').optional(),
            body('preferenceKey').notEmpty(),
            body('preferenceValue').notEmpty().isIn([0,1]),
            function (req, res) {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('PUT /v1/userConsentStatus',`Parameter validation failure for ${req && req.body && JSON.stringify(req.body)} with error:${errors.array() && JSON.stringify(errors.array())} with customer_id:${req.header('X-USER-ID')}`);
                    let result = {
                        response : null,
                        statusInfo : {
                            status : "FAILURE",
                            statusMessage : "Invalid Request Header/Parameters"
                        }
                    };
                    return res.status(422).json({ "status": 422, "data": result });
                }
                self.controller.updateUserConsentStatus(req, res);
        });

        self.app.post('/v1/getMultipleBill', function (req, res) {
            self.controller.getMultipleBill(req, res);
        })

        self.app.post('/v1/createBill', function (req, res) {
            self.controller.createBill(req, res);
        });

        self.app.post('/v1/createMultipleBill', function (req, res) {
            self.controller.createMultipleBill(req, res);
        });

        self.app.post('/v2/createMultipleBill', function (req, res) {
            self.controller.createMultipleBillv2(req, res);
        });
        self.app.put('/v1/updateBill', function (req, res) {
            self.controller.updateBill(req, res);
        });

        //update bill from UPMS to digital reminder
        self.app.post('/v2/updateBill', function (req, res) {
            self.controller.updateBillV2(req, res);
        });

        self.app.get('/v1/getRealTimeBill', function (req, res) {
            self.controller.getRealTimeBill(req, res);
        });

        self.app.post('/v1/notify',
        body('next_schedules')
            .optional()
            .isArray({ min: 1 })
            .withMessage('Minimum 1 date should be present')
            .exists({ checkNull: true, checkFalsy: true })
            .withMessage('Invalid Schedules'),
        (req, res) => {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                const errorsList = errors.array()[0];
                const error = `${errorsList.param}: ${errorsList.msg}`;
                return res.status(200).json(
                    {
                        status: '204',
                        error
                    }
                );
            }
            self.controller.createNotification(req, res);
        });


        self.app.post('/v2/notify',
        body('next_schedules')
        .optional()
        .isArray({ min: 1 })
        .withMessage('Minimum 1 date should be present')
        .exists({ checkNull: true, checkFalsy: true })
        .withMessage('Invalid Schedules'),
        (req, res) => {
            const errors = validationResult(req);
            req.body.version = 'v2';
            if (!errors.isEmpty()) {
                const errorsList = errors.array()[0];
                const error = `${errorsList.param}: ${errorsList.msg}`;
                return res.status(200).json(
                    {
                        status: '204',
                        error
                    }
                );
            }
            self.controller.createNotification(req, res);
        });
        
        self.app.put('/v1/bill/notificationStatus', function (req, res) {
            self.controller.updateNotificationStatus(req, res);
        });

        self.app.put('/v1/bill-nonPaytm/notificationStatus',
            header('X-USER-ID').notEmpty().isNumeric(),
          //  body('operator').notEmpty().toLowerCase().exists({ checkFalsy: true }),
            body('productId').notEmpty(),
            body('rechargeNumber').notEmpty(),            
            body('notificationStatus').notEmpty().isIn([0]),
            (req, res) => {
                const errors = validationResult(req);
                    if (!errors.isEmpty()) {
                        self.controller.L.error('/v1/bill-nonPaytm/notificationStatus',`Parameter validation failure for ${JSON.stringify(req.body)} with error:${JSON.stringify(errors.array())}`);
                        return res.status(422).json({ status: 422, errors: errors.array() });
                    }
                self.controller.updateNonPaytmNotificationStatusV1(req, res)
            })

        self.app.get('/v1/bill-nonPaytm/getBill',
            header('X-USER-ID').notEmpty().isNumeric(),
            query('service').notEmpty().trim().escape().toLowerCase(),
            (req, res) => {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('GET /v1/bill-nonPaytm/getBill',`Parameter validation failure with error:${errors.array() && JSON.stringify(errors.array())}`);
                    return res.status(422).json({ "status": 422, error_message: "customerId and service are mandatory fields", 
                                                    errors: errors.array() });
                }
                self.controller.getNonPaytmBills(req, res);
            })

        self.app.get('/v2/bill-nonPaytm/getBill',
            header('X-USER-ID').notEmpty().isNumeric(),
            query('service').notEmpty().trim().escape().toLowerCase(),
            (req, res) => {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('GET /v2/bill-nonPaytm/getBill',`Parameter validation failure with error:${errors.array() && JSON.stringify(errors.array())}`);
                    return res.status(422).json({ "status": 422, error_message: "customerId and service are mandatory fields", 
                                                    errors: errors.array() });
                }
                self.controller.getNonPaytmBillsMultipleServices(req, res);
            })

        self.app.put('/v3/bill/notificationStatus',
            header('X-USER-ID').notEmpty(),
            body('operator').notEmpty().toLowerCase(),
            body('productId').notEmpty(),
            body('rechargeNumber').notEmpty(),            
            body('notificationStatus').notEmpty().isIn([0,1]),
            body('referenceId').optional(),            
            function (req, res) {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('/v3/bill/notificationStatus',`Parameter validation failure for ${JSON.stringify(req.body)} with error:${JSON.stringify(errors.array())}`);
                    return res.status(422).json({ status: 422, errors: errors.array() });
                }
                self.controller.updateNotificationStatusV3(req, res);
            }
        );

        self.app.put('/v2/bill/notificationStatus',
            header('X-USER-ID').notEmpty(),
            body('operator').notEmpty().toLowerCase(),
            body('productId').notEmpty(),
            body('rechargeNumber').notEmpty(),
            body('amount').notEmpty().isFloat(),
            body('notificationStatus').notEmpty().isIn([0,2]),
            body('referenceId').optional(),
            body('triggerChannel').optional(),
            function (req, res) {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('/v2/bill/notificationStatus',`Parameter validation failure for ${JSON.stringify( req.body)} with error:${JSON.stringify(errors.array())}`);
                    return res.status(422).json({ status: 422, errors: errors.array() });
                }
                self.controller.updateNotificationStatusV2(req, res);
            }
        );

        self.app.get('/v1/bill/deleteNotification',
            header('X-USER-ID').notEmpty(),
            query('operator').notEmpty().toLowerCase(),
            query('productId').notEmpty(),
            query('rechargeNumber').notEmpty(),
            query('amount').notEmpty().isFloat(),
            query('notificationStatus').notEmpty().isIn([0,2]),
            query('referenceId').optional(),
            query('triggerChannel').optional(),
            function (req, res) {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('/v1/bill/deleteNotification',`Parameter validation failure for with error:${JSON.stringify(errors.array())}`);
                    return res.status(422).json({ status: 422, errors: errors.array() });
                }

                let qParams = _.get(req, 'query', {});
                _.set(req, 'body', qParams);
                self.controller.updateNotificationStatusV2(req, res);
            }
        );
        
        self.app.post('/v1/bill/deleteNotification',
            header('X-USER-ID').notEmpty(),
            body('operator').notEmpty().toLowerCase(),
            body('productId').notEmpty(),
            body('rechargeNumber').notEmpty(),
            body('amount').notEmpty().isFloat(),
            body('notificationStatus').notEmpty().isIn([0,2]),
            body('referenceId').optional(),
            body('triggerChannel').optional(),
            function (req, res) {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    self.controller.L.error('/v1/bill/deleteNotification',`Parameter validation failure for ${JSON.stringify( req.body)} with error:${JSON.stringify(errors.array())}`);
                    return res.status(422).json({ status: 422, errors: errors.array() });
                }
                self.controller.updateNotificationStatusV2(req, res);
            }
        );


        self.app.get('/v1/operatorList', function (req, res) {
            self.controller.getOperatorList(req, res);
        });

        self.app.get('/v1/notificationRecords', function (req, res) {
            self.controller.getNotificationRecords(req, res);
        });

        self.app.post('/v1/updateBillStatus', function (req, res) {
            self.controller.updateBillStatus(req, res);
        });

        self.app.post('/v1/bill/updateAmount', function (req, res) {
            self.controller.updateAmount(req, res);
        });

        self.app.post('/v1/deletePlan', function (req, res) {
            self.controller.deletePlan(req, res);
        });

        self.app.post('/v1/deleteRecord', function (req, res) {
            self.controller.deleteRecord(req, res);
        });

        self.app.post('/tinyurl/create', function (req, res) {
            self.controller.createUrl(req, res);
        });

        self.app.post('/userDetails/getData', function (req, res) {
            self.controller.getCustomerData(req, res);
        });

        self.app.post('/merchant/ordersummaryquery', function (req, res) {
            self.controller.getOrderSummarydata(req, res);
        });

        self.app.post('/v3/notification/callback', function(req, res) {

            self.controller.notificationCallback(req, res);
        });

        /**
         * API for mark as paid
         * Method PUT
         */
        self.app.put('/v1/markAsPaid',
            header('content-type').matches('application/json').bail().withMessage('Invalid Body. Content type should be JSON'),
            body('rechargeNumber').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid Recharge Number'),
            body('operator').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid operator'),
            body('customerID').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid Customer ID'),
            body('paytype').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid paytype'),
            body('service').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid service'),
            body('productID').notEmpty(),
            // body('planBucket')
            //     .trim().escape()
            //     .if((value, { req }) => req.body.paytype.toLowerCase() === 'prepaid')
            //     .exists({ checkNull: true, checkFalsy: true })
            //     .withMessage('Invalid planBucket'),
            (req, res) => {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {
                    return res.status(422).json(
                        {
                            statusCode: 'ERR02',
                            data: {
                                rows_updated: 0,
                                errors: errors.array()
                            }
                        }
                    );
                }
                self.controller.markAsPaid(req.body, res);
            }
        );

        self.app.post('/v1/setRemindLater',
            header('content-type').matches('application/json').bail().withMessage('Invalid Body. Content type should be JSON'),
            body('customer_id').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid Customer ID'),
            body('productId').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid Product ID'),
            body('service').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid service'),
            body('operator').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid operator'),
            body('recharge_number').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid Recharge Number'),
            body('remindLaterOffset').isNumeric().withMessage('Invalid remindLaterOffset'),
            body('remindLaterDate').optional({ checkFalsy: true }).isDate().withMessage('Invalid remindLaterDate'),
            (req, res) => {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {

                    const errorMessage = JSON.stringify(errors.array());
                    return res.status(422).json({ "status": "failure", "message": errorMessage}); 
                }
                self.controller.setRemindLater(req.body, res);
            }
        );


        self.app.post('/v1/testCustomNotification',
            header('content-type').matches('application/json').bail().withMessage('Invalid Body. Content type should be JSON'),
            body('test_customer_id').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid Customer ID'),
            body('campaign_name').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid campaign name'),
            body('service').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid service'),
            body('template_id').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid templateId'),
            body('template_name').trim().escape().exists({ checkNull: true, checkFalsy: true }).withMessage('Invalid template name'),

            (req, res) => {
                const errors = validationResult(req);
                if (!errors.isEmpty()) {

                    const errorMessage = JSON.stringify(errors.array());
                    return res.status(422).json({ "status": "failure", "message": errorMessage });
                }
                self.controller.testCustomNotification(req.body, res);
            }
        );
        self.app.get('/fetchUPMStoken', function (req, res) {
            self.controller.fetchUPMStoken(req, res);
        });
    }
}
