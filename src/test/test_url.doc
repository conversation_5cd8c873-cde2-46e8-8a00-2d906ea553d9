-POST 
 curl -XPOST "127.0.0.1:7000/v1/createBill" -d '{"customerId":123456, "rechargeNumber":"7799168039", "productId":11112, "operator":"bses yamuna","amount":100}' -H 'content-type: application/json'
 
 curl -XPOST "127.0.0.1:7000/v1/createMultipleBill" -d '[{"customerId":12345, "rechargeNumber":"7799168039", "productId":11111, "operator":"bses yamuna","amount":100, "user_data":"{\"recharge_number\":\"7799168039\",\"recharge_number_2\":\"1234565\"}"}]' -H 'content-type: application/json'
 
 curl -XPOST "127.0.0.1:7000/v1/createMultipleBill" -d '[{"customerId":12345, "rechargeNumber":"7799168039", "productId":11111, "operator":"bses yamuna","amount":100, "user_data":"{\"recharge_number\":\"7799168039\",\"recharge_number_2\":\"12345\"}"}]' -H 'content-type: application/json'
 
 curl -XPOST "127.0.0.1:7000/v1/createBill" -d '{"customerId":123456, "rechargeNumber":"7799168039", "productId":11111, "operator":"bses yamuna","amount":100, "user_data":"{\"recharge_number\":\"7799168039\",\"recharge_number_2\":\"12345444\"}"}' -H 'content-type: application/json'
 

-GET
 curl -XGET "127.0.0.1:7000/v1/getBill?customerId=123456&rechargeNumber=7799168039&productId=11111&operator=bses%20yamuna"
 curl -XGET "127.0.0.1:7000/v1/getBill?customerId=123456&rechargeNumber=7799168039&productId=11112&operator=bses%20yamuna"

**********************************************************************************************
 curl -X POST \
  http://127.0.0.1:7000/v2/createMultipleBill \
  -H 'Cache-Control: no-cache' \
  -H 'Content-Type: application/json' \
  -H 'Postman-Token: 94f2de4d-3690-4529-84ea-718467226e74' \
  -d '{"data" : [
  {
    "customerId": 12345,
    "rechargeNumber": "7799168039",
    "productId": 11111,
    "operator": "bses yamuna1",
    "amount": 100,
    "user_data": "{\"recharge_number\":\"7799168039\",\"recharge_number_2\":\"1234565\"}"
  },
  {
    "customerId": 12345,
    "rechargeNumber": "7799168039",
    "productId": 11111,
    "operator": "bses yamuna",
    "amount": 100,
    "user_data": "{\"recharge_number\":\"7799168039\",\"recharge_number_2\":\"1234565\"}"
  },
  {
    "customerId": 12345,
    "rechargeNumber": "7799168039",
    "productId": 11111,
    "operator": "bses yamuna",
    "amount": 100,
    "user_data": "{\"recharge_number\":\"7799168039\",\"recharge_number_2\":\"1234565\"}"
  }
],

"callback": "http://localhost:5555/multipleBillCallbackUrl"
}'
***********************************************************************************************