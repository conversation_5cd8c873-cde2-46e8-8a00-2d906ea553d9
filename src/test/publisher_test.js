import PUBLISHER from '../services/publisher'
import L from 'lgr'
import config from '../config'
import SQL<PERSON>AP from 'sqlwrap'
import <PERSON>LAST<PERSON><PERSON>ARCH from 'elasticsearch'
import _ from 'lodash'
import LatencyProvider from '../services/latencyProvider'
import MOMENT from 'moment'
import AS<PERSON><PERSON>     from 'async'

let dbInstance = new SQLWRAP(config.SQLWRAP);
let esInstance = new ELASTICSEARCH.Client({
    apiVersion: '0.90',
    host: config.ELASTICSEARCH.ES_URL,
    log: 'error'
});

let options = {
    L,
    config,
    dbInstance,
    esInstance
};




(function () {
    if (require.main === module) {
        L.log('**************************** Publisher Test Started ****************************');
        L.setLevel('verbose');
        var publisher = new PUBLISHER({
            ...options,
            tableName: 'bills_bses',
            latencyProvider: new LatencyProvider()
        });

        let params = {
            statuses: [
                _.get(self.config, 'COMMON.bills_status.DISABLED', 7),
                _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13)
            ],
            retryCount: 3,
            nextBillFetchDate: MOMENT().format('YYYY-MM-DD HH:mm:ss')
        };

        ASYNC.waterfall([

            //fetch fresh records from Database
            next => {
                publisher.bills.fetchFreshRecords(publisher.tableName, publisher.dbBatchSize, params, next);
            },

            //Publish and update the records 
            (records, next) => {
                _.each(records, function (record) {
                    let boolValue = publisher.checkIfRecordHasToBePublished(record);
                    L.log('Record', record.id, record.payment_date, boolValue);
                })
                next();
            }
        ],
            (err, count) => {
                process.exit(0);
            }
        );

    }
}());