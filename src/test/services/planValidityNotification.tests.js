/*
  jshint 
    esversion: 8
 */

'use strict';


import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinon from 'sinon';
import sinonChai from "sinon-chai";

import chaiAsPromised from "chai-as-promised";
import proxyquire from "proxyquire";

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;


describe("Module: services/planValidityNotification.js", function () {
    let planValidityNotification;

    before(function () {
        let PlanValidityNotification = proxyquire.noCallThru()("../../services/planValiditySubscriber.js", {
            '../models/planValidity': class c { },
            '../lib': {
                TinyUrl: () => class c { }
            },
            '../models/catalogVerticalRecharge': class c { },
            '../lib/startup': {},
            '../lib/oauth': class c { }
        });

        planValidityNotification = new PlanValidityNotification.default({
            L: {},
            infraUtils: {
                kafka: { producer: () => { } }
            },
            config: {
                TOPICS: {
                    PLAN_VALIDITY_NOTIFICATION: {}
                }
            }
        });
    });

    beforeEach(function () {

    });

    afterEach(function () {
        sinon.restore();
    });

    it("getUtmParam", () => {
        let utmparam = "utm param";
        expect(planValidityNotification.getUtmParam(utmparam)).to.be.equal('utm%20param');
    });

});
