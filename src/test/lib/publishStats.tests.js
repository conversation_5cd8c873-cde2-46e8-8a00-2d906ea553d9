/*
  jshint 
    esversion: 8
 */

'use strict';


import { describe, it, before, beforeEach, afterEach } from 'mocha';
import chai from "chai";
import sinon from 'sinon';
import sinonChai from "sinon-chai";

import chaiAsPromised from "chai-as-promised";
import proxyquire  from "proxyquire";

chai.use(chaiAsPromised);
chai.use(sinonChai);

const { expect } = chai;


describe("Module : Lib PublishStats", function() {
  
});