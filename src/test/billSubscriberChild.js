import RQ from 'rqueue'
import <PERSON><PERSON><PERSON> from 'async'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator'
import MOMENT from 'moment'
import REQUEST from 'request'
import _ from 'lodash'

import BILLS from '../models/bills'

import utility from '../lib'
import BILLSUBSCRIBER from '../services/billSubscriber'

var events = require('events');

let L = null;
var numberOfRecordsPublished = 0;
var numberOfRecordsAckd = 0;


class BS extends BILLSUBSCRIBER {
    constructor(options) {
        super(options);
        L = options.L;
        this.bills = {
            updateBill(cb, tableName, params) {
                L.log('Data to update in bills:::', tableName, JSON.stringify(params));
                cb();
            }
        }
    }

    _configureSubscriber() {
        let self = this;
        self.reminderSubscriber = new events.EventEmitter();
        L.log("_configureSubscriber", "Subscriber Configured");
        self.reminderSubscriber.on('message', function(data) {
            numberOfRecordsPublished++;
            self.processMessage(data);
        });
    }
    // _dumpInSQL (data, cb) {
    //     console.log('IN dump in sql')
    //     var self =  this;
    //     self.bills.updateBill(cb);
    // }
    _ackMessage(message) {
        let self = this;
        try {
            numberOfRecordsAckd++;
            console.log('Message acknowledged.')
        } catch (err) {
            L.critical('billSubscriber::_ackMessage', 'Error while ack message.', err);
        }
    }
    _finish() {
        console.log('Finished:: Published: ' + numberOfRecordsPublished + ' Ackd: ' + numberOfRecordsAckd);
    }
}

export default BS