import moment from 'moment';
import _ from 'lodash';
import validator from 'validator';
import AWS from 'aws-sdk';
import { parseStream } from 'fast-csv';
import utility from '../lib';
import nonPaytmBills from '../models/nonPaytmBills';

let totalRow = 0;
let batchNumber = 0;

class FallbackCustomerIdIngester {
    constructor(options) {
        this.L = options.L;
        this.nonPaytmBillsModel = new nonPaytmBills(options);
        this.config = options.config;
        this.s3bucketName = "digital-reminder";
        this.folderPath = _.get(options.config, ['DYNAMIC_CONFIG', 'FallbackCustomerIdIngester', 's3path', 'value'], 'digital-reminder/FallbackCustomerId/');
        this.streamHighWaterMark = _.get(options.config, ['DYNAMIC_CONFIG', 'FallbackCustomerIdIngester', 'highWaterMark', 'value'], 16384); // 1024(1kb) * 16 = 16 kb (default highWaterMark)
        this.batchRecords = [];
    }

    async getCSVDataFromAWSS3Bucket(cb){
        const self = this;
        const path = self.folderPath;
        const fileName = `${moment().format('DD-MM-YYYY')}.csv`;
        const fileKey = path + fileName;
        const params = {
            Bucket: self.s3bucketName,
            Key: fileKey,
        };
        const s3 = new AWS.S3();
        
        try {
            const s3Stream = s3.getObject(params).createReadStream();
            const csvStream = parseStream(s3Stream, {
                headers:headers => headers.map(h => h.trim()), 
                skipRows:0, 
                trim:true,
                highWaterMark:self.streamHighWaterMark
            });

            csvStream
                .on('data', async (data) => {
                    totalRow++;
                    this.batchRecords.push(data);

                    if(this.batchRecords.length == 1000){
                        csvStream.pause();
                        self.L.log("Processing batch of length ", this.batchRecords.length);
                        try {
                            await self.processBatchOfFallback(this.batchRecords);
                        } catch(err){
                            self.L.error("Error while processing batch",err);
                        }
                        self.L.log("Finished processing batch ");
                        this.batchRecords = [];
                        csvStream.resume();
                        batchNumber++;
                    }
                })
                .on('end', async (rowCount) => {
                    if (this.batchRecords.length > 0) {
                        self.L.log("Processing last batch of length ", this.batchRecords.length);
                        try {
                            await self.processBatchOfFallback(this.batchRecords);
                        } catch(err){
                            self.L.error("Error while processing batch",err);
                        }
                    }
                    setTimeout(() => {
                        self.L.log(`getCSVDataFromAWSS3Bucket :: ${rowCount} Data rows processed successfully !!`);
                        return cb();
                    }, 1000);
                })
                .on('error', (error) => {
                    return cb(error);
                });
        } catch (err) {
            return cb(err);
        }
    }
    
    async processBatchOfFallback(batchRecords) {
        let self = this;
        for (let i = 0; i < batchRecords.length; i++) {
            try {
                self.L.log(`processBatchOfFallback :: processing for customer_id: ${batchRecords[i].customer_id}, row no: ${(batchNumber*1000)+i}`);
                await self.processRecord(batchRecords[i]);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:FALLBACK_CUSTOMER_ID_INGESTER', 'STATUS:READ_CSV_ROW']);
            }
            catch(err) {
                self.L.error(`processBatchOfFallback :: Error in processing record customer_id: ${batchRecords[i].customer_id}, row no: ${(batchNumber*1000)+i} : `, err);
            }
        }
    }


    async executeFlow(cb) {
        const self = this;
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:FALLBACK_CUSTOMER_ID_INGESTER', 'STATUS:STARTED']);
        await self.getCSVDataFromAWSS3Bucket((err) => {
            if (err) {
                self.L.error('executeFlow :: Error ', err);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:FALLBACK_CUSTOMER_ID_INGESTER', 'STATUS:RECORDS_PROCESSING_FAILED']);
            } else {
                self.L.log(`executeFlow::completed, totalRowCount : ${totalRow}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:FALLBACK_CUSTOMER_ID_INGESTER', 'STATUS:COMPLETED']);
            }
            return cb(err);
        });
    }

    processRecord(data) {
        const self = this;
        const params = {
            'customerId': data['customer_id'],
        };

        if (!params.customerId) {
            throw 'Received customerId as null from CSV File';
        }

        params.customerId = typeof params.customerId === 'string' ? validator.toInt(params.customerId) : params.customerId;
        return self.nonPaytmBillsModel.insertFallbackCustomerId(params);
    }

}

export default FallbackCustomerIdIngester;