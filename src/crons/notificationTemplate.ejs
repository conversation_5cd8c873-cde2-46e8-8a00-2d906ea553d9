<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>
<body bgcolor="#FFFFFF" topmargin="0" leftmargin="0" marginheight="0"
  marginwidth="0">
    <% if (data.length) { %>
        <table style="border:1px solid black;border-collapse:collapse;">
            <tbody>
                <tr>
                    <th style="border:1px solid;">Operator</th>
                    <th style="border:1px solid;">Service</th>
                    <th style="border:1px solid;">Paytype</th>
                    <th style="border:1px solid;">Type</th>
                    <th style="border:1px solid;">Pending</th>
                    <th style="border:1px solid;">Sent</th>
                    <th style="border:1px solid;">Error</th>
                </tr>
                <% data.forEach(function(record) { %>
                    <tr>
                        <td style="border:1px solid;"><%= record.operator %></td>
                        <td style="border:1px solid;"><%= record.service %></td>
                        <td style="border:1px solid;"><%= record.paytype %></td>
                        <td style="border:1px solid;"><%= record.type %></td>
                        <td style="border:1px solid;"><%= record.pending %></td>
                        <td style="border:1px solid;"><%= record.sent %></td>
                        <td style="border:1px solid;"><%= record.error %></td>
                    </tr>
                <% }) %>
            </tbody>
        </table>
    <% } %>
</body>
</html>
