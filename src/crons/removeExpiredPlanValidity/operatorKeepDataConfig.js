let operatorConfig = [
    {
        operators: [],
        keepDataUpToDays: 10,
    },
];

module.exports = operatorConfig;


/*

FYI , logic is here

1. we sort operatorKeepData array in decreasing order of keepDataUpToDays
2. we take a array excludedOperators which is initially as []
3. we are processing first record of operatorKeepData array with excludedOperators([])
4. after processing we concat excludedOperators with record.operators,

By this we ignore current-record operator-recharges in next iteration which has less value of keepDataUpToDays.
We need to keep our config as clean, in place of lot of same word iteration.

 operatorConfig = [{
        operators: ["vodafone", "idea"],
        keepDataUpToDays: 10,
    }, {
        operators: ["airtel"],
        keepDataUpToDays: 5,
    }, {
        operators: [],
        keepDataUpToDays: 3,
 },]
Through the above configuration, we will only keep 10 days of records for "Vodafone" and "Idea", 5 days for Airtel and 3 days for all other operators,

srcipt running date: 24 july 2020, all following query run in sync way

for plan_validity table

-----   queries for first operator config    --------------------

select id from plan_validy where validity_expiry_date < "2020-07-14 11:59:00" and id > 0 order by id limit 0,1000

make ; separate query of 100 ids for deletion
delete from plan_validy where id=348987;  delete from plan_validy where id=348989; ... upto 100 queries
run this delete query, this is for using single connection.

update id with records[n-1].id, now records[n-1].id is 3343232
now going for fetch records for condition (validity_expiry_date < "2020-07-14 11:59:00" and id > 3343232) 
...
..

now id will be 100023

cotinue fetch for validity_expiry_date < "2020-07-14 11:59:00"

------ queries for 2nd operator config  ---------------------

select id from plan_validy where validity_expiry_date < "2020-07-19 11:59:00" and id > 100023 and operator not in ("vodafone", "idea") order by id limit 0,1000

delete from plan_validy where id=3489871  -- same as before , make chunks of 100 and run delete query

fetch records until records availble for validity_expiry_date <  "2020-07-19 11:59:00" and id > {last_id_value_in_last_fetch_records)} and operator not in ("vodafone", "idea") 

------ queries for 3nd operator config  --------------------
select id from plan_validy where validity_expiry_date < "2020-07-21 11:59:00" and id > 2342443  and operator not in ("vodafone", "idea", "airtel") order by id limit 0,1000

delete from plan_validy where id=2342449  -- same as before , make chunks of 100 and run delete query

fetch records until records availble for where condition (validity_expiry_date < "2020-07-21 11:59:00" and id > {last_id_value_in_last_fetch_records} and operator not in ("vodafone", "idea", "airtel")) 

----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
same operation will happen in same way for 2nd table  plan_validity_new


FYI: there is no combine index, index only on validity_expiry_date key
*/
