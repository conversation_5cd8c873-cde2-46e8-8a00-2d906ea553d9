'use strict';

import StatsD from '../../lib/publishStats';

class Helper {
    constructor(options) {
        this.dbInstance = options.dbInstance
    }
    runQuery(poolName, query, params) {
        return new Promise((resolve, reject) => {
            this.dbInstance.exec((error, results) => {

                if (error) {
                    this.publishStats();

                    setTimeout(() => {
                        reject(error);
                    }, 1000);

                } else {
                    resolve(results);
                }
            }, poolName, query, params);
        });
    }

    getDeleteQuery(ids, table) {
        let query = `delete from ${table} where id=`;

        let finalQuery = '';

        for (let { id } of ids) {
            finalQuery += query + id + ";";
        }

        return finalQuery;
    }

    evaluateFetchRecordQuery(time, excludedOperators, self) {
        let query = `select id from ${self.table} where validity_expiry_date < ? and id > ? and notification_status!=0`,
            params = [time, self.id];

        if (excludedOperators.length) {
            query += ` and operator not in (?)`;
            params.push(excludedOperators);
        }

        query += ` order by id limit ?`;
        params.push(self.dbFetchRecordsLimit);

        return {
            query,
            params
        };
    }

    publishStats() {
        StatsD.publishCounter(1, {
            REQUEST_TYPE: "REMOVE_EXPIRED_PLAN_VALIDITY",
            STATE: "QUERY_FAILED"
        });
    }
}

export default Helper;