import ASYNC from 'async'
import _ from 'lodash'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import OS from 'os'
import NotificationLibrary from '../lib/notification'
import utility from '../lib';

class AirtelPrepaidBillDuePublisher {
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.dbInstance = options.dbInstance;
        this.bills = new BILLS(options);
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.notificationLibrary = new NotificationLibrary(options);
        this.offsetIdMap = {};
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.billFetchIntervals = [-1, -2, -3, -4, -5, -6];
        this.dueDateDiffMap = {
            '2': [-3, -4, -5, -6],
            '3': [-2, -4, -5, -6],
            '4': [-2, -3, -4, -5],
            '5': [-1, -2, -3, -4],
            '6': [-1, -2, -3],
        },
        this.defaultDueDateMap = [-1,-2,-3];
       
        this.dateKeyArr = Object.keys(this.dueDateDiffMap);
       
    }

    cal_date_key_array(dueDate){

        let self =this;
        let dateKeyObject = {};
        self.dateKeyArr.forEach(function(dateDiff){
            dateKeyObject[dateDiff] = MOMENT(dueDate).startOf('day').subtract(dateDiff,'days').format('YYYY-MM-DD')
        })
        console.log(dateKeyObject);
        return dateKeyObject;
    }



    start() {
        let self = this;
        self._start(function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }

    _start(done) {
        let
            self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('_start', 'Going to initialize Kakfa Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('_start', 'Going to publish records for all operators');
                return self.processOperators(next);
            }
        ], function (error) {
            if (error) {
                self.L.error('_start', 'Error', error);
            }
            return done(error);
        });
    }

    /**
     * Kafka publisher
     */
    configureKafkaPublisher(done) {
        let self = this;

        self.kafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
        });
        self.kafkaPublisher.initProducer('high', function (error) {
            if (!error)
                self.L.log("notify :: configureKafkaPublisher", "publisher Configured");
            return done(error);
        });
    }

    processOperators(done) {
        let self = this;
        let table = 'bills_airtelprepaid';
    
        self.offsetIdMap[table] = {};
        /** Passing ALL in place of operator name as after IN-33661 the function would not be using operator to get Due dates*/
        self.billFetchIntervals.forEach((dueDateInterval) => {
            self.offsetIdMap[table][dueDateInterval] = 0; // start with 0 offset
        });
        self.L.log('processOperators', 'processing records for  table: ' + table);
        self.processBills(table, self.billFetchIntervals, () => {
            self.L.log('processOperators', 'Completed !!');
            return done();
        });
    }
    
    processBills(tableName, dueDateIntervals, done) {
        let self = this;
        ASYNC.forEachOfSeries(dueDateIntervals, (dueDateInterval, index , callback) => {
                self.L.log('processBills', 'processing records for the  table: ' + tableName + ' and dueDateInterval: '+  dueDateInterval);

                self.processBillsForEachDueDate(tableName, dueDateInterval, () => {
                    return callback();
                });     
            } 
            ,(err)=>{
                if(err) {
                    self.L.log('processBills', 'Error while processing records for the table: ' + tableName + ' and dueDateInterval: '+  dueDateInterval +' MSG:-' + err);
                }
                return done(err);
            })
    }

    processBillsForEachDueDate(tableName, dueDateInterval, done) {
        let self = this,
            batchSize = 100,
            offsetId = self.offsetIdMap[tableName][dueDateInterval],
            dueDate = MOMENT().add(dueDateInterval, 'day').format('YYYY-MM-DD');


        self.bills.getAirtelPrepaidBillsToNotify(function _doUntilNoMoreRecords(error, data) {
            if (!error && data && data.length > 0) {
                self.processBatch(data, tableName, dueDateInterval, dueDate, () => {
                    self.offsetIdMap[tableName][dueDateInterval] = data.length + self.offsetIdMap[tableName][dueDateInterval];

                    if (self.greyScaleEnv) {
                        self.L.log('processBillsForEachDueDate', `GREYSCALEMODE for tableName ${tableName}, Total processed records ${self.offsetIdMap[tableName][dueDateInterval]}`);
                        return done();
                    }
                    
                    self.L.log('processBillsForEachDueDate', `processing next batch for tableName ${tableName} and offset ${self.offsetIdMap[tableName][dueDateInterval]}`);
                    setTimeout(() => {
                        self.bills.getAirtelPrepaidBillsToNotify(_doUntilNoMoreRecords, tableName, batchSize, dueDate, self.offsetIdMap[tableName][dueDateInterval]);
                    }, 100);
                });
            } else {
                self.L.log('processBillsForEachDueDate', `No record found for tableName ${tableName}, Total processed records ${self.offsetIdMap[tableName][dueDateInterval]}`);
                done();
            }
        }, tableName, batchSize, dueDate, offsetId);
    }

    processBatch(records, tableName, dueDateInterval, dueDate, done) {
        let self = this,
            dateKeyObject = self.cal_date_key_array(dueDate)

        ASYNC.map(
            records,
            (record, next) => {
                let extra = record.extra ? JSON.parse(record.extra) : {};
                let dueDateList = null;
                let breakLoop = false;

                self.dateKeyArr.forEach(function(dateDiff){
                    if(!breakLoop && extra[dateKeyObject[dateDiff]] === false){
                        breakLoop = true;
                        console.log("inside dateArr loop" ,self.dueDateDiffMap[dateDiff], dueDateInterval, dateDiff);
                        if(self.dueDateDiffMap[dateDiff].indexOf(dueDateInterval) > -1 ) {
                            dueDateList = self.dueDateDiffMap[dateDiff];
                        }
                    }
                });
                if(breakLoop == false && self.defaultDueDateMap.indexOf(dueDateInterval) > -1){
                    dueDateList = self.defaultDueDateMap;
                }
                console.log(extra, dueDateInterval, dueDateList, breakLoop); 
                if (dueDateList && record && record['customer_id'] && record['notification_status'] == 1) {
                    if(dueDateList.indexOf(dueDateInterval) < (dueDateList.length -1 )){
                        return self.publishData(record, next);
                    }
                    self.bills.updateAirtelBills(next, tableName, record['id']);
                } else {
                    next();
                }
            },
            err => {
                done();
            }
        )
    }

   
    publishData(record, done) {

        let
            self = this;
        let
            payload = self.prepareDataToPublish(record),
            skipNotification = self.greyScaleEnv ? 1 : 0;
            _.set(payload, 'data.skipNotification', skipNotification);
        
        self.kafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', null),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {
                self.L.critical('publishData', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            } else {
                self.L.log('publishData', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', null),
                    'REQUEST_TYPE:DUEDATE_NOTIFICATION',
                    'OPERATOR:' + _.get(payload,'data.operator')
                ])
            }
            return done();
        }, [200, 800]);
    }

    prepareDataToPublish(record) {
        return {
            source: 'airtelBillFetch',
            notificationType: 'DUEDATE',
            data:
            {
                id: record.id,
                customerId: record.customer_id,
                rechargeNumber: record.recharge_number,
                productId: record.product_id,
                operator: 'airtel',
                amount: 79,  // amount =0 was creating validation faluire 
                dueDate: null,
                billFetchDate: null,
                nextBillFetchDate: null,
                gateway: null,
                paytype: 'prepaid',
                service: 'mobile',
                circle: null,
                customerMobile: record.customer_mobile,
                customerEmail: '',
                retryCount: 0,
                status: 4,
                notification_status: record.notification_status,
                old_product_id: record.product_id,
                current_retry_count: 0,
                sendNotification: true,
                templates: { PUSH: 7353, SMS: null, EMAIL: null, CHAT: null }
            }
        }
    }
}

export default AirtelPrepaidBillDuePublisher