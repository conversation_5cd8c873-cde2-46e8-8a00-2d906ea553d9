import startup from "../lib/startup";
import MOMENT from "moment";
import _, { reject } from "lodash";
import L from "lgr";
import fs from "fs";
import { parseStream } from "fast-csv";
import { each, eachLimit, eachSeries, parallel } from "async";
import AWSCsvIngester from "../lib/awscsvingester";
import utility from "../lib";
import OAuth from "../lib/oauth";

let serviceName = "CC_INGESTION";
let progressFilePath = `/var/log/digital-notification/progress-CC-${MOMENT().subtract(1, "day").format("MMYYYY")}.json`;
let progressTracker = {};
/** Maintain offset and processed files in a path */
class CCIngestion {
  constructor(options) {
    this.L = options.L;
    this.dbInstance = options.dbInstance;
    this.activePidLib = options.activePidLib;
    this.rechargeConfig = options.rechargeConfig;
    this.config = options.config;
    this.bucketName = "digital-reminder";
    progressTracker = this.getProgressObject();
    this.csvIngester = new AWSCsvIngester(options, this.updateProgress);
    this.logPrefix = serviceName;
    this.readBatchSize = 100;
    this.parallelWrites = 5;
    this.serviceMissing = 0;
    this.operatorMissing = 0;
    this.productidMissing = 0;
    this.rechargeNumberMissing = 0;
    this.customerIdMissing = 0;
    this.inserted_row_count = 0;
    this.operatorMapping = {};
    this.currentFile = "";
    this.OAuth = new OAuth({ batchSize: _.get(this.config, ["NOTIFICATION", "notificationapi", "BR_CHUNKSIZE"], 50), rechargeConfig: this.rechargeConfig });
    this.folderPath = `${_.get(options.config, ["DYNAMIC_CONFIG", "CC_INGESTION", "path", "value"], "digital-reminder/CCBP_BBPS_FETCH_NEW")}`;

    this.files = [];
  }

  /** Get table Name based on record */
  getTableName(record) {
    return "bills_creditcard";
  }

  getProgressObject() {
    let progress = {};
    this.L.info("Loading progress object from", progressFilePath);
    if (fs.existsSync(progressFilePath)) {
      const progressData = fs.readFileSync(progressFilePath, "utf-8");
      progress = JSON.parse(progressData);
    }
    this.L.info("Loaded", progress);
    return progress;
  }

  updateProgress(filename, count) {
    if (_.get(progressTracker, [filename], 0) == -1) return;
    _.set(progressTracker, [filename], count);
    this.L.info("Updated progess Object", JSON.stringify(progressTracker), count);
    fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2));
  }

  filterFileByMonth(filename) {
    try{
       let date = filename.split('$')[1].split('.')[0].slice(0,7)
       if(date == MOMENT().subtract(1,'day').format('YYYY-MM'))return true

    }catch(err){
        return false
    }
    return false
  }

  start(callback) {
    let self = this;
    try {
      this.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize);
    } catch (error) {
      self.L.critical(this.logPrefix, "Cannot initialize AWS");
      return callback(error);
    }
    eachSeries(["digital-reminder/CCBP_BBPS_FETCH_NEW/CIR_PG_S3 data.csv"],self.processEachFile.bind(self), callback)

    // this.csvIngester.getFileNames(this.folderPath, function (err, data) {
    //   if (err) {
    //     self.L.error("Error while getting files");
    //     return callback(err);
    //   } else {
    //     // data = _.filter(data, self.filterFileByMonth);
    //     return eachSeries(data, self.processEachFile.bind(self), callback);
    //   }
    // });
  }

  processEachFile(filename, callback) {
    if (_.get(progressTracker, filename, null) == -1) {
      /** File has already been processed we can skip*/
      this.L.info("Skipping file ", filename, "as it has been already processed");
      return callback();
    }
    this.L.info("Processing file :- ", filename);
    this.currentFile = filename;
    let skipRows = _.get(progressTracker, [filename], 0);
    this.csvIngester.start(
      this.processRecordinBatch.bind(this),
      filename,
      function (error, data) {
        return callback();
      },
      skipRows
    );
  }

  async processRecordinBatch(data) {
    let self = this;
    let records = [];
    /** Distribute a  batch of 1000 records into further smaller batches */
    for (let i = 0; i < data.length; i = i + self.readBatchSize) {
      records.push(data.slice(i, Math.min(i + self.readBatchSize, data.length)));
    }
    await new Promise((resolve, reject) => {
      eachSeries(records, self.processOneBatch.bind(self), function (error) {
        if (error) self.L.error(self.logPrefix, "Error while processing batch", error);
        return resolve();
      });
    }); // await new Promise((resolve , reject)=>{
    //     eachLimit(records ,self.parallelWrites, function(record , cb){
    //         self.processRecord(function(){
    //             cb()
    //         } ,record)
    //     },function(error){
    //         /** Release memory of records variable */
    //         records = null;
    //         if(error){
    //             self.L.error(self.logPrefix , "Error while processing batch",error)
    //             return reject(error)
    //         }
    //         return resolve()
    //     })
    // })
  }

  processOneBatch(records, callback) {
    let self = this;
    let map = {};
    self.getRecordsFromDB(records).then((dbRecords) => {
      dbRecords.forEach((dbRecord) => {
        let key = `${dbRecord.customer_id}_${dbRecord.recharge_number.replace(/\s+/g, '').slice(-4)}`;
        if (_.get(map, key, null) == null) {
          map[key] = [dbRecord];
        } else map[key].push(dbRecord);
      });
      /** Start processing Batch */
      eachLimit(
        records,
        self.parallelWrites,
        function (record, cb) {
          self.processRecord(
            function () {
              cb();
            },
            record,
            map
          );
        },
        function (error) {
          /** Release memory of records variable */
          (records = null), (map = null);
          if (error) {
            self.L.error(self.logPrefix, "Error while processing batch", error);
            return callback(error);
          }
          return callback();
        }
      )
    });
  }


  validateRecord(data) {
    let self = this;
    let nbfd = MOMENT().format("YYYY-MM-DD HH:mm:ss");
    let params = {
      service: _.get(data, "service", null),
      operator: _.get(data, "operator", null),
      productid: _.get(data, "product_id", null),
      rechargeNumber: _.get(data, "recharge_number", _.get(data, "rechargeNumber", null)),
      customer_id: _.get(data, "customer_id", null),
      status: "0",
      service_id: "0",
      is_automatic: "0",
      next_bill_fetch_date: nbfd,
      paytype: _.get(data, "paytype", null),
      source: _.get(data, "source", null),
      bank_code: _.get(data, "bank_code", null),
    };
    let tableName = self.getTableName(data);
    if (!params.customer_id) {
      self.L.error(self.currentFile, "Customer ID missing", params);
      self.customerIdMissing++;
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:CUSTOMER_ID_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    if (!params.rechargeNumber || (typeof params.rechargeNumber=="string" && params.rechargeNumber.replace(/\s+/g,"").match(/^\d*X*\d{4}$/)==null )) {
      self.L.error(self.currentFile, "Recharge number missing or incorrect", params.rechargeNumber);

      self.rechargeNumberMissing++;
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECHARGE_NUMBER_MISSING_OR_INCORRECT"])
      return false;
    }
    if (!params.operator) {
      self.L.error(self.currentFile, "Operator missing", params, _.get(data, "operator", null));
      self.operatorMissing++;
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:OPERATOR_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    if (!params.service) {
      self.L.error(self.currentFile, "Service name missing", params);
      self.serviceMissing++;
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:SERVICE_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    if (!params.productid) {
      self.L.error(self.currentFile, "Product Id missing", params);
      
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:PRODUCT_ID_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    if (!params.paytype) {
      self.L.error(self.currentFile, "Paytype missing", params);
      
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:PAYTYPE_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    if (!params.bank_code) {
      self.L.error(self.currentFile, "Paytype missing", params);
      
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:BANKCODE_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    if (!params.source) {
      self.L.error(self.currentFile, "Paytype missing", params);
      
      utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:SOURCE_NOT_FOUND_FROM_CSV"]);
      return false;
    }
    params.bank_code = _.toLower(params.bank_code)
    params.source = _.toUpper(params.source)
    return params;
  }

  async getRecordsFromDB(records) {
    let cids = [],
      self = this;
    records.forEach((element) => {
      let cid = _.get(element, "customer_id", null);
      if (_.isString(cid) && cid.length > 0) {
        cids.push(cid);
      }
    });
    /** Run SQL Query */
    let query = `SELECT * from bills_creditcard where customer_id IN (${cids})`;
    let res = await new Promise((resolve, reject) => {
      self.dbInstance.exec(
        (err, records) => {
          if (err) {
            self.L.critical("", "Error while processing records", err);
            reject(err);
          }
          resolve(records);
        },
        "DIGITAL_REMINDER_SLAVE",
        query,
        []
      );
    });
    return res;
  }

  processRecord(cb, data, map) {
    let self = this;

    let params = self.validateRecord(data);
    if (params === false) {
      return cb();
    }
    let key = `${params.customer_id}_${params.rechargeNumber.replace(/\s+/g, '').slice(-4)}`;
    let tableName = self.getTableName(data);
    let matchingID = null,existingNBFD=null;;
    self.L.info(self.logPrefix, "key:", key, map[key]);

    if (_.isEmpty(map[key]) == false) {
      let existingRecords = map[key];
      /** We will check if bank name of any of the records matches */

      existingRecords.forEach((existingRecord) => {
        if (_.isString(existingRecord.bank_name) && existingRecord.bank_name.length > 0) {
          if (_.toLower(existingRecord.bank_name) == _.toLower(data.bank_code)) {
            self.L.info("process Record::","Existing record with same recharge number , bank and cust ID found on ID",existingRecord.id)
            matchingID = existingRecord.id;
            existingNBFD = MOMENT(existingRecord['next_bill_fetch_date'])
          }
        } else {
          let productId = _.get(existingRecord, ["product_id"], "");
          let bankAttributes, bankName, cardNetwork;
          bankAttributes = JSON.parse(_.get(self.config, ["CVR_DATA", productId, "attributes"], "{}"));
          bankName = _.toLower(_.get(bankAttributes, ["bank_code"], ""));

          /** Fetch bank name from cvr and set */
          if (bankName == data.bank_code) {
            self.L.info("process Record::","Existing record with same recharge number , bank and cust ID found on ID",existingRecord.id)
            matchingID = existingRecord.id;
            existingNBFD = MOMENT(existingRecord['next_bill_fetch_date'])
          }
        }
      });
    }
    if (matchingID == null) {
      self.L.info(self.logPrefix, "No matching record found , INSERTING..");
      self.insertRecords(tableName, params, (error) => {
        if (error) {
          utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS", "STATUS:RECORDS_NOT_INSERTED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_NOT_INSERTED_INTO_TABLE"]);
          L.error("error", error);
        } else {
          self.L.info(self.currentFile, "Record inserted");
          utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS", "STATUS:RECORDS_INSERTED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_INSERTED_INTO_TABLE"]);
          self.L.log(`insertRecords :: Inserted into ${tableName} for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
        }
        return cb(error);
      });
    } else {
      if(existingNBFD && existingNBFD > MOMENT()){
        self.L.info("process Record::","Existing record has NBFD in future , no need to update")
        return cb()
      }
      
      /** We have a matching ID we have to update the NBFD */
      self.updateRecords(tableName, matchingID, params, (error) => {
        if (error) {
          utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS", "STATUS:RECORDS_NOT_INSERTED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_NOT_INSERTED_INTO_TABLE"]);
          L.error("error", error);
        } else {
          self.L.info(self.currentFile, "Record updated");
          utility._sendMetricsToDD(1, ["REQUEST_TYPE:COMMON_CAAS", "STATUS:RECORDS_UPDATED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_UPDATED_INTO_TABLE"]);
          self.L.log(`updateRecords :: Updated into ${tableName} for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
        }
        return cb(error);
      });
    }
  }

  insertRecords(table_name, params, cb) {
    let self = this;

    let reference_id
    try{
      reference_id = `CSV_${params.customer_id}_${params.rechargeNumber.replace(/\s+/g, '').slice(-4)}_${params.bank_code}`;
    }catch(err){
        self.L.critical("Error",err)
        cb(err)
    }
    const query = `INSERT INTO ${table_name} \
       (customer_id, recharge_number, product_id, operator, next_bill_fetch_date, service  , paytype ,bank_name ,  data_source,reference_id ,extra) \
       values (?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE next_bill_fetch_date=VALUES(next_bill_fetch_date)`;

    const param = [params.customer_id, params.rechargeNumber, params.productid, params.operator, params.next_bill_fetch_date, params.service, params.paytype, params.bank_code, params.source, reference_id,JSON.stringify({})];

    self.dbInstance.exec(
      (error, res) => {
        if (error) {
          self.L.critical("writeCustomerDetails::", query, error);
        }
        return cb(error, res);
      },
      "DIGITAL_REMINDER_MASTER",
      query,
      param
    );
  }

  updateRecords(table_name, id, params, cb) {
    let self = this;

    const query = `UPDATE ${table_name} \
    SET next_bill_fetch_date=? WHERE id = ?`;

    const param = [params.next_bill_fetch_date, id];

    self.L.info("updateRecords::",query , param)

    self.dbInstance.exec(
      (error, res) => {
        if (error) {
          self.L.critical("writeCustomerDetails::", query, error);
        }
        return cb(error, res);
      },
      "DIGITAL_REMINDER_MASTER",
      query,
      param
    );
  }
}

(function main() {
  if (require.main === module) {
    startup.init({}, function (err, options) {
      let script;
      try {
        script = new CCIngestion(options);
        script.start(function (err) {
          setTimeout(function () {
            if (err) {
              console.log("main::Error" + err);
              process.exit(1);
            } else {
              console.log("main::completed");
              process.exit(0);
            }
          }, 1000);
        });
      } catch (err) {
        options.L.error(err);
        process.exit(1);
      }
    });
  }
})();

export default CCIngestion;
