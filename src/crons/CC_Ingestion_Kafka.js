import startup from "../lib/startup";
import MOMENT from "moment";
import _, { reject } from "lodash";
import L from "lgr";
import fs from "fs";
import { parseStream } from "fast-csv";
import { each, eachLimit, eachSeries, parallel } from "async";
import AWSCsvIngester from "../lib/awscsvingester";
import utility from "../lib";
import OAuth from "../lib/oauth";
import ASYNC from 'async'


let serviceName = "CC_INGESTION_KAFKA";
let progressFilePath = `/var/log/digital-notification/progress-CC-Kafka-${MOMENT().subtract(1, "day").format("MMYYYY")}.json`;
let progressTracker = {};
/** Maintain offset and processed files in a path */
class CCIngestionKafka {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject();
        this.csvIngester = new AWSCsvIngester(options, this.updateProgress);
        this.logPrefix = serviceName;
        this.readBatchSize = 100;
        this.parallelWrites = 5;
        this.serviceMissing = 0;
        this.operatorMissing = 0;
        this.productidMissing = 0;
        this.rechargeNumberMissing = 0;
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {};
        this.currentFile = "";
        this.OAuth = new OAuth({ batchSize: _.get(this.config, ["NOTIFICATION", "notificationapi", "BR_CHUNKSIZE"], 50), rechargeConfig: this.rechargeConfig });
        this.folderPath = `${_.get(options.config, ["DYNAMIC_CONFIG", "CC_INGESTION", "path", "value"], "digital-reminder/CCBP_BBPS_FETCH_NEW")}`;

        this.files = [];
    }

    getProgressObject() {
        let progress = {};
        progressFilePath = `/var/log/digital-notification/progress-CC-Kafka-${MOMENT().subtract(1, "day").format("MMYYYY")}.json`
        this.L.info("Loading progress object from", progressFilePath);
        if (fs.existsSync(progressFilePath)) {
            const progressData = fs.readFileSync(progressFilePath, "utf-8");
            progress = JSON.parse(progressData);
        }
        this.L.info("Loaded", progress);
        return progress;
    }

    updateProgress(filename, count) {
        if (_.get(progressTracker, [filename], 0) == -1) return;
        _.set(progressTracker, [filename], count);
        this.L.info("Updated progess Object", JSON.stringify(progressTracker), count);
        fs.writeFileSync(progressFilePath, JSON.stringify(progressTracker, null, 2));
    }

    filterFileByMonth(filename) {
        try {
            let date = filename.split('$')[1].split('.')[0].slice(0, 7)
            if (date == MOMENT().subtract(1, 'day').format('YYYY-MM')) return true

        } catch (err) {
            return false
        }
        return false
    }

    start() {
        let self = this;
        self._start(function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }

    _start(callback) {
        let self = this;
        progressTracker = this.getProgressObject();
        ASYNC.waterfall([
            next => {
                self.L.log('_start', 'Going to initialize Kakfa Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('_start', 'Going to cofigure csv ingestor');
                self.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize);
                next();
            },
            next => {
                self.L.info("Getting Files in the folder");
                this.csvIngester.getFileNames(this.folderPath, function (err, data) {
                    if (err) {
                        self.L.error("Error while getting files");
                        return callback(err);
                    } else {
                        data = _.filter(data, self.filterFileByMonth);
                        return eachSeries(data, self.processEachFile.bind(self), callback);
                    }
                });
            }], function (error) {
                if (error) {
                    self.L.error('_start', 'Error', error);
                }
                return callback(error);
            });
    }

    configureKafkaPublisher(done) {
        let self = this;

        self.kafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.PUBLISHER_NON_RU.HOSTS
        });
        self.kafkaPublisher.initProducer('high', function (error) {
            if (!error) {
                self.L.log("notify :: configureKafkaPublisher", "publisher Configured");
                return done();
            }
            return done(error);
        });
    }

    processEachFile(filename, callback) {
        if (_.get(progressTracker, filename, null) == -1) {
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ", filename, "as it has been already processed");
            return callback();
        }
        this.L.info("Processing file :- ", filename);
        this.currentFile = filename;
        let skipRows = _.get(progressTracker, [filename], 0);
        this.csvIngester.start(
            this.processRecordinBatch.bind(this),
            filename,
            function (error, data) {
                return callback();
            },
            skipRows
        );
    }

    async processRecordinBatch(data) {
        let self = this;
        let records = [];
        /** Distribute a  batch of 1000 records into further smaller batches */
        for (let i = 0; i < data.length; i = i + self.readBatchSize) {
            records.push(data.slice(i, Math.min(i + self.readBatchSize, data.length)));
        }
        await new Promise((resolve, reject) => {
            eachSeries(records, self.processOneBatch.bind(self), function (error) {
                if (error) self.L.error(self.logPrefix, "Error while processing batch", error);
                return resolve();
            });
        });
    }

    processOneBatch(records, callback) {
        let self = this;
        /** Start processing Batch */
        eachLimit(
            records,
            self.parallelWrites,
            function (record, cb) {
                self.processRecord(
                    function () {
                        cb();
                    },
                    record
                );
            },
            function (error) {
                /** Release memory of records variable */
                (records = null);
                if (error) {
                    self.L.error(self.logPrefix, "Error while processing batch", error);
                    return callback(error);
                }
                return callback();
            });
    }

    validateRecord(data) {
        let self = this;
        let nbfd = MOMENT().add(1, "d").format("YYYY-MM-DD HH:mm:ss");
        let productId = _.get(data,"product_id", null)
        
        let bankAttributes, bankName;
        try{
            bankAttributes = JSON.parse(_.get(self.config, ["CVR_DATA", productId, "attributes"], "{}"));
            bankName = _.toLower(_.get(bankAttributes, ["bank_code"], ""));
        }
        catch (error) {
            this.L.error('validationRecor',`Error occured while parsing CVR_DATA:${data}`,error)
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", 'STATUS:ERROR','TYPE:PID_INVALID_PARSE',`PRODUCT_ID : ${productId}`]);
            //return this.respondWithMessage(res,500,"Error while serving request,Please try again in some time");
        }
          
        let params = {
            service: _.get(data, "service", null),
            operator: _.get(data, "operator", null),
            product_id: _.get(data, "product_id", null),
            recharge_number: _.get(data, "recharge_number", _.get(data, "rechargeNumber", null)),
            customer_id: _.get(data, "customer_id", null),
            status: "0",
            service_id: "0",
            is_automatic: "0",
            next_bill_fetch_date: nbfd,
            paytype: _.get(data, "paytype", null),
            source: _.get(data, "source", null),
            extra: null,
            bank_name : bankName,
            alternate_number_1 : _.get(data, "alternate_number_1", null),
            alternate_number_2 : _.get(data, "alternate_number_2", null),
            alternate_number_3 : _.get(data, "alternate_number_3", null),
            alternate_number_4 : _.get(data, "alternate_number_4", null),
            alternate_number_5 : _.get(data, "alternate_number_5", null)
        };

        if (!params.customer_id) {
            self.L.error(self.currentFile, "Customer ID missing", params);
            self.customerIdMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:CUSTOMER_ID_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.recharge_number) {
            self.L.error(self.currentFile, "Recharge number missing", params);
            self.rechargeNumberMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.operator) {
            self.L.error(self.currentFile, "Operator missing", params, _.get(data, "operator", null));
            self.operatorMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:OPERATOR_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.service) {
            self.L.error(self.currentFile, "Service name missing", params);
            self.serviceMissing++;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:SERVICE_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.product_id) {
            self.L.error(self.currentFile, "Product Id missing", params);

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:PRODUCT_ID_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.paytype) {
            self.L.error(self.currentFile, "Paytype missing", params);

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:PAYTYPE_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.source) {
            self.L.error(self.currentFile, "Source missing", params);

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:SOURCE_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        if (!params.bank_name) {
            self.L.error(self.currentFile, "BankName missing", params);

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:PARAMS_MISSING", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:BANK_NAME_NOT_FOUND_FROM_CSV"]);
            return false;
        }
        
        params.source = _.toUpper(params.source)
        return params;
    }

    processRecord(cb, data) {
        let self = this;
        let params = self.validateRecord(data);
        if (params === false) {
            return cb();
        }
        self.publishRecord(params, (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:RECORDS_NOT_PUBLISHED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_NOT_PUBLISHED"]);
                L.error("error", error);
            } else {
                self.L.info(self.currentFile, "Record published");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CC_INGESTION_KAFKA", "STATUS:RECORDS_PUBLISHED", `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, "TYPE:RECORDS_PUBLISHED"]);
                self.L.log(`publishRecords :: Records Published  for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
            }
            return cb(error);
        });
    }

    publishRecord(record, done) {
        let
            self = this;
        let
            payload = self.prepareDataToPublish(record);
        self.kafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.PUBLISHER_NON_RU.TOPIC', null),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {
                self.L.critical('publishData', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            } else {
                self.L.log('publishData', 'Message published successfully in Kafka', ' on topic PUBLISHER_BILL_FETCH', JSON.stringify(payload));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.PUBLISHER_NON_RU', null),
                    'REQUEST_TYPE:NOTIFICATION',
                    'OPERATOR:' + _.get(payload, 'operator')
                ])
            }
            return done();
        }, [200, 800]);
    }

    prepareDataToPublish(record) {
        return {
            customer_id: record.customer_id,
            recharge_number: record.recharge_number,
            service: record.service,
            product_id: record.product_id,
            operator: record.operator,
            bucket_id: null,
            amount: null,
            due_date: null,
            bill_date: null,
            bill_fetch_date: null,
            next_bill_fetch_date: null,
            gateway: null,
            paytype: record.paytype,
            circle: null,
            customerMobile: null,
            customerEmail: null,
            retryCount: 0,
            status: null,
            published_date: null,
            user_data: '{}',
            notification_status: 1,
            payment_date: null,
            service_id: 0,
            extra: JSON.stringify({ 'source': record.source }),
            bank_name : record.bank_name,
            alternate_number_1 : record.alternate_number_1,
            alternate_number_2 : record.alternate_number_2,
            alternate_number_3 : record.alternate_number_3,
            alternate_number_4 : record.alternate_number_4,
            alternate_number_5 : record.alternate_number_5
        }
    }
}

(function main() {
    if (require.main === module) {
        startup.init({}, function (err, options) {
            let script;
            try {
                script = new CCIngestionKafka(options);
                script._start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                });
            } catch (err) {
                options.L.error(err);
                process.exit(1);
            }
        });
    }
})();

export default CCIngestionKafka;
