import ASYNC from 'async'
import _ from 'lodash'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import OS from 'os'
import NotificationLibrary from '../lib/notification'
import cassandraBills from '../models/cassandraBills'
import utility from '../lib';
import NOTIFIER from '../services/notify'
import billsLib from '../lib/bills'
import KafkaPublisherFactory from '../lib/kafkaPublisherFactory'
import RejectedNotificationHandler from '../lib/rejectedNotificationHandler'

class OldBillDuePublisher {
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.dbInstance = options.dbInstance;
        this.bills = new BILLS(options);
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.blackListOperators = _.get(this.notificationConfig, 'BlackListOperator', null);
        this.notificationLibrary = new NotificationLibrary(options);
        this.offsetIdMap = {};
        this.lastIdMap = {};
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.cassandraBills = new cassandraBills(options);
        this.notify = new NOTIFIER(options);
        this.billsLib = new billsLib(options);
        this.kafkaPublisherFactory = new KafkaPublisherFactory(this.config, this.infraUtils, this.L);
        this.rejectedNotificationHandler = new RejectedNotificationHandler(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

        this.operatorList = {}
        this.getOperatorList();
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: billDuePublisher", "Re-initializing variable after interval");
        self.notificationConfig = _.get(this.config, 'NOTIFICATION');
        self.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.blackListOperators = _.get(this.notificationConfig, 'BlackListOperator', null);
    }

    getOperatorList() {
        let self = this;
        Object.keys(self.operatorTableMap).forEach((operator) => {
            /** Check if operator is blacklisted */
            if (self.blackListOperators && self.blackListOperators.indexOf(operator) != -1) {
                return
            }
            else {
                self.operatorList[operator] = true;
            }
        })

        self.L.log("operator list :", JSON.stringify(self.operatorList))
    }

    start() {
        let self = this;
        self._start(function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
            }, 1000);
        });
    }

    _start(done) {
        let
            self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('_start', 'Going to initialize Kakfa Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('_start', 'Going to publish records for all operators');
                return self.processOperators(next);
            }
        ], async function (error) {
            if (error) {
                self.L.error('_start', 'Error', error);
            }
            try {
                await self.close();
                self.L.log('_start', 'Successfully closed all Kafka connections');
            } catch (closeError) {
                self.L.error('_start', 'Error closing Kafka connections', closeError);
            }
            return done(error);
        });
    }

    /**
     * Kafka publisher
     */
    async configureKafkaPublisher(done) {
        let self = this;
        try {
            // Initialize both publishers
            self.kafkaNotificationServicePublisher = await self.kafkaPublisherFactory.createPublisher('REMINDER_BILLFETCH_PIPELINE');
            await self.rejectedNotificationHandler.initialize();
            return done();
        } catch (error) {
            self.L.error('configureKafkaPublisher', 'Error configuring Kafka publishers:', error);
            return done(error);
        }
    }

    processOperators(done) {
        let self = this;

        let tables = {}
        Object.keys(self.operatorList).forEach(operator => {
            let table = _.get(self.operatorTableMap, [operator], null)
            tables[table] = true;
        })

        // if(_.get(self.config, ['DYNAMIC_CONFIG', 'PAYTM_POSTPAID_CONFIG', 'BILL_DUE_NOTIFICATIONS', 'PAUSE_NOTIFICATIONS'], 0)){
        delete tables["bills_paytmpostpaid"];
        // }
        self.L.verbose("🚀 ~ BillDuePublisher ~ processOperators ~ tables:", tables)

        ASYNC.forEachOfSeries(tables, (value, table, callback) => {
            if (self.offsetIdMap[table] || self.lastIdMap[table]) {
                self.L.log('processOperators', `Table ${table} already processed...skipping it`);
                return callback();
            } else {
                self.offsetIdMap[table] = {};
                self.lastIdMap[table] = {};
                /** Passing ALL in place of operator name as after IN-33661 the function would not be using operator to get Due dates*/
                let dueDateIntervals = self.notificationLibrary.getIntervalForOldDueDates('ALL', table);
                if (dueDateIntervals != null && dueDateIntervals.length > 0) {
                    dueDateIntervals.forEach((dueDateInterval) => {
                        self.offsetIdMap[table][dueDateInterval] = 0; // start with 0 offset
                        self.lastIdMap[table][dueDateInterval] = 0; // start with 0 offset
                    });
                    self.L.log('processOperators', 'processing records for  table: ' + table);
                    self.processBills(table, dueDateIntervals, () => {
                        return callback();
                    });
                }
                else {
                    self.L.log('processOperators', 'No due date intervals found for table: ' + table);
                    return callback();
                }
            }
        },
            (err) => {
                self.L.log('processOperators', 'Completed !!');
                return done(err);
            });
    }

    processBills(tableName, dueDateIntervals, done) {
        let self = this;
        ASYNC.forEachOfSeries(dueDateIntervals, (dueDateInterval, index, callback) => {
            self.L.log('processBills', 'processing records for the  table: ' + tableName + ' and dueDateInterval: ' + dueDateInterval);

            self.processBillsForEachDueDate(tableName, dueDateInterval, () => {
                return callback();
            });
        }
            , (err) => {
                if (err) {
                    self.L.log('processBills', 'Error while processing records for the table: ' + tableName + ' and dueDateInterval: ' + dueDateInterval + ' MSG:-' + err);
                }
                return done(err);
            })
    }

    processBillsForEachDueDate(tableName, dueDateInterval, done) {
        let self = this,
            batchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'OLD_DUEDATE_PUBLISHER', 'BATCHSIZE'], 5) : _.get(self.config, ['NOTIFICATION', 'OLD_BILL_DUE_PUBLISHER_BATCH'], 100),
            offsetId = self.offsetIdMap[tableName][dueDateInterval],
            lastId = self.lastIdMap[tableName][dueDateInterval],
            dueDate = MOMENT().add(dueDateInterval, 'day').format('YYYY-MM-DD'),
            currentDate = MOMENT().format('YYYY-MM-DD');


        self.bills.getBillsToNotifyForOldBillDuePublisher(function _doUntilNoMoreRecords(error, data) {
            if (!error && data && data.length > 0) {
                self.processBatch(data, tableName, () => {
                    self.offsetIdMap[tableName][dueDateInterval] = data.length + self.offsetIdMap[tableName][dueDateInterval];
                    self.lastIdMap[tableName][dueDateInterval] = _.last(data)['id'];
                    if (self.greyScaleEnv) {
                        self.L.log('processBillsForEachDueDate', `GREYSCALEMODE for tableName ${tableName}, Total processed records ${self.offsetIdMap[tableName][dueDateInterval]}`);
                        return done();
                    }

                    self.L.log('processBillsForEachDueDate', `processing next batch for tableName ${tableName} and offset ${self.offsetIdMap[tableName][dueDateInterval]}`);
                    self.L.log('processBillsForEachDueDate', `processing next batch for tableName ${tableName} and id ${self.lastIdMap[tableName][dueDateInterval]}`);
                    setTimeout(() => {
                        self.bills.getBillsToNotifyForOldBillDuePublisher(_doUntilNoMoreRecords, tableName, batchSize, dueDate, self.offsetIdMap[tableName][dueDateInterval], self.lastIdMap[tableName][dueDateInterval], currentDate);
                    }, 100);
                });
            } else {
                self.L.verbose('processBillsForEachDueDate', `No record found for tableName ${tableName}, Total processed records ${self.offsetIdMap[tableName][dueDateInterval]}`);
                done();
            }
        }, tableName, batchSize, dueDate, offsetId, lastId, currentDate);
    }

    processBatch(records, tableName, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                if (record && record['customer_id']) {
                    return self.publishData(record, tableName, next);
                } else {
                    next();
                }
            },
            err => {
                done();
            }
        )
    }

    validateRecord(record, tableName) {
        let self = this;
        let currentDate = MOMENT().format('YYYY-MM-DD');
        let minAmount = _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)
        let disabledStatus = _.get(self.config, 'COMMON.notification_status.DISABLED', 0)

        /** First check if record's operator belongs to operator list */
        let operator = _.get(record, 'operator', '');

        // if(_.isArray(self.AllowedListOperatorBillGen) &&  self.AllowedListOperatorBillGen.indexOf(operator) > -1 && record.bill_fetch_date){
        //     let billGenDateIntervals  = self.notificationLibrary.getPossibleBillGenDates('ALL', tableName);
        //     let isSameDateFound = false;

        //     billGenDateIntervals.forEach(function(billGenDateInterval){
        //         if(!isSameDateFound){
        //             let billGenDate = MOMENT(record.bill_fetch_date).add(billGenDateInterval, 'day').format('YYYY-MM-DD');
        //             if(billGenDate == currentDate) isSameDateFound = true;
        //         }
        //     })
        //     if(isSameDateFound) return [true, 'Rejecting due date notification as bill generation date is same as current date'];
        // }

        if (self.operatorList && self.operatorList[operator] == true) {

            /** Checking amount and notification status */
            if ((_.toLower(_.get(record, 'service', null)) == 'paytm postpaid' || _.get(record, 'amount', null) === null || _.get(record, 'amount', 0) > minAmount) && _.get(record, 'notification_status', '') != disabledStatus) {

                /** Checking bill fetch date */
                let bill_fetch_date = _.get(record, 'bill_fetch_date', null),
                    old_bill_fetch_date = _.get(record, 'old_bill_fetch_date', null);

                bill_fetch_date = bill_fetch_date ? (MOMENT.utc(bill_fetch_date).isValid() ? MOMENT.utc(bill_fetch_date).format('YYYY-MM-DD') : null) : null;
                old_bill_fetch_date = old_bill_fetch_date ? (MOMENT.utc(old_bill_fetch_date).isValid() ? MOMENT.utc(old_bill_fetch_date).format('YYYY-MM-DD') : null) : null;

                if (bill_fetch_date && bill_fetch_date < currentDate && bill_fetch_date < old_bill_fetch_date) return [false, null];
                else return [true, 'Rejecting due date notification as bill fetch date is greater than or equal to current date']
            } else {
                return [true, 'Rejecting due date notification as amount is less than min amount or notification status is disabled']
            }
        }

        return [true, 'Rejecting due date notification as operator is not allowed']
    }

    publishData(record, tableName, done) {
        let self = this;
        let [validationFailed, error_msg] = self.validateRecord(record, tableName);
        if (validationFailed) {
            return self.rejectedNotificationHandler.insertRejectedNotifications(done, error_msg, { ...record, dataFrom: 'oldBillDueCron' });
        }
      
        let payload = self.prepareDataToPublish(record),
            skipNotification = self.greyScaleEnv ? 1 : 0;
        _.set(payload, 'data.skipNotification', skipNotification);
        utility.sendNotificationMetricsFromSource(payload)
    
        self.kafkaNotificationServicePublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', null),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {
                utility.sendNotificationMetricsFromSource(payload, "ERROR")
                self.L.critical('publishData', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                let errorMsg = self.billsLib.createErrorMessage(error);
                return self.rejectedNotificationHandler.insertRejectedNotification(done, errorMsg, { ...record, dataFrom: 'billDueCron' });
            } else {
                self.L.log('publishData', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', null),
                    'REQUEST_TYPE:OLD_BILL_NOTIFICATION',
                    'SOURCE:OLD_BILL_DUE_PUBLISHER',
                    'OPERATOR:' + _.get(payload, 'data.operator')
                ])
                return done();
            }
        }, [200, 800]);
    }

    prepareDataToPublish(record) {
        return {
            "source": "reminderOldBillDuePublisher",
            "notificationType": "OLD_BILL_NOTIFICATION",
            "machineId": OS.hostname(),
            "data": {
                "id": _.get(record, 'id'),
                "is_automatic": _.get(record, 'is_automatic'),
                "customerId": _.get(record, 'customer_id'),
                "rechargeNumber": _.get(record, 'recharge_number'),
                "productId": _.get(record, 'product_id'),
                "operator": _.get(record, 'operator'),
                "amount": _.get(record, 'amount'),
                "dueDate": _.get(record, 'due_date'),
                "billFetchDate": _.get(record, 'bill_fetch_date'),
                "nextBillFetchDate": _.get(record, 'next_bill_fetch_date'),
                "gateway": _.get(record, 'gateway'),
                "paytype": _.get(record, 'paytype'),
                "service": _.get(record, 'service'),
                "circle": _.get(record, 'circle'),
                "customerMobile": _.get(record, 'customer_mobile'),
                "customerEmail": _.get(record, 'customer_email'),
                "paymentChannel": _.get(record, 'payment_channel'),
                "retryCount": _.get(record, 'retry_count'),
                "status": _.get(record, 'status'),
                "reason": _.get(record, 'reason'),
                "userData": _.get(record, 'user_data'),
                "createdAt": _.get(record, 'created_at'),
                "updatedAt": _.get(record, 'updated_at'),
                "billDate": _.get(record, 'bill_date'),
                "paymentDate": _.get(record, 'payment_date'),
                "notification_status": _.get(record, 'notification_status'),
                "service_id": _.get(record, 'service_id'),
                "customerOtherInfo": _.get(record, 'customerOtherInfo'),
                "extra": _.get(record, 'extra', null),
                "bank_name": _.get(record, 'bank_name', null),
                "card_network": _.get(record, 'card_network', null),
                "data_source": _.get(record, 'data_source', null),
                "billDue": true,
                "oldBillFetchDate": _.get(record, 'old_bill_fetch_date', null),
                "remindLaterDate": _.get(record, 'remind_later_date', null)
            }
        };
    }

    /**
     * Close all Kafka connections and cleanup resources
     * @returns {Promise<void>}
     */
    async close() {
        let self = this;
        try {
            // Close the notification service publisher
            if (self.kafkaNotificationServicePublisher && self.kafkaNotificationServicePublisher.client) {
                await new Promise((resolve, reject) => {
                    self.kafkaNotificationServicePublisher.client.close((error) => {
                        if (error) {
                            self.L.error('Error closing notification service Kafka client:', error);
                            reject(error);
                        } else {
                            self.L.log('Successfully closed notification service Kafka client');
                            resolve();
                        }
                    });
                });
            }

            // Close the rejected notification handler
            if (self.rejectedNotificationHandler) {
                await self.rejectedNotificationHandler.close();
            }

            // Close the Kafka publisher factory
            if (self.kafkaPublisherFactory) {
                await self.kafkaPublisherFactory.closeAll();
            }

            self.L.log('All Kafka connections closed successfully');
        } catch (error) {
            self.L.error('Error closing Kafka connections:', error);
            throw error;
        }
    }
}

export default OldBillDuePublisher