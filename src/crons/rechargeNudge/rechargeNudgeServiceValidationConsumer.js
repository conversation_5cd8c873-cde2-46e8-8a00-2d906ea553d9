/*jshint multistr: true ,node: true*/
"use strict";

import OS               from 'os'
import MOMENT           from 'moment'
import <PERSON><PERSON><PERSON>            from 'async'
import REQUEST          from 'request'
import <PERSON><PERSON><PERSON>AT<PERSON>        from 'validator'
import _                from 'lodash'
import utility from '../../lib'
import NOTIFIER from '../../services/notify'
const { Kafka } = require('kafkajs')

/**
 * @class
 * Recharge nudge validation consumer is a cron to fetch validation data from kafka, check its corresponding recharge data from cache and send PUSH notification to user accordingly
 */

class rechargeNudgeServiceValidationConsumer {
    /**
     * Configure specifications
     * @param { object } options Contains configuration and dependencies
     */
    constructor(options) {
        this.L                      = options.L;
        this.config                 = options.config;
        this.infrautils             = options.INFRAUTILS;
        this.notify                 = new NOTIFIER(options);
        this.dbInstance             = options.dbInstance;
        this.activePidLib           = options.activePidLib;
        this.operatorLabelMap       = {};
        // initializing redis
        this.redis                  = new this.infrautils.cache("REDIS", this.config.REDIS);
        // used for logging purpose
        this.batchNumber            = 0;
        this.enabledServices = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.enabledValidationServices', {});
        this.nudgeOnValidationSuccess = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.nudgeOnValidationSuccess', {});
    }

    /**
     * Starting point of service
     */
    start() {
        /**
         * Flow of execution:
         * 1. Create operator label map, connect with redis cache and initialize kafka consumer.
         * 2. Fetch validation data from kafka in chunks and check if notification is to be sent
         * 3. Send notification
         */
        ASYNC.waterfall([
            (callback) => {
                // create pid : operator_label map
                this.createOperatorLabelMap(callback);
            },
            (callback) => {
                // connect with redis server
                this.L.log('configureKafkaPublisher', 'Going to create connection with redis');
                this.redis.connect((err, data) => {
                    if(err) {
                        this.L.err('configureKafkaPublisher', 'Error occured while connecting to redis :: Error', err);
                    }
                    callback(err);
                })
            },
            (callback) => {
                this.L.log('configureKafkaPublisher', 'Going to initialize Kakfa Publisher');
                return this.notify.configureKafkaPublisher(callback);
            },
            (callback) => {
                // initialize kafka consumer
                this.initializeKafkaConsumer(callback);
            }
        ], (error, result) => {
            if(error){
                this.L.error('rechargeNudgeServiceValidationConsumer :: start', 'Error:', error);
                // exiting in case of error
                process.exit(0);
            }
            this.L.info('rechargeNudgeServiceValidationConsumer :: starting', "Kafka consumer initialized.");
        });
    }

    // create pid : operator_label map
    createOperatorLabelMap(cb){
        let
            query   = 'select product_id, operator_label from catalog_vertical_recharge',
            productId, operatorLabel;
        this.dbInstance.exec((err, data) => {
            if (err || !_.isArray(data)) {
                this.L.critical('rechargeNudgeServiceValidationConsumer :: createOperatorLabelMap', 'Error while creating pid:operator_label map', err);
            }
            data.forEach((row) => {
                productId       = _.get(row, 'product_id', null);
                operatorLabel   = _.get(row, 'operator_label', null);
                if(productId && operatorLabel){
                    this.operatorLabelMap[productId] = operatorLabel;
                }
            });
            cb(err);
        }, 'FS_RECHARGE_SLAVE*', query, []);
    }

    // initialize kafka consumer
    initializeKafkaConsumer(cb) {
        //  setup kafka configuration
        this.L.info("Initializing consumer")

        try{
            this.Kafka = new Kafka({
                "brokers"     : _.get(this.config, 'KAFKA.TOPICS.RECHARGE.HOSTS','').split(','),
                "clientId"       : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.validationServiceConsumerKafkaGroupId') + OS.hostname(),
            });

            this.consumer = this.Kafka.consumer({
                groupId:_.get(this.config, 'RECHARGE_NUDGE_CONFIG.validationServiceConsumerKafkaGroupId')
            })

            this.L.info("rechargeNudgeServiceValidationConsumer::initializeKafkaConsumer", "Connecting to kafka consumer");

            this.consumer.connect()
            .then(()=>{
                let topics = _.get(this.config, 'DYNAMIC_CONFIG.RECHARGE_NUDGE.COMMON.LIST_OF_TOPICS', []);
                topics = topics.map(ele => {return '('+ele+')'}).join('|')
                return this.consumer.subscribe({
                    "topic":new RegExp(topics,'g')
                })
            })
            .then(()=>{
                this.consumer.run({
                    eachBatchAutoResolve: false,
                    eachBatch: async ({batch,resolveOffset,heartbeat,commitOffsetsIfNecessary,uncommittedOffsets,isRunning,isStale}) => {
                        let {toBePaused, partitionPausetimeOutInterval} = await this.processKafkaData(batch.messages,resolveOffset, batch.topic,batch.partition);
                        if(toBePaused === true){
                            this.L.info('rechargeNudgeServiceValidationConsumer::initializeKafkaConsumer', "Keeping Paused for topic",   _.get(batch,'topic', '') , "and partition", _.get(batch, 'partition', null), ",Partition pause timeout interval is: ", partitionPausetimeOutInterval, "milliseconds");

                            setTimeout(()=>{
                                this.L.info('rechargeNudgeServiceValidationConsumer::initializeKafkaConsumer', "Resuming Paused topic after timeout", _.get(batch,'topic', '') , "and partition", _.get(batch, 'partition', null));
                                                                
                                this.consumer.resume([{topic :batch.topic , partitions : [batch.partition]}])
                            },partitionPausetimeOutInterval || 10000);
                        } else {
                            this.L.info('rechargeNudgeServiceValidationConsumer::initializeKafkaConsumer', "Resuming Paused topic",   _.get(batch,'topic', '') , "and partition", _.get(batch, 'partition', null));
                            this.consumer.resume([{topic :batch.topic , partitions : [batch.partition]}])
                        }
                    },
                })
                cb()
            }).catch(err=>{
                this.L.error('rechargeNudgeServiceValidationConsumer::initializeKafkaConsumer', 'Error occured while consuming data : error ', err);
            })
        } catch(err) {
            this.L.error('rechargeNudgeServiceValidationConsumer::initializeKafkaConsumer', 'Error occured while initializing kafka consumer : error ', err);
        }
    }

    // process data from kafka
    async processKafkaData(validationData,resolveOffset,topic,partition) {
        
        this.L.info("rechargeNudgeServiceValidationConsumer::processKafkaData", "processing data in kafka for topic:",topic,"partition:",partition)
    
        return new Promise((resolve,reject)=>{
            try{
                if (!_.isEmpty(validationData) && _.isArray(validationData)) {
                    // pause consumer
                    this.consumer.pause([{topic,partitions:[partition]}]);

                    this.L.info("rechargeNudgeServiceValidationConsumer::processKafkaData", "Kafka consumer paused until the current batch processing gets complete and Consumer paused for topic:",topic,"partition:",partition)

                    utility._sendMetricsToDD(validationData.length, [
                        'STATUS:CONSUMED',
                        'TOPIC:' + topic,
                        'REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER'
                    ])
                    this.L.info('rechargeNudgeServiceValidationConsumer :: processKafkaData', 'Validation data length:', validationData.length, 'batch:', ++this.batchNumber);
                    // prepare sourceArray containing all valid validation records
                    const {sourceArray,toBePaused,offset,partitionPausetimeOutInterval} = this.validateValidationRecords(validationData);
                    // process sourceArray, check recharge and notification sent data from redis and accordingly sned notification for all records
                    this.L.info("rechargeNudgeServiceValidationConsumer::processKafkaData", "Records are ready to be send with offset value:", offset);

                    this.distributeDataForProcessing(sourceArray, () => {
                        offset && resolveOffset(offset);
                        resolve({toBePaused, partitionPausetimeOutInterval});
                    });
                } else {
                    this.L.error('rechargeNudgeServiceValidationConsumer :: processKafkaData', 'Invalid validation data found', validationData); 
                    resolve(false)   
                }
            } catch(error){
                this.L.critical('rechargeNudgeServiceValidationConsumer :: processKafkaData', 'Error:', error);
                reject(error)
            }
        })
    }

    // validate validation records
    validateValidationRecords(validationData){
        let 
            sourceArray = [],
            valueData,
            initTimeStamp,
            initTimeStampOriginal, 
            customerId, 
            userDataRechargeNumber, 
            productId, 
            service, 
            dueAmount, 
            rechargeAmount,
            toBePaused=false, 
            offset = null, 
            partitionPausetimeOutInterval = null, 
            categoryId = null,
            dropOffTime = null,
            validationDataObj = {};
        // iterate all records and insert valid records in sourceArray
        for(let i = 0; i < validationData.length; i++){
            valueData = _.get(validationData[i], 'value', null).toString();
            if(valueData && typeof(valueData) == 'string'){
               
                try{
                    // parse value inside
                    valueData = JSON.parse(valueData);
                } catch(err){
                    this.L.error('rechargeNudgeServiceValidationConsumer :: validateValidationRecords', 'Error parsing validation data:', valueData);
                    // ignoring this record and processing next one
                    continue;
                }
                // updating validationData value
                _.set(validationData[i], 'value', valueData);

                //Updating PID for rerouting changes 
                let originalPid = _.get(valueData, 'originalPid', null);

                this.L.info("rechargeNudgeServiceValidationConsumer:: validateValidationRecords updating PID:- catalogProductID, originalPid ",_.get(valueData, 'catalogProductID', ''), originalPid);
                let catalogProductId = this.activePidLib.getActivePID(originalPid || _.get(record, 'value.catalogProductID', null));

                 _.set(valueData, "catalogProductID", catalogProductId);

                // check required condition
                customerId              = _.get(valueData, 'customerInfo_customer_id', '');
                productId               = _.get(valueData, 'catalogProductID', '');
                userDataRechargeNumber  = _.get(valueData, 'userData_recharge_number', '')+'';
                service                 = typeof(_.get(valueData, 'productInfo_service', null)) === 'string' ? _.get(valueData, 'productInfo_service').toLowerCase() : '';
                dueAmount               = _.get(valueData, 'dueAmount', '');
                initTimeStampOriginal   = _.get(valueData, 'timestamps_init', '');
                initTimeStamp           = initTimeStampOriginal ? (MOMENT(initTimeStampOriginal).isValid() ? MOMENT(initTimeStampOriginal) : null) : null;
                categoryId = _.get(valueData, 'productInfo_category_id', null);
                
                /** Recharge amount for prepaid recharges */
                rechargeAmount = _.get(valueData,'userData_amount',0);

                this.L.info("rechargeNudgeServiceValidationConsumer::validateValidationRecords", " Records in process ", customerId, userDataRechargeNumber , initTimeStamp , this.enabledServices[service],customerId,userDataRechargeNumber);

                // dropOffTime = MOMENT().subtract(20, 'minute');
                // Here taking buffer of 1 minute i.e 60 seconds
                dropOffTime = MOMENT().subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'RECHARGE_NUDGE', categoryId, 'TIME'], 10), 'minute')
                if(this.enabledServices[service] && initTimeStamp > dropOffTime && parseInt(MOMENT.duration(initTimeStamp.diff(dropOffTime)).asMilliseconds()) > 60000){
                    this.L.info('rechargeNudgeServiceValidationConsumer::validateValidationRecords','record found fresher than', _.get(this.config, ['DYNAMIC_CONFIG', 'RECHARGE_NUDGE', categoryId, 'TIME'], 10), 'minute')

                    // this.L.info('rechargeNudgeServiceValidationConsumer::validateValidationRecords','record found fresher than 20 minutes')
                    toBePaused =true;
                    offset = i > 0 ? _.get(validationData[i-1], 'offset', null) : null;
                    partitionPausetimeOutInterval = this.getPartitionPauseTimeOutInterval(i, validationData, dropOffTime);
                    break;
                }
                let debugKey = `rech:${userDataRechargeNumber}::custId:${customerId}`;
                if(this.enabledServices[service] && (this.validateAmount(valueData,debugKey)) && customerId && !isNaN(customerId) && customerId != 0 && productId && !isNaN(productId) && userDataRechargeNumber && initTimeStamp > MOMENT(dropOffTime).subtract(4, 'hours')){
                    if(validationDataObj[productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase()]) {
                        this.L.log("rechargeNudgeServiceValidationConsumer::validateValidationRecords", "Not checking validation data for", productId, customerId, userDataRechargeNumber, "since record was found earlier");
                    } else {
                        let channel_id = _.get(valueData , "customerInfo_channel_id" , null)
                        this.L.verbose("rechargeNudgeServiceValidationConsumer::validateValidationRecords",debugKey,"channel ID:",channel_id)
                        if (channel_id == "digital-reminder") {
                            this.L.info("rechargeNudgeServiceValidationConsumer::validateValidationRecords:",debugKey,`Channel_id: ${channel_id} is disabled`);
                        }
                        else{
                            sourceArray.push(validationData[i]);
                            validationDataObj[productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase()] = true;
                        }
                      
                    }
                    offset = _.get(validationData[i], 'offset', null);
                }
            }
        }
        if(_.isEmpty(sourceArray) && !offset && !toBePaused) {
            this.L.info('rechargeNudgeServiceValidationConsumer::validateValidationRecords', 'source array is empty, maybe data is older or unknown service :: service :', service);
            offset = _.isArray(validationData) && _.get(validationData[validationData.length - 1], 'offset', null);
        }
        return {sourceArray,toBePaused,offset,partitionPausetimeOutInterval};
    }

    getPartitionPauseTimeOutInterval(index, validationData, dropOffTime) {
        // here minTime is used to find the minimum timestamp of records.
        let valueData = null, minTime = MOMENT().add(50, 'minute'), initTimeStamp = null;
        _.set(validationData[index], 'value', JSON.stringify(_.get(validationData[index], 'value', null) ));
        for(let i = index; i < validationData.length; i++) {
            valueData = _.get(validationData[i], 'value', null).toString();
            if(valueData && typeof(valueData) == 'string'){
                try{
                    // parse value inside
                    valueData = JSON.parse(valueData);
                } catch(err){
                    this.L.error('rechargeNudgeServiceValidationConsumer :: getPartitionPauseTimeOutInterval', 'Error parsing validation data:', valueData);
                    // ignoring this record and processing next one
                    continue;
                }

                initTimeStamp = _.get(valueData, 'timestamps_init', '') ? (MOMENT(_.get(valueData, 'timestamps_init', '')).isValid() ? MOMENT(_.get(valueData, 'timestamps_init', '')) : null) : null;

                minTime = minTime > initTimeStamp ? initTimeStamp : minTime;
            }
        }
        return parseInt(MOMENT.duration(minTime.diff(dropOffTime)).asMilliseconds());
    }

    validateAmount(valueData,debugKey='DebugKeyNotPassed',prepaid=false) {
        this.L.info('rechargeNudgeServiceValidationConsumer :: validateAmount', 'validating amount');

        let self = this,isPrepaid=false;
        if(_.get(valueData , 'productInfo_paytype','') === 'prepaid')isPrepaid=true;
        if(isPrepaid){
            return _.get(valueData,'userData_amount',0) > 0 
        }
        let operator = typeof(_.get(valueData, 'productInfo_operator', null)) === 'string' ? _.get(valueData, 'productInfo_operator').toLowerCase().trim() : '';
        let validationSuccess = _.get(valueData, 'validationSuccessful', null);
        let dueAmount = _.get(valueData, 'userData_price', 0) ? _.get(valueData, 'userData_price', 0) : _.get(valueData, 'dueAmount', 0);
        if (validationSuccess && dueAmount > 0) {
            self.L.info('validateAmount:: ', `record:${debugKey} operator:${operator} validationResponse:${validationSuccess}`);
            return true;
        }
        return false;
    }

    // distribute records for further processing
    distributeDataForProcessing(sourceArray, cb){
        if(sourceArray.length == 0){
            // return if no data is present
            return cb();
        }
        // iterate for each record in parallel
        ASYNC.each(
            sourceArray,
            (record, next) => {
                this.processRecordForNotification(record, next);
            },
            (err, result) => {
                if(err){
                    this.L.error('rechargeNudgeServiceValidationConsumer :: distributeDataForProcessing', 'Error while processing record for notification', err);
                }
                // this cb will now update offset
                cb();
            }
        );
    }

    // process data further to check if recharge is done or notification is sent and accordingly send notification
    processRecordForNotification(record, cb) {
        let 
            customerId              = _.get(record, 'value.customerInfo_customer_id', ''),
            productId               = _.get(record, 'value.catalogProductID', ''),
            userDataRechargeNumber  = _.get(record, 'value.userData_recharge_number', '')+'',
            initTimeStamp           = MOMENT(_.get(record, 'value.timestamps_init', '')),
            rechargeKey             = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.rechargeRedisPrefix', 'RNRC_') + productId + (_.get(record, 'value.productInfo_service', null).toLowerCase() == 'financial services' ? ':' + customerId + ':' : ':') + userDataRechargeNumber.toLowerCase(),
            notificationKey         = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.notificationRedisPrefix', 'RNN_') + productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase(),
            // flag to check if notification is to be sent or not
            notificationFlag        = true,
            service                 = _.get(record, 'value.productInfo_service', null) && _.get(record, 'value.productInfo_service', '').toUpperCase();

        this.L.info('rechargeNudgeServiceValidationConsumer :: processRecordForNotification', 'processing records for notification');

        ASYNC.waterfall([
            (callback) => {
                // check for recharge data in redis
                this.L.info('rechargeNudgeServiceValidationConsumer :: processRecordForNotification', 'checking redis for recharge key:', rechargeKey);
                this.redis.getData((err, redisData) => {
                    if(err){
                        callback(err);
                    } else{
                        if(redisData && MOMENT(redisData).isAfter(initTimeStamp)) {
                            this.L.info('rechargeNudgeServiceValidationConsumer :: processRecordForNotification', 'data found in redis for recharge key:', rechargeKey);
                            // in case of error or if validation timestamp is less than recharge timestamp from redis
                            notificationFlag = false;
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER'+service, 'STATUS:PAYMENT_DONE']);
                        }
                        callback();
                    }
                }, {key: rechargeKey});
            },
            (callback) => {
                if(notificationFlag == false){
                    // notification is not to be sent
                    callback();
                } else{
                    // check for notification data in redis
                    this.L.info('rechargeNudgeServiceValidationConsumer :: processRecordForNotification', 'checking redis for notification key:', notificationKey);
                    this.redis.getData((err, redisData) => {
                        if(err){
                            callback(err);
                        } else{
                            if(redisData){
                                this.L.info('rechargeNudgeServiceValidationConsumer :: processRecordForNotification', 'data found in redis for notification key:', notificationKey);
                                notificationFlag = false;
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER'+service, 'STATUS:NOTIFICATION_ALREADY_SENT']);
                            }
                            callback();
                        }
                    }, {key: notificationKey});
                }
            },
            (callback) => {
                if(notificationFlag){
                    // send notification to user
                    this.sendNotification(record, callback);
                } else{
                    callback();
                }
            },
            (callback) => {
                if(notificationFlag){
                    // set notification data in redis
                    this.redis.setData((err, data) => {
                        err ? callback(err) : callback();
                    }, {
                        key     : notificationKey,
                        value   : true,
                        ttl     : this.config.RECHARGE_NUDGE_CONFIG.notificationDataRedisTTL
                    })
                } else{
                    callback();
                }
            }
        ], (error, result) => {
            if (error) {
                this.L.error('rechargeNudgeServiceValidationConsumer :: processRecordForNotification', 'Error:', error, 'recharge key:', rechargeKey, 'notification key: ', notificationKey);
            }
            // this cb will run next() of distributeDataForProcessing function, not passing error in cb to process all the records
            cb();
        });
    }

    sendNotification(record, callback) {
        let self = this,
            attributes = null;
        try{
            attributes = JSON.parse(_.get(self.config, ['CVR_DATA', _.get(record, 'value.catalogProductID', null), 'attributes'] , '{}'))
        } catch(err){
            this.L.error('rechargeNudgeServiceValidationConsumer :: sendNotification', 'Error parsing CVR data:', err);
        }
        let payLoad = {
            amount: _.get(record, 'value.userData_price', 0) ? _.get(record, 'value.userData_price', 0) : _.get(record, 'value.dueAmount', 0),
            recharge_number: _.get(record, 'value.userData_recharge_number', null),
            operator: _.get(self.operatorLabelMap, _.get(record, 'value.catalogProductID', null), null),
            category_id: _.get(record, 'value.productInfo_category_id', null),
            service: _.get(record, 'value.productInfo_service', null),
            bankName: _.get(attributes, ['bank_code'] , ''),
            cardNetwork : _.get(attributes, ['card_network'], ''),
            card_last_four_digits : _.get(record, 'value.userData_recharge_number', '').substr(-4)
        };
        let notificationRecord = {
            type: 'PUSH',
            recipients: _.get(record, 'value.customerInfo_customer_id', null),
            template_id: _.get(self.config, ['DYNAMIC_CONFIG', 'RECHARGE_NUDGE', _.get(payLoad, 'category_id'), 'TEMPLATE_ID'], _.get(self.config, ['NOTIFICATION', 'RECHARGE_NUDGE_TEMPLATE_ID', 'DEFAULT', 'd'], null)),
        };

        if (!_.get(notificationRecord, 'template_id', null)) {
            return callback('Template_id is null');
        } else if (_.get(notificationRecord, 'recipients', null) != null) {
            let
                notificationData = {
                    "template_type": _.get(notificationRecord, 'type', 'PUSH').toLowerCase(),
                    "template_id": _.get(notificationRecord, 'template_id', null),
                    "options": {
                        "notificationOpts": {
                            "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                        },
                        "type": "async",
                        "data": payLoad
                    }
                };
            let url_type = "external";
            let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], "utility");
            let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
            let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null) ;
            
            let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;

            let deepLinkData = {
                    "channel_id"    : "both",
                    "deepLinkObj"   : {
                        "extra"     : {
                            "url"       : url + "?product_id=" + _.get(record, 'value.catalogProductID', null) + "$recharge_number=" + _.get(record, 'value.userData_recharge_number', '').split(" ").join("") + "$amount=" + (_.get(record, 'value.userData_price', 0) ? _.get(record, 'value.userData_price', 0) : _.get(record, 'value.dueAmount', 0)),
                            "url_type"  : url_type
                        }
                    },
                    "noRich": false
                };

            _.extend(notificationData.options.notificationOpts,deepLinkData);

            // We are waiting till morning for notification adding minimum hours to current time
            let
                date            = new Date(),
                timeToSend      = date.getHours(),
                sendAt          = null,
                timeRange       = {
                    gte         : _.get(self.config, 'RECHARGE_NUDGE_CONFIG.blockedNotificationStartHour', 0),
                    lte         : _.get(self.config, 'RECHARGE_NUDGE_CONFIG.blockedNotificationEndHour', 7)
                };

            // We are sending notification just after 7+ extra hours after 00:00 AM
            if (timeToSend >= timeRange.gte && timeToSend < timeRange.lte) {
                sendAt = MOMENT().add(timeRange.lte, "hours").format("YYYY-MM-DD HH:mm:ss");
            } else {
                sendAt = MOMENT().format("YYYY-MM-DD HH:mm:ss");
            }

            let body = {
                "source_id"         : 3,
                "category_id"       : 3,
                "recharge_number"   : _.get(record, 'value.userData_recharge_number', null),
                "product_id"        :  _.get(record, 'value.catalogProductID', null),
                "max_retry_count"   : 2,
                "retry_interval"    : 30,
                "type"              : _.get(notificationRecord, 'type', null),
                "template_id"       : _.get(notificationRecord, 'template_id', null),
                "recipient"         : _.get(notificationRecord, 'recipients', null),
                "send_at"           : sendAt,
                "data"              : notificationData,
                "rules": {
                    "condition": `category_id=3 and source_id=3 and recharge_number='${_.get(record, 'value.userData_recharge_number', null)}' and product_id=${_.get(record, 'value.catalogProductID', null)}
                            and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)} and recipient='${_.get(record, 'value.customerInfo_customer_id', null)}'`,
                    "actions": [
                        {
                            "status": "pending",
                            "action": "drop"
                        },
                        {
                            "status": "sent",
                            "action": "drop"
                        },
                        {
                            "status": "error",
                            "action": "drop"
                        }
                    ]
                }
            }

            self.L.info('rechargeNudgeServiceValidationConsumer :: sendNotification', 'Sending notification with notification data:', JSON.stringify(body));
            self.notify.__createNotification(function (error, data) {
                if (error) {
                    self.L.error("rechargeNudgeServiceValidationConsumer :: sendNotification", 'Error in sending ' + _.get(notificationRecord, 'type', null) + ' notification to the recipient: ' + _.get(notificationRecord, 'recipients', null) + ' ' + error);
                    callback(error);
                } else {
                    self.L.log("rechargeNudgeServiceValidationConsumer :: sendNotification", _.get(notificationRecord, 'type', null) + ' notification process done for the recipient: ' + _.get(notificationRecord, 'recipients', null) + ' ' + JSON.stringify(body));
                    callback();
                }
            }, body);
        } else {
            // recipients not found, sending error
            callback("Recipients not found while sending notification");
        }
    }
}

export default rechargeNudgeServiceValidationConsumer;
