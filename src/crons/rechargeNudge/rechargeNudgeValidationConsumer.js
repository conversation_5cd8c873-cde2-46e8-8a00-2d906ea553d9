/*jshint multistr: true ,node: true*/
"use strict";

import _                from 'lodash'
import OS               from 'os'
import MOMENT           from 'moment'
import ASYNC            from 'async'
import FS               from 'fs'
import PATH             from 'path'
import REQUEST          from 'request'
import <PERSON><PERSON><PERSON><PERSON><PERSON>        from 'validator'
import utility from '../../lib'
import notify from '../../services/notify'

/**
 * @class
 * Recharge nudge validation consumer is a cron to fetch validation data from kafka, check its corresponding recharge data from cache and send PUSH notification to user accordingly
 */

class RechargeNudgeValidationConsumer {
    /**
     * Configure specifications
     * @param { object } options Contains configuration and dependencies
     */
    constructor(options) {
        this.L                      = options.L;
        this.infrautils             = options.INFRAUTILS;
        this.config                 = options.config;
        this.mode                   = options.mode;
        this.dbInstance             = options.dbInstance;
        this.operatorLabelMap       = {};
        this.notify                 = new notify(options);
        // initializing redis
        this.redis                  = new this.infrautils.cache("REDIS", this.config.REDIS);
        // used for logging purpose
        this.batchNumber            = 0;
        /* validationDataObj object will store pid:customerId:rechargeNumber as key and true as value.
        This is done to check if this combination has been used for checking recharge data.
        */
        this.validationDataObj      = {};
        // message to be commited in kafka
        this.offsetMessage          = null;
        // flag to decide if more data is to be fetched from kafka
        this.exitFlag               = false;
        this.enabledServices = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.enabledServices', {});
        this.nudgeOnValidationSuccess = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.nudgeOnValidationSuccess', {});
        // setting path
        this.lockFilePath           = PATH.join(__dirname, _.get(this.config, 'RECHARGE_NUDGE_CONFIG.lockFileName.'+ this.mode, ''));
        // setting file path for fetching start time
        this.endTimeFilePath        = PATH.join(__dirname, _.get(this.config, 'RECHARGE_NUDGE_CONFIG.endTimeFileName.'+ this.mode, ''));
        // time format
        this.fileTimeFormat         = 'YYYY-MM-DD HH:mm:ss';
        // setting extended offset message and flag for record in extended period
        this.extendedOffsetMessage  = null;
        this.extendedOffsetStatus   = false;
        // check prerequisites to run the consumer
        this.checkPrerequisites();
        // set startTime and endTime intervals
        this.setTimeInterval();
    }

    /**
     * Starting point of service
     */
    start() {
        /**
         * Flow of execution:
         * 1. Create operator label map, connect with redis cache and initialize kafka consumer.
         * 2. Fetch validation data from kafka in chunks and check if notification is to be sent
         * 3. Send notification
         */
        ASYNC.waterfall([
            (callback) => {
                // create pid : operator_label map
                this.createOperatorLabelMap(callback);
            },
            (callback) => {
                // connect with redis server
                this.redis.connect((err, data) => {
                    callback(err);
                })
            },
            (callback) => {
                // initialize kafka consumer
                this.initializeKafkaConsumer(callback);
            }
        ], (error, result) => {
            if(error){
                this.L.error('rechargeNudgeValidationConsumer :: start', 'Error:', error);
                // deleting lock file
                this.deleteMarkerFile();
                // exiting in case of error
                process.exit(0);
            }
            this.L.info('rechargeNudgeValidationConsumer :: start', "Kafka consumer initialized.");
        });
    }

    // create pid : operator_label map
    createOperatorLabelMap(cb){
        let
            query   = 'select product_id, operator_label from catalog_vertical_recharge',
            productId, operatorLabel;
        this.dbInstance.exec((err, data) => {
            if (err || !_.isArray(data)) {
                this.L.critical('rechargeNudgeValidationConsumer :: createOperatorLabelMap', 'Error while creating pid:operator_label map', err);
            }
            data.forEach((row) => {
                productId       = _.get(row, 'product_id', null);
                operatorLabel   = _.get(row, 'operator_label', null);
                if(productId && operatorLabel){
                    this.operatorLabelMap[productId] = operatorLabel;
                }
            });
            cb(err);
        }, 'FS_RECHARGE_SLAVE*', query, []);
    }

    // check if run mode is valid
    checkMode(){
        return this.config.RECHARGE_NUDGE_CONFIG.availableValidationmodes[this.mode];
    }

    // check if another cron is already running
    checkMarkerFileExists(){
        return FS.existsSync(this.lockFilePath);
    }

    // create marker file to avoid multiple cron runs
    createMarkerFile(){
        try{
            FS.closeSync(FS.openSync(this.lockFilePath, 'w'));
            this.L.log("rechargeNudgeValidationConsumer :: createMarkerFile", "Created marker file");
            return true;
        } catch(e){
            this.L.critical("rechargeNudgeValidationConsumer :: createMarkerFile", "Failure while creating marker file");
            return false;
        }
    }

    // delete marker file
    deleteMarkerFile(){
        try{
            if(FS.existsSync(this.lockFilePath)){
                FS.unlinkSync(this.lockFilePath);
                this.L.log("rechargeNudgeValidationConsumer :: deleteMarkerFile", "Deleted marker file");
            } else{
                this.L.error("rechargeNudgeValidationConsumer :: deleteMarkerFile", "Marker file is not present");
            }
        } catch(e){
            this.L.critical("rechargeNudgeValidationConsumer :: deleteMarkerFile", "Failure while deleting marker file", e);
            return false;
        }
        return true;
    };

    // check if mode is correct and marker file is not present
    checkPrerequisites(){
        try{
            // check valid run mode
            if(this.checkMode()){
                this.L.info("rechargeNudgeValidationConsumer :: checkPrerequisites", "Running validation consumer for mode:", this.mode);
            } else{
                throw this.mode + ' mode not available. Available modes are "d" and "d+2"';
            }
            // check concurrent crons for same mode are not running
            if(this.checkMarkerFileExists()){
                throw "Another instance of cron is already running";
            }
            if(!this.createMarkerFile()){
                throw "Error creating marker file";
            }
        } catch(err){
            this.L.critical("rechargeNudgeValidationConsumer :: checkPrerequisites", err);
            process.exit(0);
        }
    }

    // set start and end time
    setTimeInterval(){
        // fetch start time from file
        this.startTime   = this.fetchStartTime();
        if(this.mode == 'd+2'){
            if(!this.startTime){
                this.startTime = MOMENT().subtract(2, 'days').startOf("day");
            }
            this.endTime = this.startTime.clone().add(1, "days");
        } else{
            if(!this.startTime){
                this.startTime = MOMENT().subtract(60, 'minutes');
            }
            this.endTime = this.startTime.clone().add(30, 'minutes');
        }
        
        // setting extended time due to data in different kafka partitions
        this.extendedEndTime = this.endTime.clone().add(5, 'minutes');
        // logging time
        this.L.info('rechargeNudgeValidationConsumer :: setTimeInterval', "Setting start and end timestamps", this.startTime.format("YYYY-MM-DD HH:mm:ss"), this.endTime.format("YYYY-MM-DD HH:mm:ss"));
    }

    // fetch start time from file
    fetchStartTime(){
        if(FS.existsSync(this.endTimeFilePath)){
            try{
                let startTime = FS.readFileSync(this.endTimeFilePath, 'utf8');
                startTime = startTime ? MOMENT(startTime, this.fileTimeFormat, true) : null;
                return startTime ? (startTime.isValid() ? startTime : null) : null;
            } catch(err){
                this.L.critical('rechargeNudgeValidationConsumer :: fetchStartTime', "Error fetching data from file", err);
                return null;
            }
        } else{
            return null;
        }
    }

    // set end time in file
    setEndTime(){
        try{
            FS.writeFileSync(this.endTimeFilePath, this.endTime.format(this.fileTimeFormat));
        } catch(err){
            this.L.critical('rechargeNudgeValidationConsumer :: setEndTime', 'Error setting end time in file', err);
            // delete file in case there is any error
            if(FS.existsSync){
                try{
                    FS.unlinkSync(this.endTimeFilePath);
                } catch(error){
                    this.L.critical('rechargeNudgeValidationConsumer :: setEndTime', 'Error deleting time file', error);
                }
            }
        }
    }

    // initialize kafka consumer
    initializeKafkaConsumer(cb) {
        // setup kafka configuration
        this.consumer = new this.infrautils.kafka.consumer({
            "kafkaHost"     : _.get(this.config, 'KAFKA.TOPICS.RECHARGE.HOSTS'),
            "groupId"       : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.validationConsumerKafkaGroupId.' + this.mode, '') + OS.hostname(),
            "topics"        : _.get(this.config, 'KAFKA.SERVICES.RECHARGE_NUDGE_CONSUMERS.VALIDATION_TOPICS'),
            "id"            : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.validationConsumerKafkaId.' + this.mode, ''),
            "fromOffset"    : "earliest",
            "batchSize"     : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.kafkaBatchSize', 500),
            "autoCommit"    : false
        });
        // initialize consumer and bind it to processKafkaData function for further processing
        this.consumer.initConsumer(this.processKafkaData.bind(this), cb);
    }

    // process data from kafka
    processKafkaData(validationData) {
        try{
            let sourceArray = [];
            if (!_.isEmpty(validationData) && _.isArray(validationData)) {
                // pause consumer
                this.consumer._pauseConsumer();
                utility._sendMetricsToDD(validationData.length, [
                    'STATUS:CONSUMED',
                    'TOPIC:' + _.get(this.config, 'KAFKA.SERVICES.RECHARGE_NUDGE_CONSUMERS.VALIDATION_TOPICS'),
                    'REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER',
                    'MODE:' + this.mode
                ])
                this.L.info('rechargeNudgeValidationConsumer :: processKafkaData', 'Validation data length:', validationData.length, 'batch:', ++this.batchNumber);
                // prepare sourceArray containing all valid validation records
                sourceArray = this.validateValidationRecords(validationData);
                // process sourceArray, check recharge and notification sent data from redis and accordingly sned notification for all records
                this.distributeDataForProcessing(sourceArray, () => {
                    this.processMessageOffset();
                });
            } else {
                this.L.error('rechargeNudgeValidationConsumer :: processKafkaData', 'Invalid validation data found', validationData);    
            }
        } catch(error){
            this.L.critical('rechargeNudgeValidationConsumer :: processKafkaData', 'Error:', error);
            this.exitFlag = true;
            this.handleRecursiveKafkaCalls();
        }
    }

    // validate validation records
    validateValidationRecords(validationData){
        let 
            sourceArray = [],
            valueData, initTimeStamp, initTimeStampOriginal, customerId, userDataRechargeNumber, productId, service, index, dueAmount, extendedIndex;
        // iterate all records and insert valid records in sourceArray
        for(let i = 0; i < validationData.length; i++){
            valueData = _.get(validationData[i], 'value', null);
            if(valueData && typeof(valueData) == 'string'){
                try{
                    // parse value inside
                    valueData = JSON.parse(valueData);
                } catch(err){
                    this.L.error('rechargeNudgeValidationConsumer :: processKafkaData', 'Error parsing validation data:', valueData);
                    // ignoring this record and processing next one
                    continue;
                }
                // updating validationData value
                _.set(validationData[i], 'value', valueData);

                //Updating PID for rerouting changes 
                let originalPid = _.get(valueData, 'originalPid', null);

                this.L.info("rechargeNudgeValidationConsumer: updating PID:- catalogProductID, originalPid ",
                 _.get(valueData, 'catalogProductID', ''), originalPid);

                 _.set(valueData, "catalogProductID", originalPid || _.get(valueData, 'catalogProductID',''));

                // check required condition
                customerId              = _.get(valueData, 'customerInfo_customer_id', '');
                productId               = _.get(valueData, 'catalogProductID', '');
                userDataRechargeNumber  = _.get(valueData, 'userData_recharge_number', '')+'';
                service                 = typeof(_.get(valueData, 'productInfo_service', null)) === 'string' ? _.get(valueData, 'productInfo_service').toLowerCase() : '';
                dueAmount               = _.get(valueData, 'dueAmount', '');
                initTimeStampOriginal   = _.get(valueData, 'timestamps_init', '');
                initTimeStamp           = initTimeStampOriginal ? (MOMENT(initTimeStampOriginal).isValid() ? MOMENT(initTimeStampOriginal) : null) : null;
                
                this.L.info("rechargeNudgeValidationConsumer:", " Records in process ", customerId, userDataRechargeNumber);
                let debugKey = `rech:${userDataRechargeNumber}::custId:${customerId}`;
                if(initTimeStamp && initTimeStamp.isBetween(this.startTime, this.endTime, null, '[]') && this.enabledServices[service] && (this.validateAmount(valueData,debugKey)) && customerId && !isNaN(customerId) && customerId != 0 && productId && !isNaN(productId) && userDataRechargeNumber){
                    // check if the same validation data has been processed
                    if(this.validationDataObj[productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase()]){
                        this.L.log("rechargeNudgeValidationConsumer :: processKafkaData", "Not checking recharge data for", productId, customerId, userDataRechargeNumber, "since record was found earlier");
                    } else{
                        // push data in source array
                        sourceArray.push(validationData[i]);
                        // add pid:customerId:rechargeNumber in validationDataObj
                        this.validationDataObj[productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase()] = true;
                    }
                } else if(initTimeStamp && initTimeStamp.isBetween(this.endTime, this.extendedEndTime, null, '(]')){
                    // record found in extended period
                    if(!this.extendedOffsetMessage){
                        extendedIndex = i - 1;
                        if(extendedIndex < 0){
                            // first message of batch found in extended period, stop any other message commits from now onwards
                            this.extendedOffsetStatus = true;
                        } else{
                            // found a message in extended period, store the previous message for commiting
                            this.extendedOffsetMessage = validationData[extendedIndex];
                        }
                    }
                } else if(initTimeStamp && initTimeStamp.isAfter(this.extendedEndTime)){
                    // setting exit flag since a record is found with later timestamp
                    this.exitFlag = true;
                    // setting index to one index less
                    index = i - 1;
                    break;
                } else {
                    let initTimeStampCondition = initTimeStamp && initTimeStamp.isBetween(this.startTime, this.endTime, null, '[]');
                    this.L.info('rechargeNudgeValidationConsumer :: processKafkaData', 'No condition matched, skipping record', _.get(validationData[i], 'topic'), _.get(validationData[i], 'offset'), customerId, productId, userDataRechargeNumber, service, initTimeStampOriginal,initTimeStampCondition, 'dueAmount: ',dueAmount, this.enabledServices[service]);
                }
            } else {
                this.L.info('rechargeNudgeValidationConsumer :: processKafkaData', 'Record not valid, not checking it further', _.get(validationData[i], 'topic'), _.get(validationData[i], 'offset'));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER_INVALID_DATA', 'STATUS:ERROR']);
            }
        }
        // set offset message value
        if(this.exitFlag){
            // setting offset as null in case index is negative
            this.offsetMessage = index < 0 ? null : validationData[index];
        } else{
            this.offsetMessage = validationData.length ? validationData[validationData.length - 1] : null;
        }
        return sourceArray;
    }

    validateAmount(valueData,debugKey='DebugKeyNotPassed') {
        let self = this;
        let operator = typeof(_.get(valueData, 'productInfo_operator', null)) === 'string' ? _.get(valueData, 'productInfo_operator').toLowerCase().trim() : '';
        let validationSuccess = _.get(valueData, 'validationSuccessful', null);
        let dueAmount = _.get(valueData, 'dueAmount', '');
        if (_.get(self.nudgeOnValidationSuccess,operator,null)) {
            self.L.info('validateAmount:: ', `record:${debugKey} operator:${operator} validationResponse:${validationSuccess}`);
            if (validationSuccess) return true;
        }

        return dueAmount > 0;
    }

    // distribute records for further processing
    distributeDataForProcessing(sourceArray, cb){
        if(sourceArray.length == 0){
            // return if no data is present
            return cb();
        }
        // iterate for each record in parallel
        ASYNC.each(
            sourceArray,
            (record, next) => {
                this.processRecordForNotification(record, next);
            },
            (err, result) => {
                if(err){
                    this.L.error('rechargeNudgeValidationConsumer :: distributeDataForProcessing', 'Error while processing record for notification', err);
                }
                // this cb will now update offset
                cb();
            }
        );
    }

    // process data further to check if recharge is done or notification is sent and accordingly send notification
    processRecordForNotification(record, cb){
        let 
            customerId              = _.get(record, 'value.customerInfo_customer_id', ''),
            productId               = _.get(record, 'value.catalogProductID', ''),
            userDataRechargeNumber  = _.get(record, 'value.userData_recharge_number', '')+'',
            initTimeStamp           = MOMENT(_.get(record, 'value.timestamps_init', '')),
            rechargeKey             = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.rechargeRedisPrefix', 'RNRC_') + productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase(),
            notificationKey         = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.notificationRedisPrefix', 'RNN_') + productId + ":" + customerId + ":" + userDataRechargeNumber.toLowerCase(),
            // flag to check if notification is to be sent or not
            notificationFlag        = true;

        ASYNC.waterfall([
            (callback) => {
                // check for recharge data in redis
                this.redis.getData((err, redisData) => {
                    if(err){
                        callback(err);
                    } else{
                        if(redisData && MOMENT(redisData).isAfter(initTimeStamp)){
                            // in case of error or if validation timestamp is less than recharge timestamp from redis
                            notificationFlag = false;
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER', 'STATUS:PAYMENT_DONE']);
                        }
                        callback();
                    }
                }, {key: rechargeKey});
            },
            (callback) => {
                if(notificationFlag == false){
                    // notification is not to be sent
                    callback();
                } else{
                    // check for notification data in redis
                    this.redis.getData((err, redisData) => {
                        if(err){
                            callback(err);
                        } else{
                            if(redisData){
                                notificationFlag = false;
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_VALIDATION_CONSUMER', 'STATUS:NOTIFICATION_ALREADY_SENT']);
                            }
                            callback();
                        }
                    }, {key: notificationKey});
                }
            },
            (callback) => {
                if(notificationFlag){
                    // send notification to user
                    this.sendNotification(record, callback);
                } else{
                    callback();
                }
            },
            (callback) => {
                if(notificationFlag){
                    // set notification data in redis
                    this.redis.setData((err, data) => {
                        err ? callback(err) : callback();
                    }, {
                        key     : notificationKey,
                        value   : true,
                        ttl     : this.config.RECHARGE_NUDGE_CONFIG.notificationDataRedisTTL
                    })
                } else{
                    callback();
                }
            }
        ], (error, result) => {
            if (error) {
                this.L.error('rechargeNudgeValidationConsumer :: processRecordForNotification', 'Error:', error, 'recharge key:', rechargeKey);
            }
            // this cb will run next() of distributeDataForProcessing function, not passing error in cb to process all the records
            cb();
        });
    }

    // send notification
    sendNotification(record, callback) {
        
         let payLoad = {
                amount: _.get(record, 'value.dueAmount', null),
                recharge_number: _.get(record, 'value.userData_recharge_number', null),
                operator: _.get(this.operatorLabelMap, _.get(record, 'value.catalogProductID', null), null),
                category_id: _.get(record, 'value.productInfo_category_id', null),
                service: _.get(record, 'value.productInfo_service', null)
            };
        let notificationRecord = {
            type: 'PUSH',
            recipients: _.get(record, 'value.customerInfo_customer_id', null),
            template_id: _.get(this.config, ['OPERATOR_TEMPLATE_MAPPING', _.get(payLoad, 'operator'), 'RECHARGE_NUDGE_TEMPLATE',this.mode],
                _.get(this.config, ['NOTIFICATION', 'RECHARGE_NUDGE_TEMPLATE_ID', _.get(payLoad, 'service',''), this.mode],
                    _.get(this.config, ['NOTIFICATION', 'RECHARGE_NUDGE_TEMPLATE_ID', 'DEFAULT', this.mode], null)))
        };

        if (!_.get(notificationRecord, 'template_id', null)) {
            return callback('Template_id is null');
        }

        else if (_.get(notificationRecord, 'recipients', null) != null) {
            let
                notificationData = {
                    "template_type": _.get(notificationRecord, 'type', 'PUSH').toLowerCase(),
                    "template_id": _.get(notificationRecord, 'template_id', null),
                    "options": {
                        "notificationOpts": {
                            "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                        },
                        "type": "async",
                        "data": payLoad
                    }
                };
            let url_type = "external";
            let landing_path = _.get(this.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], "utility");
            let deeplink_url = _.get(this.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
            let deeplink_api = _.get(this.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null) ;
            
            let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;

            let deepLinkData = {
                    "channel_id"    : "both",
                    "deepLinkObj"   : {
                        "extra"     : {
                            "url"       : url + "?product_id=" + _.get(record, 'value.catalogProductID', null) + "$recharge_number=" + _.get(record, 'value.userData_recharge_number', null)+ "$" + this._utmParams(this.mode, '$'),
                            "url_type"  : url_type
                        }
                    },
                    "noRich": false
                };
    
            _.extend(notificationData.options.notificationOpts,deepLinkData);
    
            // We are waiting till morning for notification adding minimum hours to current time
            let
                date            = new Date(),
                timeToSend      = date.getHours(),
                sendAt,
                timeRange       = {
                    gte         : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.blockedNotificationStartHour', 0),
                    lte         : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.blockedNotificationEndHour', 7)
                };

            // We are sending notification just after 7+ extra hours after 00:00 AM
            if (timeToSend >= timeRange.gte && timeToSend < timeRange.lte) {
                sendAt = MOMENT().add(timeRange.lte, "hours").format("YYYY-MM-DD HH:mm:ss");
            } else {
                sendAt = MOMENT().format("YYYY-MM-DD HH:mm:ss");
            }

            let 
                apiOpts = {
                    "uri"       : _.get(this.config, 'NOTIFICATION.notificationapi.DIGITALNOTIFICATIONAPI', null),
                    "method"    : "POST",
                    "timeout": 1000,
                    "json"      : {
                        "source_id"         : 3,
                        "category_id"       : 3,
                        "recharge_number"   : _.get(record, 'value.userData_recharge_number', null),
                        "product_id"        :  _.get(record, 'value.catalogProductID', null),
                        "max_retry_count"   : 2,
                        "retry_interval"    : 30,
                        "type"              : _.get(notificationRecord, 'type', null),
                        "template_id"       : _.get(notificationRecord, 'template_id', null),
                        "recipient"         : _.get(notificationRecord, 'recipients', null),
                        "send_at"           : sendAt,
                        "data"              : notificationData,
                        "rules": {
                            "condition": `category_id=3 and source_id=3 and recharge_number='${_.get(record, 'value.userData_recharge_number', null)}' and product_id=${_.get(record, 'value.catalogProductID', null)} 
                                    and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)} and recipient='${_.get(record, 'value.customerInfo_customer_id', null)}'`,
                            "actions": [
                                {
                                    "status": "pending",
                                    "action": "drop"
                                },
                                {
                                    "status": "sent",
                                    "action": "drop"
                                },
                                {
                                    "status": "error",
                                    "action": "drop"
                                }
                            ]
                        }
                    }
                };
            
            var latencyStart = new Date().getTime();

            REQUEST(apiOpts, (error, response, body) => {
                
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'NOTIFY_API',
                    'URL': _.get(this.config, 'NOTIFICATION.notificationapi.DIGITALNOTIFICATIONAPI', null)
                });

                if (body && typeof body === 'string') {
                    try{
                        body = JSON.parse(body);
                    } catch(e){
                        this.L.error("rechargeNudgeValidationConsumer :: sendNotification", "Error parsing data received", e);
                    }
                }
                if (error || (body && body.status && body.status != 200)) {
                    let errorMsg = (error) ? error : ((body.error) ? body.error : "body status: " + body.status);
                    this.L.error("rechargeNudgeValidationConsumer :: sendNotification", 'Error in sending ' + _.get(notificationRecord, 'type', null) + ' notification to the recipient: ' + _.get(notificationRecord, 'recipients', null) + ' ' + errorMsg);
                    callback(errorMsg);
                } else {
                    this.L.log("rechargeNudgeValidationConsumer :: sendNotification", _.get(notificationRecord, 'type', null) + ' notification sent to the recipient: ' + _.get(notificationRecord, 'recipients', null) + ' ' + body.data);
                    callback();
                }
            });
        }
        else {
            // recipients not found, sending error
            callback("Recipients not found while sending notification");
        }
    }

    // commit offset and resume or exit consumer
    processMessageOffset(){
        if(!this.extendedOffsetStatus && this.extendedOffsetMessage){
            // extendedOffsetStatus is false and there is a message found in the extended time frame then commit the previous message and stop further message commits
            this.consumer.commitOffset(this.extendedOffsetMessage, (err)=>{
                if(err) {
                    this.L.critical('rechargeNudgeValidationConsumer :: processMessageOffset', 'Error while commiting extended offset in kafka', _.get(this.offsetMessage,'topic'), _.get(this.offsetMessage,'offset'), _.get(this.offsetMessage,'partition'), err);
                } else {
                    this.L.log('rechargeNudgeValidationConsumer :: processMessageOffset', 'Extended offset commited successfully', _.get(this.offsetMessage,'topic'), _.get(this.offsetMessage,'offset'), _.get(this.offsetMessage,'partition'));
                }
                // stop commiting any further message
                this.extendedOffsetStatus = true;
                this.handleRecursiveKafkaCalls();
            });
        } else if(!this.extendedOffsetStatus && this.offsetMessage){
            // extendedOffsetStatus is false and there is a message found before the extended period, then commit the message
            this.consumer.commitOffset(this.offsetMessage, (err)=>{
                if(err) {
                    this.L.critical('rechargeNudgeValidationConsumer :: processMessageOffset', 'Error while commiting offset in kafka', _.get(this.offsetMessage,'topic'), _.get(this.offsetMessage,'offset'), _.get(this.offsetMessage,'partition'), err);
                } else {
                    this.L.log('rechargeNudgeValidationConsumer :: processMessageOffset', 'Offset commited successfully', _.get(this.offsetMessage,'topic'), _.get(this.offsetMessage,'offset'), _.get(this.offsetMessage,'partition'));
                }
                this.handleRecursiveKafkaCalls();
            });
        } else{
            // extendedOffsetStatus is true or there is no message to commit, just continue processing next batch or exit the flow
            this.handleRecursiveKafkaCalls();
        }
    }

    // helper function of processMessageOffset to resume or exit consumer
    handleRecursiveKafkaCalls(){
        if(this.exitFlag){
            // if exitFlag is true, close the consumer and exit
            this.consumer.close(() => {
                this.L.info('rechargeNudgeValidationConsumer :: handleRecursiveKafkaCalls', 'Consumer closed');
                this.deleteMarkerFile();
                // update end time in file
                this.setEndTime();
                process.exit(0);
            })
        } else{
            this.consumer._resumeConsumer();
        }
    }
    _utmParams(mode, delimiter = '&') {
        let utm = _.get(this.config, 'NOTIFICATION.RECHARGE_NUDGE_UTM.' + mode, null);
        if (!utm) {
            this.L.error(`UTM config not found for template: ${mode}`);
            utm = _.get(this.config, ['NOTIFICATION','RECHARGE_NUDGE_UTM','notfound'], '');
        }
        return utm.replace(/&/g, delimiter);
    }
}

export default RechargeNudgeValidationConsumer;