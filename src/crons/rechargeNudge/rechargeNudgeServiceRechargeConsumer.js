/*jshint multistr: true ,node: true*/
"use strict";

import _                from 'lodash'
import OS               from 'os'
import MOMENT           from 'moment'
import ASYNC            from 'async'
import utility          from '../../lib'
/**
 * @class
 * Recharge nudge service recharge consumer is a service to fetch recharge data from kafka in real time and update relevant recharge information in redis cache
 */

class RechargeNudgeServiceRechargeConsumer {
    /**
     * Configure specifications
     * @param { object } options Contains configuration and dependencies
     */
    constructor(options) {
        this.L                      = options.L;
        this.infrautils             = options.INFRAUTILS;
        this.config                 = options.config;
        // setting ttl to two days in miliseconds
        this.rechargeDataRedisTTL   = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.rechargeDataRedisTTL', 172800000);
        // initializing redis
        this.redis                  = new this.infrautils.cache("REDIS", this.config.REDIS);
        this.enabledServices        = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.enabledValidationServices', {});
    }

    /**
     * Starting point of service
     */
    start() {
        /**
         * Flow of execution:
         * 1. Connect with redis cache and initialize kafka consumer.
         * 2. Fetch real time data from kafka for provided topics.
         * 3. Check fetched data and update in redis.
         */
        ASYNC.waterfall([
            (callback) => {
                // connect with redis
                this.redis.connect((err, data) => {
                    callback(err);
                });
            },
            (callback) => {
                // initialize kafka consumer
                this.initializeKafkaConsumer(callback);
            }
        ], (error, result) => {
            if(error){
                this.L.critical('rechargeNudgeServiceRechargeConsumer :: start', 'Error:', error);
                // exiting in case of error
                process.exit(0);
            }
            this.L.info('rechargeNudgeServiceRechargeConsumer :: start', "Kafka consumer initialized.");
        });
    }

    // initialize kafka consumer
    initializeKafkaConsumer(cb) {
        // setup kafka configuration
        this.consumer       = new this.infrautils.kafka.consumer({
            "kafkaHost"     : _.get(this.config, 'KAFKA.TOPICS.RECHARGE.HOSTS'),
            "groupId"       : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.rechargeServiceConsumerKafkaGroupId', ''),
            "topics"        : _.get(this.config, 'KAFKA.SERVICES.RECHARGE_NUDGE_CONSUMERS.RECHARGE_CC_TOPICS'),
            "id"            : _.get(this.config, 'RECHARGE_NUDGE_CONFIG.rechargeServiceConsumerKafkaId', ''),
            "fromOffset"    : "earliest",
            "autoCommit"    : true
        });
        // initialize consumer and bind it to processKafkaData function for further processing
        this.consumer.initConsumer(this.processKafkaData.bind(this), cb);
    }

    // process data from kafka
    processKafkaData(rechargeData) {
        try{
            // check if there is data present inside rechargeData. Also the data should be of type string
            let 
                valueData = _.get(rechargeData, 'value', null),
                customerId, userDataRechargeNumber, productId, initTimeStamp, initTimeStampOriginal, service, key;
            if(valueData && typeof(valueData) == 'string'){
                try{
                    // parse value inside
                    valueData = JSON.parse(valueData); 
                } catch (err) {
                    this.L.error('rechargeNudgeServiceRechargeConsumer :: processKafkaData', 'Error parsing recharge data:', valueData);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_RECHARGE_CONSUMER_PARSING_RECHARGE_DATA', 'STATUS:ERROR']);
                    throw err;
                }

                //Changes for BBPS rerouting using original PID

                let originalPid = _.get(valueData, 'originalPid', null);
                this.L.info("rechargeNudgeServiceRechargeConsumer: updating PID, catalogProductID, originalPid ",
                 _.get(valueData, 'catalogProductID', ''), originalPid);

                 _.set(valueData, "catalogProductID", originalPid|| _.get(valueData, 'catalogProductID', ''));
 
                // check required condition for insertion in redis
                customerId              = _.get(valueData, 'customerInfo_customer_id', '');
                productId               = _.get(valueData, 'catalogProductID', '');
                userDataRechargeNumber  = _.get(valueData, 'userData_recharge_number', '');
                service                 = typeof(_.get(valueData, 'productInfo_service', null)) === 'string' ? _.get(valueData, 'productInfo_service').toLowerCase() : '';
                initTimeStampOriginal   = _.get(valueData, 'timestamps_init', '');
                initTimeStamp           = initTimeStampOriginal ? (MOMENT(initTimeStampOriginal).isValid() ? MOMENT(initTimeStampOriginal) : null) : null;
                if(this.enabledServices[service] && customerId && !isNaN(customerId) && customerId != 0 && productId && !isNaN(productId) && userDataRechargeNumber && initTimeStamp){
                    key = _.get(this.config, 'RECHARGE_NUDGE_CONFIG.rechargeRedisPrefix', 'RNRC_') + productId + (service == 'financial services' ? ':' + customerId + ':' : ':') + userDataRechargeNumber.toLowerCase();
                    if(_.get(valueData, 'inStatusMap_transactionStatus', '')=='SUCCESS'||_.get(valueData, 'inStatusMap_transactionStatus', '')=='FAILED'||_.get(valueData, 'inStatusMap_transactionStatus', '')=='PENDING'||_.get(valueData, 'inStatusMap_transactionStatus', '')=='CANCELLED'||_.get(valueData, 'inStatusMap_transactionStatus', '')=='QUEUED'){
                    this.setRechargeDataInRedis(key, initTimeStampOriginal, this.rechargeDataRedisTTL);
                    }
                } else{
                    this.L.info('rechargeNudgeServiceRechargeConsumer :: processKafkaData', 'Not setting data in redis for', _.get(rechargeData, 'topic'), _.get(rechargeData, 'offset'), customerId, productId, userDataRechargeNumber, service, initTimeStampOriginal);
                }
            } else{
                this.L.info('rechargeNudgeServiceRechargeConsumer :: processKafkaData', 'Corrupt data found, not setting data in redis for', _.get(rechargeData, 'topic'), _.get(rechargeData, 'offset'));
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NUDGE_RECHARGE_CONSUMER_CORRUPT_DATA', 'STATUS:ERROR']);
            }
        } catch(error){
            this.L.error('rechargeNudgeServiceRechargeConsumer :: processKafkaData', 'Error:', error)
        }
    }

    // add or update recharge data entry in redis
    setRechargeDataInRedis(key, value, ttl){
        // check if data exists in redis
        this.redis.getData((err, redisData) => {
            if(err){
                this.L.critical('rechargeNudgeServiceRechargeConsumer :: setRechargeDataInRedis', 'Unable to get key from redis', key, err);
            } else{
                if(redisData){
                    // if data exists, then update value with most recent time between value and redisData
                    value = MOMENT(redisData).isValid() ? (MOMENT(value).isAfter(redisData) ? value : redisData) : value;
                }
                // add/update redis data and its ttl
                this.redis.updateData((error, result) => {
                    if(error){
                        this.L.critical('rechargeNudgeServiceRechargeConsumer :: setRechargeDataInRedis', 'Unable to set key in redis', key, error);
                    }
                    else{
                        this.L.info('rechargeNudgeServiceRechargeConsumer :: setRechargeDataInRedis', 'Redis key added/updated for', key);
                        utility._sendMetricsToDD(1, [
                            'STATUS:UPDATE_DATA',
                            'REQUEST_TYPE:NUDGE_RECHARGE_CONSUMER'
                        ])
                    }
                }, {
                    key     : key,
                    value   : value,
                    ttl     : ttl
                })
            }
        }, {key: key});
    }
}

export default RechargeNudgeServiceRechargeConsumer;