import ASYNC from 'async'
import _ from 'lodash'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import OS from 'os'
import NotificationLibrary from '../lib/notification'
import cassandraBills from '../models/cassandraBills'
import utility from '../lib';
import NOTIFIER from '../services/notify'
import billsLib from '../lib/bills'
import Logger from '../lib/logger'
class BillGenPublisher {
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.dbInstance = options.dbInstance;
        this.bills = new BILLS(options);
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.BlackListOperatorBillGen = _.get(this.notificationConfig, 'BlackListOperatorBillGen', null);
        this.AllowedListOperatorBillGen = _.get(this.notificationConfig,'AllowedListOperatorBillGen',null);
        this.notificationLibrary = new NotificationLibrary(options);
        this.offsetIdMap = {};
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.cassandraBills = new cassandraBills(options);
        this.notify = new NOTIFIER(options);
        this.billsLib = new billsLib(options);
        this.logger = new Logger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

        this.operatorList = {}
        this.getOperatorList();
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: billGenPublisher", "Re-initializing variable after interval");
        self.notificationConfig = _.get(this.config, 'NOTIFICATION');
        self.operatorTableMap = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.BlackListOperatorBillGen = _.get(this.notificationConfig, 'BlackListOperatorBillGen', null);
        self.AllowedListOperatorBillGen = _.get(this.notificationConfig,'AllowedListOperatorBillGen',null)
    }

    getOperatorList(){
        let self = this;
        Object.keys(self.operatorTableMap).forEach((operator)=>{
            /** Check if operator is blacklisted */
            if (self.BlackListOperatorBillGen && self.BlackListOperatorBillGen.indexOf(operator) != -1){
                return
            }
            else if(self.AllowedListOperatorBillGen && self.AllowedListOperatorBillGen.indexOf(operator) == -1){
                return
            }
            else{
                self.operatorList[operator] = true;
            }
        })

        self.L.log("operator list :",JSON.stringify(self.operatorList))
    }

    start(callback) {
        let self = this;
        console.time('Execution Time');
        return self._start((err) => {
            if (err) {
                console.log(err);
                console.log('FAILURE');
                console.timeEnd('Execution Time');
                return callback(err);
            }
            else {
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                return callback(null);
            }
        });
    }

    _start(done) {
        let
            self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('_start', 'Going to initialize Kakfa Publisher');
                return self.configureKafkaPublisher(next);
            },
            next => {
                self.L.log('_start', 'Going to publish records for all operators');
                return self.processOperators(next);
            }
        ], function (error) {
            if (error) {
                self.L.error('_start', 'Error', error);
            }
            return done(error);
        });
    }

    /**
     * Kafka publisher
     */
    configureKafkaPublisher(done) {
        let self = this;
        self.notify.configureKafkaPublisher((err) => {
            return done(err);
        })
    }

    getBillGenIntervalsWithConfig(table, callback) {
        let self = this;
        
        // Get default bill gen date intervals first
        let billGenIntervals = self.notificationLibrary.getPossibleBillGenDates('ALL', table) || [];
        
        // Fetch one record to identify service
        self.bills.getBillsToNotifyBillGen((err, data) => {
            if (err || !data || data.length === 0) {
                self.L.error('getBillGenIntervalsWithConfig', `Error fetching record from table ${table}: ${err}`);
                // Return default billGenIntervals when no records found
                self.L.log('getBillGenIntervalsWithConfig', `No records found, returning default billGenIntervals: ${JSON.stringify(billGenIntervals)}`);
                return callback(null, billGenIntervals);
            }

            const record = data[0];
            const service = _.get(record, 'service', '').toLowerCase();
            self.L.log("Service " + service);
            // Get BILLGEN day values from config for the service
            const serviceConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'COHORT_CONFIG', service,'value'], {});
            const billGenDays = new Set();
            
            // Add BILLGEN day values from config
            if (serviceConfig && typeof serviceConfig === 'object') {
                Object.keys(serviceConfig).forEach(rangeKey => {
                    const rangeConfig = serviceConfig[rangeKey];
                    if (rangeConfig && rangeConfig.isEnabled && rangeConfig.BILLGEN && Array.isArray(rangeConfig.BILLGEN)) {
                        rangeConfig.BILLGEN.forEach(timePoint => {
                            if (timePoint && timePoint.dayValue) {
                                billGenDays.add(timePoint.dayValue);
                            }
                        });
                    }
                });
            }
            // Add new day values to billGenIntervals if not present
            billGenDays.forEach(day => {
                if (!billGenIntervals.includes(day)) {
                    billGenIntervals.push(day);
                }
            });

            self.L.log('getBillGenIntervalsWithConfig', `Found billGenIntervals for service ${service}: ${JSON.stringify(billGenIntervals)}`);
            return callback(null, billGenIntervals);
        }, table, 1, MOMENT().subtract(1, 'day').format('YYYY-MM-DD'), 0, MOMENT().format('YYYY-MM-DD'));
    }

    processOperators(done) {
        let self = this;

        let tables = {}
        Object.keys(self.operatorList).forEach(operator=>{
            let table = _.get(self.operatorTableMap , [operator], null)
            tables[table] = true;
        })

        ASYNC.forEachOfSeries(tables, (value, table, callback) => {
            if (self.offsetIdMap[table]) {
                self.L.log('processOperators', `Table ${table} already processed...skipping it`);
                return callback();
            } else {
                self.offsetIdMap[table] = {}; 

                self.getBillGenIntervalsWithConfig(table, (err, billGenIntervals) => {
                    if (err) {
                        return callback(err);
                    }

                    billGenIntervals.forEach((billGenDateInterval) => {
                        self.offsetIdMap[table][billGenDateInterval] = 0; // start with 0 offset
                    });

                    self.L.log('processOperators', 'processing records for table: ' + table);
                    self.processBills(table, billGenIntervals, () => {
                        return callback();
                    });
                });
            }
        },
        (err) => {
            self.L.log('processOperators', 'Completed !!');
            return done(err);
        });
    }
    
    processBills(tableName, billGenDateIntervals, done) {
        let self = this;
        ASYNC.forEachOfSeries(billGenDateIntervals, (billGenDateInterval, index , callback) => {
                self.L.log('processBills', 'processing records for the  table: ' + tableName + ' and billGenInterval: '+  billGenDateInterval);

                self.processBillsForEachBillGenDate(tableName, billGenDateInterval, () => {
                    return callback();
                });     
            } 
            ,(err)=>{
                if(err) {
                    self.L.log('processBills', 'Error while processing records for the table: ' + tableName + ' and billGenDateInterval: '+  billGenDateIntervals +' MSG:-' + err);
                }
                return done(err);
            })
    }

    processBillsForEachBillGenDate(tableName, billGenDateInterval, done) {
        let self = this,
            batchSize = self.greyScaleEnv ? _.get(self.config,['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'BILLGENDATE_PUBLISHER', 'BATCHSIZE'],5) : _.get(self.config, ['NOTIFICATION', 'BILL_GEN_DATE_PUBLISHER_BATCH'], 100),
            offsetId = self.offsetIdMap[tableName][billGenDateInterval],
            billDate = MOMENT().subtract(billGenDateInterval, 'day').format('YYYY-MM-DD'),
            currentDate = MOMENT().format('YYYY-MM-DD');

        self.bills.getBillsToNotifyBillGen(function _doUntilNoMoreRecords(error, data) {
            if (!error && data && data.length > 0) {
                self.processBatch(data, () => {
                    self.offsetIdMap[tableName][billGenDateInterval] = data.length + self.offsetIdMap[tableName][billGenDateInterval];

                    if (self.greyScaleEnv) {
                        self.L.log('processBillsForEachBillGenDate', `GREYSCALEMODE for tableName ${tableName}, Total processed records ${self.offsetIdMap[tableName][billGenDateInterval]}`);
                        return done();
                    }
                    
                    self.L.log('processBillsForEachBillGenDate', `processing next batch for tableName ${tableName} and offset ${self.offsetIdMap[tableName][billGenDateInterval]}`);
                    setTimeout(() => {
                        self.bills.getBillsToNotifyBillGen(_doUntilNoMoreRecords, tableName, batchSize, billDate, self.offsetIdMap[tableName][billGenDateInterval], currentDate);
                    }, 100);
                });
            } else {
                self.L.log('processBillsForEachBillGenDate', `No record found for tableName ${tableName}, Total processed records ${self.offsetIdMap[tableName][billGenDateInterval]}`);
                done();
            }
        }, tableName, batchSize, billDate, offsetId, currentDate);
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                if (record && record['customer_id']) {
                    return self.publishData(record, next);
                } else {
                    next();
                }
            },
            err => {
                done();
            }
        )
    }

    validateRecord(record){
        let self = this;
        let currentDate = MOMENT().format('YYYY-MM-DD');
        let minAmount = _.get(self.config, 'COMMON.MIN_NOTIFY_AMOUNT', 0)
        let disabledStatus = _.get(self.config, 'COMMON.notification_status.DISABLED', 0)

        /** First check if record's operator belongs to operator list */
        let operator = _.get(record , 'operator','')
        if( self.operatorList && self.operatorList[operator] == true){
            
            /** Checking amount and notification status */
            if((_.get(record,'amount',0) >  minAmount || _.get(record, 'amount', null) === null) && _.get(record,'notification_status','')!=disabledStatus ){

                /** Checking bill fetch date */
                let bill_fetch_date = _.get(record , 'bill_fetch_date',null)

                bill_fetch_date =bill_fetch_date ? (MOMENT.utc(bill_fetch_date).isValid() ? MOMENT.utc(bill_fetch_date).format('YYYY-MM-DD') : null) : null;

                if(bill_fetch_date && bill_fetch_date < currentDate)return [false,null];
                else return [true, 'Rejecting due date notification as bill fetch date is greater than or equal to current date']
            }else{
                return [true, 'Rejecting due date notification as amount is less than min amount or notification status is disabled']
            }
        }else{
            return [true, 'Rejecting due date notification as operator is not allowed']
        }

        return false
    }

    publishData(record, done) {

        let
            self = this;
        
        let [validationFailed, error_msg] = self.validateRecord(record)
        if(validationFailed){
            return self.notify.insertRejectedNotifications(done,error_msg, {...record, dataFrom: 'billGenCron'});
        }
        let
            payload = self.prepareDataToPublish(record),
            skipNotification = self.greyScaleEnv ? 1 : 0;
        _.set(payload, 'data.skipNotification', skipNotification);
        utility.sendNotificationMetricsFromSource(payload)
        self.notify.kafkaNotificationServicePublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', null),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {
                utility.sendNotificationMetricsFromSource(payload,"ERROR")
                self.logger.error(`publishData Error while publishing message in Kafka ${error} - MSG:-`, payload, _.get(payload, 'data.service', null));
                let rejectMsg = self.billsLib.createErrorMessage(error);
                return self.notify.insertRejectedNotifications(done, rejectMsg, {...record, dataFrom: 'billGenCron'});
            } else {
                self.logger.log(`publishData Message published successfully in Kafka on topic REMINDER_BILL_FETCH`, payload, _.get(payload, 'data.service', null));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', null),
                    'REQUEST_TYPE:BILLGEN_NOTIFICATION_PUBLISHER',
                    'OPERATOR:' + _.get(payload,'data.operator')
                ])
                return done();
            }
        }, [200, 800]);
    }

    prepareDataToPublish(record) {
        let self = this;
        self.L.log("printing in the prepareDataToPublish :: ", JSON.stringify(record));

        return {
            "source": "reminderBillGenPublisher",
            "notificationType": "BILLGEN",
            "machineId": OS.hostname(),
            "data": {
                "id": _.get(record, 'id'),
                "is_automatic": _.get(record, 'is_automatic'),
                "customerId": _.get(record, 'customer_id'),
                "rechargeNumber": _.get(record, 'recharge_number'),
                "productId": _.get(record, 'product_id'),
                "operator": _.get(record, 'operator'),
                "amount": _.get(record, 'amount'),
                "dueDate": _.get(record, 'due_date'),
                "billFetchDate": _.get(record, 'bill_fetch_date'),
                "nextBillFetchDate": _.get(record, 'next_bill_fetch_date'),
                "gateway": _.get(record, 'gateway'),
                "paytype": _.get(record, 'paytype'),
                "service": _.get(record, 'service'),
                "circle": _.get(record, 'circle'),
                "customerMobile": _.get(record, 'customer_mobile'),
                "customerEmail": _.get(record, 'customer_email'),
                "paymentChannel": _.get(record, 'payment_channel'),
                "retryCount": _.get(record, 'retry_count'),
                "status": _.get(record, 'status'),
                "reason": _.get(record, 'reason'),
                "userData": _.get(record, 'user_data'),
                "createdAt": _.get(record, 'created_at'),
                "updatedAt": _.get(record, 'updated_at'),
                "billDate": _.get(record, 'bill_date'),
                "paymentDate": _.get(record, 'payment_date'),
                "notification_status": _.get(record, 'notification_status'),
                "service_id": _.get(record, 'service_id'),
                "customerOtherInfo" : _.get(record, 'customerOtherInfo'),
                "extra" : _.get(record, 'extra', null),
                "bank_name" : _.get(record, 'bank_name', null),
                "card_network": _.get(record, 'card_network', null),
                "data_source":_.get(record, 'data_source', null),
                "billGen": true,
                "remindLaterDate": _.get(record, 'remind_later_date', null)
            }
        };
    }
}

export default BillGenPublisher