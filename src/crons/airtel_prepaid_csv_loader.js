import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
let serviceName = "AIRTEL_PREPAID_INGEST" 
let progressFilePath = `/var/log/digital-notification/progress-airtel-${MOMENT().format('YYYY-MM')}.json`
let progressTracker = {}
/** Maintain offset and processed files in a path */
class AIRTELCSVINGEST {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        options.isRecordProcessingCapped = _.get(options.config , ['DYNAMIC_CONFIG','AIRTEL_PREPAID_INGEST','implementDailyCapping','value'],false);
        options.cappingNumberForCurrentDay = _.get(options.config , ['DYNAMIC_CONFIG','AIRTEL_PREPAID_INGEST','dailyCappingNumber','value'],10000000);
        options.allowedCronExecutionTime = Math.floor(Math.abs(_.get(options.config , ['DYNAMIC_CONFIG','AIRTEL_PREPAID_INGEST','allowedCronExecutionTimeInMinutes','value'],null))) ? MOMENT().add(Math.floor(Math.abs(_.get(options.config , ['DYNAMIC_CONFIG','AIRTEL_PREPAID_INGEST','allowedCronExecutionTimeInMinutes','value'],null))),'minutes') : false;       // in minutes
        this.csvIngester = new AWSCsvIngester(options , this.updateProgress)
        this.logPrefix = serviceName
        this.batchSize = _.get(options.config,['DYNAMIC_CONFIG',serviceName,'batch_size','value'],5)
        this.serviceMissing = 0
        this.operatorMissing = 0;
        this.productidMissing = 0
        this.rechargeNumberMissing = 0
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {};
        this.currentFile=""
        this.folderPath = `${_.get(options.config , ['DYNAMIC_CONFIG','AIRTEL_PREPAID_INGEST','path','value'],'digital-reminder/AIRTEL_PREPAID_INGEST')}` 
        
        this.files = []
    }
    getProgressObject(){
        let progress = {}
        progressFilePath = `/var/log/digital-notification/progress-airtel-${MOMENT().format('YYYY-MM')}.json`
        this.L.info("Loading progress object from",progressFilePath)
        if(fs.existsSync(progressFilePath)){
            const progressData = fs.readFileSync(progressFilePath , 'utf-8')
            progress = JSON.parse(progressData)
        }
        this.L.info("Loaded",progress)
        return progress
    }   
    updateProgress(filename ,count ){
        if(_.get(progressTracker , [filename] , 0 ) == -1)return;
        _.set(progressTracker , [filename] , count )
        this.L.info("Updated progess Object",JSON.stringify(progressTracker),count)
        fs.writeFileSync(progressFilePath , JSON.stringify(progressTracker , null  , 2))
    }
    filterFileByMonth(filename){
        try{
           let date = filename.split('$')[1].split('.')[0].slice(0,7)
           if(date == MOMENT().format('YYYY-MM'))return true
        }catch(err){
            return false
        }
        return false
    }
    start(callback){
        let self = this;
        progressTracker = this.getProgressObject()
        try{
            this.csvIngester.configure(this.bucketName, this.logPrefix,this.batchSize)
        }catch(error){
            self.L.critical(this.logPrefix , "Cannot initialize AWS")
            return callback(error)
        }
        self.L.info("Getting Files in the folder")
       
        // let data =['digital-reminder/Airtel_API_Recharge_Number.csv']
        // eachSeries(data , self.processEachFile.bind(self) , callback)
        this.csvIngester.getFileNames(this.folderPath,function(err , data){
            if(err){
                self.L.error("Error while getting files")
                return callback(err)
            }else{
                data = _.filter(data , self.filterFileByMonth)
                return eachSeries(data , self.processEachFile.bind(self) , callback)
            }
        })
      
    }
    processEachFile(filename , callback){
        if(_.get(progressTracker , filename, null) == -1){
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ",filename , "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ",filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker , [filename], 0) 
        this.csvIngester.start(this.processRecordinBatch.bind(this),filename , function(error , data){
            return callback()
        },skipRows)
    }
    async processRecordinBatch(data){
        let self = this;
        return new Promise((resolve , reject)=>{
            eachLimit(data ,self.batchSize, function(record , cb){
                self.processRecord(function(){
                    cb()
                } ,record)
            },function(error){
                if(error){
                    self.L.error(self.logPrefix , "Error while processing batch",error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }
    
    processRecord(cb, data) {
        let self=this;
        // if(_.get(data , 'customer_id',null) == null  && _.get(data , 'mobileNumber',null)!=null && _.get(data , 'OauthTried',0)==0){
        //     _.set(data , 'OauthTried',1)
        //     return self.getCustDetails(data).then(cid=>{
        //             _.set(data , 'customer_id',cid)
        //             return self.processRecord(cb , data)
        //     })
        // }
        let operator = _.get(data, 'operator', 'airtel');
        let nbfd = _.get(data , 'NBFD',null)
        let operation =  _.toUpper(_.get(data , 'flag',''))
        nbfd = MOMENT().startOf('day').format("YYYY-MM-DD HH:mm:ss")
        // if(operator!=null){
        //     operator = _.get(self.operatorMapping,  operator, null);  
        // }
        let params={
            'service': _.get(data, 'service', 'mobile'),
            'operator': operator ,
            'productid': _.get(data, 'product_id', null),
            'rechargeNumber': _.get(data, 'recharge_number', null),
            'customer_id':_.get(data , 'customer_id',null),
            'status':'0',
            'service_id':'0',
            'is_automatic':'0',
            'next_bill_fetch_date':nbfd,
            'paytype' : _.get(data , 'paytype','prepaid')
        }
        if(!params.customer_id){           
            self.L.error(self.currentFile , 'Customer ID missing',params)
            self.customerIdMissing++
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:CUSTOMER_ID_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(!params.rechargeNumber || _.isEmpty(params.rechargeNumber)){
            self.L.error(self.currentFile , 'Recharge number missing',params)
            self.rechargeNumberMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(operation!="INSERT" && operation!="DELETE"){
            self.L.error(self.currentFile , 'Recharge number missing',params)
            self.rechargeNumberMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:INVALID_OPERATION', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:INVALID_OPERATION']);
            return cb();
        }
        let tableName = self.getTableName(params)  
        
        if(!tableName){
            self.L.error(self.currentFile , 'table name missing',params)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:TABLE_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:TABLE_NOT_FOUND_FROM_CSV']);
            return cb();
            
        } 
        if(operation == "INSERT"){
            if(!params.productid){
                self.L.error(self.currentFile , 'Product Id missing',params)
                self.productidMissing++;
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:PRODUCT_ID_NOT_FOUND_FROM_CSV']);
                return cb();
                
            } 

            self.insertRecords(tableName,params,(error)=>{
                    if (error) {
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:RECORDS_NOT_INSERTED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_NOT_INSERTED_INTO_TABLE']);
                        L.error('error',error);
                    } 
                    else{
                        self.L.info(self.currentFile , 'Record inserted')
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:RECORDS_INSERTED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_INSERTED_INTO_TABLE']);
                        self.L.log(`insertRecords :: Inserted into ${tableName} for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
                    }
                        return cb(error);
            }); 
        }

        if(operation == "DELETE"){
            self.deleteRecords(tableName,params,(error)=>{
                if (error) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:RECORDS_NOT_DELETED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_NOT_DELETED']);
                    L.error('error',error);
                } 
                else{
                    self.L.info(self.currentFile , 'Record deleted')
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_INGEST', 'STATUS:RECORDS_DELETED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_DELETED_INTO_TABLE']);
                    self.L.log(`insertRecords :: DELETED from ${tableName} for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
                }
                    return cb(error);
        }); 
        }
    }
   
    insertRecords(tableName,params,cb){
       
       let self = this;
       const query = `INSERT IGNORE INTO ${tableName} \
       (customer_id, recharge_number, product_id, operator, next_bill_fetch_date, service , paytype) \
       values (?,?,?,?,?,?,?)`; 
       const param = [
           params.customer_id,
           params.rechargeNumber,
           params.productid,
           params.operator,
           params.next_bill_fetch_date,
           params.service,
           params.paytype,
           params.cust_email,
           params.cust_mobile
       ];
       self.dbInstance.exec((error, res) => {
           if (error) {
               self.L.critical('writeCustomerDetails::', query, error);
           }
           return cb(error,res);
       }, 'DIGITAL_REMINDER_MASTER', query, param);
}

    deleteRecords(tableName,params,cb){
        
        let self = this;
        const query = `DELETE FROM ${tableName} WHERE recharge_number = ? and customer_id= ?  and product_id = ? and operator = ?`; 
        const param = [
            params.rechargeNumber,
            params.customer_id,
            params.productid,
            params.operator,
        ];
        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('writeCustomerDetails::', query, error);
            }
            return cb(error,res);
        }, 'DIGITAL_REMINDER_MASTER', query, param);
    }

/** Logic for fetching table name from params */
    getTableName(params){
        let recharge_number = params.rechargeNumber
        let i = recharge_number.slice(recharge_number.length -1 )
        if(/^[0-9]$/.test(i))
            return "bills_airtelprepaid"+i;
        else return null;
    }
}
(function main() {
    if (require.main === module) {
        startup.init({
              
        }, function (err, options) {
            let script;
            try{
            script = new AIRTELCSVINGEST(options);
            script.start(
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                })
            }catch(err){
                options.L.error(err)
                process.exit(1)
            }
          
        });
    }
})();
export default AIRTELCSVINGEST