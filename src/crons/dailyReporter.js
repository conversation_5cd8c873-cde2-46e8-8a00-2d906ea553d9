import FS      from 'fs'
import PATH    from 'path'
import <PERSON><PERSON><PERSON>   from 'async'
import MOMENT  from 'moment'
import _       from 'lodash'
import EJS     from 'ejs'
import MODELS  from '../models'
import utility from '../lib'


class DailyReporter {
    static exec(options, done) {
        ASYNC.waterfall([
                //Initialise models and other things (if any)
                next => {
                    let aggregatorModel = new MODELS.Aggregator(options)
                    next(null, aggregatorModel)
                },

                //fetch data from DB for each operator
                (aggregatorModel, next) => {
                    DailyReporter.getDataForEachOperator(aggregatorModel, options, (err, data)=> {
                        if(err) {
                            next(err)
                        }
                        else {
                            next(null, data)
                        }
                    })
                },

                //Create a CSV file for attaching with email
            (dataFromDB, next) => {
                let headers = `Service, Operator, Total, Bill Fetched, Old Bill, No Bill, NPTO, Not Received, Connection Errors, Validation Failure, Wrong Due Date Format, Payment Done, Errors While Publishing, Not In Use, Disabled,Max Retry Reached,Total Eligible Records,To Be Published Records,To Be Published Records Percentage,Bills Fetched on BillDate,Bills Fetched after BillDate, Avg days diff, Bills Fetched Percentage,Old Bill Percentage, No bill percentage`;
                let csvData = [headers];
                let ejsData = [];
                let disabledOperator;
                Object.keys(dataFromDB).forEach(service => {
                    Object.keys(dataFromDB[service]).forEach(operator => {
                        disabledOperator = _.get(options, ['config', 'DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, 'DISABLED_PERMANENT'], false);
                        if (!disabledOperator && !_.get(options, ['config', 'DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, 'EXCLUDE_OPERATOR_FROM_PUBLISHER_REPORT'], false)) {
                            let statusObject = _.get(dataFromDB, [service, operator, 'statusData']),
                                total = _.values(statusObject).reduce((total, val) => total + val, 0),
                                totalBillFetched = _.get(statusObject, options.config.COMMON.bills_status.BILL_FETCHED, 0),
                                totalOldBillFound = _.get(statusObject, options.config.COMMON.bills_status.OLD_BILL_FOUND, 0),
                                totalBillNotFound = _.get(statusObject, options.config.COMMON.bills_status.BILL_NOT_FOUND, 0),
                                totalPromotedToNextCycle = _.get(statusObject, options.config.COMMON.bills_status.RETRY, 0) + _.get(statusObject, options.config.COMMON.bills_status.MAX_RETRY_REACHED, 0),
                                totalNotReceived = _.get(statusObject, options.config.COMMON.bills_status.PUBLISHED, 0),
                                totalConnectionError = _.get(statusObject, options.config.COMMON.bills_status.CONNECTION_ERROR, 0),
                                totalValidationFailure = _.get(statusObject, options.config.COMMON.bills_status.VALIDATION_FAILED, 0),
                                totalWrongDueDateFmt = _.get(statusObject, options.config.COMMON.bills_status.WRONG_DUE_DATE, 0),
                                totalPaymentDone = _.get(statusObject, options.config.COMMON.bills_status.PAYMENT_DONE, 0),
                                totalErrorsWhilePublishing = _.get(statusObject, options.config.COMMON.bills_status.ERROR_WHILE_PUBLISHING, 0),
                                notinUse = _.get(statusObject, options.config.COMMON.bills_status.NOT_IN_USE, 0),
                                disabled = _.get(statusObject, options.config.COMMON.bills_status.DISABLED, 0),
                                maxRetryReached = _.get(statusObject, options.config.COMMON.bills_status.PENDING, 0),

                                tobePublishedRecords = _.get(dataFromDB, [service, operator, 'analyticsData', 'tobePublishedRecords'], 0),
                                totalEligibleRecords = total + tobePublishedRecords,
                                tobePublishedRecordsPer = totalEligibleRecords > 0 ? (100.00 * tobePublishedRecords / totalEligibleRecords).toFixed(2) : 100,
                                // 6 extra columns here
                                totalEligibleBillFetched = _.get(dataFromDB, [service, operator, 'billDateStats', 'eligibleBills'], 0),
                                billFetchSameDay = _.get(dataFromDB, [service, operator, 'billDateStats', 'billFetchedSameDay'], 0),
                                billFetchLate = totalEligibleBillFetched - billFetchSameDay,
                                avgDayDifference = _.get(dataFromDB, [service, operator, 'billDateStats', 'avgDayDifference'], 0),
                                billFetchPercentage = totalEligibleRecords > 0 ? (100 * totalBillFetched / totalEligibleRecords).toFixed(2) : 0,
                                oldBillPercentage = totalEligibleRecords > 0 ? (100 * totalOldBillFound / totalEligibleRecords).toFixed(2) : 0,
                                noBillPercentage = totalEligibleRecords > 0 ? (100 * totalBillNotFound / totalEligibleRecords).toFixed(2) : 0,
                                successRate = totalEligibleRecords > 0 ? (100 * totalBillFetched / totalEligibleRecords).toFixed(2) : 100,
                                connectionErrorRate = totalEligibleRecords > 0 ? (100 * totalConnectionError / totalEligibleRecords).toFixed(2) : 0,
                                validationFailureRate = totalEligibleRecords > 0 ? (100 * totalValidationFailure / totalEligibleRecords).toFixed(2) : 0;
                            // csvData.push(`${operator},${total},${totalBillFetched},${totalOldBillFound},${totalBillNotFound},${totalPromotedToNextCycle},${totalNotReceived},${totalConnectionError},${totalValidationFailure},${totalWrongDueDateFmt},${totalPaymentDone},${totalErrorsWhilePublishing}`);
                            csvData.push(`${service}, ${operator}, ${total}, ${totalBillFetched}, ${totalOldBillFound}, ${totalBillNotFound}, ${totalPromotedToNextCycle}, ${totalNotReceived}, ${totalConnectionError}, ${totalValidationFailure}, ${totalWrongDueDateFmt}, ${totalPaymentDone}, ${totalErrorsWhilePublishing}, ${notinUse}, ${disabled}, ${maxRetryReached}, ${totalEligibleRecords}, ${tobePublishedRecords}, ${tobePublishedRecordsPer}, ${billFetchSameDay}, ${billFetchLate}, ${avgDayDifference}, ${billFetchPercentage}, ${oldBillPercentage}, ${noBillPercentage}`);

                            ejsData.push({
                                service: service,
                                operator: operator,
                                total: total,
                                totalBillFetched: totalBillFetched,
                                totalOldBillFound: totalOldBillFound,
                                totalBillNotFound: totalBillNotFound,
                                totalPromotedToNextCycle: totalPromotedToNextCycle,
                                totalNotReceived: totalNotReceived,
                                totalConnectionError: totalConnectionError,
                                totalValidationFailure: totalValidationFailure,
                                totalWrongDueDateFmt: totalWrongDueDateFmt,
                                totalPaymentDone: totalPaymentDone,
                                totalErrorsWhilePublishing: totalErrorsWhilePublishing,
                                notinUse: notinUse,
                                disabled: disabled,
                                maxRetryReached: maxRetryReached,
                                tobePublishedRecords: tobePublishedRecords,
                                totalEligibleRecords: totalEligibleRecords,
                                tobePublishedRecordsPer: tobePublishedRecordsPer,
                                billFetchSameDay: billFetchSameDay,
                                billFetchLate: billFetchLate,
                                avgDayDifference: avgDayDifference,
                                billFetchPercentage: billFetchPercentage,
                                oldBillPercentage: oldBillPercentage,
                                noBillPercentage: noBillPercentage
                            });

                            // Send data to Datadog
                            if (tobePublishedRecords) {
                                utility._sendMetricsToDD(tobePublishedRecords, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_SPEED_REPORT',
                                    'OPERATOR:' + operator
                                ]);

                                utility._sendMetricsToDD(tobePublishedRecordsPer, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:SPILL_OVER_RATE'
                                ]);
                            }
                           
                            if (successRate) {
                                utility._sendMetricsToDD(successRate, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:SUCCESS_RATE'
                                ]);
                            }
                          
                            if (oldBillPercentage) {
                                utility._sendMetricsToDD(oldBillPercentage, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:OLD_BILL_RATE'
                                ]);
                            }
                            
                            if (noBillPercentage) {
                                utility._sendMetricsToDD(noBillPercentage, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:NO_BILL_RATE'
                                ]);
                            }
                            
                            if (connectionErrorRate) {
                                utility._sendMetricsToDD(connectionErrorRate, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:CONNECTION_ERROR_RATE'
                                ]);
    
                            }
                           
                            if (validationFailureRate) {
                                utility._sendMetricsToDD(validationFailureRate, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:VALIDATION_FAILURE_RATE'
                                ]);
                            }
                            
                            if (totalEligibleRecords) {
                                utility._sendMetricsToDD(totalEligibleRecords, [
                                    'REQUEST_TYPE:' + 'PUBLISHER_DAILY_REPORT',
                                    'OPERATOR:' + operator,
                                    'STATUS:TOTAL_ELIGIBLE_RECORDS'
                                ]);
                            }

                            if (tobePublishedRecords > 0 && totalBillFetched < 1 && totalOldBillFound < 1 && totalBillNotFound < 1 && totalValidationFailure < 1 && totalConnectionError < 1 && DailyReporter.isOperatorEligibleForNoRecordPublished(options, operator)) { 
                                options.L.log(`Publisher Report:: No records published for ${operator}`);
                                utility._sendMetricsToDD(1, [
                                    'REQUEST_TYPE:' + 'DAILY_REPORT_NO_RECORD_PUBLISHED',
                                    'OPERATOR:' + operator 
                                ]);
                            }
                            options.L.log(`Publisher Report operator:${operator} connectionErrorRate :${connectionErrorRate} validationFailure Rate: ${validationFailureRate} nobillRate:${noBillPercentage} oldBillRate:${oldBillPercentage} successRate:${successRate}`);

                        } else {
                            options.L.log('PublisherReport:: Skipping for operator because publisher is disabled: ', operator)
                        }
                    })
                })
                    csvData = csvData.join('\n');

                    let allOperatorsDataObj = {
                        csvData : csvData,
                        ejsData : ejsData
                    };
                let topOperatorsDataObj = DailyReporter.getTopOperatorCSVAndHTMLTempData(options, dataFromDB);
                    DailyReporter.createFileAndSendMail(options, allOperatorsDataObj, topOperatorsDataObj , (err, data)=> {
                        if(err) {
                            next(err)
                        }
                        else {
                            next(null);
                        }
                    });
                }
            ],
            err => {
                if (err) {
                    options.L.error('DAILY_REPORT',err);
                    done(err)
                }
                else {
                    done()
                }
            }
        )
    }
    static getBillRemStats(options, dataFromDB, service, operator, defaultValue) {

        let billFetchExistsForOperator = false;
            
        if (_.has(dataFromDB, [service, operator])) {

        var statusObject = _.get(dataFromDB, [operator, 'statusData']),
            total = _.values(statusObject).reduce((total, val) => total + val, 0),
            totalBillFetched = _.get(statusObject, options.config.COMMON.bills_status.BILL_FETCHED, 0),
            totalOldBillFound = _.get(statusObject, options.config.COMMON.bills_status.OLD_BILL_FOUND, 0),
            totalBillNotFound = _.get(statusObject, options.config.COMMON.bills_status.BILL_NOT_FOUND, 0),
            totalPromotedToNextCycle = _.get(statusObject, options.config.COMMON.bills_status.RETRY, 0) + _.get(statusObject, options.config.COMMON.bills_status.MAX_RETRY_REACHED, 0),
            totalNotReceived = _.get(statusObject, options.config.COMMON.bills_status.PUBLISHED, 0),
            totalConnectionError = _.get(statusObject, options.config.COMMON.bills_status.CONNECTION_ERROR, 0),
            totalValidationFailure = _.get(statusObject, options.config.COMMON.bills_status.VALIDATION_FAILED, 0),
            totalWrongDueDateFmt = _.get(statusObject, options.config.COMMON.bills_status.WRONG_DUE_DATE, 0),
            totalPaymentDone = _.get(statusObject, options.config.COMMON.bills_status.PAYMENT_DONE, 0),
            totalErrorsWhilePublishing = _.get(statusObject, options.config.COMMON.bills_status.ERROR_WHILE_PUBLISHING, 0),
            notinUse = _.get(statusObject, options.config.COMMON.bills_status.NOT_IN_USE, 0),
            disabled = _.get(statusObject, options.config.COMMON.bills_status.DISABLED, 0),
            maxRetryReached = _.get(statusObject, options.config.COMMON.bills_status.PENDING, 0),
            tobePublishedRecords = _.get(dataFromDB, [operator, 'analyticsData', 'tobePublishedRecords'], 0),
            totalEligibleRecords = total + tobePublishedRecords,
            totalEligibleBillFetched = _.get(dataFromDB, [operator, 'billDateStats', 'eligibleBills'], 0),
            billFetchSameDay = _.get(dataFromDB, [operator, 'billDateStats', 'billFetchedSameDay'], 0),
            billFetchLate = totalEligibleBillFetched - billFetchSameDay,
            avgDayDifference = DailyReporter.helperToFixedIfFraction(_.get(dataFromDB, [operator, 'billDateStats', 'avgDayDifference'], 0));
        
        var billRemStatsPerObject = DailyReporter.getBillRemStatsPercentage({
                "totalEligibleRecords" : totalEligibleRecords,
                "tobePublishedRecords" : tobePublishedRecords,
                "totalOldBillFound" :  totalOldBillFound,
                "totalBillNotFound" : totalBillNotFound,
                "totalBillFetched" : totalBillFetched,
                "totalConnectionError" :  totalConnectionError,
                "totalValidationFailure" : totalValidationFailure,
                "total" : total,
                "totalPaymentDone" : totalPaymentDone,
                "totalPromotedToNextCycle" : totalPromotedToNextCycle,
                "totalNotReceived" : totalNotReceived,
                "totalWrongDueDateFmt" : totalWrongDueDateFmt,
                "totalErrorsWhilePublishing" : totalErrorsWhilePublishing,
                "notinUse" : notinUse,
                "disabled" : disabled,
                "maxRetryReached" : maxRetryReached
            });
        var tobePublishedRecordsPer = billRemStatsPerObject.tobePublishedRecordsPer,
            billFetchPercentage = billRemStatsPerObject.billFetchPercentage,
            oldBillPercentage = billRemStatsPerObject.oldBillPercentage,
            noBillPercentage = billRemStatsPerObject.noBillPercentage,
            successRate = billRemStatsPerObject.successRate,
            connectionErrorRate = billRemStatsPerObject.connectionErrorRate,
            validationFailureRate = billRemStatsPerObject.validationFailureRate,
            
            totalPercentage = billRemStatsPerObject.totalPercentage,
            paymentDonePercentage = billRemStatsPerObject.paymentDonePercentage,
            promotedToNextCyclePercentage = billRemStatsPerObject.promotedToNextCyclePercentage,
            notReceivedPercentage = billRemStatsPerObject.notReceivedPercentage,
            wrongDueDateFmtPercentage = billRemStatsPerObject.wrongDueDateFmtPercentage,
            errorsWhilePublishingPercentage = billRemStatsPerObject.errorsWhilePublishingPercentage,
            notinUsePercentage = billRemStatsPerObject.notinUsePercentage,
            disabledPercentage = billRemStatsPerObject.disabledPercentage,
            maxRetryReachedPercentage = billRemStatsPerObject.maxRetryReachedPercentage;

            billFetchExistsForOperator = true;
        } 

        return {
            total : billFetchExistsForOperator ? total : defaultValue,
            totalBillFetched : billFetchExistsForOperator ? totalBillFetched : defaultValue,
            totalOldBillFound : billFetchExistsForOperator ? totalOldBillFound : defaultValue,
            totalBillNotFound : billFetchExistsForOperator ? totalBillNotFound : defaultValue,
            totalPromotedToNextCycle : billFetchExistsForOperator ? totalPromotedToNextCycle : defaultValue,
            totalNotReceived : billFetchExistsForOperator ? totalNotReceived : defaultValue,
            totalConnectionError : billFetchExistsForOperator ? totalConnectionError : defaultValue,
            totalValidationFailure : billFetchExistsForOperator ? totalValidationFailure : defaultValue,
            totalWrongDueDateFmt : billFetchExistsForOperator ? totalWrongDueDateFmt : defaultValue,
            totalPaymentDone : billFetchExistsForOperator ? totalPaymentDone : defaultValue,
            totalErrorsWhilePublishing : billFetchExistsForOperator ? totalErrorsWhilePublishing : defaultValue,
            notinUse : billFetchExistsForOperator ? notinUse : defaultValue,
            disabled : billFetchExistsForOperator ? disabled : defaultValue,
            maxRetryReached : billFetchExistsForOperator ? maxRetryReached : defaultValue,
            tobePublishedRecords : billFetchExistsForOperator ? tobePublishedRecords : defaultValue,
            totalEligibleRecords : billFetchExistsForOperator ? totalEligibleRecords : defaultValue,
            tobePublishedRecordsPer : billFetchExistsForOperator ? tobePublishedRecordsPer : defaultValue,
            totalEligibleBillFetched : billFetchExistsForOperator ? totalEligibleBillFetched : defaultValue,
            billFetchSameDay : billFetchExistsForOperator ? billFetchSameDay : defaultValue,
            billFetchLate : billFetchExistsForOperator ? billFetchLate : defaultValue,
            avgDayDifference : billFetchExistsForOperator ? avgDayDifference : defaultValue,
            billFetchPercentage : billFetchExistsForOperator ? billFetchPercentage : defaultValue,
            oldBillPercentage : billFetchExistsForOperator ? oldBillPercentage : defaultValue,
            noBillPercentage : billFetchExistsForOperator ? noBillPercentage : defaultValue,
            successRate : billFetchExistsForOperator ? successRate : defaultValue,
            connectionErrorRate : billFetchExistsForOperator ? connectionErrorRate : defaultValue,
            validationFailureRate : billFetchExistsForOperator ? validationFailureRate : defaultValue,
            totalPercentage : billFetchExistsForOperator ? totalPercentage : defaultValue,
            paymentDonePercentage : billFetchExistsForOperator ? paymentDonePercentage : defaultValue,
            promotedToNextCyclePercentage : billFetchExistsForOperator ? promotedToNextCyclePercentage : defaultValue,
            notReceivedPercentage : billFetchExistsForOperator ? notReceivedPercentage : defaultValue,
            wrongDueDateFmtPercentage : billFetchExistsForOperator ? wrongDueDateFmtPercentage : defaultValue,
            errorsWhilePublishingPercentage : billFetchExistsForOperator ? errorsWhilePublishingPercentage : defaultValue,
            notinUsePercentage : billFetchExistsForOperator ? notinUsePercentage : defaultValue,
            disabledPercentage : billFetchExistsForOperator ? disabledPercentage : defaultValue,
            maxRetryReachedPercentage : billFetchExistsForOperator ? maxRetryReachedPercentage : defaultValue
        };
    }
    static getBillRemStatsPercentage(billRemObj) { 
        let totalEligibleRecords = billRemObj.totalEligibleRecords,
            tobePublishedRecords = billRemObj.tobePublishedRecords,
            totalOldBillFound = billRemObj.totalOldBillFound,
            totalBillNotFound = billRemObj.totalBillNotFound,
            totalBillFetched = billRemObj.totalBillFetched,
            totalConnectionError = billRemObj.totalConnectionError,
            totalValidationFailure = billRemObj.totalValidationFailure,
            total = billRemObj.total,
            totalPaymentDone = billRemObj.totalPaymentDone,
            totalPromotedToNextCycle = billRemObj.totalPromotedToNextCycle,
            totalNotReceived = billRemObj.totalNotReceived,
            totalWrongDueDateFmt = billRemObj.totalWrongDueDateFmt,
            totalErrorsWhilePublishing = billRemObj.totalErrorsWhilePublishing,
            notinUse = billRemObj.notinUse,
            disabled = billRemObj.disabled,
            maxRetryReached = billRemObj.maxRetryReached;

        return {
            tobePublishedRecordsPer : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100.00 * tobePublishedRecords / totalEligibleRecords) : 100 ),
            billFetchPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalBillFetched / totalEligibleRecords) : 0 ),
            oldBillPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalOldBillFound / totalEligibleRecords) : 0 ),
            noBillPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalBillNotFound / totalEligibleRecords) : 0 ),
            successRate : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalBillFetched / totalEligibleRecords) : 100 ),
            connectionErrorRate : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalConnectionError / totalEligibleRecords) : 0 ),
            validationFailureRate : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalValidationFailure / totalEligibleRecords) : 0 ),
            
            totalPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * total / totalEligibleRecords) : 0 ),
            paymentDonePercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalPaymentDone / totalEligibleRecords) : 0 ),
            promotedToNextCyclePercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalPromotedToNextCycle / totalEligibleRecords) : 0 ),
            notReceivedPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalNotReceived / totalEligibleRecords) : 0 ),
            wrongDueDateFmtPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalWrongDueDateFmt / totalEligibleRecords) : 0 ),
            errorsWhilePublishingPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * totalErrorsWhilePublishing / totalEligibleRecords) : 0 ),
            notinUsePercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * notinUse / totalEligibleRecords) : 0 ),
            disabledPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * disabled / totalEligibleRecords) : 0 ),
            maxRetryReachedPercentage : (totalEligibleRecords > 0 ? DailyReporter.helperToFixedIfFraction(100 * maxRetryReached / totalEligibleRecords) : 0 )
        }
    }
    
    static helperToFixedIfFraction(number) {
        if(number === "" || isNaN(number)) 
            return number;
            
        number = parseFloat(number).toFixed(2);
        if(number.split(".").length == 2 && number.split(".")[1] == "00") {
            number = parseInt(number);
        }
        return number;
    }
    
    static getTopOperatorCSVAndHTMLTempData(options, dataFromDB) {
        let csvDataObj = {},
            ejsDataObj = {};
        let headersReportTable1 = [];
            headersReportTable1.push('Table 1 --> Bill Fetch Attempt %');
            headersReportTable1.push('Operator, Eligible, Attempted, Not attempted, % of Attempted, % of Not attempted');
        let headersReportTable2 = [];
            headersReportTable2.push('Table 2 --> Breakup of Attempted Records (in % upto 2 decimals)');
            headersReportTable2.push('Operator, New Bill, Old Bill, No Bill, Payment Done, NPTO, Connection Errors, Validation Failure, Timeout, Wrong Due Date Format, Errors while publishing, Not In Use, Disabled,Max Retry Reached');
        let headersReportTable3 = [];
            headersReportTable3.push('Table 3 --> Breakup of Attempted Records (actual numbers)');
            headersReportTable3.push('Operator, New Bill, Old Bill, No Bill, Payment Done, NPTO, Connection Errors, Validation Failure, Timeout, Wrong Due Date Format, Errors while publishing, Not In Use, Disabled,Max Retry Reached, Avg days diff');

        let topOperatorsList = ['airtel',
                                'vodafone idea',
                                'jio',
                                'airtel landline',
                                'bsnl landline bbps',
                                'lic',
                                'tsspdcl', 
                                'uttar pradesh power corporation ltd. (uppcl)',
                                'msedcl',
                                'bangalore electricity supply company ltd.',
                                'bses rajdhani',
                                'jaipur vidyut vitran nigam ltd. (jvvnl)',
                                'apepdcl',
                                'torrent power limited',
                                'tamil nadu electricity board (tneb)',
                                'punjab state power corporation limited (pspcl)',
                                'kerala state electricity board ltd (kseb ltd)',
                                'dakshin haryana bijli vitran nigam (dhbvn)',
                                'bses yamuna',
                                'tata power delhi distribution limited',
                                'uttar haryana bijli vitran nigam(uhbvn)',
                                'southern power distribution company of a.p ltd (apspdcl)',
                                'northern power distribution company limited: warrangal (tsnpdcl)',
                                'ajmer vidyut vitran nigam limited (avvnl)',
                                'south bihar power distribution',
                                'mp poorv kshetra vidyut vitaran-jabalpur',
                                'central power distribution corporation of a.p ltd (apcpdcl)',
                                'north bihar power distribution co. ltd (nbpdcl)',
                                'jodhpur vidyut vitran nigam limited (jdvvnl)',
                                'chhattisgarh state power distribution company ltd (cspdcl)',
                                'igl'
                            ];
                            
        csvDataObj.billFetchAttemptReport = [];
        csvDataObj.breakupAttemptPercentageReport = [];
        csvDataObj.breakupAttemptValuesReport = [];
       
        ejsDataObj.billFetchAttemptReport = [];        
        ejsDataObj.breakupAttemptPercentageReport = [];
        ejsDataObj.breakupAttemptValuesReport = [];

        let defaultValue = "--" , totalRowIndex = 0
        let totalOfTopOperators = {
            total : 0,
            totalBillFetched : 0,
            totalOldBillFound : 0,
            totalBillNotFound : 0,
            totalPromotedToNextCycle : 0,
            totalNotReceived : 0,
            totalConnectionError : 0,
            totalValidationFailure : 0,
            totalWrongDueDateFmt : 0,
            totalPaymentDone : 0,
            totalErrorsWhilePublishing : 0,
            notinUse : 0,
            disabled : 0,
            maxRetryReached : 0,
            tobePublishedRecords : 0,
            totalEligibleRecords : 0,
            tobePublishedRecordsPer : 0,
            avgDayDifference : defaultValue
            // totalEligibleBillFetched : 0,
            // billFetchSameDay : 0,
            // billFetchLate : 0,
        };
        let disabledOperator; 
        Object.keys(dataFromDB).forEach(service => {
            topOperatorsList.forEach(operator => {
                disabledOperator = _.get(options, ['config', 'DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, 'DISABLED_PERMANENT'], false);
                if (!disabledOperator && !_.get(options, ['config', 'DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, 'EXCLUDE_OPERATOR_FROM_PUBLISHER_REPORT'], false)) {
                    let billRemStatsObject = DailyReporter.getBillRemStats(options, dataFromDB, service, operator, defaultValue);
                
                    let total = billRemStatsObject.total,
                        totalBillFetched = billRemStatsObject.totalBillFetched,
                        totalOldBillFound = billRemStatsObject.totalOldBillFound,
                        totalBillNotFound = billRemStatsObject.totalBillNotFound,
                        totalPromotedToNextCycle = billRemStatsObject.totalPromotedToNextCycle,
                        totalNotReceived = billRemStatsObject.totalNotReceived,
                        totalConnectionError = billRemStatsObject.totalConnectionError,
                        totalValidationFailure = billRemStatsObject.totalValidationFailure,
                        totalWrongDueDateFmt = billRemStatsObject.totalWrongDueDateFmt,
                        totalPaymentDone = billRemStatsObject.totalPaymentDone,
                        totalErrorsWhilePublishing = billRemStatsObject.totalErrorsWhilePublishing,
                        notinUse = billRemStatsObject.notinUse,
                        disabled = billRemStatsObject.disabled,
                        maxRetryReached = billRemStatsObject.maxRetryReached,
                        tobePublishedRecords = billRemStatsObject.tobePublishedRecords,
                        totalEligibleRecords = billRemStatsObject.totalEligibleRecords,
                        tobePublishedRecordsPer = billRemStatsObject.tobePublishedRecordsPer,
                        // totalEligibleBillFetched = billRemStatsObject.totalEligibleBillFetched,
                        // billFetchSameDay = billRemStatsObject.billFetchSameDay,
                        // billFetchLate = billRemStatsObject.billFetchLate,
                        avgDayDifference = billRemStatsObject.avgDayDifference,
                        billFetchPercentage = billRemStatsObject.billFetchPercentage,
                        oldBillPercentage = billRemStatsObject.oldBillPercentage,
                        noBillPercentage = billRemStatsObject.noBillPercentage,
                        // successRate = billRemStatsObject.successRate,
                        connectionErrorRate = billRemStatsObject.connectionErrorRate,
                        validationFailureRate = billRemStatsObject.validationFailureRate,
                        totalPercentage = billRemStatsObject.totalPercentage,
                        paymentDonePercentage = billRemStatsObject.paymentDonePercentage,
                        promotedToNextCyclePercentage = billRemStatsObject.promotedToNextCyclePercentage,
                        notReceivedPercentage = billRemStatsObject.notReceivedPercentage,
                        wrongDueDateFmtPercentage = billRemStatsObject.wrongDueDateFmtPercentage,
                        errorsWhilePublishingPercentage = billRemStatsObject.errorsWhilePublishingPercentage,
                        notinUsePercentage = billRemStatsObject.notinUsePercentage,
                        disabledPercentage = billRemStatsObject.disabledPercentage,
                        maxRetryReachedPercentage = billRemStatsObject.maxRetryReachedPercentage;
                
                    csvDataObj.billFetchAttemptReport.push(`${operator},${totalEligibleRecords},${total},${tobePublishedRecords},${totalPercentage},${tobePublishedRecordsPer}`);
                    csvDataObj.breakupAttemptPercentageReport.push(`${operator},${billFetchPercentage},${oldBillPercentage},${noBillPercentage},${paymentDonePercentage},${promotedToNextCyclePercentage}, ${connectionErrorRate}, ${validationFailureRate},${notReceivedPercentage},${wrongDueDateFmtPercentage},${errorsWhilePublishingPercentage},${notinUsePercentage},${disabledPercentage},${maxRetryReachedPercentage}`);
                    csvDataObj.breakupAttemptValuesReport.push(`${operator},${totalBillFetched},${totalOldBillFound},${totalBillNotFound},${totalPaymentDone},${totalPromotedToNextCycle}, ${totalConnectionError}, ${totalValidationFailure},${totalNotReceived},${totalWrongDueDateFmt},${totalErrorsWhilePublishing},${notinUse},${disabled},${maxRetryReached},${avgDayDifference}`);
                
                    /** total row for all operators -> actual values */

                    total !== defaultValue && (totalOfTopOperators.total += total);
                    totalBillFetched !== defaultValue && (totalOfTopOperators.totalBillFetched += totalBillFetched);
                    totalOldBillFound !== defaultValue && (totalOfTopOperators.totalOldBillFound += totalOldBillFound);
                    totalBillNotFound !== defaultValue && (totalOfTopOperators.totalBillNotFound += totalBillNotFound);
                    totalPromotedToNextCycle !== defaultValue && (totalOfTopOperators.totalPromotedToNextCycle += totalPromotedToNextCycle);
                    totalNotReceived !== defaultValue && (totalOfTopOperators.totalNotReceived += totalNotReceived);
                    totalConnectionError !== defaultValue && (totalOfTopOperators.totalConnectionError += totalConnectionError);
                    totalValidationFailure !== defaultValue && (totalOfTopOperators.totalValidationFailure += totalValidationFailure);
                    totalWrongDueDateFmt !== defaultValue && (totalOfTopOperators.totalWrongDueDateFmt += totalWrongDueDateFmt);
                    totalPaymentDone !== defaultValue && (totalOfTopOperators.totalPaymentDone += totalPaymentDone);
                    totalErrorsWhilePublishing !== defaultValue && (totalOfTopOperators.totalErrorsWhilePublishing += totalErrorsWhilePublishing);
                    notinUse !== defaultValue && (totalOfTopOperators.notinUse += notinUse);
                    disabled !== defaultValue && (totalOfTopOperators.disabled += disabled);
                    maxRetryReached !== defaultValue && (totalOfTopOperators.maxRetryReached += maxRetryReached);
                    tobePublishedRecords !== defaultValue && (totalOfTopOperators.tobePublishedRecords += tobePublishedRecords);
                    totalEligibleRecords !== defaultValue && (totalOfTopOperators.totalEligibleRecords += totalEligibleRecords);
                    //totalEligibleBillFetched !== defaultValue && (totalOfTopOperators.totalEligibleBillFetched += billRemStatsObject.totalEligibleBillFetched);
                    //billFetchSameDay !== defaultValue && (totalOfTopOperators.billFetchSameDay += billRemStatsObject.billFetchSameDay);
                    //billFetchLate !== defaultValue && (totalOfTopOperators.billFetchLate += billRemStatsObject.billFetchLate);
                    //avgDayDifference !== defaultValue && (totalOfTopOperators.avgDayDifference += billRemStatsObject.avgDayDifference);

                    /** total row of all operators -> actual values */

                    ejsDataObj.billFetchAttemptReport.push({
                        operator: operator,
                        totalEligibleRecords: totalEligibleRecords,
                        total: total,
                        tobePublishedRecords: tobePublishedRecords,
                        totalPercentage: totalPercentage,
                        tobePublishedRecordsPer: tobePublishedRecordsPer
                    });
                    ejsDataObj.breakupAttemptPercentageReport.push({
                        operator: operator,
                        billFetchPercentage: billFetchPercentage,
                        oldBillPercentage: oldBillPercentage,
                        noBillPercentage: noBillPercentage,
                        paymentDonePercentage: paymentDonePercentage,
                        promotedToNextCyclePercentage: promotedToNextCyclePercentage,
                        connectionErrorRate: connectionErrorRate,
                        validationFailureRate: validationFailureRate,
                        notReceivedPercentage: notReceivedPercentage,
                        wrongDueDateFmtPercentage: wrongDueDateFmtPercentage,
                        errorsWhilePublishingPercentage: errorsWhilePublishingPercentage,
                        notinUsePercentage: notinUsePercentage,
                        disabledPercentage: disabledPercentage,
                        maxRetryReachedPercentage: maxRetryReachedPercentage
                    });

                    ejsDataObj.breakupAttemptValuesReport.push({
                        operator: operator,
                        totalBillFetched: totalBillFetched,
                        totalOldBillFound: totalOldBillFound,
                        totalBillNotFound: totalBillNotFound,
                        totalPaymentDone: totalPaymentDone,
                        totalPromotedToNextCycle: totalPromotedToNextCycle,
                        totalConnectionError: totalConnectionError,
                        totalValidationFailure: totalValidationFailure,
                        totalNotReceived: totalNotReceived,
                        totalWrongDueDateFmt: totalWrongDueDateFmt,
                        totalErrorsWhilePublishing: totalErrorsWhilePublishing,
                        notinUse: notinUse,
                        disabled: disabled,
                        maxRetryReached: maxRetryReached,
                        avgDayDifference: avgDayDifference
                    });
                
                }
            })
        });
        /** total row of all operators -> Percentage */
        let billRemStatsPerObject = DailyReporter.getBillRemStatsPercentage({
            "totalEligibleRecords" : totalOfTopOperators.totalEligibleRecords,
            "tobePublishedRecords" : totalOfTopOperators.tobePublishedRecords,
            "totalOldBillFound" :  totalOfTopOperators.totalOldBillFound,
            "totalBillNotFound" : totalOfTopOperators.totalBillNotFound,
            "totalBillFetched" : totalOfTopOperators.totalBillFetched,
            "totalConnectionError" :  totalOfTopOperators.totalConnectionError,
            "totalValidationFailure" : totalOfTopOperators.totalValidationFailure,
            "total" : totalOfTopOperators.total,
            "totalPaymentDone" : totalOfTopOperators.totalPaymentDone,
            "totalPromotedToNextCycle" : totalOfTopOperators.totalPromotedToNextCycle,
            "totalNotReceived" : totalOfTopOperators.totalNotReceived,
            "totalWrongDueDateFmt" : totalOfTopOperators.totalWrongDueDateFmt,
            "totalErrorsWhilePublishing" : totalOfTopOperators.totalErrorsWhilePublishing,
            "notinUse" : totalOfTopOperators.notinUse,
            "disabled" : totalOfTopOperators.disabled,
            "maxRetryReached" : totalOfTopOperators.maxRetryReached
        });
        totalOfTopOperators.tobePublishedRecordsPer = billRemStatsPerObject.tobePublishedRecordsPer;
        totalOfTopOperators.billFetchPercentage = billRemStatsPerObject.billFetchPercentage;
        totalOfTopOperators.oldBillPercentage = billRemStatsPerObject.oldBillPercentage;
        totalOfTopOperators.noBillPercentage = billRemStatsPerObject.noBillPercentage;
        totalOfTopOperators.successRate = billRemStatsPerObject.successRate;
        totalOfTopOperators.connectionErrorRate = billRemStatsPerObject.connectionErrorRate;
        totalOfTopOperators.validationFailureRate = billRemStatsPerObject.validationFailureRate;
        totalOfTopOperators.totalPercentage = billRemStatsPerObject.totalPercentage;
        totalOfTopOperators.paymentDonePercentage = billRemStatsPerObject.paymentDonePercentage;
        totalOfTopOperators.promotedToNextCyclePercentage = billRemStatsPerObject.promotedToNextCyclePercentage;
        totalOfTopOperators.notReceivedPercentage = billRemStatsPerObject.notReceivedPercentage;
        totalOfTopOperators.wrongDueDateFmtPercentage = billRemStatsPerObject.wrongDueDateFmtPercentage;
        totalOfTopOperators.errorsWhilePublishingPercentage = billRemStatsPerObject.errorsWhilePublishingPercentage;
        totalOfTopOperators.notinUsePercentage = billRemStatsPerObject.notinUsePercentage; 
        totalOfTopOperators.disabledPercentage = billRemStatsPerObject.disabledPercentage;
        totalOfTopOperators.maxRetryReachedPercentage = billRemStatsPerObject.maxRetryReachedPercentage;
        /** total row of all operators -> Percentage */

        /** sort desc. */  
        csvDataObj.billFetchAttemptReport.sort(DailyReporter.helperCSVSortComparator);
        csvDataObj.breakupAttemptPercentageReport.sort(DailyReporter.helperCSVSortComparator);
        csvDataObj.breakupAttemptValuesReport.sort(DailyReporter.helperCSVSortComparator);
        
        ejsDataObj.billFetchAttemptReport.sort( (firstVal,secondVal ) => { return  DailyReporter.helperSortComparator(firstVal.totalEligibleRecords, secondVal.totalEligibleRecords);  } );
        ejsDataObj.breakupAttemptPercentageReport.sort((firstVal,secondVal)=>{ return DailyReporter.helperSortComparator(firstVal.billFetchPercentage, secondVal.billFetchPercentage); } );
        ejsDataObj.breakupAttemptValuesReport.sort((firstVal,secondVal)=>{ return DailyReporter.helperSortComparator(firstVal.totalBillFetched, secondVal.totalBillFetched); } );
        /** sort desc. */  

        /** insert total row at top */
        csvDataObj.billFetchAttemptReport.splice(totalRowIndex, 0, `Total,${totalOfTopOperators.totalEligibleRecords},${totalOfTopOperators.total},${totalOfTopOperators.tobePublishedRecords},${totalOfTopOperators.totalPercentage},${totalOfTopOperators.tobePublishedRecordsPer}` );
        csvDataObj.breakupAttemptPercentageReport.splice(totalRowIndex, 0, `Total,${totalOfTopOperators.billFetchPercentage},${totalOfTopOperators.oldBillPercentage},${totalOfTopOperators.noBillPercentage},${totalOfTopOperators.paymentDonePercentage},${totalOfTopOperators.promotedToNextCyclePercentage}, ${totalOfTopOperators.connectionErrorRate}, ${totalOfTopOperators.validationFailureRate},${totalOfTopOperators.notReceivedPercentage},${totalOfTopOperators.wrongDueDateFmtPercentage},${totalOfTopOperators.errorsWhilePublishingPercentage},${totalOfTopOperators.notinUsePercentage},${totalOfTopOperators.disabledPercentage},${totalOfTopOperators.maxRetryReachedPercentage}` );
        csvDataObj.breakupAttemptValuesReport.splice(totalRowIndex, 0, `Total,${totalOfTopOperators.totalBillFetched},${totalOfTopOperators.totalOldBillFound},${totalOfTopOperators.totalBillNotFound},${totalOfTopOperators.totalPaymentDone},${totalOfTopOperators.totalPromotedToNextCycle}, ${totalOfTopOperators.totalConnectionError}, ${totalOfTopOperators.totalValidationFailure},${totalOfTopOperators.totalNotReceived},${totalOfTopOperators.totalWrongDueDateFmt},${totalOfTopOperators.totalErrorsWhilePublishing},${totalOfTopOperators.notinUse},${totalOfTopOperators.disabled},${totalOfTopOperators.maxRetryReached},${totalOfTopOperators.avgDayDifference}`);

        ejsDataObj.billFetchAttemptReport.splice(totalRowIndex, 0, {
            operator : 'Total',
            totalEligibleRecords: totalOfTopOperators.totalEligibleRecords,
            total: totalOfTopOperators.total,
            tobePublishedRecords: totalOfTopOperators.tobePublishedRecords,
            totalPercentage: totalOfTopOperators.totalPercentage,
            tobePublishedRecordsPer: totalOfTopOperators.tobePublishedRecordsPer
        });
        ejsDataObj.breakupAttemptPercentageReport.splice(totalRowIndex, 0,{
            operator: 'Total',
            billFetchPercentage: totalOfTopOperators.billFetchPercentage,
            oldBillPercentage: totalOfTopOperators.oldBillPercentage,
            noBillPercentage: totalOfTopOperators.noBillPercentage,
            paymentDonePercentage: totalOfTopOperators.paymentDonePercentage,
            promotedToNextCyclePercentage: totalOfTopOperators.promotedToNextCyclePercentage,
            connectionErrorRate: totalOfTopOperators.connectionErrorRate,
            validationFailureRate: totalOfTopOperators.validationFailureRate,
            notReceivedPercentage: totalOfTopOperators.notReceivedPercentage,
            wrongDueDateFmtPercentage: totalOfTopOperators.wrongDueDateFmtPercentage,
            errorsWhilePublishingPercentage: totalOfTopOperators.errorsWhilePublishingPercentage,
            notinUsePercentage: totalOfTopOperators.notinUsePercentage,
            disabledPercentage: totalOfTopOperators.disabledPercentage,
            maxRetryReachedPercentage: totalOfTopOperators.maxRetryReachedPercentage
        });

        ejsDataObj.breakupAttemptValuesReport.splice(totalRowIndex, 0,{
            operator: 'Total',
            totalBillFetched: totalOfTopOperators.totalBillFetched,
            totalOldBillFound: totalOfTopOperators.totalOldBillFound,
            totalBillNotFound: totalOfTopOperators.totalBillNotFound,
            totalPaymentDone: totalOfTopOperators.totalPaymentDone,
            totalPromotedToNextCycle: totalOfTopOperators.totalPromotedToNextCycle,
            totalConnectionError: totalOfTopOperators.totalConnectionError,
            totalValidationFailure: totalOfTopOperators.totalValidationFailure,
            totalNotReceived: totalOfTopOperators.totalNotReceived,
            totalWrongDueDateFmt: totalOfTopOperators.totalWrongDueDateFmt,
            totalErrorsWhilePublishing: totalOfTopOperators.totalErrorsWhilePublishing,
            notinUse: totalOfTopOperators.notinUse,
            disabled: totalOfTopOperators.disabled,
            maxRetryReached: totalOfTopOperators.maxRetryReached,
            avgDayDifference: totalOfTopOperators.avgDayDifference
        });        
        /** insert total row at top */

        csvDataObj.topOperatorsData = _.concat(headersReportTable1, csvDataObj.billFetchAttemptReport,'',headersReportTable2, csvDataObj.breakupAttemptPercentageReport, '', headersReportTable3, csvDataObj.breakupAttemptValuesReport);
        csvDataObj.topOperatorsData = csvDataObj.topOperatorsData.join('\n');
                    
        return {
            csvData : csvDataObj.topOperatorsData,
            ejsData : ejsDataObj
        }
    }

    static createFileAndSendMail(options, allOperatorsDataObj, topOperatorsDataObj, done) {
        let billFetchDate = MOMENT().subtract(1, 'days').startOf('day').format('YYYY-MM-DD');

        ASYNC.parallel([
            (nextParallel) => {
                ASYNC.waterfall([
                    next => {
                        let fileName = `BillReminderReport-${billFetchDate}.csv`,
                            csvData = allOperatorsDataObj.csvData,
                            ejsData = allOperatorsDataObj.ejsData;

                        FS.writeFile(PATH.resolve(__dirname, fileName), csvData, (err) => {
                            if(err) {
                                next(err)
                            }
                            else {
                                next(null, fileName,csvData, ejsData)
                            }
                        })
                    },
                     //Send email to respective spocs
                    (fileName,csvData,  ejsData, next) => {
                        let template    = PATH.join( __dirname, './reportTemplate.ejs'),
                            attachments = [{
                                name : fileName,
                                content: new Buffer.from(csvData,'utf-8').toString('base64')
                            }];
                        let emailConfig =   _.cloneDeep(options.config.EMAIL_CONFIG);
                        _.set(emailConfig, ['EMAIL', 'SUBJECT'], 'Daily Bill Fetch Report - All Operators | '+  billFetchDate);
                        
                        utility.sendMail(
                            // mail config is in this
                            emailConfig.EMAIL,
                            // Content is rendered from EJS
                            EJS.render(FS.readFileSync(template, 'utf8'), { data : ejsData }),
                            attachments,
                            (err, res) => {
                                if(res) {
                                    options.L.log('mail sent', res)
                                } else {
                                    options.L.log('mail error', err)
                                }
                                
                                FS.unlinkSync(PATH.resolve(__dirname, fileName));
                                next()
                            }
                        ); 
                    }
                ],
                (err)=>{
                    if(err) {
                        options.L.error('DAILY_REPORT_ALL_OPERATORS ',err);
                        nextParallel(err)
                    }
                    nextParallel();
                })   
            },
            (nextParallel) => {
                ASYNC.waterfall([
                    next => {
                        let csvTopOperatorsData = topOperatorsDataObj.csvData,
                            ejsTopOperatorsData = topOperatorsDataObj.ejsData;

                        let fileName = `BillReminderTopOperatorsReport-${billFetchDate}.csv`
                        FS.writeFile(PATH.resolve(__dirname, fileName), csvTopOperatorsData, (err) => {
                            if(err) {
                                next(err)
                            }
                            else {
                                next(null, fileName,csvTopOperatorsData, ejsTopOperatorsData)
                            }
                        })
                    },
                     //Send email to respective spocs
                    (fileName, csvTopOperatorsData, ejsTopOperatorsData, next) => {
                        let template    = PATH.join( __dirname, './topOperatorsReportTemplate.ejs'),
                            attachments = [{
                                name : fileName,
                                content: new Buffer.from(csvTopOperatorsData,'utf-8').toString('base64')
                            }];
                        let emailConfig =   _.cloneDeep(options.config.EMAIL_CONFIG);
                        _.set(emailConfig, ['EMAIL', 'SUBJECT'], 'Daily Bill Fetch Report - Top Operators | '+ billFetchDate );
                        
                        utility.sendMail(
                            // mail config is in this
                            emailConfig.EMAIL,
                            // Content is rendered from EJS
                            EJS.render(FS.readFileSync(template, 'utf8'), { data : ejsTopOperatorsData }),
                            attachments,
                            (err, res) => {
                                if(res) {
                                    options.L.log('mail sent top operators', res)
                                } else {
                                    options.L.log('mail error top operators', err)
                                }
                                
                                FS.unlinkSync(PATH.resolve(__dirname, fileName));
                                next()
                            }
                        ); 
                    }
                ],
                (err)=>{
                    if(err) {
                        options.L.error('DAILY_REPORT_TOP_OPERATORS ',err);
                        nextParallel(err)
                    }
                    nextParallel();
                })   
            }
        ],
        (err)=> {
            if (err) {
                options.L.error('DAILY_REPORT',err);
                done(err)
            }
            else {
                done()
            }
        });
    }

    static getDataForEachOperator(aggregatorModel, options, done) {
        let endDate               = MOMENT().subtract(1, 'days').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            startDate             = MOMENT().subtract(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            operatorTableRegistry = _.get(options, 'config.OPERATOR_TABLE_REGISTRY', {}),
            uniqTableNames        = _.uniq(_.values(operatorTableRegistry)),
            returnObject          = {}
        
        ASYNC.eachSeries(
            
            uniqTableNames,

            (tableName, next) => {
                
                if (_.get(options, ['config','DYNAMIC_CONFIG', 'PUBLISHER_REPORT', 'EXCLUDE_TABLE_FROM_PUBLISHER_REPORT', tableName], null)) {
                    return next();
                }

                ASYNC.waterfall([
                    next => {
                        aggregatorModel.getDailyReportData(tableName, startDate, endDate, (err, rows)=> {
                            if(err) {
                                next(err)
                            }
                            else {
                                rows.forEach(row => {
                                    if (!_.get(returnObject, [row.service, row.operator], null)) {
                                        _.set(returnObject, [row.service, row.operator, 'statusData'], {})
                                    }
                                    _.set(returnObject, [row.service, row.operator, 'statusData', row.statusLabel], row.statusCount)
                                    
                                    // Send data to Datadog
                                    utility._sendMetricsToDD(row.statusCount, [
                                        'REQUEST_TYPE:'     +'DAILY_REPORT', 
                                        'STATUS:'           + row.statusLabel,
                                        'OPERATOR:'         + row.operator
                                    ]);
                                })
                                next()
                            }
                        })
                    },
                    next => {
                        let params = {
                            statuses          : [
                                _.get(options.config, 'COMMON.bills_status.PUBLISHED', 1),
                                _.get(options.config, 'COMMON.bills_status.DISABLED', 7),
                                _.get(options.config, 'COMMON.bills_status.ERROR_WHILE_PUBLISHING', 12),
                                _.get(options.config, 'COMMON.bills_status.NOT_IN_USE', 13)
                            ],
                            retryCount              : 3,
                            nextBillFetchDateFrom   : MOMENT().subtract(33, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                            nextBillFetchDateTo     : MOMENT().subtract(1,'days').endOf('day').format('YYYY-MM-DD HH:mm:ss')
                        };

                        aggregatorModel.fetchEligibleRecordsForPublishCount(tableName,params,function(err,rows){
                            if(err) {
                                next(err);
                            }
                            else {
                                rows.forEach(row => {
                                    _.set(returnObject, [row.service, row.operator, 'analyticsData', 'tobePublishedRecords'], row.recordsCount);
                                });
                                next();
                            }
                        });
                    },
                    next => {                    
                        let billDate = MOMENT().subtract(180, "days").startOf("day").format("YYYY-MM-DD HH:mm:ss"); // to filter invalid billDates rows
                        aggregatorModel.getBillDateStats(tableName, startDate, endDate, billDate, function (err, rows) {
                             if (err) {
                                  next(err);
                             } else {
                                  rows.forEach((row) => {
                                      _.set(returnObject, [row.service, row.operator, "billDateStats", "billFetchedSameDay"], row.sameDayCount);
                                      _.set(returnObject, [row.service, row.operator, "billDateStats", "eligibleBills"], row.totalbillfetched);
                                      _.set(returnObject, [row.service, row.operator, "billDateStats", "avgDayDifference"], row.avgDayDiff);
                                  });
                                  next();
                             }
                        });
                    }
                ], (err)=>{
                    if(err) {
                        options.L.error('getDataForEachOperator : error while aggregating data - ',err);
                    }
                    next();
                });

            },
            (err) => {
                if(err) {
                    options.L.error('getDataForEachOperator',err);
                }
                done(err,returnObject);
            }
        )
    }

    static helperCSVSortComparator(firstStr,secondStr) {
        let firstValue = firstStr.split(',')[1];
        let secondValue = secondStr.split(',')[1];
        return DailyReporter.helperSortComparator(firstValue, secondValue);
    }

    static helperSortComparator(firstValue,secondValue) {
        if(!isNaN(firstValue) && !isNaN(secondValue)) { // when both were numbers then desc. order
            return parseFloat(secondValue) - parseFloat(firstValue);
        } else if(!isNaN(firstValue) && isNaN(secondValue)) { // shift numbers first then blank entries
            return -1;
        } else if(isNaN(firstValue) && !isNaN(secondValue)) { // shift numbers first then blank entries
            return 1;
        } else {
            return 1;
        }
    }
    static isOperatorEligibleForNoRecordPublished(options, operator) {
        if( _.has(options, ['config','DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator,'EXCLUDE_OPERATOR_FROM_PUBLISHER_REPORT_FOR_ALERT_NO_RECORD_PUBLISHED']) ) {
            return false;
        }
        return true;
    }

}

export default DailyReporter
