import FS from 'fs'
import PATH from 'path'
import MOMENT from 'moment'
import _ from 'lodash'
import EJS from 'ejs'
import MODELS from '../models'
import CVRMODEL from '../models/catalogVerticalRecharge'
import utility from '../lib'
import L from 'lgr'

class NotificationReport {
    static exec(options, done) {

        let notificationModel = new MODELS.Notification(options)
        let cvrModel = new CVRMODEL(options)

        let categories = _.keys(options.config.NOTIFICATION.category)
        let templates = _.keys(options.config.NOTIFICATION.type)
        let date = MOMENT().subtract(1, 'days').format('YYYY-MM-DD 00:00:00')
        let endDate = MOMENT().format('YYYY-MM-DD 00:00:00')
        let ejsData = []

        L.log("NotificationReport::exec", "Getting Paritions to Select")

        this.getNextPartitionId(notificationModel, date, endDate)
            .then((partitions) => {
                L.log("NotificationReport::exec", "Partitions Range", partitions)
                let promises = Promise.resolve()

                for (let category of categories) {
                    for (let templateType of templates) {
                        promises = promises.then(() => {
                            return new Promise((resolve, reject) => {
                                let startTime = new Date().getTime()
                                notificationModel.getNotificationsCount((err, count) => {
                                    let endTime = new Date().getTime()
                                    let seconds = (endTime - startTime) / 1000

                                    if (seconds <= 60) {
                                        L.log('QueryTime::getNotificationsCount', 'Query took', seconds, 'seconds')
                                    } else {
                                        L.critical('QueryTime::getNotificationsCount', 'Query took', seconds, 'seconds for', category, templateType, partitions, date)
                                    }

                                    return err ? reject(err) : resolve(count)
                                }, category, templateType, partitions, date, endDate)
                            })
                        })
                            .then((data) => {
                                ejsData = ejsData.concat(data)
                            })
                            .catch((err) => {
                                L.error('NotificationReport::getNotificationsCount', 'Error Fetching Count', category, templateType, partitions, err)
                            })
                    }
                }

                return promises
            })
            .then(() => {
                return this.getOperatorNameFromCVR(ejsData, cvrModel)
            })
            .then((data) => {
                let operatorMap = this.createOperatorPIDMap(data)
                ejsData = this.computeOperatorOnlyCount(operatorMap, ejsData)
                let templatePath = PATH.join(__dirname, './notificationTemplate.ejs')
                let file = FS.readFileSync(templatePath, 'utf8')

                let fileData = {
                    data: ejsData,
                    map: operatorMap,
                    category: options.config.NOTIFICATION.category
                }

                let csvfile = this.createCSVFromData(fileData)
                let template = EJS.render(file, fileData)

                return new Promise((resolve, reject) => {

                    let EMAIL_CONFIG = options.config.EMAIL_CONFIG

                    EMAIL_CONFIG.EMAIL.SUBJECT = "Daily Reminder Notifications Report"

                    utility.sendMail(
                        EMAIL_CONFIG,
                        template,
                        [{
                            filename: `NotificationReport-${MOMENT().format('YYYY-MM-DD')}.csv`,
                            content: new Buffer.from(csvfile,'utf-8').toString('base64')
                        }],
                        (err, res) => {
                            return err ? reject(err) : resolve()
                        })
                })
            })
            .then(() => {
                done()
            })
            .catch((err) => {
                L.error("NotificationReport::exec", "Error Occured", err)
                done()
            })
    }

    static computeOperatorOnlyCount(map, data) {
        var dataMap = {}
        for (let row of data) {
            if (!map[row.product_id]) continue;
            // a key is created to aggregate data
            // currently it is on operator, service, paytype and template
            var key = [
                map[row.product_id].operator,
                map[row.product_id].service,
                map[row.product_id].paytype,
                row.type
            ].join("-")

            if (!dataMap[key]) {
                dataMap[key] = {
                    "operator": map[row.product_id].operator,
                    "paytype": map[row.product_id].paytype,
                    "service": map[row.product_id].service,

                    "type": row.type,

                    "pending": 0,
                    "sent": 0,
                    "error": 0
                }
            }

            dataMap[key]["pending"] += row.PENDING
            dataMap[key]["sent"] += row.SENT
            dataMap[key]["error"] += row.ERROR
        }

        return _.values(dataMap)
    }

    static createCSVFromData(fileData) {
        let csv = [
            [
                "Operator",
                "Service",
                "Paytype",
                "Type",
                "Pending",
                "Sent",
                "Error"
            ].join(",")
        ]
        for (let record of fileData.data) {
            csv.push([
                record.operator,
                record.service,
                record.paytype,
                record.type,
                record.pending,
                record.sent,
                record.error,
            ].join(","))

            let totalNotifications = record.sent + record.pending + record.error;
            let successRate = totalNotifications > 0 ? record.sent / totalNotifications : 100;
            // Send prepared data to prometheous
            if (record.sent > 0) utility._sendMetricsToDD(record.sent, ['REQUEST_TYPE:NOTIFICATION_REPORT', `OPERATOR:${record.operator}`, `SERVICE:${record.service}`, `PAYTYPE:${record.paytype}`, `TYPE:${record.type}`, 'STATUS:SENT']);
            if (record.pending > 0) utility._sendMetricsToDD(record.pending, ['REQUEST_TYPE:NOTIFICATION_REPORT', `OPERATOR:${record.operator}`, `SERVICE:${record.service}`, `PAYTYPE:${record.paytype}`, `TYPE:${record.type}`, 'STATUS:PENDING']);
            if (record.error > 0) utility._sendMetricsToDD(record.error, ['REQUEST_TYPE:NOTIFICATION_REPORT', `OPERATOR:${record.operator}`, `SERVICE:${record.service}`, `PAYTYPE:${record.paytype}`, `TYPE:${record.type}`, 'STATUS:ERROR']);
            if (successRate > 0) utility._sendMetricsToDD(successRate, ['REQUEST_TYPE:NOTIFICATION_REPORT', `OPERATOR:${record.operator}`, `SERVICE:${record.service}`, `PAYTYPE:${record.paytype}`, `TYPE:${record.type}`, 'STATUS:SUCCESS_RATE']);
        }
        return csv.join("\n")
    }

    static createOperatorPIDMap(data) {
        var map = {}
        for (let row of data) {
            map[row.product_id] = {
                operator: row.operator.replace(",", " "),
                circle: row.circle.replace(",", " "),
                service: row.service.replace(",", " "),
                paytype: row.paytype.replace(",", " ")
            }
        }
        return map
    }

    static getOperatorNameFromCVR(data, cvrModel) {
        let productIds = _.reduce(data, (result, obj) => {
            if (result.indexOf(obj['product_id']) < 0) {
                result.push(obj['product_id'])
            }
            return result
        }, [])

        L.log("NotificationReport::getOperatorNameFromCVR", "Getting Product Data", productIds.length)
        return new Promise((resolve, reject) => {
            if (productIds.length == 0) {
                return reject('No Products Available')
            }
            cvrModel.getCvrData((err, data) => {
                return err ? reject(err) : resolve(data)
            }, 'product_id in (' + productIds.join(',') + ')')
        })
    }

    /*
        this function returns the range of partition where it will find
        the data of only one day, in case in future more data is required
        update the partition range accordingly
    */
    static getNextPartitionId(notificationModel, date, endDate) {
        let startPartition = 0
        L.log("NotificationReport::getNextPartitionId", "Getting Paritions to Select")
        let promises = Promise.resolve()

        promises = promises.then(() => {
            return new Promise((resolve, reject) => {
                notificationModel.getPartitionRangeByDate((err, data) => {
                    L.log("NotificationReport::getPartitionRangeByDate", err, data)
                    if (err || !data || data.length != 1) {
                        return reject(err);
                    }
                    resolve(data[0]['id'])
                }, date)
            })
        })
            .then((partition) => {
                startPartition = partition
                return new Promise((resolve, reject) => {
                    notificationModel.getPartitionRangeByDate((err, data) => {
                        L.log("NotificationReport::getPartitionRangeByDate", err, data)
                        if (err || !data || data.length != 1) {
                            return reject(err);
                        }
                        resolve(data[0]['id'])
                    }, endDate)
                })
            })
            .then((parition) => {
                return Promise.resolve({
                    start: startPartition,
                    end: parition
                })
            })

        return promises
    }
}

export default NotificationReport
