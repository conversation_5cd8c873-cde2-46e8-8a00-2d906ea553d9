import sys
import boto3
from urllib.parse import urlparse
import json
from kafka import KafkaProducer
import pandas as pd
import fsspec
import s3fs
from datetime import datetime

KAFKA_TOPIC = "EMI_DUE_DETAILS"
CONFIG = {
    'development' : {
        # 'source_path' : "s3://daas-ingest-prod-ap-south-1/snapshots/paytm_herocorp/collection/1620297913073000/full",
        'source_path' : "s3://daas-computed-datasets-prod/snapshots/cdo/hero_corp_paytm_customers/1620462964493000",
        'KAFKA_STATE_PATH' : "s3://sms-parser/staging/emi_kafka_max_date.csv",
        'BOOTSTRAP_SERVER' : "localhost:9092"
    },
    'staging' : {
        # 'source_path' : "s3://daas-ingest-prod-ap-south-1/snapshots/paytm_herocorp/collection/1620297913073000/full",
        'source_path' : "s3://daas-computed-datasets-prod/snapshots/cdo/hero_corp_paytm_customers/1620462964493000",
        'KAFKA_STATE_PATH' : "s3://sms-parser/staging/emi_kafka_max_date.csv",
        'BOOTSTRAP_SERVER' : "***********:9092"
    },
    'production' : {
        # 'source_path' : s3://daas-ingest-prod-ap-south-1/snapshots/paytm_herocorp/collection/1620297913073000/full",  # DWH Bucket
        'source_path' : "s3://daas-computed-datasets-prod/snapshots/cdo/hero_corp_paytm_customers/1620462964493000",
        'KAFKA_STATE_PATH' : "s3://sms-parser/production/emi_kafka_max_date.csv",      # using same bucket as sms-parser for now
        'BOOTSTRAP_SERVER' : ["**********:9092","***********:9092","***********:9092"]
    }
}

ENV = sys.argv[1] if sys.argv[1] in ['staging','production'] else 'development'

print('Selected environment:{0}'.format(ENV))

KAFKA_PUSH = (sys.argv[2] == '1')

source_path = CONFIG[ENV]['source_path']
KAFKA_STATE_PATH = CONFIG[ENV]['KAFKA_STATE_PATH']
BOOTSTRAP_SERVER = CONFIG[ENV]['BOOTSTRAP_SERVER']

s3 = boto3.resource('s3')
kafka_producer = KafkaProducer(bootstrap_servers=BOOTSTRAP_SERVER)

def getCurrentTime():
    return datetime.now()

def get_kafka_max_date():
    try:
        return pd.read_csv(KAFKA_STATE_PATH)["max_updated_date"][0]
    except:
        return None

def update_kafka_max_date(max_date):
    df = pd.DataFrame([[max_date]],columns=["max_updated_date"])
    df.to_csv(KAFKA_STATE_PATH)
    print(getCurrentTime(),"kafka max date updated to {0}".format(max_date))

def get_bucket_key(s3_path):
    parsed_url = urlparse(s3_path, allow_fragments=False)
    bucket = parsed_url.netloc
    key = parsed_url.path.lstrip("/")
    return bucket, key

def emitTokafka(data):
    if KAFKA_PUSH == True:
        print(getCurrentTime(),"Trigger sending to Kafka for data",json.dumps(data))
        kafka_producer.send(KAFKA_TOPIC, json.dumps(data).encode('utf-8'))
    else:
        print(getCurrentTime(),"Skippping trigger to Kafka for data",json.dumps(data))

def list_files_s3_dir(s3_path,ignore_success_file=False):
    s3_bucket, s3_key = get_bucket_key(s3_path)
    my_bucket = s3.Bucket(s3_bucket)
    file_list = []
    if(ignore_success_file):
        for object_summary in list(my_bucket.objects.filter(Prefix=s3_key)):
            key = object_summary.key
            if("_SUCCESS" not in key):
                file_list.append("s3://{0}/{1}".format(object_summary.bucket_name, key))
    else:
        for object_summary in list(my_bucket.objects.filter(Prefix=s3_key)):
            file_list.append("s3://{0}/{1}".format(object_summary.bucket_name, object_summary.key))
    return file_list


def list_success_date_dirs(s3_path):
    file_list = list_files_s3_dir(s3_path)
    file_list = [x.rsplit('/', 1)[0] for x in file_list if "_SUCCESS" in x ]
    partition_set = set(file_list)
    return list(partition_set)


def list_latest_read_data(source_dataset_path,start_date=None, end_date=None, ignore_destination_max_date=False, ignore_today = True):
    destination_max_date = get_kafka_max_date()
    source_s3_dirs = list_success_date_dirs(source_dataset_path)
    return source_s3_dirs
    source_s3_dirs = sorted(source_s3_dirs, reverse=False, key=lambda x:x[-10:])
    source_s3_dirs= list(map(lambda x: x.replace("s3", "s3a"), source_s3_dirs))
    today_date = datetime.today().strftime('%Y-%m-%d')
    if(source_s3_dirs[-1][-10:]> today_date):
        sys.exit("Exiting Program future data found on source dataset {0}".format(source_dataset_id))
    if(ignore_today and source_s3_dirs[-1][-10:] == today_date):
        source_s3_dirs = source_s3_dirs[:-1]
    if(start_date):
        source_s3_dirs = list(filter(lambda x: x[-10:] >= start_date, source_s3_dirs))
    if(end_date):
        source_s3_dirs = list(filter(lambda x: x[-10:] <= end_date, source_s3_dirs))
    if(ignore_destination_max_date):
        return source_s3_dirs
    if(destination_max_date == None):
        new_source_dirs = source_s3_dirs
    else:
        new_source_dirs = list(filter(lambda x: x[-10:] > destination_max_date, source_s3_dirs))
    return new_source_dirs


def main():
    print(getCurrentTime(),'Script started...!!')
    latest_data_dirs = list_latest_read_data(source_path)
    if(len(latest_data_dirs)>0):
        for path in latest_data_dirs:
            partition_val = path[-10:]
            part_files = list_files_s3_dir(path,True)
            for part_file in part_files:
                print(getCurrentTime(),'main::Processing for file:{0}'.format(part_file))
                counter = 0
                df = pd.read_parquet(part_file)
                json_array = df.to_json(orient="records")
                for data in json.loads(json_array):
                    emitTokafka(data)
                    counter = counter + 1
                print(getCurrentTime(),'main::Processing done for file:{0} total records processed:{1}'.format(part_file,counter))
            update_kafka_max_date(partition_val)
    else:
        print(getCurrentTime(),"No new data found at source for sending to kafka")

if __name__ == "__main__":
    main()

"""
Script execution: {path}/src/crons/sms_parser_s3_to_kafka.py 'production' 1
SAMPLE filename: part-00000-d3726c18-fafc-4425-84a2-a3abce512089-c000.snappy.parquet
sample parquet file path: s3://daas-ml-sms-parser/prod/sms-parser/dt=2020-12-15/part-00000-7cc541d9-236a-4718-baed-161d3cdc83b7-c000.snappy.parquet
"""