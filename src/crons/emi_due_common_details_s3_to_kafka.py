import sys
import requests
import json
from datetime import datetime
import boto3
from kafka import KafkaProducer
import pandas as pd
import os.path
import time

CONFIGS = {
    'common' : {
        'processLatestFile' : True,
        'operator_config' : {
            'Aditya Birla Finance Limited' : {
                'abfl':'Aditya Birla Finance Limited',
                'path' : "/var/www/digital-reminder/emi_due_abfl.csv",
                'emi_olap' : {
                    'dataset_id' : 5064,
                    'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/5064/manifest'
                },
                "KAFKA_TOPIC": "EMI_DUE_COMMON_DETAILS"
            },
            'Fullerton India credit company limited' : {
                'credit' : 'Fullerton India credit company limited',
                'path' : "/var/www/digital-reminder/emi_due_fullerton.csv",
                'emi_olap' : {
                    'dataset_id' : 5060,
                    'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/5060/manifest'
                },
                "KAFKA_TOPIC": "EMI_DUE_COMMON_DETAILS"
            },
            'Clix' : {
                'clix' : 'Clix',
                'path' : "/var/www/digital-reminder/emi_due_clix.csv",
                'emi_olap' : {
                    'dataset_id' : 5236,
                    'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/5236/manifest'
                },
                "KAFKA_TOPIC": "EMI_DUE_COMMON_DETAILS"
            },
            'StashFin-CAAS' : {
                'StashFin' : 'StashFin-CAAS',
                'path' : "/var/www/digital-reminder/emi_due_stashfin-caas.csv",
                'emi_olap' : {
                    'dataset_id' : 5303,
                    'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/5303/manifest'
                },
                "KAFKA_TOPIC": "EMI_DUE_COMMON_DETAILS"
            },
            'L&T Finance Limited-CAAS' : {
                'LnT' : 'L&T Finance Limited-CAAS',
                'path' : "/var/www/digital-reminder/emi_due_lAndt_finance_limited-caas.csv",
                'emi_olap' : {
                    'dataset_id' : 5301,
                    'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/5301/manifest'
                },
                "KAFKA_TOPIC": "EMI_DUE_COMMON_DETAILS"
            }
        }
    },
    'development' : {
        'BOOTSTRAP_SERVER' : "localhost:9092"
    },
    'staging' : {
        'BOOTSTRAP_SERVER' : "***********:9092"
    },
    'production' : {
        'BOOTSTRAP_SERVER' : ["**********:9092","***********:9092","***********:9092"]
    }
}


class Loader():
    def __init__(self,operator):
        self.config = self.load_config()
        self.datasets = ['emi_olap']
        self.kafka_producer = KafkaProducer(bootstrap_servers=self.config['BOOTSTRAP_SERVER'])
        self.kafka_push = (sys.argv[2] == '1')
        self.operator = operator
        print(f'{self.getCurrentTime()} operator::'+self.operator)
        self.KAFKA_TOPIC = self.config['operator_config'][self.operator]['KAFKA_TOPIC']

    def getCurrentTime(self):
        return datetime.now()

    def load_config(self):
        # validate arguments and load config
        ENV = sys.argv[1] if sys.argv[1] in ['staging','production'] else 'development'
        print(f'{self.getCurrentTime()} environment::'+ENV)
        config = CONFIGS[ENV]
        common_config = CONFIGS['common']
        
        merged_config = {**config,**common_config}
        return merged_config

    def get_parquet_file_dir(self,dataset):

        try:
            response = requests.get(self.config['operator_config'][self.operator][dataset]['location_finder_url'])
            body = response.json()
            print(f'{self.getCurrentTime()} Response from get_parquet_file_dir for {dataset}, response body:{body}')
            if body and body['partitions']:
                key = 'full'
                print(f'{self.getCurrentTime()} Checking path for key: {key}')                
                if body['partitions'][key]:
                    return body['partitions'][key][:-1]
        except Exception as error:
            print(f'{self.getCurrentTime()} CRITICAL! Unable to get parquet file location for dataset:{dataset} with error:{error}')
        
        # could not get desired file path, so retrying after 10 seconds
        time.sleep(10)
        print(f'{self.getCurrentTime()} Trying again to get parquet file location for dataset:{dataset}')
        return self.get_parquet_file_dir(dataset)

    def get_filelist_from_s3(self,path):
        if not path:
            return None

        try:
            file_list = []
            s3_client = boto3.resource('s3')
            bucket = s3_client.Bucket('daas-computed-datasets-prod')
            
            # hero sample path: 's3://daas-computed-datasets-prod/snapshots/cdo/hero_corp_paytm_customers/1609854875847000/full/'
            # hero sample prefix: 'snapshots/cdo/hero_corp_paytm_customers/1609854875847000/full'
            # hero sample oject summary object: s3.ObjectSummary(bucket_name='daas-computed-datasets-prod', key='snapshots/cdo/hero_corp_paytm_customers/1609854875847000/full/part-00028-d8b9d5d4-e28f-4583-bee6-049d140ef888.c000.parquet')
            
            prefix = '/'.join(path.split('/')[3:]) 
            for object_summary in bucket.objects.filter(Prefix=prefix):
                if object_summary and object_summary.bucket_name and object_summary.key:
                    file_list.append(object_summary.key)
            return file_list
        except:
            print(f'{self.getCurrentTime()} CRITICAL! Unable to list files from path:{path}')
            return None

    def check_if_valid_record(self,record):
        try:
            mandatory_params = []
            error = []
            for param in mandatory_params:
                if param not in record or not record.get(param,None):
                    error.append(param)
                
            if len(error) > 0:
                return (False,','.join(error))
            else:
                return (True, None)
        except Exception as ex:
            return (False,ex)

    def get_progress_tracker_file_path(self):
        path = self.config['operator_config'][self.operator]['path']
        return path

    def get_csv_records(self,path):
        if not os.path.exists(path):
            return []

        records_df = pd.read_csv(path)
        records = records_df.to_json(orient="records")
        return json.loads(records)

    def get_status_offset_progress_csv(self,file_name):
        path = self.get_progress_tracker_file_path()
        records = self.get_csv_records(path)
        if self.config['processLatestFile']:
            return ("NEW_FILE",0)
        for record in records:
            if record["file_name"] == file_name:
                return (record["state"],record["records"])

        # no record found in file for this file_name
        return ("NEW_FILE",0)

    def update_progress_in_csv(self,dataset,file_name,progress_status='INITIATED',counter=0,invalid_records=0):
        print(f'{self.getCurrentTime()} PROGRESS_UPDATE: Updating csv file for dataset:{dataset},file_name:{file_name},progress_status:{progress_status},counter:{counter}')
        
        path = self.get_progress_tracker_file_path()
        headers = ['dataset','file_name','state','records','invalid_records']

        if os.path.exists(path):
            # read file and store in dictionary
            records = self.get_csv_records(path)
            records_to_insert = []
            file_exists = False
            for record in records:
                if record["file_name"] == file_name:
                    record["state"] = progress_status
                    record["records"] = counter
                    record["invalid_records"] = invalid_records
                    file_exists = True
                
                records_to_insert.append([ record["dataset"],record["file_name"],record["state"],record["records"],record["invalid_records"] ])
            
            if not file_exists:
                records_to_insert.append([dataset,file_name,progress_status,counter,invalid_records])
        else:
            records_to_insert = [[dataset,file_name,progress_status,counter,invalid_records]]

        record_df = pd.DataFrame(records_to_insert,columns=headers)
        record_df.to_csv(path)

        return
    
    def emitTokafka(self, data):
        data['reminder_operator'] = self.config['operator_config'][self.operator][data['operator']]
        if self.kafka_push == True:
            print(self.getCurrentTime(),"Trigger sending to Kafka for data", json.dumps(data))
            self.kafka_producer.send(self.KAFKA_TOPIC, json.dumps(data).encode('utf-8'))
        else:
            print(self.getCurrentTime(),"Skippping trigger to Kafka for data",json.dumps(data))

    def process_file(self,directory,file_name,dataset):
        status,offset = self.get_status_offset_progress_csv(file_name)
        if status == "PROCESSED":
            print(f'{self.getCurrentTime()} file already processsed:{file_name} skipping it...')
            return
        else:
            print(f'{self.getCurrentTime()} Processing file:{file_name} from status:{status} offset:{offset}')

        try:
            s3_file_location = '/'.join([directory,file_name])
            df = pd.read_parquet(s3_file_location)
            row_json_array = df.to_json(orient="records")
            print(f'{self.getCurrentTime()} Parsing JSON response got from dataframe')
            json_array = json.loads(row_json_array)
            print(f'{self.getCurrentTime()} Parsing done, now iterating and processing records from dataframe for dataser file:{file_name}')
            counter = 0
            invalid_records = 0

            # update file status as started only if its starting for the first time, else do not override status
            if offset == 0:
                self.update_progress_in_csv(dataset,file_name,'STARTED',counter)
            
            for record in json_array:
                counter+=1

                # if counter > offset:
                valid,error = self.check_if_valid_record(record)

                if valid:
                    self.emitTokafka(record)
                else:
                    invalid_records+=1
                    print(f'{self.getCurrentTime()} ERR! record validation error:{error} for {record}')

                # keeping trace for batch of 1000 records interval
                if counter % 1000 == 0:
                    print(f'{self.getCurrentTime()} PROGRESS: for dataset:{dataset} and file:{file_name} - {counter} , updating it in File')
                    if counter > offset:
                        self.update_progress_in_csv(dataset,file_name,'PROCESSING',counter,invalid_records)

            self.update_progress_in_csv(dataset,file_name,'PROCESSED',counter,invalid_records)
            print(f'{self.getCurrentTime()} Processing completed for dataset:{dataset} and file:{file_name}. Processed Records:{counter}')
        except Exception as ex:
            print(f'{self.getCurrentTime()} CRITICAL! Error in process_file for dataset:{dataset} and file:{file_name}:{ex}')
            return

    def load_data(self,dataset):
        directory = self.get_parquet_file_dir(dataset)
        files = self.get_filelist_from_s3(directory)

        if not files:
            return
        else:
            print(f'{self.getCurrentTime()} File List: {files}')

        for file_location in files:
            file_name = file_location.split('/')[-1]
            print(f'{self.getCurrentTime()} Start Processing dataset:{dataset} and file:{file_name}')
            self.process_file(directory,file_name,dataset)
            print(f'{self.getCurrentTime()} Finished Processing dataset:{dataset} and file:{file_name}')
            time.sleep(4)

    def load(self):
        for dataset in self.datasets:
            print(f'{self.getCurrentTime()} Going to process dataset:{dataset}')
            self.load_data(dataset)

def validateArgument():
    if (sys.argv[1] not in ['staging','production' , 'development']):
        return [False , 'Invalid argument for environment']
    if (not (sys.argv[2] == '1' or sys.argv[2] == '0')):
        return [False , 'Invalid argument for Kafka push flag']
    if (len(sys.argv) >= 5):
        return [False , 'Invalid number of arguments']
    if (len(sys.argv) == 3):
        return [True , 'Executing cron for all eligible operators:'+ ','.join(list(CONFIGS['common']['operator_config'].keys()))]
    if (len(sys.argv) == 4):
        operatorsList = list(map(lambda operator : operator.strip() , sys.argv[3].split(",")))
        # if len(operatorsList) == 0:
        #     return [False , 'No operator name specified']
        #cases : null, "" , ","
        for operator in operatorsList:
            if operator not in CONFIGS['common']['operator_config']:
                return [False , 'Operator not eligible:'+ operator]
        return [True , 'Executing cron for operators:'+ ','.join(operatorsList)]

def getOperatorName():
    if (len(sys.argv) == 3):
        return list(CONFIGS['common']['operator_config'].keys())
    elif (len(sys.argv) == 4):
        return list(map(lambda operator : operator.strip() , sys.argv[3].split(",")))

def execute():
    valid, message = validateArgument()
    if not valid:
        print(f'{datetime.now()} Error in Starting cron with message {message}')
        return
    else:
        print(f'{datetime.now()} Starting cron with message {message}')
    operatorsList = getOperatorName()
    # case no operator
    for operator in operatorsList:
        loader = Loader(operator)
        loader.load()

if __name__ == "__main__":
    execute()
    


"""
SAMPLE data : 
manifest location path :http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/4967/manifest
data received: {
    "dataset_id":4967,
    "partitions":
        {"full":"s3://daas-computed-datasets-prod/snapshots/cdo/hero_corp_paytm_customers/1621235146782000/full/"},
    "metadata":null,
    "max_date":"2021-05-16T23:59:59.000+05:30"
    }

Flow: (Task: https://jira.mypaytm.com/browse/IN-20023)
1. API call k though -> path -> and then list all the files in it
2. Iterate one by one and put state in some csv file locally with its offset->  with states = STARTED, COMPLETED
3. Publish to kafka
4. Logging for each file and its offset….
5. File wise status -> 

sudo python3.7 <file> <env> <kafka_push> <operator_name_1,operator_name_2,...,operator_name_n>
sudo python3.7 emi_due_common_details_s3_to_kafka.py staging 0 'Aditya Birla Finance Limited'
"""