<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>

<body bgcolor="#FFFFFF" topmargin="0" leftmargin="0" marginheight="0"
  marginwidth="0">
    <% if (data.billFetchAttemptReport.length) { %>
        <div> 
            <b>Table 1 --> Bill Fetch Attempt %</b>
        </div>
        <table style="border:1px solid black;border-collapse:collapse;">
            <tbody>
                <tr>
                    <th style="border:1px solid;">Operator</th>
                    <th style="border:1px solid;">Eligible</th>
                    <th style="border:1px solid;">Attempted</th>
                    <th style="border:1px solid;">Not attempted</th>                    
                    <th style="border:1px solid;">% of Attempted</th>
                    <th style="border:1px solid;">% of Not attempted</th>                    
                </tr>
                <% data.billFetchAttemptReport.forEach(function(record) { %>
                    <tr>
                        <td style="border:1px solid;"><%= record.operator %></td>
                        <td style="border:1px solid;"><%= record.totalEligibleRecords %></td>
                        <td style="border:1px solid;"><%= record.total %></td>
                        <td style="border:1px solid;"><%= record.tobePublishedRecords %></td>
                        <td style="border:1px solid;"><%= record.totalPercentage %></td>
                        <td style="border:1px solid;"><%= record.tobePublishedRecordsPer %></td>
                    </tr>
                <% }) %>
            </tbody>
        </table>
    <% } %>
    <br></br>
    <% if (data.breakupAttemptPercentageReport.length) { %>
        <div> 
            <b>Table 2 --> Breakup of Attempted Records (in % upto 2 decimals)</b>
        </div>
        <table style="border:1px solid black;border-collapse:collapse;">
            <tbody>
                <tr>
                    <th style="border:1px solid;">Operator</th>
                    <th style="border:1px solid;">New Bill</th>
                    <th style="border:1px solid;">Old Bill</th>
                    <th style="border:1px solid;">No Bill</th>
                    <th style="border:1px solid;">Payment Done</th>
                    <th style="border:1px solid;">NPTO</th>
                    <th style="border:1px solid;">Connection Errors</th>
                    <th style="border:1px solid;">Validation Failure</th>
                    <th style="border:1px solid;">Timeout</th>
                    <th style="border:1px solid;">Wrong Due Date Format</th>
                    <th style="border:1px solid;">Errors while publishing</th>
                    <th style="border:1px solid;">Not In Use</th>
                    <th style="border:1px solid;">Disabled</th>
                    <th style="border:1px solid;">Max Retry Reached</th>
                </tr>
                <% data.breakupAttemptPercentageReport.forEach(function(record) { %>
                    <tr>
                        <td style="border:1px solid;"><%= record.operator %></td>                
                        <td style="border:1px solid;"><%= record.billFetchPercentage %></td>                
                        <td style="border:1px solid;"><%= record.oldBillPercentage %></td>                
                        <td style="border:1px solid;"><%= record.noBillPercentage %></td>                
                        <td style="border:1px solid;"><%= record.paymentDonePercentage %></td>                
                        <td style="border:1px solid;"><%= record.promotedToNextCyclePercentage %></td>                
                        <td style="border:1px solid;"><%= record.connectionErrorRate %></td>                
                        <td style="border:1px solid;"><%= record.validationFailureRate %></td>                
                        <td style="border:1px solid;"><%= record.notReceivedPercentage %></td>                
                        <td style="border:1px solid;"><%= record.wrongDueDateFmtPercentage %></td>                
                        <td style="border:1px solid;"><%= record.errorsWhilePublishingPercentage %></td>                
                        <td style="border:1px solid;"><%= record.notinUsePercentage %></td>                
                        <td style="border:1px solid;"><%= record.disabledPercentage %></td>                
                        <td style="border:1px solid;"><%= record.maxRetryReachedPercentage %></td>                
                    </tr>
                <% }) %>
            </tbody>
        </table>
    <% } %>
    <br></br>
    <% if (data.breakupAttemptValuesReport.length) { %>
        <div> 
            <b>Table 3 --> Breakup of Attempted Records (actual numbers) </b>
        </div>
        <table style="border:1px solid black;border-collapse:collapse;">
            <tbody>
                <tr>
                    <th style="border:1px solid;">Operator</th>
                    <th style="border:1px solid;">New Bill</th>
                    <th style="border:1px solid;">Old Bill</th>
                    <th style="border:1px solid;">No Bill</th>
                    <th style="border:1px solid;">Payment Done</th>
                    <th style="border:1px solid;">NPTO</th>
                    <th style="border:1px solid;">Connection Errors</th>
                    <th style="border:1px solid;">Validation Failure</th>
                    <th style="border:1px solid;">Timeout</th>
                    <th style="border:1px solid;">Wrong Due Date Format</th>
                    <th style="border:1px solid;">Errors while publishing</th>
                    <th style="border:1px solid;">Not In Use</th>
                    <th style="border:1px solid;">Disabled</th>
                    <th style="border:1px solid;">Max Retry Reached</th>
                    <th style="border:1px solid;">Avg days diff</th>
                </tr>
                <% data.breakupAttemptValuesReport.forEach(function(record) { %>
                    <tr>
                        <td style="border:1px solid;"><%= record.operator %></td>                
                        <td style="border:1px solid;"><%= record.totalBillFetched %></td>                
                        <td style="border:1px solid;"><%= record.totalOldBillFound %></td>                
                        <td style="border:1px solid;"><%= record.totalBillNotFound %></td>                
                        <td style="border:1px solid;"><%= record.totalPaymentDone %></td>                
                        <td style="border:1px solid;"><%= record.totalPromotedToNextCycle %></td>                
                        <td style="border:1px solid;"><%= record.totalConnectionError %></td>                
                        <td style="border:1px solid;"><%= record.totalValidationFailure %></td>                
                        <td style="border:1px solid;"><%= record.totalNotReceived %></td>                
                        <td style="border:1px solid;"><%= record.totalWrongDueDateFmt %></td>                
                        <td style="border:1px solid;"><%= record.totalErrorsWhilePublishing %></td>                
                        <td style="border:1px solid;"><%= record.notinUse %></td>                
                        <td style="border:1px solid;"><%= record.disabled %></td>                
                        <td style="border:1px solid;"><%= record.maxRetryReached %></td>                
                        <td style="border:1px solid;"><%= record.avgDayDifference %></td>                
                    </tr>
                <% }) %>
            </tbody>
        </table>
    <% } %>
    <br></br>
    <b>Where:</b>
    <ul>
       <li><b>Attempted: </b>Total number of records which were picked for bill fetch</li>
       <li><b>New Bill: </b>Count of records for which a positive bill amount is fetched from operator for a future due date</li>
       <li><b>Old Bill: </b>Count of records for which old bill is returned from operator, means due date was old or amount was negative</li>
       <li><b>No Bill: </b>Count of records for which No bill is returned from operator (no due date, no amount or error message code from gateway is 1,3 or 11)</li>
       <li><b>NPTO (Not Published to Operator): </b>Count of records not sent to operator as operator queue was already full.</li>
       <li><b>Timeout: </b>TimeOuts from Operator.</li>
       <li><b>Connection Errors: </b>Validation failed due to connectivity issues between us and opertaor.</li>
       <li><b>Validation Failure: </b>Validation failed due to other reasons, which can be anything, either wrong CA number, no bill amount or anything.</li>
       <li><b>Wrong Due Date Format: </b>The date received from gateways is not in accepable format, our acceptable date formats are : (YYYY-MM-DD, DD-MM-YYYY).</li>
       <li><b>Payment Done: </b>Count of records for which payment is done for operator.</li>
       <li><b>Errors while publishing: </b>Total number of errors encountered while publishing the data for operator.</li>
       <li><b>Not In Use: </b>Count of the records for which payment not done thrugh paytm since last 90 days for operator.</li>
       <li><b>Disabled: </b>Total number of records that are disabled for operator.</li>
       <li><b>Max Retry Reached: </b>Total number of records for which retry count reached to MAX(3) for operator.</li>
       <li><b>% of Attempted : </b> Percentage of (Total number of records which were picked for bill fetch) out of Total Eligible Records</li>
       <li><b>% of Not attempted : </b> Percentage of (To Be Published records) out of Total Eligible Records</li>       
    </ul>
</body>
</html>