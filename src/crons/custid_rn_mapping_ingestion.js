import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import BILLS from '../models/bills'
import cassandraBills from '../models/cassandraBills';
import utility from '../lib'
import OAuth from '../lib/oauth';
import ASYNC from 'async'

let serviceName = "custid_rn_mapping_ingestion" 
let progressFilePath = `/var/log/digital-reminder/custid-rn-mapping-ingestion-progress-${MOMENT().format('YYYY-MM')}.json`
let progressTracker = {}
/** Maintain offset and processed files in a path */
class CustIdRnMappingIngestion {
    constructor(options) {

        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.cassandraBills = new cassandraBills(options);
        this.infraUtils = options.INFRAUTILS;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        this.csvIngester = new AWSCsvIngester(options , this.updateProgress.bind(this))
        this.logPrefix = serviceName
        this.batchSize = _.get(options.config,['DYNAMIC_CONFIG',serviceName,'batch_size','value'],100)
        this.operatorMapping = {}
        this.currentFile=""
        this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
        this.folderPath = `${_.get(options.config , ['DYNAMIC_CONFIG','CUSTID_RN_MAPPING_INGESTION','path','value'],'digital-reminder/CUSTID_RN_MAPPING_DUMP')}`;

        this.files = []
        /** As per product requirement the CSV will have operator name without spaces
         * So we need to create a mapping of Operator name(with removed spaces ) -> table_name
         */
        for (const [key, value] of Object.entries(_.get(this.config, 'OPERATOR_TABLE_REGISTRY', {}))){ 
            this.operatorMapping[key.replace(/\s/g,'')]=key
            this.operatorMapping[key]=key
        }

    }

    getProgressObject(){
        let progress = {}
        this.L.info("Loading progress object from",progressFilePath)
        if(fs.existsSync(progressFilePath)){
            const progressData = fs.readFileSync(progressFilePath , 'utf-8')
            progress = JSON.parse(progressData)
        }
        this.L.info("Loaded",progress)
        return progress
    }   

    updateProgress(filename ,count  ){
        if(_.get(progressTracker , [filename] , 0 ) == -1)return;
        _.set(progressTracker , [filename] , count )
        this.L.info("Updated progess Object",JSON.stringify(progressTracker),count)
        fs.writeFileSync(progressFilePath , JSON.stringify(progressTracker , null  , 2))
    }

    filterFileByMonth(filename){
        try{
           let date = filename.split('$')[1].split('.')[0].slice(0,7)
           if(date == MOMENT().format('YYYY-MM'))return true

        }catch(err){
            return false
        }
        return false

    }

    start(callback){
        let self = this;
        
        const processFiles = () => {
            progressTracker = this.getProgressObject();

            try {
                this.csvIngester.configure(this.bucketName, this.logPrefix, this.batchSize);
            } catch(error) {
                self.L.critical(this.logPrefix, "Cannot initialize AWS");
                setTimeout(processFiles, 5 * 60 * 1000);
                return;
            }

            self.L.info("Getting Files in the folder");

            this.csvIngester.getFileNames(this.folderPath, function(err, data) {
                if(err) {
                    self.L.error("Error while getting files");
                    setTimeout(processFiles, 5 * 60 * 1000);
                } else {
                    data = _.filter(data, self.filterFileByMonth);
                    if (data.length === 0) {
                        self.L.info("No new files to process, scheduling next check");
                        setTimeout(processFiles, 5 * 60 * 1000);
                        return;
                    }
                    return eachSeries(data, self.processEachFile.bind(self), (err) => {
                        if(err) {
                            self.L.error("Error processing files:", err);
                        }
                        self.L.info("Completed processing all files, scheduling next check");
                        setTimeout(processFiles, 5 * 60 * 1000);
                    });
                }
            });
        };

        processFiles();
    }

    processEachFile(filename , callback){
        if(_.get(progressTracker , filename, null) == -1){
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ",filename , "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ",filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker , [filename], 0) 
        this.csvIngester.start(this.processRecordinBatch.bind(this),filename , function(error , data){
            if (error) {
                this.L.error("Error processing file:", filename, error)
                return callback(error)
            }
            return callback()
        },skipRows)
    }

    async processRecordinBatch(data){
        let self = this;
        return new Promise((resolve , reject)=>{
            eachLimit(data ,self.batchSize, function(record , cb){
                self.processRecord(function(){
                    cb()
                } ,record)
            },function(error){
                if(error){
                    self.L.error(self.logPrefix , "Error while processing batch",error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }
    
    processRecord(cb, record) {
        let self = this;
        let debug_key = `${record.customer_id}_${record.service}_${record.recharge_number}_${record.operator}`;
        _.set(record, 'debug_key', debug_key);
        //let sanitizedFilename = _.get(this.currentFile, 'UNKNOWN').replace(/[\$\s]/g, '_');
        utility._sendMetricsToDD(1, [
            `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
            `STATUS:TRAFFIC`, 
            `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
            `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
        ]);
        let validationSuccess = self.validateRecordForIngestion(record);
        if (validationSuccess === false) {
            self.L.error('processRecord::', `validation failed for record ${debug_key}`);
            return cb();
        }

        self.L.log('processRecord::', `validation success for record ${debug_key}`);
        utility._sendMetricsToDD(1, [
            `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
            `STATUS:VALIDATION_SUCCESS`, 
            `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
            `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
        ]);

        ASYNC.waterfall([
            (next) => {
                self.checkForUserAgent(record, next);
            },
            (next) => {
                self.fetchRecordStatusAndOtherInfo(record, next);
            },
            (next) => {
                self.cassandraBills.dumpIntoCustIdRnMappingTable(next, record);
            }
        ], function (error) {
            if (error) {
                self.L.error('custIdRnUniversalDump::processRecord::', `error occurred while processing for record ${debug_key}`, error);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                    `STATUS:INGESTION_FAILED`, 
                    `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                    `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
                ]);
                return cb();
            }
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:INGESTION_SUCCESS`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
            ]);
            cb();
        })
    }

    checkForUserAgent(record, callback) {
        let self = this;
        let userAgentLimit = _.get(self.config, ['DYNAMIC_CONFIG', 'CUST_ID_RN_MAPPING_INGESTION', 'USER_AGENT', 'RECORD_LIMIT'], 100);
        self.cassandraBills.get_cust_id_rn_mapping_records(record.customer_id, record.service)
        .then((records) => {
            if (records.length > Number(userAgentLimit)) {
                self.L.error('checkForUserAgent::', `user agent case found, stopping further execution for customer id ${record.customer_id}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                    `STATUS:USER_AGENT_CASE`, 
                    `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                    `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
                ]);
                return callback(`user agent case found, stopping further execution for customer id ${record.customer_id}`);
            }
            self.L.log('checkForUserAgent::', `customer id ${record.customer_id} is not a user agent, proceeding further execution`);
            return callback();
        })
        .catch((error) => {
            self.L.error('checkForUserAgent::', `unable to check for user agent, error occurred ${error}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:ERROR_IN_CHECK_FOR_USER_AGENT`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
            ]);
            return callback(error);
        });
    }

    validateRecordForIngestion(record) {
        let self = this;
        let customerId = _.get(record, 'customer_id', null);
        let service = _.get(record, 'service', null);
        let rechargeNumber = _.get(record, 'recharge_number', null);
        let operator = _.get(record, 'operator', null);
        let productId = _.get(record, 'product_id', null);

        if (_.isNull(customerId) || customerId.trim() == '' || isNaN(Number(customerId))) {
            self.L.error(self.currentFile, `customer id is missing or NaN for record ${record.debug_key}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:VALIDATION_FAILED`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`, 
                "TYPE:CUSTID_MISSING_OR_NAN"
            ]);
            return false;
        } 
        if (_.isNull(service) || service.trim() == '') {
            self.L.error(self.currentFile, `service is missing for record ${record.debug_key}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:VALIDATION_FAILED`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`, 
                "TYPE:SERVICE_MISSING"
            ]);
            return false;
        }
        if (_.isNull(rechargeNumber) || rechargeNumber.trim() == '') {
            self.L.error(self.currentFile, `recharge number is missing for record ${record.debug_key}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:VALIDATION_FAILED`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`, 
                "TYPE:RECHARGE_NUMBER_MISSING"
            ]);
            return false;
        }
        if (_.isNull(operator) || operator.trim() == '') {
            self.L.error(self.currentFile, `operator is missing for record ${record.debug_key}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:VALIDATION_FAILED`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`, 
                "TYPE:OPERATOR_MISSING"
            ]);
            return false;
        }
        if (_.isNull(productId) || productId.trim() == '') {
            self.L.error(self.currentFile, `product id is missing for record ${record.debug_key}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:VALIDATION_FAILED`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`, 
                "TYPE:PRODUCT_ID_MISSING"
            ]);
            return false;
        }

        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
        if (!tableName) {
            self.L.error(self.currentFile, `table doesn't exist corresponding operator ${operator} for record ${record.debug_key}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                `STATUS:VALIDATION_FAILED`, 
                `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`, 
                "TYPE:TABLE_DOESNT_EXIST_IN_REGISTRY"
            ]);
            return false;
        }

        return true;
    }

    fetchRecordStatusAndOtherInfo(record, cb) {
        let self = this;
        let tableName = _.get(self.config,  ['OPERATOR_TABLE_REGISTRY', record.operator], null); 
        let meta = {};
        self.bills.getBill((err, data) => {
            if (err) {
                self.L.error('fetchRecordStatusAndOtherInfo::', `error occurred for record ${record.debug_key}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                    `STATUS:ERROR_IN_GET_BILL_CALL`, 
                    `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                    `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
                ]);
                return cb(err);
            } else if (data && data.length > 0) {
                self.L.log('fetchRecordStatusAndOtherInfo::', `record found in mysql for ${record.debug_key}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                    `STATUS:RECORD_STATUS_ACTIVE`, 
                    `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                    `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
                ]);
                _.set(record, 'status', 'ACTIVE');
                _.set(meta, 'deleted_bills_status', null);
                _.set(meta, 'deleted_bills_notification_status', null);
            } else {
                self.L.log('fetchRecordStatusAndOtherInfo::', `record not found in mysql for ${record.debug_key}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_CSV_INGESTION`, 
                    `STATUS:RECORD_STATUS_DELETED`, 
                    `SERVICE:${_.get(record, 'service', 'UNKNOWN')}`, 
                    `OPERATOR:${_.get(record, 'operator', 'UNKNOWN')}`
                ]);
                _.set(record, 'status', 'DELETED');
                _.set(meta, 'deleted_bills_status', null);
                _.set(meta, 'deleted_bills_notification_status', 1);
            }
            _.set(meta, 'user_type', 'RU');
            _.set(record, 'meta', JSON.stringify(meta));
            return cb();
        }, tableName, record.operator, record.customer_id, record.product_id, record.recharge_number); 
    }
}



(function main() {
    if (require.main === module) {
        const runScript = () => {
            startup.init({}, function(err, options) {
                if (err) {
                    console.error("Failed to initialize:", err);
                    process.exit(1);
                }

                let script;
                try {
                    options.L.info("Starting CustIdRnMappingIngestion script");
                    script = new CustIdRnMappingIngestion(options);
                    script.start(function(err) {
                        if (err) {
                            options.L.error("Script execution failed:", err);
                            process.exit(1);
                        }
                        options.L.info("Script completed successfully, scheduling next run in 5 minutes");
                        setTimeout(runScript, 5 * 60 * 1000);
                    });
                } catch(err) {
                    options.L.error("Unexpected error during script execution:", err);
                    process.exit(1);
                }
            });
        };

        runScript();
    }
})();


export default CustIdRnMappingIngestion