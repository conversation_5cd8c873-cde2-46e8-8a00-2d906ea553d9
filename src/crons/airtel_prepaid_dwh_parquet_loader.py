import sys
import requests
import json
from datetime import datetime,timedelta
import boto3
import pandas as pd
import mysql.connector
import os.path
import time
import json
from mergedeep import merge

CREDIANTIAL_CONFIGS_PATH = {
    'development' : { 
        'path' : '../digital_reminder_configs.json'
    },
    'staging' : {
        'path' : '/var/www/digital-reminder/releases/digital_reminder_configs.json'
    },
    'production' : {
        'path' : '/var/www/digital-reminder/releases/digital_reminder_configs.json'
    }
}
CONFIGS = {
    'common' : {
        'table' : 'bills_airtelprepaid',
        'operator' :'airtel',
        'paytype': 'prepaid',
        'service' :'mobile'
    },
    'development' : {
        'airtel_olap' : {
            'dataset_id' : 10682,
            'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/10682/manifest'
        },
        'airtel_churn_olap' : {
            'dataset_id' : 4531 ,
            'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/4531/manifest'
        },
        'sql' : {
            'host'    : 'localhost',
            'database': 'digital_reminder',
        }
    },
    'staging' : {
        'airtel_olap' : {
            'dataset_id' : 4530,
            'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/4530/manifest'
        },
        'airtel_churn_olap' : {
            'dataset_id' : 4531 ,
            'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/4531/manifest'
        },
        'sql' : {
            'host'    : '***********',
            'database': 'digital_reminder',
        }
    },
    'production' : {
        'airtel_olap' : {
            'dataset_id' : 4531,
            'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/4531/manifest'
        },
        # 'airtel_churn_olap' : {
        #     'dataset_id' : 4531 ,
        #     'location_finder_url' : 'http://dataset-service-prod.mm7pbnhhzr.ap-south-1.elasticbeanstalk.com/v2/datasets/4531/manifest'
        # },
        'sql' : {
            'host'    : 'digital_reminderwrite.prod.paytmdgt.io',
            'database': 'digital_reminder',
        }
    }
}

class Loader():
    def __init__(self):
        self.config = self.load_config()
        self.connection = self.create_connection()
        self.datasets = ['airtel_olap']
        self.partition_date = self.find_partition_date_to_process()
        self.dry_run = int(sys.argv[3]) if len(sys.argv) > 3 else 1
        self.grey_scale = int(sys.argv[4]) if len(sys.argv) > 4 else 1
        self.grey_scale_batch = int(sys.argv[5]) if len(sys.argv) > 5 else 4000

    def getCurrentTime(self):
        return datetime.now()

    def load_config(self):
        # validate arguments and load config
        ENV = sys.argv[1] if len(sys.argv) > 1 and sys.argv[1] in ['staging','production'] else 'development'
        START_BILL_FETCH_DURATION = int(sys.argv[2]) if len(sys.argv) > 2 else 3

        config = CONFIGS[ENV]
        config['nbfd'] = {
            'airtel_olap' : datetime.strftime(datetime.today() - timedelta(days=START_BILL_FETCH_DURATION), '%Y-%m-%d'),
            'airtel_churn_olap' : datetime.strftime(datetime.today() - timedelta(days=START_BILL_FETCH_DURATION+1), '%Y-%m-%d')
        }
        common_config = CONFIGS['common']
        CREDIANTIAL_CONFIGS = json.load(open(CREDIANTIAL_CONFIGS_PATH[ENV]['path'],))
        config = merge({}, config, CREDIANTIAL_CONFIGS['airtel_prepaid_config'][ENV])
        merged_config = {**config,**common_config}
        return merged_config

    def find_partition_date_to_process(self):
        today = datetime.today()
        first = today.replace(day=1)
        lastMonth = first - timedelta(days=1)
        partition_date = lastMonth.strftime("%Y-%m-%d")
        return "partition_date=" + partition_date

    def get_parquet_file_dir(self,dataset):

        try:
            response = requests.get(self.config[dataset]['location_finder_url'])
            body = response.json()
            print(f'{self.getCurrentTime()} Response from get_parquet_file_dir for {dataset}, response body:{body}')
            if body and body['partitions']:
                key = self.find_partition_date_to_process()
                print(f'{self.getCurrentTime()} Checking path for key: {key}')                
                if body['partitions'][key]:
                    return body['partitions'][key]
        except Exception as error:
            print(f'{self.getCurrentTime()} CRITICAL! Unable to get parquuet file location for dataset:{dataset} with error:{error}')
        
        # could not get desired file path, so retrying after 10 seconds
        time.sleep(10)
        print(f'{self.getCurrentTime()} Trying again to get parquet file location for dataset:{dataset}')
        return self.get_parquet_file_dir(dataset)

    def get_filelist_from_s3(self,path):
        if not path:
            return None

        try:
            file_list = []
            s3_client = boto3.resource('s3')
            bucket = s3_client.Bucket('daas-computed-datasets-prod')
            # sample path: 's3://daas-computed-datasets-prod/snapshots/cdo/airtel_olap/1609854875847000/partition_date=2020-12-31'
            # sample prefix:'snapshots/cdo/airtel_olap/1609854875847000/partition_date=2020-12-31'
            # sample oject summary object: s3.ObjectSummary(bucket_name='daas-computed-datasets-prod', key='snapshots/cdo/airtel_churn_olap/1609854964824000/partition_date=2020-12-31/part-00028-d8b9d5d4-e28f-4583-bee6-049d140ef888.c000.parquet')
            prefix = '/'.join(path.split('/')[3:]) 
            for object_summary in bucket.objects.filter(Prefix=prefix):
                if object_summary and object_summary.bucket_name and object_summary.key:
                    file_list.append(object_summary.key)
            return file_list
        except:
            print(f'{self.getCurrentTime()} CRITICAL! Unable to list files from path:{path}')
            return None

    def check_if_valid_record(self,record):
        try:
            mandatory_params = ['customer_id', 'recharge_number_1', 'product_id']
            error = []
            for param in mandatory_params:
                if param not in record or not record.get(param,None):
                    error.append(param)
                
            if len(error) > 0:
                return (False,','.join(error))
            else:
                return (True, None)
        except Exception as ex:
            return (False,ex)

    def get_extra_params(self,dataset):
        extra_params = {'table' : self.config['table'],'operator':self.config['operator'],'paytype': self.config['paytype'],'service': self.config['service'], 'next_bill_fetch_date':self.config['nbfd'][dataset]}
        return extra_params

    def update_record_db(self,record,dataset,trace_key=''):
        try:
            extra_params = self.get_extra_params(dataset)
            operation = 'UPDATE'
            table = extra_params['table']
            update_statement = 'SET next_bill_fetch_date=%s,due_date=NULL,bill_fetch_date=NULL,customer_email=%s,customer_mobile=%s,bill_fetch_date=NULL,published_date=NULL,customerOtherInfo=NULL,retry_count=0,status=0,reason=NULL,extra=NULL WHERE recharge_number = %s and customer_id = %s'
            query = ' '.join([operation,table,update_statement])
            values = (extra_params.get('next_bill_fetch_date'),record.get('cust_email'),record.get('cust_mobile'),record.get('recharge_number_1',''),record.get('customer_id',''))
            
            if self.dry_run == 1:
                print(f'{self.getCurrentTime()} dry_run=1, skipping update_record_db for query:{query} and values:{values}')
            else:
                cursor = self.connection.cursor()
                cursor.execute(query,values)

            #execute
            self.connection.commit()
        except Exception as ex:
            print(f'{self.getCurrentTime()} CRITICAL! Exception occured while update_record_db operation for {trace_key}:{ex}')

    def insert_record_db(self,record,dataset,trace_key=''):
        try:
            # prepare param
            extra_params = self.get_extra_params(dataset)
            operation = 'INSERT INTO'
            table = extra_params['table']
            insert_statement = '(customer_id,recharge_number,product_id,operator,next_bill_fetch_date,service,paytype,customer_mobile,customer_email) VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s)'            
            query = ' '.join([operation,table,insert_statement])
            values = (record.get('customer_id',''),record.get('recharge_number_1',''),record.get('product_id',''),extra_params.get('operator',''),extra_params.get('next_bill_fetch_date',''), extra_params.get('service'), extra_params.get('paytype'),record.get('cust_mobile'),record.get('cust_email'))

            # prepare query
            if self.dry_run == 1:
                print(f'{self.getCurrentTime()} dry_run=1, skipping insert_record_db for query:{query} and values:{values}')
            else:
                cursor = self.connection.cursor()
                cursor.execute(query,values)

            # execute
            self.connection.commit()
            print(f'{self.getCurrentTime()} Record Inserted Successfully - {trace_key}')

        except mysql.connector.IntegrityError as ex:
            print(f'{self.getCurrentTime()} Entry already present, going to update entry for {trace_key}')
            self.update_record_db(record,dataset,trace_key)
        except Exception as ex:
            # this exception is because of duplicate entry...so in this case we will simply update its values           
            print(f'{self.getCurrentTime()} CRITICAL! Exception occured while insert_record_db for {trace_key}:{ex}')

    def get_progress_tracker_file_path(self):
        return "/var/www/digital-reminder/" + self.partition_date + ".csv"

    def get_csv_records(self,path):
        if not os.path.exists(path):
            return []

        records_df = pd.read_csv(path)
        records = records_df.to_json(orient="records")
        return json.loads(records)

    def get_status_offset_progress_csv(self,file_name):
        path = self.get_progress_tracker_file_path()
        records = self.get_csv_records(path)
        for record in records:
            if record["file_name"] == file_name:
                return (record["state"],record["records"])

        # no record found in file for this file_name
        return ("NEW_FILE",0)

    def update_progress_in_csv(self,dataset,file_name,progress_status='INITIATED',counter=0,invalid_records=0):
        print(f'{self.getCurrentTime()} PROGRESS_UPDATE: Updating file:{self.partition_date} for dataset:{dataset},file_name:{file_name},progress_status:{progress_status},counter:{counter}')
        
        path = self.get_progress_tracker_file_path()
        headers = ['dataset','file_name','state','records','invalid_records']

        if os.path.exists(path):
            # read file and store in dictionary
            records = self.get_csv_records(path)
            records_to_insert = []
            file_exists = False
            for record in records:
                if record["file_name"] == file_name:
                    record["state"] = progress_status
                    record["records"] = counter
                    record["invalid_records"] = invalid_records
                    file_exists = True
                
                records_to_insert.append([ record["dataset"],record["file_name"],record["state"],record["records"],record["invalid_records"] ])
            
            if not file_exists:
                records_to_insert.append([dataset,file_name,progress_status,counter,invalid_records])
        else:
            records_to_insert = [[dataset,file_name,progress_status,counter,invalid_records]]

        record_df = pd.DataFrame(records_to_insert,columns=headers)
        record_df.to_csv(path)

        return
        
    def get_dataFrame_from_parquet(self, s3_file_location):
        try:
            df = pd.read_parquet(s3_file_location, engine='auto', columns=["customer_id", "recharge_number_1", "product_id", "cust_mobile", "cust_email"])
            return df
        except Exception as ex:
            print(f'{self.getCurrentTime()} CRITICAL! Error in s3_file_location:{s3_file_location} with Error Msg:{ex}')
            return pd.read_parquet(s3_file_location)

            
    def process_file(self,directory,file_name,dataset):
        status,offset = self.get_status_offset_progress_csv(file_name)
        if status == "PROCESSED":
            print(f'{self.getCurrentTime()} file already processsed:{file_name} skipping it...')
            return
        else:
            print(f'{self.getCurrentTime()} Processing file:{file_name} from status:{status} offset:{offset}')

        try:
            s3_file_location = '/'.join([directory,file_name])
            df = self.get_dataFrame_from_parquet(s3_file_location)
            row_json_array = df.to_json(orient="records")
            print(f'{self.getCurrentTime()} Parsing JSON response got from dataframe')
            json_array = json.loads(row_json_array)
            print(f'{self.getCurrentTime()} Parsing done, now iterating and processing records from dataframe for dataser file:{file_name}')
            counter = 0
            invalid_records = 0

            # update file status as started only if its starting for the first time, else do not override status
            if offset == 0:
                self.update_progress_in_csv(dataset,file_name,'STARTED',counter)
            
            for record in json_array:
                counter+=1

                if counter > offset:
                    valid,error = self.check_if_valid_record(record)
                    if valid:
                        trace_key = '_'.join(['customerId:RN:pID',str(record.get('customer_id','')),record.get('recharge_number_1',''),str(record.get('product_id',''))])
                        self.insert_record_db(record,dataset,trace_key)
                    else:
                        invalid_records+=1
                        print(f'{self.getCurrentTime()} ERR! record validation error:{error} for {record}')

                # keeping trace for batch of 1000 records interval
                if counter % 1000 == 0:
                    print(f'{self.getCurrentTime()} PROGRESS: for dataset:{dataset} and file:{file_name} - {counter} , updating it in File')
                    if counter > offset:
                        self.update_progress_in_csv(dataset,file_name,'PROCESSING',counter,invalid_records)

                    if self.grey_scale == 1 and counter >= self.grey_scale_batch:
                        print(f'{self.getCurrentTime()} Grey scale is ON, Processing for next batch')
                        break

                    time.sleep(2)

            self.update_progress_in_csv(dataset,file_name,'PROCESSED',counter,invalid_records)
            print(f'{self.getCurrentTime()} Processing completed for dataset:{dataset} and file:{file_name}. Processed Records:{counter}')
            del df
            del row_json_array
            del json_array
        except Exception as ex:
            print(f'{self.getCurrentTime()} CRITICAL! Error in process_file for dataset:{dataset} and file:{file_name}:{ex}')
            return

    def load_data(self,dataset):
        directory = self.get_parquet_file_dir(dataset)
        files = self.get_filelist_from_s3(directory)

        if not files:
            return
        else:
            print(f'{self.getCurrentTime()} File List: {files}')

        for file_location in files:
            file_name = file_location.split('/')[-1]
            print(f'{self.getCurrentTime()} Start Processing dataset:{dataset} and file:{file_name}')
            self.process_file(directory,file_name,dataset)
            print(f'{self.getCurrentTime()} Finished Processing dataset:{dataset} and file:{file_name}')
            time.sleep(10)

    def create_connection(self,sql_config=None):
        if not sql_config:
            sql_config = self.config["sql"]

        try:
            connection = mysql.connector.connect(host=sql_config['host'],user=sql_config['user'],passwd=sql_config['password'],database=sql_config['database'])
            return connection
        except Exception as ex:
            print(f'{self.getCurrentTime()} CRITICAL! exception occured while create connection : {ex}')
            return None

    def load(self):
        for dataset in self.datasets:
            print(f'{self.getCurrentTime()} Going to process dataset:{dataset}')
            self.load_data(dataset)


if __name__ == "__main__":
    loader = Loader()
    loader.load()

"""
Flow: (Task: https://jira.mypaytm.com/browse/IN-16826)
1. API call k though -> path -> and then list all the files in it
2. Iterate one by one and put state in some csv file locally with its offset->  with states = STARTED, COMPLETED
3. DB entry or update on master machine….sql ka code add
4. Logging for each file and its offset….
5. File wise status -> 
6. <optional> mail if possible

sudo python3.7 <file> <env> <bill fetch date> <dry_run> <grey scale> <grey scale batch size>
sudo python3.7 Jan7.py staging 10 1 1 5000
"""