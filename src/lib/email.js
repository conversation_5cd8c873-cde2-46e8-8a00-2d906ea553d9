/*jshint multistr: true ,node: true*/
"use strict";

var 
    REQUEST        = require('request'),
    L              = require('lgr');

var sendMail = (config, body, attachments, cb) => {
    function _getOptions(config) {
        return {
            uri     : config.URI, 
            method  : "POST",
            headers : {
                "api_key": config.API_KEY,
                "content-type": "application/json"
            },
        }
    };

    function _getMail(config, body, attachments) {
        return {
            from                    : {email: config.FROM},
            subject                 : config.SUBJECT,
            content                 : [{type: 'html', value: body}],
            personalizations        : [{to: config.TO}],
            replyTo                 : config.REPLYTO,
            attachments             : attachments
        };
    }

    /* validate config */
    if(!(config &&
        config.API_KEY &&
        config.TO))
            if(cb && typeof(cb) == 'function')  return cb("Incorrect config");
            else                                return;

    var
        options  = _getOptions(config),
        mail    = _getMail(config, body, attachments);

    options.body = JSON.stringify(mail);

    REQUEST(options, function(err, resp, body){
        if(!err){
            L.log('Email Sent to : '  + JSON.stringify(config.TO)    +
                '\nMail Subject :'       + mail.subject     +
                '\nResponse : '          + JSON.stringify(resp));
        }

        if(cb && typeof(cb) == 'function')  
            return cb(err, resp);
        else                                
            return;  
    })
}

export default {
    sendMail 
};

//----------------------- TEST -------------------------//

(function () {
    if (require.main === module) {
        var details = {
                API_KEY         : 'b88ec3675d0fc9b8510038e7ddbdef40',
                SMTPTRANSPORT           : {
                    host        : 'internal-marketplace-postfix-internal-456529558.ap-southeast-1.elb.amazonaws.com',
                    port        : 587
                },
                URI             : 'https://gptmtrans.pepipost.com/v5/mail/send',
                TO              : [{email: '<EMAIL>'},{email: '<EMAIL>'}],
                FROM            : '<EMAIL>',
                SUBJECT         : 'reminder test mail',
                HEADERS         : {
                    'X-PAYTM-ENV'  : 'DEV'
                },
                REPLYTO         : '<EMAIL>'
            };
        
        sendMail(details, "This is a Test mail", [], function (err, res) {
            if (err) {
                console.log('error::', err);
            }
            process.exit();
        });
    }
}());


