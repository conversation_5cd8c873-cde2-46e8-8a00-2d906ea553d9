const _getCustomChannelId = function(channelId) {

    if(channelId === null) {
        return 'NA'
    }
    
    if(/android/i.test(channelId)) {
        channelId = "ANDROIDAPP";
    } else if(/ios/i.test(channelId)) {
        channelId = "IOSAPP";
    } else if(/web/i.test(channelId) || /html/i.test(channelId)) {
        channelId = "WEB";
    } else if(/windows/i.test(channelId)) {
        channelId = "WINDOWSAPP";
    } else if(/mall/i.test(channelId)) {
        channelId = "MALL-APP";
    } else if(/ebps/i.test(channelId)) {
        channelId = "DIGITAL-EBPS";
    } else if(/bbps/i.test(channelId)) {
        channelId = "BBPS";
    } else if(/reminder/i.test(channelId)) {
        channelId = "DIGITAL-REMINDER";
    } else if(/SUBS/i.test(channelId)) {
        channelId = "SUBS";
    } else if(/UMP-PAYOUTS/i.test(channelId)) {
        channelId = "UMP-PAYOUTS";
    }
    return channelId;
}

export default {
    _getCustomChannelId : _getCustomChannelId
}