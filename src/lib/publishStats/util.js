import { sendMonitorMetrics } from 'paytm-statsd';
import L from 'lgr'
import { promisify } from 'util';

class Helper {
    /**
     * 
     * @param {*} type metrics type, increment for counter and histogram for latency
     * @param {*} metrics 
     */
    static async sendMetrics(type, metrics) {
        try {
            await promisify(sendMonitorMetrics)(metrics, { metricType: type });
            L.verbose('metrics sent Successfully...');
        } catch (error) {
            L.error('Error while sending metrics..', error);
        }
    }

    static createTagFromJSON(statsObj) {
        let metricTags = []

        if (statsObj) {
            for (let key in statsObj) {
                metricTags.push(key + ':' + statsObj[key]);
            }
        }

        return metricTags;
    }

    static validator(value, statsObj) {

        if (typeof value != "number") {
            throw new Error("requestStartTime should be a number!");
        }

        if (statsObj && statsObj.constructor != ({}).constructor) {
            throw new Error("statsObj is a JSON Object!");
        }

        if (!statsObj.REQUEST_TYPE) {
            throw new Error("there is no REQUEST_TYPE in statsObj!");
        }
    }
}

export default Helper;