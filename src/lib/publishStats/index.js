import Util from './util';

class PublishStats {
    static publishCounter(value, statsObj) {
        Util.validator(value, statsObj);

        let metrics = [
            {
                name: 'DigitalBillReminder.Metrics',
                value: value,
                tags: Util.createTagFromJSON(statsObj)
            }
        ];

        Util.sendMetrics('increment', metrics);
    }

    static publishLatency(requestStartTime, statsObj) {
        Util.validator(requestStartTime, statsObj);

        let metrics = [
            {
                value: new Date().getTime() - requestStartTime,
                name: 'DigitalBillReminder.Latency',
                tags: Util.createTagFromJSON(statsObj)
            }
        ];

        Util.sendMetrics('histogram', metrics);
    }
}

export default PublishStats;