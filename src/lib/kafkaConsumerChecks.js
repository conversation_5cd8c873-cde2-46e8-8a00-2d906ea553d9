"use strict";

import _ from 'lodash';
import utility from '../lib';


class KafkaConsumerChecks {
    constructor(options) {
        this.L = options.L;
        this.partitionLastOffsetTracker = {};
    }

    /**
     * Method to find all offsets which which have been consumed mulitple times (hence, duplicates)
     * @param {*} serviceName
     * @param {*} records
     * @param {*} topic | this param is passed only in case of new kafkajs consumer, else topic is retrieved from the record payload
     * @param {*} partition | this param is passed only in case of new kafkajs consumer, else partition is retrieved from the record payload
     */
    findOffsetDuplicates(serviceName, records, topic, partition) {
        let self = this;
        for (let i = 0; i < records.length; i++) {
            let currentOffset = Number(_.get(records[i], 'offset', -1));
            let currentTopic = topic != null ? topic : _.get(records[i], 'topic', null);
            let currentPartition = partition != null ? partition : _.get(records[i], 'partition', null);
            _.set(records[i],'topic',currentTopic)
            _.set(records[i],'partition',currentPartition)

            let maxObservedOffset = (_.get(self.partitionLastOffsetTracker , [currentTopic , currentPartition] , 0))
            // self.L.log("findKafkaConsumerDuplicates:",`${currentTopic}_${currentPartition}_${currentOffset}`)
            if (currentOffset != -1 && Number(currentOffset) < Number(maxObservedOffset)) {
                self.L.log(`findOffsetDuplicates :: Duplicate message received for ${currentTopic} partition:${currentPartition} and offset:${currentOffset}`);
                utility._sendMetricsToDD(1, [
                    'STATUS:DUPLICATED_OFFSET',
                    `TOPIC:${currentTopic}`,
                    `PARTITION:${currentPartition}`,
                    `REQUEST_TYPE:${serviceName}`
                ]);
            }

            if(maxObservedOffset < Number(currentOffset) && currentOffset!=-1){
                _.setWith(self.partitionLastOffsetTracker ,[currentTopic , currentPartition] , Number(currentOffset ),Object)
            }
            
        }
        self.L.log("findKafkaConsumerDuplicates :: partitionLastOffsetTracker : ", JSON.stringify(self.partitionLastOffsetTracker));
    }
}


export default KafkaConsumerChecks;