"use strict";

import _ from 'lodash' 

import VIL from "./vodafone_idea_limited"

class OperatorValidation {
    constructor({ options, group, type }) {
        this.group = group;
        this.type = type;
        this.config = options.config;
        this.VIL = new VIL(options)
    }

    async validate(record) {

        if (_.get(record, 'notification_status', null) != _.get(this.config, 'COMMON.notification_status.ENABLED', 1)) {
            return [false, "Notification is disabled for this customer"];
        }

        if (this.group.toUpperCase() == "VIL") {
            return this.VIL.validate(record);
        } else {
            return [true,null];
        }
    }
}

export default OperatorValidation;