'use strict'

import PublishStats from '../publishStats'
import L from 'lgr'
import MOMENT from 'moment'
import { get } from 'lodash'

class VIL {
    constructor(options) {
        this.dbInstance = options.dbInstance;
        this.config = options.config;

        this.mysqlSyncTable = "vil";

        this.dbSyncCompletionTime = MOMENT().startOf('day').format("YYYY-MM-DD HH:mm:ss");
        this.maxValidityInDays = 120;  // need to validate with VIL sync db, then we need to update
    }

    async validate(record) {

        let { id, latest_recharge_date: latestRechargeDate, dayValue, recharge_number: rechargeNumber, amount, validity_expiry_date: expiryDate, status } = record;

        if (this.shouldBeSyncWithDb(dayValue, latestRechargeDate, expiryDate, status)) {
            if (get(this.config, ['DYNAMIC_CONFIG', 'PLAN_VALIDTY_NOTIFICATION_CONSUMER', 'VALIDATION_WITH_VIL_SYNC_DB', 'ENABLE'], 1)) {
                /**
                 * we will live with following 2 lines
                 */

                this.syncWithDB(id, rechargeNumber, expiryDate, amount, status)
                return [true,null];

                //return await this.syncWithDB(id, rechargeNumber, expiryDate, amount, status);
            } else {
                return [true,null];
            }
        } else {
            return [true,null];
        }
    }

    /**
     * 
     * @param {*} dayValue should be integer
     * @param {*} latestRechargeDate should be a date
     * @param {*} expiryDate 
     * @param {*} status paid on paytm or other platform
     */
    shouldBeSyncWithDb(dayValue, latestRechargeDate, expiryDate, status) {

        // sunday check, there is no validation on sunday.
        if (MOMENT().weekday() == 0) {
            return false;
        }

        let validityInDays = MOMENT(expiryDate).startOf('day').diff(MOMENT(latestRechargeDate).startOf('day'), 'days');

        if ((validityInDays + dayValue) > this.maxValidityInDays) {
            return false;
        } else if (validityInDays > 2) {
            return true;
        }
        else {
            try {

                if (status == this.config.COMMON.bills_status.PAYMENT_DONE) {
                    if (validityInDays == 1 && dayValue < 2) {
                        return false;
                    } else if (validityInDays == 2 && dayValue < 1) {
                        return false;
                    }
                }
            } catch (error) {
                L.error("[src/lib/operatorValidation/vodafone_idea_limited.js] shouldBeSyncWithDb:: error in json parsing, ", error);
            }

            return true;
        }
    }

    /**
     * check existancy in VIl temp table, if not available then update it's in VIL_MAIN table. 
     * @param {*} id 
     * @param {*} rechargeNumber 
     * @param {*} expiryDate 
     * @param {*} amount 
     */
    async syncWithDB(id, rechargeNumber, expiryDate, amount, prevStatus) {
        try {
            expiryDate = MOMENT(expiryDate).format('YYYY-MM-DD');

            let query = `select * from ${"vil_temp_" + MOMENT().format("YYYY_MM_DD")} where recharge_number = ? and amount = ?`;
            let params = [+rechargeNumber, amount];

            L.log("syncWithDB, query", query, " and params: ", params);

            let res = await this.runQuery('OPERATOR_SYNC_SLAVE', query, params);    //  need to be change

            L.log("syncWithDB, data from VIL Temp table ", res)

            if (res.length == 0) {
                let status = this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM;

                PublishStats.publishCounter(1, {
                    REQUEST_TYPE: "OPERATOR_VALIDATION_FOR_PV_NOTIFICATION",
                    status: "PAID_ON_OTHER_PLATFORM",
                    OPERATOR: "VIL"
                });

                // if (prevStatus != status) {
                //     this.updatePlanValidity(id, status);
                // }

                this.updateVilSyncTable(rechargeNumber, expiryDate, amount, status);

                return false;
            } else {
                for (let record of res) {
                    let diff = MOMENT(record.expiry_date).diff(expiryDate);

                    if (diff > 0) {
                        L.log("syncWithDB, wrong expiry from VI, record: ", record);
                        PublishStats.publishCounter(1, {
                            REQUEST_TYPE: "OPERATOR_VALIDATION_FOR_PV_NOTIFICATION",
                            status: "WRONG_EXPIRY_IN_THE_TEMP_TABLE",
                            OPERATOR: "VIL"
                        });
                    }
                }

                return true;
            }

        } catch (error) {
            L.error("[src/lib/operatorValidation/VIL] error in syncWithDB: ", error);

            this.publishStats({
                type: "SYNC_DB_QUERY_FAILED"
            });

            /**
             * need to handle logic when we need to return true and when we need false in this catch block. need to discuss with product.
             */
            return true;
        }
    }

    async updateVilSyncTable(rechargeNumber, expiryDate, amount, status) {
        try {

            let query = `update ${this.mysqlSyncTable} set final_status=? where recharge_number = ? and expiry_date = ? and amount = ?;`,
                params = [status, +rechargeNumber, expiryDate, amount];

            await this.runQuery('OPERATOR_SYNC', query, params);
        } catch (error) {
            L.error("[src/lib/operatorValidation/VIL] error in updateVilSyncTable: ", error);

            this.publishStats({
                type: "UPDATE_VIL_SYNC_TABLE_QUERY_FAILED"
            });
        }
    }

    async updatePlanValidity(id, status) {
        try {

            let query = "update plan_validity set status=? where id=?",
                params = [status, id];

            await this.runQuery('RECHARGE_ANALYTICS', query, params);
        } catch (error) {
            L.error("[src/lib/operatorValidation/VIL] error in updatePlanValidity: ", error);
            this.publishStats({
                type: "UPDATE_PLAN_VALIDITY_QUERY_FAILED"
            });
        }
    }

    runQuery(poolName, query, params) {
        return new Promise((resolve, reject) => {
            this.dbInstance.exec((error, results) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(results);
                }
            }, poolName, query, params);
        });
    }

    publishStats({ type }) {
        PublishStats.publishCounter(1, {
            REQUEST_TYPE: "OPERATOR_VALIDATION_FOR_PV_NOTIFICATION",
            STATE: "ERROR",
            TYPE: type,
            OPERATOR: "VIL"
        });
    }
}

export default VIL