/**
 * Module is used to fetch messages localised messages 
 * 
 * if language message present in the system then return message
 * 
 * if fall back language english messages present in the system then return message
 * else return empty message 
 * 
 * IN FFR in case of empty we pass the merchant message directly into the payload without any manipulation
 * 
 * @param {*} opts contains information about localisation client
 */
'use strict';

 var 
    //Third party library
    _               = require('lodash'),
    L               = require('lgr');


var Localisation    = {

    /**
    * 
    * @param {*} keyComponents  contains all key components to get a message
    * if message not find for specified local then check message for the english language
    * if english message is also not there then send the same message comming from the merchant
    *  */

    getLocalisedMessage:function(localisationMessages, keyComponents, flag, payload){
       return localisationMessages.getMessage(function(error, textMessage){
            if(error||!textMessage){
                return Localisation.getDefaultEnglishMessage(localisationMessages, keyComponents, flag, payload);
            }else{
                return Localisation.applyTemplate(textMessage, payload); 
            }
        },keyComponents,flag);
    },
    
    /**
    *  
    * @param {*} keyComponents  replace message local to english to get fallback message (english) 
    */

   getDefaultEnglishMessage:function(localisationMessages, keyComponents, flag, payload){
        keyComponents[0]    =   'en-IN';
        return localisationMessages.getMessage(function(error, textMessage){
            if(error||!textMessage){
                return '';
            }else{
                return Localisation.applyTemplate(textMessage, payload); 
            }
        },keyComponents,flag);
    },

    /**
     * @param {*} messageData contains information about message
     * @param {*} payload contains variables to be templated
     */
    applyTemplate:function(messageData, payload){
        try{
            var compiled = _.template(messageData);
        //lets replace the variable parts of message (if any) 
            var textMessage = compiled(payload);
            
            return textMessage;
        }catch (ex) {
        //This exception will occur, when the passed payload doesn't contain the attributes required by template, If the template doesn't have any variable part in it, then value of payload doesn't matter (even if its null, undefined or blank object)
            L.error('validate.controller.validate', ex);
            return '';                   
        }
    }
}

module.exports  =   Localisation;

(function() {
    
    var keyComponents = [
        'DIGITALRECHARGE',
        2,
        'en',
        'VALIDATION',
        76,
        0
    ],

    payload        = {       
        'paytype_label'     :'Airtel',
        'operator_label'    :'Airtel',
        'recharge_number'   :1234567890
    };

    if (require.main == module) {
        Localisation.getLocalisedMessage(console.log);
    }
  }());

