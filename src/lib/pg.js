"use strict";

import _ from 'lodash'
import CRYPTO from 'crypto'
import request from 'request'
import <PERSON>Y<PERSON> from 'async'
import Helper from '../lib/helper'
import L from 'lgr'
import REQUEST from 'request'
import JWT from 'jsonwebtoken'
import utility from '../lib';
import EncryptionDecryptioinHelper from './EncryptionDecryptioinHelper';

class PG {

    constructor(options) {
        this.L = options.L,
        this.config = options.config
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
    }
    
    getCardIndexNumber(cb, cardId) {
        let self = this;
        let cardType = 'CC_BILL_ID';
        let tokenType = 'SHA256';
        let cardIndexNumber = null;
        let signature = CRYPTO.createHash('sha256')
            .update(`${cardId}${tokenType}${cardType}${_.get(self.config, 'COMMON.PG_CARD_INDEX_API_SECRET_KEY', '')}`, 'utf8')
            .digest('hex'); // TODO move to config
        let requestInfo = {
            uri: _.get(self.config, 'COMMON.PG_CARD_INDEX_API_URI', ''),
            method: 'POST',
            json: {
                "head": {
                    "tokenType": tokenType,
                    "signature": signature
                },
                "body": {
                    "cardId": cardId,
                    "cardIdType": cardType
                }
            }
        };
        self.L.verbose(`pgLib::getCardIndexNumber`,`Request for ${cardId}`,JSON.stringify(requestInfo));

        request(requestInfo, function (err, res, body) {
            if (err) {
                self.L.error(`Error: ${err} for request: ${requestInfo}`);
            } else if (body) {
                try {
                    if (body.body.resultInfo.resultMsg === 'Success' && body.body.cardId === cardId) {
                        cardIndexNumber = body.body.cardIndexNumber;
                    }
                } catch (e) {
                    self.L.error(`Error: ${e} for response: ${JSON.stringify(body, null, 4)}`);
                }
            }
            if(err || !cardIndexNumber) {
                self.L.error(`pgLib::getCardIndexNumber`,`Response for ${cardId}: error-${err}, response - ${JSON.stringify(body)}`);
            }
            return cb(err, cardIndexNumber);
        });
    }

    /**
     * 
     * @param {*} requestData 
     * @returns 
     */
    getJwtToken(requestData) {
        let self = this,
            jwt_secret = _.get(self.config, 'COMMON.PG_SAVED_CARDS_BY_USER_ID_API_JWT_TOKEN', ''),
            claims = { "userId": requestData.custId , "tokenType" :"JWT", "iss":"ts" },
            signOptions = {
                algorithm: 'HS256',
                noTimestamp:  true,
                expiresIn: 2 * 60 * 60
            },
            token = JWT.sign(claims, jwt_secret, signOptions);
        return token;
    }
    
    /**
     * 
     * @param {*} cb 
     * @param {*} requestData 
     * @param {*} processedRecord 
     * 
     * wiki doc. link: https://wiki.mypaytm.com/display/PGP/Saved+Card+new+API+for+Phone+Update+Task
     */
    getCreditCardDataFromPG(cb , requestData, processedRecord){ 
        let self = this,
            url = _.get(self.config, 'COMMON.PG_SAVED_CARDS_BY_USER_ID_API_URI', ''),
            apiOpts = {
                url: url, 
                method: 'POST',
                json: {
                    "head": {
                        "tokenType": 'JWT',
                        "token": self.getJwtToken(requestData),
                        "clientId": "CC_Bill_Payment",
                    },
                    "body": {
                        "userId": requestData.custId,
                        "isCardIndexNumberRequired": true
                    }
                },
                reHitTimedOut: false,
                timeout: _.get(self.config, 'COMMON.PG_SAVED_CARDS_BY_USER_ID_API_TIMEOUT', '')
                
            };
        let latencyStart = new Date().getTime();    
        self.L.log('PG_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromPG ApiOpts', `${JSON.stringify(apiOpts)}_having_debug_key ${processedRecord.debugKey}`);
        REQUEST(apiOpts, (error, response, body) =>{
            let latencyTime = new Date().getTime() - latencyStart;
            utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:PG_SAVED_CARDS_BY_USER_ID_API', `URL:${url}`]);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:PG_SAVED_CARDS_BY_USER_ID_API', 
                `URL:${url}`,
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
            ]);
            if (!error && body) {
                let res = body;
                this.L.log('PG_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromPG Response', `${JSON.stringify(body)}_having_debug_key ${processedRecord.debugKey}`);
                return cb(null, res);
            } else {
                this.L.error('PG_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromPG Error', `${error}_having_ApiOpts${JSON.stringify(apiOpts)}_having_debug_key ${processedRecord.debugKey}`);
                return cb(`PG_SAVED_CARDS_BY_USER_ID_API Error Msg: ${error}`);
            }
        });
    }

    getCreditCardDataFromSaga(cb, customerId){ 
        let self = this, url;
        let sagaVersion = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'SMS_PARSING'], 0);
        let sagaPriority = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'ENABLE_NEW_FORMAT_MCN', 'COFT_ENABLED'], 0);
        
        if(sagaVersion == 0){
            url = _.get(self.config, 'COMMON.SAGA_SAVED_CARDS_BY_USER_ID_API_URI', '') + '/customer/' + customerId + '/recents'
        } else {
            url = _.get(self.config, 'COMMON.SAGA_SAVED_CARDS_BY_USER_ID_API_URI', '') + '/v2/customer/' + customerId + '/recents'
        }

        if(sagaPriority == 1){
            url += "?isCoft=true"
        }

        let apiOpts = {
                url: url, 
                method: 'POST',
                headers: {
                    "content-type": "application/json"
                },
                body: '{"type":"savedCard","product.cardType":"CC","product.service":"credit card"}'           
            };
        let latencyStart = new Date().getTime();    
        self.L.log('SAGA_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromSAGA ApiOpts', `${JSON.stringify(apiOpts)}_having_customer_id ${customerId}`);
        REQUEST(apiOpts, (error, response, body) =>{
            let latencyTime = new Date().getTime() - latencyStart;
            utility._sendMetricsToDD(latencyTime, ['REQUEST_TYPE:SAGA_SAVED_CARDS_BY_USER_ID_API', `URL:${url}`]);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SAGA_SAVED_CARDS_BY_USER_ID_API', 
                `URL:${url}`,
                'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
            ]);
            if (!error && body) {
                let res = body;
                this.L.log('SAGA_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromSaga Response', `${this.encryptionDecryptionHelper.encryptData(JSON.stringify(body))}`, `having_customer_id ${customerId}`);
                return cb(null, res);
            } else {
                this.L.error('SAGA_SAVED_CARDS_BY_USER_ID_API::getCreditCardDataFromSaga Error', `${error}_having_ApiOpts${JSON.stringify(apiOpts)}having_customer_id ${customerId}`);
                return cb(`SAGA_SAVED_CARDS_BY_USER_ID_API Error Msg: ${error}`);
            }
        });
    }
}

export default PG;

(function () {
    if (require.main === module) {
        var commander = require('commander');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-i, --id <value>', 'id', Helper.num_list)
            .parse(process.argv);

        if(commander.verbose){
            L.setLevel('verbose');
        }
        let pgLib = new PG({
            L : L,
            //config : config // This lib or call via command line wont be required, If required plz use startup.init
        });

        console.time('Execution Time');
        ASYNC.eachLimit(commander.id,2,function(cardId, callback){
            pgLib.getCardIndexNumber(function(error,cardIndexNumber){
                console.log("Response -",cardId,cardIndexNumber,error);
                return callback();
            },cardId);
        },function(err){
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=production node dist/lib/pg.js --id 776655,34563