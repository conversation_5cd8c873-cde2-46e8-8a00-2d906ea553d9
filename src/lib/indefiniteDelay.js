import REQUEST from 'request'
import <PERSON>YNC from 'async'
import _ from 'lodash'

let L;
function pauseIndefinitely(options, done) {
    L = options.L;
    let gateway = _.get(options, 'gateway');
    let prefetchStrategy = _.get(options, 'prefetchStrategy',null); 
    let traceKey = `Gw:${gateway}_Q:${options.queueName}_` + _.get(options, 'traceKey', '');
    let prefetchPercent = _.get(options,'prefetchPercent',50);
    let productId = options.productId;
    let byPassOperatorList = _.get(options.config,['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'BILL_FETCH_CONFIG', 'BYPASS_LATENCY_CHECK_FOR_OPERATOR'],['airtel'])
    let allNewMethodPidList = _.get(options.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'BIL<PERSON>_FETCH_CONFIG', 'ALLOW_PRODUCT_ID'],null);

    if (allNewMethodPidList && allNewMethodPidList.includes(productId)) {
        // productId is present in allNewMethodPidList
        return done(null,1/100);
    }

    for (let i = 1; i <= 9; i++) {
        let productIdList = _.get(options.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', 'BILL_FETCH_CONFIG', `ALLOW_PRODUCT_ID${i}`],null);
        if (productIdList && productIdList.includes(productId)) {
            // productId is present in productIds
            return done(null, 1 / 100);
        }
    }

    if (_.isArray(byPassOperatorList) && byPassOperatorList.includes(options.operator)) {
        return done(null, 1 / 100);
    } else
        return done(null, 1 / 100);
    if (!gateway) {
        L.error('pauseIndefinitely', `Gateway not found for ${_.get(options, 'traceKey')}`)
        return done(new Error('Gateway not found'), 100);
    }

    ASYNC.waterfall([
        //get current ingress rate
        next => {
            L.verbose('pauseIndefinitely::_getQueueDetails', 'Getting Q Details for ', options.queueName);
            _getQueueDetails(options.queueName, options.config, _.get(options, 'traceKey'), next)
        },

        //get current latency metric value from datadog
        (queueDetails, next) => {
            L.verbose('pauseIndefinitely::getLatency', 'Getting latency for gateway', options.gateway);
            let latency = options.latencyProvider.getLatency(options.gateway)
            next(null, queueDetails, latency / 1000) //convert latency in seconds
        }
    ],
        function (err, queueDetails, latency) {
            if (err) {
                //Returning in case of error. Logs added in the callback method.
                L.error('pauseIndefinitely', `Error in getting queue details or gateway latency for ${traceKey} and Q:${_.get(queueDetails, 'queueName')}`, err)
                return done(err, 100);
            }

            let rates = _.get(queueDetails, 'rates', {}),
                consumerCount = _.get(queueDetails, 'consumerCount', 1),
                prefetchCountPerConsumer = _.get(queueDetails, 'prefetchCountPerConsumer', 30),
                totalPrefetchCountForGateway = _.get(queueDetails, 'totalPrefetchCountForGateway', 20);

            if (_.get(rates, 'ingress', 0) < 0 || _.get(rates, 'egress', 0) < 0 || !latency) {
                //resort to basic Pause Mechanism
                L.log('pauseIndefinitely', 'Using basic pause mechanism', 'GATEWAY: ', gateway, ' INGRESS', rates.ingress, 'EGRESS', rates.egress, 'LATENCY', latency, `for ${traceKey}`);
                return basicPauseMechanism(options, traceKey, done)
            }

            /* For now not multiplying the consumerCount with output rate, Since it created issues on queue side */
            // let maxPossibleOutputRate = consumerCount * (prefetchCountPerConsumer/parseFloat(latency))
            let gatewayPrefetchForReminder;
            if(prefetchStrategy == "GW_TOTAL_PREFETCH_PERCENT") {
                gatewayPrefetchForReminder = prefetchPercent*totalPrefetchCountForGateway/100;
                L.log('pauseIndefinitely',`Using ${prefetchStrategy} strategy for ${traceKey} with gatewayPrefetchForReminder:${gatewayPrefetchForReminder}`);
            } else {
                gatewayPrefetchForReminder = prefetchCountPerConsumer;
            }

            let maxPossibleOutputRate =0;
            // if (['rajasthandiscom', 'jvvnlvcid' , 'bharatbillpay' , 'wbsedcl' , 'tsspdcl' ].includes(gateway)) {
                maxPossibleOutputRate = consumerCount  * (prefetchCountPerConsumer / parseFloat(latency));
            // } else {
            //     maxPossibleOutputRate = gatewayPrefetchForReminder / parseFloat(latency);
            // }

            L.verbose('pauseIndefinitely', 'maxPossibleOutputRate', maxPossibleOutputRate);

            // L.log(`For gateway ${options.gateway}: Current Ingress: ${rates.ingress}, Egress: ${rates.egress}, maxPossibleOutputRate: ${maxPossibleOutputRate}`);
            if (rates.ingress > 0.95 * maxPossibleOutputRate) {
                let pauseTime =Math.ceil(1000 / maxPossibleOutputRate);
                L.log(`Pausing since ingress rate is > 95% of maxPossibleOutputRate of gateway ${options.gateway}: Current Ingress: ${rates.ingress}, Egress: ${rates.egress}, maxPossibleOutputRate: ${maxPossibleOutputRate}, pausing for:${pauseTime} mS`);
                //Lets check after this much time
                setTimeout(function () {
                    pauseIndefinitely(options, done)
                }, pauseTime)
            }
            else {
                //Lets push the message to queue, and convey the waiting time after that push
                done(null, Math.ceil(1000 / maxPossibleOutputRate))
            }
        })
}

function basicPauseMechanism(options, traceKey = '', done) {
    try {
        options.rmqPublisher.getQcount(function (count) {
            //Continue if current queue count < MAX_QUEUE_COUNT
            if (count < options.maxQueueCount) {
                return done(null, 1);
            }
            else {
                options.L.log(`Pausing via basicPauseMechanism for ${traceKey}`)
                setTimeout(() => {
                    pauseIndefinitely(options, done)
                }, 10000)
            }
        }, options.queueName);
    }
    catch (ex) {
        setTimeout(() => {
            pauseIndefinitely(options, done)
        }, 10000)
    }
}

function _getQueueDetails(queueName, config, traceKey = '', done) {
    REQUEST({
        url: `${config.RABBITMQ_CONFIG.URL}/api/queues/%2F/${queueName}`,
        headers: {
            'Authorization': config.RABBITMQ_CONFIG.AUTHORIZATION
        }
    }, function (err, response, body) {
        if (err) {
            L.error('indefiniteDelay::_getQueueDetails', `Error in getting queue details for ${traceKey}`, err);
            return done(err, {
                queueName: queueName
            })
        }

        if (typeof body == 'string') {
            try {
                body = JSON.parse(body)
            }
            catch (ex) {
                L.error('indefiniteDelay::_getQueueDetails', 'Error while parsing queue details', body, ex);
                body = {}
            }
        }

        let maxPrefetchCount = _.isArray(_.get(body, 'consumer_details', '')) ? _.get(body, 'consumer_details').reduce((maxValue, queueConsumer) => (maxValue < queueConsumer.prefetch_count ? queueConsumer.prefetch_count : maxValue), 1) : 20;
        let totalPrefetchCount = 0;

        if(body && _.get(body, 'consumer_details', null) && _.isArray(_.get(body, 'consumer_details', ''))) {
            for(let consumer of body.consumer_details) {
                let prefetch = _.get(consumer,'prefetch_count',0);
                totalPrefetchCount+= prefetch;
            }
        }

        done(null, {
            queueName: queueName,
            rates: {
                ingress: _.get(body, 'backing_queue_status.avg_ingress_rate', -1),
                egress: _.get(body, 'backing_queue_status.avg_egress_rate', -1)
            },
            consumerCount: _.get(body, 'consumers', 1),
            prefetchCountPerConsumer: maxPrefetchCount,
            totalPrefetchCountForGateway : totalPrefetchCount
        })
    })
}

export default {
    pauseIndefinitely
}







/*
Sample queueDetails api Response
[ { arguments: {},
    channel_details:
     { peer_host: 'ip-10-4-36-55.ap-south-1.compute.internal',
       peer_port: 56546,
       connection_name: 'ip-10-4-36-55.ap-south-1.compute.internal:56546 -> txnsqueuerabbitmq-billpayments-v1-33-227.prod.ap-south-1.paytmdgt.io:5672',
       user: 'app',
       number: 1,
       node: 'rabbit@txnsqueuerabbitmq-billpayments-v1-33-227',
       name: 'ip-10-4-36-55.ap-south-1.compute.internal:56546 -> txnsqueuerabbitmq-billpayments-v1-33-227.prod.ap-south-1.paytmdgt.io:5672 (1)' },
    prefetch_count: 50,
    ack_required: true,
    exclusive: false,
    consumer_tag: 'amq.ctag-O4TpkZZLiF1l3JMid_LcIw',
    queue: { vhost: '/', name: 'q_gw_vodafonedirect' } },
  { arguments: {},
    channel_details:
     { peer_host: 'ip-10-4-36-209.ap-south-1.compute.internal',
       peer_port: 37116,
       connection_name: 'ip-10-4-36-209.ap-south-1.compute.internal:37116 -> txnsqueuerabbitmq-billpayments-v1-33-53.prod.ap-south-1.paytmdgt.io:5672',
       user: 'app',
       number: 1,
       node: 'rabbit@txnsqueuerabbitmq-billpayments-v1-33-53',
       name: 'ip-10-4-36-209.ap-south-1.compute.internal:37116 -> txnsqueuerabbitmq-billpayments-v1-33-53.prod.ap-south-1.paytmdgt.io:5672 (1)' },
    prefetch_count: 50,
    ack_required: true,
    exclusive: false,
    consumer_tag: 'amq.ctag-dub_3U6fRU46MzMYhEynMw',
    queue: { vhost: '/', name: 'q_gw_vodafonedirect' } },
  { arguments: {},
    channel_details:
     { peer_host: 'ip-10-4-35-180.ap-south-1.compute.internal',
       peer_port: 59068,
       connection_name: 'ip-10-4-35-180.ap-south-1.compute.internal:59068 -> txnsqueuerabbitmq-billpayments-v1-33-53.prod.ap-south-1.paytmdgt.io:5672',
       user: 'app',
       number: 1,
       node: 'rabbit@txnsqueuerabbitmq-billpayments-v1-33-53',
       name: 'ip-10-4-35-180.ap-south-1.compute.internal:59068 -> txnsqueuerabbitmq-billpayments-v1-33-53.prod.ap-south-1.paytmdgt.io:5672 (1)' },
    prefetch_count: 50,
    ack_required: true,
    exclusive: false,
    consumer_tag: 'amq.ctag-oCWV14yhs0qi_aV6EHtZTg',
    queue: { vhost: '/', name: 'q_gw_vodafonedirect' } },
  { arguments: {},
    channel_details:
     { peer_host: 'ip-10-4-35-234.ap-south-1.compute.internal',
       peer_port: 57324,
       connection_name: 'ip-10-4-35-234.ap-south-1.compute.internal:57324 -> txnsqueuerabbitmq-billpayments-v1-33-247.prod.ap-south-1.paytmdgt.io:5672',
       user: 'app',
       number: 1,
       node: 'rabbit@txnsqueuerabbitmq-billpayments-v1-33-247',
       name: 'ip-10-4-35-234.ap-south-1.compute.internal:57324 -> txnsqueuerabbitmq-billpayments-v1-33-247.prod.ap-south-1.paytmdgt.io:5672 (1)' },
    prefetch_count: 50,
    ack_required: true,
    exclusive: false,
    consumer_tag: 'amq.ctag-e1dHiUiNV_h2kbjmdEma1A',
    queue: { vhost: '/', name: 'q_gw_vodafonedirect' } } ]
*/