/*
jshint esversion: 8
*/

import STATSD from 'paytm-statsd';
import L from 'lgr';
import _ from 'lodash'
import MOMENT from 'moment'
import { error } from 'pinpoint-node-agent/lib/utils/logger';
let statusMap = {
   "0": "PENDING",
   "1": "SENT",
   "2": "CANCELED",
   "3": "ERROR",
   "4": "SUCCESS",
   "5": "FAILED",
   "6": "RETRY",
   "7": "RESCHEDULE",
   "8": "DISABLED",
   "9": "RETRY_FALLBACK",
   "10": "FAILED_FALLBACK"
 }

let _sendMetricsToDD = (value, tags,typeOfMetric='increment') => {
   var self = this,
      metrics = [];

   metrics = [
      {
         name: 'DigitalBillReminder.Metrics',
         value: value,
         tags: tags
      }
   ];

   L.verbose('_sendMetricsToDD: metricType,tags,value ::',typeOfMetric,tags,value);

   try {
      STATSD.sendMonitorMetrics(metrics, { metricType: typeOfMetric }, function (err) {
         if (err) {
            L.error('Error while sending metric data to DataDog..', err);
         }
         else {
            L.verbose('Metric data sent Successfully to DataDog...');
         }
      });
   }
   catch (exception) {
      L.error('Exception occurred while sending metric data to DataDog..', exception);
   }
};


let _sendLatencyToDD = (requestStartTime, tags) => {
   /*
     The below structure of HealthData is according to the documenation provided here:
        https://paytmlabswiki.atlassian.net/wiki/spaces/MP/pages/84084003/Service+HealthCheck
   */
   let metricTags = [];
   if (tags) {
      for (var key in tags) {
         metricTags.push(key + ':' + tags[key]);
      }
   }

   const latencyDiff = new Date().getTime() - requestStartTime;

   let metrics = [
      {
         value: latencyDiff ? latencyDiff : 1,
         name: 'DigitalBillReminder.Latency',
         tags: metricTags
      }
   ];

   try {
      //send latency data to DD
      STATSD.sendMonitorMetrics(metrics, { metricType: 'histogram' }, function (err) {
         if (err) {
            L.error('Error while sending latency data to DataDog..', err);
         }
         else {
            L.verbose('Latency sent Successfully to DataDog...');
         }
      });
   }
   catch (exception) {
      L.error('Exception occurred while sending latency data to DataDog..', exception);
   }
};

let _setSMSLatencyToDD = (requestedPeriod, tags) => {
   /*
     The below structure of HealthData is according to the documenation provided here:
        https://paytmlabswiki.atlassian.net/wiki/spaces/MP/pages/84084003/Service+HealthCheck
   */
   let metricTags = [];
   if (tags) {
      for (var key in tags) {
         metricTags.push(key + ':' + tags[key]);
      }
   }

   let metrics = [
      {
         value: requestedPeriod,
         name: 'DigitalBillReminder.Latency',
         tags: metricTags
      }
   ];

   try {
      //send latency data to DD
      STATSD.sendMonitorMetrics(metrics, { metricType: 'histogram' }, function (err) {
         if (err) {
            L.error('Error while sending latency data to DataDog..', err);
         }
         else {
            L.verbose('Latency sent Successfully to DataDog...');
         }
      });
   }
   catch (exception) {
      L.error('Exception occurred while sending latency data to DataDog..', exception);
   }
};

let sendNotificationMetricsFromSource = (payload ,status="INITIATED"  , type="COMMON" )=>{
   try{
      if(type == "COMMON"){
         if(_.get(payload,['data', 'billFetchReminder_onBoardTime',null])==null )_.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());

         _sendMetricsToDD(1, [
            'STATUS:'+status,
            'TYPE:'+_.get(payload,'notificationType','UNKNOWN'),
            'NAME:NOTIFICATION_INIT',
            'SERVICE:'+_.get(payload,'data.service','UNKNOWN')
      ])
   }else{
      if(_.get(payload,['billFetchReminder_onBoardTime',null]) == null )_.set(payload, [ 'billFetchReminder_onBoardTime'],new Date().getTime());
         _sendMetricsToDD(1, [
            'STATUS:'+status,
            'TYPE:'+_.get(payload,'notificationType','UNKNOWN'),
            'NAME:NOTIFICATION_INIT',
            'SERVICE:'+_.get(payload,'service','UNKNOWN'),
            'SOURCE_TYPE:'+type
      ])
   }
   }catch(err){
      L.error("Error while publishing notification metrics")
   }
}

let sendNotificationMetricsFromSend = (payload ,status="PUBLISHED",error = null)=>{
   status = _.get(statusMap,status,status)
   try{
      let operator = "UNKNOWN",service="UNKNOWN",templateId="UNKNOWN"
      if(_.get(payload, ['data','dynamicParams'], null)) {
          operator = _.toUpper(_.get(payload, ['data', 'dynamicParams', 'operator'], "UNKNOWN"));
          service = _.toUpper(_.get(payload, ['data', 'dynamicParams', 'service'], null))
        }
      else {  
         service = _.toUpper(_.get(payload, ['data', 'options', 'data', 'service'], "UNKNOWN"));
         operator = _.toUpper(_.get(payload, ['data', 'options', 'data', 'operator'], "UNKNOWN"));
      }
      templateId = _.get(payload,'template_id', "UNKNOWN")
      let tags = [
         'STATUS:'+status,
         'SEND_TYPE:'+_.get(payload,'type','UNKNOWN'),
         'NAME:NOTIFICATION_SEND',
         'SERVICE:'+service,
         'TEMPLATE_ID:'+templateId
   ]
   
   if(error!=null){
      tags.push("REASON:"+error)
   }
   if([6,7,9].includes(_.get(payload,'status',0))){
      tags.push("RETRY:TRUE")
   }
   _sendMetricsToDD(1, tags)
   if(status == "SENT"){

      let timeDiff = MOMENT().diff(MOMENT(_.get(payload , 'send_at')),'ms')
      _sendMetricsToDD(
         timeDiff, 
         ['STATUS:'+status,
         'SEND_TYPE:'+_.get(payload,'type','UNKNOWN'),
         'NAME:NOTIFICATION_SEND_LATENCY',
         'SERVICE:'+service],'histogram'
      )
   }
}catch(err){
   L.error(`sendNotificationMetricsFromSend :  Error while publishing matrics:-${err}`)
}
}

let sendNotificationMetricsFromCreate = (payload ,notificationRecord , status )=>{
   _sendMetricsToDD(1, [
      'STATUS:'+status,
      'TYPE:'+_.get(payload,'notificationType','UNKNOWN'),
      'PARTIAL_BILL:'+_.get(payload, 'partialBillState', "NO_STATE"),
      'NAME:NOTIFICATION_CREATE',
      'SERVICE:'+_.get(payload,'service','UNKNOWN'),
      'SEND_TYPE:'+_.get(notificationRecord,'type','UNKNOWN'),
      'SOURCE_ID:'+_.get(payload, "source", 'NO_SOURCE')
  ])
}

  let sendNotificationMetricsFromCustomNotificationsCreate = (payload ,notificationRecord , status )=>{
   _sendMetricsToDD(1, [
      'STATUS:'+status,
      'TYPE:'+_.get(payload,'notificationType','UNKNOWN'),
      'NAME:CUSTOM_NOTIFICATION_CREATE',
      'SERVICE:'+_.get(payload,'service','UNKNOWN'),
      'SEND_TYPE:'+_.get(notificationRecord,'type','UNKNOWN')
  ])

}

let sendNonPaytmBillsMetrics = (status, type, source, payload, error = null) => {
   let tags = [
      'REQUEST_TYPE:NON_PAYTM_BILLS',
      'FLOW:NEW_NON_PAYTM_BILLS',
      'STATUS:' + status,
      'TYPE:' + type,
      'SOURCE:' + source,
      'SERVICE:' + _.get(payload, 'service', 'UNKNOWN'),
      'OPERATOR:' + _.get(payload, 'operator', 'UNKNOWN'),
      'ORIGIN:' + _.get(payload, 'source', 'UNKNOWN')
   ];

   const partialBillState = _.get(payload, 'partialBillState');
   if (partialBillState) {
      tags.push('PARTIAL_BILL:' + partialBillState);
   }
   const dataExhaust = _.get(payload, 'isRealTimeDataExhausted', false);
   if (dataExhaust) {
      tags.push('DATA_EXHAUST:' + dataExhaust);
   }
   const isValidityExpired = _.get(payload, 'isValidityExpired', false);
   if (isValidityExpired) {
      tags.push('IS_VALIDITY_EXPIRED:' + isValidityExpired);
   }
   if (error) {
      tags.push('ERROR:' + error);
   }
   _sendMetricsToDD(1, tags);
};

export default {
   _sendMetricsToDD,
   _sendLatencyToDD,
   _setSMSLatencyToDD,
   sendNotificationMetricsFromSource,
   sendNotificationMetricsFromSend,
   sendNotificationMetricsFromCreate,
   sendNotificationMetricsFromCustomNotificationsCreate,
   sendNonPaytmBillsMetrics

};
