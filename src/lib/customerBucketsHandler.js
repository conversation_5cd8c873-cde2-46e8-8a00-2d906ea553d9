"use strict";

import _ from 'lodash'
import utility from './index'

/* 
 * This class contains helper functions that are being used for
 * handling customer buckets in publisher service
*/

class customerBucketsHandler {

    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
    }

    getServicePaytypeFromRecords(records,parent){
        let self = this;
        try{
            let service = _.toLower(_.get(records[0], 'service', '').replace(/\s/g, ""));
            let paytype = _.toLower(_.get(records[0], 'paytype', '').replace(/\s/g, ""));

            if(service == '' || paytype == ''){
                let product_id = _.get(records[0], 'product_id', '');
                let active_pid = self.activePidLib.getActivePID(product_id);
                service = _.toLower(_.get(self.config, ['CVR_DATA', active_pid, 'service'], '').replace(/\s/g, ""));
                paytype = _.toLower(_.get(self.config, ['CVR_DATA', active_pid, 'paytype'], '').replace(/\s/g, ""));
            }

            let servicePaytype = `${service}_${paytype}`;
            self.L.log('Publisher::', parent.operator, 'Service Paytype:', servicePaytype);
            return servicePaytype;
        }catch(e){
            self.L.error('Publisher::', parent.operator, 'Error while getting service paytype:', e);
            throw e;
        }
    }

    getPriorityBucketsList(records, parent){
        let self = this;
        try{
            let servicePaytype = self.getServicePaytypeFromRecords(records, parent);
            /*
            name: USER_SCORE_INGESTOR
            node:USER_SCORE_BUCKET_CONFIG
            key_name:mobile_prepaid
            value: {
                "scores": [10,30,50], 
                "buckets": ['silver','gold','diamond'], 
                "nbfdHours":[-24,-48,-72],
                "thresholdScore":0
            }
            */ 
            let serviceWiseBucketConfig = _.cloneDeep(_.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'USER_SCORE_BUCKET_CONFIG', `${servicePaytype}`], null));
            if(serviceWiseBucketConfig){
                let buckets = _.get(serviceWiseBucketConfig, 'buckets', []);
                if(_.isArray(buckets) && buckets.length > 0){
                    //save the reversed array , because diamond is the highest priority
                    parent.priorityBucketsList = buckets.reverse();
                    self.L.log('Publisher::', parent.operator, 'Priority Bucket List:', parent.priorityBucketsList);
                }else{
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', 'TYPE:SETTING_PRIORITY_BUCKETS','STATUS:NO_PRIORITY_BUCKETS_FOUND', 'TABLE:' + parent.tableName, `batch:${parent.batchId}`, `operator:${parent.operator}`, `servicePaytype:${servicePaytype}`]);
                }
            }else{
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', 'TYPE:SETTING_PRIORITY_BUCKETS','STATUS:NO_PRIORITY_BUCKETS_FOUND', 'TABLE:' + parent.tableName, `batch:${parent.batchId}`, `operator:${parent.operator}`, `servicePaytype:${servicePaytype}`]);
            }

            if(_.isArray(parent.priorityBucketsList) && parent.priorityBucketsList.length > 0){
                parent.priorityBucketsList.push('noBucket'); //last bucket will be for null
            }else{
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', 'TYPE:SETTING_PRIORITY_BUCKETS','STATUS:NO_PRIORITY_BUCKETS_FOUND', 'TABLE:' + parent.tableName, `batch:${parent.batchId}`, `operator:${parent.operator}`, `servicePaytype:${servicePaytype}`]);
            }
        }catch(e){
            self.L.error('Publisher::', parent.operator, 'Error while getting priority buckets list:', e);
            throw e;
        }
    }

    canPrioritiesBeSet(records, parent){
        let self = this;
        if(!_.isArray(records) || records.length == 0){
            self.L.log('Publisher::', parent.operator, 'No records found to set priority buckets');
            return false;
        }

        let service = _.get(records[0], 'service', '');
        let operatorWhiteListed = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'OPERATOR_WHITE_LIST', 'OPERATOR_LIST'], []);
        let serviceWhiteListed = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'SERVICE_WHITE_LIST', 'SERVICE_LIST'], []);

        if(operatorWhiteListed.indexOf(_.toLower(parent.operator)) > -1){
            return true;
        }else if (operatorWhiteListed.indexOf('ALL_OPERATORS')> -1){
            if(serviceWhiteListed.indexOf(_.toLower(service)) > -1 || serviceWhiteListed.indexOf('ALL_SERVICES') > -1){
                return true;
            }else{
                self.L.log('Publisher::', parent.operator, 'Service not white listed:', service);
                return false;
            }
        }else{
            self.L.log('Publisher::', parent.operator, 'Operator not white listed:', parent.operator);
            return false;
        }
    }

    setPriorityBucketsByServicePaytype(records,parent){
        let self = this;
        try{
            if(self.canPrioritiesBeSet(records, parent)){
                self.getPriorityBucketsList(records, parent);
                if(_.isArray(parent.priorityBucketsList) && parent.priorityBucketsList.length > 0){
                    parent.priorityBuckets = {};
                    for(let i=0; i< parent.priorityBucketsList.length; i++){
                        _.set(parent.priorityBuckets, i, parent.priorityBucketsList[i]);
                    }
                    self.L.log('Publisher::', parent.operator, 'Priority Buckets:', parent.priorityBuckets);
                }else{
                    self.L.log('Publisher::', parent.operator, 'No config found to set priority buckets');
                    // utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', 'TYPE:SETTING_PRIORITY_BUCKETS', 'STATUS:NO_PRIORITY_BUCKETS_FOUND', 'TABLE:' + parent.tableName, `batch:${parent.batchId}`, `operator:${parent.operator}`]);
                }
            }else{
                if(!parent.priorityBuckets) self.L.log('Publisher::', parent.operator, 'No records found to set priority buckets');
            }
        }catch(e){
            self.L.error('Publisher::', parent.operator, 'Error while setting priority buckets:', e);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', 'TYPE:SETTING_PRIORITY_BUCKETS', 'STATUS:ERROR', 'TABLE:' + parent.tableName, `batch:${parent.batchId}`, `operator:${parent.operator}`]);
        }
    }

    startPriorityResetTimer(parent) {
        let self = this;
        parent.priorityResetTimerCompleted = false;
        self.L.log('Publisher::', parent.operator, 'Starting the timer for', parent.priorityResetTime, 'minutes');
        parent.timer = setTimeout(() => {
            self.L.log('Publisher::', parent.operator, 'Timer completed for', parent.priorityResetTime, 'minutes');
            parent.priorityResetTimerCompleted = true;
        }, parent.priorityResetTime*60*1000);
    }

    restartPriorityResetTimer(parent){
        let self = this;
        clearTimeout(parent.timer);
        self.startPriorityResetTimer(parent);
    }

    getCurrentPriorityBucket(parent){
        let self = this;
        if(!parent.currentPriorityBucket){
            parent.currentPriorityBucket = 0;
        }
        self.L.log('Publisher::', parent.operator, 'Current priority bucket:', parent.priorityBuckets[parent.currentPriorityBucket]);
        return parent.priorityBuckets[parent.currentPriorityBucket];
    }

    resetPriorityBucket(parent){
        let self = this;
        self.L.log('Publisher::', parent.operator, 'Resetting the priority bucket', 'current bucket:', parent.priorityBuckets[parent.currentPriorityBucket], 'next bucket:', parent.priorityBuckets[0]);
        parent.currentPriorityBucket = 0;
        self.restartPriorityResetTimer(parent);
    }

    shiftPriorityBucket(parent){
        let self = this;
        if(parent.currentPriorityBucket < parent.priorityBucketsList.length-1){
            self.L.log('Publisher::', parent.operator, 'Shifting to next priority bucket', 'current bucket:', parent.priorityBuckets[parent.currentPriorityBucket], 'next bucket:', parent.priorityBuckets[parent.currentPriorityBucket+1]);
            if(parent.currentPriorityBucket == 0){
                self.restartPriorityResetTimer(parent);
            }
            parent.currentPriorityBucket++;
        }
    }

    getLastPriorityBucket(parent){
        let self = this;
        self.L.log('Publisher::', parent.operator, 'Last priority bucket:', parent.priorityBuckets[parent.priorityBucketsList.length-1]);
        return parent.priorityBuckets[parent.priorityBucketsList.length-1];
    }
}

export default customerBucketsHandler;

