/*
    Author: <PERSON><PERSON><PERSON>
*/

import _ from 'lodash';
import MOMENT from 'moment';


const _TIER = "_tier";
const _SCORE_100 = "_score_100";
// const aa = abcd;
const SCORES = 'scores';
const BUKCETS = 'buckets';
const NBFD_HOURS = "nbfdHours";
import utility              from './'

class UserScoreHelper{

    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.refreshScoreConfig();
    }

    refreshScoreConfig() { //refreshes config after every 15 mins

        let self = this;
        let scoreConfig = {};
        let dbConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'USER_SCORE_BUCKET_CONFIG'], {});  // {mobile_prepaid: {scores: [50,30,10], buckets: ['diamond','gold','silver'], nbfdHours:[-72,-48,-24], thresholdScore:0}}
        
        self.L.log('refreshScoreConfig::', `updating score config`);

        for (const [key, value] of Object.entries(dbConfig)) {
            if(((_.get(value, SCORES, [])).length != (_.get(value, BUKCETS, [])).length) ||           
                ((_.get(value, SCORES, [])).length != (_.get(value, NBFD_HOURS, [])).length) ||
                (_.get(value, SCORES, [])).length == 0 ||
                !self.isArraySortedinascendingOrder(_.get(value, SCORES, []))
            ){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR', "ERROR:ERROR_IN_DB_CONFIG", "FUNCTION_NAME:refreshScoreConfig"]);
                self.L.error('refreshScoreConfig::', 'error while parsing score config for :',key);
                continue;
            }
            // self.sortArrays(dbConfig[key][SCORES], dbConfig[key][BUKCETS], dbConfig[key][NBFD_HOURS]);
            scoreConfig[key] = _.cloneDeep(dbConfig[key]);
        }

        self.scoreConfig = scoreConfig;

        self.L.log('refreshScoreConfig::', `score config: ${JSON.stringify(self.scoreConfig)}`);
        
        setTimeout(() => {
            self.refreshScoreConfig();
        }, 15 * 60 * 1000);
    }

    getUserTierBasedOnCategory(category, payType, score) {
        let self = this;

        category = self.stringToLowerAndFormat(category);
        payType = self.stringToLowerAndFormat(payType);
        
        let bucket = null,
            category_paytype = `${category}_${payType}`,
            nbfdHours = null,
            thresholdScore = _.get(self.scoreConfig, [category_paytype,'thresholdScore'], -999999);   

        for(let i=_.get(self.scoreConfig, [category_paytype, SCORES], []).length - 1; i >=0 ; i--) {
            if(score >= _.get(self.scoreConfig, [category_paytype, SCORES])[i]) {
                bucket = _.get(self.scoreConfig, [category_paytype, BUKCETS])[i];
                nbfdHours = _.get(self.scoreConfig, [category_paytype, NBFD_HOURS])[i];
                break;
            }
        }
        return [bucket, nbfdHours, thresholdScore];
    }

    isArraySortedinascendingOrder(arr) { 
        return arr
            .slice(1)
            .every((num,i) => num > arr[i]); 
    }

    sortArrays(scores, buckets, nbfdHours) { // sorts in descending order
        
        let list = [];
        for (let j = 0; j < scores.length; j++) 
            list.push({'score': scores[j], 'bucket': buckets[j], 'nbfd': nbfdHours[j]});
    
        //2) sort:
        list.sort(function(a, b) {
            return ((a.score > b.score) ? -1 : ((a.score == b.score) ? 0 : 1));
            //Sort could be modified to, for example, sort on the bucket 
        });
    
        //3) separate them back out:
        for (let k = 0; k < list.length; k++) {
            scores[k] = list[k].score;
            buckets[k] = list[k].bucket;
            nbfdHours[k] = list[k].nbfd;
        }
    }

    stringToLowerAndFormat(string) {
        if(string != null && (typeof string) == "string") {
            return string.toLowerCase().replace(/\s/g, "");
        }
        return string;
    }

    parseDateString(stringDate) {
        let self = this;
        let date = null, 
            dateFormat= "YYYY-MM-DD HH:mm:ss",
            dateFormat1 = "YYYY-MM-DD HH:mm:ss";
        if(self.isStringAPositiveInteger(stringDate)) {
            date = MOMENT(parseInt(stringDate)).format(dateFormat);
        } else if(MOMENT(stringDate, dateFormat1).isValid()) {
            date = MOMENT(stringDate, dateFormat1).format(dateFormat);
        } else if(MOMENT(stringDate).isValid()) {
            date = MOMENT(stringDate).format(dateFormat);
        }

        if(date == null){
            self.L.error('parseDateString::', 'Unable to parse date for:',stringDate);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR', "ERROR:UNABLE_TO_PARSE_DATE", "FUNCTION_NAME:parseDateString"]);
        }

        return date;
    }

    isStringAPositiveInteger(string) {
        return string >>> 0 === parseFloat(string);
    }

    getUserTier(category, payType, score) {
        let self = this;

        category = self.stringToLowerAndFormat(category);
        payType = self.stringToLowerAndFormat(payType);
        
        let bucket = null,
            category_paytype = `${category}_${payType}`,
            nbfdHours = null,
            thresholdScore = _.get(self.scoreConfig, [category_paytype,'thresholdScore'], -999999);
        if(_.get(self.scoreConfig, [category_paytype, SCORES],[]).length == 0) {
            return {error:"invalid config"};
        }    

        for(let i=_.get(self.scoreConfig, [category_paytype, SCORES], []).length - 1; i >=0 ; i--) {
            if(score >= _.get(self.scoreConfig, [category_paytype, SCORES])[i]) {
                bucket = _.get(self.scoreConfig, [category_paytype, BUKCETS])[i];
                nbfdHours = _.get(self.scoreConfig, [category_paytype, NBFD_HOURS])[i];
                break;
            }
        }
        return {bucket, nbfdHours, thresholdScore};
    }
}

export default UserScoreHelper;