import _ from 'lodash';
import utility from '../lib';
import <PERSON><PERSON><PERSON> from 'async';
import <PERSON><PERSON>EN<PERSON> from 'moment';
import billsLib from './bills';
import KafkaPublisherFactory from '../lib/kafkaPublisherFactory';
import logger from '../lib/logger';

class RejectedNotificationHandler {
    /**
     * Creates a new RejectedNotificationHandler instance
     * @param {Object} options - The options object

     */
    constructor(options) {
        this.config = options.config;
        this.L = options.L;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.billsLib = new billsLib(options);
        this.logger = new logger(options);
        this.kafkaPublisherFactory = new KafkaPublisherFactory(this.config, this.infraUtils, this.L);
    }

    /**
     * Initialize the rejected notification handler
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            this.rejectNotificationkafkaPublisher = await this.kafkaPublisherFactory.createPublisher('NOTIFICATION_REJECTS');
            this.L.log('RejectedNotificationHandler initialized successfully');
        } catch (error) {
            this.L.error('Failed to initialize RejectedNotificationHandler:', error);
            throw error;
        }
    }

    /**
     * Close all Kafka connections
     * @returns {Promise<void>}
     */
    async close() {
        try {
            if (this.rejectNotificationkafkaPublisher && this.rejectNotificationkafkaPublisher.client) {
                await new Promise((resolve, reject) => {
                    this.rejectNotificationkafkaPublisher.client.close((error) => {
                        if (error) {
                            this.L.error('Error closing reject notification Kafka client:', error);
                            reject(error);
                        } else {
                            this.L.log('Successfully closed reject notification Kafka client');
                            resolve();
                        }
                    });
                });
            }

            // Close the Kafka publisher factory
            await this.kafkaPublisherFactory.closeAll();
            
            this.L.log('All Kafka connections closed successfully');
        } catch (error) {
            this.L.error('Error closing Kafka connections:', error);
            throw error;
        }
    }

    /**
     * Insert a rejected notification
     * @param {Object} record - The record that was rejected
     * @param {string} error - The error message
     * @param {Object} options - Additional options
     * @returns {Promise<void>}
     */

    async insertRejectedNotifications(cb, error, record, notificationRecord) {
        let self = this;
        if(_.get(record, 'value', null) && _.get(record, 'partition',null)){ //direct kafka payload
            try{
                if(typeof record.value == 'string')
                record = JSON.parse(record.value);
            }catch(e){
                self.L.error('Error while parsing kafka payload', e);
                return cb();
            }
        }
        let [kafkaPayload] = self.createParamsForNotificationRejection(error, record, notificationRecord);
        ASYNC.parallel([
            function (callback) {
                self.rejectNotificationkafkaPublisher.publishData([
                    { topic: _.get(self.config, ['KAFKA', 'SERVICES', 'NOTIFICATION_REJECTS', 'TOPIC'], null), 
                    messages: JSON.stringify(kafkaPayload), 
                    key: _.get(record, 'recharge_number', null) }], function (error) {
                    if (error) {
                        utility.sendNotificationMetricsFromSend(record, "ERROR","PUBLISHING_TO_NOTIFICATION_REJECT_KAFKA")
                        self.logger.error(`Error while publishing message in Kafka rejectNotificationkafkaPublisher ${error} - MSG:- `, kafkaPayload, _.get(kafkaPayload, 'service', ''));
                    } else {
                        utility.sendNotificationMetricsFromSend(record, "SUCCESS","PUBLISHING_TO_NOTIFICATION_REJECT_KAFKA")
                        self.logger.log(`Message published successfully in Kafka  on topic ${_.get(self.config, ['KAFKA', 'SERVICES', 'NOTIFICATION_REJECTS', 'TOPIC'], null)}`, kafkaPayload, _.get(kafkaPayload, 'service', ''));
                    }
                    callback(error);
                }, [200, 800]);
            }
        ]
        , function (err, results) {
            return cb();
        })
    }

    createParamsForNotificationRejection(error, record, notificationRecord) {
        let self = this;
        let date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        let raw_expiry_date = _.get(record, 'due_date', null) || _.get(record, 'validity_expiry_date', null) || _.get(record, ['data','additional_data','due_date'], null);
        let timepoint = _.get(record, 'timepoint', _.get(record, ['data','additional_data','timepoint'], null));
        let notif_type = _.get(record, 'notificationType', null) || _.get(record, ['data','additional_data','notif_type'], null);
        let recon_id = _.get(record, 'recon_id', null) || _.get(record, ['data','additional_data','recon_id'], null);
        let billSource = 'RU';
        if(self.notificationBillSource == 'NONRU') {
            billSource = 'NONRU';
        }
        if(MOMENT(raw_expiry_date).isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date).format('YYYY-MM-DD HH:mm:ss');
        }else if(MOMENT(raw_expiry_date, 'Do MMM YYYY', true).isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date, 'Do MMM YYYY', true).format('YYYY-MM-DD HH:mm:ss');
        }

        if(_.get(record, 'requestType', null) && _.get(record, 'requestType', null).includes('PLAN_VALIDITY_NOTIFICATION')){
            timepoint = -_.get(record, 'dayValue',null);
            notif_type = 'DUEDATE';
        }

        let isTimepointAbsent = (timepoint===null || timepoint===undefined || timepoint==='');

        if(_.get(record, 'dataFrom',null)=='billDueCron' || _.get(record, 'dataFrom',null)=='pvCron' || _.get(record, 'dataFrom',null)=='billGenCron' || isTimepointAbsent){
            if(_.get(record, 'dataFrom',null)=='billGenCron' || notif_type=='BILLGEN'){
                notif_type = 'BILLGEN';
                let bFdate = MOMENT(_.get(record, 'bill_fetch_date', null)).isValid()? MOMENT(_.get(record, 'bill_fetch_date', null)).format('YYYY-MM-DD') :null;
                let currDate = MOMENT().format('YYYY-MM-DD');
                if(bFdate) timepoint = MOMENT(currDate).diff(MOMENT(bFdate), 'days');
            }else{
                notif_type = 'DUEDATE';
                let dDate = MOMENT(_.get(record, 'due_date', _.get(record, 'validity_expiry_date', null))).isValid()? MOMENT(_.get(record, 'due_date', _.get(record, 'validity_expiry_date', null))).format('YYYY-MM-DD') :null;
                let currDate = MOMENT().format('YYYY-MM-DD');
                console.log('dueDate and currDate', dDate, currDate);
                if(dDate) timepoint = MOMENT(dDate).diff(MOMENT(currDate), 'days');
            }
            try{
                let extra = _.get(record, 'extra', null);
                if(typeof extra == 'string'){
                    extra = JSON.parse(extra);
                }
                if(_.get(extra, 'recon_id', null)){
                    recon_id = _.get(extra, 'recon_id', null);
                }else{
                    recon_id = utility.generateReconID(_.get(record, 'recharge_number', null), _.get(record, 'operator', null), _.get(record, 'amount', null), _.get(record, 'due_date', null) || _.get(record, 'validity_expiry_date'), _.get(record, 'bill_date', null));
                }
            }catch(e){
                self.L.error('Error while parsing extra for recon_id', e);
                recon_id = utility.generateReconID(_.get(record, 'recharge_number', null), _.get(record, 'operator', null), _.get(record, 'amount', null), _.get(record, 'due_date', null) || _.get(record, 'validity_expiry_date'), _.get(record, 'bill_date', null));
            }
        }


        /** ccbp encryption changes needed here 
         * NO CHANGES needed since this record is not getting inserted in calling function 
         * removing 0 indexed value from the array*/
        return [
        {
            recharge_number: _.get(record, 'recharge_number', null),
            customer_id: _.toNumber(_.get(record, 'customer_id', null) || _.get(record, ['data','additional_data','customer_id'], null)),
            source_id: _.get(record, 'source_id', null),
            category_id: _.get(record, 'category_id', null),
            product_id: _.get(record, 'product_id', null),
            recon_id: recon_id,
            type: _.get(notificationRecord, 'type', null) || _.get(record, 'type', null) || 'UNKNOWN',
            template_id: _.get(notificationRecord, 'template_id', null) || _.get(record, 'template_id', null),
            recipient: _.get(record, 'recipient', null) || _.get(notificationRecord, 'recipient', null),
            data: _.get(record, 'data', null),
            priority: _.get(record, 'priority', null),
            error_msg: error,
            status: _.get(self.config,['NOTIFICATION', 'status', 'CANCELED'], 2),
            notif_type: notif_type,
            operator: _.get(record, 'operator', null) || _.get(record, ['data','additional_data','operator'], null),
            amount: _.get(record, 'amount', null) || _.get(record, ['data','additional_data','amount'], null),
            service: _.get(record, 'service', null) || _.get(record, ['data','additional_data','service'], null),
            raw_expiry_date: raw_expiry_date,
            bill_source: _.get(record, 'bill_source', null) || _.get(record, ['data','additional_data','bill_source'], null) || billSource,
            promocode: _.get(record, 'promocode', null) || _.get(record, ['data','additional_data','promocode'], null),
            msg_type: _.get(record, 'msg_type', null) || _.get(record, ['data','additional_data','msg_type'], null),
            timepoint: _.toString(timepoint),
            template_name: _.get(record, 'template_name', null) || _.get(record, ['data','additional_data','templateName'], null),
            created_at: date,
            updated_at: date,
            job_id:null,
            send_at:null,
            sent_at:null,
            retry_interval:30,
            max_retry_count: 3,
            retry_count: 0,
            message_id: _.get(record, 'message_id', null)
        }
    ];
    }
}

export default RejectedNotificationHandler; 