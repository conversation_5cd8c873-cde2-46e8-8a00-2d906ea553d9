"use strict";

import _ from 'lodash'
import DigitalCatalog from './digitalCatalog'
import MOMENT from 'moment'
import utility from '../lib'
import SAGA from './saga';



/**
 * SMS Parsing dashboard Libary will publish all diffrent delays interval related to smsData
 */

 class SmsParsingLagDashboard {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.digitalCatalogLib = new DigitalCatalog(options);
        this.sagaUtils = new SAGA(options);
        this.dcatCategoryCacheData = null;
    }

    validateLatencies(timestamp1, timestamp2, latency){
        if(!timestamp1 || !timestamp2 || !latency ||isNaN(latency) ){
            return false;
        }
        return true;
    }

    publishDelaysMetrics(cb, source, timestamps, operator,processedRecord){    
        let self = this; 
        try{
            let client_processing_time_smsDateTime, client_processing_time_deviceDateTime
                let client_processing_time = Math.abs(timestamps.data_smsDateTime - timestamps.data_deviceDateTime),
                data_processing_time = Math.abs(timestamps.data_deviceDateTime - timestamps.data_timestamp),
                collector_processing_time = Math.abs(timestamps.data_timestamp - timestamps.collector_timestamp),
                kafka_onboarding_time = Math.abs(timestamps.collector_timestamp - timestamps.data_uploadTime),
                RU_read_latency = Math.abs(timestamps.data_uploadTime - timestamps.RUreadsKafkaTime),
                RU_read_latency_byKafkaUploadTime = Math.abs(timestamps.dwhKafkaPublishedTime - timestamps.RUreadsKafkaTime),
                RU_processing_time = Math.abs(timestamps.RUreadsKafkaTime - timestamps.RUupdatesDbTime),
                RU_act_latency = Math.abs(timestamps.RUupdatesDbTime - timestamps.RUupdateRecentTime),
                bill_fetch_kafka_read_latency = Math.abs(timestamps.billFetchReminder_onBoardTime - timestamps.billFetchReminder_acknowledgeTime),
                notification_create_latency = Math.abs(timestamps.notify_onBoardTime - timestamps.billFetchReminder_acknowledgeTime),
                notify_read_latency = Math.abs(timestamps.notify_onBoardTime - timestamps.notify_acknowledgeTime),
                notify_process_latency = Math.abs(timestamps.centralNotifyApiHitTime - timestamps.notify_acknowledgeTime),
                end_to_end_latency = Math.abs(timestamps.centralNotifyApiHitTime - timestamps.dwhKafkaPublishedTime),
                RU_upstream_time  = Math.abs(timestamps.data_smsDateTime - timestamps.RUreadsKafkaTime),
                whatsapp_response_latency = Math.abs(timestamps.cnsPublishedTime - timestamps.eventTimestamp),
                end_to_end_latency_for_sms_invoke_flow = Math.abs(timestamps.centralNotifyApiHitTime - timestamps.eventTimestamp);

                let latencies = [
                    {timestamp1 : timestamps.data_smsDateTime, timestamp2 : timestamps.data_deviceDateTime, latency:client_processing_time , delayType : 'CLIENT_PROCESSING_TIME'},
                    {timestamp1 : timestamps.data_deviceDateTime, timestamp2 : timestamps.data_timestamp, latency:data_processing_time , delayType : 'DATA_PROCESSING_TIME'},
                    {timestamp1 : timestamps.data_timestamp, timestamp2 : timestamps.collector_timestamp, latency:collector_processing_time , delayType : 'COLLECTOR_PROCESSING_TIME'},
                    {timestamp1 : timestamps.collector_timestamp, timestamp2 : timestamps.data_uploadTime, latency:kafka_onboarding_time , delayType : 'KAFKA_ONBOARDING_TIME'},
                    {timestamp1 : timestamps.data_uploadTime, timestamp2 : timestamps.RUreadsKafkaTime, latency:RU_read_latency , delayType : 'RU_READ_LATENCY'},
                    {timestamp1 : timestamps.dwhKafkaPublishedTime, timestamp2 : timestamps.RUreadsKafkaTime, latency:RU_read_latency_byKafkaUploadTime , delayType : 'RU_READ_LATENCY_KAFKA_UPLOAD_TIME'},
                    {timestamp1 : timestamps.RUreadsKafkaTime, timestamp2 : timestamps.RUupdatesDbTime, latency:RU_processing_time , delayType : 'RU_PROCESSING_TIME'},
                    {timestamp1 : timestamps.RUupdatesDbTime, timestamp2 : timestamps.RUupdateRecentTime, latency:RU_act_latency , delayType : 'RU_ACT_LATENCY'},
                    {timestamp1 : timestamps.RUreadsKafkaTime, timestamp2 : timestamps.RUupdatesDbTime, latency:RU_processing_time , delayType : 'RU_PROCESSING_TIME'},
                    {timestamp1 : timestamps.billFetchReminder_acknowledgeTime, timestamp2 : timestamps.billFetchReminder_onBoardTime, latency:bill_fetch_kafka_read_latency , delayType : 'BILL_FETCH_KAFKA_READ_LATENCY'},
                    {timestamp1 : timestamps.billFetchReminder_acknowledgeTime, timestamp2 : timestamps.notify_onBoardTime, latency:notification_create_latency , delayType : 'NOTIFICATION_CREATE_LATENCY'},
                    {timestamp1 : timestamps.notify_acknowledgeTime, timestamp2 : timestamps.notify_onBoardTime, latency:notify_read_latency , delayType : 'NOTIFY_READ_LATENCY'},
                    {timestamp1 : timestamps.notify_acknowledgeTime, timestamp2 : timestamps.centralNotifyApiHitTime, latency:notify_process_latency , delayType : 'NOTIFY_PROCESS_LATENCY'},
                    {timestamp1 : timestamps.dwhKafkaPublishedTime, timestamp2 : timestamps.centralNotifyApiHitTime, latency:end_to_end_latency , delayType : 'END_TO_END_LATENCY'},
                    {timestamp1 : timestamps.data_smsDateTime, timestamp2 : timestamps.RUreadsKafkaTime, latency:RU_upstream_time , delayType : 'RU_UPSTREAM_TIME'},
                    {timestamp1 : timestamps.cnsPublishedTime, timestamp2 : timestamps.eventTimestamp, latency:whatsapp_response_latency , delayType : 'WHATSAPP_RESPONSE_LATENCY'},
                    {timestamp1 : timestamps.centralNotifyApiHitTime, timestamp2 : timestamps.eventTimestamp, latency:end_to_end_latency_for_sms_invoke_flow , delayType : 'END_TO_END_LATENCY_FOR_SMS_INVOKE_FLOW'},
                ];
            
            latencies.forEach((latency) => {
                if(self.validateLatencies(latency.timestamp1, latency.timestamp2, latency.latency)){
                    self.L.log(`publish metrics: delay ${latency.delayType} : ${latency.latency} ms, for operator: ${operator} ,source: ${source}`)
                    utility._setSMSLatencyToDD(latency.latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'DELAY_TYPE':`${latency.delayType}`});
                }
            });

            // if(!isNaN(RU_upstream_time)){
            //     self.L.log(`publish metrics: delay RU_upstream_time : ${RU_upstream_time} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(RU_upstream_time, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'SERVICE':`${processedRecord.service}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'RU_UPSTREAM_TIME'});
            // }

            // if( !isNaN(client_processing_time))
            // {    
            //     self.L.log(`publish metrics: delay client_processing_time : ${client_processing_time} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(client_processing_time, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'CLIENT_PROCESSING_TIME'});
            // }
            // if( !isNaN(client_processing_time_smsDateTime))
            // {    
            //     self.L.log(`publish metrics: delay client_processing_time_smsDateTime : ${client_processing_time_smsDateTime} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(client_processing_time_smsDateTime, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'CLIENT_PROCESSING_TIME_SMSDATETIME'});
            // }
            // if( !isNaN(client_processing_time_deviceDateTime))
            // {    
            //     self.L.log(`publish metrics: delay client_processing_time_deviceDateTime : ${client_processing_time_deviceDateTime} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(client_processing_time_deviceDateTime, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'CLIENT_PROCESSING_TIME_DEVICEDATETIME'});
            // }
            // if( !isNaN(data_processing_time))
            // {    
            //     self.L.log(`publish metrics: data_processing_timedelay : ${data_processing_time} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(data_processing_time, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'DATA_PROCESSING_TIME'});
            // } 
            // if( !isNaN(collector_processing_time))
            // {   
            //     self.L.log(`publish metrics: delay collector_processing_time: ${collector_processing_time} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(collector_processing_time, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'COLLECTOR_PROCESSING_TIME'});
            // }
            // if( !isNaN(kafka_onboarding_time))
            // {   
            //     self.L.log(`publish metrics: delay kafka_onboarding_time: ${kafka_onboarding_time} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(kafka_onboarding_time, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'KAFKA_ONBOARDING_TIME'});
            // }
            // if( !isNaN(RU_read_latency))
            // {   
            //     self.L.log(`publish metrics: delay RU_read_latency: ${RU_read_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(RU_read_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'RU_READ_LATENCY'});
            // }
            // if( !isNaN(RU_read_latency_byKafkaUploadTime))
            // {   
            //     self.L.log(`publish metrics: delay RU_read_latency_byKafkaUploadTime: ${RU_read_latency_byKafkaUploadTime} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(RU_read_latency_byKafkaUploadTime, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'RU_READ_LATENCY_KAFKA_UPLOAD_TIME'});
            // }
            // if( !isNaN(RU_processing_time))
            // {   
            //     self.L.log(`publish metrics: delay RU_processing_time: ${RU_processing_time} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(RU_processing_time, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'SERVICE':`${processedRecord.service}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'RU_PROCESSING_TIME'});
            // }

            // if( !isNaN(RU_act_latency))
            // {   
            //     self.L.log(`publish metrics: delay RU_act_latency: ${RU_act_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(RU_act_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'RU_ACT_LATENCY'});
            // }
            // if( !isNaN(bill_fetch_kafka_read_latency))
            // {   
            //     self.L.log(`publish metrics: delay bill_fetch_kafka_read_latency: ${bill_fetch_kafka_read_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(bill_fetch_kafka_read_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'BILL_FETCH_KAFKA_READ_LATENCY'});
            // }
            // if( !isNaN(notification_create_latency))
            // {   
            //     self.L.log(`publish metrics: delay notification_create_latency: ${notification_create_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(notification_create_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'NOTIFICATION_CREATE_LATENCY'});
            // }
            // if( !isNaN(notify_read_latency))
            // {   
            //     self.L.log(`publish metrics: delay notify_read_latency: ${notify_read_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(notify_read_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'NOTIFY_READ_LATENCY'});
            // }
            // if( !isNaN(notify_process_latency))
            // {   
            //     self.L.log(`publish metrics: delay notify_process_latency: ${notify_process_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(notify_process_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'NOTIFY_PROCESS_LATENCY'});
            // }
            // if( !isNaN(end_to_end_latency))
            // {   
            //     self.L.log(`publish metrics: delay end_to_end_latency: ${end_to_end_latency} ms, for operator: ${operator} ,source: ${source}`)
            //     utility._setSMSLatencyToDD(end_to_end_latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'CLASSID':`${_.get(processedRecord, 'rtspClassId', null)}`, 'DELAY_TYPE':'END_TO_END_LATENCY'});
            // }
            self.L.log('publishDelaysMetrics :: successfully published metrices');
            return cb(null);
        }
        catch(err){
            self.L.error(`publishDelaysMetrics :: Error in publishing metrices error : ${err}`);
            return cb(err);
        }
    }

    async publishWhatsappNotificationFallbackLatencies(timestamps, source){
        let self = this;
        return new Promise((resolve, reject) => {
            try{
                let ruAckLatency = Math.abs(timestamps.cnsPublishedTime - timestamps.ruAckTime);
                let whatsappResponseLatency = Math.abs(timestamps.cnsPublishedTime - timestamps.eventTimestamp);
                let endToEndLatencyForDwhLogFlow = Math.abs(timestamps.processingEndTime - timestamps.eventTimestamp);

                let latenciesToPublish = [
                    {timestamp1 : timestamps.cnsPublishedTime, timestamp2 : timestamps.cnsAcknowledgedTime, latency:ruAckLatency , delayType : 'RU_READ_LATENCY'},
                    {timestamp1 : timestamps.cnsPublishedTime, timestamp2 : timestamps.eventTimestamp, latency:whatsappResponseLatency , delayType : 'WHATSAPP_RESPONSE_LATENCY'},
                    {timestamp1 : timestamps.processingEndTime, timestamp2 : timestamps.eventTimestamp, latency:endToEndLatencyForDwhLogFlow , delayType : 'DWH_LOG_FLOW_END_TO_END_LATENCY'},
                ];

                latenciesToPublish.forEach((latency) => {
                    self.L.log(`publish metrics: delay ${latency.delayType} : ${latency.latency} ms, for ,source: ${source}`)
                    utility._setSMSLatencyToDD(latency.latency, {'REQUEST_TYPE':'SMS_PARSING_DASHBOARD', 'SOURCE':  `${source}`, 'DELAY_TYPE':`${latency.delayType}`});
                });
                resolve(null);
            }catch(err){
                self.L.error(`publishWhatsappNotificationFallbackLatencies :: Error in publishing latencies error : ${err}`);
                resolve(null);
            }
        });
    }
}

export default SmsParsingLagDashboard;