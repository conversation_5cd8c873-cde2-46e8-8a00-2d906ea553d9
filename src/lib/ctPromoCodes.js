import REQUEST from 'request'
import _ from 'lodash'
import utility from './datadog'

class CtPromoCodes {


    /*
        doc for promocodes from CT : https://docs.google.com/document/d/10EXOL8gyVSdIVaG2oMUhilkfcozgKwDxabVR2gs_ukM/edit
    */
    

	getPromoCodeFromCT(callback , config , params) {
        let self = this;
        let url = _.get(config,'CT_PROMOCODE_API',"https://in1-paytm.api.clevertap.com/pull");
        let ct_account_id = _.get(config,'accountId',null);
        let ct_account_password = _.get(config,'password',null);
		let apiOpts = {
            "uri": url,
            "method": "POST",
            "headers": {
                "X-CleverTap-Account-Id": ct_account_id,
                "X-CleverTap-Passcode": ct_account_password,
                "Accept": "application/json",
                "Cache-Control": "no-cache"
            },
            "json": {
                "Identity": params.customer_id,
                "device": "Android",
                "contexts": [
                    {
                        "context": `${params.service}_${params.paytype}_promocode_${params.template_id}`
                    }
                ]
                
            },
            "timeout": 3000,        // timeout of 3 secs
            "strictSSL":false
		};
        if(ct_account_id == null || ct_account_password == null) {
            return callback("ct_account_id or ct_account_password is null");
        }
        let latencyStart = new Date().getTime();
		
        REQUEST(apiOpts, (error, response, body) => {
			
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'GET_PROMOCODE_API',
                'URL': _.get(config,'CT_PROMOCODE_API',null)
            });

	    	let errorMsg = null,
                promoText = null,
                filteredRecord = null,
                campaigns = [];

	        if (error || (body && body.status && body.status != "success") || response.statusCode !== 200) {
	            errorMsg = (error) ? error : ((body.error) ? body.error : '');
	            errorMsg = 'Error in fetching promocode from CT '+ errorMsg;
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GET_PROMOCODE_API', 
                    `URL:${url}`,
                    'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                    'API_ERROR:ERROR_FROM_CT',
                    `CT_ERROR:${_.get(body,"error","null")}`
                ]);
	        } else if (body && typeof body === 'object') {
	            try {
	                campaigns = body["adUnit_notifs"][`${params.service}_${params.paytype}_promocode_${params.template_id}`];
	                if(campaigns.length == 0) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:GET_PROMOCODE_API', 
                            `URL:${url}`,
                            'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                            'API_ERROR:NO_CT_PROMOCODE_AVAILABLE'
                        ]);
                        throw `No promocodes available at CT's end for this customer_id: ${params.customer_id}, service: ${params.service} and paytype: ${params.paytype} combination`;
                    }
                    filteredRecord = self.filterPromoCodesApiResponseOnTheBasisOfPriority ( self.filterPromoCodesApiResponseOnTheBasisOfAmount ( campaigns, params));
                    if(filteredRecord == null) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:GET_PROMOCODE_API', 
                            `URL:${url}`,
                            'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                            'API_ERROR:NO_CT_PROMOCODE_MATCHED'
                        ]);
                        throw `No PromoContext matched/found for given customerId: ${params.customer_id} and service: ${params.service}`
                    }
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:GET_PROMOCODE_API', 
                        `URL:${url}`,
                        'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                        'API_RESPONSE:VALID_CT_PROMOCODE_FOUND'
                    ]);
                    // promoText = _.get(filteredRecord, 'Promo_Text', null);
                    // if(promoText == null) throw `Promotext not there in matched/found promoContect for given customerId: ${params.customer_id}, service: ${params.service} and paytype: ${params.paytype}`
	            } catch(error) {
	                errorMsg = error;
	            }
	        } else {
	            errorMsg = `Response body is not object for customer_id : ${params.customer_id}`;
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:GET_PROMOCODE_API', 
                    `URL:${url}`,
                    'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                    'API_ERROR:CT_PROMOAPI_PARSING_ERROR'
                ]);
	        }
	        return callback(errorMsg, filteredRecord);
        });
	}

    filterPromoCodesApiResponseOnTheBasisOfAmount(records, params) {
        // returns records with minAmount(in CT response) less than amount(in notification payload)
        let filtereRecords = [];
        for(let i=0; i<records.length; i++) {
            if(!records[i]["custom_kv"]["min_amount"] || isNaN(records[i]["custom_kv"]["min_amount"])) continue;
            if(records[i]["custom_kv"]["min_amount"] <= _.get(params, 'amount', 0)) {      // set default value to 0 to handle cases of partial bill
                filtereRecords.push(records[i]["custom_kv"]);
            }
        }
        return filtereRecords;
    }

    filterPromoCodesApiResponseOnTheBasisOfPriority(records) {
        // returns record with highest priority. lesser the number higher the priority
        let filteredRecord = null;
        for(let i=0; i<records.length; i++) {
            if(!records[i].priority || isNaN(records[i].priority)) continue;
            if(filteredRecord == null) {
                filteredRecord = records[i];
            } else if(records[i].priority < filteredRecord.priority) {
                filteredRecord = records[i];
            }
        }
        return filteredRecord;
    }
}

export default {
	CtPromoCodes
}