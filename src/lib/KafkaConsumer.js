/* jshint multistr: true, node: true*/
'use strict';

import _        from "lodash";
import Q        from "q";
import async    from "async";
import L        from "lgr";
import CONFIG   from "infra-utils/dest/kafka/config";
import admin    from "infra-utils/dest/kafka/admin";
import MOMENT   from "moment"
import { Kafka, CompressionTypes, CompressionCodecs } from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import utility from '../lib/datadog'

let _sendMetricsToDD = utility._sendMetricsToDD
CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;


/**
 * @class
 * Consumer Class to create kafka consumer and consume messages from the kafka topic
 */
class KafkaConsumer {
    /**
     * Configure specifications
     * @param { object } configParams The connection configuration.
     * @param { string } configParams.kafkaHost Kafka broker/host combination delimited by comma.
     * @param { string } configParams.groupId Consumer group.
     * @param { string|Array } configParams.topics Kafka topic or topics array.
     * @param { string } configParams.id Consumer Id (label).
     * @param { number } [configParams.sessionTimeout] Remove consumer session from the zookeeper in case of consumer restart.
     * @param { string } [configParams.protocol] An array of partition assignment protocols ordered by preference.
     * @param { string } [configParams.fromOffset] Offset, from where a consumer will start to consume messages value can be ['earliest', 'none', 'latest'].
     * @param { boolean } [configParams.autoCommit] If true then offset will commit automatically otherwise use commitOffset function to commit offset manually.
     * @param { number } [configParams.fetchMinBytes] This is the minimum number of bytes of messages that must be available to give a response, default 1 byte.
     * @param { number } [configParams.fetchMaxBytes] The maximum bytes to include in the message set for this partition. This helps bound the size of the response.
     * @param { number } [configParams.connectTimeout] In ms it takes to wait for a successful connection before moving to the next host default.
     * @param { number } [configParams.requestTimeout] In ms for a kafka request to timeout.
     * @param { number } [configParams.autoCommitIntervalMs] If autoCommit is true then commit after specified Ms.
     * @param { number } [configParams.batchSize] Message batch size.
     * @param { number } [configParams.consumer_idel_intervalMs] In ms wating time interval to process remaining messages from message queue.
     */


    



    constructor(configParams) {
        this.kafkaConfig = configParams ? configParams : {};

        this.options = {};


        this.adminOptions = {
            kafkaHost    : _.get(this.kafkaConfig, 'kafkaHost', null),
            requestTimeout : _.get(this.kafkaConfig, 'requestTimeout', CONFIG.KAFKA_CONFIG.REQUEST_TIMEOUT),
            connectTimeout : _.get(this.kafkaConfig, 'connectTimeout', CONFIG.KAFKA_CONFIG.CONNECTION_TIMEOUT)
        }

        

        this.options.kafkaHost      = _.get(this.kafkaConfig, 'kafkaHost', null);
        this.options.groupId        = _.get(this.kafkaConfig, 'groupId', null);
        this.options.maxBytes       = _.get(this.kafkaConfig, 'maxBytes', 10485760);
        this.options.sessionTimeout = _.get(this.kafkaConfig, 'sessionTimeout', 120*1000);
        this.options.maxProcessTimeout = _.get(this.kafkaConfig, 'maxProcessTimeout', 3*60*1000);
        this.options.protocol       = _.get(this.kafkaConfig, 'protocol', CONFIG.KAFKA_CONFIG.PROTOCOL);
        this.options.fromOffset     = _.get(this.kafkaConfig, 'fromOffset', CONFIG.KAFKA_CONFIG.FROM_OFFSET);
        this.options.autoCommit     = _.get(this.kafkaConfig, 'autoCommit', CONFIG.KAFKA_CONFIG.AUTO_COMMIT);
        this.options.fetchMinBytes  = _.get(this.kafkaConfig, 'fetchMinBytes', CONFIG.KAFKA_CONFIG.FETCH_MIN_BYTES);
        this.options.fetchMaxBytes  = _.get(this.kafkaConfig, 'fetchMaxBytes', CONFIG.KAFKA_CONFIG.FETCH_MAX_BYTES);
        this.options.connectTimeout = _.get(this.kafkaConfig, 'connectTimeout', CONFIG.KAFKA_CONFIG.CONNECTION_TIMEOUT);
        this.options.requestTimeout = _.get(this.kafkaConfig, 'requestTimeout', CONFIG.KAFKA_CONFIG.REQUEST_TIMEOUT);
        this.options.autoCommitIntervalMs       = _.get(this.kafkaConfig, 'autoCommitIntervalMs', CONFIG.KAFKA_CONFIG.AUTO_COMMIT_INTERVAL_MS);
        this.options.batchSize                  = _.get(this.kafkaConfig, 'batchSize', CONFIG.KAFKA_CONFIG.BATCH_SIZE);
        this.options.consumer_idel_intervalMs   = _.get(this.kafkaConfig, 'consumer_idel_intervalMs', CONFIG.KAFKA_CONFIG.CONSUMER_IDEL_INTERVAL_MS);
        
        this.topics         = _.get(this.kafkaConfig, 'topics', null);
        this.clientId       = _.get(this.kafkaConfig, 'id', null);

        this.heartBeatInterval = 2000

        this.serviceName = _.get(this.options,'serviceName','')

        

        if (this.options.kafkaHost === null) {
            L.error('Kafka consumer: ', CONFIG.MESSAGES.KAFKA_HOST_ERROR);
            throw new Error(CONFIG.MESSAGES.KAFKA_HOST_ERROR);
        } else if (this.options.groupId === null) {
            L.error('Kafka consumer: ', CONFIG.MESSAGES.GROUP_ID_NULL);
            throw new Error(CONFIG.MESSAGES.GROUP_ID_NULL);
        } else if (this.topics === null) {
            L.error('Kafka consumer: ', CONFIG.MESSAGES.TOPIC_NULL);
            throw new Error(CONFIG.MESSAGES.TOPIC_NULL);
        } else if (this.clientId === null) {
            L.error('Kafka consumer: ', CONFIG.MESSAGES.ID_NULL);
            throw new Error(CONFIG.MESSAGES.ID_NULL);
        }
    }

   

    _prepareResponse(cb, deferred, error, data) {
        error ? deferred.reject(error) : deferred.resolve(data);

        if (cb && typeof cb !== 'undefined') {
            return error ? cb(error) : cb(null, data);
        }
    }

    _validateCreateConsumer(done) {


        let adminObj = new admin({"kafkaHost": this.options.kafkaHost});
        let inputTopics = Array.isArray(this.topics) ? this.topics : [this.topics];

        async.waterfall([
            (callback) => {
                adminObj.connect((error, response) => {
                    error ? callback(error) : callback(null);
                });
            },
            (callback) => {
                async.parallel([
                        (cb) => {
                            adminObj.partitionCountOfTopics(inputTopics, (partitionCountError, partitionCount) => {
                                partitionCountError ? cb(partitionCountError) : cb(null, partitionCount);
                            });
                        },
                        (cb) => {
                            adminObj.consumersCountOfTopicsInGroup(inputTopics, this.options.groupId, (consumerCountError, consumerCount) => {
                                consumerCountError ? cb(consumerCountError) : cb(null, consumerCount);
                            });
                        }
                    ], (error, results) => {
                        error ? callback(error, null) : callback(null, results[0], results[1]);
                    }
                );
            },
            (partitionsCount, consumersCount, callback) => {
                let errorMessage = null;
                for (let i = 0; i < inputTopics.length; i++) {
                    let consumers  = _.get(consumersCount, inputTopics[i], 0);
                    let partitions = _.get(partitionsCount, inputTopics[i], 0);

                    if (consumers !== 0 && partitions !== 0 && (consumers === partitions)) {
                        L.error('_validateCreateConsumer: ', CONFIG.MESSAGES.CONSUMER_PARTITION_EQUAL);
                        errorMessage = CONFIG.MESSAGES.CONSUMER_PARTITION_EQUAL + inputTopics[i];
                        break;
                    }
                }
                callback(errorMessage);
            }
            ], (err) => {
                adminObj.close();
                done(err);
            }
        );
    }

    _pauseConsumer(){
        let pauseObj = []
        if(Array.isArray(this.topics)){
            this.topics.forEach(topic=>{
                pauseObj.push({
                    topic
                })
            })
        }else pauseObj.push({topic:this.topics})
        this.consumerPaused = true;
        this.consumer.pause(pauseObj)
    }

    _resumeConsumer(){
        let pauseObj = []
        if(Array.isArray(this.topics)){
            this.topics.forEach(topic=>{
                pauseObj.push({
                    topic
                })
            })
        }else pauseObj.push({topic:this.topics})
        this.consumerPaused = false;
        this.consumer.resume(pauseObj)
    }

    /**
     * Function to create a kafka consumer
     * @param { function } onMessage Consumer function to process messages
     * @param { function } [cb] callback function
     */
    initConsumer(onMessage  , cb ) {
        let self =  this;
        let serviceName =  self.serviceName;
        let deferred = Q.defer();
      
        self.Kafka = new Kafka({
            "brokers": self.options.kafkaHost.split(","),
            "clientId": self.options.clientId,
        });

        L.info("kafka consumer batchsize in bytes", self.options.maxBytes);

        self.consumer = self.Kafka.consumer({
            groupId: self.options.groupId,
            sessionTimeout:this.options.sessionTimeout,
            maxBytes: self.options.maxBytes
        });

        
        this._validateCreateConsumer((error) => {
            
            try {
                if (error) {
                    L.error('initConsumer: ', error);
                    this._prepareResponse(cb, deferred, error, null);
                } else {
                    self.consumer.connect()
                    .then(() => {
                        let topics = self.topics
                        if(Array.isArray(topics)){
                            return Promise.all(topics.map(ele => {
                                L.info("subscribing to array of topic ", ele)
                                return self.consumer.subscribe({
                                    "topic": ele,
                                    "fromBeginning": true
                                })
                            }))
                        }
                        
                        L.info("subscribing to ",topics)
                        return self.consumer.subscribe({
                            "topic": topics,
                            "fromBeginning": true
                        })
                    })
                    .then(() => {
                        this.consumer.run({
                            eachBatchAutoResolve: false,
                            autoCommitThreshold: 1,
                            eachBatch: async ({
                                batch,
                                resolveOffset,
                                heartbeat,
                                commitOffsetsIfNecessary                
                            }) => {
                                L.info(`Recieved batch for ${serviceName} : ${process.pid} : ${batch.topic}_${batch.partition}`)
                                let maxProcessTimeout = self.options.maxProcessTimeout;
                                L.log('maxProcessTimeout configured as ', maxProcessTimeout);
                                let heartBeatInterval = self.heartBeatInterval;
                                let maxHeartBeats = Math.round(maxProcessTimeout / heartBeatInterval)
                                let n = 0;
                                let consumerHeartbeat = setInterval(async function() {
                                    try{
                                        if(n >  maxHeartBeats){
                                            L.log('heartbeat cleared after maxTimeout of ', maxProcessTimeout);
                                            clearInterval(consumerHeartbeat);
                                            return;
                                        }
                                        n++;
                                        await heartbeat();
                                        L.log(serviceName  ,' Consumer sent heartbeat...');
                                    }catch(err){
                                        _sendMetricsToDD(1 , ["REQUEST_TYPE:"+serviceName,"TYPE:ERROR_WHILE_HB","STATUS:CONSUMER_ERROR"])
                                        L.error(`${serviceName} :: Error while sending heartbeat:`,err);
                                    }
                                }, 2000);
                                                            
                                try {

                                    self.consumer.pause([{topic:batch.topic, partitions:[batch.partition]}]);

                                    await new Promise((resolve, reject) => {
                                        onMessage(batch.messages, resolveOffset, batch.topic,batch.partition,resolve);
                                    });


                                    if(self.consumerPaused!=true)self.consumer.resume([{topic:batch.topic, partitions:[batch.partition]}])
        
                                   

                                    await commitOffsetsIfNecessary();

                                    L.info(`Completed batch for ${serviceName} : ${process.pid} : ${batch.topic}_${batch.partition}`)
                                    _sendMetricsToDD(_.get(batch,'messages.length',0) , ["REQUEST_TYPE:"
                                    +serviceName,"TYPE:CONSUMED","PID:"+process.pid])

                                } catch (err) {
                                    L.error(serviceName, 'Error occured while processing data : ', err);
                                    _sendMetricsToDD(1 , ["REQUEST_TYPE:"
                                    +serviceName,"TYPE:ERROR_WHILE_PROCESSING","STATUS:CONSUMER_ERROR"])
                                }
                                clearInterval(consumerHeartbeat);
                                L.log(serviceName,'setInterval(consumerHeartbeat) cleared...');

                                
                            },
                            
                        })
                    }).catch(err => {
                        L.error(serviceName , ' :: initializeKafkaConsumer', 'Error occured while consuming data : ', err);
                        this._prepareResponse(cb, deferred, err, null);
                    });
                    this._prepareResponse(cb, deferred, null, 'consumer initiated!');
                }
            } catch(error) {
                this._prepareResponse(cb, deferred, error, null);
            }
        });

        return deferred.promise;
    }

   
  

  

    /**
     * Close a consumer
     * { function } [cb] Callback function.
     */
    close(cb) {
        let deferred = Q.defer();
            this.consumer.disconnect().then((data) => {
                console.log("REached here",data)
                this._prepareResponse(cb, deferred, null, CONFIG.MESSAGES.CONSUMER_CLOSED);
            }).catch(err=>{
                this._prepareResponse(cb, deferred, err, CONFIG.MESSAGES.CONSUMER_CLOSED);
            });
        return deferred.promise;
    }
}

module.exports = KafkaConsumer;