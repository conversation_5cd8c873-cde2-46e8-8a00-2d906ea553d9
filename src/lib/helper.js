const util = require('util');

let HelperLib = {
    isString: function (value) {
        return typeof value === 'string';
    },
    isFunction: function (value) {
        return typeof value === 'function';
    },
    isNumber: function (value) {
        return typeof value === 'number';
    },
    list: function (input) {
        var returnList = [];
        if (input && HelperLib.isString(input) && input.length) {
            input.split(',').forEach(function (eachEl) {
                returnList.push(eachEl.trim());
            });
        }
        return returnList;
    },
    listBySeparator: function (splitBy = ',',input) {
        var returnList = [];
        if (input && HelperLib.isString(input) && input.length) {
            input.split(splitBy).forEach(function (eachEl) {
                returnList.push(eachEl.trim());
            });
        }
        return returnList;
    },
    num_list: function (val) {
        var numList = [];
        if (HelperLib.isString(val)) {
            var stringArray = val.split(',');
            stringArray.forEach(function (str) {
                var num = Number(str);
                if (isNaN(num)) {
                    throw new Error('Unable to parse ' + str);
                } else {
                    numList.push(num);
                }
            });
        } else if (HelperLib.isNumber(val)) {
            numList.push(val);
        } else if (util.isArray(val)) {
            val.forEach(function (str) {
                var num = Number(str);
                if (isNaN(num)) {
                    throw new Error('Unable to parse ' + str);
                } else {
                    numList.push(num);
                }
            });
        }
        if (!numList.length) {
            throw new Error('Unable to parse ' + val);
        }
        return numList;
    },
    getParsedJSON: function (val){
        try {
            let parseValue = JSON.parse(val);
            return parseValue;    
        } catch (error) {
            throw new Error('Unable to parse stringified json' + val);   
        }
    }
};

module.exports = HelperLib;
