"use strict";

import _ from 'lodash'

class PublisherBillFetchPattern {

    constructor(options) {
        this.toBeProcessedRecordNumber = 0;
        this.processingRecordHashMapCounter = {
        };
        this.records = [];
    }

    reInitializeBillFetchVars(records) {
        this.processingRecordHashMapCounter = {};
        this.toBeProcessedRecordNumber = 0;
        this.records = records;
    }
    getToBeProcessedRecordNumber(){
        return this.toBeProcessedRecordNumber;
    }
    getCurrentRecordAndUpdateToBeProcessedRecordNumber(){
        return this.records[this.toBeProcessedRecordNumber++];
    }
}

export default PublisherBillFetchPattern;