import L from 'lgr'

/* NPM Third Party */
import _ from 'lodash'
import Q from 'q'

/* NPM Paytm*/
import S<PERSON><PERSON><PERSON> from 'sqlwrap'
import <PERSON>LA<PERSON><PERSON><PERSON>ARCH from 'elasticsearch'
import INFRAUTILS from 'infra-utils'

/* Project Files */
import RULE_SERVICES from '../services/ruleEngine'
import CVR_SERVICE from '../services/cvrDataLoader'
import LOCALISATION_CLIENT from 'localisation-client'
import LIB from '.'
import utility from '../lib'
import RECHARGE_CONFIG_LOADER from 'recharge-config'
import REMINDER_CONFIG_LOADER from '../config'
import cassandra from 'cassandra-driver'

let dbInstance, esInstance, mongoDbInstance, cassandraDbClient, notificationNewClusterClient, options, internalCustIdMap,
    config = null,
    greyScaleEnv = _.get(process, 'env.GREYSCALE', 0) == 1 ? true : false;

process.on('uncaughtException', function(error){
    utility._sendMetricsToDD(1, [
        'STATUS:CRITICAL',
        'TYPE:UNCAUGHT_EXCEPTION',
        `REQUEST_TYPE:PROCESS`,
        `MESSAGE:${error}`
    ]);
    console.error('CRITICAL!','Found uncaught error in flow:',error);
});

function init(PROGRAM, cb) {
    let opts = {};
    Q(undefined)
        .then(function () {
            // Loading reminderConfig, required for all services which use startupLib
            L.log('Loading digital reminder config');
            return REMINDER_CONFIG_LOADER();
        })
        .then(function (digitalReminderConfig) {
            config = digitalReminderConfig;

            opts.exclude = getExcludeConfig(PROGRAM, config);
            // Loading recharge-config
            if (opts.exclude && opts.exclude.rechargeConfigVault) {
                L.log('startup skipping recharge-config loader');
                return Q(null);
            }
            L.log('startup Loading rechargeConfig from Vault');
            return RECHARGE_CONFIG_LOADER();
        })
        .then(function (rechargeConfig) {
            /* Global Variables */
            dbInstance = new SQLWRAP(config.SQLWRAP);
            esInstance = new ELASTICSEARCH.Client({
                apiVersion: '0.90',
                host: config.ELASTICSEARCH.ES_URL_NEW,
                log: 'error'
            });
            
            options = {
                L,
                rechargeConfig,
                config,
                dbInstance,
                esInstance,
                mongoDbInstance,
                INFRAUTILS,
                LOCALISATION_CLIENT,
                cassandraDbClient,
                greyScaleEnv,
                notificationNewClusterClient,
                internalCustIdMap
                
            };

            // mongoDbInstance = new INFRAUTILS.mongo(config.MONGO.MASTER);

            if (opts.exclude && opts.exclude.ruleEngine) {
                L.log('startup skipping ruleEngine');
                return Q();
            }
            L.log('startup Loading ruleEngine');
            let ruleEngineInstance = new RULE_SERVICES(options);
            return ruleEngineInstance.fetchOperatorGwMapFromRuleEngine(options);
        })
        .then(function () {
            if (opts.exclude && opts.exclude.cvr) {
                L.log('startup skipping cvr');
                return Q();
            }
            L.log('startup Loading cvr');
            let cvrDataLoader = new CVR_SERVICE(options);
            return cvrDataLoader.start();
        })
        .then(function() {
            if (opts.exclude && opts.exclude.cassandraDb) {
                L.log('startup skipping cassandra');
                return Q();
            }
            L.log('startup loading cassandra DB');
            let cassandraConfig = _.get(config, 'CASSANDRA.REMINDER_CASSANDRA', {});
            _.set(cassandraConfig, 'keyspace', config.CASSANDRA.reminder_keyspace);
            options.cassandraDbClient = new cassandra.Client(config.CASSANDRA.REMINDER_CASSANDRA);
            _.set(cassandraConfig, 'keyspace', config.CASSANDRA.notification_keyspace);
            options.notificationCassandraDbClient = new cassandra.Client(config.CASSANDRA.REMINDER_CASSANDRA);
            return Q();
        })
        .then(function() {
            if (opts.exclude && opts.exclude.nonruCassandraDb) {
                L.log('startup skipping nonrucassandra');
                return Q();
            }
            L.log('startup loading nonrucassandra DB');
            let cassandraNewConfig = _.get(config, 'CASSANDRA.REMINDER_NEW_CASSANDRA', {});
            _.set(cassandraNewConfig, 'keyspace', config.CASSANDRA.nonru_keyspace);
            options.nonRuCassandraDbClient = new cassandra.Client(cassandraNewConfig);
            L.log('successfully loaded nonrucassandra DB');
            return Q();
        })
        .then(function() {
            if(opts.exclude && opts.exclude.rechargeSagaCassandraDb){
                L.log('startup skipping rechargeSagaCassandraDb');
                return Q();
            }
            L.log('startup loading rechargeSagaCassandraDb');
            options.rechargeSagaCassandraDb = new cassandra.Client(config.CASSANDRA.RECHARGE_SAGA_CASSANDRA);
            let recentKeyspace = config.CASSANDRA.recent_keyspace;
            let cassConfig = _.get(config, 'CASSANDRA.RECHARGE_SAGA_CASSANDRA', {});
            _.set(cassConfig, 'keyspace', recentKeyspace);
            options.rechargeSagaCassandraDbRecentKeySpace = new cassandra.Client(cassConfig);

            // console.log('rechargeSagaCassandraDb connected: ', options.rechargeSagaCassandraDb);
            return Q();
        })
        .then(function() {
            if(opts.exclude && opts.exclude.notificationNewCluster){
                L.log('startup skipping notificationNewCluster');
                return Q();
            }
            L.log('startup loading notificationNewCluster');
            let cassandraConfig = _.get(config, 'NOTIFICATION_CASSANDRA.NOTIFICATION_CASSANDRA', {});

            options.notificationNewClusterClient = new cassandra.Client(cassandraConfig);
            return Q();
        })
        .then(function () {
            if (opts.exclude && opts.exclude.activePidLib) {
                L.log('startup skipping activePidLib');
                return Q();
            }
            L.log('startup Loading activePidLib');
            options.activePidLib = new LIB.ActivePID(options);
            return options.activePidLib.load();
        })
        // .then(function () {
        //     if (opts.exclude && opts.exclude.mongoDb) {
        //         L.log('startup skipping mongoDb');
        //         return Q();
        //     }
        //     L.log('startup Loading mongoDb');

        //     let deferred = Q.defer();
        //     options.mongoDbInstance.connect(function (err) {
        //         if (err) {
        //             deferred.reject(new Error(`mongo not initialized ${err}`));
        //         } else {
        //             L.log('startup mongoDb Connected!');
        //         }
        //         deferred.resolve(options);
        //     });
        //     return deferred.promise;
        // })
        .then(function () {
            if (opts.exclude && opts.exclude.dynamicConfig) {
                L.log('startup skipping dynamicConfig');
                return Q();
            }
            L.log('startup Loading dynamicConfig');
            let dynamicConfigLib = new LIB.DigitalReminderConfigLib(options);
            return dynamicConfigLib.load();
        })
        .then(function () {
            if (opts.exclude && opts.exclude.loadInternalCustIdLib) {
                L.log('startup skipping loadInternalCustIdLib');
                return Q();
            }
            L.log('startup Loading loadInternalCustIdLib');
            let internalCustIdLib = new LIB.LoadInternalCustIdLib(options);
            return internalCustIdLib.load();
        })
        .then(function () {
            if (opts.exclude && opts.exclude.serviceConfig) {
                L.log('startup skipping serviceConfig');
                return Q();
            }
            L.log('startup Loading serviceConfig');
            let serviceConfigLib = new LIB.FetchServiceConfig(options);
            return serviceConfigLib.load();
        })
        .then(function () {
            L.log('statup init success');
            return cb(null, options);
        }, function (err) {
            return cb(new Error(`Startup not initialized: ${err}`));
        });
}

function getExcludeConfig(PROGRAM, config) {

    let excludeConfig = _.get(config, 'COMMON.EXCLUDE_STARTUP_CONFIG', {}),
        finalExcludeConfig;

    Object.keys(excludeConfig).forEach(function (optionKey) {

        let selectedOption = _.get(PROGRAM, optionKey, false);
        if (selectedOption === true || (_.isArray(selectedOption) && selectedOption.length > 0)) {
            finalExcludeConfig = _.get(excludeConfig, optionKey, {});
            L.log('getExcludeConfig', 'optionKey -', optionKey);
        }
    });

    finalExcludeConfig = finalExcludeConfig || _.get(config, 'COMMON.EXCLUDE_STARTUP_CONFIG.DEFAULT', {});
    L.log('getExcludeConfig', 'startup exclude config -', finalExcludeConfig);

    return finalExcludeConfig;
}

export default {
    init: init
};

(function () {
    if (require.main === module) {
        L.setLevel('verbose');
        console.time('Execution Time');

        init({}, function (err, options) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();