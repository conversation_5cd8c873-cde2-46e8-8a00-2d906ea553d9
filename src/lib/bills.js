"use strict";

import _ from 'lodash'
import MOMENT from 'moment'
import B<PERSON><PERSON> from '../models/bills';
import utility from '../lib';
import <PERSON><PERSON><PERSON><PERSON>OR from 'validator';
import Logger from './logger';
import EncryptionDecryptioinHelper from './EncryptionDecryptioinHelper';

/* 
 * This class contains helper functions that are being used in
 * billSubscriber services
*/

class BillsLibrary {

    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.logger = new Logger(options);
        this.encryptionDecryptionHelper = new EncryptionDecryptioinHelper(options);
    }
    getEarlyBillFetchResultParams(params) {
        let self = this,
            billDueDate = params.billDueDate,
            currentAutomaticStatus = params.currentAutomaticStatus,
            earliestBillFetchDuration = self.getConfigByKeys({
                dynamicConfig:false, 
                name:'SUBSCRIBER_CONFIG', 
                node: 'EARLIEST_BILL_FETCH_DELAY', 
                keyname: 'DURATION', 
                default: 1}, {
                    currentAutomaticStatus: currentAutomaticStatus,
                    prefix:'AUTOPAY_',
                    prefixToKey: 'node'
                }),
            
            earliestBillFetchUnit = self.getConfigByKeys({
                dynamicConfig:false, 
                name:'SUBSCRIBER_CONFIG',
                node:'EARLIEST_BILL_FETCH_DELAY', 
                keyname:'UNIT', 
                default:'month'}, {
                    currentAutomaticStatus: currentAutomaticStatus,
                    prefix:'AUTOPAY_',
                    prefixToKey: 'node'
                })

        let earlyBillFetchResult = {};
        earlyBillFetchResult.nextBillFetchDate = billDueDate.subtract(earliestBillFetchDuration, earliestBillFetchUnit);
        earlyBillFetchResult.status = _.get(self.config, 'COMMON.bills_status.EARLY_BILL_FETCH', 16);
        
        return earlyBillFetchResult;
    }
    isEarlyBillFetch(params) {
        let self = this,
            billDueDate = (params.billDueDate ? MOMENT(params.billDueDate , 'YYYY-MM-DD').startOf('day') : null),
            lastDueDate = (params.lastDueDate ? MOMENT(params.lastDueDate , 'YYYY-MM-DD').startOf('day') : null),
            earliestBillFetchDuration = _.get(self.config, ['SUBSCRIBER_CONFIG', 'EARLIEST_BILL_FETCH_DELAY', 'DURATION'], 1 ),
            earliestBillFetchUnit = _.get(self.config, ['SUBSCRIBER_CONFIG', 'EARLIEST_BILL_FETCH_DELAY', 'UNIT'], 'month' );

        let isEarlyBillFetchFlag = false;
        if( billDueDate && billDueDate.diff(MOMENT(), earliestBillFetchUnit) >= earliestBillFetchDuration) {
            if ( ( lastDueDate === null ) || ( lastDueDate && billDueDate.diff(lastDueDate) > 0) ) {
                isEarlyBillFetchFlag = true;
            }
        } 
        return isEarlyBillFetchFlag;
    }

    /**
         * Returns active users for which smsparsing data will be updated
         * @param {*} dbRecords 
         */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }

    getSortedDbData(dbRecords) {
        dbRecords.sort(function (record1, record2) {
            let isValidR1date = MOMENT(record1.due_date).isValid(false);
            let isValidR2date = MOMENT(record2.due_date).isValid(false);

            if (isValidR1date && isValidR2date) {
                let daysDiff = MOMENT(record1.due_date).diff(record2.due_date, 'days');
                if (daysDiff > 0) return -1;
                else return 1;
            } else if (isValidR1date) {
                return -1;
            } else if (isValidR2date) {
                return 1;
            } else {
                return 0;
            }
        });

        return dbRecords;
    }

    processRecordForDuplicateCANUmber(record, cb) {
        let self = this,
            clonedRecord = _.cloneDeep(record);
        if (_.get(self.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR', _.toLower(record.operator), 'PREFIX'], "N.A") != "N.A") {
            let prefix = _.get(self.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR', _.toLower(record.operator), 'PREFIX'], "N.A");
            _.set(record, 'isDuplicateCANumberOperator', true);
            _.set(record, 'rechargeNumberWithoutPrefix', record.rechargeNumber.startsWith(prefix) ? record.rechargeNumber.substring(prefix.length) : record.rechargeNumber);
            _.set(record, 'rechargeNumberWithPrefix', record.rechargeNumber.startsWith(prefix) ? record.rechargeNumber : prefix + record.rechargeNumber);
            _.set(record, 'alternateRechargeNumber', record.rechargeNumber.startsWith(prefix) ? record.rechargeNumber.substring(prefix.length) : prefix + record.rechargeNumber);
        }
        return cb(null);
    }

    checkIfRecordOfSameCustId(data, record) {
        if (record.isDuplicateCANumberOperator) {
            let alternateCAExistsInDB = data.filter((dataValue) => dataValue.customer_id == record.customerId && dataValue.recharge_number == record.alternateRechargeNumber).length > 0 ? true : false;
            _.set(record, 'alternateCAExistsInDB', alternateCAExistsInDB);
            data = data.filter((dataValue) => dataValue.recharge_number == record.rechargeNumber)
        }
        return data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;
    }

    removeRecordsWithPrefixForCustomersHavingBothRNs(data, record){
        const customersWithBoth = new Set();
        const prefixMap = new Map();

        data.forEach(item => {
            const custId = item.customer_id;
            
            if (!prefixMap.has(custId)) {
                prefixMap.set(custId, { hasPrefix: false, hasNonPrefix: false });
            }
            
            if (item.recharge_number === record.rechargeNumberWithPrefix) {
                prefixMap.get(custId).hasPrefix = true;
            } else if (item.recharge_number === record.rechargeNumberWithoutPrefix) {
                prefixMap.get(custId).hasNonPrefix = true;
            }
            
            if (prefixMap.get(custId).hasPrefix && prefixMap.get(custId).hasNonPrefix) {
                customersWithBoth.add(custId);
            }
        });
        
        //removing records with prefix for customers having both recharge numbers
        data = data.filter(item => !(
            customersWithBoth.has(item.customer_id) &&
            item.recharge_number === record.rechargeNumberWithPrefix
        ));
        return data;
    }

    getRecordsFromDb(done, record) {
        let self = this;

        self.bills.getBillsOfSameRech((err, data) => {
            if (err) {
                self.L.error('getRecordFromDb::Error while fetching data from DB');
                return done(err, false);
            }
            if (!data || !_.isArray(data) || data.length < 1) return done(null, false);

            let maxPaymentDate = data.reduce((prev, current) => {
                if (!current.payment_date || !MOMENT(current.payment_date).isValid()) {
                    return prev;
                }
                if (!prev.payment_date || !MOMENT(prev.payment_date).isValid()) {
                    return current;
                }
                return MOMENT(prev.payment_date).isSameOrAfter(MOMENT(current.payment_date)) ? prev : current;
            }, {});
            
            let maxBillDate = data.reduce((prev, current) => {
                if (!current.bill_date || !MOMENT(current.bill_date).isValid()) {
                    return prev;
                }
                if (!prev.bill_date || !MOMENT(prev.bill_date).isValid()) {
                    return current;
                }
                return MOMENT(prev.bill_date).isSameOrAfter(MOMENT(current.bill_date)) ? prev : current;
            }, {});

            let recordOfSameCustId = self.checkIfRecordOfSameCustId(data, record);
            //let recordOfSameCustId = data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;

            if (record.isDuplicateCANumberOperator) {
                data = self.removeRecordsWithPrefixForCustomersHavingBothRNs(data, record);
            }

            _.set(record, 'maxBillDate', _.get(maxBillDate, 'bill_date', null));
            _.set(record, 'maxPaymentDate', _.get(maxPaymentDate, 'payment_date', null));
            _.set(record, 'noOfFoundRecord', data.length);
            _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
            _.set(record, 'is_automatic', data[0].is_automatic);
            _.set(record, 'dbData', self.getSortedDbData(data));
            _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
            _.set(record, 'isRecordExist', true);

            return done(null, true);
        }, record.tableName, record);
    }

    //to decide for RU and NonRu
    getForwardActionFlow(done, processedRecord) {
        let self = this;
        self.L.log('bills:getForwardActionFlow:: starting getForwardActionFlow');
        self.getRecordsFromDb((err, recordsFound) => {
            if (err) {
                self.L.error(`lib:bills: Error in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:UPMS_BILL_UPDATE',
                    `SERVICE:${_.get(processedRecord, 'service', null)}`,
                    `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                    'STATUS:ERROR',
                    'TYPE:ERROR_GETTING_RECORD_FROM_DB',
                    'SOURCE:getForwardActionFlow'
                ]);
                return done({ message: err, errorCode: "5XX" });
            } else {
                if (recordsFound) {
                    self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound :`, `for the processedRecord: ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:UPMS_BILL_UPDATE',
                        `SERVICE:${_.get(processedRecord, 'service', null)}`,
                        'STATUS:SUCCESS',
                        `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                        'TYPE:RECORD_FOUND_IN_DB',
                        'SOURCE:getForwardActionFlow'
                    ]);

                    let maxPaymentDate = _.get(processedRecord, 'maxPaymentDate', null),
                        maxBillDate = _.get(processedRecord, 'maxBillDate', null),
                        dbRecord = _.get(processedRecord, 'dbData', {}),
                        dbDueDate = _.get(dbRecord, '[0].due_date', null) ? MOMENT(dbRecord[0].due_date).startOf('day') : null,
                        curDueDate = _.get(processedRecord, 'billDueDate', null) ? MOMENT(processedRecord.billDueDate).startOf('day') : null;
                    dbDueDate = dbDueDate ? MOMENT(dbDueDate).format('YYYY-MM-DD HH:mm:ss') : null;

                    if (dbDueDate && curDueDate && MOMENT(curDueDate).isSameOrBefore(MOMENT(dbDueDate)) && maxPaymentDate && maxBillDate && MOMENT(maxPaymentDate) > MOMENT(maxBillDate)) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPMS_BILL_UPDATE", 'STATUS:ERROR', 'TYPE:COMPARE_AND_UPDATE_DATA', 'REASON:PAYMENT_DONE']);
                        self.L.log('getForwardActionFlow:: Payment Event already processed for: ', processedRecord.debugKey);
                        return done({ message: "payment already done", errorCode: "2XX" });
                    }

                    if (_.isNull(processedRecord.billDueDate) || _.isNull(processedRecord.amount)) {
                        self.L.log(`processRecord:: DueDate or Amount found as null, so skipping this record ${processedRecord.debugKey}`);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:UPMS_BILL_UPDATE',
                            `SERVICE:${_.get(processedRecord, 'service', null)}`,
                            'STATUS:ERROR',
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:DUE_DATE_AMOUNT_NULL',
                            'SOURCE:getForwardActionFlow'
                        ]);
                        //if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), `DueDate or Amount found as null`, done);
                        return done({ message: 'DueDate or Amount found as null', errorCode: "2XX" });
                    }

                    let amountDiff = Math.abs(processedRecord.amount - _.get(dbRecord, '[0].amount', 0)),
                        dueDateDiff = MOMENT(processedRecord.billDueDate).startOf('day').diff(MOMENT(dbDueDate).startOf('day'), 'days'),
                        dbStatus = _.get(dbRecord, '[0].status', null), dbExtra = null;

                    if(dueDateDiff === 0){
                        _.set(processedRecord, 'skipNotificationWhenUPMSBillAlreadyinDB', true);
                        self.L.log(`getForwardActionFlow:: Skipping notification since UPMS bill already in DB with same due date for: ${processedRecord.debugKey}`);
                    }
                    try {
                        dbExtra = JSON.parse(_.get(dbRecord, '[0].extra', {}));
                        if (!dbExtra) dbExtra = {};
                    } catch (err) {
                        self.L.error("getBillsData", "Error in JSON parsing" + err);
                    }
                    if (_.get(dbExtra, 'isPrepaid', null) === true || _.get(dbExtra, 'isPrepaid', null) === 1 || _.get(dbExtra, 'isPrepaid', null) === "1" || _.get(dbExtra, 'isPrepaid', null) === '1') {
                        return done({ message: 'found in prepaid table', errorCode: "2XX" }); 
                    }
                    else if (_.get(dbExtra, 'billSource', null) == 'sms_parsed' && (dueDateDiff || amountDiff)) {
                        return done(null, 'update', processedRecord);
                    }
                    if (MOMENT(processedRecord.billDueDate).isValid() && dueDateDiff < 0) {
                        self.L.log(`lib:bills: getRecordsFromDb ,payloadDueDate:${MOMENT(processedRecord.billDueDate).utc().startOf('day').format('YYYY-MM-DD')}, dbDueDate: ${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}`);
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UPMS_BILL_UPDATE",
                            `SERVICE:${_.get(processedRecord, 'service', null)}`,
                            "STATUS:ERROR",
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:UPMS_DUE_DATE_LESS_THAN_DB_DUE_DATE',
                            'SOURCE:getForwardActionFlow'
                        ]);
                        //if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate == null || processedRecord.amount == null) ? "PARTIAL_BILL" : "FULL_BILL", "RU"), 'smsDueDate less than dbDueDate', done);
                        return done({ message: 'UPMS due date less than dbDueDate', errorCode: "2XX" });
                    } else if (_.get(dbExtra, 'billSource', null) == 'sms_parsed' && (dueDateDiff || amountDiff)) {
                        return done(null, 'update', processedRecord);
                    } else if (dbStatus == _.get(self.config, ['COMMON', 'bills_status', 'DISABLED'], 7) || dbStatus == _.get(self.config, ['COMMON', 'bills_status', 'NOT_IN_USE'], 13)) {
                        self.L.log(`lib:bills: getRecordsFromDb , dbStatus: ${dbStatus}`);
                        processedRecord.dueDate = dbDueDate;
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UPMS_BILL_UPDATE",
                            `SERVICE:${_.get(processedRecord, 'service', null)}`,
                            "STATUS:ERROR",
                            `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                            'TYPE:INACTIVE_RECORD',
                            'SOURCE:getForwardActionFlow'
                        ]);
                        return done({ message: 'inactive record in db', errorCode: "2XX" });
                    } else {
                        return done(null, 'update', processedRecord);
                    }
                } else {
                    self.L.log(`processRecord:: No recordsFound in DB for the processedRecord: ${processedRecord.debugKey}`);
                    return done(null, 'findAndUpdateToCassandra', processedRecord);
                }
            }
        }, processedRecord);
    }

    createErrorMessage(error) {
        let failureReason = null;
        if (error && error.message && error.stack)
            failureReason = JSON.stringify(error, this.replaceError);
        else {
            try {
                failureReason = JSON.stringify(error);
            }
            catch (err) {
                failureReason = error;
            }

        }
        return failureReason;
    }
    replaceError(key, value) {
        if (value instanceof Error) {
            const newValue = Object.getOwnPropertyNames(value)
                .reduce((obj, propName) => {
                    obj[propName] = value[propName];
                    return obj;
                }, { name: value.name });
            return newValue;
        } else {
            return value;
        }
    }

    getConfigByKeys(configObject, extraVariableObject){
        let self=this,
        currentAutomaticStatus = _.get(extraVariableObject, 'currentAutomaticStatus', null),
        prefix = _.get(extraVariableObject, 'prefix', ''),
        prefixToKey = _.get(extraVariableObject, 'prefixToKey', null);

        let dynamicConfig = _.get(configObject, 'dynamicConfig', false),
        name = _.get(configObject, 'name', null),
        node = _.get(configObject, 'node', null),
        nodeWithPrefix = prefixToKey=='node'? prefix+node : node,
        keyname = _.get(configObject, 'keyname', null),
        keynameWithPrefix = prefixToKey=='keyname'? prefix+keyname : keyname,
        defaultValue = _.get(configObject, 'default', null);



        if(currentAutomaticStatus){
            if(dynamicConfig){
                return _.get(self.config, ['DYNAMIC_CONFIG',name, nodeWithPrefix, keynameWithPrefix], 
                        _.get(self.config, ['DYNAMIC_CONFIG',name,node,keyname], defaultValue))
            }else{
                return _.get(self.config, [name, nodeWithPrefix, keynameWithPrefix], 
                        _.get(self.config, [name, node, keyname], defaultValue))
            }
        }else{
            if(dynamicConfig){
                return _.get(self.config, ['DYNAMIC_CONFIG',name, node, keyname], defaultValue)
            }else{
                return _.get(self.config, [name, node, keyname], defaultValue)
            }
        }
    }

    getHighestPublishedDateAmongestRows(rows){
        let self = this;
        try{
            let highestPublishedDate = _.maxBy(rows, (row) => {
                return MOMENT(_.get(row, 'published_date', '1970-01-01 00:00:00'), 'YYYY-MM-DD HH:mm:ss');
            });
            return MOMENT(_.get(highestPublishedDate, 'published_date', '1970-01-01 00:00:00'), 'YYYY-MM-DD HH:mm:ss');
        }catch(e){
            self.L.error('getHighestPublishedDateAmongestRows:: Error while getting highest published date:', e);
            return null;
        }
    }

    getHighestPriorityAmongestRows(rows){
        let self = this;
        try{
            let service = _.toLower(_.get(rows, '[0].service', '')).replace(/\s/g, ""),
            paytype = _.toLower(_.get(rows, '[0].paytype', '')).replace(/\s/g, "");
            let servicePaytype = `${service}_${paytype}`;

            let serviceWiseBucketConfig = _.cloneDeep(_.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'USER_SCORE_BUCKET_CONFIG', `${servicePaytype}`], null));
            if(serviceWiseBucketConfig){
                let buckets = _.get(serviceWiseBucketConfig, 'buckets', []);
                self.L.log('getHighestPriorityAmongestRows:: buckets:', buckets);
                if(_.isArray(buckets) && buckets.length > 0){
                    //get the bucket from each row, check index of bucket in buckets array, return the highest index
                    let highestPriorityRecord = _.maxBy(rows, (row) => {
                        let bucket = _.get(row, 'customer_bucket', null);
                        return _.indexOf(buckets, bucket);
                    });
                    this.logger.log('getHighestPriorityAmongestRows:: highestPriorityRecord:', highestPriorityRecord, _.get(highestPriorityRecord, 'service', null));
                    return _.get(highestPriorityRecord,'customer_bucket', null);
                }
            }else{
                self.L.log('getHighestPriorityAmongestRows:: No serviceWiseBucketConfig found for:', `${servicePaytype}`);
                return null;
            }
        }catch(e){
            self.L.error('getHighestPriorityAmongestRows:: Error while getting highest priority:', e);
            return null;
        }
    }

    updateRecordWithOffsetNbfd(billsData, customer_bucket, published_date){
        let self = this;
        try{
            let
            service = _.toLower(_.get(billsData, 'service', '').replace(/\s/g, "")),
            paytype = _.toLower(_.get(billsData, 'paytype', '').replace(/\s/g, "")),
            debugKey = `${_.get(billsData, 'rechargeNumber', '')}_${_.get(billsData, 'customerId', '')}_${service}_${paytype}`;

            if(_.toLower(_.get(billsData, 'paytype', '')) == 'credit card') {
                debugKey = `${this.encryptionDecryptionHelper.encryptData(_.get(billsData, 'rechargeNumber', ''))}_${_.get(billsData, 'customerId', '')}_${service}_${paytype}`;
            }

            let extra = _.get(billsData, 'extra', {});
            if(typeof extra == 'string'){
                extra = JSON.parse(extra);
            }

            /*
                name: USER_SCORE_INGESTOR
                node:USER_SCORE_BUCKET_CONFIG
                key_name:mobile_prepaid
                value: {
                    "scores": [10,30,50], 
                    "buckets": ['silver','gold','diamond'], 
                    "nbfdHours":[-24,-48,-72],
                    "thresholdScore":0
                }
            */ 
            let serviceWiseBucketConfig = _.cloneDeep(_.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'USER_SCORE_BUCKET_CONFIG', `${service}_${paytype}`], null));
            if(serviceWiseBucketConfig && customer_bucket){
                let buckets = _.get(serviceWiseBucketConfig, 'buckets',[])
                self.L.log('updateRecordWithOffsetNbfd:: buckets:', buckets, 'debugKey:', debugKey);
                if(_.isArray(buckets) && buckets.length>0){
                    let currentBucketIndex = _.indexOf(buckets, customer_bucket);
                    let nbfdHours = _.get(serviceWiseBucketConfig, 'nbfdHours', []);
                    if(_.isArray(nbfdHours) && nbfdHours.length > 0 && buckets.length == nbfdHours.length){
                        let nbfdHour = _.get(nbfdHours, currentBucketIndex, 0);
                        self.L.log('updateRecordWithOffsetNbfd:: nbfdHour:', nbfdHour, 'debugKey:', debugKey);
                        let nbfd = _.get(billsData, 'nextBillFetchDate', null);
                        let actual_nbfd = _.get(billsData, 'nextBillFetchDate', null);
                        if(nbfd){
                            nbfd = MOMENT(nbfd, 'YYYY-MM-DD HH:mm:ss').add(nbfdHour, 'hours');

                            if(MOMENT(nbfd).diff(MOMENT().startOf('day'),'days')<=0 && MOMENT(actual_nbfd).diff(MOMENT().startOf('day'),'days') == 0){
                                nbfd = _.get(billsData, 'nextBillFetchDate', null);
                            }else if (MOMENT(nbfd).diff(MOMENT().startOf('day'),'days')<=0 && MOMENT(published_date).diff(MOMENT().startOf('day'),'days') == 0){
                                nbfd = MOMENT().startOf('day').add(1, 'days');
                            } 
                            else if (MOMENT(nbfd).diff(MOMENT().startOf('day'),'days')<0){
                                nbfd = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                            }else{
                                nbfd = MOMENT(nbfd).format('YYYY-MM-DD HH:mm:ss');
                            }

                            self.L.log('updateRecordWithOffsetNbfd:: nbfd:', nbfd, 'actualNbfd:',actual_nbfd ,'debugKey:', debugKey);
                            nbfd = MOMENT(nbfd).format('YYYY-MM-DD HH:mm:ss');
                            _.set(billsData, 'nextBillFetchDate', nbfd);
                            _.set(extra, 'user_score_actual_nbfd', actual_nbfd);
                            _.set(extra, 'user_score_nbfd_updatedAt', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
                            _.set(billsData, 'extra', JSON.stringify(extra));
                        }
                    }else{
                        self.L.log('updateRecordWithOffsetNbfd:: nbfdHours not found for:', `${service}_${paytype}`);
                    }
                }else{
                    self.L.log('updateRecordWithOffsetNbfd:: No buckets found for:', `${service}_${paytype}`);
                }
            }else{
                self.L.log('updateRecordWithOffsetNbfd:: No serviceWiseBucketConfig found or customer_bucket is null for:', `${service}_${paytype}`);
            }
        }catch(e){
            self.L.error('updateRecordWithOffsetNbfd:: Error while updating record with offset nbfd:', e);
        }

        return billsData;
    }

    isRUOperator(processedRecord, source) {
        let self = this;
        let operator = _.get(processedRecord, 'operator', null);
        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null);
        if (_.get(processedRecord, 'demergerOperatorsList', null) != null && _.isArray(processedRecord.demergerOperatorsList)) {
            return true;
        }
        
        _.set(processedRecord, 'tableName', tableName);
        if(!tableName){
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: ${source}`,
                `SERVICE:${_.get(processedRecord, 'service', null)}`,
                `OPERATOR:${_.get(processedRecord, 'operator', 'NO_OPERATOR')}`,
                'STATUS:ERROR',
                'TYPE:OPERATOR_NOT_ONBOARDED',
                'SOURCE:isRUoperator'
            ]);
            return false;
        } else {
            self.L.verbose(`isRUoperator :: Table found for operator ${operator} : ${tableName} for record: ${processedRecord.debugKey}`);
            return true;
        }
    }

    parseCustomerId(cId) {
        if (typeof cId === 'number') return cId;
        if (typeof cId === 'string' && VALIDATOR.isNumeric(cId)) return VALIDATOR.toInt(cId);
        return null;
    }

    getSmsBillDate(smsDateTime) {
        //if smsDateTime is not valid, return current date
        return MOMENT(smsDateTime).isValid() ? 
            MOMENT(smsDateTime).endOf('day').format('YYYY-MM-DD') : 
            MOMENT().endOf('day').format('YYYY-MM-DD');
    }
    
    generateDebugKey(record) {
        return `customerId:${record.customerId}::operator:${record.operator}::service:${record.service}::productId:${record.productId}`;
    }
        
    generateDummyRechargeNumber(record) {
        let self = this;

        // Input validation
        if (!record) {
            self.L.error('bills:generateDummyRechargeNumber', 'Record is null or undefined');
            throw new Error('Invalid record provided');
        }

        // Get customerId from various possible fields
        const customerId = this.extractCustomerId(record);
        const operator = _.get(record, 'operator');
        const debugKey = _.get(record, 'debugKey', 'UNKNOWN');

        // Validate required fields
        if (!customerId || !operator) {
            self.L.error('bills:generateDummyRechargeNumber',
                `Missing required fields - customerId: ${customerId}, operator: ${operator}, debugKey: ${debugKey}`);
            throw new Error('Missing required fields: customerId or operator');
        }

        // Format operator string - remove whitespace and replace with underscores
        const formattedOperator = operator.trim().replace(/\s+/g, '_');

        return `${customerId}_${formattedOperator}`;
    }

    extractCustomerId(record) {
        // Check all possible variations of customerId field
        const possibleFields = ['customerId', 'customerID', 'customerid'];
        

        for (const field of possibleFields) {
            const value = _.get(record, field);
            if (value) return value;
        }
        
        return null;
    }
    
}

export default BillsLibrary;
