import _ from 'lodash';

class KafkaPublisherFactory {
    /**
     * Creates a new KafkaPublisherFactory instance
     * @param {Object} config - The config object containing KAFKA.TOPICS
     * @param {Object} infraUtils - The infraUtils object containing kafka.producer
     * @param {Object} logger - Logger instance
     */
    constructor(config, infraUtils, logger) {
        this.config = config;
        this.infraUtils = infraUtils;
        this.logger = logger;
        this.publishers = new Map();
    }

    /**
     * Create or get a Kafka publisher for a given topic key
     * @param {string} topicKey - The key in config.KAFKA.TOPICS (e.g., 'REMINDER_BILLFETCH_PIPELINE')
     * @returns {Promise} - Promise that resolves with the publisher
     */
    async createPublisher(topicKey) {
        if (this.publishers.has(topicKey)) {
            this.logger.log(`[KafkaPublisherFactory] Publisher for ${topicKey} already exists`);
            return this.publishers.get(topicKey);
        }

        const kafkaConfig = {
            kafkaHost: _.get(this.config.KAFKA, `TOPICS.${topicKey}.HOSTS`)
        };

        const producer = new this.infraUtils.kafka.producer(kafkaConfig);
        
        try {
            await new Promise((resolve, reject) => {
                producer.initProducer('high', (error) => {
                    if (error) {
                        this.logger.error(`[KafkaPublisherFactory] Error initializing publisher for ${topicKey}:`, error);
                        reject(error);
                    } else {
                        this.logger.log(`[KafkaPublisherFactory] Publisher for ${topicKey} initialized successfully`);
                        resolve();
                    }
                });
            });

            this.publishers.set(topicKey, producer);
            return producer;
        } catch (error) {
            this.logger.error(`[KafkaPublisherFactory] Failed to create publisher for ${topicKey}:`, error);
            throw error;
        }
    }

    /**
     * Get an already created publisher
     * @param {string} topicKey - The key in config.KAFKA.TOPICS
     * @returns {Object} - The Kafka publisher instance
     */
    getPublisher(topicKey) {
        const publisher = this.publishers.get(topicKey);
        if (!publisher) {
            throw new Error(`[KafkaPublisherFactory] Publisher for ${topicKey} not found`);
        }
        return publisher;
    }

    /**
     * Close all Kafka publishers
     * @returns {Promise<void>}
     */
    async closeAll() {
        for (const [name, publisher] of this.publishers) {
            try {
                if (publisher && publisher.client) {
                    await new Promise((resolve, reject) => {
                        publisher.client.close((error) => {
                            if (error) {
                                this.logger.error(`[KafkaPublisherFactory] Error closing publisher ${name}:`, error);
                                reject(error);
                            } else {
                                this.logger.log(`[KafkaPublisherFactory] Publisher ${name} closed successfully`);
                                resolve();
                            }
                        });
                    });
                }
            } catch (error) {
                this.logger.error(`[KafkaPublisherFactory] Error closing publisher ${name}:`, error);
            }
        }
        this.publishers.clear();
    }
}

export default KafkaPublisherFactory; 