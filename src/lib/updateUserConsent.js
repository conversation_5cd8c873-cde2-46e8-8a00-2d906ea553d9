"use strict";

import _ from 'lodash'
import MOMENT from 'moment'
import utility from '.';
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator'
import uuidv1 from 'uuidv1'
import REQUEST from 'request'
import jwt from  'jsonwebtoken'
import CryptoJs from  'crypto-js'

class UpdateUserConsentLibrary {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        
    }
    /**
     * 
     * @param {*} processedRecord 
     */
     getDbRecordToUpdate(processedRecord) {
        let self = this,
            dbFormatData = {};

        _.set(dbFormatData, ['customer_id'], processedRecord.customer_id);
        _.set(dbFormatData, ['is_retailer'], false);
        _.set(dbFormatData, ['counter'], 0);
        _.set(dbFormatData, ['min_date'], null);
        _.set(dbFormatData, ['whatsapp_notification_status'], _.get(processedRecord ,['preferenceValue'] , _.get(self.config, ['COMMON', 'USER_PREFERENCES_WHATSAPP_NOTIFICATION_STATUS' , 'NOT_AVAILABLE'], -1)));

        return dbFormatData;
    }
    getRequestDataToUpdate(processedRecord){
        let requestData = {},
            preferenceValue;
        switch(processedRecord.preferenceValue){
            case 1 : preferenceValue = 'ENABLED';
            break;
            case 0 : preferenceValue = 'DISABLED';
            break;
            case -1: preferenceValue = 'NOT_AVAILABLE';
            break;
        }

        _.set(requestData, ['customer_id'] , processedRecord.customer_id);
        _.set(requestData, ['preferenceKey'] , 'ocl.user.consent.ru_whatsapp_reminders');
        _.set(requestData, ['preferenceValue'] , preferenceValue);

        return requestData;
    }
    
    /**
     * 
     * @param {*} cb 
     * @param {*} requestData :{
     *      customer_id : <315494420>  <numeric>,
     *      preferenceKey  : "ocl.user.consent.ru_whatsapp_reminders" <string>
     *      preferenceValue : "<ENABLED/DISABLED/NOT_AVAILABLE>" <enum>
     * }
     */
     updateUserPreferences(cb, requestData, url, source){
        try {
            // let requestBody = {
            //     "request": {
            //         "preferenceKey": "ocl.user.consent.ru_whatsapp_reminders",
            //         "preferenceValue": "ENABLED"
            //     }
            // };
            let self = this,
                requestBody = {
                "request": {
                    "preferenceKey": _.get(requestData , ['preferenceKey'] , '') ,
                    "preferenceValue": _.get(requestData , ['preferenceValue'] , '')
                }
            };
            url = url ? url : _.get(self.config, 'COMMON.USER_PREFERENCES_UPDATE_API_URL', '');            

            // parse customer_id to integer if it is a number
            if (requestData.customer_id) {
                requestData.customer_id = typeof (requestData.customer_id) === 'string' ? VALIDATOR.toInt(requestData.customer_id) : requestData.customer_id;   
            }

            let debugKey = `customer_id:${requestData.customer_id}_preferenceValue:${requestData.preferenceValue}_source:${source}`,
                apiOpts = {
                    url: url, 
                    method: 'PUT',
                    headers: {
                        "Content-Type": 'application/json',
                        "jwt-token" : self.generateJWTForUPS("PUT", requestBody),
                        "requestId": self.getUUID(),
                        "X-User-Id": requestData.customer_id
                    },
                    "json": requestBody
                };
            let latencyStart = new Date().getTime();    
            self.L.log('USER_PREFERENCES_UPDATE_API_URL::updateUserPreferences ApiOpts', `${JSON.stringify(apiOpts)}_having_debug_key ${debugKey}`);

            REQUEST(apiOpts, (error, response, body) =>{
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'USER_PREFERENCES_UPDATE_API_URL',
                    'URL': `${url}`,
                    'SOURCE' : `${source}`
                });
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:USER_PREFERENCES_UPDATE_API_URL', 
                    `URL:${url}`,
                    'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX')),
                    'SOURCE:'+source
                ]);
                if (!error && body) {
                    let res = body;
                    this.L.log('USER_PREFERENCES_UPDATE_API_URL::updateUserPreferences Response', `${JSON.stringify(body)}_having_debug_key ${debugKey}`);
                    
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:USER_PREFERENCES_UPDATE_API_URL', 
                        `URL:${url}`,
                        'SOURCE:'+source,
                        'STATUS:SUCCESS',
                    ]);

                    return cb(null, res);
                } else {
                    this.L.error('USER_PREFERENCES_UPDATE_API_URL::updateUserPreferences Error', `${error}_having_ApiOpts${JSON.stringify(apiOpts)}_having_debug_key ${debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:USER_PREFERENCES_UPDATE_API_URL', 
                        `URL:${url}`,
                        'SOURCE:'+source,
                        'STATUS:FAILED',
                    ]);
                    return cb(`USER_PREFERENCES_UPDATE_API_URL Error Msg: ${error}`);
                }
            });
        } catch(error) {
            this.L.error('USER_PREFERENCES_UPDATE_API_URL::updateUserPreferences Error', `${error}_having_ApiOpts${JSON.stringify(requestData)}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:USER_PREFERENCES_UPDATE_API_URL', 
                `URL:${url}`,
                'SOURCE:'+source,
                'STATUS:EXCEPTION',
            ]);
            return cb(`USER_PREFERENCES_UPDATE_API_URL Error Msg: ${error}`);
        }
    }

    /**
     * 
     * @param {*} cb 
     * @param {*} requestData :{
     *      customer_id : <315494420>  <numeric>
     *      preferenceKey  : "ocl.user.consent.ru_whatsapp_reminders" <string>
     * }
     */

     getUserPreferences(cb, requestData, url){
        try {
            let self = this;
            if (requestData.customer_id) {
                requestData.customer_id = typeof (requestData.customer_id) === 'string' ? VALIDATOR.toInt(requestData.customer_id) : requestData.customer_id;   
            }
            url = url ? url : _.get(self.config, 'COMMON.USER_PREFERENCES_UPDATE_API_URL', '');
            url += `?preferenceKeys=${requestData.preferenceKey}`;

            let debugKey = `customer_id:${requestData.customer_id}_preferenceKey:${requestData.preferenceKey}`,
                //url = _.get(self.config, 'COMMON.USER_PREFERENCES_UPDATE_API_URL', '') + `?preferenceKeys=${requestData.preferenceKey}`,
                apiOpts = {
                    url: url, 
                    method: 'GET',
                    headers: {
                        "jwt-token" : self.generateJWTForUPS("GET"),
                        "requestId": self.getUUID(),
                        "X-User-Id": requestData.customer_id
                    }
                    // "json": true
                };
            let latencyStart = new Date().getTime();    
            self.L.log('USER_PREFERENCES_GET_API_URL::getUserPreferences ApiOpts', `${JSON.stringify(apiOpts)}_having_debug_key ${debugKey}`);
            REQUEST(apiOpts, (error, response, body) =>{
                utility._sendLatencyToDD(latencyStart, {
                    'REQUEST_TYPE': 'USER_PREFERENCES_GET_API_URL',
                    'URL': `${url}`
                });
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:USER_PREFERENCES_GET_API_URL', 
                    `URL:${url}`,
                    'STATCODE:' + _.get(response, 'statusCode', _.get(error, 'code', '5XX'))
                ]);
                if (!error && body) {
                    let res = body;
                    try {
                        res = JSON.parse(res);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:USER_PREFERENCES_GET_API_URL', 
                            `URL:${url}`,
                            'STATUS:SUCCESS',
                        ]);
                        this.L.log('USER_PREFERENCES_GET_API_URL::getUserPreferences Response', `${JSON.stringify(res)}_having_debug_key ${debugKey}`);
                        return cb(null, res);
                    } catch (error) {
                        this.L.error('USER_PREFERENCES_GET_API_URL::getUserPreferences Error', `Error ${error} while parsing response:${JSON.stringify(res)}`);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:USER_PREFERENCES_GET_API_URL', 
                            `URL:${url}`,
                            'STATUS:EXCEPTION'
                        ]);
                        return cb(`USER_PREFERENCES_GET_API_URL Error Msg: ${error}`);
                    }
                } else {
                    this.L.error('USER_PREFERENCES_GET_API_URL::getUserPreferences Error', `${error}_having_ApiOpts${JSON.stringify(apiOpts)}_having_debug_key ${debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:USER_PREFERENCES_GET_API_URL', 
                        `URL:${url}`,
                        'STATUS:FAILED'
                    ]);
                    return cb(`USER_PREFERENCES_GET_API_URL Error Msg: ${error}`);
                }
            });   
        } catch (error) {
            this.L.error('USER_PREFERENCES_GET_API_URL::getUserPreferences Error', `${error}_having_ApiOpts${JSON.stringify(requestData)}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:USER_PREFERENCES_GET_API_URL', 
                `URL:${url}`,
                'STATUS:EXCEPTION'
            ]);
            return cb(`USER_PREFERENCES_GET_API_URL Error Msg: ${error}`);
        }
    }

    getUUID() {
        return uuidv1();
    }

    
    generateJWTForUPS(method, requestBody){
        let self = this,
            jwtToken = '';
        try {
            const jwtSecret = _.get(self.config, 'COMMON.USER_PREFERENCES_API_CLIENT_SECRET', '')
            const clientId = _.get(self.config, 'COMMON.USER_PREFERENCES_API_CLIENT_ID', '');
            const currentTimestamp = Math.floor(Date.now());
            const signOptions = {
                algorithm: 'HS256',
                noTimestamp: true,
                expiresIn: 2 * 60 * 60
            };
            switch(method){
                case 'PUT': {
                    const requestHash = CryptoJs.SHA256(JSON.stringify(requestBody));
                    const payload = {
                        'clientId': ''+clientId,
                        'ts': ''+currentTimestamp,
                        'requestHash':''+requestHash
                    };
                    jwtToken = jwt.sign(payload, jwtSecret, signOptions);
                }
                break;
                case 'GET': {
                    const payload = {
                        'clientId': ''+clientId,
                        'ts': ''+currentTimestamp
                    };
                    jwtToken = jwt.sign(payload, jwtSecret, signOptions);
                }
                break;
                default:
                    jwtToken = ''; 
                break;
            }
            return jwtToken;
        } catch(error) {
            // error log & metrics
            this.L.error('generateJWTForUPS::Error:', `${error}_having_requestBody:${JSON.stringify(requestBody)}_method:${method}`);
            return jwtToken;
        }
    }

}

export default UpdateUserConsentLibrary;