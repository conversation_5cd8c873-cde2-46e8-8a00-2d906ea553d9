import { EventEmitter } from 'events';


/**
 * 
 * 
 * 
 * The file has not been tested by the QA
 * 
 * 
 * 
 * 
 */
class Lock {
    constructor(options) {
        this.isLocked = false;
        this.eventEmitter = new EventEmitter();
        
        this.maxSize = options.batchSize * 2
        this.eventEmitter.setMaxListeners(this.maxSize);
    }

    acquire() {
        return new Promise(resolve => {
            if (!this.isLocked) {
                this.isLocked = true;
                return resolve();
            }

            let self = this;
            this.eventEmitter.on('release', function ff() {
                self.eventEmitter.removeListener('release', ff);
                return resolve();
            });
        });
    }

    release() {
        this.isLocked = false;
        this.eventEmitter.emit('release');
    }
}

export default Lock;