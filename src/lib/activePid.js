import Q                    from 'q'
import _                    from 'lodash'
import digitalUtility       from 'digital-in-util'

class ActivePID{

    constructor(options){
        this.activePidMapping   = {};
        this.L                  = options.L;
        this.config             = options.config;
        this.productUtils       = new digitalUtility.product();
        this.productConfig      = {
            activeInactivePidsFlag  : true, // if active inactive PID based mapping is required
            refreshInverval         : 30 * 1000 * 60, // Refresh interval required min= 15 min
            debug                   : true, // Debug log is needed
            loadCvrDataFlag         : false
        }
    }

    load(){
        let 
            self        = this,
            deferred    = Q.defer();

        //self.activePidMapping = _.get(self.config,'ACTIVE_PID_REGISTRY',{});
        self.productUtils.configureProducts(function(){
            self.L.log('ActivePID :: load','Active PID Data Loaded Successfully');    
            
            self.activePidMapping = self.productUtils.getInactiveVsActiveProductData();
            deferred.resolve();
        },self.productConfig);

        return deferred.promise;
    }

    getActivePID(oldProductId){
        let
            self        = this;
            
        /** We update the activePidMapping variable with the latest one from library */    
        try{
            self.activePidMapping = self.productUtils.getInactiveVsActiveProductData()
        }catch(err){
            self.L.error("ActivePID :: getActivePID","Error while fetching Active PID")
        }

        let activePid   = _.get(self.activePidMapping,[oldProductId],oldProductId);
        self.L.verbose("ActivePID :: getActivePID",`Old PID : ${oldProductId}, Active PID: ${activePid}`);
        return activePid;
    }
}


export default {
    ActivePID
};