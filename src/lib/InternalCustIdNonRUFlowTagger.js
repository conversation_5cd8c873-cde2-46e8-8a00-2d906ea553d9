import _ from 'lodash';
class InternalCustIdNonRUFlowTagger{
    constructor(options) {
        this.options = options;
        this.L = options.L;
    }

    async mapInternalCustIdToNonRUFlow(record) {
        let self = this;
        self.L.log('InternalCustIdNonRUFlowTagger:mapInternalCustIdToNonRUFlow', 'Mapping internal cust id to non ru flow');
        const rechargeNumber = _.get(record, 'recharge_number', _.get(record, 'rechargeNumber', _.get(record, 'rechargeNumber', '')));
        const service = _.get(record, 'service', null);
        const paytype = _.get(record, 'paytype', null);
        const operator = _.get(record, 'operator', null);
        const customerId = _.get(record, 'customer_id', _.get(record, 'customerId', null));
        const key = rechargeNumber + "_" + service + "_" + paytype + "_" + operator;
        self.L.log('InternalCustIdNonRUFlowTagger:mapInternalCustIdToNonRUFlow', 'Key', key);
        const internalCustIds = this.options.internalCustIdMap ? this.options.internalCustIdMap[key] : null;
        self.L.log('InternalCustIdNonRUFlowTagger:mapInternalCustIdToNonRUFlow', 'Internal cust ids', this.options.internalCustIdMap[key]);
        let nonRuData = [];
        nonRuData.push(JSON.stringify(record));
        if (internalCustIds && internalCustIds.length > 0) {
            internalCustIds.map(internalCustId => {
                let clonedData = _.cloneDeep(record);
                if (internalCustId == customerId) return;
                else {
                    clonedData.customerId = internalCustId;
                    nonRuData.push(JSON.stringify(clonedData));
                }
            });
        }
        return nonRuData;
    }

    checkIfRecordHasInternalCustId(record) {
        const rechargeNumber = _.get(record, 'recharge_number', _.get(record, 'rechargeNumber', _.get(record, 'rechargeNumber', '')));
        const service = _.get(record, 'service', null);
        const paytype = _.get(record, 'paytype', null);
        const operator = _.get(record, 'operator', null);
        const key = rechargeNumber + "_" + service + "_" + paytype + "_" + operator;
        const internalCustIds = this.options.internalCustIdMap ? this.options.internalCustIdMap[key] : null;
        //check if record.customer_id is present in internalCustIds
        if (internalCustIds && internalCustIds.length > 0) {
            return internalCustIds.includes(_.get(record, 'customerId', _.get(record, 'customer_id', null)));
        }
        return false;
    }
}

export default InternalCustIdNonRUFlowTagger;