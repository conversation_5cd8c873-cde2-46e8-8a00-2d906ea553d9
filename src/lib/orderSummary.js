import REQUEST from 'request'
import _ from 'lodash'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from 'jsonwebtoken'

class OrderSummary {
    constructor(option){
        this.config = option.config;
    }
    getPGDonationData (cb , reqBody){
        
        var clientId = _.get(this.config, ["ORDERSUMMARY", "client_id"]);
        var Signed_key = _.get(this.config, ["ORDERSUMMARY", "Signed_key"]);

        let self = this,
            payload= {clientId: clientId, iss: "ts"},
            signOptions = {
            expiresIn: 2 * 60 * 60                //seconds
            },
            JWT_TOKEN = JSONWEBTOKEN.sign(payload, Signed_key, signOptions);

        let apiOpts = {
                url: _.get(self.config, ["ORDERSUMMARY", "END_POINT"], "")+"/pg-plus-bo/merchant/ordersummaryquery",
                headers: {
                'Content-Type': 'application/json',
                'x-jwt-token': JWT_TOKEN
                },
                method: 'POST',
                json: reqBody,
                timeout: 55000
            };
        REQUEST(apiOpts, (error, response, body) =>{
            if (!error && body) {
                var res = {
                    totalAmount: _.get(body, ["totalAmount", "amountInRs"], 0),
                    totalCount : _.get(body, "totalCount", 0)
                };
                return cb(null, res);
            } else {
                return cb(error);
            }
        });
    }


}

module.exports = OrderSummary;