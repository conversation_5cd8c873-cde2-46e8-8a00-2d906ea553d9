"use strict";


class ReminderBaseFlow{
    constructor(options){
        this.config = options.config;
        this.L = options.L;
        this.users = options.users ;
        this.recentsLayer = options.recentsLayer;
    }
    
    /* start of Notification Management classes */

    /**
     * Validates notification disable request params
     * @param {*} params 
     */
    notificationManager_validateReq(params){
        this.L.log("ReminderFlowsBase:notificationManager_validateReq",`${params.traceKey}`)
        return {
            status : true,
            errors : []
        }
    }

    /**
     * Updates notification_status for customer and RN in DB
     * @param {*} params 
     */
    async notificationManager_updateReminderDB(params){}

    /**
     * Updates/deletes Recents based on notification event
     * @param {*} params 
     */
    async notificationManager_updateRecents(params){
        try {
            this.L.log("ReminderBaseFlow:notificationManager_updateRecents",`for ${params.traceKey}`)
            await this.recentsLayer.removeRecents(params,"ReminderBaseFlow");
            this.L.log("ReminderBaseFlow:notificationManager_updateRecents flow complete",`for ${params.traceKey}`)
        } catch (error) {
            this.L.log("ReminderBaseFlow:notificationManager_updateRecents error",`for ${params.traceKey}`)
            throw new Error(error);
        }
    }

    async notificationManager_updateBillReminderFlag(params){
        let self = this,
            notificationStatus = params.notificationStatus,
            remindableFlag = notificationStatus ? true : false ;    
        this.L.log(`ReminderBaseFlow:notificationManager_updateBillReminderFlag notificationStatus ${notificationStatus} and remindableFlag ${remindableFlag}`);
        try {
            await this.users.updateBillReminderFlag(function (error, data) {    
                if (!error && data) {                    
                    self.L.log("ReminderBaseFlow:notificationManager_updateBillReminderFlag updated sucessfully ");
                }

            }, 'users', params, remindableFlag);
        } catch (error) {
            throw new Error(error);
        }
    }
    /* end of Notification Management classes */

    /**
     * Find operator based on operator and paytype
     * @param {*} operator 
     * @param {*} paytype 
     */
    getTableName(operator,paytype){}

    /* Impelements mark as paid functionality */
    async markAsPaid(params){}
}

export default ReminderBaseFlow