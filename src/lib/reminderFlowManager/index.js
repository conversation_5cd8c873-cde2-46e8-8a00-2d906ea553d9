"use strict";

import _ from 'lodash'

import RechargeFlow from './recharge'
import BillPaymentsFlow from './billpayments'
import CreditCardFlow from './creditcard'
import AirtelPrePaidFlow from './airtelPrepaid'
//import RentPaymentFlow from './rentpayment'
import MonthlyPaymentFlow from './monthlypayment'

/**
 * Idea here to maintan functionality flow wise to avoid if elses in main code 
 * and better code readability + maintanance
 */

class ReminderFlows {
    constructor(options) {
        this.recentsLayer = options.recentsLayer;
        this.L = options.L;
        this.config = options.config;
        this.prepaidFlowInstance = this.getPrepaidInstance(options)
        this.postPaidFlowInstance = this.getPostpaidInstance(options)
        this.creditCardFlowInstance = this.getCreditCardInstance(options)
        this.airtelPrepaidFlowInstance = this.getAirtelPrepaidInstance(options)
        //this.rentPaymentFlow = this.getRentPaymentInstance(options)
        this.monthlyPaymentFlow=this.getMonthlyPaymentInstance(options);

        this.includedOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        this.includedOperator = this.includedOperatorList.split(',').map((e) => e.trim());
    }

    /**
     * Return flow instance based on operator and paytype
     * @param {*} params 
     */
    getFlowInstance(params) {
        let
            self=this,
            service = _.get(params, 'service', null),
            operator = _.get(params, 'operator', null),
            paytype = _.get(params, 'paytype', null),
            includedOperator = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_POSTPAIDFLOW', 'COMMON', 'INCLUDE_OPERATOR'],[]),
            ALLOWED_DTH_OPERATOR = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []);

        if (!operator || !paytype) {
            self.L.error("ReminderFlows:getFlowInstance","Invalid params operator{} paytype{}", operator, paytype);
            return null;
        } else if (operator == 'airtel' && paytype == 'prepaid') {
            return this.airtelPrepaidFlowInstance;
        } else if (service === 'dth' && ALLOWED_DTH_OPERATOR.includes(operator) && paytype === 'prepaid') {
            return this.postPaidFlowInstance;
        } 
        /*
        else if(operator == 'rent payment') {
            return this.rentPaymentFlow;
        }
        */else if(self.includedOperator.includes(operator)) {
            return this.monthlyPaymentFlow;    
        }else if(paytype=='prepaid' && includedOperator.indexOf(operator) < 0){
            return this.prepaidFlowInstance;
        } else if(paytype == 'credit card') {
            return this.creditCardFlowInstance;
        } else {
            return this.postPaidFlowInstance;
        }
    }

    getAirtelPrepaidInstance(options) {
        return new AirtelPrePaidFlow(options)
    }

    getPrepaidInstance(options) {
        return new RechargeFlow(options)
    }

    getPostpaidInstance(options) {
        return new BillPaymentsFlow(options)
    }

    getCreditCardInstance(options) {
        return new CreditCardFlow(options)
    }

    /*
    getRentPaymentInstance(options) {
        return new RentPaymentFlow(options)
    }
    */
    getMonthlyPaymentInstance(options) {
        return new MonthlyPaymentFlow(options)
    }
}

export default ReminderFlows