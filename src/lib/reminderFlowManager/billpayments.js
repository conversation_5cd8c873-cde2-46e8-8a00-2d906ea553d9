"use strict";

import _ from 'lodash'

import <PERSON>mind<PERSON><PERSON><PERSON><PERSON><PERSON> from './base'
import ASY<PERSON> from 'async'
import utility from '../index.js'

class BillPaymentsFlow extends ReminderBaseFlow {
    constructor(options){
        super(options)
        this.billsModel = options.billsModel;
        this.commonLib = new utility.commonLib(options);
    }

    /* start of Notification Management classes */
    async notificationManager_updateReminderDB(params){
        this.L.log("BillPaymentsFlow:notificationManager_updateReminderDB",`Updating for ${params.traceKey}`)
        let { query, queryParams } = this.notificationManager_getUpdateQueryParams(params);
        let lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        if (lowBalancePrepaidElectricityAllowedOperators.includes(params.operator)) {
            let { prepaidQuery, prepaidQueryParams } = this.notificationManager_getUpdateQueryParamsForPrepaid(params);
            ASYNC.parallel([
                async (cbparallel) => {
                    await this.billsModel.updateBillsTable(query, queryParams);
                    cbparallel(null);
                },
                async (cbparallel) => {
                    await this.billsModel.updateBillsTable(prepaidQuery, prepaidQueryParams);
                    cbparallel(null);
                }
            ], (err, results) => {
                return;
            });
        } else {
            await this.billsModel.updateBillsTable(query, queryParams);
        }
    }

    notificationManager_getUpdateQueryParamsForPrepaid(params) {
        let self = this,
            tableName = this.getTableName(params.operator, params.paytype),
            query,
            values;
        tableName = tableName + '_prepaid';
        if (_.get(params, 'v3UpdateNotificationStatus', null)) {
            query = `UPDATE ${tableName} SET ${params.stopBillFetch == true ? 'status =' + _.get(self.config, 'COMMON.bills_status.DISABLED', 7) + ',' : 'status =' + _.get(self.config, 'COMMON.bills_status.PENDING', 0) + ','} notification_status = ? WHERE customer_id = ? AND recharge_number = ? AND operator = ? AND service = ? ;`;
        } else {
            query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND recharge_number = ? AND operator = ? AND service = ? AND notification_status!=0;`;
        }
        values = [params.notificationStatus, params.customerId, params.rechargeNumber, _.toLower(params.operator), _.toLower(params.service)];

        return { prepaidQuery: query, prepaidQueryParams: values }
    }

    notificationManager_getUpdateQueryParams(params){
        let self = this,
            tableName = this.getTableName(params.operator,params.paytype),
            query,
            values;
        let RnCondition = '';
        let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(params.rechargeNumber, params.operator);
        
        if(isOperatorPrefixEnabled){
            RnCondition = 'recharge_number IN (?, ?)';
            values = [
                params.notificationStatus, 
                params.customerId,
                params.rechargeNumber,
                alternateRechargeNumber,
                _.toLower(params.operator),
                _.toLower(params.service)
            ];
        } else {
            RnCondition = 'recharge_number = ?';
            values = [
                params.notificationStatus, 
                params.customerId,
                params.rechargeNumber,
                _.toLower(params.operator),
                _.toLower(params.service)
            ];
        }

        if(_.get(params, 'v3UpdateNotificationStatus', null)) {
            query = `UPDATE ${tableName} SET ${params.stopBillFetch == true ? 'status =' + _.get(self.config, 'COMMON.bills_status.DISABLED', 7) + ',' : 'status =' + _.get(self.config, 'COMMON.bills_status.PENDING', 0) + ','} notification_status = ? WHERE customer_id = ? AND ${RnCondition} AND operator = ? AND service = ?;`;
        } else {
            query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND ${RnCondition} AND operator = ? AND service = ? AND notification_status!=0;`;              
        }

        if(params.referenceId != null && params.isCreditCardOperator){
            if(_.get(params, 'v3UpdateNotificationStatus', null)) {
                query = `UPDATE ${tableName} SET ${params.stopBillFetch == true ? 'status =' + _.get(self.config, 'COMMON.bills_status.DISABLED', 7) + ',' : 'status =' + _.get(self.config, 'COMMON.bills_status.PENDING', 0) + ','} notification_status = ? WHERE customer_id = ? AND reference_id = ? AND operator = ? AND service = ?;`;
            } else {
                query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND reference_id = ? AND operator = ? AND service = ? AND notification_status!=0;`;              
            }
            values = [params.notificationStatus, params.customerId, params.referenceId, _.toLower(params.operator), _.toLower(params.service)];
        }

        return {query: query, queryParams: values}
    }

    getTableName(operator,paytype){
       return _.get(this.config,['OPERATOR_TABLE_REGISTRY',operator],null)
    }
    
    notificationManager_getBill(cb,params){
        
        let 
            tableName = this.getTableName(params.operator,params.paytype);
            
            let query = `SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ? ;`,
            queryParams = [
                params.customerId,
                _.toLower(params.operator),
                _.toLower(params.service),
                params.rechargeNumber
            ];
        this.L.log("BillPaymentsFlow:notificationManager_getBill",` for ${params.traceKey}`);
        return this.billsModel.getBillData(cb, query,queryParams)        
    }


    async  getBillsData(reminderFlowInstance, reqData) {
        return new Promise((resolve, reject) => {
            reminderFlowInstance.notificationManager_getBill((error, data) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(data);
                }
            }, reqData);
        });
    }

     updateRemindLaterDate(params){
        return new Promise((resolve, reject) => {
            let self = this,
                tableName = this.getTableName(params.operator,params.paytype),
                query,
                queryParams ;
                
            query = `UPDATE ${tableName} SET remind_later_date = ?, extra = ? WHERE customer_id = ?`;
            queryParams = [params.remindLaterDate, params.updatedExtra, params.customer_id];

            let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(params.recharge_number, params.operator);
            if(isOperatorPrefixEnabled){
                query += ` AND recharge_number in (?,?)`;
                queryParams.push(params.recharge_number, alternateRechargeNumber);
            }else{
                query += ` AND recharge_number = ?`;
                queryParams.push(params.recharge_number);
            }

            query += ` AND operator = ? AND service = ? AND extra = ?;`;
            queryParams.push(params.operator, params.service, params.extra);
    
            // Assuming you have a method to execute the query

            self.L.log(`billPayments::updateRemindLaterDate :: Updating remind_later_date for query ${query} and params ${JSON.stringify(queryParams)}`);

            self.billsModel.updateBillsTable(query, queryParams)
                .then(result => {
                    resolve(result);
                })
                .catch(error => {
                    reject(error);
                });
        });
    }


}

export default BillPaymentsFlow