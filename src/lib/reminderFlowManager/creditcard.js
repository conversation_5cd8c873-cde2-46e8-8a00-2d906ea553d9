"use strict";

import _ from 'lodash'

import BillPaymentsFlow from './billpayments'
import ENCDECPUTIL from '../EncryptionDecryptioinHelper'
import MOMENT from 'moment'

class CreditCardFlow extends BillPaymentsFlow {
    constructor(options){
        super(options)
        this.billsModel = options.billsModel;
        this.encDecpUtil = new ENCDECPUTIL(options);
        this.config = options.config;
    }

    /* start of Notification Management classes */

    /**
     * Validate request for notification management API
     * @param {*} params 
     */
    notificationManager_validateReq(params){
        this.L.log('CreditCardFlow::notificationManager_validateReq',`evaluating for ${params && params.traceKey}`);
        let validationResponse = super.notificationManager_validateReq(params);
        /* It should have referenceId in it*/
        if(params.parId && params.parId!= '' && !params.referenceId)
                params.referenceId = params.parId;
        if(!params || !params.referenceId) {
            validationResponse.errors.push({msg:"Invalid Value",param:"referenceId"})
            validationResponse.status = false
        }
        return {
            status : validationResponse.status,
            errors : validationResponse.errors
        }
    }

    async notificationManager_updateReminderDB(params){
        let self = this;
        if(self.encDecpUtil.isWhitelistedForCC('financial services', 'credit card', parseInt(params.customerId)) && _.get(params, 'v3UpdateNotificationStatus', false)) {
            let encryptedParams = _.cloneDeep(params);
            if(typeof encryptedParams.referenceId != 'string') self.encDecpUtil.parseToString(encryptedParams.referenceId);
            encryptedParams.referenceId = self.encDecpUtil.encryptData(encryptedParams.referenceId);

                let response = await self.notificationManager_updateReminderDBDefault(encryptedParams);
                if(_.get(response, ['message'], -1) > 0) {
                    return;
                } else {
                    await self.notificationManager_updateReminderDBDefault(params);
                }
        } else {
            await self.notificationManager_updateReminderDBDefault(params);
        }
    }

    getInsertQueryAndParams(params, tableName) {
        let data = _.cloneDeep(params);
        delete data['id'];
        _.set(data, 'is_encrypted', 1);
        _.set(data, 'updated_at', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        let keys = Object.keys(data);
        let values = Object.values(data);

        let insertQuery = `INSERT INTO ${tableName} (${keys.join(',')}) VALUES (${keys.map(() => '?').join(',')})`;
        return {insertQuery, insertQueryParams: values};
    }

    getDeleteQueryAndParams(params, tableName) {
        let deleteQuery = `DELETE FROM ${tableName} WHERE customer_id = ? AND reference_id = ?`;
        let deleteQueryParams = [params.customerId, params.referenceId];
        return {deleteQuery, deleteQueryParams};
    }

    encryptData(params) {
        let self = this;
        let rootLevelKeysToEncrypt = ['recharge_number','reference_id','customer_mobile','customer_email', 'enc_amount', 'enc_due_date'];
                
        let keysToEncryptForUserData = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'USER_DATA_KEYS'], ['recharge_number_2', 'recharge_number_3', 'recharge_number_4', 'recharge_number_5', 
            'recharge_number_6']);
                        
        let keysToEncryptForCustomerOtherInfo =  _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'CUSTOMER_OTHER_INFO_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress',
        'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 
        'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey']);
            
        let keysToEncryptForCustomerExtra = _.get(self.config, ['DYNAMIC_CONFIG', 'CCBP', 'ENCRYPTION_CONFIG', 'EXTRA_KEYS'], ['subscriberNumber', 'subscriberName', 'subscriberEmailId', 'subscriberDOB', 'subscriberAddress',
        'subscriberAltNumber', 'subscriberGender', 'subscriberCity', 'minReloadAmount', 'currentBillAmount', 
        'currentMinBillAmount', 'billDueDate', 'billDueAmount', 'lastCC', 'rawLastCC', 'debugKey']);

        if(params.due_date != null) {
            if(MOMENT(params.due_date, MOMENT.ISO_8601).isValid()) {
                params.due_date = MOMENT(params.due_date, MOMENT.ISO_8601).utc().format('YYYY-MM-DD HH:mm:ss');
            } else if(typeof params.due_date == "object") {
                params.due_date = self.encDecpUtil.parseToString(params.due_date);
            }
        }
        let encryptedRecord = self.encDecpUtil.getEncryptedParamsFromGenericParams(params,rootLevelKeysToEncrypt, keysToEncryptForCustomerExtra, keysToEncryptForCustomerOtherInfo, keysToEncryptForUserData);
        _.set(encryptedRecord, 'amount', null);
        _.set(encryptedRecord, 'due_date', null);
        return encryptedRecord;
    }

    getBill(params) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.notificationManager_getBill((error, data) => {
                if(error) {
                    reject(error);
                } else {
                    resolve(data);
                }
            }, params);
        });
    }

    async notificationManager_updateReminderDBDefault(params){
        this.L.log("CreditCardFlow:notificationManager_updateReminderDB",`Updating for ${params.traceKey}`)
        let {query,queryParams} = this.notificationManager_getUpdateQueryParams(params);
        return this.billsModel.updateBillsTable(query,queryParams);
    }

    notificationManager_getUpdateQueryParams(params){
        let self = this,
            tableName = this.getTableName(params.operator,params.paytype),
            query,
            values ;
            if(params.parId && params.parId!= '' && !params.referenceId)
                params.referenceId = params.parId;
            if(_.get(params, 'v3UpdateNotificationStatus', null)) {
                query = `UPDATE ${tableName} SET ${params.stopBillFetch ? 'status =' + _.get(self.config, 'COMMON.bills_status.DISABLED', 7) + ',' : ''} notification_status = ? WHERE customer_id = ? AND reference_id = ?;`;
            }else{
                query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND reference_id = ? AND notification_status!=0;`;                
            }
            values = [params.notificationStatus, params.customerId, params.referenceId];

        return {query:query,queryParams:values}
    }

    getTableName(operator,paytype){
        return _.get(this.config,['OPERATOR_TABLE_REGISTRY',operator],null)
    }
    
    notificationManager_getBill(cb,params){
        let self = this;
        if(self.encDecpUtil.isWhitelistedForCC('financial services', 'credit card', parseInt(params.customerId))) {
            let encryptedParams = _.cloneDeep(params);
            if(typeof encryptedParams.referenceId != 'string') self.encDecpUtil.parseToString(encryptedParams.referenceId);
            encryptedParams.referenceId = self.encDecpUtil.encryptData(encryptedParams.referenceId);
            self.notificationManager_getBill_default((error, data) => {
                if(error) {
                    return cb(error);
                } else if(data && data.length == 0) {
                    return self.notificationManager_getBill_default(cb,params);
                }
                data = self.encDecpUtil.parseDbResponse(data, params.customerId);
                return cb(null, data);
            }, encryptedParams);
        } else {
            return self.notificationManager_getBill_default(cb,params);
        }
    }

    notificationManager_getBill_default(cb,params){
        if(params.parId && params.parId!= '' && !params.referenceId)
                params.referenceId = params.parId;
        let 
            tableName = this.getTableName(params.operator,params.paytype),
            query = `SELECT * FROM ${tableName} WHERE customer_id = ? AND reference_id = ?`,
            queryParams = [
                params.customerId,
                params.referenceId
            ];
        this.L.log("BillPaymentsFlow:notificationManager_getBill",` for ${params.traceKey}`);
        return this.billsModel.getBillData(cb, query,queryParams)        
    }    
}

export default CreditCardFlow