"use strict";

import _ from 'lodash'
import MOMENT from 'moment'

import BillPaymentsFlow from './billpayments'

class RentPaymentFlow extends BillPaymentsFlow {
    constructor(options) {
        super(options)
        this.config = options.config;
        this.L = options.L;
        this.billsModel = options.billsModel;
    }

    /**
     * Based on transaction amount  
     * @param {float} transactionAmount 
     * @return {date} due date
     */
    getHeauristicDueDateBasedOnTxn(transactionAmount) {
        let
            thresholdAmount = _.get(this.config, ['DYNAMIC_CONFIG', 'RENT_PAYMENT', 'PAYMENT_THRESHOLD', 'AMOUNT'], 1000),
            belowThresholdAddTimeDuration = _.get(this.config, ['DYNAMIC_CONFIG', 'RENT_PAYMENT', 'PAYMENT_THRESHOLD', 'BELOW_TIME_DURATION'], 1),
            belowThresholdAddTimeUnit = _.get(this.config, ['DYNAMIC_CONFIG', 'RENT_PAYMENT', 'PAYMENT_THRESHOLD', 'BELOW_TIME_UNIT'], 'days'),
            aboveThresholdAddTimeDuration = _.get(this.config, ['DYNAMIC_CONFIG', 'RENT_PAYMENT', 'PAYMENT_THRESHOLD', 'ABOVE_TIME_DURATION'], 1),
            aboveThresholdAddTimeUnit = _.get(this.config, ['DYNAMIC_CONFIG', 'RENT_PAYMENT', 'PAYMENT_THRESHOLD', 'ABOVE_TIME_UNIT'], 'months'),
            deducedDueDate = null;

        if (transactionAmount < thresholdAmount) {
            deducedDueDate = MOMENT().add(belowThresholdAddTimeDuration, belowThresholdAddTimeUnit).format('YYYY-MM-DD');
        } else {
            deducedDueDate = MOMENT().add(aboveThresholdAddTimeDuration, aboveThresholdAddTimeUnit).format('YYYY-MM-DD');
        }

        return deducedDueDate;
    }

    /**
     * Fetch due date from DB and then shift it to next month
     * @param {*} params 
     */
    async markAsPaid(params) {
        let debugKey = `Op:${params.operator}_RN:${params.rechargeNumber}_CustId:${params.customerID}`;

        this.L.log("rentPayment::markAsPaid", `for params : ${debugKey}`);
        this.L.log("rentPayment::markAsPaid",`Fetching data from DB for ${debugKey}`);

        try {
            let rows = await this.billsModel.getRecordByRechargeNumberCustIdFromBillsTable(params);
            this.L.log("rentPayment::markAsPaid",`${rows.length} Record found in DB for ${debugKey}`);
            
            if(rows.length == 1) {
                let dbDueDate = rows[0].due_date;
                let dueDateCurrDateDiff = MOMENT(dbDueDate).utc().diff(MOMENT(), 'days'); 

                if(dueDateCurrDateDiff < 40) { // To stop recursive due date shifting in each api call
                    let updatedDueDate = MOMENT(dbDueDate).utc().add(1,'months').format('YYYY-MM-DD');
                    let 
                        query = `UPDATE ${this.getTableName(_.toLower(params.operator))} SET due_date = ?, status = ? WHERE id = ?`,
                        queryParams = [ updatedDueDate, _.get(this.config,['COMMON','bills_status','MARKED_AS_PAID'],15) , rows[0].id ];

                    this.L.log("rentPayment::markAsPaid",`Updating due date from ${dbDueDate} to ${updatedDueDate} for ${debugKey}`);
                    await this.billsModel.updateBillsTable(query,queryParams);
                    this.L.log("rentPayment::markAsPaid",`Data successfully updated for ${debugKey}`);
                    return 1;
                } else {
                    this.L.log('rentPayment::markAsPaid',`Not updating dueDate since dbDueDate is:${dbDueDate} and dueDateCurrDateDiff:${dueDateCurrDateDiff} >= 40`);
                    return 0;
                }
            } else {
                return 0;
            }
        } catch(error){
            this.L.critical("rentPayment::markAsPaid",`Error encountered for ${debugKey} , error: ${error}`);
            return 0;
        }
    }
}

export default RentPaymentFlow