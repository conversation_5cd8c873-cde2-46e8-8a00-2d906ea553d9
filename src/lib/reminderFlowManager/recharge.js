"use strict";

import _ from "lodash"

import <PERSON>mind<PERSON><PERSON><PERSON><PERSON><PERSON> from './base'

class Recharge<PERSON>low extends ReminderBaseFlow {
    constructor(options){
        super(options)
        this.billsModel = options.billsModel        
        this.tableName = 'plan_validity'
    }

    /* start of Notification Management classes */
    async notificationManager_updateReminderDB(params){
        this.L.log("RechargeFlow:notificationManager_updateReminderDB",`Updating for ${params.traceKey}`)
        let {query,queryParams} = this.notificationManager_getUpdateQueryParams(params);
        await this.billsModel.updatePlanValidityTable(query,queryParams);
    }

    notificationManager_getUpdateQueryParams(params){
        let 
            tableName = this.getTableName(params.operator,params.paytype),
            query,
            values ;
            if(_.get(params, 'v3UpdateNotificationStatus', null)) {
                query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND recharge_number = ? AND operator = ? AND service = ? ;`;            
            }else{
                query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND recharge_number = ? AND operator = ? AND service = ? AND notification_status!=0;`;
            }
            values = [params.notificationStatus, params.customerId,params.rechargeNumber,_.toLower(params.operator),_.toLower(params.service)];

        return {query:query,queryParams:values}
    }

    getTableName(operator,paytype){
        return this.tableName
    }
    notificationManager_getBill(cb,params){
        let 
            tableName = this.getTableName(params.operator,params.paytype),
            query = `SELECT * FROM ${tableName} WHERE customer_id = ? AND operator = ? AND service = ? AND recharge_number = ?`,
            queryParams = [
                params.customerId,
                _.toLower(params.operator),
                _.toLower(params.service),
                params.rechargeNumber
            ];
        this.L.log("BillPaymentsFlow:notificationManager_getBill",` for ${params.traceKey}`);
        return this.billsModel.getPlanValidityData(cb, query,queryParams)        
    }
}

export default RechargeFlow
