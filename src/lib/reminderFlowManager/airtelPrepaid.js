"use strict";

import _ from 'lodash'

import RechargeFlow from './recharge'

class AirtelPrepaidFlow extends RechargeFlow {
    constructor(options){
        super(options)
        this.airtelTableName = 'bills_airtelprepaid'
    }

    /* start of Notification Management classes */
    async notificationManager_updateReminderDB(params){
        await super.notificationManager_updateReminderDB(params);

        this.L.log("AirtelPrepaidFlow:notificationManager_updateReminderDB",`for ${params.traceKey}`)
        let {query,queryParams} = this.notificationManager_getUpdateQueryParams_BR(params);
        await this.billsModel.updateBillsTable(query,queryParams);
    }

    /**
     * Private method
     * @param {*} params 
     */
    notificationManager_getUpdateQueryParams_BR(params){
        let self = this,
            tableName = this.getAirtelTableName(),
            query,
            values ;
            if(_.get(params, 'v3UpdateNotificationStatus', null)) {
                query = `UPDATE ${tableName} SET ${params.stopBillFetch ? 'status =' + _.get(self.config, 'COMMON.bills_status.DISABLED', 7) + ',' : ''} notification_status = ? WHERE customer_id = ? AND recharge_number = ? AND operator = ? AND service = ? ;`;            
            }else{
                query = `UPDATE ${tableName} SET notification_status = ? WHERE customer_id = ? AND recharge_number = ? AND operator = ? AND service = ? AND notification_status!=0;`;
            }
            values = [params.notificationStatus, params.customerId,params.rechargeNumber,_.toLower(params.operator),_.toLower(params.service)];

        return {query:query,queryParams:values}
    }

    /**
     * Private Method
     */
    getAirtelTableName(){
        return this.airtelTableName
    }
}

export default AirtelPrepaidFlow