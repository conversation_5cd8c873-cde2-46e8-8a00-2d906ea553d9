import L from 'lgr'

class Util {
    static customError({ status, message, stack }) {
        const error = new Error(message)
        error.status = error.statusCode = status;
        error.stack = stack;

        return error;
    }

    static sleep(delay) {

        if (typeof delay != "number") {
            throw new Error("delay should be number");
        }

        return new Promise((resolve, reject) => {
            setTimeout(() => {
                resolve();
            }, delay);
        })
    }

    static getEndPointForStats(url) {
        try {
            let qsSplitArray = url.split('?');
            let slashSplitArray = qsSplitArray[0].split('/');

            let endPoint = '';

            let len = slashSplitArray.length;
            
            for (let i = 3; i < len; i++) {
                endPoint += `/${!isNaN(slashSplitArray[i]) ? "XXXXX" : slashSplitArray[i]}`;
            }

            return endPoint;
        } catch (error) {
            L.error('[src/lib/requestWrapper/util/getEndPointForStats] error: ', error);
            return '';
        }
    }
}

export default Util;