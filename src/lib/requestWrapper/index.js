import Request from 'request-promise'
import { get, cloneDeep } from 'lodash';
import L from 'lgr';

import PublishStats from '../publishStats'
import CONSTANT from './constant';

import Util from './util';

class RequestWrapper {
    constructor(options = {}) {
        this.retryCount = options.count || 3;

        this.validateConnectionObject(options);

        this.sleepAfter5xx = options.sleepAfter5xx || 2000;
        this.sleepAfterConnectionTimeout = options.sleepAfterConnectionTimeout || 2000;
        this.sleepAfterReadTimeDout = options.sleepAfterReadTimeDout || 2000;
    }

    /**
     * @param {*} apiOpts.resolveWithFullResponse give response with http statusCode in res.statusCode
     * @param {*} apiOpts.json give response in json format response.body
     * @param {*} retryCount 
     */
    async hitRequest(options, retryCount = 0) {
        let requestStartTime = new Date().getTime();

        try {
            L.verbose(`[src/lib/requestWrapper] requestOptions`, options);

            let { statusCode: status, body } = await Request(this.validateReqOptions(options));

            if (typeof body == "string") {
                try {
                    body = JSON.parse(body);
                } catch (error) {
                    let { message, stack } = error;
                    throw Util.customError({ status, message, stack });
                }
            }

            this.publishStats({ status, requestStartTime, options });
            return body;
        } catch (err) {

            let { status, sleepTime, isRetry, message, stack } = this.parseError(err);

            L.error(`for uri: ${options.url}, status: ${status}, sleeptime: ${sleepTime}, isretry: ${isRetry}, errMessage: ${message}`);

            this.publishStats({ status, requestStartTime, options });

            if (isRetry && retryCount < this.retryCount) {
                await Util.sleep(sleepTime);
                return await this.hitRequest(options, retryCount + 1);
            }

            throw Util.customError({ status, message, stack });
        }
    }

    parseError(err = {}) {
        let status = err.statusCode || err.status,
            sleepTime = 0,
            isRetry = false,
            message = err.message,
            stack = err.stack,
            code = get(err, ["error", "code"], null);

        if (err.statusCode > 499) {
            isRetry = true;
            sleepTime = this.sleepAfter5xx;
        } else if (code) {
            let connect = err.error.connect;
            status = code;

            if (CONSTANT.CONNECT_ERROR_CODES.includes(code)) {
                isRetry = true;

                if (connect) {
                    sleepTime = this.sleepAfterConnectionTimeout;
                    status = "CONNECT_ETIMEOUT";
                } else {
                    sleepTime = this.sleepAfterReadTimeDout;
                }
            }
        }
        
        return { status, sleepTime, isRetry, message, stack };
    }

    validateReqOptions(options) {
        if (!options.timeout) {
            throw new Error("Please set API timeout");
        }

        let requestOption = cloneDeep(options)

        requestOption.resolveWithFullResponse = true;
        requestOption.json = true;

        return requestOption;
    }

    /**
     * 
     * @param status it will 2xx, 4xx, 5xx, ETIMEOUT, EAIAGAIN, CONNECT_ETIMEOUT
     * @param {*} param0 
     */
    publishStats({ status, requestStartTime, options }) {
        try {
            
            let statsObj = { REQUEST_TYPE: this.requestType };

            if (status) statsObj.STATUS = status;
            if (get(options, "url", "")) statsObj.uri = Util.getEndPointForStats(options.url);

            PublishStats.publishCounter(1, statsObj);
            PublishStats.publishLatency(requestStartTime, statsObj);

        } catch (error) {
            L.error("[src/lib/requestWrapper/index/publishStats] error: ", error)
        }
    }

    validateConnectionObject(options) {
        if (options.requestType) {
            this.requestType = options.requestType;
        } else {
            throw new Error("there is no requestType");
        }
    }
}

export default RequestWrapper;