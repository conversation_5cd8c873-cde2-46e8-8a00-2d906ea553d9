"use strict";

import _ from 'lodash'
import REQUEST from 'request'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator'
import <PERSON><PERSON>EN<PERSON> from 'moment'

/**
 * Notification Libary will perfrom all operation related to data manipuation
 */
class NotificationLibrary {
    constructor(options) {
        this.L = options.L;
    }

    /**
     * Methos will be used to prepare notification configuration data
     * @param {*} configuration | notification_configuration table data 
     * @param {*} templates | notification_template table data
     */
    prepareNotificationConfiguration(configuration, templates, config, merchantData) {
        let notificationConfiguration = {},
            templateConfiguration = {},
            template_type = _.get(config, ['NOTIFICATION_SERVICE_CONFIG', "TEMPLATE_TYPE"], {}),
            notification_type = _.get(config, ['NOTIFICATION_SERVICE_CONFIG', "NOTIFICATION_TYPE"], {});

        //Active map configuration
        _.set(notificationConfiguration, ['active', 'product_map'], {});
        _.set(notificationConfiguration, ['active', 'merchant_map'], {});
        _.set(notificationConfiguration, ['active', 'vertical_map'], {});
        _.set(notificationConfiguration, ['active', 'default_map'], []);

        //Inactive map configuration
        _.set(notificationConfiguration, ['inactive', 'product_map'], {});
        _.set(notificationConfiguration, ['inactive', 'merchant_map'], {});
        _.set(notificationConfiguration, ['inactive', 'vertical_map'], {});
        _.set(notificationConfiguration, ['inactive', 'default_map'], []);

        //Merchant notification config with templates
        _.set(notificationConfiguration, ['merchant'], {});

        //Preparing template map, we will use it to prepare notification configuration
        templates.forEach(element => {
            _.set(templateConfiguration, element.id, element);
            _.set(element, 'template_type', _.get(template_type, element.template_type, null));
            _.set(element, 'notification_type', _.get(notification_type, element.notification_type, null));
        });

        //preparing merchant notification configuration and add templates
        merchantData.forEach(element => {
            let merchant_id = _.get(element, 'merchant_id', null),
                email_id = _.get(element, 'email_id', ""),
                mobile_no = _.get(element, "mobile_no", ""),
                template_index = _.get(element, 'template_id', null),
                template = _.get(templateConfiguration, template_index, {}),
                config = notificationConfiguration['merchant'];

            //Set mobile number and email_id for merchant configuration
            _.set(config, [merchant_id, "email_id"], email_id);
            _.set(config, [merchant_id, "mobile_no"], mobile_no);

            if (!config[merchant_id]['templates']) {
                config[merchant_id]['templates'] = [];
            }
            config[merchant_id]['templates'].push(template);
        });

        //Preparing notification configuration map, we will use it to find template based on product_id, merchant_id or vertical_id
        configuration.forEach(element => {

            let product_id = _.get(element, 'product_id', null),
                merchant_id = _.get(element, 'merchant_id', null),
                vertical_id = _.get(element, 'vertical_id', null),
                status = _.get(element, 'status', 1), //Default status is 1 active
                template_index = _.get(element, 'template_id', null),
                template = _.get(templateConfiguration, template_index, {}),
                config = status == 0 ? _.get(notificationConfiguration, 'inactive', {}) : _.get(notificationConfiguration, 'active', {}); //Deciding where to add config template

            /* Keep pushing templates in their respective maps to decide priority 
             ** product_id = 0, merchant_id = 0, vertical_id = 0
             */

            if (product_id && product_id != 0) {

                if (!config['product_map'][product_id]) {
                    config['product_map'][product_id] = [];
                }

                config['product_map'][product_id].push(template);
            } else if (merchant_id && merchant_id != 0) {

                if (!config['merchant_map'][merchant_id]) {
                    config['merchant_map'][merchant_id] = [];
                }

                config['merchant_map'][merchant_id].push(template);
            } else if (vertical_id && vertical_id != 0) {

                if (!config['vertical_map'][vertical_id]) {
                    config['vertical_map'][vertical_id] = [];
                }

                config['vertical_map'][vertical_id].push(template);
            } else {
                config['default_map'].push(template);
            }
        });
        return notificationConfiguration;
    }

    /**
     * Method is used to check can we send notification for order, 
     * condition order_items_count should be equal to aerospike items_count and 
     * @param {*} rechargeData 
     */
    isRechargeDataNotifiable(rechargeData, IN_STATUS_CODE) {
        let self = this,
            item_count = _.get(rechargeData, 'orderInfo_order_item_count', null),
            status = self.isStatus(rechargeData, IN_STATUS_CODE);


        if (!item_count) {
            self.L.error("Notification Service library: isRechargeDataNotifiable(): item_count not found:", item_count);
            item_count = _.get(rechargeData, 'orderInfo_order_item_count', 1);
        }
        if (!status) {
            return 2;
        }
        if (item_count == 1) {
            return 0;
        } else {
            return 1;
        }
    }

    /**
     * Method will check if we can notify given order items, Here we will check item count only
     * @param {*} order_items 
     * @param {*} rechargeData 
     */
    canNotify(order_items, rechargeData) {
        let order_item_count = _.get(rechargeData, 'orderInfo_order_item_count', 1),
            new_item_count = _.isArray(order_items) ? (order_items.length + 1) : 1;

        if (order_item_count <= new_item_count) { //Keeping <= for safe side although its not required
            return true;
        } else {
            return false;
        }
    }

    /**
     * Method will return Either SUCCESS\FAILED or PENDING status based on response code
     * @param {*} item Contains message data 
     * @param {*} IN_STATUS_CODE | All IN status codes
     */
    isStatus(item, IN_STATUS_CODE) {
        let in_status = _.get(item, 'inStatusMap_responseCode', null),
            success_codes = _.get(IN_STATUS_CODE, 'SUCCESS', []),
            failure_codes = _.get(IN_STATUS_CODE, 'FAILED', []),
            pending_codes = _.get(IN_STATUS_CODE, 'PENDING', []);

        if (success_codes.indexOf(in_status) > -1) {
            return "SUCCESS";
        } else if (failure_codes.indexOf(in_status) > -1) {
            return "FAILED";
        } else if (pending_codes.indexOf(in_status) > -1) {
            return "PENDING";
        } else {
            return null;
        }
    }

    /**
     * Method will apply and prepare all templates and return notifiable object
     * @param {*} orders | order items
     * @param {*} notificationConfig | notification configuration
     */
    collectAllTemplates(orders, notificationConfig, config) {
        let self = this,
            configuredNotification = [];
        self.L.info("Notification Service Library : collectAllTemplates(): Start collecting notification data", orders);

        try {
            Object.keys(orders).forEach(order_id => {
                Object.keys(orders[order_id]).forEach(status => {
                    let items = orders[order_id][status],
                        templates = [],
                        merchant_id = null,
                        itemLength = items.length;


                    for (let i = 0; i < itemLength; i++) {
                        templates = self.isConfiguredWithPanel(items[i], notificationConfig, status);
                        merchant_id = _.get(items[i], "orderInfo_merchant_id", null); //keeping this for merchant template

                        if (_.isArray(templates) && templates.length > 0) {
                            break;
                        }
                    }
                    

                    //Here we will prepare notification
                    templates.forEach(template => {
                        let notifyObject = self.prepareItemNotification(items, template, config);

                        if (notifyObject) {
                            configuredNotification.push(notifyObject);
                        }
                    });

                    //Adding merchant Notification
                    let merchantTemplates = self.merchantNotificationTemplates(merchant_id, notificationConfig, status);

                    if (_.isArray(merchantTemplates) && merchantTemplates.length > 0) {

                        merchantTemplates.forEach(template => {
                            let type = _.get(template, 'notification_type', '');
                            if (type === "SMS" || type === "EMAIL") {
                                let notifyMerchantObject = self.prepareMerchantNotification(template, items, merchant_id, notificationConfig, config);

                                if (notifyMerchantObject) {
                                    configuredNotification.push(notifyMerchantObject);
                                }
                            } else {
                                self.L.error("Notification Service Library : collectAllTemplates(): Merchant template type is invalid ", template);
                            }
                        });
                    } else {
                        self.L.info("Notification Service Library : collectAllTemplates(): Merchant template not configured");
                    }
                });
            });
        } catch (ex) {
            self.L.critical("Notification Service Library : collectAllTemplates(): Object keys error =>", ex);
        }
        return configuredNotification;
    }

    /**
     * Method will collect all templates , which can be applied for merchant
     * @param {*} merchant_id |merchant-id for success item
     * @param {*} notificationConfig | contains notification configuration
     * @param {*} template_type | status of request
     */
    merchantNotificationTemplates(merchant_id, notificationConfig, template_type) {
        let self = this,
            templates = [],
            merchantMap = _.get(notificationConfig, 'merchant', {});

        if (merchant_id) {
            let configureTempaltes = _.get(merchantMap, [merchant_id, "templates"], []);
            templates = configureTempaltes.filter(element => element.template_type == template_type);
        } else {
            self.L.error("Notification Service library : merchantNotificationTemplates():merchant_id is invalid=>", merchant_id);
        }
        return templates;
    }

    /**
     * Method will find all unique orders
     * @param {*} order_items | contains all items with expired notification time
     */
    findUniqueIds(order_items) {
        let orderIds = [],
            order_id = null;

        order_items.forEach(item => {
            order_id = _.get(item, "orderInfo_order_id", null);
            if (order_id) {
                orderIds.push(order_id);
            }
        });

        return _.uniq(orderIds);
    }

    /**
     * Method will check, if item notification configured with panel or not
     * if configuration found in  notification['inactive'] then return false
     * else return true with template id found in notification['active'],
     * if configuration also not there then send default template id for vertical 
     * @param {*} item | Notification record data
     * @param {*} notification | Notification record data
     * @param {*} template_type | tempate type ,SUCCESS, FAILURE
     */
    isConfiguredWithPanel(item, notificationConfig, template_type) {

        let self = this,
            activeNotificationConfig = _.get(notificationConfig, 'active', {}),
            inactiveNotificationConfig = _.get(notificationConfig, 'inactive', {}),
            activeTemplates = [],
            index = 0,
            inactiveTemplates = [];

        /**
         * Find templates, where notification has to be send 
         */

        activeTemplates = self.findTemplatesForNotification(item, activeNotificationConfig);
        inactiveTemplates = self.findTemplatesForNotification(item, inactiveNotificationConfig);

        if (activeTemplates.length > 0 || inactiveTemplates.length > 0) {

            //Removing inactive templates 
            inactiveTemplates.forEach(inactiveElement => {
                index = activeTemplates.findIndex(activElement => activElement.template_id == inactiveElement.template_id);
                if(index!==-1){
                    activeTemplates.splice(index, 1);
                }
            });
        }

        //Filter template based on template_type
        activeTemplates = activeTemplates.filter(element => element.template_type == template_type);

        return activeTemplates;
    }

    //Method will only use to find specific config
    findTemplatesForNotification(item, notifyConfig) {
        let product_id = _.get(item, 'catalogProductID', null),
            vertical_id = _.get(item, 'productInfo_verticalId', null),
            merchant_id = _.get(item, 'orderInfo_merchant_id', null),
            notifyTemplate = [];

        if (_.get(notifyConfig, ['product_map', product_id], null)) {
            notifyTemplate = _.get(notifyConfig, ['product_map', product_id]);
        } else if (_.get(notifyConfig, ['merchant_map', merchant_id], null)) {
            notifyTemplate = _.get(notifyConfig, ['merchant_map', merchant_id], null);
        } else if (_.get(notifyConfig, ['vertical_map', vertical_id], null)) {
            notifyTemplate = _.get(notifyConfig, ['vertical_map', vertical_id], null);
        } else {
            notifyTemplate = _.get(notifyConfig, ['default_map'], null)
        }

        return notifyTemplate;
    }

    /**
     * Method will use to created order_id based map or recharge data
     * @param {*} ordersData | order data
     */
    appendCustomerInformation(ordersData) {
        let self = this,
            orderCustomerDetails = {};

        self.L.info("Notification Service library :appendCustomerInformation(): Start preparing customer info map for orders");

        if (ordersData.length > 0) {
            ordersData.forEach(element => {

                let metaData =  _.get(element,"metaData", null),
                    meta_data = {};

                try{
                    meta_data = typeof metaData =='string' ? JSON.parse(metaData): metaData;
                }catch(ex){
                    self.L.error("appendCustomerInformation : Invalid meta data",ex);
                }
                let customerInfo = {
                    order_id: _.get(element, "orderInfo_order_id", null),
                    customer_id: _.get(element, "customerInfo_customer_id", null),
                    customer_phone: _.get(element, "customerInfo_customer_phone", null),
                    customer_email: _.get(element, "customerInfo_customer_email", null),
                    currentGw : _.get(element, "currentGw", null),
                    paymentInfo_paymentMode :  _.get(element, "paymentInfo_paymentMode", null),
                    paymentInfo_paymentBank :  _.get(element, "paymentInfo_paymentBank", null),
                    meta_data :meta_data
                }

                if (customerInfo.order_id) {
                    _.set(orderCustomerDetails, customerInfo.order_id, customerInfo);
                } else {
                    self.L.error("Notification Service Library: appendCustomerInformation(): product_id is empty, product_id=>", customerInfo.product_id);
                }
            });
        } else {
            self.L.error("Notification Service Library: appendCustomerInformation(): order data is empty =>", ordersData);
        }
        return orderCustomerDetails;
    }

    /**
     * Method will club all items based on order_id and success type for notification
     * @param {*} order_items contains item array
     */
    clubOrders(customerInfoMap, order_items, IN_STATUS_CODE, productData) {
        let self = this,
            orders = {};

        self.L.info("Notification Service Library : clubOrders(): Start combining order items");
        order_items.forEach(item => {
            let order_id = _.get(item, "orderInfo_order_id", null),
                product_id = _.get(item, 'catalogProductID', null),
                reqType = self.isStatus(item, IN_STATUS_CODE);

            if (order_id && productData[product_id]) {
                if (!orders[order_id]) {
                    orders[order_id] = {};
                }
                if (reqType && customerInfoMap[order_id]) {
                    if (!orders[order_id][reqType]) orders[order_id][reqType] = [];

                    //setting CVR data at item level for templating
                    _.set(item, "orderInfo_merchant_id", _.get(productData[product_id], "merchant_id", null));
                    _.set(item, "productInfo_verticalId", _.get(productData[product_id], "vertical_id", null));
                    _.set(item, "productInfo_operator", _.get(productData[product_id], "operator", null));
                    _.set(item, "productInfo_service", _.get(productData[product_id], "service", null));
                    _.set(item, "productInfo_operator_label", _.get(productData[product_id], "operator_label", null));
                    _.set(item, "productInfo_category_id", _.get(productData[product_id], "category_id", null));
                    _.set(item, "productInfo_brand", _.get(productData[product_id], "brand", null));
                    _.set(item, "productData", productData[product_id]);

                    //Setting customer info details for notification configuration
                    _.set(item, "customerInfo_customer_id", _.get(customerInfoMap[order_id], "customer_id", null));
                    _.set(item, "customerInfo_customer_phone", _.get(customerInfoMap[order_id], "customer_phone", null));
                    _.set(item, "customerInfo_customer_email", _.get(customerInfoMap[order_id], "customer_email", null));
                    _.set(item, "currentGw", _.get(customerInfoMap[order_id], "currentGw", null));
                    _.set(item, "paymentInfo_paymentMode", _.get(customerInfoMap[order_id], "paymentInfo_paymentMode", null));
                    _.set(item, "paymentInfo_paymentBank", _.get(customerInfoMap[order_id], "paymentInfo_paymentBank", null));
                    _.set(item, "meta_data", _.get(customerInfoMap[order_id], "meta_data", null));

                    orders[order_id][reqType].push(item);
                } else {
                    self.L.error("Notification Service Library : clubOrders(): invalid request type or no customer data for order_id :", reqType, order_id);
                }
            } else {
                self.L.error("Notification Service Library : clubOrders(): invalid order_id or product Map=>", order_id, _.get(productData, product_id));
            }
        });
        return orders;
    }

    /**
     * method will prepare merchant notification
     * @param {*} merchantTemplates | merchant templates
     * @param {*} merchant_id | merchant id
     * @param {*} notificationConfig | contains notification configuration details 
     */
    prepareMerchantNotification(template, items, merchant_id, notificationConfig, config) {
        let self = this,
            item = items[0],
            merchant_email_id = _.get(notificationConfig, ["merchant", merchant_id, "email_id"]),
            merchant_mobile_no = _.get(notificationConfig, ["merchant", merchant_id, "mobile_no"]),

            type = _.get(template, 'notification_type', ''),
            receipent = null,

            //Preparing payload data for all items
            payLoad = {
                items: [],
                category_id: _.get(item, 'productInfo_category_id', null), //Either we should pick this from item or provided by product
                service: _.get(item, 'productInfo_service', null) //Either we should pick this from item or provided by product
            },

            //Notification information perpared only by considering first items details in one category
            notificationRecord = {
                type: _.get(template, 'notification_type', ''),
                recipients: receipent,
                template_id: _.get(template, 'template_id', null)
            };

        self.L.info("Notification Service Library: prepareMerchantNotification(): preparing merchant level configuration");
        //Update notification type based reciepent
        if (type === "EMAIL") {
            notificationRecord.recipients = merchant_email_id;

        } else if (type === "SMS") {
            notificationRecord.recipients = merchant_mobile_no;
        }

        items.forEach(itemK => {
            let itemPayload = {
                price: _.get(itemK, 'userData_amount', null),
                recharge_number: _.get(itemK, 'userData_recharge_number', null),
                operator: _.get(itemK, "productInfo_operator_label", null),
                name: _.get(itemK, "productInfo_brand", null)
            };

            payLoad.items.push(itemPayload);
        });

        return self.prepareFinalNotifyObject(payLoad, notificationRecord, item, config);

    }

    /**
     * Method will prepare data for notification and return for notification
     * @param {*} item  contains message data for notification
     * @param {*} config Service config 
     * @param {*} template object contains information of notification_template and notification type
     * @param {*} productMap CVR operator map used to prepare notification data
     */
    prepareItemNotification(items, template, config) {
        let self = this,
            item = items[0],
            type = _.get(template, 'notification_type', ''),
            key = {
                "PUSH": "customerInfo_customer_id",
                "SMS": "customerInfo_customer_phone",
                "EMAIL": "customerInfo_customer_email"
            },
            //Preparing payload data for all items
            payLoad = {
                items: [],
                category_id: _.get(item, 'productInfo_category_id', null),
                service: _.get(item, 'productInfo_service', null),
                payments : [],
                context : null,
                collectableAmount: 0,
                grandtotal: 0,
                refund_text: "",
                id :_.get(item, 'orderInfo_order_id', null),
                updated_at : MOMENT(),
                phone : _.get(item, "customerInfo_customer_phone", null)
            },

            //Notification information perpared only by considering first items details in one category
            notificationRecord = {
                type: _.get(template, 'notification_type', ''),
                recipients: _.get(item, key[type], null), // Setting type of notification 
                template_id: _.get(template, 'template_id', null)
            };

        //Initialize context refund item aaray
        _.set(payLoad, "context.refund", []);

        self.L.info("Notification Service Library: prepareItemNotification(): preparing item level configuration");
        items.forEach(itemK => {

            let itemPayload = { 
                price: _.get(itemK, 'userData_amount', null),
                name: _.get(itemK, "productInfo_brand", null),
                operator: _.get(itemK, "productInfo_operator_label", null),
                max_refund : _.get(itemK, 'userData_amount', null),
                product :{
                    attributes : _.get(itemK, ["productData","attributes"],{}),
                    brand : _.get(itemK, ["productData","brand"], ""),
                    id : _.get(itemK, 'productData.product_id', "")
                },
                merchant_id :_.get(itemK, 'orderInfo_merchant_id', null),
                vertical_id : _.get(itemK, 'productInfo_verticalId', null),
                status : 6, //As we always have check on status in case of failure

                fulfillment_req: {
                    recharge_number:_.get(itemK, "userData_recharge_number", null),
                    price: _.get(itemK, 'userData_amount', null),
                },
                meta_data : _.get(itemK, "meta_data",{}),
                promo_code: null //We will update it once we will get promo details
            };
            
            let meta_fields ={
                amount: _.get(itemK, 'userData_amount', null),
                recharge_number: _.get(itemK, "userData_recharge_number",null),
                price: _.get(itemK, 'userData_amount', null),
            }

            _.extend(itemPayload.meta_data,meta_fields);

            let paytmentData ={
                    order_id:_.get(item, 'orderInfo_order_id', null),
                    payment_method:_.get(item, "paymentInfo_paymentMode", null),
                    payment_bank : _.get(item, "paymentInfo_paymentBank", null)
            };

            //Adding payment data
            payLoad.payments.push(paytmentData);

            let refundPayload = {
                amount : itemPayload.price,
                gateway : _.get(itemK, 'currentGw', null)
            };

            _.set(payLoad,"collectableAmount", (parseFloat(payLoad.collectableAmount)+parseFloat(itemPayload.price)) );
            
            //Set refund amount in context for each item
            payLoad.context.refund.push(refundPayload);
            
            //At least one recipient will be set from any item
            if (!notificationRecord['recipients']) {
                notificationRecord['recipients'] = _.get(item, key[type], null) //If recipients is null the set from other items
            }
            payLoad.items.push(itemPayload);

        });

        //Calculating runfud amount for orders
        _.set(payLoad,"grandtotal", payLoad.collectableAmount);

        self.L.info("Notification :: Paytload:",JSON.stringify(payLoad));
        return self.prepareFinalNotifyObject(payLoad, notificationRecord, item, config);
    }

    /**
     * Method finalise notification object merchant and customer both
     * @param {*} payLoad | contains payload data
     * @param {*} notificationRecord | notification records object
     * @param {*} item | item some dummy values
     * @param {*} config | contains configuration details
     */
    prepareFinalNotifyObject(payLoad, notificationRecord, item, config) {
        let self = this,
            apiOpts = null;
        if (_.get(notificationRecord, 'recipients', null)) {
            let notificationData = {};

            try {
                notificationData = {
                    "template_type": _.get(notificationRecord, 'type', 'PUSH').toLowerCase(), //Considering default as push notificatiion
                    "template_id": _.get(notificationRecord, 'template_id', null),
                    "options": {
                        "notificationOpts": {
                            "recipients": VALIDATOR.toString(_.get(notificationRecord, 'recipients', null))
                        },
                        "type": "async",
                        "data": payLoad
                    }
                };
            } catch (ex) {
                self.L.error("Notification Service Library : prepareItemNotification(): recipients invalid", ex);
                return apiOpts;
            }

            //Need help to prepare this structure
            //self.prepareNotificationOptionData(notificationData, items, template, config, operatorLabelMap);

            //We are waiting till morning for notification adding minimum hours to current time
            let date = new Date(),
                time_to_send = date.getHours(),
                send_at,

                timeRange = {
                    gte: _.get(config, ["NOTIFICATION_SERVICE_CONFIG", "STOP_TIME_RANGE", "GTE"], 0),
                    lte: _.get(config, ["NOTIFICATION_SERVICE_CONFIG", "STOP_TIME_RANGE", "LTE"], 7)
                };

            // We are sending notification just after 7+ extra hours after 00:00 AM
            if (time_to_send >= timeRange.gte && time_to_send < timeRange.lte) {
                send_at = MOMENT().add(timeRange.lte, "hours").format("YYYY-MM-DD HH:mm:ss");
            } else {
                send_at = MOMENT().format("YYYY-MM-DD HH:mm:ss");
            }

            //Prepare  apiOpts for notification

            apiOpts = {
                "uri": _.get(config, 'NOTIFICATION.notificationapi.DIGITALNOTIFICATIONAPI', null),
                "method": "POST",
                "headers" : {
                    "Host": "digitalreminder.mkt.paytm"
                },
                "timeout": 500,
                "json": {
                    "source_id": 5,
                    "category_id": 5,
                    "recharge_number": _.get(item, 'userData_recharge_number', null),
                    "product_id": _.get(item, 'catalogProductID', null),
                    "max_retry_count": 2,
                    "retry_interval": 30,
                    "type": _.get(notificationRecord, 'type', null),
                    "template_id": _.get(notificationRecord, 'template_id', null),
                    "recipient": _.get(notificationRecord, 'recipients', null),
                    "send_at": send_at,
                    "data": notificationData,
                    "rules": {
                        "condition": `category_id=5 and source_id=5 and recharge_number='${_.get(item, 'userData_recharge_number', null)}' and product_id=${_.get(item, 'catalogProductID', null)} 
                                    and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)} and recipient='${_.get(item, 'customerInfo_customer_id', null)}'`,
                        "actions": [{
                                "status": "pending",
                                "action": "drop"
                            },
                            {
                                "status": "sent",
                                "action": "drop"
                            },
                            {
                                "status": "error",
                                "action": "drop"
                            }
                        ]
                    }
                }
            };
        } else {
            self.L.error("Receipent not found");
        }
        return apiOpts;
    }

    /**
     * Method will send notification by hitting notification API
     * @param {*} cb |callback function
     * @param {*} apiOpts | object data to notiify
     */
    sendNotification(cb, apiOpts) {
        let self = this;
        self.L.info("NotificationLibrary: sendNotification():", apiOpts);

        REQUEST(apiOpts, (error, response, body) => {
            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    self.L.error("NotificationLibrary: sendNotification(): Failed to parse notification response body:" + e);
                }
                self.L.log("Notification: Response body:",body);
            }
            if (error || (body && body.status && body.status != 200)) {
                let errorMsg = (error) ? error : ((body.error) ? body.error : "body status: " + body.status);
                self.L.error("NotificationLibrary: sendNotification(): Failed to send notification :", errorMsg);
            } else {
                self.L.info("NotificationLibrary: sendNotification(): Notification Sent Successfully : receipent, recharge_number, product_id, type",
                    _.get(apiOpts, ['json', 'recipient'], null), _.get(apiOpts, ['json', 'recharge_number'], null),
                    _.get(apiOpts, ['json', 'product_id'], null), _.get(apiOpts, ['json', 'type'], null));
            }
            cb();
        });
    }
}

export default NotificationLibrary;