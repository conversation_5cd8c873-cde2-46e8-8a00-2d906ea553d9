import {createHash} from 'crypto'
import MOMENT from 'moment'
import _ from 'lodash'
import L from 'lgr'
import utility from './'

const generateReconID = function(rn , operator , amount , due_date  , billDate   ){
   let rawData = rn
   if(_.isEmpty(operator) == false){
      rawData = rawData + "_" + _.toLower(operator)
   }
   if(amount && _.isNaN(parseFloat(amount)) == false){
    rawData = rawData + "_" + parseFloat(amount)
   }
   else{
    rawData = rawData + "_" + 0
   }
   if(due_date!=null && MOMENT(due_date).isValid() == true){
      rawData = rawData + "_" + MOMENT(due_date)
   }
   if(billDate!=null && MOMENT(billDate).isValid() == true){
    try{
        rawData = rawData + "_" + MOMENT(billDate).startOf('day')
    }catch(err){
        utility._sendMetricsToDD(1,["REQUEST_TYPE:RECON_ID_GENERATION","STATUS:ERROR"])
        L.error("Error while generating ReconID for ",rn,operator,amount,due_date,billDate)
    }
   } 
   let hash = createHash('sha1').update(rawData).digest('base64');

   L.info("generating Hash for ",rawData)

   return hash
}


const setReconIdInString = function(extra , recon_id , user_type="RU"){
    /** If extra is null or string */
    if(_.isEmpty(extra)){
        return JSON.stringify({
            recon_id , user_type
        })
    }
    try{
        let tempExtra
        if(typeof extra != "object")
            tempExtra = JSON.parse(extra)
        else tempExtra = extra
        if(tempExtra == null){
            return JSON.stringify({
                recon_id,
                user_type
            })
        }else{
            
            if(_.isString(tempExtra)){
                /** This should not be required since we have aleady parsed the string
                 * But in some cases the string was stringified twice , therefore will need to be parsed twice as well. 
                 */
                tempExtra = JSON.parse(tempExtra)
            }
            _.set(tempExtra , 'recon_id',recon_id)
            _.set(tempExtra , 'user_type',user_type)
            return JSON.stringify(tempExtra)
        }
    }catch(err){
        return JSON.stringify({
            recon_id,
            user_type
        })
    }
}

export default {
    generateReconID : generateReconID,
    setReconIdInString : setReconIdInString
}