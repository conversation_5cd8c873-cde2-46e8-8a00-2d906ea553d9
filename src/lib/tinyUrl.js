import REQUEST from 'request'
import _ from 'lodash'
import utility from './datadog'
class TinyUrl {
	createShortUrl(callback , config , originalUrl) {
		let tinyUrlApi = _.get(config,'TINY_URL_API',null),
			authKey = _.get(config, 'TINY_URL_AUTHORIZATION_KEY', null);
		if(tinyUrlApi == null || authKey == null || originalUrl == null) {
			let errorMsg = 'Value of config or original url cannot be null';
			return callback(errorMsg , null);
		}
		tinyUrlApi = tinyUrlApi+"/"+encodeURIComponent(originalUrl);
		let apiOpts = {
				"uri": tinyUrlApi,
				"method": "GET",
	            "headers": {
	            	"Authorization": authKey,
	            	"Accept": "application/json",
	            	"Cache-Control": "no-cache"
	            }
		};
		let latencyStart = new Date().getTime();
		REQUEST(apiOpts, (error, response, body) => {
			utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'CREATE_TINY_URL',
                'URL': _.get(config,'TINY_URL_API',null)
            });
	    	let shortLinkUrl = null,
	    		errorMsg = null;
	        if (error || (body && body.status && body.status != 200) || response.statusCode !== 200) {
	            errorMsg = (error) ? error : ((body.error) ? body.error : '');
	            errorMsg = 'Error in fetching tiny url for original url '+ errorMsg;
	        } else if (body && typeof body === 'string') {
	            try {
	                shortLinkUrl = JSON.parse(body).shortUrl;
	                if(shortLinkUrl == undefined) {
	                	errorMsg = 'Short url is not defined '+originalUrl;
	                	shortLinkUrl = null;
	                }
	            } catch(error) {
	                errorMsg = 'Error in parsing of data fetched: '+ error;
	            }
	        } else {
	            errorMsg = 'Response body is not string '+originalUrl;
	        }
	        return callback(errorMsg, shortLinkUrl);
	        });
		// let shortLinkUrl = 'https://m.paytm.me/msg';
		// let errorMsg = null;
		// return callback(errorMsg, shortLinkUrl);
	}
}

export default {
	TinyUrl
}