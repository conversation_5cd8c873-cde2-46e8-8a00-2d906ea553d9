"use strict";


import _ from 'lodash';
import UTIL from 'util'
import uuidv1 from 'uuidv1'
import Request<PERSON>rapper from './requestWrapper'
import REQUEST from 'request'
import utility from '.';
import Logger from './logger';


class BillPush {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;

        this.requestWraper = new RequestWrapper({
            requestType: "BILL_PUSH",
            retryCount: 3
        });

        this.appName = options.appName;
        this.logger = new Logger(options);
    }

    pushToRegistrationProcess(ref, record, payLoad, source, flag = 'reminder') {
        let self = this;
        let finalPayload = self.preparePayload(record, payLoad, source);
        const blockedPaytype = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPE'], ['prepaid']);
        if (blockedPaytype.includes(_.get(finalPayload, 'paytype', null))) {
            self.L.log(`pushToRegistrationProcess:: Skipping bill push with paytype ${_.get(record, 'paytype', null)} for recharge number ${_.get(finalPayload, 'rechargeNumber', null)
                }`);
            return;
        } else {
            self.publishBillPushUser(ref, finalPayload, source, flag);
        }
    }

    preparePayload(record, payLoad, source) {
        let userData = _.get(record, 'user_data', null);
        if (typeof userData == 'string') {
            try {
                userData = JSON.parse(userData);
            } catch (e) {
                userData = {};
            }
        }

        let productId = _.get(record, 'productId', _.get(record, 'product_id', null));

        let newRecord = {
            "operation": "Create",
            "productId": _.get(record, 'productId', _.get(record, 'product_id', null)),
            "rechargeNumber": _.get(record, 'rechargeNumber', _.get(record, 'recharge_number', null)),
            "source": source ? source : "reminder",
            "operator": _.get(record, 'operator', null),
            "onboardingOperator": "euronet",
            "service": _.get(record, 'service', null),
            "customerId": _.get(record, 'customerId', _.get(record, 'customer_id', null)),
            "paytype": _.get(record, 'paytype', null),
            "rechargeNumber2": _.get(userData, 'recharge_number_2', null),
            "rechargeNumber3": _.get(userData, 'recharge_number_3', null),
            "rechargeNumber4": _.get(userData, 'recharge_number_4', null),
            "rechargeNumber5": _.get(userData, 'recharge_number_5', null),
            "rechargeNumber6": _.get(userData, 'recharge_number_6', null),
            "rechargeNumber7": _.get(userData, 'recharge_number_7', null),
            "rechargeNumber8": _.get(userData, 'recharge_number_8', null),
            "mobileNo": _.get(record, 'customerMobile', _.get(record, 'customer_mobile', null)),
            "circle": _.toLower(_.get(this.config, ['CVR_DATA', productId, 'circle'], null)),
        }
        return newRecord;
    }

    getKeyForKafka(payload) {
        // RECHARGE_NO_SERVICE_OPERATOR_PAYTYPE_CIRCLE_OPERATION
        let key = ''; 
        if (_.get(payload, 'rechargeNumber')) key += _.get(payload, 'rechargeNumber');
        if (_.get(payload, 'service')) key += (key ? '_' : '') + _.get(payload, 'service');
        if (_.get(payload, 'operator')) key += (key ? '_' : '') + _.get(payload, 'operator');
        if (_.get(payload, 'paytype')) key += (key ? '_' : '') + _.get(payload, 'paytype');
        if (_.get(payload, 'circle')) key += (key ? '_' : '') + _.get(payload, 'circle');
        if (_.get(payload, 'operation')) key += (key ? '_' : '') + _.get(payload, 'operation');
        return key;
    }

    publishBillPushUser(ref, payload, source, flag) {
        let self = this;
        let key = self.getKeyForKafka(payload);
        if (payload != null || payload != undefined) {
            if (ref.upmsPublisher) {
                ref.upmsPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.UPMS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(payload),
                    key: key,
                    attributes: 1
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UMPS_SUBCRIPTION",
                            `SERVICE:${_.get(payload, 'service', null)}`,
                            'STATUS:ERROR',
                            "TYPE:KAFKA_PUBLISH",
                            "TOPIC:UMPS_SUBCRIPTION",
                            "OPERATOR:" + _.get(payload, 'operator', null),
                            "SOURCE:" + source,
                            "FLAG:" + flag
                        ]);
                        self.logger.critical(`billPush :: publishRecordInKafka Error while publishing message in Kafka ${error} - MSG:- `, payload, _.get(payload, 'service', null));
                    } else {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:UMPS_SUBCRIPTION",
                            `SERVICE:${_.get(payload, 'service', null)}`,
                            'STATUS:PUBLISHED',
                            "TYPE:KAFKA_PUBLISH",
                            "TOPIC:UMPS_SUBCRIPTION",
                            "OPERATOR:" + _.get(payload, 'operator', null),
                            "SOURCE:" + source,
                            "FLAG:" + flag
                        ]);
                        self.logger.log(`billPush :: publishRecordInKafka Message published successfully in Kafka - MSG:- for key ${key} with payload::`, payload,);
                    }
                    return;
                }, [200, 800]);
            }
        }

    }

}

export default BillPush;