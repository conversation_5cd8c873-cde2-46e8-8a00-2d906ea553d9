"use strict";

import _ from 'lodash'
import AWS from 'aws-sdk'
import { parseStream } from 'fast-csv';
const csv = require('fast-csv')
import utility from './'
import MOMENT from 'moment'


class AWSCsvIngester {

    constructor(options,updateProgress=()=>{}) {
        this.L = options.L,
        this.config = options.config;
        this.totalProcessedRecordsForCurrentDay = 0;
        this.isRecordProcessingCapped = _.get(options,'isRecordProcessingCapped',false);
        this.cappingNumberForCurrentDay = _.get(options,'cappingNumberForCurrentDay');
        this.allowedCronExecutionTime = _.get(options,'allowedCronExecutionTime', false);
        this.updateProgress = updateProgress 
    }


    configure(bucketName,serviceName="",batchSize = 1){
        let self = this;
        this.s3Params = {
            Bucket: _.get(this.config, 'AWS.Bucket', null),
            ClientInfo: _.get(this.config, 'AWS.ClientInfo', null),
        };
        this.bucketName = bucketName
        this.batchSize = batchSize;
        self.batchRecords = []
        try{

            this.s3 = new AWS.S3(this.s3Params.ClientInfo)
        }catch(error){
            this.L.error("Error while initializing S3",error)
        }
       
        this.serviceName = serviceName
    }

    getFileNames(folderPath , callback){
        let self = this;
        let files= [] 
        this.s3.listObjects({
            Bucket : 'digital-reminder',
            Delimiter: '/',
            Prefix: `${folderPath}/`
        }, function(err, data) {
            if (err) {
                self.L.critical("Error while Retreiving Data")
                return callback(err)
            } else {
              data.Contents.forEach(function(obj,index) {
                console.log(obj)
                files.push(obj.Key)
              })
              self.L.info(self.serviceName , "files ::",files)
              return callback(null , files)
            }
          })
    }

    start(processBatch ,filename ,  callback ,skipRows=0){    

        let self = this;
        // checking if capping number or capping time is reached then stopping the execution.
        if(self.isRecordProcessingCapped && ((self.totalProcessedRecordsForCurrentDay == self.cappingNumberForCurrentDay) || (self.allowedCronExecutionTime && self.allowedCronExecutionTime.isBefore(MOMENT())))) {
            self.L.info(this.serviceName ,filename, "Daily limit of processing reached. Ending the processing for today");
            return callback(null);
        }

        let params =  {
            Bucket:  "digital-reminder",
            Key: filename
        }
        self.totalRecords = skipRows
        let s3Stream,csvStream

        
        self.L.info(self.serviceName , "Starting Processing for file :",filename)
        
        try{
            s3Stream = this.s3.getObject(params).createReadStream();
            s3Stream.on('error',(error)=>{
                self.L.critical("Error while configuring s3Stream - ",error)
                if(self.csvStream){
                    self.csvStream.destroy()
                }else{
                    return callback("Error")
                }
            }) 
            this.csvStream = csv.parseStream(s3Stream,{headers:headers => headers.map(h => h.trim()),skipRows , trim:true});   
        }catch(error){
            self.L.critical("Error while configuring s3Stream",error)
        }

        let recordsProcessedOfCurrentFile = 0;

        this.csvStream
            .on('data', async function processRow(data) {
                    recordsProcessedOfCurrentFile++;
                    if(self.isRecordProcessingCapped && ((self.totalProcessedRecordsForCurrentDay >= self.cappingNumberForCurrentDay - 1) || (self.allowedCronExecutionTime && self.allowedCronExecutionTime.isBefore(MOMENT())))) {
                        self.totalRecords++;
                        self.batchRecords.push(data);
                        self.totalProcessedRecordsForCurrentDay++;
                        self.L.info(this.serviceName ,filename, "Daily limit of processing reached. Ending the processing for today");
                        self.csvStream.removeListener('data',processRow);
                        self.csvStream.end();
                    } else {    
                        self.totalRecords++;
                        self.totalProcessedRecordsForCurrentDay++;
                        self.batchRecords.push(data)
                        if(self.batchRecords.length == 1000){
                            self.csvStream.pause();
                            self.L.info(this.serviceName ,filename, "Processing batch of length ",self.batchRecords.length)
                            try{
                                await processBatch(self.batchRecords)

                            }catch(err){
                                self.L.error(this.serviceName ,filename,  "Error while processing batch",err)
                            }
                            self.L.info(this.serviceName , filename, "Finished processing batch ")
                            self.updateProgress(filename , self.totalRecords)
                            self.batchRecords = []
                            self.csvStream.resume()
                        
                        }
                    }
            })
            .on('end', async (rowCount) => {
                /** Process the last batch */
                if(self.batchRecords.length > 0)
                {
                    self.L.info(this.serviceName ,filename, "Processing batch of length ",self.batchRecords.length)
                    try{
                        await processBatch(self.batchRecords)
                    }catch(err){
                        self.L.error(this.serviceName ,filename,  "Error while processing batch",err)
                    }
                    self.L.info(this.serviceName , filename, "Finished processing batch ")
                    self.batchRecords = []
                }
                if(recordsProcessedOfCurrentFile == rowCount) {
                    utility._sendMetricsToDD(rowCount || "0" ,['REQUEST_TYPE:CSV_PROCESSED',`FILE:${filename}`])
                    self.L.info(this.serviceName ,filename,  "Reached end of csv file , rowCount",rowCount)
                    self.updateProgress(filename , -1)
                } else if(self.isRecordProcessingCapped && ((self.totalProcessedRecordsForCurrentDay >= self.cappingNumberForCurrentDay) || (self.allowedCronExecutionTime && self.allowedCronExecutionTime.isBefore(MOMENT())) )) {
                    utility._sendMetricsToDD(rowCount || "0" ,['REQUEST_TYPE:DAILY_LIMIT_BREACHED',`FILE:${filename}`])
                    self.L.info(this.serviceName ,filename,  "DAILY LIMIT BREACHED, rowCount",rowCount);
                    self.updateProgress(filename , self.totalRecords)
                }
                return callback(null)
               
            })
            .on('error', error => {
                utility._sendMetricsToDD(1 ,['REQUEST_TYPE:CSV_ERROR',`FILE:${filename}`])
                self.L.error(self.serviceName , filename,"Error in CSV Stream : ", error)
                if(self.csvStream){
                    self.csvStream.destroy()
                }
                return callback(error)
            });

    }

    

}

export default AWSCsvIngester;

