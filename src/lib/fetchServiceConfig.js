import Q from 'q';
import _ from 'lodash';
import request from 'request';

class FetchServiceConfig {

    constructor(options) {
        this.refreshInterval = 15 * 60 * 1000;
        this.L = options.L;
        this.config = options.config;
        //this.configsList = _.get(this.config, ['COMMON', 'CONFIGS_LIST'], []);
    }

    load() {
        let self = this;
        let deferred = Q.defer();

        self.getParsedConfig(function (err, parsedConfig) {
            if (err) {
                deferred.reject(err);
            } else {
                _.extend(self.config.SAGA_SERVICE_CONFIG, parsedConfig);
                setInterval(function () {
                    self.getParsedConfig(function (err, parsedConfig) {
                        if (err) {
                            self.L.critical(`Unable to parse service config: ${err}`);
                        } else {
                            _.extend(self.config.SAGA_SERVICE_CONFIG, parsedConfig);
                        }
                    });
                }, self.refreshInterval);
                deferred.resolve();
            }
        });

        return deferred.promise;
    }

    getParsedConfig(callback) {
        let self = this;
        const apiUrl = _.get(self.config, 'COMMON.SAGA_SERVICE_CONFIG_URL','');//'https://digitalapiproxy-staging.paytm.com/digitalrecharge/v1/offloadcpu/data';
        const params = {
            key: 'serviceConfig',
            service_name: 'bff_saga_common_config'
        };

        request({
            url: apiUrl,
            qs: params,
            method: 'GET',
            json: true
        }, (error, response, body) => {
            if (error || response.statusCode !== 200) {
                callback(error || new Error(`API call failed with status code: ${response.statusCode}`));
                return;
            }
            try {

                if (body && typeof body === 'string') {
                    body = JSON.parse(body);
                }

                if(body.status == 200){
                    if (!body.data || !_.isArray(body.data)) {
                        callback(new Error('Invalid response format from API'));
                        return;
                    }
                    // Parse config from the API response
                    let config = _.get(body, 'data[0].config', '{}');
                    if (typeof config === 'string') {
                        config = JSON.parse(config);
                    }
                    callback(null, config);
                }else{
                    callback(new Error('API response status is non 200 - ' + body.status));
                }
            } catch (parseError) {
                callback(new Error('Error parsing API response: ' + parseError.message));
            }
        });
    }
}

export default {
    FetchServiceConfig
};
