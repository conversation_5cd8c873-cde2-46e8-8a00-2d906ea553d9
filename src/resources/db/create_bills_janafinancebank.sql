CREATE TABLE `bills_janafinancebank` 
( `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT, 
`customer_id` bigint(20) unsigned NOT NULL, 
`recharge_number` varchar(255) DEFAULT NULL,
`product_id` int(12) unsigned DEFAULT NULL,
`operator` varchar(255) DEFAULT NULL, 
`amount` decimal(12,2) DEFAULT '0.00',
`bill_date` datetime DEFAULT NULL COMMENT 'operator bill generation date', 
`due_date` datetime DEFAULT NULL, 
`bill_fetch_date` datetime DEFAULT NULL, 
`next_bill_fetch_date` datetime DEFAULT NULL, 
`gateway` varchar(255) DEFAULT NULL, 
`paytype` varchar(255) DEFAULT NULL, 
`service` varchar(255) DEFAULT NULL, 
`circle` varchar(255) DEFAULT NULL, 
`customer_mobile` varchar(15) DEFAULT NULL, 
`customer_email` varchar(255) DEFAULT NULL, 
`payment_channel` varchar(255) DEFAULT NULL, 
`retry_count` int(11) unsigned DEFAULT '0', 
`status` int(11) NOT NULL DEFAULT '0', 
`reason` text, 
`extra` text, 
`published_date` datetime DEFAULT NULL, 
`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, 
`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, 
`user_data` text, 
`notification_status` tinyint(4) DEFAULT '1', 
`payment_date` datetime DEFAULT NULL, 
`service_id` int(11) NOT NULL DEFAULT '0', 
`customerOtherInfo` text, 
`is_automatic` tinyint(1) NOT NULL DEFAULT '0', 
PRIMARY KEY (`id`), 
UNIQUE KEY `customer_id_2` (`customer_id`,`recharge_number`,`operator`,`service`), 
KEY `bill_fetch_date` (`bill_fetch_date`), KEY `due_date` (`due_date`), 
KEY `idx_published_date` (`published_date`), KEY `idx_updated_at` (`updated_at`), 
KEY `idx_status_retry_count_next_bill_fetch_date` (`status`,`retry_count`,`next_bill_fetch_date`), 
KEY `next_bill_fetch_date` (`next_bill_fetch_date`), 
KEY `idx_comp` (`recharge_number`,`operator`,`service`) ) 
ENGINE=InnoDB  DEFAULT CHARSET=utf8