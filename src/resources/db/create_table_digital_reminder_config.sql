create table digital_reminder_config (
    `id` int(12) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL DEFAULT 'MAIN',
    `node` varchar(255) NOT NULL DEFAULT 'DEFAULT',
    `key_name` varchar(255) NOT NULL,
    `value` varchar(255) DEFAULT NULL,
    `type` varchar(255) NOT NULL DEFAULT 'string',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `brc_nnk` (`name`,`node`,`key_name`)
);
