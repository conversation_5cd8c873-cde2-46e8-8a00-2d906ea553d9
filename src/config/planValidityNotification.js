export default {
    common: {
        CONSUMER_SCHEDULER: {
            "vil": {
                TOPIC: "VIL_PV_NOTIFICATION",
                SCHEDULE_ON_INTERVAL: true,
                FROM: '11:00',
                TO: '23:59'
            },
            "common": {
                TOPIC: "COMMON_PV_NOTIFICATION",
                SCHEDULE_ON_INTERVAL: false
            },
            "common_realtime":{
                TOPIC: "COMMON_PV_NOTIFICATION_REALTIME",
                SCHEDULE_ON_INTERVAL: false
            }
        },
        CONSUMER_GROUP: {
            1: "common",
            2: "vil",
            3: "common_realtime"
        },

        VIL: ["vodafone", "vodafone idea", "idea"],
    },
    production: {
        PUBLISHER: {
            MYSQL_FETCH_LIMIT: 1000,
        },
        CONSUMER: {
            BATCH_SIZE: 100,
        },

    },
    staging: {
        PUBLISHER: {
            MYSQL_FETCH_LIMIT: 1000,
        },
        CONSUMER: {
            BATCH_SIZE: 1000,
        },
    },
    development: {
        PUBLISHER: {
            MYSQL_FETCH_LIMIT: 2,
        },
        CONSUMER: {
            BATCH_SIZE: 2,
        },
    }
}