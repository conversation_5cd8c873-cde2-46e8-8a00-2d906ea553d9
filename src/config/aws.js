/*jshint multistr: true ,node: true*/
'use strict';

module.exports = {
    common: {
        ClientInfo: {
            endpoint: 's3.ap-south-1.amazonaws.com',
            signatureVersion: 'v4',
            sslEnabled: true,
            region: 'ap-south-1'
        },
        BucketPrefix : 'snapshots/cdo/'
    },
    production: {
        Bucket: 'daas-computed-datasets-prod'
    },
    development: {
        Bucket: 'daas-computed-datasets-stg'
    },
    staging: {
        Bucket: 'daas-computed-datasets-stg'
    }
};
