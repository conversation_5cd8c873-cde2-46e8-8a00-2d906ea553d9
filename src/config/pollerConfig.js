export default {
    common : {
        /*
           Below is the structure of gateway object in this config:
               <GATEWAY_NAME> : {
                  paytypes          : [ARRAY OF STRINGS],
                  firstBillDelay    : <INTEGER_NUM_OF_DAYS>
               }

               where paytypes is an array of strings describing the possible paytypes under which we want to fetch bill for that gateway
               e.g. if there is a gateway named 'ABC_TELECOM' which serves for both 'postpaid' & 'prepaid' and we want to fetch bills only 
               for 'postpaid', then we should write it as:
                   ABC_TELECOM : {
                      paytypes   : ['postpaid']
                   }
               And if we want to fetch its first bill after 7 days, then we should write it as:
                   ABC_TELECOM : {
                      paytypes         : ['postpaid'],
                      firstBillDelay   : 7
                   }

               The main reason to put this config this way is to make it easy for fellow developers to introduce new configs on gateway level
               when and where necessary. So in case if you want to introduce a new config 'fetch_Bill_On_This_Particular_Date_Only', then you can 
               write it like:
                   ABC_TELECOM : {
                      paytypes                                  : ['postpaid'],
                      firstBillDelay                            : 7,
                      fetch_Bill_On_This_Particular_Date_Only   : <DATE>
                   }
               But to get it work you need to write the supporting code as well.                
        */
        BILL_FETCH_GATEWAYS   : {
            bses : {
                paytypes : ['postpaid']
            }
        }
    },
    production: {
        
    },
    staging: {
        
    },
    development: {

    }
};