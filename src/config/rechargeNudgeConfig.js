/*jshint multistr: true ,node: true*/
'use strict';

module.exports = {
    common: {
        rechargeDataRedisTTL            : 2 * 24 * 60 * 60 * 1000,
        notificationDataRedisTTL        : 1 * 24 * 60 * 60 * 1000,
        enabledServices                 : {
            "electricity"                 : true,
            "gas"                         : true,
            "water"                       : true,
            "cylinder booking"            : true
        },
        enabledValidationServices         : {
            "financial services"          : true
        },
        rechargeConsumerKafkaGroupId    : "recharge_nudge_recharge_",
        rechargeServiceConsumerKafkaGroupId    : "recharge_cc_nudge_recharge_",
        rechargeConsumerKafkaId         : "recharge-nudge-recharge-consumer",
        rechargeServiceConsumerKafkaId  : "recharge-cc-nudge-recharge-consumer",
        availableValidationmodes        : {
            "d"                         : true,
            "d+2"                       : true
        },
        lockFileName                    : {
            "d"                         : "rechargeNudgeDLockFile",
            "d+2"                       : "rechargeNudgeD2LockFile"
        },
        endTimeFileName                 : {
            "d"                         : "rechargeNudgeDTimeFile",
            "d+2"                       : "rechargeNudgeD2TimeFile"
        },
        validationConsumerKafkaGroupId  : {
            "d"                         : "recharge_nudge_validation_d_",
            "d+2"                       : "recharge_nudge_validation_d2_"
        },
        validationServiceConsumerKafkaGroupId : "recharge_nudge_service_validation",
        validationConsumerKafkaId       : {
            "d"                         : "recharge-nudge-validation-d-consumer",
            "d+2"                       : "recharge-nudge-validation-d2-consumer"
        },
        kafkaBatchSize                  : 500,
        rechargeRedisPrefix             : "RNRC_",
        notificationRedisPrefix         : "RNN_",
        blockedNotificationStartHour    : 0,
        blockedNotificationEndHour      : 7,
        nudgeOnValidationSuccess:       { 'indane':true}
    }
};
