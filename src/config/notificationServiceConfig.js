/*jshint multistr: true ,node: true*/
'use strict';

module.exports = {
    common: {
        REDIS_TTL: 1 * 25 * 60 * 60 * 1000,
        REDIS_PREFIX: "NSRD_EDU_",
        REDIS_ITEM_KEYS: [  'catalogProductID', 
                            'orderInfo_item_id',
                            'inStatusMap_responseCode',
                            'userData_amount', 
                            'userData_recharge_number'
        ],

        STOP_TIME_RANGE:{
            GTE: 0,
            LTE: 7
        },
        IN_STATUS_CODE: {
            "SUCCESS": ['00'],
            "FAILED": ['05', '06', '08', '10', '11', '15', '19', '404', '1000', '2000', '3000'],
            "PENDING": ['07', '13']
        },
        REFRESH_INTERVAL: 15 * 60 * 1000,
        PENDING_INTERVAL: 2 * 60 * 1000,
        
        ES_INDEX: {
            MIS_INDEX: 'recharge_mis',
        },

        NOTIFICATION_TYPE: {
            1: "PUSH",
            2: "SMS",
            3: "EMAIL"
        },

        TEMPLATE_TYPE: {
            0: "FAILED",
            1: "SUCCESS",
            2: "PENDING"
        },

        ES_QUERY_FILE : "../query/fetchExpiredItems.json",
        TIME_STAMP_FILE : "notificationServiceTimeStamp.log"
    }
}