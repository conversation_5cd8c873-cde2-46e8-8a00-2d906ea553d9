export default {
    common      : {
        MYSQL_QUERY_TIMEOUT: 60 * 1000, // milliseconds
        DEBUG: false,
    },
    production  : {
        MYSQL_CLUSTER: {
            RECHARGE_ANALYTICS       : {
                host    : 'recharge_analyticswrite.prod.paytmdgt.io',
                database: 'recharge_analytics',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true  
            },
            RECHARGE_ANALYTICS_SLAVE: {
                host    : 'recharge_analyticsread.prod.paytmdgt.io',
                database: 'recharge_analytics',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true, //don't need this as of now
            },
            DIGITAL_REMINDER_MASTER    : {
                host    : 'digital_reminderwrite.prod.paytmdgt.io',
                database: 'digital_reminder',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true,
		timezone	    : '+00:00' 
            },
            DIGITAL_REMINDER_SLAVE    : {
                host    : 'digital_reminderread.prod.paytmdgt.io',
                database: 'digital_reminder',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true,
	        timezone            : '+00:00'  
            },
            FS_RECHARGE_MASTER      : {
                host    : 'fs_rechargewrite.prod.paytmdgt.io',
                database: 'fs_recharge',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, //milliseconds
                multipleStatements  : true
            },
            FS_RECHARGE_SLAVE1      : {
                host    : 'fs_rechargeread.prod.paytmdgt.io',
                database: 'fs_recharge',

                connectionLimit     : 10,
                waitForConnections  : true,
                queueLimit          : 0,
                connectTimeout      : 30000, 
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            FS_RECHARGE_SLAVE2      : {
                host    : 'fs_rechargeread.prod.paytmdgt.io',
                database: 'fs_recharge',

                connectionLimit     : 10,
                waitForConnections  : true,
                queueLimit          : 0,
                connectTimeout      : 30000, 
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },       
            
            OPERATOR_SYNC : {
                host    : 'digital_vodafonemysqlwrite.prod.paytmdgt.io',
                database: 'operator_sync',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            OPERATOR_SYNC_SLAVE : {       //configured to be shared
                host    : 'digital_vodafonemysqlwrite.prod.paytmdgt.io',
                database: 'operator_sync',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            }
        },
        MYSQL_CLUSTER_SETTING: {
            canRetry            : true,
            restoreNodeTimeout  : 3000,
            removeNodeErrorCount: 10000
        }
    },
    development : {
        MYSQL_CLUSTER: {
            RECHARGE_ANALYTICS       : {
                host    : 'localhost',
                database: 'recharge_analytics',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            RECHARGE_ANALYTICS_SLAVE : {
                host    : 'localhost',
                database: 'recharge_analytics',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            DIGITAL_REMINDER_MASTER    : {
                host    : 'localhost',
                database: 'digital_reminder',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true,
        timezone            : '+00:00'
            },
            DIGITAL_REMINDER_SLAVE    : {
                host    : 'localhost',
                database: 'digital_reminder',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true,
        timezone            : '+00:00'
            },
            FS_RECHARGE_MASTER      : {
                host    : 'localhost',
                database: 'fs_recharge',
                connectionLimit     : 2,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            FS_RECHARGE_SLAVE1      : {
                host    : 'localhost',
                database: 'fs_recharge',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            OPERATOR_SYNC : {
                host    : 'localhost',
                database: 'operator_sync',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            OPERATOR_SYNC_SLAVE : {
                host    : 'localhost',
                database: 'operator_sync',
                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            }
        },
        MYSQL_CLUSTER_SETTING: {
            canRetry            : true,
            restoreNodeTimeout  : 3000,
            removeNodeErrorCount: 10000
        }
    },
    staging : {
        MYSQL_CLUSTER: {
            DIGITAL_REMINDER_MASTER    : {
                host    : '***********',
                database: 'digital_reminder',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true,
		timezone            : '+00:00' 
            },
            DIGITAL_REMINDER_SLAVE    : {
                host    : '***********',
                database: 'digital_reminder',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true,
		timezone            : '+00:00'  
            },
            RECHARGE_ANALYTICS       : {
                host    : '***********',
                database: 'recharge_analytics',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true  
            },
            RECHARGE_ANALYTICS_SLAVE  : {
                host    : '***********',
                database: 'recharge_analytics',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true  
            },
            FS_RECHARGE_MASTER      : {
                host    : '***********',
                database: 'fs_recharge',

                connectionLimit     : 2,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            FS_RECHARGE_SLAVE1      : {
                host    : '***********',
                database: 'fs_recharge',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },   
            OPERATOR_SYNC : {
                host    : '***********',
                database: 'operator_sync',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            },
            OPERATOR_SYNC_SLAVE : {
                host    : '***********',
                database: 'operator_sync',

                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true
            }
        },
        MYSQL_CLUSTER_SETTING: {
            canRetry            : true,
            restoreNodeTimeout  : 3000,
            removeNodeErrorCount: 10000
        }
    }
};
