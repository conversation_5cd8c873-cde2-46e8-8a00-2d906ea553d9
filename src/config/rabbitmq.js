export default {
    common: {
        PUBLISHER_EXCHANGE_NAME  : 'ex_recharge_to_gw',
        PUBLISHER_EXCHANGE_TYPE  : 'topic',
        CLM_EXCHANGE_NAME        : 'ex_clm',
        CLM_EXCHANGE_TYPE        : 'topic',
        DTH_EXCHANGE_NAME        : 'ex_gw_to_reminder',
        DTH_BINDING_KEY          : 'gateway.callback.reminder.*',
        BILL_REMINDER_QUEUE_NAME: 'q_bill_reminder',
        PLAN_VALIDITY_RMQ_CONSUMER : 'q_pv_consumer',
        BILL_REMINDER_BINDINGKEY : 'gateway.*.CUSTOMERDATA',
        PREFETCH_MSG_COUNT       : 20,
        G<PERSON>Y<PERSON><PERSON><PERSON>_PREFETCH_MSG_COUNT  : 1,
        GREYSCALE_PROCESSING_DELAY : 5*60*1000 // 5 min
    },
    production: {
	 RABBITMQ: {
            // Heartbeat in seconds
            RETRY_INTERVAL: 5000, // milliseconds
        },
        URL           : 'http://rechargermq-internal-node.prod.paytmdgt.io:15672',
        //AUTHORIZATION : ''
    },
    development: {
        RABBITMQ: {
            // Heartbeat in seconds
            RETRY_INTERVAL: 5000  // milliseconds
        },
        URL           : 'http://127.0.0.1:15672',
        //AUTHORIZATION : ''
    },
    staging: {
        RABBITMQ: {
            // Heartbeat in seconds
            RETRY_INTERVAL: 5000 // milliseconds
        },
        URL           : 'https://digitalrabbit-staging.paytm.com',
        //AUTHORIZATION : ''
    }
};
