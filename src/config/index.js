import _ from 'lodash'
import L from 'lgr'
import Q from 'q'
import ASYNC from 'async'

import DIGITAL_IN_UTIL from 'digital-in-util'

import SQLWRAP from './sqlwrap'
import MONGO from './mongo'
import RABBITMQ_CONFIG from './rabbitmq'
import OPERATOR_TABLE_REGISTRY from './operatorTableRegistry'
import SERVICE_TABLE_REGISTRY from './serviceTableRegistry'
import OPERATOR_GATEWAY_REGISTRY from './operatorGatewayRegistry'
import OPERATOR_NOT_IN_USE_CONFIG from './operatorNotInUseToDaysMapping'
import AIRTEL_PUBLISHER_CONFIG from './airtelPublisherConfig'
import SUBSCRIBER_CONFIG from './subscriberConfig'
import MIGRATION from './migration'
import POLLER_CONFIG from './pollerConfig'
import PUBLISHER_CONFIG from './publisherConfig'
import <PERSON><PERSON><PERSON><PERSON><PERSON>ARCH from './elasticsearch'
import COMMON from './common'
import EMAIL_CONFIG from './emailConfig'
import NOTIFICATION from './notification'
import TINYURL_CONFIG from './tinyUrlConfig'
import CT_PROMOCODE_CONFFIG from './ctPromoCodeConfig'
import KAFKA from './kafka'
import RECENT_BILL_CONFIG from './recentBillConfig'
import NOTIFICATION_SERVICE_CONFIG from './notificationServiceConfig'
import REDIS from './redis'
import NOTIFICATION_REDIS from './notificationRedis'
import RECHARGE_NUDGE_CONFIG from './rechargeNudgeConfig'
import CUSTOM_BILL_FETCH_DATE from './customBillFetchDate'
import FFR from './ffr'
import OPERATOR_TEMPLATE_MAPPING from './operatorTemplateMapping'
import ACTIVE_PID_REGISTRY from './activePidRegistry'
import ORDERSUMMARY from './orderSummaryConfig'
import DYNAMIC_CONFIG from './dynamicConfig'
import PLAN_VALIDITY_NOTIFICATION from './planValidityNotification'
import ES from './es'
import VIL_SYNC_DB from './vilSyncDB'
import AWS from './aws'
import DIGITAL_CATALOG from './digitalCatalog'
import VAULT from './vault'
import CASSANDRA from './cassandra'
import NOTIFICATION_CASSANDRA from './notificationCassandra'
import SAGA_SERVICE_CONFIG from './sagaServiceConfig'


const env = (process.env.NODE_ENV || 'development').toLowerCase();

function loadConfig() {
    let config = {
        SQLWRAP,
        MONGO,
        RABBITMQ_CONFIG,
        OPERATOR_TABLE_REGISTRY,
        SERVICE_TABLE_REGISTRY,
        OPERATOR_GATEWAY_REGISTRY,
        OPERATOR_NOT_IN_USE_CONFIG,
        AIRTEL_PUBLISHER_CONFIG,
        SUBSCRIBER_CONFIG,
        MIGRATION,
        POLLER_CONFIG,
        ELASTICSEARCH,
        COMMON,
        PUBLISHER_CONFIG,
        EMAIL_CONFIG,
        NOTIFICATION,
        TINYURL_CONFIG,
        CT_PROMOCODE_CONFFIG,
        KAFKA,
        RECENT_BILL_CONFIG,
        NOTIFICATION_SERVICE_CONFIG,
        REDIS,
        NOTIFICATION_REDIS,
        RECHARGE_NUDGE_CONFIG,
        CUSTOM_BILL_FETCH_DATE,
        FFR,
        OPERATOR_TEMPLATE_MAPPING,
        ACTIVE_PID_REGISTRY,
        ORDERSUMMARY,
        DYNAMIC_CONFIG,
        ES,
        PLAN_VALIDITY_NOTIFICATION,
        VIL_SYNC_DB,
        AWS,
        DIGITAL_CATALOG,
        VAULT,
        CASSANDRA,
        NOTIFICATION_CASSANDRA,
        SAGA_SERVICE_CONFIG
    },
    deferred =Q.defer();

    if (!process.env.NODE_ENV)
        L.critical('config :: loadConfig', 'Loading config with default node_env(development) as none is defined');
    else
        L.log('config :: loadConfig', 'Loading config with defined node_env i.e. ', env);


    Object.keys(config).forEach(function (config_key) {
        let
            all_configs = _.get(config, config_key, {}),
            env_conf = _.get(all_configs, env, null) || _.get(all_configs, 'development', {});

        config[config_key] = _.extend({}, all_configs.common, env_conf);

        if (_.isEmpty(config[config_key]) && !_.has(all_configs, env) && !_.has(all_configs, 'common')) {
            config[config_key] = all_configs
        }

    });

    if(process.env.VAULT == 0) { // Pass this env variable if vault secrets are not required
        deferred.resolve(config);
    } else {
        L.log('reminderConfig::index','Going to fetch secret from Vault');
        getSecrets(function(error,data){
            if(error) {
                L.critical('reminderConfig::index','Error from getSecrets',error);
                process.exit(1);
            } else {
                _.merge(config,data);
                deferred.resolve(config);
            }
        }, config.VAULT);
    }

    return deferred.promise;
}

/**
 * Method to get secrets from Vault
 * @param {*} vaultConfig : config params to connect vault server 
 * @param {*} callback 
 * @param {*} leftRetries 
 * @param {*} delay 
 */
function getSecrets(callback, vaultConfig ,leftRetries = 10,delay = 1000){
    if(leftRetries <= 0) return callback('Max Retries reached for getSecretsFromVault');
    L.verbose('reminderConfig::index::getSecrets',`Using Vault config to load secrets:- ${JSON.stringify(vaultConfig)}`);
    let vault = new DIGITAL_IN_UTIL.vault(vaultConfig);

    L.log('reminderConfig::index::getSecrets',`Trying to get Vault secrets. Retries left:${leftRetries}`);
    
    ASYNC.waterfall([
        next => {
            L.log('reminderConfig::index::getSecrets','Going to login vault');
            return vault._login(next);
        },
        next => {
            L.log('reminderConfig::index::getSecrets','Going to fetch secrets');
            vault.getSecretsFromVault(vaultConfig.CONFIG_NAME,function(err,data){
                if(err) {
                    L.critical('reminderConfig::index::getSecrets','Error while getSecretsFromVault',err);
                    return next('unable to get secrets');
                }
        
                if(typeof data == 'string'){
                    try{
                        data = JSON.parse(data);
                    }catch(ex){
                        L.critical("reminderConfig::index::getSecrets unable to parse vault data",data);
                        return next('unable to parse vault secrets');
                    }
                }
                // Keeping this temp log so that it can be verified on production deployments...can be removed after change is stable.
                L.log(`reminderConfig:Data from Valult for Reminder config: ${JSON.stringify(data,null,4)}`);
                return next(null,data);
            },"serviceConfig");
        }
    ], function(error, data){
        if(error || !data) {
            L.critical("reminderConfig::index",`Vault secrets fetch Attempt failed with error: ${error}`);
            setTimeout(function () {
                return getSecrets(callback,vaultConfig, leftRetries-1,delay);
            }, delay);
        } else {
            return callback(null,data);
        }
    })
}

export default loadConfig;


if (require.main == module) {
    loadConfig();
}
