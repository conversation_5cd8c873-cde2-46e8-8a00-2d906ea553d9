/*
1. daysToNextDueDate:
    - If days are specified for prepaid operator then it sets due date = now +  daysToNextDueDate in each notification
    - If days are not specified for prepaid operators then it fetches bill from operator and behaves like postpaid (in terms of bill fetch)
2. updateAllCustIdRecords : 
    - default -> true 
    - decides If update on all records having same RN is required or not ?
    - flag useful when we have restrictions to update record of only transacted customerId
*/

module.exports = {
    EXCLUDED: {
        CUSTOMER_IDS: [
            "11292619",
            "11613001",
            "19102731",
            "20971466",
            "26078282",
            "28570735",
            "136554028",
            "152942826",
            "198165771",
        ],
        PAYTYPE: [
            // "credit card"
        ]
    }
}
