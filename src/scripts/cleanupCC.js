import L from 'lgr'
import _ from 'lodash'
import Async from 'async'
import startup from '../lib/startup'
import RecentsLayerLib from '../lib/recentsLayer'
import MOMENT from 'moment'
import utility from '../lib'
import digitalUtility from 'digital-in-util'

const maxCount= 1000;
const LIMIT = 100;



class cleanupCC {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.recentsLayerLib = new RecentsLayerLib(options); 
        this.infraUtils = options.INFRAUTILS;
        this.activePidLib = options.activePidLib;
        this.commonLib = new utility.commonLib(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
    }

    startCTPublisher(cb){
        let self=this;
        // console.log("reached to kafak start");
        self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
        });
        // console.log(self.ctKafkaPublisher)
        self.ctKafkaPublisher.initProducer('high', function (error) {
            if (error){
                console.error('error in initialising ctKafkaPublisher Producer :: ', error);
                return cb(error);
            }
            console.log("ctKafkaPublisher KAFKA PRODUCER STARTED....");
            return cb(null);
        });
    }

    async sendCTEvents(data, cb){
        for(let i=0; i<data.length; i++){
            await this.publishCtEventsForDeletedRecords(data[i])
        }
        return cb(null);
    }

    async publishCtEventsForDeletedRecords(recentDataa) {
        let self = this;
        return new Promise((resolve, reject) => {
            recentDataa.productId = self.activePidLib.getActivePID(recentDataa.product_id);
        const productId = recentDataa.productId;
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull');

        if (!_.get(recentDataa, 'notificationStatus', 1)) {
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(recentDataa, 'notificationStatus', 0)}`);
            resolve() 
        } 	

            recentDataa.bill_date = recentDataa.billDate ? MOMENT(recentDataa.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            recentDataa.due_date = recentDataa.dueDate ? MOMENT(recentDataa.dueDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : recentDataa.due_date;
            recentDataa.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
            

            if( eventName == _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull') || !recentDataa.status){
                recentDataa.status = _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11)
            }

            if(!recentDataa.customer_id){
                recentDataa.customer_id = recentDataa.customerId //fixing mismatch in naming conventions
                recentDataa.recharge_number = recentDataa.rechargeNumber;
            }
            let dbDebugKey = `rech:${recentDataa.recharge_number}::cust:${recentDataa.customer_id}::op:${recentDataa.operator}`;
            if(recentDataa.referenceId){
                dbDebugKey += `::ref_id:${recentDataa.referenceId}`
            } else if(recentDataa.reference_id) {
                dbDebugKey += `::ref_id:${recentDataa.reference_id}`
            }
            recentDataa.debugKey = dbDebugKey;

            Async.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, recentDataa.customer_id, recentDataa);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, recentDataa);
                },
                next => {                  
                    let mappedData = self.reminderUtils.createCTPipelinePayload(recentDataa, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILLS", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + recentDataa.operator]);
                            self.L.error('publishInKafka :: publishCtEventsForDeletedRecords', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILLS", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + recentDataa.operator]);
                            self.L.log('prepareKafkaResponse :: publishCtEventsForDeletedRecords', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.error('publishInKafka :: publishCtEventsForDeletedRecords', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' , err);
                resolve()
            })
        })
    }

    async deleteDuplicates(dbInstance, data,cb){
        let self=this;
        for(let i=0;i<data.length;i++){
            await self.deleteDuplicatesOneByOne(dbInstance,data[i]);
        }
        return cb();
    }
    deleteDuplicatesOneByOne(dbInstance, record){
        let self=this;
        return new Promise((resolve, reject) => {
        let query = `delete from bills_creditcard`
        let params = [];
    
        if (_.get(record,'id',null)) {
            query += ` where id=(?)`;
            params.push(_.get(record,'id',null));
        }
        else{
            reject("No valid id to recieved");
        }
    
        L.verbose(`Executing: ${query}`);
        setTimeout(function(){
            dbInstance.exec(function (err, data) {
                if (err) {
                    reject(err);
                }
                // console.log("result of query ->", data);
                resolve(null, data);
            }, 'DIGITAL_REMINDER_MASTER', query, params);
        },1000);
    }
    )}

    getRecords(dbInstance, cb) {
        // console.log("dbInstance ->", dbInstance);
        let query = `select customer_id, SUBSTR(recharge_number,12,16), count(*) as cnt from bills_creditcard group by customer_id,SUBSTR(recharge_number,12,16) having cnt > 1 limit ${LIMIT};`;
    
        L.verbose(`Executing: ${query}`);
        dbInstance.exec(function (err, data) {
            // console.log("err", err);
            // console.log("dataaa->", data)
            if (err) {
                console.log(err);
                return cb(err);
            }
            return cb(null, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, []);
    }
    
    getToBeDeletedRecords(rows, cb){
        let self = this;
        let recordsToBeDeleted=[];
        self.recentsLayerLib.getCCDetailsByMcnCustId(function (error, data) {
            if (error) {
                self.L.error('getRecentRecord', `Error getting recents for ${JSON.stringify(rows[0])}_error:${error}`);
                return cb(error);
            } else { 
                let lastFourMCN = _.get(rows[0],'recharge_number').substr(-4);
                data = data.filter(row => row.recharge_number.substr(-4) == lastFourMCN); // removing other CC of customer
                if(data && data.length==1){
                    console.log("dataaaa", JSON.stringify(data));
                    /* data ->
                    [ { recharge_number: 'XXXX XXXX XXXX 8715',
                        operator: 'visa_hdfcbank',
                        customer_id: *********,
                        product_id: **********,
                        bills: [ [Object] ],
                        cin: '',
                        panUniqueReference: '*********',
                        updated_at: '2022-10-04T06:00:16.883Z' } ]
                    */

                    if(_.get(data[0], 'panUniqueReference',null)){
                        for(let i=0; i<rows.length; i++){
                            if(!_.get(rows[i], 'par_id', null) || _.get(rows[i], 'reference_id', null) !== _.get(data[0], 'panUniqueReference',null))
                            recordsToBeDeleted.push(rows[i]);
                        }
                    }else if(_.get(data[0], 'cin',null)){
                        for(let i=0; i<rows.length;i++){
                            if(_.get(rows[i], 'par_id', null) || _.get(rows[i], 'reference_id', null) !== _.get(data[0], 'cin',null))
                            recordsToBeDeleted.push(rows[i]);
                        }
                    }
                }
                else{
                    recordsToBeDeleted=rows;
                }
                if(recordsToBeDeleted.length==0 && rows.length> 1){
                    recordsToBeDeleted.push(rows[0]);
                }
                console.log("records which needs to be deleted ::", JSON.stringify(recordsToBeDeleted));
                return cb(null, recordsToBeDeleted);
            }
        }, rows[0], "smsParsingCCBills");
    }
    
    getConflictedRecords(rows,record,cb){
        let customer_id = _.get(record, 'customer_id', null);
        let last4MCN = _.get(record, 'SUBSTR(recharge_number,12,16)', null);
        let confictedRecords=[];
        for(let i=0; i<rows.length; i++){
            if(rows[i].customer_id===customer_id && rows[i].recharge_number.substr(-4)===last4MCN.substr(-4)){
                confictedRecords.push(rows[i]);
            }
        }
        // console.log("conflicted rowss arrrayyy ", confictedRecords);
        return cb(null,confictedRecords);
    }
    
    fetchRecordsByCustID(dbInstance,customer_id,cb){
        let query = `select * from bills_creditcard`
        let params = [];
    
        if (customer_id) {
            query += ` where customer_id=(?)`;
            params.push(customer_id);
        
    
        L.verbose(`Executing: ${query}`);
        dbInstance.exec(function (err, data) {
            if (err) {
                return cb(err);
            }
            // console.log("result of query ->", data);
            return cb(null, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, params);
    }
    else{
        L.error("No customer id found for record not hiting query")
        return cb("No customer id found for record not hiting query")
    }
    }
    
    async runForAllRecord(options, callback, count) {
        let self= this;
        Async.waterfall([
            next => {
                self.getRecords(options.dbInstance, function(err, records){
                    if(err) return next(err);
                    else if(!records || !records.length) return next("No more records left")
                    else return next(null,records)
                })
            },
            async (records,next) => {
                console.log("records recieved of length", records.length);
                console.log("check count#########", count)
                count+=records.length;
                for(let i=0;i<records.length;i++){
                    await self.runForSingleRecord(records[i],options)
                }
                console.log("End of cycle, will start processing after 10 seconds..")
                if((records.length==LIMIT && !maxCount) || (records.length==LIMIT && count <= maxCount) ){
                    setTimeout(function(){
                        self.runForAllRecord(options,callback, count)
                    },10000);
                }
                else{
                    return next(null);
                }
            },
            
    ],
    (err)=>{
        return callback();
    })
    
        
}
    
    async runForSingleRecord(record, options){ 
        console.log("Single record for processing->", JSON.stringify(record));
        let self=this;  
        return new Promise((resolve, reject) => {
            Async.waterfall([
                next => {
                    self.fetchRecordsByCustID(options.dbInstance, record.customer_id,function(err,rows){
                        if(err){
                            L.error(`Error: ${err} for record: ${JSON.stringify(record)}`);
                            return next(err);
                        }else{
                            self.getConflictedRecords(rows,record,function(err,data){
                                if(data.length==0){
                                    L.info(`No confict exists for record: ${JSON.stringify(record)}`);
                                    return next("No confict exists");
                                }else{
                                    // console.log("conflicted data->", data);
                                    return next(null,data);
                                }
                            })
                        }
                    })
                },
                (data,next) => {
                    self.getToBeDeletedRecords(data, function(err, result){
                        // console.log("to be deleted records",result);
                        return next(null,result);
                    })
                },
                (result,next) => {
                    if(result && _.isArray(result) && result.length){
                        self.sendCTEvents(result,function(err,data){
                            if(err){
                                L.error(`error while sendingCT: ${err}`)
                            }
                            return next(null,result);
                        })
                    }
                    else return next("Array to be deleted is not correct");
                },
                (result,next) => {
                    if(result && _.isArray(result) && result.length){
                        self.deleteDuplicates(options.dbInstance, result,function(err,data){
                            if(err){
                                L.error(`error while deleting: ${err}`)
                            }
                            return next(null);
                        })
                    }
                    else return next("Array to be deleted is not correct");
                }
        ],
        (err)=>{
            return resolve();
        })
        })          
    }
    

}

function execute(cb) {
    L.setLevel('verbose');
        Async.waterfall([
            next => {
                startup.init({
                    exclude: {
                        cvr: true,
                        mongoDb: true,
                        ruleEngine: true,
                        activePidLib: true,
                        dynamicConfig: true
                    }
                }, function (err, options) {
                    if (err) {
                        return next(err);
                    }
                    else return next(null,options)
                })
                    
            },
            (options,next) => {
                let script = new cleanupCC(options);
                script.startCTPublisher(function(err,res){
                    return next(err,options,script);
                })
            },
            (options,script,next) => {
                script.runForAllRecord(options,function(err,res){
                    return next(err);
                },0)
            },
            
    ],
    (err)=>{
        return cb(err);
    })
}

(function () {
    if (require.main === module) {
        console.time('Execution Time');
        
        execute(function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=production node dist/scripts/saved_card_update.js --id 123
