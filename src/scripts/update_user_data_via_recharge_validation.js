const request = require('request');
const async = require('async');
import startup from '../lib/startup'
import L from 'lgr'
const moment = require('moment');
let couterObject = {
    recordsProcessed : 0,   // records processed successfully
    recordsError : 0        // Error counter while processing records
}; 

function execDBQuery(query, dbInstance, operationType , cb) {
    L.info(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    if(operationType != "WRITE") {
        dbInstance.exec(function (err, data) {
            if (err) {
                return cb(err);
            }
            return cb(null, data);
        }, MASER_SLAVE_TYPE, query, []);
    } else {
        return cb(null, true);
    }
}

function getDataFromES(options, currentRecord, cb) {
    let requestInfo = {
        uri: 'http://internal-digital-searchstats-alb-2006557837.ap-south-1.elb.amazonaws.com/recharge_validation/_search',
        method: 'POST',
        json: {
            "_source": [
                "userData_recharge_number",
                "userData_recharge_number_2",
                "userData_recharge_number_3",
                "userData_recharge_number_4",
                "userData_recharge_number_5",
                "userData_recharge_number_6",
                "userData_recharge_number_7",
                "customerInfo_customer_id",
                "productInfo_service",
                "productInfo_operator",
                "timestamps_init",
                "rechargeGwResponse_operatorResponseCode"
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "customerInfo_customer_id": currentRecord.customer_id
                            }
                        },
                        {
                            "term": {
                                "userData_recharge_number": currentRecord.recharge_number
                            }
                        },
                        {
                            "term": {
                                "validationSuccessful": true
                            }
                        },
                        {
                            "term": {
                                "productInfo_operator": options.operator
                            }
                        }, 
                        {
                            "range": {
                                "timestamps_init": {
                                    "gte": options.fromDate,
                                    "lt": options.toDate
                                }
                            }
                        }
                    ]
                }
            },
            "size": options.size,
            "sort": [
                { "timestamps_init": "desc" }
            ]
        }
    };
    let dubugkey = `customer_id::${currentRecord.customer_id}_recharge_number::${currentRecord.recharge_number}_operator::${options.operator}_fromDate::${options.fromDate}_toDate::${options.toDate}`
    L.info(`getDataFromES :: requestInfo ::`,dubugkey);
    request(requestInfo, function (err, res, body) {
        if (err) {
            return cb(err);
        }
        let data = [];
        try {
            if (body) {
                L.info(`getDataFromES :: API data ::`,JSON.stringify(body));

                body.hits.hits.forEach(function (hit) {
                    let n = hit._source;
                    if ( options.operator ==  n.productInfo_operator) {
                        let row = {};
                        row.userData_recharge_number = n.userData_recharge_number;
                        row.customerInfo_customer_id = n.customerInfo_customer_id;
                        row.productInfo_operator = n.productInfo_operator;
                        row.productInfo_service = n.productInfo_service;
                        row.user_data = JSON.stringify({
                            recharge_number_2: n.userData_recharge_number_2
                        });
                        data.push(row);
                    }
                });
            }
        } catch (e) {
            return cb(e);
        }
        return cb(null, data);
    });
}

function toUpper(recharge_number) {

    if (typeof recharge_number == "object") {
        return recharge_number;
    }
    if(typeof recharge_number == "number") {
        recharge_number = recharge_number.toString().toLocaleUpperCase();
    } else if (typeof recharge_number == "string") {
        recharge_number = recharge_number.toLocaleUpperCase();
    }
    return recharge_number
}

function toLower(recharge_number) {
    
    if (typeof recharge_number == "object") {
        return recharge_number;
    }
    if(typeof recharge_number == "number") {
        recharge_number = recharge_number.toString().toLocaleLowerCase();
    } else if (typeof recharge_number == "string") {
        recharge_number = recharge_number.toLocaleLowerCase();
    }
    return recharge_number
}


function processRecord(currentRecord, options, cb) { 
    async.waterfall([
        (next) => {
            return delay(next, options.timeout);
        },
        next => {
            return getDataFromES(options, currentRecord, next);
        },
        (ESData , next) => {
            if(!ESData || !ESData.length) {
                let msg = `Data not exists in ES for customer_id :: ${currentRecord.customer_id}, recharge_number :: ${currentRecord.recharge_number}`;
                return next(msg);
            }
            if(ESData.length > 1)  {
                let msg = "Multiple records exists in ES" + JSON.stringify(ESData);
                return next(msg);
            }
            let firstRecord = ESData[0];
            
            if (currentRecord.customer_id != firstRecord.customerInfo_customer_id || 
                 currentRecord.recharge_number != firstRecord.userData_recharge_number ||
                currentRecord.operator != firstRecord.productInfo_operator ) {

                let msg = "DB data not matching with ES data" + JSON.stringify(ESData);
                return next(msg);
            }
            
            
            let update_query = `UPDATE ${options.tableName} \
                  SET user_data = '${firstRecord.user_data}' where recharge_number = '${firstRecord.userData_recharge_number}' \
                  AND operator = '${firstRecord.productInfo_operator}' AND service = '${firstRecord.productInfo_service}' \
                  AND (user_data IS NULL OR user_data = '{}') AND updated_at >= '${options.fromDateRecords}' `;
            return execDBQuery(update_query, options.dbInstance, "WRITE", next);
        }
    ],  (err) => {
            if(err) {
                let errMsg = 'Error in processRecord :: ' + err;
                L.error(errMsg);
                couterObject.recordsError++;
            } else {
                couterObject.recordsProcessed++;
            } 
            cb();     
    });
}
function delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}

function executeRecursive(options, cb) { 
    let query = `SELECT * FROM ${options.tableName} where operator = '${options.operator}' AND (user_data IS NULL OR user_data = '{}') `+( options.id ? `AND id > ${options.id} ` : `` )+` AND updated_at >= '${options.fromDateRecords}' ORDER BY id`
    if(options.dBQueryBatchSize) {
        query += ` LIMIT ${options.dBQueryBatchSize} `;
    }
    query += ';';
    execDBQuery(query, options.dbInstance, "READ" , (err, records)=>{
        if (err) {
            console.log(err);
            console.log('execDBQuery :: FAILURE');
            L.info(`execDBQuery :: FAILURE`);
            return cb(err);
        }
        if(!records.length) {
            L.info(`No more records left for processing `);
            return cb();
        }
        
        options.id = records[records.length-1].id;
        
        L.info(`Total records: ${records.length}`);
        async.waterfall([
                next => {
                    async.eachSeries(records, function (currentRecord, cb) {
                        L.info('processing each record : ', JSON.stringify(currentRecord));
                        processRecord(currentRecord, options, function (err) {
                            if (err) {
                                return cb(err);
                            }
                            return cb();
                        });
                    },next);
                }
            ],
            (error)=> {
                if(error) {
                    let errorMsg = 'executeRecursive Error' + error;
                    L.error(errorMsg);
                    return cb(errorMsg);
                } else {
                    executeRecursive(options, cb);
                }
            });
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        let  fromDate = moment("2021-03-20").format('YYYY-MM-DD HH:mm:ss'),
            toDate = moment("2021-05-01").format('YYYY-MM-DD HH:mm:ss');

        fromDate = moment(fromDate).unix();
        toDate = moment(toDate).unix();
        L.setLevel('verbose');
        console.time('Execution Time');
        executeRecursive({
            operator: 'bsnl landline bbps',
            fromDateRecords : "2021-03-20",
            fromDate : fromDate,
            toDate : toDate,
            size: 1,                                            // ES request payload size
            tableName: 'bills_bsnlbbps',                        // DB query table name
            dBQueryBatchSize : 100,                             // DB query limit value
            dbInstance : options.dbInstance,                    // DB connection instance
            timeout:  100
        }, function (err) {
            setTimeout(function () {
                let debugKey = `recordsProcessedCouter_${couterObject.recordsProcessed}_recordsErrorCouter_${couterObject.recordsError}`;
                if (err) {
                    console.log(err);
                    L.info(`executeRecursive::`,debugKey);
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                L.info(`executeRecursive::`,debugKey);
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();

