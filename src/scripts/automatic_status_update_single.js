const async = require('async');
import startup from '../lib/startup'
import Helper from '../lib/helper'
import _ from 'lodash'
import utility from '../lib'
import L from 'lgr'

let OPTIONS = null;
let id = 0;
let operatorTableMap;

function readDataFromBillsTable(options, data, cb) {
    async.eachLimit(data, 50, function (row, localCb) {
        setImmediate(function () {
            let tableName = _.get(OPTIONS.config, ['OPERATOR_TABLE_REGISTRY', row.operator], null);
            if (tableName == null) {
                return localCb(null)
            }
                let writeQuery1 = `UPDATE ${tableName} SET is_automatic = 1 WHERE recharge_number = '${row.recharge_number}' and customer_id=${row.customer_id} and operator = '${row.operator}' and service = '${row.service}'`
                let writeQuery2 = `UPDATE ${tableName} SET is_automatic = 2 WHERE recharge_number = '${row.recharge_number}' and customer_id!=${row.customer_id} and operator = '${row.operator}' and service = '${row.service}'`
                L.log("Executing query:", writeQuery1)
                L.log("Executing query:", writeQuery2)

                async.parallel([
                    (cbparallel) => {
                        OPTIONS.dbInstance.exec(function (err, data) {
                            if(err) {
                                L.error("Error while executing query : ", writeQuery1, err);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:ERROR','TYPE:UPDATING_BILLS',`RN:${row.recharge_number}`]);
                                cbparallel(err)
                            }else {
                                L.log(`is_automatic=1 status updated successfully for RN:${row.recharge_number}_CID:${row.customer_id}_table:${tableName}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:RECORD_UPDATED','TYPE:AUTOMATIC_1',`TABLE:${tableName}`]);
                                cbparallel(null);
                            }
                        }, 'DIGITAL_REMINDER_MASTER', writeQuery1, []);
                    },
                    (cbparallel) => {
                        OPTIONS.dbInstance.exec(function (err, data) {
                            if(err) {
                                L.error("Error while executing query : ", writeQuery2, err);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:ERROR','TYPE:UPDATING_BILLS',`RN:${row.recharge_number}`]);
                                cbparallel(err)
                            }else {
                                L.log(`is_automatic=2 status updated successfully for RN:${row.recharge_number}_CID:${row.customer_id}_table:${tableName}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:RECORD_UPDATED','TYPE:AUTOMATIC_2',`TABLE:${tableName}`]);
                                cbparallel(null);
                            }
                        }, 'DIGITAL_REMINDER_MASTER', writeQuery2, []);
                    }
                ], (err) => {
                    return localCb(null);
                }
            );
        });
    }, (err) => {
        if (err) {
            L.error("Updation failed with error", err)
        }
        return cb(err);
    });
}


function readDataFromSubscription(options,opts, cb) {
    let returnData = [];
    let recharge_number=null;
    let count = 0;
    let sql;
    if(opts.recharge_number){
        recharge_number = opts.recharge_number;
        sql = `SELECT * from ${options.subscriptionTableName} where recharge_number='${recharge_number}' and status = 1`;
    }
    if(opts.count){
        count = opts.count;
        sql = `SELECT * from ${options.subscriptionTableName} where status = 1 and id > ${id} ORDER BY id limit ${count}`;
    }  
    L.log("subscription record fetching query", sql);
    OPTIONS.dbInstance.exec(function (err, data) {
        if (err || data.length == 0) {
            if(err) {
                L.error("Error while fetching records from subscription table", err);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:ERROR','TYPE:FETCHING_SUBSCRIPTION_TABLE']);
            }
            if(data.length==0) L.log("No more records left in subscription table");
            return cb(err);
        }
        L.log(`${data.length} fresh records fetched from subscription table`);
        utility._sendMetricsToDD(data.length, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:RECORD_FETCHED','TYPE:SUBSCRIPTION_TABLE']);
        data.forEach(element => {
            let row = {}
            if(!element.customer_id || !element.operator || !element.recharge_number) {
                return;
            }
            row['recharge_number'] = element.recharge_number;
            row['customer_id'] = element.customer_id;
            row['operator'] = element.operator;
            row['updated_at'] = element.updated_at;
            row['service'] = element.service;
            returnData.push(row)
        });
        id = data[data.length-1].id //max id
        return cb(null, returnData);
    }, 'FS_RECHARGE_SLAVE1', sql, []);
}

function executeFlow(options,opts, cb){
    let startDate = new Date();
    L.log("Start time for iteration", startDate);
    readDataFromSubscription(options,opts, function(err, data) {
        if(err){
            return cb(err);
        }
        if(!data) {
            return cb("no more data in subscription table")
        }
        readDataFromBillsTable(options, data, function (err) {
            let endDate = new Date();
            L.log("End time for iteration", endDate);
            L.log("Execution time for iteration in seconds", (endDate-startDate)/1000)
            L.log("completed 1 iteration")
            process.exit(1);
        })
    })
}

function main(opts) {
    if ((!opts.recharge_number || !opts.recharge_number.length) && (!opts.count)) {
        console.error("Recharge_number or count , atleast 1 is required");
        process.exit(1);
    }
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: false
        }
    }, function (err, options) {
        if (err) {
            process.exit(1);
        }
        OPTIONS = options;
        operatorTableMap = _.get(OPTIONS.config, 'OPERATOR_TABLE_REGISTRY', {});
        if(Object.keys(operatorTableMap).length == 0){
            console.error("table config is empty");
            process.exit(1);
        }
        executeFlow({
            subscriptionTableName: 'subscription',
            size: 1000
        }, opts, function (err) {
            setTimeout(function () {
                if (err) {
                    L.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-r, --recharge_number <value>', 'recharge_number', Helper.list)
            .option('-c, --count <value>', 'count', Helper.list)

            .parse(process.argv);
        main(commander);
    }
})();
