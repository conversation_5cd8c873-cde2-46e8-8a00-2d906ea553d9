import L from 'lgr'
import Helper from '../lib/helper'
import async from 'async'
import MOMENT from 'moment'
import _ from 'lodash'
import cliProgress from 'cli-progress';
import startup from '../lib/startup'

let config = null, dbInstance = null;

function getRecords(cb, operator, table, range) {
    let query = `select id,bill_date,due_date from ${table} where operator = '${operator}' and updated_at >= '${range.f}' and updated_at < '${range.t}'`;

    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function updateRecords(cb, table, nextBillFetchDate, id) {
    let query = `update ${table} set next_bill_fetch_date = ? where id = ${id}`;

    <PERSON>.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            L.error('updateRecords',`table-${table},id-${id},NBFD-${nextBillFetchDate}`,'Error',err);
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_MASTER', query, [
        nextBillFetchDate
    ]);
}

function runForOperator(operator, done) {
    L.log('runForOperator', 'Processing for operator', operator);

    let timeIntervals = getIntervals(),
        table = _.get(config, ['OPERATOR_TABLE_REGISTRY', operator], null);

    // for progress tracking
    let progressBar = new cliProgress.SingleBar({}, cliProgress.Presets.shades_classic);
    progressBar.start(timeIntervals.length, 0);

    if (!table) {
        L.error('runForOperator', 'Unable to get table for', operator);
        return done();
    }

    async.eachLimit(timeIntervals, 5, processInterval.bind(null, progressBar, operator, table), function () {
        L.log('runForOperator', 'Finished for operator', operator);
        progressBar.update(timeIntervals.length);
        progressBar.stop();
        return done();
    });
}

function processInterval(progressBar, operator, table, interval, done) {
    L.verbose("processInterval", "Processing...", operator, table, interval)

    getRecords(function (error, data) {
        progressBar.update(interval.i);
        if (error) {
            L.error('processInterval', 'Error -', table, interval, error);
            return done();
        } else if (data && data.length && data.length > 0) {
            return processRecords(data, operator, table, done);
        }
        else {
            L.verbose('processInterval', 'No data found for', operator, table, interval);
            return done();
        }
    }, operator, table, interval);
}

function processRecords(records, operator, table, done) {

    async.eachLimit(records, 5, correctCycle.bind(null, operator, table), function () {
        L.verbose('processRecords', 'Finished...');
        return done();
    });
}

function correctCycle(operator, table, record, done) {

    let
        billDateBasedGateways = _.get(config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []),
        nextBillFetchDate = null;

    if (billDateBasedGateways.indexOf(operator) > -1) { // bill gen based gw
        let billGenDate = _.get(record, 'bill_date');
        if (!billGenDate) {
            L.error('correctCycle', `billGenDate is NULL for table:${table} and id:${record.id}`);
            return done();
        }
        nextBillFetchDate =getFirstBillFetchInterval(operator)< 0 ? MOMENT(billGenDate).utc().add(Math.abs(Number(getFirstBillFetchInterval(operator))), 'months') : MOMENT(billGenDate).utc().add(getFirstBillFetchInterval(operator), 'days');
    } else { // bill due based gw
        let billDueDate = _.get(record, 'due_date');
        if (!billDueDate) {
            L.error('correctCycle', `billDueDate is NULL for table:${table} and id:${record.id}`);
            return done();
        }
        nextBillFetchDate =getFirstBillFetchInterval(operator) < 0 ? MOMENT(billDueDate).utc().add(Math.abs(Number(getFirstBillFetchInterval(operator))), 'months') : MOMENT(billDueDate).utc().add(getFirstBillFetchInterval(operator), 'days');
    }

    if (MOMENT().diff(nextBillFetchDate) > 0) {
        // No need to update
        L.verbose('correctCycle', 'Skipping updation for', operator, table, record.id);
        return done();
    } else {
        nextBillFetchDate = nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');
    }

    updateRecords(function () {
        return done();
    }, table, nextBillFetchDate, record.id);
}

function getFirstBillFetchInterval(operator) {
    let
        nextBillFetchDates =
            _.get(config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', operator], null);

    if (_.isArray(nextBillFetchDates))
        return nextBillFetchDates[0];
    else if (nextBillFetchDates)
        return nextBillFetchDates;
    else
        return 20; // default value
}

function getIntervals() {
    let intervals = [],
        from = MOMENT().add(-32, 'days'),
        to = MOMENT(),
        index = 0;

    while (from.isBefore(to)) {
        intervals.push({
            f: from.format('YYYY-MM-DD HH:mm:ss'),
            t: from.add(10, 'minutes').format('YYYY-MM-DD HH:mm:ss'),
            i: index
        });
        index = index + 1;
    }
    return intervals;
}

function execute(opts, cb) {

    if ((!opts.operator || !opts.operator.length)) {
        return cb(new Error('please provide operator'));
    }

    L.log('execute', 'Running for operators', opts.operator);

    async.eachLimit(opts.operator, 1, runForOperator, function (error) {
        L.log("execute", "finished for all operators !!");
        return cb(error);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-o, --operator <value>', 'operator', Helper.listBySeparator.bind(null, ';'))
            .parse(process.argv);

        if (commander.verbose) {
            L.setLevel('verbose');
        }

        startup.init({}, function(err,options){
            config = options && options.config;
            dbInstance = options && options.dbInstance;
            execute(commander, function (err) {
                setTimeout(function () {
                    if (err) {
                        console.log(err);
                        console.log('FAILURE');
                        console.timeEnd('Execution Time');
                        process.exit(1);
                    }
                    console.log('SUCCESS');
                    console.timeEnd('Execution Time');
                    process.exit(0);
                }, 1000);
            });
        });

    }
})();

// NODE_ENV=production node dist/scripts/correctBillCycle.js --operator "airtel;nagda tower" -v