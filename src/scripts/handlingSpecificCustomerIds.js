const async = require('async');
import startup from '../lib/startup';
import _ from 'lodash';
import L from 'lgr';

let batch = 1000;
let updates_table_hash={};
let id=0;

 function execDBQuery(query, dbInstance, operationType , cb) {
    L.log(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, MASER_SLAVE_TYPE, query, []);
}

function delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}

function readDataFromtableAndUpdate(configOptions, tableName, cb) {
    let dbInstance = configOptions.dbInstance;
    let recordIds = [];

    let customerIds = _.get(configOptions.config, ['DYNAMIC_CONFIG','HANDLING_CUSTOMER_IDS','COMMON','CUSTOMER_IDS'], []);
 
    console.log("Now using id: ", id);
    
    async.waterfall([
        (next)=> {
            let select_query = `SELECT id FROM ${tableName} WHERE customer_id IN (${customerIds}) AND notification_status <> 0 AND  id > ${id} ORDER BY id limit ${batch};`;
            L.log(`readDataFromtableAndUpdate::select_query:${select_query}`);
            return execDBQuery(select_query, dbInstance, "READ", next);
        },
        (dbResponse, next)=> {
            recordIds =  dbResponse ? dbResponse : [];
            if (recordIds && recordIds.length) {

                if(!updates_table_hash[tableName]) updates_table_hash[tableName]=recordIds.length;
                else updates_table_hash[tableName]+=recordIds.length;


                recordIds = recordIds.map((record)=>{return record.id});
                let update_query = `UPDATE ${tableName} SET notification_status = 0 WHERE id  IN (${recordIds}) ;`;

                L.log(`readDataFromtableAndUpdate::update_query:${update_query}`);
                return execDBQuery(update_query, dbInstance, "WRITE", next);   
            } else {
                L.log(`readDataFromtableAndUpdate::update_query:No Records for table ${tableName} `);
                next(null, []);
            }
        },
        (dbResponse, next)=>{
            let delayTimeinMilliseconds = 100;
            delay(next, delayTimeinMilliseconds);
        }
        ], 
        (err) => {
            if(err) {
                let errMsg = 'Error in readDataFromtableAndUpdate :: ' + err;
                L.error(errMsg);
                return cb(errMsg);
            } 
            if (recordIds && recordIds.length) {    
                id = recordIds[recordIds.length-1];
                readDataFromtableAndUpdate(configOptions, tableName, cb);
            } else {
                id=0;
                cb();
            }
    });

 }


function executeFlow(options, cb) {
    let configOptions = options.configOptions;

    let operatorTableRegistry = _.get(configOptions, 'config.OPERATOR_TABLE_REGISTRY', {});
    let uniqTableNames        = _.uniq(_.values(operatorTableRegistry));
    
    async.eachSeries(
        uniqTableNames,
        (tableName, next) => {
            
            
            readDataFromtableAndUpdate(configOptions, tableName, (error)=>{
                if (error) {
                    next(error);
                } else {
                    next();
                }
            })
        },
        (err) => {
            if(err) {
                L.error('executeFlow::Error',err);
                cb(err);
            } else {
                L.log('executeFlow::completed', 'picked no. of record per table:' ,updates_table_hash);
                cb();
            }
        }
    )

}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, configOptions) {
        if (err) {
            return cb(err);
        }
        executeFlow({
            configOptions: configOptions
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log("main::Error"+err);
                    process.exit(1);
                } else {
                    console.log("main::completed");
                    process.exit(0);
                }
            }, 1000); 
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();