import L from 'lgr'
import _ from 'lodash'
import Async from 'async'
import CRYPTO from 'crypto'
import request from 'request'
import startup from '../lib/startup'
import Helper from '../lib/helper'
import Notify from '../services/notify'

function getRecords(id, dbInstance, cb) {
    let query = `select id, reference_id from bills_paytmfirstcc`;
    let params = [];

    if (id && id.length) {
        query += ` where id in (?)`;
        params.push(id);
    }

    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, params);
}

function updateRecord(record, dbInstance, cb) {
    if (!record || !record.id || !record.reference_id) {
        return cb(new Error(`updateRecord: Invalid record to update: ${JSON.stringify(record)}`));
    }
    let query = `update bills_paytmfirstcc set reference_id = '${record.reference_id}' where id = ${record.id}`;
    let params = [];

    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_MASTER', query, params);
}

function getCardIndexNumber(cardId, options, cb) {
    let cardType = 'CC_BILL_ID';
    let tokenType = 'SHA256';
    let cardIndexNumber = null;
    let signature = CRYPTO.createHash('sha256')
        .update(`${cardId}${tokenType}${cardType}${_.get(options.config, 'COMMON.PG_CARD_INDEX_API_SECRET_KEY', '')}`, 'utf8')
        .digest('hex'); // TODO move to config
    let requestInfo = {
        uri: _.get(options.config, 'COMMON.PG_CARD_INDEX_API_URI', ''),
        method: 'POST',
        json: {
            "head": {
                "tokenType": tokenType,
                "signature": signature
            },
            "body": {
                "cardId": cardId,
                "cardIdType": cardType
            }
        }
    };
    request(requestInfo, function (err, res, body) {
        if (err) {
            L.error(`Error: ${err} for request: ${requestInfo}`);
        } else if (body) {
            try {
                if (body.body.resultInfo.resultMsg === 'Success' && body.body.cardId === cardId) {
                    cardIndexNumber = body.body.cardIndexNumber;
                }
            } catch (e) {
                L.error(`Error: ${e} for response: ${JSON.stringify(body, null, 4)}`);
            }
        }
        return cb(null, cardIndexNumber);
    });
}

function runForAllRecord(opts, options, cb) {
    let notify = new Notify(options);

    L.verbose('fetching record');
    getRecords(opts.id, options.dbInstance, function (err, records) {
        if (err) {
            return cb(err);
        }
        L.verbose('Found records: ' + records.length);
        Async.eachLimit(records, 5, function (record, cb) {
            if (!record.reference_id || isNaN(record.reference_id)) {
                L.error(`Skipping: for record: ${JSON.stringify(record)}`);
                return cb();
            }
            getCardIndexNumber(record.reference_id, options,function (err, cardIndexNumber) {
                if (err) {
                    L.error(`Error: ${err} for record: ${JSON.stringify(record)}`);
                    return cb();
                }
                if (!cardIndexNumber) {
                    L.error(`cardIndexNumber not found for record: ${JSON.stringify(record)}`);
                    return cb();
                }
                record.reference_id = cardIndexNumber;
                updateRecord(record, options.dbInstance, function (err) {
                    if (err) {
                        L.error(`Error: ${err} while updating record: ${JSON.stringify(record)}`);
                    }
                    return cb();
                });
            });
        }, function (err) {
            return cb(err);
        });
    });
}

function execute(opts, cb) {
    L.setLevel('verbose');
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }

        runForAllRecord(opts, options, cb);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-i, --id <value>', 'id', Helper.num_list)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=production node dist/scripts/saved_card_update.js --id 123
