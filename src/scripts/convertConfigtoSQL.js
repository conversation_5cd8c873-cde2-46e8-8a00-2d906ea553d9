'use strict'

const fs = require('fs');
const operatorTemplateMapping = require('./../../dist/config/operatorTemplateMapping');
const operatorTableRegistry = require('./../../dist/config/operatorTableRegistry');
const recentBillConfig = require('./../../dist/config/recentBillConfig');
const operatorNotInUseConfig = require('./../../dist/config/operatorNotInUseToDaysMapping');
const publisherConfig = require('./../../dist/config/publisherConfig');
const subscriberConfig = require('./../../dist/config/subscriberConfig');

// operator template mapping
const exportOperatorTemplate = (filename) => {
    console.log("coming in to write for exportOperatorTemplate");
    let writeStream = fs.createWriteStream(filename);
    for(const [key,values] of Object.entries(operatorTemplateMapping)){
        for(const subKeys in values ){
            let valueType = 'number';
    
            let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('OPERATOR_TEMPLATE_MAPPING', '${key}', '${subKeys}', '${values[subKeys]}', '${valueType}', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);`
            writeStream.write(sqlQuery);
        }
    }
}

// Operator table registry
const exportOperatorTable = (filename) => {
    console.log("coming in to write for exportOperatorTable");
    let writeStream = fs.createWriteStream(filename);
    let tableObject = operatorTableRegistry['default']['common'];
    for(const [key,values] of Object.entries(tableObject)){
        let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('OPERATOR_TABLE_REGISTRY', '${key}', 'TABLE_NAME', '${values}', 'string', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);`
        writeStream.write(sqlQuery);
    }
}

const exportRecentBill = (filename) => {
    console.log("coming in to write for exportRecentBill");
    let writeStream = fs.createWriteStream(filename);
    let operatorObject = recentBillConfig['OPERATORS'];

    for(const [key,values] of Object.entries(operatorObject)){
        for(const subKeys in values ){
            let valueType = 'string';
            
            //just one value in config, saving as number 1
            if(values[subKeys] === true) values[subKeys] = 1
            
            if (typeof values[subKeys] === 'number'){
                valueType = 'number'
            } else if (typeof values[subKeys] === 'object'){   // arrays come as object
                valueType = 'str_list'
            }

            let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('RECENT_BILL_CONFIG', '${key}', '${subKeys}', '${values[subKeys]}', '${valueType}', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);`
            writeStream.write(sqlQuery);
        }
    }
}

const exportpublisherConfig = (filename) => {
    console.log("coming in to write for exportpublisherConfig");
    const configObject = publisherConfig['default'];
    for(const [key,values] of Object.entries(configObject)){
        if(values){
            let filePath = key + '_' + filename;
            let writeStream = fs.createWriteStream(filePath);
            for(const subKeys in values ){
                let valueType = 'number';
                values[subKeys].forEach(element => {
                    let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('PUBLISHER_CONFIG', '${element}', 'PUBLISHER', '${subKeys}', '${valueType}', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);` 
                    writeStream.write(sqlQuery);    
                });
            }         
        }
    }
}

const exportSubscriberConfig = (filename) => {
    console.log("coming in to write for exportSubscriberConfig");
    const configObject = subscriberConfig['default']['common'];
    let writeStream = fs.createWriteStream(filename);
    let valueType = 'number'
    for(const [key,values] of Object.entries(configObject)){
        if(key === 'NEXT_BILL_FETCH_DATES' || key === 'NEXT_RETRY_FREQUENCY'){
            for(const subKeys in values) {
                let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('SUBSCRIBER_CONFIG', '${subKeys}', '${key}', '${values[subKeys]}', '${valueType}', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);`; 
                writeStream.write(sqlQuery);
            }
        } else if(key === 'BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS'){
            values.forEach(element => {
                let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('SUBSCRIBER_CONFIG', '${element}', 'BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', '1', '${valueType}', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);`
                writeStream.write(sqlQuery);
            })
        }
    }
}

const exportOperatorNotInUse = (filename) => {
    console.log("coming in to write for exportOperatorNotInUse");
    const configObject = operatorNotInUseConfig['default']['common'];
    let writeStream = fs.createWriteStream(filename);
    let valueType = 'number';

    for(const [key,values] of Object.entries(configObject)){
        let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status) VALUES('OPERATOR_NOT_IN_USE_CONFIG', '${key}', 'NOTIFICATION_EXPIRY_PERIOD', '${values}', '${valueType}', 1) ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type);`
        writeStream.write(sqlQuery);
    }
}

exportpublisherConfig('publisher_config_query.sql');
exportRecentBill('recent_bills_query.sql');
exportOperatorTemplate('operator_template_mapping_query.sql');
exportSubscriberConfig('subscriber_config_query.sql');
exportOperatorNotInUse('operator_not_in_use_query.sql');
exportOperatorTable('operator_table_registry_query.sql');
