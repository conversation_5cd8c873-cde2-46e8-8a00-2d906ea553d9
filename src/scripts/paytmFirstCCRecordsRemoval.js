/*
import L from 'lgr'
import _ from 'lodash'
import MOMENT from 'moment'
import startup from '../lib/startup'
import async from 'async'
const fs = require('fs');

let whereQuery = `user_data is null AND gateway is null AND recharge_number like '4381 06%' `;
let delayTimeinMilliseconds = 1000;
let writeStream = fs.createWriteStream("bills_creditcard_archived_paytmfirstcc_records.sql");


function execute(opts, cb) {
    L.setLevel('log');

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }
        let batchSize = 1; // <null/1/500>
        runForAllRecord(opts, batchSize, options.dbInstance, cb);
    });
}
function execDBQuery(query, dbInstance, operationType , cb) {
    L.log(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, MASER_SLAVE_TYPE, query, []);
}


function processRecord(duplicate_delete_record, dbInstance, cb) {
    
    async.waterfall([
        (next) =>{
            return delay(next, delayTimeinMilliseconds);
        },
        // Un-comment this before deploy
        // (next)=> {
        //     let delete_query = `DELETE FROM bills_creditcard WHERE id = ${duplicate_delete_record.id} AND ${whereQuery}`;
        //     return execDBQuery(delete_query, dbInstance, "WRITE", next);
        // }  
        ], 
        (err) => {
            if(err) {
                let errMsg = 'Error in processRecord :: ' + err;
                L.error(errMsg);
                return cb(errMsg);
            } 
            cb();    
        });
}

function delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}


function getInsertQuery(payload) {
    let columnHeadersArr = [
        {columnName : 'id' , datatype : 'number'}, {columnName : 'customer_id' , datatype : 'number'}, {columnName : 'recharge_number' , datatype : 'string'}, {columnName : 'product_id' , datatype : 'number'}, {columnName : 'reference_id' , datatype : 'string'}, {columnName : 'operator' , datatype : 'string'}, {columnName : 'amount' , datatype : 'number'}, {columnName : 'bill_date' , datatype : 'datetime'}, {columnName : 'due_date' , datatype : 'datetime'}, {columnName : 'bill_fetch_date' , datatype : 'datetime'}, {columnName : 'next_bill_fetch_date' , datatype : 'datetime'}, {columnName : 'gateway' , datatype : 'string'}, {columnName : 'paytype' , datatype : 'string'}, {columnName : 'service' , datatype : 'string'}, {columnName : 'circle' , datatype : 'string'}, {columnName : 'customer_mobile' , datatype : 'string'}, {columnName : 'customer_email' , datatype : 'string'}, {columnName : 'payment_channel' , datatype : 'string'}, {columnName : 'retry_count' , datatype : 'number'}, {columnName : 'status' , datatype : 'number'}, {columnName : 'reason' , datatype : 'string'}, {columnName : 'extra' , datatype : 'string'}, {columnName : 'published_date' , datatype : 'datetime'}, {columnName : 'created_at' , datatype : 'datetime'}, {columnName : 'updated_at' , datatype : 'datetime'}, {columnName : 'user_data' , datatype : 'string'}, {columnName : 'notification_status' , datatype : 'number'}, {columnName : 'payment_date' , datatype : 'datetime'}, {columnName : 'service_id' , datatype : 'number'}, {columnName : 'customerOtherInfo' , datatype : 'string'}, {columnName : 'is_automatic' , datatype : 'number'}
    ];
    let headerStr = '', valuesStr = '';
    for(let itr = 0 ; itr < columnHeadersArr.length; itr++) {
        let columnHeader = columnHeadersArr[itr].columnName;
        headerStr = headerStr + columnHeader + ( itr <= columnHeadersArr.length - 2 ? ', ' : '');

        let value = _.get(payload, [columnHeader] , '');// default fallback values for each respective column header
        if (columnHeadersArr[itr].datatype == 'string') {
            value = (value === 'null' || value === null) ? null : ("'"+value+"'");
        } else if (columnHeadersArr[itr].datatype == 'datetime') {
            value = MOMENT(value).isValid() ? ("'"+MOMENT(value).format('YYYY-MM-DD')+"'") : null;
        }
        valuesStr = valuesStr + value + ( itr <= columnHeadersArr.length - 2  ? ', ' : '');
    }
    return {
        'headerStr' : headerStr,
        'valuesStr' : valuesStr
    }
}
function runForAllRecord(opts, batchSize, dbInstance, cb) {
    
    let query = `SELECT * FROM bills_creditcard WHERE ${whereQuery} `;
    if(batchSize) {
        query += ` LIMIT ${batchSize}`;
    }
    query += ';';
    
    execDBQuery(query, dbInstance, "READ", (err, records)=>{
        if (err) {
            console.log(err);
            console.log('execDBQuery :: FAILURE');
            L.log(`execDBQuery :: FAILURE`);
            return cb(err);
        }
        if(!records.length) {
            L.log(`No more records left for processing `);
            return cb();
        }
        L.log(`Total records: ${records.length}`);
        async.waterfall([

                next => {
                    async.eachSeries(records, function (currentRecord, cb) {
                        L.log('processing each record : ' + JSON.stringify(currentRecord));
                        let sqlQueryObj =  getInsertQuery(currentRecord);  
let sqlQuery = `
INSERT INTO bills_creditcard ( ${sqlQueryObj.headerStr} ) 
VALUES ( ${sqlQueryObj.valuesStr} );`

                        writeStream.write(sqlQuery);
                        processRecord(currentRecord, dbInstance, function (err) {
                            if (err) {
                                return cb(err);
                            }
                            return cb();
                        });
        
                    },next);
                }

            ],
            (error)=> {
                if(error) {
                    let errorMsg = 'runForAllRecord Error' + error;
                    L.error(errorMsg);
                    return cb(errorMsg);
                } else {
                    runForAllRecord(opts, batchSize, dbInstance, cb);
                }
            });
    })
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();
*/