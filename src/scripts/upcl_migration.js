#!/usr/bin/env node
"use strict";

import _ from 'lodash'
import L from 'lgr'
import SQLWR<PERSON> from 'sqlwrap'
import ASYNC from 'async'
import PROGRAM from 'commander'
import FS from 'fs'
import { parse } from 'fast-csv'
// If required load config from startup.init

class MoveBillsData {

    constructor() {
        this.dbInstance = new SQLWRAP(CONFIG.SQLWRAP)
        this.rechargeNumberMapping = {};
        this.batchSize = PROGRAM.batchSize || 1000;
        this.lastId = PROGRAM.fromIndex || 0;
        this.product_id_new = PROGRAM.product_id_new;
        this.sourceTable = 'bills_bbps_upcl';
        this.destTable = 'bills_bbps_upcl_new';
        this.maxBatches = PROGRAM.maxBatches || 0;
        this.analytics = {
            totalBatches : 0,
            rnMappingNotFound : 0,
            rnMappingFound : 0,
            totalDataFetchedFromSourceTable : 0,
            insertedRecords : 0,
            dataPreparedForInsertion : 0,
            dataSkippedForInsertion : 0,
            errorInProcessing : 0,
            errorInInsertion : 0,
            duplicateData : 0
        };
    }

    start(done) {
        let
            self = this;

        self.analytics.totalBatches++;
        L.log('start::', 'Processing batch', self.analytics.totalBatches);

        if(self.maxBatches && self.analytics.totalBatches > self.maxBatches){
            return self.terminate(done);
        }

        self.fetchData((error, data) => {
            L.log('start', `No. of records fetched for batch-${self.analytics.totalBatches}:${_.get(data, 'length', 0)}`)
            if (error) {
                L.error('start::fetchData', 'Error encountered', error);
                return self.terminate(done);
            }
            else if (_.get(data, 'length', 0) <= 0) {
                L.log('start::fetchData', `No more data found to process for batch ${self.analytics.totalBatches}`);
                return self.terminate(done);
            }
            else {
                self.lastId = data[data.length - 1].id;
                self.analytics.totalDataFetchedFromSourceTable += data.length; 
                self.processData(() => {
                    L.log('start::processData', `Process Data finished for batch :${self.analytics.totalBatches}`);
                    return self.start(done);
                }, data);
            }
        });
    }

    processData(done, records) {
        let
            self = this;

        ASYNC.eachLimit(records, 1, function (record, cb) {
            let dataToInsert = self.genDateToInsert(record);
            if (dataToInsert) {
                self.analytics.dataPreparedForInsertion +=1;
                self.insertData(cb,dataToInsert);
            } else {
                self.analytics.dataSkippedForInsertion +=1;
                return cb();
            }
        }, function (err) {
            if (err) {
                L.error('processData', 'Error occured - ', err);
                self.analytics.errorInProcessing +=1;
            }
            return done();
        });
    }

    genDateToInsert(record) {
        let
            self = this,
            dataToInsert = null,
            oldRechargeNumber = _.get(record, 'recharge_number', null);

        if (oldRechargeNumber && self.rechargeNumberMapping[oldRechargeNumber]) {
            dataToInsert = _.clone(record);
            dataToInsert.recharge_number = self.rechargeNumberMapping[oldRechargeNumber];
            dataToInsert.product_id = self.product_id_new;
            dataToInsert.amount = 0;
            dataToInsert.status = 0;
            try {
                let extra = record.extra ? JSON.parse(record.extra) : {};
                extra.migratedRecord = 1;
                extra.oldRechargeNumber = _.get(record, 'recharge_number', null);
                dataToInsert.extra = JSON.stringify(extra);
            } catch (err) {
                L.error('genDateToInsert', 'Unable to parse extra for id:', _.get(record, 'id', null), 'extra-', record.extra);
            }
            self.analytics.rnMappingFound+=1;
            return dataToInsert;
        } else {
            L.log('genDateToInsert',`Mapping not found for Id:${record.id}_RN:${record.recharge_number}_custId:${record.customer_id}`);
            self.analytics.rnMappingNotFound +=1;
            return null;
        }
    }

    insertData(done, params) {

        let self = this;
        let query = `INSERT INTO ${self.destTable} (customer_id,recharge_number,product_id,operator,gateway,due_date,bill_date,bill_fetch_date,next_bill_fetch_date,
            service,paytype,circle,customer_mobile,customer_email,payment_channel,amount,retry_count,status,reason,user_data, notification_status, payment_date, service_id,extra,customerOtherInfo,is_automatic)                
            VALUES ?`,
            queryParams = [[[
                params.customer_id,
                params.recharge_number,
                params.product_id,
                params.operator,
                params.gateway,
                params.due_date,
                params.bill_date,
                params.bill_fetch_date,
                params.next_bill_fetch_date,
                params.service,
                params.paytype,
                params.circle,
                params.customer_mobile,
                params.customer_email,
                params.payment_channel,
                params.amount,
                params.retry_count,
                params.status,
                params.reason,
                params.user_data,
                params.notification_status,
                params.payment_date,
                params.service_id,
                params.extra,
                params.customerOtherInfo,
                params.is_automatic
            ]]];

        L.verbose('insertData', 'INSERT Query is ', self.dbInstance.format(query, queryParams));

        self.dbInstance.exec(function (err) {
            if(err && err.code == 'ER_DUP_ENTRY') {
                L.log('insertData','Duplicate entry for -',`Id:${params.id}_RN:${params.recharge_number}_custId:${params.customer_id}`);
                self.analytics.duplicateData +=1;
                return done(null);
            } else if (err) {
                L.error('dataToInsert', 'error while executing', err);
                self.analytics.errorInInsertion +=1;
            } else {
                L.log('insertData','Insert successful for -',`Id:${params.id}_RN:${params.recharge_number}_custId:${params.customer_id}`);
                self.analytics.insertedRecords +=1;
            }
            return done(err);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    fetchData(done) {
        let
            self = this,
            tableName = self.sourceTable,
            query = `SELECT * FROM ${tableName} WHERE id > ${self.lastId} limit ${self.batchSize}`;

        if (self.lastId == PROGRAM.fromIndex) {
            L.log('Executing query', query);
        }

        self.dbInstance.exec(function (err, records) {
            L.verbose('fetchData', 'found records', records);
            if (err) L.critical('error while executing query:', query, err)
            return done(err, records)
        }, 'DIGITAL_REMINDER_SLAVE', query);
    }

    processCSV(done, filePath) {
        let self = this;

        if (!FS.existsSync(filePath)) {
            return done(`Invalid path: ${filePath}`);
        }

        FS.createReadStream(filePath)
            .pipe(parse({ ltrim: true, rtrim: true, headers: true, ignoreEmpty: true }))
            .on('data', data => {
                self.rechargeNumberMapping[data.rn_old] = [data.rn_new];
            })
            .on('end', rowCount => {
                L.log(`${rowCount} records processed succesfully !!`);
                return done();
            })
            .on('error', error => {
                L.error("processCSV", "Error", error);
            });
    }

    terminate(done){
        L.log(`Stats : ${JSON.stringify(this.analytics,null,2)}`);
        this.dbInstance.close(()=>{
            L.log('terminate','Terminating cron...');
            return done();
        });
    }
}

(function () {
    if (require.main === module) {
        console.time('Execution Time');
        PROGRAM
            .version('0.0.1')
            .option('-p, --product_id_new <value>', 'product_id_new', Number, 331840508)
            .option('-b, --batchSize <value>', 'batchSize', Number, 1000)
            .option('-m, --maxBatches <value>', 'maxBatches', Number, 0) 
            .option('-r, --path <value>', 'path', String)
            .option('-f, --fromIndex <value>', 'fromIndex', Number,0)
            .parse(process.argv);

        let MoveBillsDataObj = new MoveBillsData();
        MoveBillsDataObj.processCSV(function (err) {
            if(err) {
                L.error('Error reading csv file...',err);
            } else {
                MoveBillsDataObj.start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        process.exit(0);
                    }, 1000);
                });
            }
        }, PROGRAM.path);
    }
})();

//node dist/scripts/upcl_migration.js -r '/Users/<USER>/Downloads/Account_no_details_npci.csv -b 1000 -m 1 -f 10