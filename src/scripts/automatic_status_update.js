const async = require('async');
import startup from '../lib/startup'
import _ from 'lodash'
import utility from '../lib'
import L from 'lgr'

let OPTIONS = null;
let id = 0;
let operatorTableMap;

function readDataFromBillsTable(options, data, cb) {
    async.eachLimit(data, 50, function (row, localCb) {
        setImmediate(function () {
            let tableName = _.get(OPTIONS.config, ['OPERATOR_TABLE_REGISTRY', row.operator], null);
            if (tableName == null) {
                return localCb(null)
            }
                let writeQuery1 = `UPDATE ${tableName} SET is_automatic = 1 WHERE recharge_number = '${row.recharge_number}' and customer_id=${row.customer_id} and operator = '${row.operator}' and service = '${row.service}'`
                let writeQuery2 = `UPDATE ${tableName} SET is_automatic = 2 WHERE recharge_number = '${row.recharge_number}' and customer_id!=${row.customer_id} and operator = '${row.operator}' and service = '${row.service}'`
                L.log("Executing query:", writeQuery1)
                L.log("Executing query:", writeQuery2)

                async.parallel([
                    (cbparallel) => {
                        OPTIONS.dbInstance.exec(function (err, data) {
                            if(err) {
                                L.error("Error while executing query : ", writeQuery1, err);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:ERROR','TYPE:UPDATING_BILLS',`RN:${row.recharge_number}`]);
                                cbparallel(err)
                            }else {
                                L.log(`is_automatic=1 status updated successfully for RN:${row.recharge_number}_CID:${row.customer_id}_table:${tableName}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:RECORD_UPDATED','TYPE:AUTOMATIC_1',`TABLE:${tableName}`]);
                                cbparallel(null);
                            }
                        }, 'DIGITAL_REMINDER_MASTER', writeQuery1, []);
                    },
                    (cbparallel) => {
                        OPTIONS.dbInstance.exec(function (err, data) {
                            if(err) {
                                L.error("Error while executing query : ", writeQuery2, err);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:ERROR','TYPE:UPDATING_BILLS',`RN:${row.recharge_number}`]);
                                cbparallel(err)
                            }else {
                                L.log(`is_automatic=2 status updated successfully for RN:${row.recharge_number}_CID:${row.customer_id}_table:${tableName}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:RECORD_UPDATED','TYPE:AUTOMATIC_2',`TABLE:${tableName}`]);
                                cbparallel(null);
                            }
                        }, 'DIGITAL_REMINDER_MASTER', writeQuery2, []);
                    }
                ], (err) => {
                    return localCb(null);
                }
            );
        });
    }, (err) => {
        if (err) {
            L.error("Updation failed with error", err)
        }
        return cb(err);
    });
}


function readDataFromSubscription(options, cb) {
    let returnData = []
    if(id> 105487000){
        L.log("No more records left in subscription table");
        return cb(null);
    }
    let sql = `SELECT * from ${options.subscriptionTableName} where status = 1 and id > ${id} ORDER BY id limit ${options.size}`;
    L.log("subscription record fetching query", sql);
    OPTIONS.dbInstance.exec(function (err, data) {
        if (err || data.length == 0) {
            if(err) {
                L.error("Error while fetching records from subscription table", err);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:ERROR','TYPE:FETCHING_SUBSCRIPTION_TABLE']);
            }
            if(data.length==0) L.log("No more records left in subscription table");
            return cb(err);
        }
        L.log(`${data.length} fresh records fetched from subscription table`);
        utility._sendMetricsToDD(data.length, ['REQUEST_TYPE:AUTOMATIC_STATUS_FIX_SCRIPT','STATUS:RECORD_FETCHED','TYPE:SUBSCRIPTION_TABLE']);
        data.forEach(element => {
            let row = {}
            if(!element.customer_id || !element.operator || !element.recharge_number) {
                return;
            }
            row['recharge_number'] = element.recharge_number;
            row['customer_id'] = element.customer_id;
            row['operator'] = element.operator;
            row['updated_at'] = element.updated_at;
            row['service'] = element.service;
            returnData.push(row)
        });
        id = data[data.length-1].id //max id
        return cb(null, returnData);
    }, 'FS_RECHARGE_SLAVE1', sql, []);
}

function executeRecursive(options, cb){
    let startDate = new Date();
    L.log("Start time for iteration", startDate);
    readDataFromSubscription(options, function(err, data) {
        if(err){
            return cb(err);
        }
        if(!data) {
            return cb("no more data in subscription table")
        }
        readDataFromBillsTable(options, data, function (err) {
            let endDate = new Date();
            L.log("End time for iteration", endDate);
            L.log("Execution time for iteration in seconds", (endDate-startDate)/1000)
            L.log("completed 1 iteration")
            executeRecursive(options, cb)
        })
    })
}

function executeFlow(options, cb) {
    executeRecursive(options, function (err) {
        if (err) {
            return cb(err);
        }
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: false
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        operatorTableMap = _.get(OPTIONS.config, 'OPERATOR_TABLE_REGISTRY', {});
        if(Object.keys(operatorTableMap).length == 0){
            return cb("table config is empty");
        }
        executeFlow({
            subscriptionTableName: 'subscription',
            size: 1000
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    L.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
