import L from 'lgr'
import _ from 'lodash'
import Helper from '../lib/helper'
import startup from '../lib/startup'
import Publisher from '../services/airtelPublisher'
import async from 'async'

function getRecordFromId(id, tableName, dbInstance, cb) {
    let query = `select * from ${tableName} where id in (${id})`;

    <PERSON>.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecordFromRechargeNumber(recharge_number, tableName, dbInstance, cb) {
    let query = `select * from ${tableName} where recharge_number in ("${recharge_number.join('","')}")`;

    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecord(opts, tableName, dbInstance, cb) {
    if (opts.recharge_number && opts.recharge_number.length) {
        getRecordFromRechargeNumber(opts.recharge_number, tableName, dbInstance, cb);
    } else if (opts.id && opts.id.length) {
        getRecordFromId(opts.id, tableName, dbInstance, cb);
    } else {
        return cb(null, []);
    }
}

function runForAllRecord(opts, tableName, options, cb) {
    let publisher = new Publisher({
        ...options,
        dry_run : _.get(opts,'dry_run',0)
    });

    async.waterfall([
        next => {
            return publisher.billSubscriber._configureKafkaBillFetchPublisher(next);
        },
        next => {
            return publisher.fetchToken(next);
        },
        next => {
            return getRecord(opts, tableName, options.dbInstance,next)
        },
        (records,next) => {
            let RN = _.map(records,'recharge_number');
            publisher.validityMap = {};
            publisher.fetchValidity(RN,function(){
                return next(null,records);
            });
        },
        (records,next) => {
            async.eachSeries(records, function (currentRecord, cb) {
                L.verbose('Running publisher for record: ' + JSON.stringify(currentRecord));
                publisher._processRecords(currentRecord, function (err) {
                    if (err) {
                        return cb(err);
                    }
                    return cb();
                });
            }, next);
        }
    ],function(error){
        if(error) {
            L.error('runForAllRecord','Error',error);
        }
        return cb();
    });
}

function execute(opts, cb) {
    L.setLevel('verbose');

    if ((!opts.recharge_number || !opts.recharge_number.length) && (!opts.id || !opts.id.length)) {
        return cb(new Error('please provide recharge_number or id'));
    }

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }

        runForAllRecord(opts, 'bills_airtelprepaid', options, cb);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-i, --id <value>', 'id', Helper.num_list)
            .option('-r, --recharge_number <value>', 'recharge_number', Helper.list)
            .option('-d, --dry_run <value>', 'dry_run', Number)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

/**
 * Optional param dry_run : If value = 1  will just print db and kafka transactions
 *                        : If value = 0 it will process record
 */
// NODE_ENV=staging node dist/scripts/airtel_publisher_single.js --recharge_number '6889211000' --dry_run 1
// NODE_ENV=staging node dist/scripts/airtel_publisher_single.js --id '123' --dry_run 1
// NODE_ENV=staging node dist/scripts/airtel_publisher_single.js --recharge_number '6889211000' --dry_run 0

