const async = require('async');
import startup from '../lib/startup'
import _ from 'lodash'

let OPTIONS = null;
let id = 0;
let diffCounter = 0;
let recordsFetched = 0;
let recordsWritten = 0;
let operatorTableMap;

function readDataFromBillsTable(options, data, cb) {
    async.eachLimit(data, 50, function (row, localCb) {
        setImmediate(function () {
            let tableName = _.get(OPTIONS.config, ['OPERATOR_TABLE_REGISTRY', row.operator], null);
            if (tableName == null) {
                return localCb("tableName not found")
            }
            let sql = `SELECT is_automatic from ${tableName} WHERE recharge_number = '${row.recharge_number}' and operator = '${row.operator}' and service = '${row.service}'`;
            OPTIONS.dbInstance.exec(function (err, billsdata) {
                if (err) {
                    return localCb(err);
                }
                recordsFetched++;
                billsdata = JSON.parse(JSON.stringify(billsdata))
                if(billsdata &&  (billsdata.length > 0) && (billsdata.filter(data => data.is_automatic === 0).length )) {
                    diffCounter += billsdata.length;
                    console.log(`rech num: ${row.recharge_number}' and operator = '${row.operator}' and service = '${row.service}'`)
                    let writeQuery = `UPDATE ${tableName} SET is_automatic = 1 WHERE recharge_number = '${row.recharge_number}' and operator = '${row.operator}' and service = '${row.service}'`
                    OPTIONS.dbInstance.exec(function (err, data) {
                        if(err) {
                            return localCb(err)
                        }
                        recordsWritten += data.affectedRows;
                        return localCb(null);
                    }, 'DIGITAL_REMINDER_MASTER', writeQuery, []);
                } else {
                    return localCb(null);
                }
            }, 'DIGITAL_REMINDER_SLAVE', sql, []);
        });
    }, cb);
}


function readDataFromSubscription(options, cb) {
    let returnData = []
    console.log("Now using id: ", id)
    let sql = `SELECT * from ${options.subscriptionTableName} where status = 1 and id > ${id} ORDER BY id limit ${options.size}`;
    OPTIONS.dbInstance.exec(function (err, data) {
        if (err || data.length == 0) {
            console.log("number of mismatched records: ", diffCounter);
            console.log("total records compared from bills tables: ", recordsFetched);
            console.log("total records written: ", recordsWritten);
            return cb(err);
        }
        data.forEach(element => {
            let row = {}
            if(!element.customer_id || !element.operator || !element.recharge_number) {
                return;
            }
            row['recharge_number'] = element.recharge_number;
            row['customer_id'] = element.customer_id;
            row['operator'] = element.operator;
            row['updated_at'] = element.updated_at;
            row['service'] = element.service;
            returnData.push(row)
        });
        id = data[data.length-1].id //max id
        return cb(null, returnData);
    }, 'FS_RECHARGE_SLAVE1', sql, []);
}

function executeRecursive(options, cb){
    readDataFromSubscription(options, function(err, data) {
        if(err){
            return cb(err);
        }
        if(!data) {
            return cb("no more data in subscription table")
        }
        readDataFromBillsTable(options, data, function (err) {
            executeRecursive(options, cb)
        })
    })
}

function executeFlow(options, cb) {
    executeRecursive(options, function (err) {
        if (err) {
            return cb(err);
        }
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: false
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        operatorTableMap = _.get(OPTIONS.config, 'OPERATOR_TABLE_REGISTRY', {});
        if(Object.keys(operatorTableMap).length == 0){
            return cb("table config is empty");
        }
        executeFlow({
            subscriptionTableName: 'subscription',
            size: 1000
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
