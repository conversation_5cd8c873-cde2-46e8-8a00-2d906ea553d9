const async = require('async');
import startup from '../lib/startup'


let OPTIONS = null;
let id = 0;


function readDataFromPVAndUpdate(options, cb) {
    console.log("Now using id: ", id)
    let sql = `SELECT id, category_name from ${options.pvTableName} where  id > ${id} and order_date >'2021-12-08' and order_date < '2022-01-07' and validity_expiry_date >'2022-01-09' ORDER BY id limit ${options.size}`;
    console.log("Read query-------", sql);
    OPTIONS.dbInstance.exec(function (err, data) {
        console.log("data  read in Pv", JSON.stringify(data))
        if (err || data.length == 0) {
            if(err){
                console.log("error in fetching data : ", err, "for query:  ", sql);
            }
            console.log("data not found: ", JSON.stringify(data));
            return cb(err);
        }
        data.forEach(element => {
            if(element.category_name){
                let SqlCatnameNull = `update ${options.pvTableName} set category_name='Recharge' where id= ${element.id}`;
                console.log("SqlCatnameNull query-------", SqlCatnameNull);
                OPTIONS.dbInstance.exec(function (err, updateDataNull) {
                    console.log("updateDataNull in  Pv", JSON.stringify(updateDataNull))
                    if (err) {
                        console.log("error in update category name null for data", JSON.stringify(data))
                        return cb(err);
                    }
                    let SqlCatnameOrig = `update ${options.pvTableName} set category_name="${element.category_name}" where id= ${element.id}`;
                    console.log("SqlCatnameOrig query-------", SqlCatnameOrig);
                    OPTIONS.dbInstance.exec(function (err, updateDataOrig) {
                        console.log("updateDataOrig in  Pv", JSON.stringify(updateDataOrig))
                        if (err) {
                            console.log("error in update category name Original for data", JSON.stringify(data))
                            return cb(err);
                        }
                    }, 'RECHARGE_ANALYTICS', SqlCatnameOrig, []);


                }, 'RECHARGE_ANALYTICS', SqlCatnameNull, []);
            
         }
        });
        id = data[data.length-1].id //max id
        setTimeout(() => {
            readDataFromPVAndUpdate(options, cb);// recursive call

        }, 2000);
        
    }, 'RECHARGE_ANALYTICS_SLAVE', sql, []);

    
}


function executeFlow(options, cb) {
    readDataFromPVAndUpdate(options, function (err) {
        if (err) {
            console.log("error in main flow", err);
            
        }
        return cb(err);
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            pvTableName: 'plan_validity',
            size: 500
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                //process.exit(0);
            }, 86400000);// 24 hr
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
