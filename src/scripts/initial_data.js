const request = require('request');
const moment = require('moment');
const async = require('async');
import startup from '../lib/startup'

let OPTIONS = null;

function getDataFromES(options, dateNode, cb) {
    let requestInfo = {
        uri: 'http://internal-digital-searchstats-alb-2006557837.ap-south-1.elb.amazonaws.com/recharge_mis_*_2020/_search',
        method: 'POST',
        json: {
            "_source": [
                "userData_recharge_number",
                "userData_recharge_number_2",
                "userData_recharge_number_3",
                "userData_recharge_number_4",
                "userData_recharge_number_5",
                "userData_recharge_number_6",
                "userData_recharge_number_7",
                "customerInfo_customer_id",
                "catalogProductID",
                "productInfo_operator",
                "currentGw",
                "productInfo_paytype",
                "productInfo_service",
                "productInfo_circle",
                "customerInfo_customer_phone",
                "customerInfo_customer_email",
                "timestamps_init"
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "productInfo_operator": options.operator
                            }
                        },
                        {
                            "range": {
                                "timestamps_init": {
                                    "gte": moment(dateNode[0]).unix(),
                                    "lt": moment(dateNode[1]).unix()
                                }
                            }
                        }

                    ]
                }
            },
            "size": 10000
        }
    };

    request(requestInfo, function (err, res, body) {
        if (err) {
            return cb(err);
        }
        let data = [];
        try {
            if (body) {
                console.error(`ES: ${body.hits.total}, data: ${body.hits.hits.length}, dates: ${dateNode}`);
                body.hits.hits.forEach(function (hit) {
                    let n = hit._source;
                    let paymentDate = moment(n.timestamps_init);
                    let row = {};
                    row.userData_recharge_number = n.userData_recharge_number;
                    row.customerInfo_customer_id = n.customerInfo_customer_id;
                    row.catalogProductID = n.catalogProductID;
                    row.productInfo_operator = n.productInfo_operator;
                    row.currentGw = n.currentGw;
                    row.productInfo_paytype = n.productInfo_paytype;
                    row.productInfo_service = n.productInfo_service;
                    row.productInfo_circle = n.productInfo_circle;
                    row.customerInfo_customer_phone = n.customerInfo_customer_phone;
                    row.customerInfo_customer_email = (n.customerInfo_customer_email && n.customerInfo_customer_email.trim()) || '';
                    row.timestamps_init = moment(n.timestamps_init).format("YYYY-MM-DD HH:mm:ss");
                    row.next_bill_fetch_date = options.getNextBillFetchDate(paymentDate);
                    row.user_data = JSON.stringify({
                        recharge_number_2: n.userData_recharge_number_2,
                        recharge_number_3: n.userData_recharge_number_3,
                        recharge_number_4: n.userData_recharge_number_4,
                        recharge_number_5: n.userData_recharge_number_5,
                        recharge_number_6: n.userData_recharge_number_6,
                        recharge_number_7: n.userData_recharge_number_7,
                    });
                    data.push(row);
                });
            }
        } catch (e) {
            return cb(e);
        }
        return cb(null, data);
    });
}

function insertDataInDB(options, data, cb) {
    async.eachLimit(data, 50, function (row, cb) {
        setImmediate(function () {
            let sql = `insert into ${options.tableName} \
        (recharge_number, customer_id, product_id, operator, gateway, paytype, service, circle, customer_mobile, customer_email, user_data, payment_date, next_bill_fetch_date) \
        values (?) ON DUPLICATE KEY UPDATE payment_date=VALUES(payment_date), next_bill_fetch_date=VALUES(next_bill_fetch_date)`;
            let params = [
                row.userData_recharge_number,
                row.customerInfo_customer_id,
                row.catalogProductID,
                row.productInfo_operator,
                row.currentGw,
                row.productInfo_paytype,
                row.productInfo_service,
                row.productInfo_circle,
                row.customerInfo_customer_phone,
                row.customerInfo_customer_email,
                row.user_data,
                row.timestamps_init,
                row.next_bill_fetch_date
            ];
            OPTIONS.dbInstance.exec(function (err, data) {
                if (err) {
                    return cb(err);
                }
                return cb(null, data);
            }, 'DIGITAL_REMINDER_MASTER', sql, [params]);
        });
    }, cb);
}


function executeSingle(options, dateNode, cb) {
    getDataFromES(options, dateNode, function (err, data) {
        if (err) {
            return cb(err);
        }
        insertDataInDB(options, data, function (err) {
            return cb(err, data);
        });
    });
}

function executeRecursive(options, cb) {
    let FROM = moment(options.fromDate);
    let TO = moment(options.toDate);
    let dates = [];

    for (let temp = FROM; temp.isBefore(TO);) {
        let from = temp.format('YYYY-MM-DD HH:mm:ss');
        let to = temp.add(options.interval, 'minutes').format('YYYY-MM-DD HH:mm:ss');
        dates.push([from, to]);
    }
    async.eachLimit(dates, options.esParallelHits, function (dateNode, cb) {
        setTimeout(function () {
            executeSingle(options, dateNode, cb);
        }, 50);
    }, cb);
}

function executeFlow(options, cb) {
    executeRecursive(options, function (err) {
        if (err) {
            return cb(err);
        }
        return cb();
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            tableName: 'bills_lntfinance',
            operator: 'l&t finance limited',
            fromDate: "2020-06-01", // Date.parse("2020-03-01") / 1000,
            toDate: "2020-06-02", // Date.parse("2020-06-01") / 1000,
            interval: 60, // minutes
            size: 1000,
            esParallelHits: 5,
            getNextBillFetchDate: function (paymentDate) {
                return paymentDate
                    .add(moment().diff(paymentDate, 'month', true), 'month')
                    .add(20, 'days').format("YYYY-MM-DD HH:mm:ss");
            }
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
