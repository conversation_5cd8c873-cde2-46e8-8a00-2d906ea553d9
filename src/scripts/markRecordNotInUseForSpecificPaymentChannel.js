const async = require('async');
import startup from '../lib/startup';
import _ from 'lodash';
import L from 'lgr';

const paymentChannel = "DIGITAL-EBPS 0";
let batch = 1000;

/**
 1. List bills_<operator> table & make mapper for operator -> tableName
 2. Traverse all operators or selected operators
 3. Iterate on all records having paymentChannel == "X" and status <> 13
 4. Update eligible record batch status = 13 
 */

 function execDBQuery(query, dbInstance, operationType , cb) {
    L.log(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, MASER_SLAVE_TYPE, query, []);
}

function delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}

 function readDataFromtableAndUpdate(configOptions, tableName, cb) {
    let dbInstance = configOptions.dbInstance;
    let recordIds = [];

    async.waterfall([
        (next)=> {
            let select_query = `SELECT id FROM ${tableName} WHERE status <> 13 AND payment_channel = '${paymentChannel}' ORDER BY id limit ${batch};`;
            L.log(`readDataFromtableAndUpdate::select_query:${select_query}`);
            return execDBQuery(select_query, dbInstance, "READ", next);
        },
        (dbResponse, next)=> {
            recordIds =  dbResponse ? dbResponse : [];
            if (recordIds && recordIds.length) {
                recordIds = recordIds.map((record)=>{return record.id});
                let update_query = `UPDATE ${tableName} SET status = 13 WHERE id  IN (${recordIds}) AND payment_channel = '${paymentChannel}' ;`;

                L.log(`readDataFromtableAndUpdate::update_query:${update_query}`);
                return execDBQuery(update_query, dbInstance, "WRITE", next);    
            } else {
                L.log(`readDataFromtableAndUpdate::update_query:No Records for table ${tableName} `);
                next(null, []);
            }
        },
        (dbResponse, next)=>{
            let delayTimeinMilliseconds = 2000;
            delay(next, delayTimeinMilliseconds);
        }
        ], 
        (err) => {
            if(err) {
                let errMsg = 'Error in readDataFromtableAndUpdate :: ' + err;
                L.error(errMsg);
                return cb(errMsg);
            } 
            if (recordIds && recordIds.length) {          
                readDataFromtableAndUpdate(configOptions, tableName, cb)
            } else {
                cb();
            }
    });


 }
function executeFlow(options, cb) {
    let configOptions = options.configOptions;

    let operatorTableRegistry = _.get(configOptions, 'config.OPERATOR_TABLE_REGISTRY', {});
    let uniqTableNames        = _.uniq(_.values(operatorTableRegistry));


    async.eachSeries(
        uniqTableNames,
        (tableName, next) => {
            
            // if (_.get(options, ['config','DYNAMIC_CONFIG', 'PUBLISHER_REPORT', 'EXCLUDE_TABLE_FROM_PUBLISHER_REPORT', tableName], null)) {
            //     return next();
            // }
            readDataFromtableAndUpdate(configOptions, tableName, (error)=>{
                if (error) {
                    next(error);
                } else {
                    next();
                }
            })
        },
        (err) => {
            if(err) {
                L.error('executeFlow::Error',err);
                cb(err);
            } else {
                L.log('executeFlow::completed');
                cb();
            }
        }
    )

    


}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, configOptions) {
        if (err) {
            return cb(err);
        }
        executeFlow({
            configOptions: configOptions
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log("main::Error"+err);
                    process.exit(1);
                } else {
                    console.log("main::completed");
                    process.exit(0);
                }
            }, 1000); 
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
