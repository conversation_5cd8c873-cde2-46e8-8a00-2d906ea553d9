/**
 * Running the script
 * NODE_ENV=production node dist/scripts/updateConsent.js 
 * 
 * For one batch only -
 * GREYSCALE=1 NODE_ENV=production node dist/scripts/UpdateConsent.js 
 * 
 * For different starting ID
 * NODE_ENV=production node dist/scripts/UpdateConsent.js -s 1000
 */

import L from 'lgr'
import _, { last } from 'lodash'
import ASYNC from 'async'
import MOMENT from 'moment'
import startup from '../lib/startup'
import utility from '../lib'
import ReminderConsentConsumer from '../services/reminderConsentConsumer'
const PARALLEL_HITS = 5;
const fetchDelay = 200;
// import DeleteFromAllSystem from '../lib/deleteFromAllSystem'

class UpdateConsent {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.dbBatchSize = _.get(options, 'greyScaleEnv', false) == false ? 100 : 5
        //this.dbUpdateBatchSize =  _.get(options, 'greyScaleEnv', false) == false ? 100 : 2
        this.id = _.get(options,'startingID',0);
        this.dbInstance=options.dbInstance;
        this.consentConsumer = new ReminderConsentConsumer(options);
    }

    start(cb, opts) {
        let self = this;

        //self.L.setLevel('verbose');
        
        self.fetchRecords(function _doUntilNoMoreRecords(error, data) {
            if (error) {
                self.L.error('UpdateConsent', `Error in fetching records ${error} for starting id ${self.id}`);
                return cb(error);
            }
            if (!error && data && data.length > 0) {
                self._processRecordsInBatch(() => {
                    self.id  = _.last(data)['id']

                    if (self.greyScaleEnv) {
                        self.L.log('UpdateConsent', `end time in processing one batch for Grayscale ${MOMENT().format('YYYY-MM-DD HH:mm:ss')} with starting id ${self.id}`)
                        return cb();
                    }
                    
                    self.L.log('UpdateConsent', `processing next batch , Starting id  ${self.id}`);
                    setTimeout(() => {
                        self.fetchRecords(_doUntilNoMoreRecords);
                    }, fetchDelay);
                },data);
            }
            else {
                self.L.log('UpdateConsent', `No record found for Starting id  ${self.id}`);
                cb();
            }
        });

        // self.path = _.get(opts, 'path', null);
        // this.processCSV(function(err){
        //     return cb(err)
        // },)
    }


    fetchRecords(done) {
        let self = this;
        self.L.log('UpdateConsent', `processing next batch, Starting time ${MOMENT().format('YYYY-MM-DD HH:mm:ss')} with starting id ${self.id}`);
        const query = `SELECT * from bill_fetch_consents where id > ${self.id} order by id asc limit ${self.dbBatchSize} `;
        self.L.log("fetchRecords" ,query,[self.dbBatchSize , self.id])
        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('fetchRecords::', query, error);
            }
            return done(error,res);
        }, 'FS_RECHARGE_SLAVE*', query, [self.dbBatchSize , self.id]);
    }


 

    _processRecordsInBatch(done, records) {
        let
            self = this;
       
        ASYNC.eachLimit(records , PARALLEL_HITS , 
            async (record , next)=>{
                let consentKey = _.get(record, 'consent_key', '');
                const processedRecord = {
                    customerId: _.get(record, 'customer_id', null),
                    consentValue: 1,
                    bankCode: _.get(record, 'operator_identifier', null),
                    mobileNumber: consentKey.split("_")[1],
                    lastFourDigits: consentKey.split("_")[0]
                };    
                
                self.L.info(JSON.stringify(record));
                if (processedRecord.lastFourDigits.length != 4) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:UPDATE_CONSENT',
                        'STATUS:SKIPPED']);
                    return next();
                } else {
                    await self.consentConsumer._processMessage(processedRecord, next);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:UPDATE_CONSENT',
                        'STATUS:SUCCESS']);
                }
            },
        function(error){
            if(error){
                self.L.critical("","Error in processing records",error)
            }
            return done()
        })
    
    }

}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-s, --startingID <value>', 'startingID', Number)
            .parse(process.argv);

        startup.init({
        }, function (err, options) {
            if (err) {
                L.critical(' failed to load', err);
                process.exit(1);
            }
            try{
            let script = new UpdateConsent({...options,startingID:commander.startingID});
            script.start(function (err) {
                setTimeout(function () {
                    if (err) {
                        console.log(err);
                        console.log('FAILURE');
                        console.timeEnd('Execution Time');
                        process.exit(1);
                    }
                    console.log('SUCCESS');
                    console.timeEnd('Execution Time');
                    process.exit(0);
                }, 1000);
            }, commander);
        }catch(err){
            console.log(err)
        }
        });
    }
})();

