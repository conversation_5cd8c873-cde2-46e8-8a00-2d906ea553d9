'use strict';

/**
 * Load config from startupLib.init
 * Not making any changes due to one time script
 */

import {parse} from 'fast-csv';

const fs = require('fs');
const async = require('async');
const request = require('request');
const moment = require('moment');
const rechargeConfig = require('recharge-config');
const _ = require('lodash');

let arr = [];

function getAuthCode(options, cb) {
    let apiOpts = {
        method: 'POST',
        url: _.get(rechargeConfig, 'auth.endpointReminder', '') + _.get(rechargeConfig, 'auth.create_token_api', ''),
        headers: {
            authorization: options.authorizationKey,
            'content-type': 'application/x-www-form-urlencoded',
            'cache-control': 'no-cache'
        },
        body: 'response_type=code&client_id=' + options.authConfig.clientKey + '&do_not_redirect=true&scope=paytm&username=' + options.authConfig.auth_username + '&password=' + options.authConfig.auth_password
    };

    request(apiOpts, (error, response, body) => {
        if (body && typeof body === 'string') {
            try {
                body = JSON.parse(body);
                options.authCode = body.code;
            } catch (error) {
            }
        }
        if (error || !options.authCode) {
            return cb(new Error(`Not able to find authCode: ${error}`));
        }
        return cb();
    });
}

function getAuthToken(options, cb) {
    let apiOpts = {
        method: 'POST',
        url: options.authConfig.endpointReminder + options.authConfig.get_token_api,
        //json: true,
        headers: {
            authorization: options.authorizationKey,
            'content-type': 'application/x-www-form-urlencoded'
        },
        body: 'grant_type=authorization_code&code=' + options.authCode + '&client_id=' + options.authConfig.clientKey + '&scope=paytm&client_secret=' + options.authConfig.clientSecretAuth
    };

    request(apiOpts, (error, response, body) => {
        if (body && typeof body === 'string') {
            try {
                body = JSON.parse(body);
                options.authToken = body.access_token;
            } catch (error) {
            }
        }
        if (error || !options.authToken) {
            return cb(new Error(`Not able to find authToken: ${error}`));
        }
        return cb();
    });
}

function getCustMobile(row, options, cb) {
    let apiOpts = {
        method: 'GET',
        url: options.authConfig.endpointReminder + options.authConfig.fetch_user_api + '?fetch_strategy=BASIC&user_id=' + row.customer_id,
        headers: {
            authorization: options.authorizationKey,
            verification_type: 'service_token',
            data: options.authToken,
        },
        json: true
    };

    request(apiOpts, (error, response, body) => {
        if (body && typeof body === 'string') {
            try {
                body = JSON.parse(body);
            } catch (error) {
            }
        }
        if (!body) {
            body = {};
        }
        row.customer_phone = _.get(body, 'basicInfo.phone', null);
        if (!row.customer_phone) {
            row.error += '; CUSTOMER_PHONE_NOT_FOUND';
        }
        return cb();
    });
}

function getOrderData(row, options, cb) {
    request({
        "uri": `https://order.paytm.com/v2/merchant/0/items_v2.json?order_id=${row.order_id}`,
        "method": "GET",
        "headers": {
            "cookie": `ff.sid=${options.FF_SID}`
        }
    }, function (err, res, body) {
        if (err) {
            row.error += '; ITEM_V2_ERR: ' + err;
            return cb(row.error);
        }
        if (body && typeof body === 'string') {
            try {
                body = JSON.parse(body);
            } catch (error) {
            }
        }
        if (!body) {
            body = {};
        }

        let customer_id = _.get(body, 'data[0].order.customer_id', null);
        if (!options.TEST_CUSTOMER_ID && row.customer_id != customer_id) {
            row.error += `; CUSTOMER_ID_MISMATCH: CSV=${row.customer_id} - ORDER=${customer_id}`;
            return cb(row.error);
        }

        row.product_id = _.get(body, 'data[0].product_id', null);
        if (!row.product_id) {
            row.error += '; PRODUCT_ID_NOT_FOUND';
            return cb(row.error);
        }

        row.order_name = _.get(body, 'data[0].name', null);
        if (!row.order_name) {
            row.error += '; ORDER_NAME_NOT_FOUND';
            return cb(row.error);
        }

        row.order_price = _.get(body, 'data[0].price', null);
        if (!row.order_price) {
            row.error += '; ORDER_PRICE_NOT_FOUND';
            return cb(row.error);
        }

        if (row.amount != row.order_price) {
            row.error += `; AMOUNT_MISMATCH: CSV=${row.amount} - ORDER=${row.order_price}`;
            return cb(row.error);
        }

        row.order_created_at = _.get(body, 'data[0].created_at', null);
        if (!row.order_created_at) {
            row.error += '; ORDER_CREATED_AT_NOT_FOUND';
            return cb(row.error);
        }
        row.order_created_at = moment(row.order_created_at).format('Do MMM YYYY');

        return cb();
    });
}

function getCatalogData(row, options, cb) {
    request({
        "uri": `https://catalogadmin.paytm.com/v2/admin/catalog.json?&id=${row.product_id}`,
        "method": "GET",
        "headers": {
            "cookie": `ff.sid=${options.FF_SID}`
        }
    }, function (err, res, body) {
        if (err) {
            row.error += '; CATALOG_ERR: ' + err;
            return cb(row.error);
        }
        if (body && typeof body === 'string') {
            try {
                body = JSON.parse(body);
            } catch (error) {
            }
        }
        if (!body) {
            body = {};
        }

        row.brand = _.get(body, 'products[0].brand', null);
        if (!row.brand) {
            row.error += '; BRAND_NOT_FOUND';
            return cb(row.error);
        }

        return cb();
    });
}

function getAllData(row, options, cb) {
    async.auto({
        getCustMobile: function (cb) {
            getCustMobile(row, options, cb)
        },
        getOrderData: function (cb) {
            getOrderData(row, options, cb);
        },
        getCatalogData: ['getOrderData', function (results, cb) {
            getCatalogData(row, options, cb);
        }]
    }, cb);
}


function getPushOpts(customerId, templateName, deeplink, row) {
    return {
        "uri": _.get(CONFIG, ['NOTIFICATION', 'notificationapi', 'PUSHAPIURL']),
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "client_id": _.get(CONFIG, ['NOTIFICATION', 'notificationapi', 'V3_API_CLIENT_ID']),
            "secret_key": _.get(CONFIG, ['NOTIFICATION', 'notificationapi', 'V3_API_SECRET_KEY'])
        },
        json: {
            "templateName": templateName,
            "debug": false,
            "sendBroadcastPush": true,
            "notificationReceiver": {
                "notificationReceiverType": "CUSTOMERID",
                "notificationReceiverIdentifier": [customerId]
            },
            "extraCommonParams": {
                "url": deeplink,
                "url_type": "external"
            },
            "deviceType": ["ANDROIDAPP", "IOSAPP"],
            "dynamicParams": {
                bank_name: row.brand,
                mcn: row.mcn,
                payment_date: row.order_created_at,
                amount: row.order_price,
            }
        }
    };
}

function getSMSOpts(phone, templateName, sms_url, row) {
    return {
        "uri": _.get(CONFIG, ['NOTIFICATION', 'notificationapi', 'SMSAPIURL']),
        "method": "POST",
        "headers": {
            "Content-Type": "application/json",
            "client_id": _.get(CONFIG, ['NOTIFICATION', 'notificationapi', 'V3_API_CLIENT_ID']),
            "secret_key": _.get(CONFIG, ['NOTIFICATION', 'notificationapi', 'V3_API_SECRET_KEY'])
        },
        json: {
            "templateName": templateName,
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [phone]
            },
            "dynamicParams": {
                bank_name: row.brand,
                mcn: row.mcn,
                payment_date: row.order_created_at,
                amount: row.order_price,
                cst_url: sms_url,
            }
        }
    }
}

function processRecord(row, options, cb) {
    getAllData(row, options, function (err) {
        if (err) {
            row.error += '; DATA_FETCH_ERROR: ' + err;
            return cb();
        }
        async.parallel([
            function (cb) {
                if (!row.customer_id) {
                    row.error += '; NOT_SENDING_PUSH: customer_id not found';
                    return cb();
                }
                let apiOpts = getPushOpts(row.customer_id, options.TEMPLATE_PUSH, options.PUSH_DEEPLINK, row);
                request(apiOpts, function (err, res, body) {
                    if (err) {
                        row.error += '; PUSH_JOB_ERR: ' + err;
                        return cb();
                    }
                    if (!body.jobId) {
                        row.error += '; PUSH_JOB_NULL: ' + body.message;
                        return cb();
                    }
                    row.push_job_id = body.jobId;
                    return cb();
                });
            },
            function (cb) {
                if (!row.customer_phone) {
                    row.error += '; NOT_SENDING_SMS: customer_phone not found';
                    return cb();
                }
                let apiOpts = getSMSOpts(row.customer_phone, options.TEMPLATE_SMS, options.SMS_URL, row);
                request(apiOpts, function (err, res, body) {
                    if (err) {
                        row.error += '; SMS_JOB_ERR: ' + err;
                        return cb();
                    }
                    if (!body.jobId) {
                        row.error += '; SMS_JOB_NULL: ' + body.message;
                        return cb();
                    }
                    row.sms_job_id = body.jobId;
                    return cb();
                });
            }
        ], function () {
            return cb();
        });
    });
}

function main(options, cb) {
    getAuthCode(options, function (err) {
        if (err) {
            return cb(err);
        }
        getAuthToken(options, function (err) {
            if (err) {
                return cb(err);
            }
            fs.createReadStream(options.FILE_NAME)
                .pipe(parse({ltrim: true, rtrim: true, headers: true, ignoreEmpty: true}))
                .on('data', row => {
                    arr.push(row);
                })
                .on('end', rowCount => {
                    let cnt = 0;
                    async.eachLimit(arr, options.TPS, function (row, cb) {
                        row.error = '';
                        if (options.TEST_CUSTOMER_ID) {
                            // This is to test the complete flow without sending notification to actual customer
                            row.customer_id = options.TEST_CUSTOMER_ID;
                        }
                        processRecord(row, options, function () {
                            row.cnt = ++cnt;
                            if (!row.error) {
                                delete row.error;
                            }
                            console.log(JSON.stringify(row));
                            setTimeout(function () {
                                return cb();
                            }, 800);
                        });
                    }, function () {
                        return cb();
                    });
                })
                .on('error', error => {
                    return cb(new Error('ERROR CSV READER: ' + error));
                });
        });
    });
}

(function () {
    if (require.main === module) {
        main({
            TEST_CUSTOMER_ID: '23716219',
            FILE_NAME: '/tmp/cc_notify.csv',
            TEMPLATE_PUSH: 'CC refund one time - Push',
            TEMPLATE_SMS: 'CC Refund one time - SMS',
            PUSH_DEEPLINK: 'paytmmp://contactus',
            SMS_URL: 'http://m.paytm.me/care',
            FF_SID: 's%3AIkWbpUbqeNSiYa87qMk7AujO_G4y3Wo-.CcTXZuEo%2F2fH6lgQ36z3Q4a26hc1khiraHhHhYncYoY',
            authConfig: rechargeConfig.auth,
            authorizationKey: "Basic " + new Buffer.from(rechargeConfig.auth.clientKey + ":" + rechargeConfig.auth.clientSecretAuth,'utf-8').toString('base64'),
            TPS: 50,
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.error('FAILURE');
                    process.exit(1);
                }
                console.error('SUCCESS');
                process.exit(0);
            }, 1000);
        })
    }
})();
