const _ = require("lodash");
const path = require("path");
const FS = require("fs");
const cassandra = require('cassandra-driver');
// const json = require('big-json');
const json = require('jsonstream')


let addedRecords = 0
const nonPaytmBillsTable = 'bills_non_paytm';
const nonPaytmRecentsTable = 'bills_recent_records';

//prod 
// let cassandraConfig =  {
//         contactPoints : ['***********', '***********', '***********'],
//         localDataCenter : 'datacenter1',
//         keyspace: 'reminder',
//     }

    //dev
let cassandraConfig =  {
    contactPoints : ['127.0.0.1'],
    localDataCenter : 'datacenter1',
    keyspace: 'test01'
}
const client = new cassandra.Client(cassandraConfig)

/**
const parseJson = () => {
    FS.createReadStream(path.resolve(__dirname, '', 'data.json'))
    .pipe(json.parse('*'))
    .on('data', async row => {
        await addDummyData(row)
    })
    .on('end', (result) =>{
        console.log(result)
    })
}


const addDummyData = async (params) => {
    console.log(params)
    try{
        const billsQuery = `INSERT INTO ${nonPaytmBillsTable} (recharge_number, customer_id, operator , service, product_id, bill_date, paytype,
        amount, status, notification_status, due_date, card_network, customer_other_info, bill_fetch_date  )                
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        const dueDateQuery = `INSERT INTO ${nonPaytmBillsTable} (recharge_number, customer_id, operator , service, product_id, bill_date, paytype,
        amount, status, notification_status, due_date, card_network, customer_other_info, bill_fetch_date  )                
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    
        const  recentsQuery = `INSERT into ${nonPaytmRecentsTable} (customer_id, service, paytype, operator, recharge_number ) 
        VALUES (?,?,?,?,?)`;

        const queryParams = [
            params.recharge_number,
            params.customer_id,
            params.operator,
            _.toLower(params.service),
            params.product_id,
            params.bill_date,
            params.paytype,
            params.amount,
            params.status,
            params.notification_status,
            params.due_date,
            _.get(params, 'cardNetwork', null),
            params.customer_other_info,
            params.bill_fetch_date
        ];

        let recentsQueryParams = [
            params.customer_id,
            _.toLower(params.service),
            params.paytype,
            params.operator,
            [params.recharge_number],
        ];

        const batchQuery = [
            {
                query: billsQuery,
                params: queryParams
            },
            {
                query: recentsQuery,
                params: recentsQueryParams
            },
            {
                query: dueDateQuery,
                params: queryParams
            }
        ]

        
        client.batch(batchQuery, { prepare: true }, function (err) {
            // All queries have been executed successfully
            // Or none of the changes have been applied, check err
            addedRecords += 1;
            console.log(addedRecords)
            console.log(err)
         });
        
    } catch (err){
        console.log(err)
    }
}

/**
 * [
  '{{repeat(700000)}}',
  {
    recharge_number: 'XXXX XXXX XXXX {{integer(1000,9999)}}',
    customer_id: '{{integer(********, ********)}}',
    service: 'financial services',
    operator: 'dummyNetwork_{{random("hdfcbank", "icicibank")}}',
    amount: '{{integer(1000, 100000)}}',
    bill_date: '{{date(new Date(2022,04,01), new Date(2022,04,30), "YYYY-MM-ddThh:mm:ss")}}',
    bill_fetch_date: '{{date(new Date(2022,04,01), new Date(2022,04,30), "YYYY-MM-ddThh:mm:ss")}}',
    due_date: '{{date(new Date(2022,04,01), new Date(2022,04,30), "YYYY-MM-ddThh:mm:ss")}}',
    card_network: 'dummyNetwork',
    notification_status: "{{random(1,0)}}",
    paytype: "credit card",
    product_id : *********,
    status: 4,
    customer_other_info:'{billDueDate:{{date(new Date(2022,06,01), new Date(2022,06,30), "YYYY-MM-ddThh:mm:ss")}}, currentMinBillAmount:{{integer(100, 500)}} }'
  }
]
 */



const start = async () => {
    await parseJson();
}

start();
