import L from 'lgr'
import _ from 'lodash'
import MOMENT from 'moment';

import ASYNC from 'async'
import startup from '../lib/startup'
import utility from '../lib'
import FS from 'fs';
let recordNumber = 0;
import { parse } from 'fast-csv';




class update_creditcard {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.startLine = 0;
        this.CSV_PARSE_BATCH = 40;
        this.tableName = 'bills_creditcard';
        this.csvRecords = [];
        this.path = "/tmp/axis_data_1.csv";
    }

    start(cb, opts) {
        let self = this;

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }
        ASYNC.waterfall([
            next => {
                return self.processCSV(next, self.path);
            }
        ], function (error) {
            if (error) {
                self.L.critical('VILCSVLoader', 'Error - ', error);
            }
            return cb(error);
        });
    }
    processCSV(done, filePath) {
        let self = this;

        if (!FS.existsSync(filePath)) {
            return done(`Invalid path: ${filePath}`);
        }


        let stream = FS.createReadStream(filePath)
            .pipe(parse({ ltrim: true, rtrim: true, headers: true, ignoreEmpty: true }))
            .on('data', data => {
                if (recordNumber >= self.startLine) {
                    // self.L.log("Csv data", data);
                    if (self.csvRecords.length < self.CSV_PARSE_BATCH) {
                        recordNumber++;
                        self.csvRecords.push(data);
                    } else {
                        recordNumber++;
                        self.csvRecords.push(data);
                        stream.pause();
                        self.processRecords(() => {
                            self.L.info('wait started', recordNumber, "record processed", self.CSV_PARSE_BATCH);
                            setTimeout(() => {
                                self.L.info('wait end record processed', recordNumber, "staring next");
                                stream.resume();
                            }, 100);
                        });
                    }
                }
            })
            .on('end', rowCount => {
                self.processRecords(() => {
                    self.L.log(`${rowCount} records processed succesfully !!`);
                    return done();
                });
            })
            .on('error', error => {
                self.L.critical("processCSV", "Error", error);
                done(error);
            });
    }
    processRecords(done) {
        let self = this;

        ASYNC.each(self.csvRecords, ASYNC.ensureAsync(self._processRecord.bind(self)), function (err) {
            if (err) {
                self.L.error("Error processing record:", err);
            }
            self.csvRecords = [];
            return done();
        });
    }

    _processRecord(record, done) {
        let self = this;
        self.queryDB(record, (err, dbRecord) => {
            if (!err && dbRecord && dbRecord.length > 0) {
                for (let i = 0; i < dbRecord.length; i++) {
                    self.updateRecord((err) => {
                        if (err) {
                            return done(err);
                        }
                        return done();
                    }, self.tableName, dbRecord[i]);
                }

            } else if (!err && dbRecord && dbRecord.length == 0) {
                self.L.log("_processRecord: No Records");
                done();
            } else {
                self.L.log("_processRecord: Error in query", err);
                done();
            }
        });

    }

    updateRecord(cb, tableName, params) {
        let
            self = this;

       // if (MOMENT(params.updated_at).isBefore(MOMENT('2024-04-20 00:00:00'))) {

            if (params.next_bill_fetch_date && (MOMENT(params.next_bill_fetch_date).diff(MOMENT(), 'days') > 3)) {

                let nbfd = MOMENT().format('YYYY-MM-DD');
                const query = `UPDATE ${tableName} \
                    SET next_bill_fetch_date = ? where id = ?`;

                self.L.log("Updating nbfd for for ID:", params.id, query);
                var latencyStart = new Date().getTime();

                self.dbInstance.exec((error, res) => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateDBByScript' });
                    if (error) {
                        self.L.critical('writeCustomerDetails::DB update error for ID:', params.id, error);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY_ERROR', 'STATUS:RECORDS_NOT_UPDATE_DB_ERROR', 'TYPE:updateDBByScript']);
                        return cb('DB_UPDATE_ERROR');
                    } else {
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:RECORDS_UPDATE', 'TYPE:updateDBByScript', 'OPERATOR:' + params.dbRow.operator]);
                        self.L.log("successful updated old data", params.id, "original next_bill_fetch_date", params.next_bill_fetch_date);
                        return cb(null, res);
                    }
                }, 'DIGITAL_REMINDER_MASTER', query, [nbfd, params.id]);
            } else {
                self.L.log("NO update: nbfd is outside 3 days ", params.next_bill_fetch_date, params.dbRow.operator, params.dbRow.customer_id, params.dbRow.recharge_number);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:RECORDS_NOT_UPDATE_WITHIN_5', 'TYPE:updateDBByScript', 'OPERATOR:' + params.dbRow.operator]);
                return cb();
            }
        // } else {
        //     utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:RECORDS_NOT_UPDATE_GRATER_19', 'TYPE:updateDBByScript']);
        //     self.L.log("NO update: updated at greater than 19 ", params.next_bill_fetch_date);
        //     return cb();
        // }
    }

    queryDB(record, cb) {
        let self = this;
        let customerId = _.get(record, 'customer_id', ''),
            rechargeNumber = _.get(record, 'recharge_number', ''),
            pid = _.get(record, 'pid', ''),
            dbRecord = [],
            gateway = 'euronetpostpaid';
            
        const updatedAtStart = MOMENT('2024-04-10').format('YYYY-MM-DD HH:mm:ss');
        const updatedAtEnd = MOMENT().format('YYYY-MM-DD HH:mm:ss');

        self.L.log("Quering DB for", customerId, rechargeNumber, pid, gateway, updatedAtStart, updatedAtEnd);
        try {
            const query = 'SELECT id, customer_id, recharge_number, operator, gateway, product_id, updated_at, next_bill_fetch_date, extra from bills_creditcard where customer_id =? AND recharge_number=?  AND updated_at >= ?';
            var latencyStart = new Date().getTime();

            self.dbInstance.exec((error, res) => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE': 'updateDBByScript' });
                self.L.log("Recived DB response for", customerId, rechargeNumber, pid, gateway, updatedAtStart, updatedAtEnd, error, (res && res.length));

                if (error) {
                    self.L.critical('writeCustomerDetails DB read error::', customerId, rechargeNumber, pid, gateway, updatedAtStart, updatedAtEnd, query, error);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:DB_QUERY_ERROR', 'STATUS:RECORDS_NOT_READ_DB_ERROR', 'TYPE:updateDBByScript']);
                    return cb(error);
                } else {
                    if (res != null && res.length > 0) {
                        for (let i = 0; i < res.length; i++) {
                            if(res[i].gateway == gateway) {
                                self.L.log("gateway name matched", res[i].id);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:gateway_euronetpostpaid', 'TYPE:updateDBByScript', 'OPERATOR:' + res[i].operator]);
                                continue;
                            }

                            let extraDetails = _.get(res[i], 'extra', '{}');
                            try {
                                extraDetails = JSON.parse(extraDetails);
                            } catch(err) {
                                self.L.error('unable to parse extra', customerId, rechargeNumber, pid, gateway, updatedAtStart, updatedAtEnd);
                                extraDetails = {};
                            }

                            if(_.get(extraDetails, 'BBPSBillFetch', true) == false) {
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:BBPSBillFetch_true', 'TYPE:updateDBByScript', 'OPERATOR:' + res[i].operator]);
                                continue;
                            }

                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:RECORDS_READ', 'TYPE:updateDBByScript', 'OPERATOR:' + res[i].operator]);
                            self.L.log("DB row selected");
                            dbRecord.push({
                                'id': res[i].id,
                                'updated_at': res[i].updated_at,
                                'next_bill_fetch_date': res[i].next_bill_fetch_date,
                                "dbRow": res[i]
                            });
                        }
                    } else {
                        self.L.log("No record found for", customerId, rechargeNumber, pid, gateway, updatedAtStart, updatedAtEnd);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREDITCARD_UPDATE', 'STATUS:NO_DB_RECORD_FOUND', 'TYPE:updateDBByScript']);
                    }
                    return cb(null, dbRecord);

                }
            }, 'DIGITAL_REMINDER_SLAVE', query, [customerId, rechargeNumber, updatedAtStart]);
        }
        catch (error) {
            self.L.critical("unknow error while doing DB queiry", customerId, rechargeNumber, pid, gateway, updatedAtStart, updatedAtEnd);
            return cb(error);
        }

    }
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-r, --path <value>', 'path', String)
            .option('-s, --startingID <value>', 'startingID', Number)
            .parse(process.argv);

        startup.init({
        }, function (err, options) {
            if (err) {
                L.critical(' failed to load', err);
                process.exit(1);
            }
            try {
                let script = new update_creditcard({ ...options, startingID: commander.startingID });
                script.start(function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log(err);
                            console.log('FAILURE');
                            console.timeEnd('Execution Time');
                            process.exit(1);
                        }
                        console.log('SUCCESS');
                        console.timeEnd('Execution Time');
                        process.exit(0);
                    }, 5000);
                }, options);
            } catch (err) {
                console.log(err)
            }
        });
    }
})();


