import L from 'lgr'
import startup from '../lib/startup'
import async from 'async'

let duplicateEntriesTable = 'bills_gas'; // bills_gas
let duplicateDeleteTable = 'duplicate_delete';
function execute(opts, cb) {
    L.setLevel('verbose');

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }
        let batchSize = 1; // <null/1/500>
        runForAllRecord(opts, duplicateEntriesTable, duplicateDeleteTable, batchSize, options.dbInstance, cb);
    });
}
function execDBQuery(query, dbInstance, operationType , cb) {
    L.verbose(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, MASER_SLAVE_TYPE, query, []);
}
function processRecord(duplicate_delete_record, dbInstance, cb) {
    
    let deleted_ids;

    async.waterfall([
            next => {
                let read_query = `SELECT id FROM ${duplicateEntriesTable} WHERE id IN (${duplicate_delete_record.bills_gas_id}) ORDER BY updated_at DESC`;
                return execDBQuery(read_query, dbInstance, "READ", next);
            },
            (bills_gas_records , next) => {
                deleted_ids = [];
                if (bills_gas_records.length == 0 || bills_gas_records.length == 1) {
                 return next(`No records found for ids ${duplicate_delete_record.bills_gas_id} in bills_gas`);
                }
                deleted_ids = [... bills_gas_records.map((record)=>{return record.id;})];
                deleted_ids.shift(); 
                let update_query = `UPDATE duplicate_delete SET deleted_id = '${deleted_ids.join(',')}' , status = 1 WHERE  id = ${duplicate_delete_record.id}`;
                return execDBQuery(update_query, dbInstance, "WRITE", next);
            },
            (data, next)=> {
                let delete_query = `DELETE FROM ${duplicateEntriesTable} WHERE id IN (${deleted_ids.join(',')})`;
                return execDBQuery(delete_query, dbInstance, "WRITE", next);
            }
        ],  (err) => {
                if(err) {
                    let errMsg = 'Error in processRecord :: ' + err;
                    L.error(errMsg);
                    return cb(errMsg);
                } 
                    cb();
                
                
        });
}
function runForAllRecord(opts, duplicateEntriesTable, duplicateDeleteTable, batchSize, dbInstance, cb) {
    let query = `SELECT * FROM duplicate_delete where status IS NULL OR status <> 1`
    if(batchSize) {
        query += ` LIMIT ${batchSize}`;
    }
    query += ';';
    
    execDBQuery(query, dbInstance, "READ", (err, records)=>{
        if (err) {
            console.log(err);
            console.log('execDBQuery :: FAILURE');
            L.verbose(`execDBQuery :: FAILURE`);
            return cb(err);
        }
        if(!records.length) {
            L.verbose(`No more records left for processing `);
            return cb();
        }
        L.verbose(`Total records: ${records.length}`);
        async.waterfall([

                next => {
                    async.eachSeries(records, function (currentRecord, cb) {
                        L.verbose('processing each record : ' + JSON.stringify(currentRecord));
                        processRecord(currentRecord, dbInstance, function (err) {
                            if (err) {
                                return cb(err);
                            }
                            return cb();
                        });
        
                    },next);
                }

            ],
            (error)=> {
                if(error) {
                    let errorMsg = 'runForAllRecord Error' + error;
                    L.error(errorMsg);
                    return cb(errorMsg);
                } else {
                    runForAllRecord(opts, duplicateEntriesTable, duplicateDeleteTable, batchSize, dbInstance, cb);
                }
            });
    })
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();