import L from 'lgr'
import _ from 'lodash'
import startup from '../lib/startup'
import ASYNC from 'async'
import payloads from './ct_ingest_ccbp_configure'

class ct_ingest {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
    }

   async runForRecords(record) {
      let self = this;
      return new Promise(async (resolve, reject) => {

        ASYNC.waterfall([
            next => {
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                self.ctKafkaPublisher.initProducer('high', function (error) {
                    if (error){
                        console.error('error in initialising ctKafkaPublisher Producer :: ', error);
                        return next(error);
                    }
                    console.log("ctKafkaPublisher KAFKA PRODUCER STARTED....");
                    return next(null);
                });
            },
            next => {
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(record)
                    }],
                      (error) => {
                        if (error) {
                             console.error('publishInKafka :: publishCtEvents', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' +JSON.stringify(record), error);
                             return next(error);
                        } else {
                            console.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(record));
                            return next(null);
                        }
                    })
            }
    ],(error) => {
        if(error){
            console.log("Error while publishing", error)
            reject(error);
        } 
        else resolve();
    })
    })
        
}


 async execute() {
    L.setLevel('verbose');

    console.log(`Processing ${payloads.length}...`)
    let i = 0;
    return new Promise(async (resolve, reject) => {
        try {
            if (payloads.length == 0) return resolve();
            let funSync = async () => {
                await this.runForRecords(payloads[i])
                .then(()=>{
                    i++;
                    if (i == payloads.length){
                        resolve();
                    } 
                    else funSync();
                })
                .catch((error)=>{
                    reject(error);
                })
            }
            funSync();
        } 
        catch (e) {
            reject(e);
        }
    });
  }
}
( async function () {
    if (require.main === module) {
        console.time('Execution Time');

        startup.init({}, function (err, options) {
            if (err) {
                console.log("Couldn't load config ",err);
                process.exit(1);
            }
            else {
                let script = new ct_ingest(options)
                script.execute()
                .then(() => {
                    console.log('SUCCESS');
                    console.timeEnd('Execution Time');
                    process.exit(0);
                })
                .catch(error => {
                    console.log('Error:FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                })
            }
        })
    }
})();

