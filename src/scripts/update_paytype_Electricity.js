import startup from '../lib/startup';
import utility from '../lib'
import async from 'async'
import MOMENT from 'moment';
import _ from "lodash";
import L from 'lgr';
import fs from 'fs'
import csv from 'fast-csv'
import { parseStream } from 'fast-csv';
import OAuth from '../lib/oauth';
const path = require('path');

let operatorMissing = 0,
    customerIdMissing = 0,
    serviceMissing = 0,
    rechargeNumberMissing=0,
    mobileNumberMissing=0,
    productidMissing = 0,
    totalRow = 0,
    paytype_updated_count=0,
    OAuth_Response_null=0;


class update_paytype_electricity {
    constructor(options) {
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.config = options.config;
        this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
    }


     async processRecord(cb, options, data){
        let self=this;
        let params={
            'mobileNumber':_.get(data, 'mobileNumber', null),
            'service': _.get(data, 'service', null),
            'operator': _.get(data, 'operator', null),
            'productid': _.get(data, 'product_id', null),
            'rechargeNumber': _.get(data, 'rechargeNumber', null)
        }

        if(!params.mobileNumber){
            mobileNumberMissing++;
           // utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:PARAMS MISSING', 'TYPE:MOBILE_NUMBER_NOT_FOUND_FROM_CSV']);
            return cb('Got mobileNumber as null from CSV File');
        }
        if(!params.rechargeNumber){
            rechargeNumberMissing++;
            //utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:PARAMS MISSING', 'TYPE:RECHARGE_NUMBER_NOT_FOUND_FROM_CSV']);
            return cb('Got mobileNumber as null from CSV File');
        }
        if(!params.operator){
            operatorMissing++;
            //utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:PARAMS MISSING', 'TYPE:OPERATOR_NOT_FOUND_FROM_CSV']);
            return cb('Got operator as null from CSV File');
        }
        if(!params.service){
            serviceMissing++;
            //utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:PARAMS MISSING', 'TYPE:SERVICE_NOT_FOUND_FROM_CSV']);
            return cb('Got service as null from CSV File');
        } 
        if(!params.productid){
            productidMissing++;
           // utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:PARAMS MISSING', 'TYPE:PRODUCT_ID_NOT_FOUND_FROM_CSV']);
            return cb('Got productid as null from CSV File');
        } 
        let record =  await self.getCustDetails(params);
        if(!record){
        return cb('Got customer id null from auth');}
         //let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);  
         let tableName = 'bills_airtel';
        try {
            self.updatePaytypeInElectricityTable(tableName,params,record,(error)=>{
                    if (error) {
                        //utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:RECORDS_NOT_INSERTED', 'TYPE:RECORDS_NOT_INSERTED_INTO_TABLE']);
                        L.error('error',error);
                    } 
                    else{
                        //utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:RECORDS_INSERTED', 'TYPE:RECORDS_INSERTED_INTO_TABLE']);
                        self.L.log(`updatePaytypeInElectricityTable :: Updated Paytype into ${tableName} for _mobileNumber:${params.mobileNumber},Processed records so far:${++paytype_updated_count}`);

                    }
                       return cb(error);
                    }); // If insert DB query gets failed then will we have to proceed further    
        } catch (err) {
            self.L.error(`updatePaytypeInElectricityTable :: Error while inserting record into ${tableName} for mobileNumber:${params.mobileNumber}`,err);
            return cb(err);
        }   
        }

    async getCustDetails(record){
          let self=this;
        try {
            let mobileNumber = _.get(record, 'mobileNumber', '') ,
             rechargeNumber = _.get(record, 'rechargeNumber', '')    // optional

                let queryString = `?fetch_strategy=USERID,BASIC&phone=${mobileNumber}`;

                self.L.log(`getCustDetails:: from OAuth.fetchCustomerDetail API for _mobileNumber:${queryString}`);
                //let response =  await self.OAuth.fetchCustomerDetail(queryString); 
                //let response={"basicInfo":{"countryCode":"91","displayName":"Abhas","email":"<EMAIL>","firstName":"ABHAS BHATNAGAR","lastName":"","phone":"7503251652"},"userId":105439218};
                let response={"basicInfo":{"countryCode":"91","displayName":"TV SRIDHAR","email":"<EMAIL>","firstName":"TV SRIDHAR","lastName":"","phone":"9162448855"},"userId":'105439224'};

                if (_.get(response, ['userId'], '') === '') {
                    OAuth_Response_null++;
                    //utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:CUSTOMER_DETAILS_NOT_FOUND', 'TYPE:CUST_ID_NOT_FOUND_FROM_OATH_API']);
                    self.L.log(`getCustDetails:: customer details not found from OAuth API for _mobileNumber:${mobileNumber}`);
                    return null;
                }
               // utility._sendMetricsToDD(1, ['REQUEST_TYPE:ELECTRICITY_CAAS', 'STATUS:CUSTOMER_DETAILS_FOUND', 'TYPE:CUST_DETAILS_FOUND_FROM_OATH_API']);
                self.L.log(`getCustDetails:: customer details found from OAuth API for _mobileNumber:${mobileNumber}_customerId:${_.get(response, ['userId'], '')}`);

                let customerDetailsObj = {
                    "customerId" : _.get(response, ['userId'], '')
                }

                return customerDetailsObj;
        }
        catch (error) {
            //self.L.error(`Electricity::getCustDetails Error for record :${JSON.stringify(record)}, ${error}`);
            return null;
        }

    }

     updatePaytypeInElectricityTable(table_name,params,record,cb){
             let self = this;
            const query = `UPDATE ${table_name} \
            SET paytype='postpaid' where customer_id=${record.customerId} AND recharge_number="${params.rechargeNumber}" `;


            self.dbInstance.exec((error, res) => {
                if (error) {
                    self.L.critical('writeCustomerDetails::', query, error);
                }
                return cb(error,res);
            }, 'DIGITAL_REMINDER_MASTER', query);
    }

     getCSV_DataFrom_Local(options, cb){
         let self=this;
        try{
             const Stream = fs.createReadStream('/Users/<USER>/digital-reminder/dist/best1.csv')
             //const Stream = fs.createReadStream('/home/<USER>/best.csv')
             const csvStream = parseStream(Stream,{ headers : true});
             csvStream
            .on('data', (data) => {
            self.L.log(`getCSV_DataFrom_Local :: processing for mobileNumber: ${data.mobileNumber}, recharge Number : '${data.rechargeNumber}', service: '${data.service}'  , operator: '${data.operator}', productid: '${data.product_id}' ,Rows Processed till now: ${totalRow}`);
            self.L.log(`getCSV_DataFrom_Local :: Records executed till now:: totalRowCount : ${totalRow}, mobileNumberMissingCount :${mobileNumberMissing}, customerIdMissingCount : ${customerIdMissing}, serviceMissingCount : ${serviceMissing}, operatorMissingCount : ${operatorMissing}, rechargeNumberMissingCount : ${rechargeNumberMissing}, productIdMissingCount : ${productidMissing}, OAuthNullResponseCount : ${OAuth_Response_null}, InsertedRowsCount : ${paytype_updated_count}, InvalidRowsCount : ${totalRow-paytype_updated_count}`);
            totalRow++;
            csvStream.pause();
                self.processRecord((err)=>{
                    if(err){   
                        self.L.error('getCSV_DataFrom_Local :: Error in processing data', err, JSON.stringify(data));
                    }
                    csvStream.resume();
                }, options, data);
            })
            .on('end', rowCount => {
            setTimeout(()=>{
                self.L.log(`getCSV_DataFrom_Local :: ${rowCount} Data rows processed succesfully !!`);
                return cb();
                  },1000);
            })
            .on('error', error => {
            setTimeout(()=>{
                self.L.error("processCSV data", "Error", error);
                return cb(error);
                },1000);
            });

            }
        catch(err)
            {
                return cb(err);
            }
    }

async start(options, cb){
          let self=this;
  await  self.getCSV_DataFrom_Local(options, function (err) {
        if (err) {
            self.L.error('start::Error',err);
        }else{
            self.L.log(`start::completed, totalRowCount : ${totalRow}, mobileNumberMissingCount :${mobileNumberMissing}, customerIdMissingCount : ${customerIdMissing}, serviceMissingCount : ${serviceMissing}, operatorMissingCount : ${operatorMissing}, rechargeNumberMissingCount : ${rechargeNumberMissing}, productIdMissingCount : ${productidMissing}, OAuthNullResponseCount : ${OAuth_Response_null}, InsertedRowsCount : ${paytype_updated_count}, InvalidRowsCount : ${totalRow-paytype_updated_count}`);
        }
        return cb(err);
    });
}
}
(function main() {
    if (require.main === module) {

    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        let script = new update_paytype_electricity(options);
        script.start(options,
         function (err) {
            setTimeout(function () {
            if (err) {
                console.log("main::Error"+err);
                process.exit(1);
                } else {
                console.log("main::completed");
                process.exit(0);
                }
            }, 1000); 
        })
    });
}
})();
