import startup from '../lib/startup';
import MOMENT from 'moment';
import _, { reject } from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';
import { each, eachLimit, eachSeries, parallel } from 'async';
import AWSCsvIngester from '../lib/awscsvingester';
import utility from '../lib'
let serviceName = "DISABLE_CT_PN_FROM_CSV_BILLS" 
let progressFilePath = `/var/log/digital-reminder/progress-disable-ct-pn-from-csv-${MOMENT().format('YYYY-MM')}.json`
let progressTracker = {}
/** Maintain offset and processed files in a path */
class DisableCTPNFromCSV {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.rechargeConfig = options.rechargeConfig;
        this.config = options.config;
        this.bucketName = "digital-reminder";
        progressTracker = this.getProgressObject()
        //options.isRecordProcessingCapped = _.get(options.config , ['DYNAMIC_CONFIG','DISABLE_CT_PN_FROM_CSV_BILLS_INGEST','implementDailyCapping','value'],false);
        //options.cappingNumberForCurrentDay = _.get(options.config , ['DYNAMIC_CONFIG','DISABLE_CT_PN_FROM_CSV_BILLS_INGEST','dailyCappingNumber','value'],10000000);
        this.csvIngester = new AWSCsvIngester(options , this.updateProgress)
        this.logPrefix = serviceName
        this.batchSize = _.get(options.config,['DYNAMIC_CONFIG',serviceName,'batch_size','value'],5)
        this.serviceMissing = 0
        this.operatorMissing = 0;
        this.productidMissing = 0
        this.rechargeNumberMissing = 0
        this.customerIdMissing = 0;
        this.inserted_row_count = 0;
        this.operatorMapping = {};
        this.currentFile=""
        this.folderPath = `${_.get(options.config , ['DYNAMIC_CONFIG','DISABLE_CT_PN_FROM_CSV_BILLS_INGEST','path','value'],'digital-reminder/DISABLE_CT_PN_FROM_CSV_BILLS_INGEST')}` 
        
        this.files = []
    }
    getProgressObject(){
        let progress = {}
        progressFilePath = `/var/log/digital-reminder/progress-disable-ct-pn-from-csv-${MOMENT().format('YYYY-MM')}.json`
        this.L.info("Loading progress object from",progressFilePath)
        if(fs.existsSync(progressFilePath)){
            const progressData = fs.readFileSync(progressFilePath , 'utf-8')
            progress = JSON.parse(progressData)
        }
        this.L.info("Loaded",progress)
        return progress
    }   
    updateProgress(filename ,count ){
        if(_.get(progressTracker , [filename] , 0 ) == -1)return;
        _.set(progressTracker , [filename] , count )
        this.L.info("Updated progess Object",JSON.stringify(progressTracker),count)
        fs.writeFileSync(progressFilePath , JSON.stringify(progressTracker , null  , 2))
    }
    filterFileByMonth(filename){
        try{
           let date = filename.split('$')[1].split('.')[0].slice(0,7)
           if(date == MOMENT().format('YYYY-MM'))return true
        }catch(err){
            return false
        }
        return false
    }
    start(callback){
        let self = this;
        progressTracker = this.getProgressObject()
        try{
            this.csvIngester.configure(this.bucketName, this.logPrefix,this.batchSize)
        }catch(error){
            self.L.critical(this.logPrefix , "Cannot initialize AWS")
            return callback(error)
        }
        self.L.info("Getting Files in the folder")
       
        // let data =['digital-reminder/Airtel_API_Recharge_Number.csv']
        // eachSeries(data , self.processEachFile.bind(self) , callback)
        this.csvIngester.getFileNames(this.folderPath,function(err , data){
            if(err){
                self.L.error("Error while getting files")
                return callback(err)
            }else{
                //data = _.filter(data , self.filterFileByMonth)
                return eachSeries(data , self.processEachFile.bind(self) , callback)
            }
        })
      
    }
    processEachFile(filename , callback){
        if(_.get(progressTracker , filename, null) == -1){
            /** File has already been processed we can skip*/
            this.L.info("Skipping file ",filename , "as it has been already processed")
            return callback()
        }
        this.L.info("Processing file :- ",filename)
        this.currentFile = filename
        let skipRows = _.get(progressTracker , [filename], 0) 
        this.csvIngester.start(this.processRecordinBatch.bind(this),filename , function(error , data){
            return callback()
        },skipRows)
    }
    async processRecordinBatch(data){
        let self = this;
        return new Promise((resolve , reject)=>{
            eachLimit(data ,self.batchSize, function(record , cb){
                self.processRecord(function(){
                    cb()
                } ,record)
            },function(error){
                if(error){
                    self.L.error(self.logPrefix , "Error while processing batch",error)
                    return reject(error)
                }
                return resolve()
            })
        })
    }
    
    processRecord(cb, data) {
        let self=this;
        
        let customer_id = _.get(data, 'customer_id', null);
        let service = _.get(data, 'service', null);
        let operator = _.get(data, 'operator', null);
        let operation = _.get(data, 'operation', "U");

        self.L.log(`processRecord :: processing row for customerId: ${customer_id}, service: ${service}, operator: ${operator}, operation: ${operation}`);
        
        
        let params={
            'service': service,
            'operator': operator,
            'customer_id': customer_id
        }

        if(!params.operator || _.isEmpty(params.operator)){
            self.L.error(self.currentFile , 'Operator missing',params)
            self.operatorMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:PARAMS_MISSING', 'TYPE:OPERATOR_NOT_FOUND_FROM_CSV']);
            return cb();
        }
        if(!params.service || _.isEmpty(params.service)){
            self.L.error(self.currentFile , 'Service missing',params)
            self.serviceMissing++;
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:PARAMS_MISSING', 'TYPE:SERVICE_NOT_FOUND_FROM_CSV']);
            return cb();
        }

        
        if(!params.customer_id ||  customer_id == '0' || params.customer_id == '0'){           
            self.L.error(self.currentFile , 'Customer ID missing',params)
            self.customerIdMissing++
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:CUSTOMER_ID_NOT_FOUND_FROM_CSV']);
            return cb();
        }

        if(!operation || _.isEmpty(operation)){
            self.L.error(self.currentFile , 'Opereation missing ',params)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:PARAMS_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:OPERATION_NOT_FOUND_FROM_CSV']);
            return cb();
        }

        let tableName = self.getTableName(params)  
        
        if(!tableName){
            self.L.error(self.currentFile , 'table name missing',params)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:TABLE_MISSING', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:TABLE_NOT_FOUND_FROM_CSV']);
            return cb();
            
        } 
        if(operation && operation == "U"){

            self.updateRecords(tableName,params,(error)=>{
                    if (error) {
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:RECORDS_NOT_INSERTED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_NOT_INSERTED_INTO_TABLE']);
                        L.error('error',error);
                    } 
                    else{
                        self.L.info(self.currentFile , 'Record updated')
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:DISABLE_CT_PN_FROM_CSV_BILLS_INGEST', 'STATUS:RECORDS_INSERTED', `SERVICE:${params.service}`,`OPERATOR:${params.operator}`,'TYPE:RECORDS_INSERTED_INTO_TABLE']);
                        self.L.log(`updateRecords :: Updated into ${tableName} for customerId:${params.customer_id},Processed records so far:${++self.inserted_row_count}`);
                    }
                    setTimeout(() => {
                        return cb(error);
                    }, 50);
                    //return cb(error);
            }); 
        }
    }
   
    updateRecords(tableName,params,cb) {
       
       let self = this;
       let status = 7;
       let notification_status = 0;
       const query = `UPDATE ${tableName} \
       set status = ?, notification_status = ? \
       where customer_id = ? and service = ? and operator = ?`; 
       const param = [
            status,
            notification_status,
            params.customer_id,
            params.service,
            params.operator
       ];
       self.dbInstance.exec((error, res) => {
           if (error) {
               self.L.critical('writeCustomerDetails::', query, error);
           }
           return cb(error,res);
       }, 'DIGITAL_REMINDER_MASTER', query, param);
    }

    /** Logic for fetching table name from params */
    getTableName(params){
        let tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);
        return tableName;
    }
}

(function main() {
    if (require.main === module) {
        startup.init({
              
        }, function (err, options) {
            let script;
            try{
            script = new DisableCTPNFromCSV(options);
            script.start(
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                })
            }catch(err){
                options.L.error(err)
                process.exit(1)
            }
          
        });
    }
})();
export default DisableCTPNFromCSV