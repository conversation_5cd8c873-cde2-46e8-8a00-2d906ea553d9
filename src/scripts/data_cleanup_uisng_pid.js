const async = require('async');
import startup from '../lib/startup'


let OPTIONS = null;
let id = 0;


function readDataFromtableAndUpdate(options, cb) {
    console.log("Now using id: ", id)
    let sql = `SELECT id, recharge_number from ${options.tableName} where  id > ${id} ORDER BY id limit ${options.size}`;
    console.log("Read query-------", sql);
    OPTIONS.dbInstance.exec(function (err, data) {
        console.log("data  read in table", JSON.stringify(data))
        if (err || data.length == 0) {
            if(err){
                console.log("error in fetching data : ", err, "for query:  ", sql);
            }
            console.log("data not found: ", JSON.stringify(data));
            return cb(err);
        }
        data.forEach(element => {
            if(options.listOfPids.includes(element.product_id)){
                let updateSqlQuery = `update  ${options.tableName} set amount=0 , status=11 where id= ${element.id}`;
                console.log("updateSqlQuery SQL query-------", updateSqlQuery);
                OPTIONS.dbInstance.exec(function (err) {
                    if (err) {
                        console.log("error in updateSqlQuery records:  ", (updateSqlQuery))
                        return cb(err);
                    }
                    setTimeout(() => {

                        let sqlQuery = `delete from ${options.tableName} where id= ${element.id}`;
                        console.log("Delete SQL query-------", sqlQuery);
                        OPTIONS.dbInstance.exec(function (err) {
                            if (err) {
                                console.log("error in deleting records:  ", (sqlQuery))
                                return cb(err);
                            }
                        }, '    ', sqlQuery, []);

                }, 100)



                }, 'DIGITAL_REMINDER_MASTER', updateSqlQuery, []);


            
         }
        });
        id = data[data.length-1].id //max id
        setTimeout(() => {
            readDataFromtableAndUpdate(options, cb);// recursive call

        }, 10000);
        
    }, 'DIGITAL_REMINDER_SLAVE', sql, []);

    
}


function executeFlow(options, cb) {
    readDataFromtableAndUpdate(options, function (err) {
        if (err) {
            console.log("error in main flow", err);
            
        }
        return cb(err);
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            tableName: 'bills_bsnlbbps',
            listOfPids:[129254565],
            size: 500
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 100000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
