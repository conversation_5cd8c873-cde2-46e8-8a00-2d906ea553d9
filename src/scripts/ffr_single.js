import L from 'lgr'
import _ from 'lodash'
import request from 'request'
import startup from '../lib/startup'

function runForAllRecord(opts, options, cb) {
    let apiOpts = {
        "uri"       : _.get(options.config, ['FFR', 'VALIDATION_URL'], null),
        "method"    : "POST",
        "timeout"   : 60000,
        'json' : {
            'cart_items' : [
                {
                    price           : 1234,
                    product_id      : opts.product_id,
                    quantity        : 1,
                    fulfillment_req : {
                        recharge_number: opts.recharge_number
                    }
                }
            ],
            customer_id: opts.customer_id,
            'channel_id' : 'digital-reminder',
        }
    };

    L.verbose(`Hitting FFR Api: ${JSON.stringify(apiOpts, null, 4)}`);
    request(apiOpts, function (err, res, body) {
        if (err) {
            return cb(err);
        }
        L.log('RESPONSE BODY: ', JSON.stringify(body, null, 4));
        return cb();
    });
}

function execute(opts, cb) {
    <PERSON>.setLevel('verbose');

    if ((!opts.product_id || !opts.recharge_number)) {
        return cb(new Error('Invalid recharge_number and product_id'));
    }

    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        runForAllRecord(opts, options, cb);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-p, --product_id <value>', 'product_id', String)
            .option('-r, --recharge_number <value>', 'recharge_number', String)
            .option('-c, --customer_id <value>', 'customer_id', String)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=production node dist/scripts/ffr_single.js --product_id 194 --recharge_number 9971449914
