const async = require('async');
import startup from '../lib/startup'
import  csv from 'fast-csv'
import fs from 'fs'
import path from 'path'

let OPTIONS = null;
let id = 0;
let total = 0;

function readCsv(options, cb){
    let arr = fs.readFileSync(path.resolve(__dirname, '', 'msedcl.csv')).toString().split("\n");
    arr.forEach(pid => {
        let readquery = `SELECT * FROM ${options.tableName} where product_id = ${pid}`;
        let delquery = `DELETE FROM ${options.tableName} where product_id = ${pid}`
        OPTIONS.dbInstance.exec(function (err, data) {
            console.log(data.affectedRows)
            if (err || data.affectedRows == 0) {
                if(err){
                    console.log("error in fetching data : ", err, "for pid:  ", pid);
                }
                console.log("data not found: ", JSON.stringify(data), " for pid: ", pid);
                return cb(err);
            }
            // total += data.affectedRows;
            total += data.length
            console.log("data found", JSON.stringify(data), " for pid: ", pid, "total is ", total)
        }, 'RECHARGE_ANALYTICS', readquery, []);
    });
}


function executeFlow(options, cb) {

    readCsv(options, function (err) {
        if (err) {
            console.log("error in main flow", err);
            
        }
        console.log("ejwbfkebewf", total)

        return cb(err);
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            tableName: 'bills_msedcl',
            size: 500
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                // process.exit(0);
            }, 200000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
