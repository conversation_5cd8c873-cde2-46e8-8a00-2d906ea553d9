import SQLWRAP          from 'sqlwrap'
import _                from 'lodash'
// If required load config from startup.init

let dbInstance  = new SQLWRAP(config.SQLWRAP)

let tables = [
    "bills_adanigas",
    "bills_aircel",
    "bills_airtel",
    "bills_airtel_heuristic",
    "bills_ajmerelectricity",
    "bills_allib",
    "bills_apepdcl",
    "bills_apspdcl",
    "bills_assampower",
    "bills_bangalorewater",
    "bills_bbps_dnh",
    "bills_bbps_jharkhand",
    "bills_bbps_mpdcl",
    "bills_bbps_tneb",
    "bills_bbps_tripura",
    "bills_bbps_upcl",
    "bills_bescom",
    "bills_best",
    "bills_bhagalpurelectricity",
    "bills_bharatpurelectricity",
    "bills_bikaner",
    "bills_broadband",
    "bills_bses",
    "bills_bses_bkup",
    "bills_bsesprepaid",
    "bills_bsnl",
    "bills_bsnlbbps",
    "bills_calcuttaelectric",
    "bills_cescmysore",
    "bills_cesu",
    "bills_coess",
    "bills_cspdcl",
    "bills_dded",
    "bills_delhijalboard",
    "bills_deopn",
    "bills_dishtv",
    "bills_dps",
    "bills_dth",
    "bills_essel",
    "bills_gas",
    "bills_gedep",
    "bills_gescom",
    "bills_gujaratdiscom",
    "bills_gujaratgas",
    "bills_haryanaelectricity",
    "bills_hathway",
    "bills_hescom",
    "bills_hpseb",
    "bills_iciciinsurance",
    "bills_idea",
    "bills_idea_heuristic",
    "bills_igl",
    "bills_indiafirst",
    "bills_indiapower",
    "bills_insurance",
    "bills_ion",
    "bills_ipclprepaid",
    "bills_jaipurelectricity",
    "bills_jodhpurelectricity",
    "bills_jusco",
    "bills_kesco",
    "bills_kotaelectricity",
    "bills_kseb",
    "bills_ksebprepaid",
    "bills_lic",
    "bills_mahanagargas",
    "bills_manappuram",
    "bills_mangaloreelectricity",
    "bills_matrixpostpaid",
    "bills_meptolls",
    "bills_mpelectricity",
    "bills_msedcl",
    "bills_mspdcl",
    "bills_mtnl",
    "bills_mtnld",
    "bills_mtnlpostpaid",
    "bills_mumbaimetro",
    "bills_ndmc",
    "bills_nextra",
    "bills_noidapower",
    "bills_northbiharpower",
    "bills_npclprepaid",
    "bills_operator",
    "bills_orrissadiscoms",
    "bills_pedem",
    "bills_pspcl",
    "bills_reliance",
    "bills_relianceenergy",
    "bills_relianceinsurance",
    "bills_reliancemobile",
    "bills_sikkim",
    "bills_sitienergy",
    "bills_southbiharpower",
    "bills_status",
    "bills_t24",
    "bills_tatadocomo",
    "bills_tataindicom",
    "bills_tatalandline",
    "bills_tataphoton",
    "bills_tatapower",
    "bills_tatapowermumbai",
    "bills_telanganapower",
    "bills_torrentpower",
    "bills_tpajmer",
    "bills_tsnpdcl",
    "bills_ucnb",
    "bills_uppcl",
    "bills_uppclprepaid",
    "bills_utilapartments",
    "bills_voda",
    "bills_vodafone",
    "bills_water",
    "bills_wbsedcl"
];

tables.forEach(function(table){
    
    let query = `show index from ${table} where Key_name = 'next_bill_fetch_date';`;

    dbInstance.exec(function(error, data) {
        if(error) {
            console.error('error in fetching data from db for table ->', table ,error);
        }
        else if(_.get(data,'length',0) <= 0) {
            console.log('Not Having Key - ',table);
        }
        else {
            // tables with key
            console.log('Having key - ',table);
        }
    }, 'DIGITAL_REMINDER_MASTER', query,[]);
});