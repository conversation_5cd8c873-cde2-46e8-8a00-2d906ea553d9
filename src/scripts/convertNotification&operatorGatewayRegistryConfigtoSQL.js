'use strict'

const fs = require('fs');

const operatorGatewayRegistryConfig = require('./../../dist/config/operatorGatewayRegistry');
const notificationConfig = require('./../../dist/config/notification');
const updated_by = 'digital-reminder-dev'

// notification
const exportNotification = (filename) => {
    console.log("coming in to write for exportNotification");
    let writeStream = fs.createWriteStream(filename);


    let templateIdAndUTMMapper = notificationConfig['default']['common']['TEMPLATE_UTM'];
    for(const [key,values] of Object.entries(templateIdAndUTMMapper)){
        values = JSON.stringify(values);
        let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status, updated_by) VALUES('NOTIFICATION' , 'TEMPLATE_UTM' , '${key}', '${values}', 'json', 1, '${updated_by}') ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type) , updated_by=VALUES(updated_by);\r\n`
        writeStream.write(sqlQuery);
    }

    let templateServiceKeyAndTemplateIdMapper = notificationConfig['default']['common']['TEMPLATE_ID_BY_SERVICE'];
    for(const [key,values] of Object.entries(templateServiceKeyAndTemplateIdMapper)){
        let type;
        if(values == null) {
            type = 'string'
        } else if(!isNaN(values)) {
            type = 'number'
        }
        let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status, updated_by) VALUES('NOTIFICATION', 'TEMPLATE_ID_BY_SERVICE' , '${key}', ${values}, '${type}', 1, '${updated_by}') ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type), updated_by=VALUES(updated_by);\r\n`
        writeStream.write(sqlQuery);
    }


    let blackListOperators = notificationConfig['default']['common']['BlackListOperator'];
    for (let itrOperator = 0; itrOperator < blackListOperators.length; itrOperator++) {
        let operator = blackListOperators[itrOperator];
        let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status, updated_by) VALUES('NOTIFICATION', 'BlackListOperator', '${operator}', 1 ,  'number', 1, '${updated_by}') ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type), updated_by=VALUES(updated_by);\r\n`
        writeStream.write(sqlQuery);
    }
}




// Operator gateway registry
const exportOperatorGatewayRegistry = (filename) => {
    console.log("coming in to write for exportOperatorGatewayRegistry");
    let writeStream = fs.createWriteStream(filename);
    let operatorGatewayMapper = operatorGatewayRegistryConfig['default']['common'];
    for(const [key,values] of Object.entries(operatorGatewayMapper)){
        let sqlQuery = `INSERT INTO digital_reminder_config(name, node, key_name, value, type, status, updated_by) VALUES('OPERATOR_GATEWAY_REGISTRY', '${key}', 'GATEWAY_NAME', '${values}', 'string', 1, '${updated_by}') ON DUPLICATE KEY UPDATE value=VALUES(value), type=VALUES(type), updated_by=VALUES(updated_by);\r\n`
        writeStream.write(sqlQuery);
    }
}

exportOperatorGatewayRegistry('operator_gateway_registry_query.sql');
exportNotification('notfication_config_query.sql')
