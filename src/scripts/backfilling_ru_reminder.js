import _, { reject } from 'lodash'
import L from 'lgr'
import async from 'async'

import startup from '../lib/startup'
import digitalUtility from 'digital-in-util'
import utility from '../lib'

let ctKafkaPublisher = null;
let commonLib = null;
let reminderUtils = null;
//Electricity Operator
//var operator_list=['jaipur vidyut vitran nigam ltd. (jvvnl)',
//'ajmer vidyut vitran nigam limited (avvnl)'
// 'apepdcl',
// 'assam power distribution company ltd. (apdcl)',
// 'bangalore electricity supply company ltd.',
// 'bharatpur electricity services limited',
// 'bikaner electricity supply limited',
// 'brihanmumbai electricity supply and transport undertaking (best undertaking)',
// 'bses rajdhani',
// 'bses yamuna',
// 'bsesr',
//'calcutta electric supply corporation (india) limited',
// 'central power distribution corporation of a.p ltd (apcpdcl)',
// 'cesu, odisha',
// 'chamundeshwari electricity supply corporation ltd mysore',
// 'chhattisgarh state power distribution company ltd (cspdcl)',
// 'co-operative electric supply society ltd.(cess ltd),sircilla',
// 'dakshin gujarat vij company limited',
// 'dakshin haryana bijli vitran nigam (dhbvn)',
// 'daman and diu electricity (dded)',
// 'department of power, government of arunachal pradesh',
// 'department of power, nagaland',
// 'dnh power distribution company limited',
// 'electricity department chandigarh',
// 'goa electricity department',
// 'government of puducherry electricity department',
// 'gulbarga electricity supply company limited (gescom)',
// 'himachal pradesh state electricity board ltd (hpsebl)',
// 'hubli electricity supply company ltd. (hescom)',
// 'jaipur vidyut vitran nigam limited (jvvnl)',
// 'jaipur vidyut vitran nigam ltd. (jvvnl)',
// 'jammu and kashmir power development department',
// 'jamshedpur utilities &services company ltd (jusco)',
// 'jharkhand bijli vitran nigam limited (jbvnl)',
//'jodhpur vidyut vitran nigam limited (jdvvnl)'
// 'kanpur electricity supply company',
// 'kerala state electricity board ltd (kseb ltd)',
// 'kota electricity distribution limited(kedl)',
// 'lakshadweep electricity department',
// 'madhya gujarat vij company limited',
// 'madhya pradesh madhya kshetra vidyut vitran company limited (mpcz)-rural',
// 'madhya pradesh madhya kshetra vidyut vitran company limited (mpcz)-urban',
// 'madhya pradesh paschim kshetra vidyut vitaran company ltd. (mppkvvcl)',
// 'mangalore electricity supply company ltd. (mescom)',
// 'meghalaya power dist corp ltd',
// 'mp poorv kshetra vidyut vitaran-jabalpur',
// 'msedcl',
// 'nesco utility',
// 'new delhi municipal council (ndmc) - electricity',
// 'noida power company ltd (npcl)',
// 'north bihar power distribution',
// 'north bihar power distribution co. ltd (nbpdcl)',
// 'northern power distribution company limited: warrangal (tsnpdcl)',
// 'paschim gujarat vij company limited',
// 'power & electricity department - mizoram',
// 'punjab state power corporation limited (pspcl)',
// 'sikkim power',
// 'south bihar power distribution',
// 'south bihar power distribution non-rapdrp',
// 'southern electricity supply company of odisha limited (southco)',
// 'southern power distribution company of a.p ltd (apspdcl)',
// 'tamil nadu electricity board (tneb)',
// 'tata power delhi distribution limited',
// 'tata power-mumbai',
// 'torrent power limited',
// 'tp ajmer distribution limited (tpadl)',
// 'tripura electricity corp ltd',
// 'tsspdcl',
// 'uttar gujarat vij company limited',
// 'uttar haryana bijli vitran nigam(uhbvn)',
// 'uttar pradesh power corporation ltd. (uppcl)',
// 'uttrakhand power corporation limited',
// 'wesco utility',
// 'west bengal state electricity distribution company limited (wbsedcl)'
//];
//Credit Card operator
var operator_list=['*'];

function processRecord(opts,options, dbRecordResp, dbInstance, cb) {
    let self = this;
    const customerId = _.get(dbRecordResp, 'customer_id', '');
    const operator = _.get(dbRecordResp, 'operator', '');
    const rechargeNumber = _.get(dbRecordResp, 'recharge_number', '');
    
    let eventName;
    if(opts.eventname=='billPaymentFull'){
    eventName = _.get(options.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull');
    }
    else if(opts.eventname=='smsParsedBillPaymentDeleted'){
    eventName = _.get(options.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'NON_PAYTM_BILLS_DELETED'], 'smsParsedBillPaymentDeleted');
    if(dbRecordResp.paytype == 'credit card'){
        let attributes = JSON.parse(_.get(options.config, ['CVR_DATA', dbRecordResp.product_id, 'attributes'] , '{}'))
        let bankName = _.toLower(_.get(attributes, ['bank_code'] , ''));
        
         if(!bankName || bankName == ''){
            L.error(`processRecord:: bank code unavailable ${JSON.stringify(dbRecordResp)} `);
            return cb();
        }
        dbRecordResp.operator = bankName;
    }
    }
    else{
        L.log('No Valid Eventname');
        return cb('No Valid Eventname');
    }
    const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;
    

    let productId = _.get(dbRecordResp, 'product_id', '');
    productId = options.activePidLib.getActivePID(productId); // verify 
    
    if (!_.get(dbRecordResp, 'notification_status', 1)) {
        L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
        return cb()
    }

    if( eventName == _.get(options.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull') || !dbRecordResp.status){
        dbRecordResp.status = _.get(options.config, 'COMMON.bills_status.PAYMENT_DONE', 11)
    }
    L.log('processRecord::id', _.get(dbRecordResp, 'id' , "NOT_PRESENT" ) );
    async.waterfall([
            next => {
                commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {
                let mappedData = reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey); // check 
                let clonedData = _.cloneDeep(mappedData);
                // kafka connection 
                ctKafkaPublisher.publishData([{
                    topic: _.get(options.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        L.critical('publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka on topic REMINDER_CT_EVENTS - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ],  (err) => {
                if(err) {
                    let errMsg = `processRecord Exception occured Error Msg:: ${err} for record::${JSON.stringify(dbRecordResp)} debugKey:: dbDebugKey`;
                    L.error(errMsg);
                } else {
                    L.log(`processRecord`,`Record processed having debug key`, dbDebugKey);
                }
                return cb();
        });
}


function execute(opts, cb) {
    L.setLevel('verbose');

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }
        let batchSize = 10; // <null/1/500>
        //let batchSize = 5;
        commonLib = new utility.commonLib(options);
        reminderUtils = new digitalUtility.ReminderUtils();

        configureKafka(function (error) {
            if (error) {
                L.critical('Script :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                L.log('Script :: start', 'Kafka Configured successfully !!');
                runForAllRecord(opts,options, batchSize, -1, options.dbInstance)
                .then(()=>{
                    return cb();
                })
                .catch((error)=>{
                    return cb(error);
                })
            }
        }, options);
    });
}

function configureKafka(done, options) {
    let self = this;
    /**
     * maintain this sequence
     * Initialize publisher
     */
    async.waterfall([
        next => {
            /**
             * Kafka publisher to publish events to CT publisher pipeline 
             */
             
            ctKafkaPublisher = new options.INFRAUTILS.kafka.producer({
                "kafkaHost": options.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
            });
            ctKafkaPublisher.initProducer('high', function (error) {
                return next(error)
            });
        }
    ], function (error) {
        if (error) {
            L.critical('configureKafka', 'Could not initialize Kafka', error);
        }
        return done(error);
    });
}
function delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}

function runForAllRecord(opts,options, batchSize, offset, dbInstance) {
    let i = 0;
    return new Promise(async (resolve, reject) => {
        try {
            if (i == operator_list.length) return resolve();
            let funSync = async () => {
                await runForSingleOperator(opts,options, operator_list[i], batchSize, offset, dbInstance)
                .then(()=>{
                    i++;
                    if (i == operator_list.length){
                        return resolve();
                    } 
                    else funSync();
                })
                .catch((error)=>{
                    reject(error);
                })
            }
            funSync();
        } 
        catch (e) {
            reject(e);
        }
    });
}

async function runForSingleOperator(opts,options, operator, batchSize, offset, dbInstance){
    return new Promise((resolve,reject)=>{
        let query,table_name;
        if(opts.category=='creditcard'){
             query = `SELECT * FROM bills_creditcard WHERE status=11 AND due_date > NOW() AND id > ${offset} ORDER BY id` // check condition
            if(batchSize) {
        query += ` LIMIT ${batchSize}`;
    }
    query += ';';
        }
        else if(opts.category=='electricity'){

            table_name=options.config.OPERATOR_TABLE_REGISTRY[`${operator}`];
       
            query = `SELECT * FROM ${table_name} WHERE amount > 0 AND operator='${operator}' AND due_date > NOW() AND id > ${offset} ORDER BY id` // check condition
            if(batchSize) {
        query += ` LIMIT ${batchSize}`;
    }
    query += ';';
        }
        else{
            L.log('No Valid Category')
            return reject('No Valid Category');
        }
        

    execDBQuery(query, dbInstance, "READ", (err, records)=>{
        if (err) {
            console.log("DB query failed",err);
            console.log('execDBQuery :: FAILURE');
            L.log(`execDBQuery :: FAILURE`);
            return reject(err);
        }
        if(!records.length) {
            L.log(`No more records left for processing `);
           return resolve();
        }
        L.log(`Total records: ${records.length}`);
        async.waterfall([
                next => {
                    async.eachSeries(records, function (currentRecord, cb) {
                        L.log('processing each record : ' + JSON.stringify(currentRecord));
                        processRecord(opts,options, currentRecord, dbInstance, function (err) {
                            if (err) {
                                return cb(err);
                                reject();
                            }
                            return cb();
                            resolve();
                        });
        
                    },next);
                },
                next => {
                    return delay(next, 100);
                }
            ],
            (error)=> {
                if(error) {
                    let errorMsg = 'runForAllRecord Error' + error;
                    L.error(errorMsg);
                    return reject(errorMsg);
                } else {
                    offset = records[records.length-1].id;
                    runForSingleOperator(opts,options, operator, batchSize, offset, dbInstance)
                    .then(()=>{
                        return resolve();
                    })
                    .catch((error)=>{
                        return reject(error);
                    })
                    //return resolve();
                }
            });
    })
    })
}


function execDBQuery(query, dbInstance, operationType , cb) {
    L.log(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, MASER_SLAVE_TYPE, query, []);
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-c, --category <value>', 'category', String)
            .option('-e, --eventname <value>', 'eventname', String)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    //console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();