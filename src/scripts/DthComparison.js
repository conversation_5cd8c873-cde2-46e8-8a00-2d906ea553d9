const async = require('async');
import startup from '../lib/startup'


let OPTIONS = null;
let id = 0;
let diffCounter = 0;
let recordsCompared = 0;

function readDataFromDthTable(options, data, cb) {
    async.eachLimit(data, 50, function (row, localCb) {
        setImmediate(function () {
            let sql = `SELECT recharge_number, customer_id from ${options.dthTableName} WHERE recharge_number = '${row.recharge_number}' and operator = '${row.operator}' and service = 'dth'`;
            recordsCompared++;
            OPTIONS.dbInstance.exec(function (err, dthData) {
                if (err) {
                    return localCb(err);
                }
                if(dthData && dthData.length === 1 && dthData[0].customer_id && dthData[0].customer_id != row.customer_id) {
                    diffCounter++;
                    console.log(`rech num: ${row.recharge_number}, PV_cust :${row.customer_id}, DTH_cust:${dthData[0].customer_id}`)
                }
                return localCb(null, dthData);
            }, 'OPERATOR_SYNC_SLAVE', sql, []);
        });
    }, cb);
}


function readDataFromPV(options, cb) {
    let returnData = []
    console.log("Now using id: ", id)
    let sql = `SELECT id, recharge_number, customer_id, operator from ${options.pvTableName} where operator in ('dishtv', 'd2h (formerly videocon d2h)') and id > ${id} ORDER BY id limit ${options.size}`;
    OPTIONS.dbInstance.exec(function (err, data) {
        //console.log("data in read Pv", JSON.stringify(data))
        if (err || data.length == 0) {
            console.log("number of mismatched record: ", diffCounter);
            console.log("total records compared from plan Validity table: ", recordsCompared);
            return cb(err);
        }
        data.forEach(element => {
            let row = {}
            if(!element.customer_id || !element.operator || !element.recharge_number) {
                return;
            }
            row['recharge_number'] = element.recharge_number
            row['customer_id'] = element.customer_id
            row['operator'] = element.operator
            returnData.push(row)
        });
        id = data[data.length-1].id //max id
        return cb(null, returnData);
    }, 'RECHARGE_ANALYTICS_SLAVE', sql, []);
}

function executeRecursive(options, cb){
    readDataFromPV(options, function(err, data) {
        if(err){
            return cb(err);
        }
        if(!data) {
            return cb("no more data in PV table")
        }
        readDataFromDthTable(options, data, function (err) {
            executeRecursive(options, cb)
        })
    })
}

function executeFlow(options, cb) {
    executeRecursive(options, function (err) {
        if (err) {
            return cb(err);
        }
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            pvTableName: 'plan_validity',
            dthTableName: 'dth_customer_info',
            operator: ['dishtv', 'd2h (formerly videocon d2h)'],
            size: 1000
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
