const async = require('async');
import startup from '../lib/startup'


let OPTIONS = null;
let id = 0;


function readDataFromtableAndUpdate(options, cb) {
    console.log("Now using id: ", id)
    let sql = `SELECT id, recharge_number from ${options.tableName} where  id > ${id} ORDER BY id limit ${options.size}`;
    console.log("Read query-------", sql);
    OPTIONS.dbInstance.exec(function (err, data) {
        console.log("data  read in table", JSON.stringify(data))
        if (err || data.length == 0) {
            if(err){
                console.log("error in fetching data : ", err, "for query:  ", sql);
            }
            console.log("data not found: ", JSON.stringify(data));
            return cb(err);
        }
        data.forEach(element => {
            if(element.recharge_number.startsWith('1') || element.recharge_number.startsWith('4') || element.recharge_number.startsWith('6') || element.recharge_number.startsWith('9')){
                let sqlQuery = `delete from ${options.tableName} where id= ${element.id} and recharge_number = ${element.recharge_number}`;
                console.log("SQL query-------", sqlQuery);
                OPTIONS.dbInstance.exec(function (err, updateDataNull) {
                    console.log("updateDataNull in  Pv", JSON.stringify(updateDataNull))
                    if (err) {
                        console.log("error in update category name null for data", JSON.stringify(data))
                        return cb(err);
                    }
                }, 'DIGITAL_REMINDER_MASTER', sqlQuery, []);
            
         }
        });
        id = data[data.length-1].id //max id
        setTimeout(() => {
            readDataFromtableAndUpdate(options, cb);// recursive call

        }, 2000);
        
    }, 'DIGITAL_REMINDER_SLAVE', sql, []);

    
}


function executeFlow(options, cb) {
    readDataFromtableAndUpdate(options, function (err) {
        if (err) {
            console.log("error in main flow", err);
            
        }
        return cb(err);
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            tableName: 'bills_apspdcl',
            size: 500
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                //process.exit(0);
            }, 86400000);// 24 hr
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();
