#!/usr/bin/env node
"use strict";

import _        from 'lodash'
import L        from 'lgr'
import SQLWRAP  from 'sqlwrap'
import ASYNC    from 'async'
import MOMENT   from 'moment'
import PROGRAM     from 'commander'
// If required Load Config from startup.init

class MoveBillsData{

    constructor(options){
        this.dbInstance     = new SQLWRAP(CONFIG.SQLWRAP)
        this.prepareData();
    }

    prepareData(){
        // this.billsTable     = 'bills_haryanaelectricity'   
        this.billsTable = PROGRAM.tableName    
        this.batchSize      = 1000
        this.batchNo        = 0
        this.totalRecordsInActiveCycle = 0
        this.maxRecordsInActiveCycle   = PROGRAM.totalRecords || null ;
        this.previousCycleNBFD  = MOMENT().add(-33,'days').format('YYYY-MM-DD HH:mm:ss')
        this.currentCycleNBFD   = MOMENT().add(1,'days').format('YYYY-MM-DD HH:mm:ss')
        this.totalAffectedData  = 0
        this.eligiblePaymentMonth = 12 // december
        this.totalBatches = parseInt(this.maxRecordsInActiveCycle / this.batchSize)
        this.paymentDateStart = PROGRAM.paymentDateStart || null ;
        this.paymentDateEnd = PROGRAM.paymentDateEnd || null ;
        this.dueDateStart = PROGRAM.dueDateStart || null ;
        this.dueDateEnd = PROGRAM.dueDateEnd || null;
        this.nbfdStart = PROGRAM.nbfdStart || null ;
        this.nbfdEnd = PROGRAM.nbfdEnd || null ;
        this.desiredNbfd = PROGRAM.desiredNbfd ||  null ;
        this.ids = ''
        if(!this.billsTable || !this.nbfdStart || !this.nbfdEnd || !this.maxRecordsInActiveCycle ){
            this.terminate("One of TableName, nbfd startDate, nbfd endDate, maxRecords is not present")
        }
    }

    start(){
        let 
            self = this;
                    
        self.batchNo++;
        L.log('start::','Processing batch',self.batchNo);

        // to validate nbfd call, uncomment the bellow lines
        // if(! this.validateDateFormat(process.argv[5])){
        //   return  this.terminate("Target NBFD is not in valid Format")
        // }
        if(self.batchNo > this.totalBatches){
            return self.terminate();
        }

        self.fetchData((error,data)=>{
            L.log('start',`No. of records fetched for batch: ${self.batchNo} :${_.get(data,'length',0)}`)
            let offset = self.maxRecordsInActiveCycle - self.diff
            if(error) {
                L.error('start::fetchData','Error encountered',error);
                return self.terminate();
            }
            else if(_.get(data,'length',0) <= 0 ){
                L.log('start::fetchData',`No more data found to process for batch ${self.batchNo}`);
                return self.terminate();
            }
            else {
                self.processData(()=>{
                    L.log('start::processData',`Process Data finished for batch :${self.batchNo}`);
                    self.start();
                },data);
            }        
        },self.billsTable,self.getConditionToFetchRecords());    
    }

    processData(done,records){
        let 
            self = this;

            ASYNC.each(records,function(record,cb){
                self.genDateToUpdate(record,cb);
                // let dataToUpdate = self.genDateToUpdate(record,cb);
                
                // self.updateDate(cb,record,dataToUpdate);
            },function(err){
                // if(err){
                //     return done(err); }
                    self.updateDate((err)=>{
                        done()
                    },null,self.desiredNbfd);

            }); 
    }

    genDateToUpdate(record,cb){
        let
            self            = this,
            dataToUpdate    = {};
        if(self.ids == '')
            self.ids = record.id;
        else
            self.ids = self.ids + ',' + record.id
        // if(self.totalAffectedData <= self.maxRecordsInActiveCycle) {
        dataToUpdate.next_bill_fetch_date = self.desiredNbfd;
        self.totalRecordsInActiveCycle = self.totalRecordsInActiveCycle + 1;
        // return dataToUpdate;  
        cb()     
    }


    updateDate(done,record,dataToUpdate){
        
        let self = this;
            
            self.ids.replace(',',"','")
            let query = `UPDATE ${self.billsTable} SET next_bill_fetch_date = '${dataToUpdate}' WHERE id IN (${self.ids})`;
            //  let params = [
            //     dataToUpdate.next_bill_fetch_date,
            //     record.id
            // ];
                      
            L.log('UPDATE Query is ' , query)
            self.dbInstance.exec(function(err){
            if(err) L.error('updateDate','error while executing',err);
            else {
                self.totalAffectedData = self.totalAffectedData + 1;
            }
            done(err);
        },'DIGITAL_REMINDER_MASTER',query);
    
}

    fetchData(done,tableName,condition){
        let 
            self    = this,
            query   = `SELECT * FROM ${tableName} WHERE ${condition}`;
            self.ids = '';
        if(self.totalAffectedData <= 0) {
            L.log('Executing query',query);
        }

        self.dbInstance.exec(function(err,records){
            L.verbose('fetchData','found records',records);
            if(err) L.critical('error while executing query:',query,err)
            done(err,records)
        },'DIGITAL_REMINDER_SLAVE', query);
    }

    getConditionToFetchRecords(){
        let 
            self                  = this,
            nextBillFetchDateFrom = self.nbfdStart,
            nextBillFetchDateTo   = self.nbfdEnd,

            condition             = `status not in (1,7,12,13) AND retry_count < 3 AND next_bill_fetch_date >= '${nextBillFetchDateFrom}' AND next_bill_fetch_date <= '${nextBillFetchDateTo}'`;
        
        if(self.paymentDateStart && self.paymentDateEnd) {
            condition += ` AND payment_date>='${self.paymentDateStart}' AND payment_date<='${self.paymentDateEnd}'`;
        }

        if(self.dueDateStart && self.dueDateEnd) {
            condition += ` AND due_date>='${self.dueDateStart}' AND due_date<='${self.dueDateEnd}'`;
        }

        condition+=`  LIMIT ${self.batchSize}`;
        return condition;
    }

    validateDateFormat(date){
        //validation rules
        return true;
    }

    terminate(message = null){
        let self = this;
        L.log('**** Total Affected Data:',self.totalAffectedData);
        L.log('**** Total Records moved in active cycle:',self.totalRecordsInActiveCycle);
        L.log('terminate','Terminating sql connection....');
        if (message){
            L.log(message)
        }      
        this.dbInstance.close(()=>{
            L.log('terminate','Terminating cron...');
            process.exit(0);
        });
    }
   
    checkCount(){
        let self = this;
        let query = `SELECT COUNT(*) FROM ${self.billsTable} WHERE next_bill_fetch_date="${self.desiredNbfd}";`
        L.log('Query executed-- ', query)
        self.dbInstance.exec(function(err,records){  
            if(err) L.critical('error while executing query:',query,err)
            L.log(`Count of Bills for ${self.desiredNbfd} -- `, records)
            self.terminate()
            // done(err,records)
        },'DIGITAL_REMINDER_SLAVE', query);
    }
}

/**
 * Argument guideline to execute this script :-
 * 
 * node dist/scripts/migrateBillsDate.js T N1 N2 P1 P2
 * -r  : Total number of max records to 
 * -a : next bill fetch date start time
 * -b : next bill fetch date end time
 * -c : target nbfd
 * -t : table name
 * -p : payment_date start time
 * -q : payment_date end time
 * -d : Due Date start time
 * -e : Due Date end Time
 * sample command-  node -r esm migrateBillsData.js  -a "2020-01-20 00:00:00 " -b "2020-02-14 00:00:00" 
 * -c "2020-02-20 00:00:00"  -p "2000-01-01 00:00:00" -q "2015-02-01 00:00:00" 
 * -t "bills_airtel" -r 1200
 * 
 * to check count of bills of particular nbfd
 * node dist/scripts/migrateBillsDate.js count TA TD
 * TA : Table Name
 * TD : Target NBFD 
 */


(function() {
    if (require.main === module) {

    PROGRAM
    .option('-m, --mode', 'Execute count script or update NBFD script')
    .option('-v, --verbose', 'Run in verbose mode')
    .option('-r, --totalRecords <type>', 'Total Records to process')
    .option('-a, --nbfdStart <type>', 'NBFD Start Date')
    .option('-b, --nbfdEnd <type>', 'NBFD End Date')
    .option('-c, --desiredNbfd <type>', 'Target NBFD Date')
    .option('-t, --tableName <type>', 'Target Table Name')
    .option('-p, --paymentDateStart <type>', 'PaymentDate Start')
    .option('-q, --paymentDateEnd <type>', 'PaymentDate Start')
    .option('-d, --dueDateStart <type>', 'DueDate Start')
    .option('-e, --dueDateEnd <type>', 'Due Date End')
    .parse(process.argv);

    /*
        setting log level as verbose
    */
    if(PROGRAM.verbose){
        L.setLevel('verbose');
    }
    
    // L.log('Script initiated with arguments with arguments....', PROGRAM.totalRecords, PROGRAM.nbfdEnd, PROGRAM.dueDateEnd);
    
    if(!PROGRAM.tableName || !PROGRAM.nbfdStart || !PROGRAM.nbfdEnd || !PROGRAM.totalRecords || !PROGRAM.desiredNbfd){
        L.error("One of TableName, nbfd startDate, nbfd endDate, maxRecords, NewNbfd is not present")
        process.exit(0)
    }
    L.log(`Script initiated with arguments with arguments.... ${process.argv}`);
    let MoveBillsDataObj = new MoveBillsData();
  
    if(PROGRAM.mode){
        MoveBillsDataObj.checkCount()
    }else{ 
        MoveBillsDataObj.start();
    }
    }   
}());