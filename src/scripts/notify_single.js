import L from 'lgr'
import Helper from '../lib/helper'
import startup from '../lib/startup'
import Notify from '../services/notify'

function getRecordFromId(id, dbInstance, cb) {
    let query = `select * from notification where id in (${id}) and status = 0`;

    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecord(opts, dbInstance, cb) {
    getRecordFromId(opts.id, dbInstance, cb);
}

function runForAllRecord(opts, options, cb) {
    let notify = new Notify(options);

    L.verbose('fetching record');
    getRecord(opts, options.dbInstance, function (err, records) {
        if (err) {
            return cb(err);
        }
        L.verbose('Running publisher for records: ' + JSON.stringify(records));
        notify.execSteps(records, 100, cb);
    });
}

function execute(opts, cb) {
    L.setLevel('verbose');

    if ((!opts.id || !opts.id.length)) {
        return cb(new Error('please provide id'));
    }

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }

        runForAllRecord(opts, options, cb);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-i, --id <value>', 'id', Helper.num_list)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=production node dist/scripts/notify_single.js --id
