'use strict'

import L from 'lgr'
import _ from 'lodash'
import ASY<PERSON> from 'async'
import VALIDATOR from 'validator'
import models from '../models'
import startup from '../lib/startup'
import utility from '../lib'
import MOMENT from 'moment'
import parquet from 'parquetjs-lite/parquet'
import AWS from 'aws-sdk'

class AirtelParquetLoader {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.billsModel = new models.Bills(options);
        this.tableName = 'bills_airtelprepaid';
        this.operator = 'airtel';
        this.paytype = 'prepaid';
        this.service = 'mobile';
        this.s3Params = {
            Bucket: _.get(this.config, 'AWS.Bucket', null),
            ClientInfo: _.get(this.config, 'AWS.ClientInfo', null),
        };
        this.bucketPrefix = _.get(this.config, 'AWS.BucketPrefix', null);
        this.greyScaleEnv = options.greyScaleEnv;
        this.batchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'AIRTEL_DWH_DATA_LOADER', 'BATCHSIZE'], 2) : _.get(this.config, ['DYNAMIC_CONFIG', 'COMMON', 'AIRTEL_DWH_DATA_LOADER', 'BATCHSIZE'], 100);
        this.skipInitialRecords = {
            'airtel_olap': _.get(this.config, ['DYNAMIC_CONFIG', 'COMMON', 'AIRTEL_DWH_DATA_LOADER', 'AIRTEL_OLAP_SKIP_INITIAL_RECORDS'], 0),
            'airtel_churn_olap': _.get(this.config, ['DYNAMIC_CONFIG', 'COMMON', 'AIRTEL_DWH_DATA_LOADER', 'AIRTEL_OLAP_CHURN_SKIP_INITIAL_RECORDS'], 0)
        }
        this.resetAnalyticsObject();
    }

    async start(done, opts) {
        let self = this;

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }

        self.durationToStartBillFetch = _.get(opts, 'durationToStartBillFetch', 1);
        self.NBFD = MOMENT().add(self.durationToStartBillFetch, 'days').format('YYYY-MM-DD 00:00:00')
        self.NBFDForChurn = MOMENT().add(self.durationToStartBillFetch-1, 'days').format('YYYY-MM-DD 00:00:00')        
        self.dry_run = opts.dry_run;

        await self.processParquetFile();
        return done(null);
    }

    async processParquetFile() {
        let self = this;

        self.L.log('Processing airtel_olap dataset....');
        self.dataSet = 'airtel_olap';
        await self.processAirtelDataSet('airtel_olap');

        self.L.log('Processing airtel_churn_olap dataset....');
        self.dataSet = 'airtel_churn_olap';
        self.NBFD = self.NBFDForChurn; // saving churn dataset to previous date for processing priority and visibility
        await self.processAirtelDataSet('airtel_churn_olap');

        return;
    }

    async processAirtelDataSet(dataSet) {
        let self = this;

        try {
            let s3Client = new AWS.S3(self.s3Params.ClientInfo);
            let path = await self.getLatestParquetFilePath(s3Client, dataSet);
            let params = {
                Bucket: self.s3Params.Bucket,
                Key: path
            };
    
            if (!path) {
                self.L.critical('processAirtelDataSet', `Could not get/Invalid path found in s3 for ${dataSet}`);
                return;
            }
    
            self.L.log('processAirtelDataSet', `Going to load data for ${dataSet} from ${path}`);
            let reader = await parquet.ParquetReader.openS3(s3Client, params);
    
            // create a new cursor
            //let cursor = reader.getCursor(['customer_id', 'recharge_number_1', 'product_id', 'cust_mobile', 'cust_email',]);
            let cursor = reader.getCursor();
            
            // read all records from the file and print them
            let record = null, batch = [];
            while (record = await cursor.next()) {
                self.L.verbose('processAirtelDataSet', `Processing record ${JSON.stringify(record)}`);
                if (self.skipInitialRecords[dataSet] <= self.analytics.totalProcessed) {
                    batch.push(record);
                } else {
                    self.analytics.skippedInitialRecords = self.analytics.skippedInitialRecords + 1;
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_OLAP',`DATASET:${dataSet}`,'STATUS:SKIPPED']);
                }
                self.analytics.totalProcessed = self.analytics.totalProcessed + 1;
                if (batch.length % self.batchSize === 0) {
                    self.L.log('processAirtelDataSet', `Inserting records for next batch...`);
                    await self._processRecords(batch);
                    batch = [];
                    self.L.log('processAirtelDataSet', `Progress...Analytics for ${dataSet}:${JSON.stringify(self.analytics)}`);
                    if (self.greyScaleEnv == 1) break;
                }
            }
            // Lets process remining records in batch
            if (batch && batch.length > 0) {
                self.L.log('processAirtelDataSet', `Inserting ${batch.length} records for last batch...`);
                await self._processRecords(batch);
                batch = [];
            }
            self.L.log('processAirtelDataSet', `Progress...Analytics for ${dataSet}:${JSON.stringify(self.analytics)}`);
    
            self.resetAnalyticsObject();
            await reader.close();
        } catch(error) {
            self.L.critical('processAirtelDataSet',`InCatch::Error for ${dataSet}: ${error}`);
        }
        
    }

    resetAnalyticsObject() {
        this.analytics = {
            totalProcessed: 0,
            invalidRecordsInFile: 0,
            dbInsertionFailure: 0,
            dbInsertionSuccess: 0,
            skippedInitialRecords: 0,
            startTime: MOMENT().format('YYYY-MM-DD HH:mm:ss')
        };
    }

    async _processRecords(batch) {
        let self = this;

        return new Promise((resolve) => {
            ASYNC.each(batch, ASYNC.ensureAsync(self._processRecord.bind(self)), function () {
                return resolve();
            });
        });
    }

    _processRecord(record, done) {
        let self = this;

        self.validateRecord(function (error) {
            if (error) {
                self.L.error('_processRecord', `Record Validation failed for ${JSON.stringify(record)}, Error ${error}`);
                self.analytics.invalidRecordsInFile = self.analytics.invalidRecordsInFile + 1;
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_OLAP',`DATASET:${self.dataSet}`,'STATUS:INVALID_RECORD']);
                done();
            } else {
                _.extend(record, {
                    operator: self.operator,
                    paytype: self.paytype,
                    service: self.service,
                    status: 0,
                    retry_count: 0,
                    reason: null,
                    extra: null,
                    published_date: null,
                    notification_status: 1,
                    customerOtherInfo: null,
                    is_automatic: 0,
                    due_date: null,
                    bill_fetch_date: null,
                    published_date: null,
                    next_bill_fetch_date: self.NBFD
                })

                self.L.log('_processRecord', `Going to insert/update record - ${record.traceKey}`);

                if (self.dry_run == 1) {
                    self.L.log('_processRecord', `skipping insertion since dry_run == 1 for ${record.traceKey}`);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_OLAP',`DATASET:${self.dataSet}`,'STATUS:DRY_RUN']);
                    return done();
                } else {
                    self.billsModel.insertCSVRecord((error, data) => {
                        if (error) {
                            self.analytics.dbInsertionFailure = self.analytics.dbInsertionFailure + 1;
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_OLAP',`DATASET:${self.dataSet}`,'STATUS:ERROR']);
                        } else {
                            self.analytics.dbInsertionSuccess = self.analytics.dbInsertionSuccess + 1;
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_OLAP',`DATASET:${self.dataSet}`,'STATUS:SUCCESS']);
                        }
                        return done();
                    }, self.tableName, record);
                }
            }
        }, record);
    }

    validateRecord(done, record) {
        let mandatoryParams = ['customer_id', 'recharge_number_1', 'product_id', 'cust_mobile'];

        for (let index in mandatoryParams) {
            let param = mandatoryParams[index];
            if (!_.get(record, param, null)) return done(`Mandatory param ${param} missing`);
            if (param == 'recharge_number_1' && !VALIDATOR.isMobilePhone(_.get(record, param, null), 'en-IN')) return done(`Invalid Recharge Number ${_.get(record, param, null)} passed`);
            if (param == 'cust_mobile' && !VALIDATOR.isMobilePhone(_.get(record, param, null), 'en-IN')) return done(`Invalid Mobile Number ${_.get(record, param, null)} passed`);
        }
        // renaming some keys which is diff from earlier file version
        record.recharge_number = record.recharge_number_1;
        record.customer_mobile = record.cust_mobile;
        record.customer_email = record.cust_email;

        record.traceKey = `customerId:${record.customer_id}_rechargeNumber:${record.recharge_number_1}_PID:${record.product_id}`;
        return done(null);
    }

    getLatestParquetFilePath(s3Client, dataSet) {
        let self = this;

        self.L.log('getLatestParquetFilePath', `Going to get parquet file path for ${dataSet} from S3`);
        return new Promise((resolve, reject) => {
            const params = {
                Bucket: self.s3Params.Bucket,
                Prefix: `${self.bucketPrefix}${dataSet}`
            };
            let maxEpoch = '', path = null;
            s3Client.listObjects(params, function (error, data) {
                if (error) {
                    self.L.error('getLatestParquetFilePath', `Failed for dataSet:${dataSet} with error:${error}`);
                    return resolve(null);
                } else if (data && data.Contents && data.Contents.length > 0) {
                    self.L.log('getLatestParquetFilePath',`data.Contents: ${JSON.stringify(data.Contents)}`);
                    data.Contents.forEach(dataElement => {
                        let key = _.get(dataElement, 'Key', null);
                        let keyArr = key.split('/');
                        if (key.indexOf('parquet') > -1 && keyArr && keyArr.length >= 4 && +keyArr[3] > +maxEpoch) {
                            maxEpoch = keyArr[3];
                            path = key;
                        }
                    });
                    if(self.validatePath(path)) {
                        return resolve(path);
                    } else {
                        self.L.error('getLatestParquetFilePath',`filePath validation failed for path: ${path}`);
                        return resolve(null);
                    }
                } else {
                    self.L.error('getLatestParquetFilePath', `Failed for dataSet:${dataSet} with error:Unable to find parquet file path due to insufficient response values`);
                    return resolve(null);
                }
            });
        });
    }

    /**
     * validates path based on partition-date to avoid re-processing of same file
     * Validate partition date should be previous month last date
     * Sample path: 'snapshots/cdo/airtel_churn_olap/1605628539730000/partition_date=2020-10-31/part-00000-4ef60ae8-e69f-4e2c-8c56-f8779c26332a.c000.parquet'
     * @param {*} path 
     */
    validatePath(path) {
        let self = this;
        let partitionDateStr = path.match(/partition_date=\d{4}-\d{2}-\d{2}/g); // this will give ['partition_date=2020-12-31']
        let partitionDate = partitionDateStr && partitionDateStr.length > 0 && partitionDateStr[0].match(/\d{4}-\d{2}-\d{2}/g); // this will give ['2020-12-31'] as array
        let previousMonthLastDate = MOMENT().subtract(1,'months').endOf('month').format('YYYY-MM-DD');
        
        self.L.log('validatePath',`Validating path against previousMonthLastDate${previousMonthLastDate} with extracted partitionDate:${partitionDate}`);
        if(MOMENT(partitionDate,'YYYY-MM-DD',true).isValid() && previousMonthLastDate == partitionDate) {
            return true;
        } else {
            return false;
        }
    }
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-r, --dry_run <value>', 'dry_run', Number)
            .option('-d, --durationToStartBillFetch <value>', 'durationToStartBillFetch', Number)
            .parse(process.argv);

        startup.init({
            exclude: {
                cvr: true,
                ruleEngine: true,
                activePidLib: true,
                mongoDb: true,
                dynamicConfig: false
            }
        }, function (err, options) {
            let script = new AirtelParquetLoader(options);
            script.start(function () {
                console.log("Script finished!!");
                console.timeEnd('Execution Time');
                setTimeout(function(){
                    console.log("Exiting Cron !!");
                    process.exit(0);
                }, 10*1000); // waiting 10 sec to complete parallel prometheous push.
            }, commander);
        });
    }
})();

// NODE_ENV=production node dist/scripts/airtel_dwh_pipeline.js --durationToStartBillFetch=7 --dry_run=1 -v

/*
Response of getObjectList:-
{ IsTruncated: false,
  Marker: '',
  Contents:
   [ { Key: 'snapshots/cdo/airtel_olap/1603972801089000/_SUCCESS',
       LastModified: 2020-10-29T12:24:00.000Z,
       ETag: '"d41d8cd98f00b204e9800998ecf8427e"',
       Size: 0,
       StorageClass: 'STANDARD' },
     { Key: 'snapshots/cdo/airtel_olap/1603972801089000/partition_date=2020-09-30/part-00000-d3d73ef9-7b11-46cb-bf3d-09a2ffdb47e4.c000.parquet',
       LastModified: 2020-10-29T12:22:45.000Z,
       ETag: '"748bafac7889e739ee86a656ff477cf9-16"',
       Size: 2061595425,
       StorageClass: 'STANDARD' },
     { Key: 'snapshots/cdo/airtel_olap/1603983316336000/_SUCCESS',
       LastModified: 2020-10-29T15:20:55.000Z,
       ETag: '"d41d8cd98f00b204e9800998ecf8427e"',
       Size: 0,
       StorageClass: 'STANDARD' },
     { Key: 'snapshots/cdo/airtel_olap/1603983316336000/partition_date=2020-09-30/part-00000-b8f3c39d-5c34-4759-9582-bf65c15bd563.c000.parquet',
       LastModified: 2020-10-29T15:19:55.000Z,
       ETag: '"d6c43c94c5e6d88a0553c57d4030702e-15"',
       Size: 1997570166,
       StorageClass: 'STANDARD' }
    ],
  Name: 'daas-computed-datasets-stg',
  Prefix: 'snapshots/cdo/airtel_olap',
  MaxKeys: 1000,
  CommonPrefixes: [] }
 */