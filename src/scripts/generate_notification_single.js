import L from 'lgr'
import _ from 'lodash'
import Helper from '../lib/helper'
import startup from '../lib/startup'
import BillReminderNotification from '../services/billReminderNotification'
import async from 'async'

function getRecordFromId(id, tableName, dbInstance, cb) {
    let query = `select * from ${tableName} where id in (${id})`;

    <PERSON>.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecordFromRechargeNumber(recharge_number, tableName, dbInstance, cb) {
    let query = `select * from ${tableName} where recharge_number in ("${recharge_number.join('","')}")`;

    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecord(opts, tableName, dbInstance, cb) {
    if (opts.recharge_number && opts.recharge_number.length) {
        getRecordFromRechargeNumber(opts.recharge_number, tableName, dbInstance, cb);
    } else if (opts.id && opts.id.length) {
        getRecordFromId(opts.id, tableName, dbInstance, cb);
    } else {
        return cb(null, []);
    }
}

function runForAllRecord(opts, tableName, options, cb) {
    let billReminderNotification = new BillReminderNotification(options);
    billReminderNotification.blackListOperators = _.get(billReminderNotification.notificationConfig, 'BlackListOperator', null);
    billReminderNotification.operatorsSendingNotificationToRegisteredUser = _.get(billReminderNotification.notificationConfig, 'registeredUserNotificationOperator', null);
    billReminderNotification.cvrData = _.get(billReminderNotification.config, 'CVR_DATA', {});
    L.verbose('fetching record');
    getRecord(opts, tableName, options.dbInstance, function (err, records) {
        if (err) {
            return cb(err);
        }
        L.verbose(`Total records: ${records.length}`);

        L.verbose('Inititlising publisher...');
        billReminderNotification.notify.configureKafkaPublisher(function () {
            async.eachSeries(records, function (currentRecord, cb) {
                L.verbose('Running publisher for record: ' + JSON.stringify(currentRecord));
                currentRecord = convertRecordToKafkaPayload(currentRecord);
                billReminderNotification.processNotification(function (err) {
                    if (err) {
                        return cb(err);
                    }
                    return cb();
                }, currentRecord);
            }, cb);
        });
    });
}

function convertRecordToKafkaPayload(currentRecord) {
    return {
        value: JSON.stringify({
            source: "billReminderNotification_single",
            notificationType: "BILLGEN",
            data: {
                id: currentRecord.id,
                is_automatic: currentRecord.is_automatic,
                customerId: currentRecord.customer_id,
                rechargeNumber: currentRecord.recharge_number,
                productId: currentRecord.product_id,
                operator: currentRecord.operator,
                amount: currentRecord.amount,
                dueDate: currentRecord.due_date,
                billFetchDate: currentRecord.bill_fetch_date,
                nextBillFetchDate: currentRecord.next_bill_fetch_date,
                gateway: currentRecord.gateway,
                paytype: currentRecord.paytype,
                service: currentRecord.service,
                circle: currentRecord.circle,
                customerMobile: currentRecord.customer_mobile,
                customerEmail: currentRecord.customer_email,
                paymentChannel: currentRecord.payment_channel,
                retryCount: currentRecord.retry_count,
                status: currentRecord.status,
                reason: currentRecord.reason,
                userData: currentRecord.user_data,
                createdAt: currentRecord.created_at,
                updatedAt: currentRecord.updated_at,
                billDate: currentRecord.bill_date,
                extra: currentRecord.extra,
                customerOtherInfo: currentRecord.customerOtherInfo,
                paymentDate: currentRecord.payment_date,
                notificationStatus: currentRecord.notification_status,
                serviceId: currentRecord.service_id
            }
        })
    };
}

function execute(opts, cb) {
    L.setLevel('verbose');

    if (!opts.operator) {
        return cb(new Error('operator is mandatory'));
    }
    if ((!opts.recharge_number || !opts.recharge_number.length) && (!opts.id || !opts.id.length)) {
        return cb(new Error('please provide recharge_number or id'));
    }

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }

        let tableName = _.get(options.config, ['OPERATOR_TABLE_REGISTRY', opts.operator], null);
        if (!tableName) {
            return cb(new Error('Operator table not found: ' + opts.operator));
        }
        runForAllRecord(opts, tableName, options, cb);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-i, --id <value>', 'id', Helper.num_list)
            .option('-r, --recharge_number <value>', 'recharge_number', Helper.list)
            .option('-t, --operator <value>', 'operator', String)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=staging node dist/scripts/generate_notification_single.js --recharge_number '8130594357' --operator 'airtel'
