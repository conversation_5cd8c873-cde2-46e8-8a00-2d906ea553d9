import startup from '../lib/startup';
import _ from 'lodash';
import MOMENT from 'moment'
import { parseStream } from 'fast-csv';
import fs from 'fs'
import utility from '../lib'
import VALIDATOR from 'validator'


let
    operatorMissing = 0,
    rechargeNumberMissing =0,
    productIdMissing = 0,
    serviceMissing = 0,
    invalidRow = 0,
    totalRow = 0;

class billFetchMissedRevert{

    constructor(options) {
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.config = options.config;
    }

    updateBillForMissedBillFetch(cb, tableName, params) {
        let
            self = this,
            nextBillFetchDate = MOMENT().format('YYYY-MM-DD'),
            update_query = `UPDATE ${tableName} SET next_bill_fetch_date = ? , status = 2 WHERE recharge_number = ? and service = ? and operator = ? and product_id = ? and status = 7 and updated_at >= '2023-03-23'`,
            queryParams = [
                nextBillFetchDate,
                params.rechargeNumber,
                params.service,
                params.operator,
                params.productId
            ];

        self.L.log('updateBillForMissedBillFetch', 'query', self.dbInstance.format(update_query, queryParams));
        self.dbInstance.exec(function (err, data) {
            if(err)  self.L.critical('updateBillForMissedBillFetch::', 'error occurred : ', err);
            return cb(err, data);
        }, 'DIGITAL_REMINDER_MASTER', update_query, queryParams);
    }

   


    async  processRecord(cb, data){
        let self = this,
            params = {
            'rechargeNumber': _.get(data, 'recharge_number', null),
            'operator': _.get(data, 'operator', null),
            'service': _.get(data, 'service', null).toLowerCase(),
            "productId": _.get(data, 'product_id', null),
            }
        
        if(!params.rechargeNumber){
            rechargeNumberMissing++;
            return cb('Get rechargeNumber as null from CSV File');
        }

        if(!params.service){
            serviceMissing++;
            return cb('Get service as null from CSV File');
        }

        if(!params.productId){
            productIdMissing++;
            return cb('Get productId as null from CSV File');
        }

        if(!params.operator){
            operatorMissing++;
            return cb('Get operator as null from CSV File');
        }
        
        // parse customerId to integer if it is a number
        // if (params.customerId) {
        //     params.customerId = typeof (params.customerId) === 'string' ? VALIDATOR.toInt(params.customerId) : params.customerId;   
        // }



       // let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', params.productId], null);

       let tableName = 'bills_mangaloreelectricity';

        if(tableName){  
            self.updateBillForMissedBillFetch((error, data)=>{
                if (error) {
                    cb(error);
                } else {
                    self.L.log(`updateBillForMissedBillFetch:: updated successfully for `, JSON.stringify(params), 'respose:', JSON.stringify(data));
                    cb();
                }
            }, tableName, params);
        }else{
            invalidRow++;
            return cb('Invalid record');
        }
    }

    async readCSVFromPath(cb){
        let self = this;
        try {
            const s3Stream = fs.createReadStream('/home/<USER>/aaaa_manglore_retry_fetch_13_April.csv');
            const csvStream = parseStream(s3Stream,{headers : true});
        csvStream
            .on('data', (data) => {
                    totalRow++;
                    self.L.log(`readCSVFromPath :: processing for cust_Id: ${data.recharge_number}, recharge_number: '${data.service}'  , service: '${data.product_id}', NBFD: '${data.operator}' ,Row no: ${totalRow}`);
                    csvStream.pause();
                    self.processRecord((err)=>{
                        if(err){   
                        self.L.error('readCSVFromPath :: Error in processing data', err, JSON.stringify(data));
                        }
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILLFETCH_MISSED_REVERT', 'STATUS:READ_CSV_ROW']);
                        csvStream.resume();
                    }, data);
            })
            .on('end', rowCount => {
                setTimeout(()=>{
                    self.L.log(`readCSVFromPath :: ${rowCount} Data rows processed succesfully !!`);
                    return cb();
                },1000);
            })
            .on('error', error => {
                    return cb(error);
            });

        }catch(err)
        {
        return cb(err);
        }
    }
    
    async executeFlow(cb){
        let self = this;       
        self.L.log("executeFlow: start ");    
        let startTime = new Date().getTime(); 
        await self.readCSVFromPath(function (err) {
            if (err) {
                self.L.error('executeFlow :: Error ',err);
            }else{
                let endTime = new Date().getTime();
                self.L.log(`executeFlow::completed, totalRowCount : ${totalRow}, invalidRowCount : ${invalidRow}, serviceMissingCount : ${serviceMissing}, rechargeNumberMissingCount : ${rechargeNumberMissing}, operatorMissingCount : ${operatorMissing}, productIdMissing : ${productIdMissing}, executiontime : ${endTime-startTime} ms`);
            }
            return cb(err);
        });
    }
 
}
    
function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        let script = new billFetchMissedRevert(options);
        script.executeFlow(function (err) {
            setTimeout(function () {
            if (err) {
                console.log("main :: Error " ,err);
                process.exit(1);
                } else {
                console.log("main :: completed");
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILLFETCH_MISSED_REVERT', 'STATUS:SUCCESS']);
                process.exit(0);
                }
            }, 1000); 
        })
    });
}
    
(function () {
    if (require.main === module) {
        main();
    }
})();