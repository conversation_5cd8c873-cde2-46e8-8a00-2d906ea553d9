import _ from 'lodash'
import L from 'lgr'
import async from 'async'

import startup from '../lib/startup'
import digitalUtility from 'digital-in-util'
import utility from '../lib'

let ctKafkaPublisher = null;
let commonLib = null;
let reminderUtils = null;

function processRecord(options, dbRecordResp, dbInstance, cb) {
    let self = this;
    const customerId = _.get(dbRecordResp, 'customer_id', '');
    const operator = _.get(dbRecordResp, 'operator', '');
    const rechargeNumber = _.get(dbRecordResp, 'recharge_number', '');
    
    const eventName = _.get(options.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull');
    const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;
    

    let productId = _.get(dbRecordResp, 'product_id', '');
    productId = options.activePidLib.getActivePID(productId); // verify 
    
    if (!_.get(dbRecordResp, 'notification_status', 1)) {
        L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
        return cb()
    }

    if( eventName == _.get(options.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull') || !dbRecordResp.status){
        dbRecordResp.status = _.get(options.config, 'COMMON.bills_status.PAYMENT_DONE', 11)
    }
    L.log('processRecord::id', _.get(dbRecordResp, 'id' , "NOT_PRESENT" ) );
    async.waterfall([
            next => {
                commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {
                let mappedData = reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey); // check 
                let clonedData = _.cloneDeep(mappedData); 
                // kafka connection 
                ctKafkaPublisher.publishData([{
                    topic: _.get(options.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        L.critical('publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka on topic REMINDER_CT_EVENTS - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ],  (err) => {
                if(err) {
                    let errMsg = `processRecord Exception occured Error Msg:: ${err} for record::${JSON.stringify(dbRecordResp)} debugKey:: dbDebugKey`;
                    L.error(errMsg);
                } else {
                    L.log(`processRecord`,`Record processed having debug key`, dbDebugKey);
                }
                return cb();
        });
}


function execute(opts, cb) {
    L.setLevel('verbose');

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }
        let batchSize = 10; // <null/1/500>
        let tableName = "bills_rentpayment";

        commonLib = new utility.commonLib(options);
        reminderUtils = new digitalUtility.ReminderUtils();

        configureKafka(function (error) {
            if (error) {
                L.critical('Script :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                L.log('Script :: start', 'Kafka Configured successfully !!');
                runForAllRecord(options, tableName, batchSize, -1, options.dbInstance, cb);
            }
        }, options);
    });
}

function configureKafka(done, options) {
    let self = this;
    /**
     * maintain this sequence
     * Initialize publisher
     */
    async.waterfall([
        next => {
            /**
             * Kafka publisher to publish events to CT publisher pipeline 
             */
             
            ctKafkaPublisher = new options.INFRAUTILS.kafka.producer({
                "kafkaHost": options.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
            });
            ctKafkaPublisher.initProducer('high', function (error) {
                return next(error)
            });
        }
    ], function (error) {
        if (error) {
            L.critical('configureKafka', 'Could not initialize Kafka', error);
        }
        return done(error);
    });
}
function delay(cb, timeout) {
    setTimeout(()=>{
        return cb()
    },timeout)        
}

function runForAllRecord(options, tableName, batchSize, offset, dbInstance, cb) {
    // let query = `SELECT * FROM ${tableName} WHERE amount > 1000 AND due_date > (NOW() + INTERVAL 1 DAY) AND due_date > ${offset} ORDER BY due_date` // check condition
    let query = `SELECT * FROM ${tableName} WHERE amount > 1000 AND due_date > NOW() AND id > ${offset} ORDER BY id` // check condition
    if(batchSize) {
        query += ` LIMIT ${batchSize}`;
    }
    query += ';';

    execDBQuery(query, dbInstance, "READ", (err, records)=>{
        if (err) {
            console.log(err);
            console.log('execDBQuery :: FAILURE');
            L.log(`execDBQuery :: FAILURE`);
            return cb(err);
        }
        if(!records.length) {
            L.log(`No more records left for processing `);
            return cb();
        }
        L.log(`Total records: ${records.length}`);
        async.waterfall([
                next => {
                    async.eachSeries(records, function (currentRecord, cb) {
                        L.log('processing each record : ' + JSON.stringify(currentRecord));
                        processRecord(options, currentRecord, dbInstance, function (err) {
                            if (err) {
                                return cb(err);
                            }
                            return cb();
                        });
        
                    },next);
                },
                next => {
                    return delay(next, 100);
                }
            ],
            (error)=> {
                if(error) {
                    let errorMsg = 'runForAllRecord Error' + error;
                    L.error(errorMsg);
                    return cb(errorMsg);
                } else {
                    offset = records[records.length-1].id;
                    runForAllRecord(options, tableName, batchSize, offset, dbInstance, cb);
                }
            });
    })
}


function execDBQuery(query, dbInstance, operationType , cb) {
    L.log(`Executing: ${query}`);
    let MASER_SLAVE_TYPE = '';
    if(operationType === "READ") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_SLAVE';
    } else if (operationType === "WRITE") {
        MASER_SLAVE_TYPE = 'DIGITAL_REMINDER_MASTER';
    } else {
        cb('ERROR :: DB operation not specified');
    }
    
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, MASER_SLAVE_TYPE, query, []);
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();