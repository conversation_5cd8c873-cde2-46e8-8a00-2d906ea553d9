import SQLWRAP from 'sqlwrap'
import Async from 'async'
import L from 'lgr'
// If required load config from startup.init

let dbInstance = new SQLWRAP(config.SQLWRAP);

function execute(options, cb) {
    L.setLevel(options.log_level);
    getMaxId(options, function (err, maxId) {
        if (err) {
            return cb(err);
        }
        let ranges = [];
        for (let i = options.from, index = 0; i <= maxId; i += options.batch, index++) {
            ranges.push([i, i + options.batch, index]);
        }
        L.verbose(`Ranges: ${JSON.stringify(ranges)}`);
        Async.eachSeries(ranges, function ([from, to, index], cb) {
            L.info(`Progress: from: ${from}, to: ${to}, (${index} / ${ranges.length}) - ${Math.floor(index * 100 / ranges.length)}%`);
            getAllRecords(from, to, options, function (err, records) {
                if (err) {
                    return cb(err);
                }
                L.verbose(`Found ${records.length} records for - ${from} : ${to}`);
                Async.eachSeries(records, function (record, cb) {
                    checkDuplicate(record, options, function (err, isDuplicate) {
                        if (err) {
                            return cb(err);
                        }
                        if (!isDuplicate) {
                            L.verbose(`Not duplicate - id = ${record.id}, recharge_number = '${record.recharge_number}', customer_id = '${record.customer_id}', operator = '${record.operator}', service = '${record.service}'`);
                            return cb();
                        }
                        deleteRecord(record, options, cb);
                    });
                }, cb);
            });
        }, cb);
    });
}

function getMaxId(options, cb) {
    let query = `select max(id) max_id from ${options.table}`;
    myExec(query, 'DIGITAL_REMINDER_SLAVE', function (err, data) {
        if (err) {
            return cb(err);
        }
        if (!data || !data.length) {
            return cb(new Error('max_id not found'));
        }
        let maxId = data[0].max_id;
        return cb(null, maxId);
    });
}

function getAllRecords(from, to, options, cb) {
    let query = `select * from ${options.table} where id >= ${from} AND id < ${to}`;
    myExec(query, 'DIGITAL_REMINDER_SLAVE', function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    });
}

function checkDuplicate(record, options, cb) {
    let query = `select id from ${options.table} where recharge_number = '${record.recharge_number}' and customer_id = '${record.customer_id}' and operator = '${record.operator}' and service = '${record.service}' and id > ${record.id} limit 1`;
    myExec(query, 'DIGITAL_REMINDER_SLAVE', function (err, data) {
        if (err) {
            return cb(err);
        }
        if (!data.length) {
            return cb(null, false);
        }
        return cb(null, true);
    });
}

function deleteRecord(record, options, cb) {
    let query = `delete from ${options.table} where id = ${record.id}`;
    if (!options.remove) {
        L.warn(`DELETE DRY RUN - ${query}`);
        return cb();
    }
    myExec(query, 'DIGITAL_REMINDER_MASTER', function (err) {
        if (err) {
            return cb(err);
        }
        L.error(`DELETED - id = ${record.id}, recharge_number = '${record.recharge_number}', customer_id = '${record.customer_id}', operator = '${record.operator}', service = '${record.service}'`);
        return cb();
    });
}

function myExec(query, database, cb) {
    L.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, database, query, []);
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-l, --log_level <value>', 'log_level', String, 'info')
            .option('-f, --from <value>', 'from', Number, 1)
            .option('-b, --batch <value>', 'batch', Number, 10000)
            .option('-t, --table <value>', 'table', String, 'bills_gujaratgas')
            .option('-r, --remove 1', 'Remove ids')
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    L.critical(err);
                    L.critical('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                L.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();
