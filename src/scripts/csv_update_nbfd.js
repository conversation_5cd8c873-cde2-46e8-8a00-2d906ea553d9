import startup from '../lib/startup';
import MOMENT from 'moment';
import _ from "lodash";
import L from 'lgr';
import fs from 'fs'
import { parseStream } from 'fast-csv';

let totalRow = 0;
class csv_update_nbfd {
    constructor(options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.config = options.config;
    }
    

    async processRecord(cb, options, data) {
        let self = this;
        let params =
        {
            'customerId': _.get(data, 'so_customer_id', null),
            'rechargeNumber': _.get(data, 'subscriber_id', null),
            'next_bill_fetch_date': MOMENT().format("YYYY-MM-DD HH:mm:ss"),
        }

        let tableName = 'bills_airteltv';
        if(!params.customerId || !params.rechargeNumber){
            console.log("missing");
            return cb();
        }
        self.updateRecord(tableName, params, (error) => {
            if (error) {
                L.error('error', error);
            }
           console.log( ++totalRow);
            return cb(error);
        });    
    }


    updateRecord(table_name, params, cb) {
        let self = this;
        const query = `UPDATE ${table_name} SET next_bill_fetch_date = ? WHERE customer_id = ? AND recharge_number = ?;`;

        const param = [
            params.next_bill_fetch_date,
            params.customerId,
            params.rechargeNumber
        ];

        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('writeCustomerDetails::', query, error);
            }
            return cb(error, res);
        }, 'DIGITAL_REMINDER_MASTER', query, param);
    }

    getCSV_DataFrom_Local(options, cb) {
        let self = this;
        try {

            const Stream = fs.createReadStream('./airtel_may.csv')
            const csvStream = parseStream(Stream, { headers: true });
            csvStream
                .on('data', (data) => {
                    csvStream.pause();
                    self.processRecord((err) => {
                        if (err) {
                            self.L.error('getCSV_DataFrom_Local :: Error in processing data', err, JSON.stringify(data));
                        }
                        csvStream.resume();
                    }, options, data);
                })
                .on('end', rowCount => {
                    setTimeout(() => {
                        self.L.log(`getCSV_DataFrom_Local :: ${rowCount} Data rows processed succesfully !!`);
                        return cb();
                    }, 1000);
                })
                .on('error', error => {
                    setTimeout(() => {
                        self.L.error("processCSV data", "Error", error);
                        return cb(error);
                    }, 1000);
                });

        }
        catch (err) {
            return cb(err);
        }
    }

    async start(options, cb) {
        let self = this;
        await self.getCSV_DataFrom_Local(options, function (err) {
            if (err) {
                self.L.error('start::Error', err);
            } else {
                console.log("start::completed, totalRowCount  ", totalRow);
              //  self.L.log(`start::completed, totalRowCount : ${totalRow}, mobileNumberMissingCount :${mobileNumberMissing}, customerIdMissingCount : ${customerIdMissing}, serviceMissingCount : ${serviceMissing}, operatorMissingCount : ${operatorMissing}, rechargeNumberMissingCount : ${rechargeNumberMissing}, productIdMissingCount : ${productidMissing}, OAuthNullResponseCount : ${OAuth_Response_null}, InsertedRowsCount : ${inserted_row_count}, InvalidRowsCount : ${totalRow - inserted_row_count}`);
            }
            return cb(err);
        });
    }
}
(function main() {
    if (require.main === module) {

        startup.init({
              removeExpiredPlanValidity: true
        }, function (err, options) {
            let script = new csv_update_nbfd(options);
            script.start(options,
                function (err) {
                    setTimeout(function () {
                        if (err) {
                            console.log("main::Error" + err);
                            process.exit(1);
                        } else {
                            console.log("main::completed");
                            process.exit(0);
                        }
                    }, 1000);
                })
        });
    }
})();
