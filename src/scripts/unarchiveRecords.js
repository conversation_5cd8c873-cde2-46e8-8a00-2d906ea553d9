/*jshint multistr: true ,node: true, esversion: 8*/
"use strict";

import L from 'lgr';
import ASYNC from 'async';
import _ from 'lodash';
import MOMENT from 'moment';
import SQLWRAP from 'sqlwrap';
import startup from '../lib/startup'

let scriptStats = {};


class UnarchiveRecords {
    constructor(options) {
        this.config = options.config;
        this.L = L;
        this.operatorsList = [];
        this.limit = 1000;
        this.parallelProcessing = 50;
        this.dbInstance = new SQLWRAP(this.config.SQLWRAP);
        this.billDateBasedGateways = _.get(this.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        this.payment_date = "2020-01-01";
        this.sourceTable = "bills_archive";
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: unarchiveRecords", "Re-initializing variable after interval");
        self.billDateBasedGateways = _.get(this.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
    }

    start(cb, opts) {
        let self = this;
        if (!opts.operatorList) {
            return cb(new Error('Operator List is not defined'));
        }
        self.operatorList = opts.operatorList;
        let operatorNotFound = null;

        for (let opr of self.operatorList) {
            if (!_.get(self.config, ['OPERATOR_TABLE_REGISTRY', opr], null)) {
                operatorNotFound = opr;
                break;
            }
        }

        if (operatorNotFound) {
            return cb(new Error("Operator not found: " + operatorNotFound));
        }

        if (opts.payment_date && MOMENT(opts.payment_date, 'YYYY-MM-DD', true).isValid()) {
            self.payment_date = opts.payment_date;
        }
        self.setPaymentDate = opts.setpaymentdate ? true : false;
        self.defaultnbfdDays = opts.durationToStartBillFetch || 1;
        self.defaultnbfd = MOMENT().add(self.defaultnbfdDays, 'days').format('YYYY-MM-DD 00:00:00');
        self.index = opts.index || 0;

        if (opts.testLimit && (opts.testLimit <= 0 || opts.testLimit > 500)) {
            return cb(new Error("Please enter the test limit in 1-500"));
        }

        if (opts.testLimit && opts.testLimit > 0 && opts.testLimit <= 500) {
            self.L.log('start::', `Sample run for operator ${self.operatorList[0]} and limit ${opts.testLimit}`);
            self.fetchSampleData(self.payment_date, self.operatorList[0], opts.testLimit, (err, records) => {
                if (err) {
                    return cb(err);
                } else if (records && records.length > 0) {
                    self.tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', self.operatorList[0]], null);
                    self.operator = self.operatorList[0];
                    scriptStats[self.operator] = scriptStats[self.operator] || { 'totalRecords': 0, 'unarchivedRecords': 0, 'alreadyPresentRecords': 0, 'totalErrors': 0 };
                    scriptStats[self.operator].totalRecords += records.length;
                    self.processInBatch(cb, records, self.tableName);
                } else {
                    return cb(new Error(`No Data Found for operator ${self.operatorList[0]}`));
                }
            });
        }
        else {
            ASYNC.eachSeries(self.operatorList, self.processOperator.bind(self),
                (err) => {
                    if (err) {
                        return cb(err);
                    }
                    return cb();
                }
            );
        }
    }

    processOperator(operator, cb) {
        let self = this;
        let index = self.index;
        self.tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
        self.operator = operator;
        self.L.log('processOperator::', 'Starting unarchiving records for operator ', operator, self.tableName, index);
        scriptStats[self.operator] = scriptStats[self.operator] || { 'totalRecords': 0, 'unarchivedRecords': 0, 'alreadyPresentRecords': 0, 'totalErrors': 0 };
        self._processRecords(self.tableName, index, operator, function _keepFetching(err, count, index) {
            self.L.log('processOperator::', '_processRecords: ', count, index);
            if (count > 0 && index) {
                self._processRecords(self.tableName, index, operator, _keepFetching);
            }
            else {
                return cb();
            }
        });
    }

    _processRecords(tableName, index, operator, done) {
        let self = this;
        ASYNC.waterfall([
            //fetch records from Database
            next => {
                self.fetchData(index, self.payment_date, operator, self.limit, next);
            },

            //Publish and update the records
            (records, next) => {
                L.log('_processRecords', `Processing ${records.length} records for ${operator}`);
                scriptStats[self.operator].totalRecords += records.length;
                self.processInBatch(function () {
                    if (records.length > 0) {
                        return next(null, records.length, records[records.length - 1].id);
                    }
                    return next(null, records.length);
                }, records, tableName, 0);
            }
        ],
            (err, count, index = null) => {
                if (err) {
                    return done(err);
                }
                return done(null, count, index);
            }
        );

    }

    processInBatch(cb, records, tableName, pointer = 0) {
        let self = this;
        if (pointer >= records.length || !records.length) {
            setTimeout(() => {
                return cb();
            }, 1000);
        } else {
            ASYNC.each(records.slice(pointer, pointer + self.parallelProcessing), ASYNC.ensureAsync(self._processRecord.bind(self)), function () {
                self.processInBatch(cb, records, tableName, pointer + self.parallelProcessing);
            });
        }
    }

    _processRecord(record, done) {
        let self = this;
        let nbfd = self.calcNbfd(record);
        let params = {
            'next_bill_fetch_date': nbfd,
        };
        ASYNC.waterfall([
            (next) => {
                self.validateRecord(record, next);
            },
            (next) => {
                self.checkSameRechNumRecords(self.tableName, record, (err, data) => {
                    if (data) {
                        data.forEach(ele => {
                            if (ele.is_automatic == 1) {
                                record.is_automatic = 1;
                            }
                        });
                        params.next_bill_fetch_date = data[0].next_bill_fetch_date;
                    }
                    next();
                });
            }],
            (err) => {
                if (err) {
                    self.L.error("_ProcessRecord::", "Invalid record ", JSON.stringify(record));
                    return done();
                } else {
                    self.insertIntoTable(self.tableName, record, params, () => {
                        return done();
                    });
                }
            });
    }

    validateRecord(record, next) {
        if (!record.customer_id || _.isEmpty(record.recharge_number) || _.isEmpty(record.service)) {
            return next("Invalid Record");
        }
        return next();
    }

    calcNbfd(record) {
        let self = this;
        let operator = record.operator;
        let dateToBeUsed = self.billDateBasedGateways.indexOf(operator) > -1 ? record.bill_date : record.due_date;
        let cycle = _.get(self.config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', operator], null);
        if (!cycle || !dateToBeUsed) {
            return self.defaultnbfd;
        }
        let nbfd =cycle < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(cycle)), 'months'): MOMENT(dateToBeUsed).add(cycle, 'days');
        while (nbfd < MOMENT().subtract(10, 'days')) {
            nbfd = cycle < 0 ? nbfd.add(Math.abs(Number(cycle)), 'months') : nbfd.add(cycle, 'days');
        }
        return nbfd.format('YYYY-MM-DD 00:00:00');
    }

    fetchData(index, payment_date, operator, limit, cb) {
        let self = this;
        let query = `SELECT * FROM ${self.sourceTable} WHERE id > ? AND payment_date > ? AND operator = ? ORDER BY id ASC LIMIT ?`;
        let queryParams = [
            index,
            payment_date,
            operator,
            limit
        ];
        L.verbose('fetchData', self.dbInstance.format(query, queryParams));

        self.dbInstance.exec(function (err, data) {
            if (err) {
                self.L.error('fetchData::', 'Error while fetchData from bills_archive table', err);
                scriptStats[self.operator].totalErrors += 1;
                return cb(null, []);
            }
            else if (data && data.length > 0) {
                return cb(null, data);
            }
            else {
                self.L.error('fetchData::', 'Body is empty', data);
                return cb(null, []);
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    fetchSampleData(payment_date, operator, limit, cb) {
        let self = this;
        let query = `SELECT * FROM ${self.sourceTable} WHERE payment_date > ? AND operator = ? ORDER BY id ASC LIMIT ?`;
        let queryParams = [
            payment_date,
            operator,
            limit
        ];
        L.verbose('fetchSampleData', self.dbInstance.format(query, queryParams));

        self.dbInstance.exec(function (err, data) {
            if (err) {
                self.L.error('fetchSampleData::', 'Error while fetchData from bills_archive table', err);
                scriptStats[self.operator].totalErrors += 1;
                return cb(null, []);
            }
            else if (data && data.length > 0) {
                return cb(null, data);
            }
            else {
                self.L.error('fetchSampleData::', 'Body is empty', data);
                return cb(null, []);
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }


    checkSameRechNumRecords(tableName, record, cb) {
        let self = this;
        let query = `SELECT is_automatic, next_bill_fetch_date FROM ${tableName} WHERE recharge_number = ? AND operator=? AND service=?`;
        let queryParams = [
            record.recharge_number,
            record.operator,
            record.service
        ];

        self.dbInstance.exec(function (err, data) {
            if (err) {
                self.L.error('checkSameRechNumRecords::', `error while selecting from ${tableName}`, record.recharge_number, err);
            } else if (data && data.length > 0) {
                self.L.log('checkSameRechNumRecords::', `Multiple Records found in ${tableName} for recharge_number: `, record.recharge_number);
                return cb(null, data);
            }
            return cb();
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }

    insertIntoTable(tableName, record, params, cb) {
        let self = this;
        try {
            let payment_date = self.setPaymentDate ? null : record.payment_date;
            let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id, operator,gateway,next_bill_fetch_date , bill_date,
            service,paytype,circle,customer_mobile,customer_email,retry_count,status,user_data, notification_status, payment_date, service_id,due_date,is_automatic)                
            VALUES ?`;
            let queryParams = [[[
                record.customer_id,
                record.recharge_number,
                record.product_id,
                _.toLower(record.operator),
                record.gateway,
                params.next_bill_fetch_date,
                record.bill_date,
                _.toLower(record.service),
                record.paytype,
                record.circle,
                record.customer_mobile,
                record.customer_email,
                record.retry_count,
                0,
                record.user_data,
                _.get(record, 'notification_status', 0),
                payment_date,
                _.get(record, 'service_id', 0),
                record.due_date,
                record.is_automatic
            ]]];

            L.verbose('insertIntoTable', self.dbInstance.format(query, queryParams));

            self.dbInstance.exec(function (err, data) {

                if (err && err.code && err.code == 'ER_DUP_ENTRY') {
                    // Looks like record already exists...skipping it...
                    scriptStats[self.operator].alreadyPresentRecords += 1;
                    self.L.log('insertIntoTable', 'Record already exists for operator:' + record.operator + ' for cust_id: ' + record.customer_id + ', recharge_number: ' + record.recharge_number);
                    return cb(false);
                }
                else if (err || !(data)) {
                    self.L.critical('insertIntoTable', `error occurred while inserting data from DB: for ${record.operator} ${record.customer_id}::${record.recharge_number}`, err);
                    scriptStats[self.operator].totalErrors += 1;
                    return cb(false);
                } else {
                    scriptStats[self.operator].unarchivedRecords += 1;
                    self.L.log('insertIntoTable:: ', `Record Successfully inserted for operator ${record.operator}`, JSON.stringify(record), payment_date, params.next_bill_fetch_date);
                    return cb(true);
                }
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        }
        catch (err) {
            self.L.error('insertIntoTable::', `error in try catch ${record.operator}_${record.customer_id}_${record.recharge_number}`, err);
            return cb(false);
        }
    }

}




(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-d, --durationToStartBillFetch <value>', 'durationToStartBillFetch', Number)
            .option('-o, --operatorList <items>', "operators list", (value) => { return value.split(','); })
            .option('-p, --payment_date <value>', "paymentDate", String)
            .option('-t, --testLimit <value>', 'Test mode to unarchive records', Number)
            .option('-i, --index <value>', 'Id of bills_archive table', Number)
            .option('-s, --setpaymentdate', "SetpaymentDate")
            .parse(process.argv);
            
        startup.init({}, function(error, options){
            if(error) {
                console.log('Startup init crashed...',error);
                process.exit(1);
            }

            let script = new UnarchiveRecords(options);
            script.start(function (err) {
                setTimeout(function () {
                    let errors = false;
                    for (let i in scriptStats) {
                        L.log(`Script Summary of operator : ${i}`, JSON.stringify(scriptStats[i]));
                        if (scriptStats[i].totalErrors > 0) {
                            errors = true;
                        }
                    }
                    if (err) {
                        console.error(err);
                        console.error('FAILURE');
                        console.timeEnd('Execution Time');
                        L.error('Process Finished with error::', err);
                        process.exit(1);
                    } else if (errors) {
                        console.timeEnd('Execution Time');
                        L.error('Process Finished with some errors::');
                        process.exit(1);
                    }
                    console.error('SUCCESS');
                    console.timeEnd('Execution Time');
                    L.error('Process Finished Successfully::');
                    process.exit(0);
                }, 1000);
            }, commander);
        });
    }
})();

// NODE_ENV=production node dist/scripts/unarchiveRecords.js  -d 1 -o "tsspdcl,torrent power limited" -p "2020-02-01" -t 10