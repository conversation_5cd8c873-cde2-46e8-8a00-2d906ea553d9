/**
 * Running the script
 * NODE_ENV=production node dist/scripts/updateNBFD.js 
 * 
 * For one batch only -
 * GREYSCALE=1 NODE_ENV=production node dist/scripts/updateNBFD.js 
 * 
 * For different starting ID
 * NODE_ENV=production node dist/scripts/updateNBFD.js -s 1000
 */

import L from 'lgr'
import _ from 'lodash'
import ASYNC from 'async'
import { parse } from 'fast-csv'
import FS from 'fs'
import VALIDATOR from 'validator'
import models from '../models'
import SQLWRAP from 'sqlwrap'
import MOMENT from 'moment'
import startup from '../lib/startup'
import { setFips } from 'crypto'
import utility from '../lib'
const PARALLEL_HITS = 5;
const updateDelay = 4;
const fetchDelay = 200;
// import DeleteFromAllSystem from '../lib/deleteFromAllSystem'

class UpdateNBFD {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.dbBatchSize = _.get(options, 'greyScaleEnv', false) == false ? 1000 : 5
        //this.dbUpdateBatchSize =  _.get(options, 'greyScaleEnv', false) == false ? 100 : 2
        this.id = _.get(options,'startingID',0)
        this.dbInstance=options.dbInstance
    }

    start(cb, opts) {
        let self = this;

        self.L.setLevel('verbose');
        
        self.fetchRecords(function _doUntilNoMoreRecords(error, data) {
            if (error) {
                self.L.error('updateNBFD', `Error in fetching records ${error} for starting id ${self.id}`);
                return cb(error);
            }
            if (!error && data && data.length > 0) {
                self._processRecordsInBatch(() => {
                    self.id  = _.last(data)['id']

                    if (self.greyScaleEnv) {
                        self.L.log('updateNBFD', `end time in processing one batch for Grayscale ${MOMENT().format('YYYY-MM-DD HH:mm:ss')} with starting id ${self.id}`)
                        return cb();
                    }
                    
                    self.L.log('updateNBFD', `processing next batch , Starting id  ${self.id}`);
                    setTimeout(() => {
                        self.fetchRecords(_doUntilNoMoreRecords);
                    }, fetchDelay);
                },data);
            }
            else {
                self.L.log('updateNBFD', `No record found for Starting id  ${self.id}`);
                cb();
            }
        });

        // self.path = _.get(opts, 'path', null);
        // this.processCSV(function(err){
        //     return cb(err)
        // },)
    }


    fetchRecords(done) {
        self.L.log('updateNBFD', `processing next batch, Starting time ${MOMENT().format('YYYY-MM-DD HH:mm:ss')} with starting id ${self.id}`);
        let self = this;
        const query = `SELECT * from bills_haryanaelectricity where id > ${self.id} order by id asc limit ${self.dbBatchSize} `
        self.L.log("fetchRecords" ,query,[self.dbBatchSize , self.id])
        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('fetchRecords::', query, error);
            }
            return done(error,res);
        }, 'DIGITAL_REMINDER_SLAVE', query, [self.dbBatchSize , self.id]);
    }


 

    _processRecordsInBatch(done, records) {
        let
            self = this;
       

        ASYNC.eachLimit(records , 
            PARALLEL_HITS , 
            (record , next)=>{

                let billDate = _.get(record , 'bill_date',null)
                let id = record.id;
                if(this.checkIfRecordHasToBePublished(record)){
                    let nbfd;

                    if(billDate == null || !MOMENT(billDate).isValid()){
                        L.info("", "Invalid bill date for id", id)
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:UPDATE_NBFD',
                            'STATUS:ERROR',
                            'REASON:INVALID_BILL_DATE']);
                        nbfd = MOMENT().add(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
                    }
                    else if( billDate!=null && MOMENT(billDate).isValid() && MOMENT(billDate).add(28, 'days').isAfter(MOMENT())){
                        nbfd = MOMENT(billDate).add(28, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
                    }else{
                        nbfd = MOMENT().add(1, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss')
                    }

                    const query = `UPDATE  bills_haryanaelectricity SET next_bill_fetch_date='${nbfd}' where id=${id}`
                    self.L.log("","updating records ",query)
    
                    self.dbInstance.exec((error, res) => {
                        if (error) {
                            self.L.critical('writeCustomerDetails::', query, error);
                        }
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:UPDATE_NBFD',
                            'STATUS:SUCCESS']);
                        self.L.log("updateRecords","records updated succesfully for id",id)
                        return setTimeout(()=>next(error),updateDelay);
                    }, 'DIGITAL_REMINDER_MASTER', query, []);
                   
                } else {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:UPDATE_NBFD',
                        'STATUS:ERROR',
                        'REASON:RECORD_NOT_PUBLISHABLE']);
                    L.error("","Record Publish check failed",JSON.stringify(record))
                    return next()
                }


               

            },
        function(error){
            if(error){
                self.L.critical("","Error in processing records",error)
            }
            return done()
        })
    
    }

    checkIfRecordHasToBePublished(currentRecord) {
        let self = this,
            noOfDaysToStopPublishing = 180,
            paymentDate = _.get(currentRecord, 'payment_date', null),
            createdDate = _.get(currentRecord, 'created_at', null);
        
        if (!paymentDate && MOMENT().diff(MOMENT(createdDate), 'days') > noOfDaysToStopPublishing) {
            L.log('publisher::_processRecords', 'paymentDate is null or created at date reached threshhold', _.get(currentRecord, 'id'));
            return false;
        }
        if (paymentDate && MOMENT().diff(MOMENT(paymentDate), 'days') > noOfDaysToStopPublishing) {
            L.log('publisher::_processRecords', 'Threshold reached for publishing the data for id ', _.get(currentRecord, 'id'));
            return false;
        }
        return true;
        
    }
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-s, --startingID <value>', 'startingID', Number)
            .parse(process.argv);

        startup.init({
        }, function (err, options) {
            if (err) {
                L.critical(' failed to load', err);
                process.exit(1);
            }
            try{
            let script = new UpdateNBFD({...options,startingID:commander.startingID});
            script.start(function (err) {
                setTimeout(function () {
                    if (err) {
                        console.log(err);
                        console.log('FAILURE');
                        console.timeEnd('Execution Time');
                        process.exit(1);
                    }
                    console.log('SUCCESS');
                    console.timeEnd('Execution Time');
                    process.exit(0);
                }, 1000);
            }, commander);
        }catch(err){
            console.log(err)
        }
        });
    }
})();

