const request = require('request');
const moment = require('moment');
const async = require('async');
import startup from '../lib/startup'

let OPTIONS = null;

function getDataFromES(options, dateNode, cb) {
    let requestInfo = {
        uri: 'http://internal-digital-searchstats-alb-2006557837.ap-south-1.elb.amazonaws.com/recharge_mis_*_202*/_search',
        method: 'POST',
        json: {
            "_source": [
                "userData_recharge_number",
                "customerInfo_customer_id",
                "catalogProductID",
                "productInfo_operator",
                "userData_amount",
                "productInfo_paytype",
                "productInfo_service",
                "productInfo_circle",
                "customerInfo_customer_phone",
                "inStatusMap_transactionStatus",
                "timestamps_init"
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "productInfo_operator": options.operator
                            }
                        },
                        {
                            "term": {
                                "inStatusMap_transactionStatus": "SUCCESS"
                            }
                        },
                        {
                            "range": {
                                "timestamps_init": {
                                    "gte": moment(dateNode[0]).unix(),
                                    "lt": moment(dateNode[1]).unix()
                                }
                            }
                        }

                    ]
                }
            },
            "size": 100000,
            "sort": [
                { "timestamps_init": "asc" }
            ]
        }
    };

    request(requestInfo, function (err, res, body) {
        if (err) {
            return cb(err);
        }
        let data = [];
        try {
            if (body) {
                console.error(`ES: ${body.hits.total}, data: ${body.hits.hits.length}, dates: ${dateNode}`);
                body.hits.hits.forEach(function (hit) {
                    let n = hit._source;
                    console.log("status-----",n.inStatusMap_transactionStatus)
                    let paymentDate = moment(n.timestamps_init);
                    let row = {};
                    row.userData_recharge_number = n.userData_recharge_number;
                    row.customerInfo_customer_id = n.customerInfo_customer_id;
                    row.catalogProductID = n.catalogProductID;
                    row.productInfo_operator = n.productInfo_operator;
                    row.amount = n.userData_amount;
                    row.productInfo_paytype = n.productInfo_paytype;
                    row.productInfo_service = n.productInfo_service;
                    row.productInfo_circle = n.productInfo_circle;
                    row.customerInfo_customer_phone = n.customerInfo_customer_phone;
                    data.push(row);
                });
            }
        } catch (e) {
            return cb(e);
        }
        return cb(null, data);
    });
}

function insertDataInDB(options, data, cb) {
    async.eachLimit(data, 50, function (row, cb) {
        setImmediate(function () {
            let sql = `insert into ${options.tableName} \
        (recharge_number, customer_id, product_id, operator, amount, paytype, service, circle, customer_mobile) \
        values (?) ON DUPLICATE KEY UPDATE customer_id=VALUES(customer_id), product_id=VALUES(product_id), customer_mobile=VALUES(customer_mobile), amount=VALUES(amount)`;
            let params = [
                row.userData_recharge_number,
                row.customerInfo_customer_id,
                row.catalogProductID,
                row.productInfo_operator,
                row.amount,
                row.productInfo_paytype,
                row.productInfo_service,
                row.productInfo_circle,
                row.customerInfo_customer_phone
            ];
            OPTIONS.dbInstance.exec(function (err, data) {
                if (err) {
                    return cb(err);
                }
                return cb(null, data);
            }, 'OPERATOR_SYNC', sql, [params]);
        });
    }, cb);
}


function executeSingle(options, dateNode, cb) {
    getDataFromES(options, dateNode, function (err, data) {
        if (err) {
            return cb(err);
        }
        insertDataInDB(options, data, function (err) {
            return cb(err, data);
        });
    });
}

function executeRecursive(options, cb) {
    let FROM = moment(options.fromDate);
    let TO = moment(options.toDate);
    let dates = [];

    for (let temp = FROM; temp.isBefore(TO);) {
        let from = temp.format('YYYY-MM-DD HH:mm:ss');
        let to = temp.add(options.interval, 'minutes').format('YYYY-MM-DD HH:mm:ss');
        dates.push([from, to]);
    }
    async.eachLimit(dates, options.esParallelHits, function (dateNode, cb) {
        setTimeout(function () {
            executeSingle(options, dateNode, cb);
        }, 50);
    }, cb);
}

function executeFlow(options, cb) {
    executeRecursive(options, function (err) {
        if (err) {
            return cb(err);
        }
        return cb();
    });
}

function main() {
    startup.init({
        exclude: {
            cvr: true,
            mongoDb: true,
            ruleEngine: true,
            activePidLib: true,
            dynamicConfig: true
        }
    }, function (err, options) {
        if (err) {
            return cb(err);
        }
        OPTIONS = options;
        executeFlow({
            tableName: 'dth_customer_info',
            operator: ['dishtv', 'd2h (formerly videocon d2h)'], //  need to change on stag and prod
            fromDate: "2021-01-01", // Date.parse("2020-03-01") / 1000,
            toDate: "2021-02-28", // Date.parse("2020-06-01") / 1000,
            interval: 60, // minutes
            size: 1000,
            esParallelHits: 5,
            getNextBillFetchDate: function (paymentDate) {
                return paymentDate
                    .add(moment().diff(paymentDate, 'month', true), 'month')
                    .add(20, 'days').format("YYYY-MM-DD HH:mm:ss");
            }
        }, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    process.exit(1);
                }
                process.exit(0);
            }, 1000);
        })
    });
}

(function () {
    if (require.main === module) {
        main();
    }
})();

