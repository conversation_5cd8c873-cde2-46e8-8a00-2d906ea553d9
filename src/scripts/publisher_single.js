import L from 'lgr'
import _ from 'lodash'
import Helper from '../lib/helper'
import startup from '../lib/startup'
import Publisher from '../services/publisher'
import LatencyProvider from '../services/latencyProvider'
import async from 'async'

function getRecordFromId(id, tableName, dbInstance, cb) {
    let query = `select * from ${tableName} where id in (${id})`;

    <PERSON>.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecordFromRechargeNumber(recharge_number, tableName, dbInstance, cb) {
    let query = `select * from ${tableName} where recharge_number in ("${recharge_number.join('","')}")`;

    <PERSON>.verbose(`Executing: ${query}`);
    dbInstance.exec(function (err, data) {
        if (err) {
            return cb(err);
        }
        return cb(null, data);
    }, 'DIGITAL_REMINDER_SLAVE', query, []);
}

function getRecord(opts, tableName, dbInstance, cb) {
    if (opts.recharge_number && opts.recharge_number.length) {
        getRecordFromRechargeNumber(opts.recharge_number, tableName, dbInstance, cb);
    } else if (opts.id && opts.id.length) {
        getRecordFromId(opts.id, tableName, dbInstance, cb);
    } else {
        return cb(null, []);
    }
}

function runForAllRecord(opts, tableName, options, cb) {
    let publisher = new Publisher({
        ...options,
        tableName: tableName,
        latencyProvider: new LatencyProvider(options),
        operator: opts.operator
    });
    publisher.billSubscriber._configureKafkaPublisher(function (err) {
        if (err) {
            return cb(err);
        }
        L.verbose('fetching record');
        getRecord(opts, tableName, options.dbInstance, function (err, records) {
            if (err) {
                return cb(err);
            }
            L.verbose(`Total records: ${records.length}`);
            async.eachSeries(records, function (currentRecord, cb) {
                L.verbose('Running publisher for record: ' + JSON.stringify(currentRecord));
                publisher._processRecords(currentRecord, function (err) {
                    if (err) {
                        return cb(err);
                    }
                    return cb();
                });
            }, cb);
        });
    });
}

function execute(opts, cb) {
    L.setLevel('verbose');

    if (!opts.operator) {
        return cb(new Error('operator is mandatory'));
    }
    if ((!opts.recharge_number || !opts.recharge_number.length) && (!opts.id || !opts.id.length)) {
        return cb(new Error('please provide recharge_number or id'));
    }

    startup.init({}, function (err, options) {
        if (err) {
            return cb(err);
        }

        let tableName = _.get(options.config, ['OPERATOR_TABLE_REGISTRY', opts.operator], null);
        if (!tableName) {
            return cb(new Error('Operator table not found'));
        }

        runForAllRecord(opts, tableName, options, cb);
    });
}


(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-i, --id <value>', 'id', Helper.num_list)
            .option('-r, --recharge_number <value>', 'recharge_number', Helper.list)
            .option('-t, --operator <value>', 'operator', String)
            .parse(process.argv);

        execute(commander, function (err) {
            setTimeout(function () {
                if (err) {
                    console.log(err);
                    console.log('FAILURE');
                    console.timeEnd('Execution Time');
                    process.exit(1);
                }
                console.log('SUCCESS');
                console.timeEnd('Execution Time');
                process.exit(0);
            }, 1000);
        });
    }
})();

// NODE_ENV=staging node dist/scripts/publisher_single.js --recharge_number '6889211000' --operator 'uttar haryana bijli vitran nigam(uhbvn)'
