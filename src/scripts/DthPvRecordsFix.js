const INFRAUTILS = require('infra-utils');
const _ = require("lodash");
const path = require("path");
const FS = require("fs");
const csv = require('fast-csv');
const request = require('request-promise');
const moment = require('moment');


let oid = {};

/**
 * final oid value after querying from ES would be 
 * oid = {
 *  "id": {
 *      recharge_number: customer_id, // from mongo
 *      'orderId' = order_id // from ES
 *  }
 * }
 * 
 * ex: 
 * { '5e6c94919bcce3604e6b6eff': {},
  '6023a0eee09d12174930b074': { '9041322260': 1000005553, orderId: 100072526801 },
  '6023a3c4e09d12174930be4e': { '15-20-0000300-00': 1000910480, orderId: 100072541709 },
  '6023a4f7e09d12174930c457': { '15-20-0000299-00': 1000910480, orderId: 100072541713 },
  '6023a929e09d12174930e160': { '1234567891231': 1000009814, orderId: 100072489026 },
  '6023abbce09d12174930edfb': { '6890002000': 109 },
  '6023ac30e09d12174930f02b': { '9917001019': 109 },
  '6023ad87e09d12174930f716': { '9917001020': 109 },
  '6024d3fbe09d121749368694': { 'F3662-F3981': 11065108, orderId: 100072546825 },
  '6024d3fbe09d12174936869d': { '7777777777': 1000151666 } }
 * 
 */

let noRecords = 0;
let orderIdMissing = 0
let updatedRecords = 0
let orderIdSame = 0;
let orderIdDifferent = 0;

// prod
// let slave = {
//     username      : '',
//     password      : '',
//     host          : '***********',
//     replicaHostStr: '***********:27017',
//     replicas      : ['***********'],
//     options       : 'replicaSet=rs0&w=0&readPreference=nearest',
//     db            : 'inUsers',
//     poolSize      : 5,
//     port          : 27017
// }

// prod
// let master = {
//     username      : '',
//     password      : '',
//     host          : '**********',
//     replicaHostStr: '***********:27017,***********:27017',
//     replicas      : ['***********','***********'],
//     options       : 'replicaSet=rs0&w=0&readPreference=nearest',
//     db            : 'inUsers',
//     poolSize      : 5,
//     port          : 27017
// }

//dev
let slave = {
    username      : '',
    password      : '',
    host          : 'localhost',
    replicaHost   : 'localhost',
    replicaHostStr: 'localhost:27017',
    replicas      : ['localhost'],
    options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
    db            : 'inUsers',
    poolSize      : 5,
    port          : 27017
}

// development
let master = {
    username      : '',
    password      : '',
    host          : 'localhost',
    replicaHost   : 'localhost',
    replicaHostStr: 'localhost:27017',
    replicas      : ['localhost'],
    options       : 'replicaSet=localReplica&w=0&readPreference=nearest',
    db            : 'inUsers',
    poolSize      : 5,
    port          : 27017
}

//staging
// let master = {
//     username      : '',
//     password      : '',
//     host          : '***********',
//     replicaHostStr: '**********:27017',
//     replicas      : ['**********'],
//     options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
//     db            : 'inUsers',
//     poolSize      : 5,
//     port          : 27017
// }

//staging
// let slave = {
//     username      : '',
//     password      : '',
//     host          : '***********',
//     replicaHostStr: '**********:27017',
//     replicas      : ['**********'],
//     options       : 'replicaSet=rs_0&w=0&readPreference=nearest',
//     db            : 'inUsers',
//     poolSize      : 5,
//     port          : 27017
// }

const readCsv = () => {
    FS.createReadStream(path.resolve(__dirname, '', 'data.csv'))
    .pipe(csv.parse())
    .on('error', error => console.error(error))
    .on('data', row => {
        let oidValue = JSON.parse(Object.values(row));
        oid[oidValue.$oid] = {}
    })
    .on('end', async rowCount => {
        console.log(`Parsed ${rowCount} rows`)
        await readMongodata();
        await readEsData();
        await updateMongoData();
    });
}

async function getDataFromES(customer_id, recharge_number, operators) {
    console.log("coming in to make call for customerId: ", customer_id)
    let fromDate = moment("2020-09-04").format('YYYY-MM-DD HH:mm:ss');
    let toDate = moment("2021-03-31").format('YYYY-MM-DD HH:mm:ss');
    if(!recharge_number || !customer_id) {
        return null
    }
    let requestInfo = {
        uri: 'http://rechargees-staging.nonprod.paytmdgt.io:9200/recharge_mis_*_202*/_search',
        // uri: 'http://internal-digital-searchstats-alb-2006557837.ap-south-1.elb.amazonaws.com/recharge_mis_*_202*/_search',
        method: 'POST',
        json: {
            "_source": [
                "orderInfo_order_id",
                "customerInfo_customer_id",
                "userData_recharge_number",
                "timestamps_init"
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "productInfo_operator": operators
                            }
                        },
                        {
                            "term": {
                                "customerInfo_customer_id": customer_id
                            }
                        },
                        {
                            "term": {
                                "userData_recharge_number": recharge_number
                            }
                        },
                        {
                            "term": {
                                "inStatusMap_transactionStatus": "SUCCESS"
                            }
                        },
                        {
                            "range": {
                                "timestamps_init": {
                                    "gte": moment(fromDate).unix(),
                                    "lt": moment(toDate).unix()
                                }
                            }
                        }
                    ]
                }
            },
            "size": 1,
            "sort": [
                { "timestamps_init": "desc" }
            ]
        }
    };

    try {
        let body = await request(requestInfo);
        let row = {};

        if (body) {
            console.log(`ES: ${body.hits.total}, data: ${body.hits.hits.length}`);
            body.hits.hits.forEach(function (hit) {
                let n = hit._source;
                row.rechargeNumber = n.userData_recharge_number;
                row.customerId = n.customerInfo_customer_id;
                row.orderId = n.orderInfo_order_id;
            });
        }
        return row
    } catch (error) {
        console.error(`Error while querying ES for recharge number ${recharge_number}`);
        return null
    }
}

async function readMongodata() {
    console.log("coming in to read Mongo data")
    try {
        let mongoDbInstance = new INFRAUTILS.mongo(slave);

        console.log("-- making_connection---");

        await mongoDbInstance.connect(() => { });
        console.log("--slave connected---");

        for(let id in oid) {
            let mongoId = await mongoDbInstance.getObjectIdByString(id);
            let readQuery = {
                query: {
                    _id : mongoId 
                }
            }
            console.log("fetching records for Oid ", mongoId);
            let resp = await mongoDbInstance.fetchDataFromCollection(() => { }, 'users', readQuery);

            if(resp.length && _.get(resp, [0, 'service']) === 'dth') {
                let record = {};
                record[resp[0].recharge_number] = resp[0].customer_id;
                oid[id] = record;
                oid[id]['orderId'] = resp[0].order_id;
            } else {
                console.log("no record found ", id, " resp", resp);
                noRecords++;
            }
        }

        console.log(" No records counter", noRecords);
    } catch (error) {
        console.log("Error while reading mongo data", error);
    }
}

async function readEsData() {
    console.log("coming in to read ES data");
    try {
        let operators =  ['dishtv', 'd2h (formerly videocon d2h)']

        for(const [key, value] of Object.entries(oid)) {
            let rechargeNumber = Object.keys(value)[0];
            let customerId = value[rechargeNumber];
            let orderId = oid[key]['orderId']

            let esData = await getDataFromES(customerId, rechargeNumber, operators);
            if(esData && esData.rechargeNumber == rechargeNumber && esData.customerId == customerId && esData.orderId){
                if(esData.orderId !== orderId) {
                    oid[key]['orderId'] = esData.orderId;
                    console.log("setting new value for id", key, esData.orderId);
                    orderIdDifferent++;
                } else {
                    console.log("deleting key", key);
                    delete oid[key];
                    orderIdSame++
                }
            } else {
                console.log("could not add order Ids for esdata: ", JSON.stringify(esData));
                oid[key]['orderId'] = '';
                orderIdMissing++
            }
        }
        //console.log(oid)
        console.log("could not set orderIds for:", orderIdMissing);
        console.log("Order IDs same :", orderIdSame);
        console.log("Order IDs different:", orderIdDifferent)
    } catch (error) {
        console.log("Error while making ES queries", error);
    }
    return;
}

async function updateMongoData() {
    console.log("coming in to update Mongo data")
    try {

        let masterMongoDbInstance = new INFRAUTILS.mongo(master);

        console.log("-- making_connection---");

        await masterMongoDbInstance.connect(() => { });
        console.log("--master connected---");

        let mongoUpdate = [];
        let upsert = 0;

        for(let id of Object.keys(oid)) {
            let orderId = oid[id]['orderId'];
            if(!orderId || orderId === undefined) {
                console.log(`no order id found for record ${id} with values: ${JSON.stringify(oid[id])}`);
                orderId = '';
            }
            let mongoId = await masterMongoDbInstance.getObjectIdByString(id);
            let mongoObj = {};
            let set = {};
            let query = {};

            _.set(set,'order_id', orderId);
            _.set(query,'_id', mongoId);

            _.set(mongoObj, 'set', set);
            _.set(mongoObj, 'filter', query);
            mongoUpdate.push(mongoObj);
            updatedRecords++;
            console.log(`query for ID : ${id}, and details ${JSON.stringify(oid[id])} : ${JSON.stringify(mongoObj)}\n`);

            // running in batches
            if(mongoUpdate.length > 50) {
                await masterMongoDbInstance.batchUpdateQuery((error, response)=>{
                    if (error) {
                        console.log("error while updating", JSON.stringify(error))
                    }
                }, 'users', mongoUpdate, upsert);
                mongoUpdate = [];
            }
        }

        if(mongoUpdate.length) {
            await masterMongoDbInstance.batchUpdateQuery((error, response)=>{
                if (error) {
                    console.log("error while updating", JSON.stringify(error))
                }
            }, 'users', mongoUpdate, upsert);
        }
        
        console.log(`Mongo records sent for updation -> ${updatedRecords}`)
        process.exit(0);
    } catch (error) {
        console.log("Error while updating mongo --", error);
        process.exit(1);
    }

}

const start = async () => {
    await readCsv();
}

start();
