import L from 'lgr';
import _ from 'lodash';
import ASYNC from 'async';
import { parse } from 'fast-csv';
import FS from 'fs';
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator';
import SQLWRAP from 'sqlwrap';
import MOMENT from 'moment';
import REQUEST from 'request';
import throttledRequest from 'throttled-request';
// If required load config from startup.init

let mnpURL =  "https://digitalapiproxy.paytm.com/v1/mobile/getopcirclebyrange?number=";
let recordNumber = 0;
let pidMap = {
    314: 232,
    316: 234,
    278: 236,
    280: 238,
    318: 240,
    282: 242,
    284: 244,
    286: 246,
    288: 248,
    290: 250,
    292: 252,
    294: 254,
    296: 256,
    320: 258,
    298: 260,
    300: 262,
    322: 264,
    302: 266,
    304: 268,
    306: 270,
    308: 272,
    310: 274,
    312: 276,
};

class VILCSVLoader {
    constructor() {
        this.L = L;
        this.config = config;
        this.csvRecords = [];
        this.dbInstance = new SQLWRAP(config.SQLWRAP);
        this.tableName = 'bills_vodafone';
        this.paytype = 'postpaid';
        this.operator = "vodafone";
        this.service = 'mobile';
        this.startLine = 0;
        this.CSV_PARSE_BATCH = 50;
        this.tps = 80;
        this.throttledRequest = new throttledRequest(REQUEST);
        this.throttledRequest.configure({
            requests: this.tps,
            milliseconds: 1000
        });
    }

    start(cb, opts) {
        let self = this;

        if (!opts.path) {
            return cb(new Error(`Invalid path ${opts.path}`));
        }

        if (!opts.durationToStartBillFetch) {
            return cb(new Error(`Please enter nbfd with -d flag`));
        }

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }

        if (opts.startLine && opts.startLine >= 0 ) {
            self.startLine = opts.startLine;
        }

        self.path = _.get(opts, 'path', null);
        self.durationToStartBillFetch = _.get(opts, 'durationToStartBillFetch', 1);

        self.L.error('start::', ' Starting from line number: ' + self.startLine + ' nbfd set to: ' + MOMENT().add(self.durationToStartBillFetch, 'days').format('YYYY-MM-DD 00:00:00'));
        ASYNC.waterfall([
            next => {
                self.L.error('start', 'Going to parse CSV at path:', opts.path);
                return self.processCSV(next, opts.path);
            }
        ], function (error) {
            if (error) {
                self.L.critical('VILCSVLoader', 'Error - ', error);
            }
            return cb(error);
        });
    }


    processCSV(done, filePath) {
        let self = this;

        if (!FS.existsSync(filePath)) {
            return done(`Invalid path: ${filePath}`);
        }

        let stream = FS.createReadStream(filePath)
            .pipe(parse({ ltrim: true, rtrim: true, headers: true, ignoreEmpty: true }))
            .on('data', data => {
                if (recordNumber >= self.startLine) {
                        data.traceKey = `customerId:${_.get(data, 'a.customer_id')}_rechargeNumber:${_.get(data, 'a.mobile_number')}`;
                        if (self.csvRecords.length < self.CSV_PARSE_BATCH) {
                            self.csvRecords.push(data);
                        } else {
                            self.csvRecords.push(data);
                            stream.pause();
                            self.processRecords(() => {
                                stream.resume();
                            });
                        }
            }
                recordNumber++;
            })
            .on('end', rowCount => {

                self.processRecords(() => {
                    self.L.error(`${rowCount} records processed succesfully !!`);
                    return done();
                });
                
            })
            .on('error', error => {
                self.L.critical("processCSV", "Error", error);
                done(error);
            });
    }

    processRecords(done) {
        let self = this;
        ASYNC.each(self.csvRecords, ASYNC.ensureAsync(self._processRecord.bind(self)), function () {
            self.csvRecords = [];
            return done();
        });
    }

    _processRecord(record, done) {
        let self = this;
        if (self.validateRecord(record)) {
            self.getProductId(record, (err, dbRecord) => {
                if (!err && dbRecord && _.get(dbRecord, 'product_id', null) && _.get(dbRecord, 'recharge_number', null) && _.get(dbRecord, 'customer_id', null)) {
                    _.extend(dbRecord, {
                        paytype: self.paytype,
                        service: self.service,
                        status: 0,
                        retry_count: 0,
                        notification_status: 1,
                        service_id: 0,
                        next_bill_fetch_date: MOMENT().add(self.durationToStartBillFetch, 'days').format('YYYY-MM-DD 00:00:00')
                    });
                    self.insertCSVRecord(() => {
                        return done();
                    }, self.tableName, dbRecord);
                } else {
                    // error logging done in getproductId function
                    done();
                }
            });
        } else {
            self.L.critical('_processRecord::', 'Invalid record ', JSON.stringify(record));
            done();
        }     
    }

    getProductId(record, cb) {
        //call mnp API and return 
        let self = this;
        let mnpURI = mnpURL + _.get(record, 'a.mobile_number', null);
        let dbRecord = {};
        let apiOpts = {
            "uri"       : mnpURI,
            "method"    : "GET"  
        };
        self.throttledRequest(apiOpts, (error, response, body) => {
            try {
                if (!error && body && typeof body === 'string') {
                    body = JSON.parse(body);
                }

                if (error) {
                    self.L.critical('VIL_Parser::getProductId', 'Getting error from MNP API:', record.traceKey + ' ', error);
                    return cb(error);
                } else if (_.isEmpty(body)) {
                    self.L.critical('VIL_Parser::getProductId', 'MNP API response body is empty for ', record.traceKey);
                    return cb(`No productId returned for ${record.traceKey}`);
                } else if (body && _.get(body, 'product_id', null)) {
                    let validOperators = ['vodafone','idea'];
                    //should we keep idea also in vodafone table, and operator name same?
                    let product_id;
                    let operator = _.toLower(_.get(body, 'Operator'), '');
                   
                    if (['idea'].indexOf(operator) > -1) {
                        product_id = _.get(pidMap, _.get(body, 'product_id'), null);
                    } else {
                        product_id= _.get(body, 'product_id');
                    }

                    if (product_id && validOperators.indexOf(operator) > -1 && _.get(body, 'postpaid', false)) {
                        
                        dbRecord = {
                            'operator': self.operator,
                            'product_id': product_id,
                            'circle': _.get(body, 'Circle', ''),
                            'customer_mobile': _.get(record, 'a.mobile_number'),
                            'recharge_number': _.get(record, 'a.mobile_number'),
                            'customer_id': _.get(record, 'a.customer_id')
                        };
                        return cb(null, dbRecord);
                    } else {
                        self.L.log(`Invalid Record,${_.get(record, 'a.customer_id')},${_.get(record, 'a.mobile_number')},${_.get(record, 'b.circle')},${_.get(record, 'operator')},${_.get(body, 'product_id')},${_.get(body, 'Operator')},${_.get(body, 'postpaid')}`);
                        return cb(`False MNP API response ${record.traceKey}`);
                    }
                } else {
                    self.L.log(`Invalid Response,${_.get(record, 'a.customer_id')},${_.get(record, 'a.mobile_number')},${_.get(record, 'b.circle')},${_.get(record, 'operator')},${_.get(body, 'product_id')},${_.get(body, 'Operator')},${_.get(body, 'postpaid')}`);
                    return cb('Invalid MNP API response');
                }
            }
            catch (err) {
                self.L.critical('VIL_Parser::getProductId', 'Error in MNP API response', record.traceKey, err);
                return cb('Error in parsing json.');
            }
        });
    }

    validateRecord(record) {
        let mandatoryParams = ['a.customer_id', 'a.mobile_number'];
        let validOperators = ['vodafone','idea'];
        for (let index in mandatoryParams) {
            let param = mandatoryParams[index];
            if (!_.get(record, param, null)) return false;
            if (param == 'a.mobile_number' && !VALIDATOR.isMobilePhone(_.get(record, param, null), 'en-IN')) return false;
        }
        if (validOperators.indexOf(_.toLower(_.get(record, 'operator', ''))) < 0) {
            return false;
        }

        return true;
    }

    insertCSVRecord(cb, tableName, params) {
        let 
            self=this;
            
        let query = `INSERT INTO ${tableName} (customer_id,recharge_number,product_id,operator,next_bill_fetch_date,
                service,paytype,customer_mobile,retry_count,status, notification_status, service_id)                
                VALUES ?`,
            queryParams = [[[
                params.customer_id,
                params.recharge_number,
                params.product_id,
                params.operator,
                params.next_bill_fetch_date,
                _.toLower(params.service),
                params.paytype,
                params.customer_mobile,
                params.retry_count,
                params.status,
                _.get(params, 'notification_status',0),
                _.get(params, 'service_id',0)
              ]]];

        L.verbose('insertCSVRecord-insert',self.dbInstance.format(query,queryParams));

        self.dbInstance.exec(function (err, data) {

            if(err && err.code && err.code == 'ER_DUP_ENTRY') {
                // Looks like record already exists...skipping it...
                self.L.error('insertCSVRecord', 'Record already exists for cust_id' + params.customer_id + ', recharge_number ' + params.recharge_number);
                return cb('ER_DUP_ENTRY');
            }
            else if(err || !(data)) {
                self.L.critical('insertCSVRecord-insert', `error occurred while inserting data from DB: for ${params.customer_id}::${params.recharge_number}`, err);
                return cb(err);
            } else {
                self.L.error('insertCSVRecord:: ', 'Record Successfully inserted for ', JSON.stringify(params));
                return cb(err,data);
            }
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-r, --path <value>', 'path', String)
            .option('-d, --durationToStartBillFetch <value>', 'durationToStartBillFetch', Number)
            .option('-s, --startLine <value>', 'startLine', Number)
            .parse(process.argv);

        setInterval(() => {
            L.error('VILCSVLoader::', ' Total records processed till now: ', recordNumber);
        }, 60000);

        let script = new VILCSVLoader();
        script.start(function (err) {
            setTimeout(function () {
                if (err) {
                    console.error(err);
                    console.error('FAILURE');
                    console.timeEnd('Execution Time');
                    L.error('Process Finished with error::', recordNumber);
                    process.exit(1);
                }
                console.error('SUCCESS');
                console.timeEnd('Execution Time');
                L.error('Process Finished Successfully::', recordNumber);
                process.exit(0);
            }, 1000);
        }, commander);
    }
})();

// NODE_ENV=production node dist/scripts/vil_csv_loader.js  -r '/path/to/file/vil.csv' -d 1 -s 1