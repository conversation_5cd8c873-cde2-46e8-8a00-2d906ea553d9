import L from 'lgr'
import _ from 'lodash'
import ASYNC from 'async'
import { parse } from 'fast-csv'
import FS from 'fs'
import VALIDATOR from 'validator'
import models from '../models'
import SQLWRAP from 'sqlwrap'
import MOMENT from 'moment'
import startup from '../lib/startup'

class AirtelCSVLoader {
    constructor(options) {
        this.L = L;
        this.config = options.config;
        this.csvRecords = [];
        this.billsModel = new models.Bills({
            L: this.L,
            config: this.config,
            dbInstance: new SQLWRAP(this.config.SQLWRAP)
        });
        this.tableName = 'bills_airtelprepaid';
        this.operator = 'airtel';
        this.paytype = 'prepaid';
        this.service = 'mobile';
        this.CLEAN_RECORDS_BATCH = 500;
        this.CSV_PARSE_BATCH = 50;
    }

    start(cb, opts) {
        let self = this;

        if (!opts.path) {
            return cb(new Error(`Invalid path ${opts.path}`));
        }

        if (opts.verbose) {
            self.L.setLevel('verbose');
        }

        self.path = _.get(opts, 'path', null);
        self.durationToStartBillFetch = _.get(opts, 'durationToStartBillFetch', 1);

        ASYNC.waterfall([
            next => {
                if (opts.clean_up == 1) {
                    self.L.log('start', 'Cleaning DB...');
                    return self.cleanRecords(next);
                } else {
                    return next(null);
                }
            },
            next => {
                self.L.log('start', 'Going to parse CSV at path:', opts.path);
                return self.processCSV(next, opts.path);
            }
        ], function (error) {
            if (error) {
                self.L.error('AirtelCSVLoader', 'Error - ', error);
            }
            return cb(error);
        });
    }

    /**
     * Mark all records status = 13 // TODO
     * @param {*} done 
     */
    cleanRecords(done) {
        let self = this;

        self.billsModel.findActiveRecords(function callback(error, records) {
            console.log("Records...", records);
            if (records && records.length > 0 && records[0].ids) {
                self.billsModel.markRecordsInactive(function () {
                    self.billsModel.findActiveRecords(callback, self.tableName, self.CLEAN_RECORDS_BATCH);
                }, self.tableName, records[0].ids);
            } else if (error) {
                self.billsModel.findActiveRecords(callback, self.tableName, self.CLEAN_RECORDS_BATCH);
            } else {
                self.L.log('cleanRecords', 'Cleanup completed !!');
                return done();
            }
        }, self.tableName, self.CLEAN_RECORDS_BATCH);
    }

    processCSV(done, filePath) {
        let self = this;

        if (!FS.existsSync(filePath)) {
            return done(`Invalid path: ${filePath}`);
        }

        let stream = FS.createReadStream(filePath)
            .pipe(parse({ ltrim: true, rtrim: true, headers: true, ignoreEmpty: true }))
            .on('data', data => {
                data.traceKey = `customerId:${_.get(data, 'customer_id')}_rechargeNumebr:${_.get(data, 'recharge_number')}_productId:${_.get(data, 'product_id')}`;

                if (self.csvRecords.length < self.CSV_PARSE_BATCH) {
                    self.csvRecords.push(data)
                } else {
                    self.csvRecords.push(data)
                    stream.pause();
                    self.processRecords(() => {
                        stream.resume();
                    });
                }
            })
            .on('end', rowCount => {

                self.processRecords(() => {
                    self.L.log(`${rowCount} records processed succesfully !!`);
                    return done();
                });

            })
            .on('error', error => {
                self.L.error("processCSV", "Error", error);
            });
    }

    processRecords(done) {
        let self = this;

        ASYNC.each(self.csvRecords, ASYNC.ensureAsync(self._processRecord.bind(self)), function () {
            self.csvRecords = [];
            return done();
        });
    }

    _processRecord(record, done) {
        let self = this;

        self.validateRecord(function (error) {
            if (error) {
                self.L.error('_processRecord', `Record Validation failed for ${record.traceKey}, Error ${error}`);
                return done(); // Not passing error, else it will impact all processing records
            } else {
                _.extend(record, {
                    operator: self.operator,
                    paytype: self.paytype,
                    service: self.service,
                    status: 0,
                    retry_count: 0,
                    reason: null,
                    extra: null,
                    published_date: null,
                    notification_status: 1,
                    customerOtherInfo: null,
                    is_automatic: 0,
                    due_date: null,
                    bill_fetch_date: null,
                    published_date: null,
                    next_bill_fetch_date: MOMENT().add(self.durationToStartBillFetch, 'days').format('YYYY-MM-DD 00:00:00')
                })

                self.L.log('_processRecord', `Going to insert/update record - ${record.traceKey}`);
                self.billsModel.insertCSVRecord(() => {
                    return done();
                }, self.tableName, record);
            }
        }, record);
    }

    validateRecord(done, record) {
        let mandatoryParams = ['customer_id', 'recharge_number', 'product_id', 'customer_mobile'];

        for (let index in mandatoryParams) {
            let param = mandatoryParams[index];
            if (!_.get(record, param, null)) return done(`Mandatory param ${param} missing`);
            if (param == 'recharge_number' && !VALIDATOR.isMobilePhone(_.get(record, param, null), 'en-IN')) return done(`Invalid Recharge Number ${_.get(record, param, null)} passed`);
            if (param == 'customer_mobile' && !VALIDATOR.isMobilePhone(_.get(record, param, null), 'en-IN')) return done(`Invalid Mobile Number ${_.get(record, param, null)} passed`);
            //if (param == 'customer_email' && !VALIDATOR.isEmail(_.get(record, param, null))) return done(`Invalid Email ${_.get(record, param, null)} passed`);
        }

        return done(null);
    }
}

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-c, --clean_up <value>', 'clean_up', Number)
            .option('-r, --path <value>', 'path', String)
            .option('-d, --durationToStartBillFetch <value>', 'durationToStartBillFetch', Number)
            .parse(process.argv);

        startup.init({
            airtelPrepaidDataLoader: true
        }, function (err, options) {
            if (err) {
                L.critical('airtel_csv_loader failed to load', err);
                process.exit(1);
            }
            let script = new AirtelCSVLoader(options);
            script.start(function (err) {
                setTimeout(function () {
                    if (err) {
                        console.log(err);
                        console.log('FAILURE');
                        console.timeEnd('Execution Time');
                        process.exit(1);
                    }
                    console.log('SUCCESS');
                    console.timeEnd('Execution Time');
                    process.exit(0);
                }, 1000);
            }, commander);
        });
    }
})();

// NODE_ENV=production node dist/scripts/airtel_csv_loader.js --clean_up 1 --path '/path/to/file/airtel.csv' -v