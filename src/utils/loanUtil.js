import _ from 'lodash'
import billsLib from '../lib/bills';

class LoanUtil {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.billsLib = new billsLib(options); 
    }
    /**
     * Function to generate and set a dummy recharge number in the params.
     */
    updateRNtoDummy(params) {
        const rechargeNumber = this.billsLib.generateDummyRechargeNumber(params);
        _.set(params, 'rechargeNumber', rechargeNumber);
        this.L.log(`loanUtil, updateRNtoDummy, rechargeNumber: ${rechargeNumber}`);
    }
}

module.exports = LoanUtil;