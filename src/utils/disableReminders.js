/* 
Utility to disable bill reminders for customers.
Parameters:
1. schema_name: Schema in which the table is present.
2. billsTables: Name of the tables on which the query has to be executed (used with LIKE clause).
3. customerIds: Customer ids for which the reminders have to be disabled.
4. ignoreTables: List of tables on which we don't have to execute the query.
5. newStatus: Integer value of the status that has to set while updation.
*/

"use strict";
// If required Load Config from startup.init

var L               =   require('lgr')
var _               =   require('lodash')
var SQLWRAP         =   require('sqlwrap')
var Q               =   require('q')
var ASYNC 	        =   require('async')

let dbInstance      =   new SQLWRAP(config.SQLWRAP),
    /* Schema in which the table is present*/
    schema_name     =   "digital_reminder",
    billsTables     =   "bills_%",
    customerIds     =   [],
    ignoreTables    =   ['bills_status','bills_operator'],
    newStatus       =   0;

/*
 *    LOGIC:
 *    Get the table names on which we have to execute the queries.
 *    Filter the table names which have to be skipped.
 *    Execute the queries on the table one after the other.
 */

var disableReminders = {
    init: function () {
        if(!customerIds || customerIds.length <= 0) {
            L.error('No customer ids provided.');
            return;
        }

        var self = this;
        L.setLevel('verbose');

        Q(undefined).then(function () {
            return self.getAllTableNames();
        }).then(function (tableNames) {
            return self.processRecords (tableNames);
        }).then(function () {
            L.log('Process Complete');
            process.exit();
        });
    },
    /*
        This method will return all the table names like <billsTables>
    */
    getAllTableNames: function () {
        var defer = Q.defer();
        var self = this;

        var query = "select table_name from information_schema.tables where table_schema = ? and table_name like ?";
        var params = [schema_name, billsTables];

        L.log("DisableReminder:GetAllTableNames " + dbInstance.format(query, params));

        dbInstance.exec(function (err, rows) {
            if (err) {
                L.error("DisableReminder:GetAllTableNames", "Error in executing query");
                defer.reject();
            } else {
                L.log('Table Names fetched');
                defer.resolve(rows);
            }
        }, "DIGITAL_REMINDER_MASTER", query, params);

        return defer.promise;
    },

    /* This method will process the functionality for the tables which we got from the <getAllTableNames> method */
    processRecords: function(tableNames) {
        var self = this;
        var defer = Q.defer();

        L.setLevel('verbose');
        if(!tableNames) {
            L.error('No tables for bills in database');
            return defer.resolve();
        } else {
            //Call the <self.disableReminderForTable> for each <tableNames> from which we have to disable the reminders
            Q(undefined).then(function() {
                var def = Q.defer();
                ASYNC.each(tableNames, self.disableReminderForTable, function (err) {
                    if(err) {                    
                        return def.reject(err);
                    } else {
                        return def.resolve();
                    }
                });
                return def.promise;
            }).then (function() {
                return defer.resolve();
            });
        return defer.promise;
        }
    }, 
    /* Disable the reminder for the particular table
     * using the customer ids that are mentioned at the start of this file.
    */
    disableReminderForTable: function(tableName, cb) {

        if(ignoreTables.indexOf(tableName.table_name) >= 0) {
            L.log('Table to be ignored ' + tableName.table_name);
            cb();
            return;
        }

        var self = this;
        var query = `update ${tableName.table_name} set notification_status = ? where customer_id in (?)`;
        var params = [newStatus, customerIds];
    
        L.log("DisableReminder:disableReminderForTable " + dbInstance.format(query, params));

        dbInstance.exec(function(err, result) {
            if(err) {
                L.error("DisableReminderForTable:: ", "error in disabling reminders for table: " + tableName);
                return defer.reject();
            } 
            if(result.affectedRows > 0) {
                L.log("Number of rows updated in table " + tableName.table_name + " " + result.affectedRows);
            }
            //return Q(undefined);
            cb();
        }, "DIGITAL_REMINDER_MASTER", query, params);
    }
}

module.exports = disableReminders;

(function () {
    if(require.main == module) {
        disableReminders.init();
    }
}())