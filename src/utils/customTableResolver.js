// utils/customerTableResolver.js

import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'

class CustomTableResolver {
  constructor(options) {
        
    this.billGenTable = 'billgen_bills_non_paytm_';
    this.billDueTable = 'billdue_bills_non_paytm_';
    this.nonPaytmBillsTable = 'bills_non_paytm';
    this.nonRuBillsTable = 'bills_non_ru_bigint';
    this.billGenNonRU = 'billgen_non_ru_';
    this.billDueNonRU = 'billdue_non_ru_';
    this.encryptedBillDueNonRU = 'enc_billdue_non_ru_';
    this.billRecentRecordsNew = 'bills_recent_records_bigint';
    this.nonPaytmRecentsTable = 'bills_recent_records';
    this.remindLaterDateEvent = 'remind_later_event';
    this.INT_MAX_RANGE = 2147483647;
    this.encryptHelper = new EncryptionDecryptioinHelper(options);
  }

  getNonRUTableName(customerId) {
    if (customerId <= this.INT_MAX_RANGE) {
      return this.nonPaytmBillsTable;
    } else {
      return this.nonRuBillsTable;
    }
  }

 getBillGenTablePrefix(customerId) {
      return this.billGenNonRU;
  }

  getBillDueTablePrefix(customerId, service = null, paytype = null, bypassEncryption = false) {
      if(bypassEncryption) {
        return this.billDueNonRU;
      }
      if(service && paytype && this.encryptHelper.isWhitelistedForCC(service, paytype, customerId)) {
          return this.encryptedBillDueNonRU;
      }
      return this.billDueNonRU;
  }

  getNewBillsRecentTable(customerId) {
    if (customerId <= this.INT_MAX_RANGE) {
      return this.nonPaytmRecentsTable;
    } else {
      return this.billRecentRecordsNew;
    }
  }
  getRemindLaterDateEventTable() {
    return this.remindLaterDateEvent;
  }

  getCustomerIDRange(customerId) {
    if (customerId <= this.INT_MAX_RANGE) {
      return "NORMAL";
    } else {
      return "BIGINT";
    }
  }
}

module.exports = CustomTableResolver;