import MOMENT from 'moment'
import _ from 'lodash'
import <PERSON><PERSON><PERSON> from 'async'
import REQUEST from 'request'
import BILLS from '../models/bills'
import PLAN_VALIDITY from '../models/planValidity'
import BILL_SUBSCRIBER from './billSubscriber'
import utility from '../lib'
import throttledRequest from 'throttled-request'
import events from 'events'
import SCHEDULE from 'node-schedule'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';

class AirtelPublisher {

    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.bills = new BILLS(options);
        this.planValidityModel = new PLAN_VALIDITY(options);
        this.dbBatchSize = 1000;
        this.servicePeriod = 2 * 60; //time in seconds
        this.tableName = 'bills_airtelprepaid'
        this.operator = 'airtel'
        this.service = 'mobile'
        this.recordsPublished = 0
        this.billSubscriber = new BILL_SUBSCRIBER(options);
        this.commonLib = new utility.commonLib(options);

        this.dry_run = _.get(options, 'dry_run', 0); // If dry_run = 1, dont alter/push in DB/kafka just log it
        this.publishedInSession = {};
        this.MAX_RETRY_COUNT = 3;
        this.billFetchIntervals = [-6, -3, -1, 0, 1, 3];
        this.lastNotificationDay = 3; // last notificationat D + 3
        this.firstBillFetchToDueDateInterval = 6; // due date = T + 6 from no -> yes
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);

        this.priority_map_query ={
            0: `SELECT * FROM ${this.tableName} WHERE status=4 and next_bill_fetch_date > ? and next_bill_fetch_date < ?  LIMIT ?`,
            1: `SELECT * FROM ${this.tableName} WHERE status=0 and next_bill_fetch_date > ? and next_bill_fetch_date < ? order by next_bill_fetch_date desc LIMIT ?`,
            2: `SELECT * FROM ${this.tableName} WHERE status in (2,3,9) and next_bill_fetch_date > ? and next_bill_fetch_date < ?  LIMIT ?`,
            3: `SELECT * FROM ${this.tableName} WHERE status=6 and next_bill_fetch_date > ? and next_bill_fetch_date < ? order by next_bill_fetch_date LIMIT ?`   
        };

        this.templates = {
            '-6': {
                SMS: null,
                EMAIL: null,
                CHAT: null,
                PUSH: 7350
            },
            '-3': {
                SMS: null,
                EMAIL: null,
                CHAT: null,
                PUSH: 7350
            },
            '-1': {
                SMS: null,
                EMAIL: null,
                CHAT: null,
                PUSH: 7351
            },
            '0': {
                SMS: 7352,
                EMAIL: null,
                CHAT: null,
                PUSH: 7392
            },
            '1': {
                SMS: null,
                EMAIL: null,
                CHAT: null,
                PUSH: 7393
            },
            '3': {
                SMS: null,
                EMAIL: null,
                CHAT: null,
                PUSH: 7398
            },
            'GENERIC': {
                SMS: null,
                EMAIL: null,
                CHAT: null,
                PUSH: 7353
            }
        };
 
        this.tps = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', `airtel_prepaid`, 'PARALLEL_BILL_FETCH_HITS'], 30);
        this.throttledRequest = new throttledRequest(REQUEST);
        this.throttledRequest.configure({
            requests: this.tps,
            milliseconds: 1000
        });

        this.eventEmitter = new events.EventEmitter();
        this.eventEmitter.setMaxListeners(this.tps * 2);
        this.fetchTokenLocked = false;
        this.apiTimeout = 2 * 60 * 1000; // 2 min timeout
        this.resetPriorityAndQueryParams();
        this.stopService = false;

        this.L.info('Publisher::', this.tableName, this.operator, 'initialising Publisher...');
    }

    /*
      Function which will start the publisher for a table
    */
    start() {
        let self = this;
        self.startDummyLogs();
        self.L.info('Publisher::', self.operator, 'starting the service loop');
        function startMainLoop() {
            self.publishedInSession = {} //intialise a fresh session object
            self.L.log('start', 'Starting reset priority Scheduler...');
            self.startPriorityScheduler();
            self._execSteps(function () {
                self.L.log('Publisher::', self.operator, 'session finished successfully, Msg Count Published to gateway service: ', Object.keys(self.publishedInSession).length);
                setTimeout(function () {
                    self.priority = self.getNextPriority();
                    startMainLoop();
                }, self.servicePeriod * 1000);  //converting period in milli seconds
            });
        }

        self.billSubscriber._configureKafkaBillFetchPublisher((error) => {
            if (error) {
                self.L.critical('Publisher :: start', 'Error while configuring Kafka Publisher.. for', self.operator, 'with error', error);
            }
            else {
                self.L.log('Publisher :: start', 'Kafka Publisher configured', self.operator, 'with error', error);
                startMainLoop();
            }
        });

        self.billSubscriber._configureCtPublisher((error) => {
            if (error) {
                self.L.critical('Publisher :: start', 'Error while configuring CT Kafka Publisher.. for', self.operator, 'with error', error);
            }
            else {
                self.L.log('Publisher :: start', 'CT Kafka Publisher configured for', self.operator, 'with error', error);
            }
        });

    }

    _execSteps(done) {
        let self = this;
        self.L.log('_execSteps::', self.operator, 'performing steps for publishing...')

        self.recordsPublished = 0;

        let totalFreshRecordsCount = 0;
        
        ASYNC.waterfall([
            next => {
                if (self.token) { // To avoid token generation continuously
                    return next();
                } else {
                    return self.fetchToken(next);
                }
            },
            next => {
                self.fetchRecords(function __keepFetchingUntilDone(err, recordsCountInLastBatch) {
                    if (err) {
                        self.L.error('_execSteps', 'Error processing fresh records')
                        //No matter what happened, don't stop at error and proceed further
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER', 'STATUS:ERROR']);
                        return next();
                    }
                    //If batch size is equal to records fetched that means there can be more records.
                    else if (recordsCountInLastBatch >= self.dbBatchSize) {
                        self.L.log('_execSteps._processFreshRecords::', self.operator, " priority ::",  self.getPriority(), 'recordsCountInLastBatch', recordsCountInLastBatch);
                        if (recordsCountInLastBatch > 0) {
                            utility._sendMetricsToDD(recordsCountInLastBatch, ['REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER', 'STATUS:PROCESSED']);
                        }
                        totalFreshRecordsCount += recordsCountInLastBatch;
                        self.fetchRecords(__keepFetchingUntilDone)
                    }
                    else { //No records left, so proceeding to next step
                        totalFreshRecordsCount += recordsCountInLastBatch
                        if (recordsCountInLastBatch > 0) {
                            utility._sendMetricsToDD(recordsCountInLastBatch, ['REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER', 'STATUS:PROCESSED']);
                        }
                        self.L.log('_execSteps._processFreshRecords::', self.operator, " priority ::",  self.getPriority(), 'total fresh records processed ', totalFreshRecordsCount);
                        return next();
                    }
                });
            }
        ], function (error) {
            if (error) {
                self.L.error('Error in _execSteps', error);
            }
            return done();
        });
    }

    /*
       Function to process records
    */
    fetchRecords(done) {
        let self = this,
            startTime = new Date().getTime();
        self.publishedInBatch = {}
        if(self.stopService) {
             self.L.log("stop fetching record due to gracefull shutdown");
            return done(null, 0);
        }
        self.L.log("fetchRecords",`Going to fetch new batch for fresh records...`);

        ASYNC.waterfall([
            //fetch records from table
            next => {
                return self.bills.fetchAirtelRecords(self.tableName, self.params, self.priority_map_query[self.getPriority()], next);
            },

            (records, next) => {

                self.L.log("fetchRecords",`Fetched ${records.length} data from DB`);

                let recharge_numbers = _.map(records, 'recharge_number');

                //let recharge_numbers_str = '"' + recharge_numbers.join('","') + '"';
                let recharge_numbers_str = [];
                for (var i = 0; i < recharge_numbers.length; i += self.tps * 5) {
                    recharge_numbers_str.push(recharge_numbers.slice(i, i + self.tps * 5));
                }

                self.validityMap = {}; // Resetting map to release previous map memory
                ASYNC.eachSeries(recharge_numbers_str, self.fetchValidity.bind(self), function (error) {
                    return next(error, records);
                })
            },

            //Publish and update the records
            (records, next) => {
                self.L.log('_processFreshRecords', `Processing ${records.length} records`);

                self._processRecordsInBatch(function () {
                    return next(null, records.length);
                }, records, 0);
            }
        ], (err, count) => {
            if (err) {
                return done(err)
            }
            let endTime = new Date().getTime();
            let executionTime = (endTime - startTime) / 1000;      //in seconds
            executionTime = Math.round(executionTime);
            self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ', count);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:AirtelPublisher", "TIME_TAKEN:" + executionTime]);
            done(null, count)
        });
    }

    _processRecordsInBatch(done, records, index) {
        let
            self = this;

        if (index >= records.length) {
            self.L.log('_processRecordsInBatch', `Records published in this iteration for ${this.tableName} ::: and for priority ::: ${self.priority} :::  ${this.recordsPublished} out of ${records.length}`);
            this.recordsPublished = 0;
            setTimeout(function () {
                done();
            }, 1 * 1000)
            return;
        }

        ASYNC.each(records.slice(index, index + records.length), self._processRecords.bind(self), function (err) {
            if (err) {
                self.L.error('_processRecordsInBatch', `error encountered while publishing records in batch ${err}`);
            }
            self._processRecordsInBatch(done, records, index + records.length);
        });
    }

    /*
       Going recursively for this function instead with Async lib's method, because we need to have a provision of halting the process for just a small moment, in cases when the queue is full
    */
    _processRecords(currentRecord, done) {

        // Replacing product_id with active PID
        let
            self = this,
            activePid = self.activePidLib.getActivePID(currentRecord.product_id);

        self.L.verbose('_processRecords', `Found active Pid ${activePid} against PID ${currentRecord.product_id}`);
        currentRecord.old_product_id = currentRecord.product_id; // Keeping track of original PID
        currentRecord.product_id = activePid;                // Replacing active PID

        currentRecord.traceKey = `Id:${currentRecord.id}_RN:${currentRecord.recharge_number}_custId:${currentRecord.customer_id}_pId:${currentRecord.product_id}`;;
        self.L.log('_processRecords', `Processing record for ${currentRecord.traceKey}`);

        // All the updated date needs to be updated in table will he held here
        let updatedRecord = _.clone(currentRecord);
        self.refereshData(updatedRecord);

        ASYNC.waterfall([
            next => {
                // check if validity exists for this number by fetching it from ES
                return self.checkValidityActivationStatus(next, updatedRecord);
            },
            (activeValidity, next) => {
                if (activeValidity === true) {
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_PAID_ON_PAYTM', 137);
                    updatedRecord.reason = `ACTIVE_VALIDITY_EXISTS`;
                    return next('Active validity exists in our system');
                }

                self.L.log('_processRecords', `Active validity do not exists in our system, now going to fetch validity from Airtel API for ${updatedRecord.traceKey}`);
                self.recordsPublished++;
                //Lets cache this, to prevent ourselves from sending same bill record (rechargeNumber:ProductId combinatiion) again to gw Service
                self.publishedInSession[`${currentRecord.recharge_number}:${currentRecord.product_id}`] = true

                // Hit Airtel API and parse response
                return self.fetchBill(next, currentRecord, updatedRecord);
            },
            (next) => {
                // Check for notification eligibility
                if (_.get(updatedRecord, 'sendNotification', false) === true) {
                    return self.sendNotification(next, updatedRecord);
                } else {
                    return next(null);
                }
            },
            (next) => {
                if (_.get(updatedRecord, 'sendNotification', false) === true) {
                    return self.publishCtEvents(next, updatedRecord);
                } else {
                    return next(null);
                }
            }
        ], (err) => {

            if (err) {
                self.L.error('_processRecords', `Error for ${currentRecord.traceKey} - ${err}`);
            }

            // Update final table data
            self.updateBillsRecord(done, updatedRecord);
        });
    }

    /**
     * Resetting record data
     * @param {*} updatedRecord 
     */
    refereshData(updatedRecord) {
        let self = this;
        updatedRecord.amount=79; // amount =0 was creating validation faluire 

        try {
            let extra = updatedRecord.extra ? JSON.parse(updatedRecord.extra) : {};
            updatedRecord.extra = {};
            for (let prevDay = 1; prevDay <= 10; prevDay++) {
                let dateKey = MOMENT().startOf('day').subtract(prevDay,'days').format('YYYY-MM-DD');
                let dateKeyValue = _.get(extra, dateKey, null);
                if (dateKeyValue != null) _.set(updatedRecord, ['extra', dateKey], dateKeyValue);
            }
            updatedRecord.customerOtherInfo = updatedRecord.customerOtherInfo ? JSON.parse(updatedRecord.customerOtherInfo) : {};
            updatedRecord.reason = '';
            updatedRecord.current_retry_count = updatedRecord.retry_count;
            updatedRecord.retry_count = 0; // To reset it to 0 in case of no error
        } catch (e) {
            self.L.error('refereshData', `Error while refreshing data for ${updatedRecord.traceKey} - error: ${e}`);
        }
    }

    /**
     * Fetch validity for all records.recharge_number
     * @param {*} done 
     * @param {*} records 
     */
    fetchValidity(recharge_numbers_str, done) {
        let self = this;

        self.planValidityModel.getValidity(function (error, data) {
            self.L.verbose('fetchValidity', data);

            if (error) {
                self.L.critical('fetchValidity', error);
            } else {
                if (data && data.length > 0) {
                    data.forEach(row => {
                        if (_.get(row, 'operator') == self.operator && _.get(row, 'service') == self.service) {
                            self.validityMap[_.get(row, 'recharge_number')] = true;
                        }
                    });
                }
            }

            self.L.verbose('fetchValidity', 'validityMap', self.validityMap);
            return done(error);
        }, recharge_numbers_str);
    }

    /**
     * Fetch latest recharge from ES for queried number
     * @param {*} done 
     * @param {*} currentRecord 
     * @param {*} updatedRecord 
     */

    checkValidityActivationStatus(done, updatedRecord) {
        let self = this,
            rechargeNumber = _.get(updatedRecord, 'recharge_number');

        if (self.validityMap[rechargeNumber] === true) {
            self.L.log('checkValidityActivationStatus', `Active validity exists for : ${_.get(updatedRecord, 'traceKey')}`);
            return done(null, true);
        } else {
            self.L.log('checkValidityActivationStatus', `Active validity do not exists for : ${_.get(updatedRecord, 'traceKey')}`);
            return done(null, false);
        }
    }


    /**
     * Take decision based on current response
     * @param {*} updatedRecord 
     * @param {*} response 
     */
    handleResponse(updatedRecord, response) {
        let self = this,
            currentDate = MOMENT().startOf('day').format('YYYY-MM-DD'),
            prevDate = MOMENT().startOf('day').add(-1, 'days').format('YYYY-MM-DD'),
            prevResponse = _.get(updatedRecord, ['extra', prevDate], null),
            extra = _.clone(_.get(updatedRecord, 'extra', null)); // Using it for checking First time Fetch

        _.set(updatedRecord, ['extra', currentDate], response);

        if (self.firstTimeFetch(extra)) {
            if (response == true) {
                // In this case we need to send generic notification and rmeove this record from base
                //updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_AFTER_GENERIC_NOTI', 132)
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4);
                updatedRecord.sendNotification = true;
                updatedRecord.templates = self.getTemplates('GENERIC');
                updatedRecord.next_bill_fetch_date = self.addDuration(_.get(self.config, ['AIRTEL_PUBLISHER_CONFIG' , 'SNOOZE_TIME' , 'DAYS'], 7));
                updatedRecord.reason = 'COND-11: Sending generic notification and snoozing it';
                updatedRecord.extra = null;
            } else if (response == false) {
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)
                updatedRecord.next_bill_fetch_date = self.addDuration(1);
                updatedRecord.reason = 'COND-12';
            } else {
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.VALIDATION_FAILED', 9)
                updatedRecord.next_bill_fetch_date = self.addDuration(1);
                updatedRecord.reason = 'COND-13';
            }
        } else if (response === true) {
            if (prevResponse === true) {
                updatedRecord.reason = 'COND-21';
                self.setNextBillFetchDateForSuccessResponse(updatedRecord);
            } else if (prevResponse === false) {
                // First time bill fetch
                updatedRecord.reason = 'COND-22';
                updatedRecord.bill_fetch_date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                updatedRecord.due_date = self.addDuration(self.firstBillFetchToDueDateInterval);
                self.setNextBillFetchDateForSuccessResponse(updatedRecord);
            } else if (updatedRecord.due_date) { // Bill already Fetched, coming back to verify it
                updatedRecord.reason = 'COND-23';
                self.setNextBillFetchDateForSuccessResponse(updatedRecord);
            } else {
                updatedRecord.reason = 'COND-24';
                self.handleErrorScenario(updatedRecord, response);
            }
        } else if (response === false) {
            if (prevResponse === true) {
                // User has done recharge, remove this record
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_PAID_ON_OTHER_PLATFORM', 135);
                updatedRecord.reason = 'COND-31 : Looks like user has done recharge !!';
            } else if (prevResponse === false) {
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
                updatedRecord.next_bill_fetch_date = self.addDuration(1);
                updatedRecord.reason = 'COND-32';
            } else {
                updatedRecord.reason = 'COND-33';
                self.handleErrorScenario(updatedRecord, response);
            }
        } else { // Error case
            if (prevResponse === true || prevResponse === false) {
                updatedRecord.reason = 'COND-41 : Airtel Bill Fetch API Error';
                updatedRecord.next_bill_fetch_date = self.addDuration(1);
            } else {
                // Error consecutive 2 days...discard this record..
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_ERR_BILL_FETCH', 136);
                updatedRecord.reason = 'COND-42: Error for 2 consecutive days';
            }
        }
    }

    addDuration(days) {
        return MOMENT().add(days, 'days').format('YYYY-MM-DD 00:00:00');
    }

    firstTimeFetch(extra) {
        let self = this,
            prevDate = MOMENT().startOf('day').add(-1, 'days').format('YYYY-MM-DD');

        // Assumption : considering first time fetch in case of extra column is NULL
        if (!extra || _.size(extra) == 0 || (_.size(extra) <= 1 && _.get(extra, prevDate, null) == 'ERROR')) {
            return true;
        } else {
            return false;
        }
    }

    handleErrorScenario(updatedRecord, response) {
        let
            self = this,
            t_2_date = MOMENT().startOf('day').subtract(2, 'days').format('YYYY-MM-DD'), // T-2 date
            t_2_Response = _.get(updatedRecord, ['extra', t_2_date], null); // Response received on T-2

        if (response === true) {
            if (t_2_Response === true) {
                updatedRecord.reason += '11';
                self.setNextBillFetchDateForSuccessResponse(updatedRecord);
            } else {
                // Remove from base and send generic notification
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_AFTER_GENERIC_NOTI_AFTER_ERR', 133);
                updatedRecord.sendNotification = true;
                updatedRecord.templates = self.getTemplates('GENERIC');
                updatedRecord.reason += '12: Missed T-1 response,Sending Generic notification and dropping it';
                updatedRecord.due_date = MOMENT().startOf('day').format('YYYY-MM-DD');
            }
        } else if (response === false) {
            if (t_2_Response === true) {
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_PAID_ON_OTHER_PLATFORM', 135);
                updatedRecord.reason += '21: Looks like user has done Recharge !!';
            } else {
                // continue processing
                updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
                updatedRecord.next_bill_fetch_date = self.addDuration(1);
                updatedRecord.reason += '22';
            }
        }
    }

    setNextBillFetchDateForSuccessResponse(updatedRecord) {
        let self = this,
            dueDate = MOMENT(updatedRecord.due_date).startOf('day'),
            currDate_dueDate_diff = MOMENT().startOf('day').diff(dueDate.startOf('day'), 'days'),
            daysToAdd = 1;

        for (let i in self.billFetchIntervals) {
            if (self.billFetchIntervals[i] > currDate_dueDate_diff) {
                daysToAdd = self.billFetchIntervals[i];
                break;
            }
        }

        if (self.billFetchIntervals.indexOf(currDate_dueDate_diff) > -1) {
            updatedRecord.sendNotification = true;
            updatedRecord.templates = self.getTemplates(currDate_dueDate_diff);
        }

        // End of bill fetch after D+2
        if (currDate_dueDate_diff >= self.lastNotificationDay) {
            updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_AFTER_LAST_NOTI', 134);
            updatedRecord.reason += '11: Sent all notifications, now removing this record';
        } else {
            updatedRecord.status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4);
            updatedRecord.next_bill_fetch_date = MOMENT(updatedRecord.due_date).add(daysToAdd, 'days').format('YYYY-MM-DD 00:00:00');
            updatedRecord.reason += '12';
        }
    }

    getTemplates(templateType) {
        let self = this;
        return {
            PUSH: _.get(self.templates, [templateType, 'PUSH'], null),
            SMS: _.get(self.templates, [templateType, 'SMS'], null),
            EMAIL: _.get(self.templates, [templateType, 'EMAIL'], null),
            CHAT: _.get(self.templates, [templateType, 'CHAT'], null)
        };
    }

    sendNotification(done, updatedRecord) {
        let
            self = this,
            payload = {
                source: "airtelBillFetch",
                notificationType: "DUEDATE",
                data: self.commonLib.mapBillsTableColumns(updatedRecord)
            };

        // Setting status = 4 because in case of T+2 notification we are updating status=13 in table, In this case next pipe line will discard this record. To avoid this we are setting status=4 explicitly
        _.set(payload, 'data.status', 4);

        if (self.dry_run == 1) {
            self.L.log('dry_run :: sendNotification :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
            return done();
        }

        self.billSubscriber.kafkaBillFetchPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {
                self.L.critical('sendNotification :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            } else {
                self.L.log('sendNotification :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                utility._sendMetricsToDD(1, [
                    'STATUS:PUBLISHED',
                    'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                    'REQUEST_TYPE:AIRTEL_PREPAID_NOTIFICATION',
                    'OPERATOR:AIRTEL_PREPAID'
                ])
            }
            return done(error);
        }, [200, 800]);
    }

    /**
     * Update Record in Table
     */
    updateBillsRecord(done, updatedRecord) {
        let self = this;

        let reason = _.get(updatedRecord, 'reason', {});
        if (reason && reason.length && reason.length > 0) {
            reason = reason.split(' ')[0];
        }
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_RESULT', `RESULT:${reason}`]);

        self.bills.updateAirtelRecord(err => {
            if (err) {
                self.L.critical('updateBillsRecord', `error occurred while updating record for ${updatedRecord.traceKey}`, err);
            }
            return done();
        }, self.tableName, {
            id: updatedRecord.id,
            status: _.get(updatedRecord, 'status', 0),
            retryCount: _.get(updatedRecord, 'retry_count', 0),
            billDueDate: updatedRecord.due_date,
            operator: updatedRecord.operator,
            service: updatedRecord.service,
            nextBillFetchDate: updatedRecord.next_bill_fetch_date,
            billFetchDate: updatedRecord.bill_fetch_date,
            extra: JSON.stringify(_.get(updatedRecord, 'extra', {})),
            reason: _.get(updatedRecord, 'reason', ''),
            publishedDate: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            customerOtherInfo: JSON.stringify(_.get(updatedRecord, 'customerOtherInfo', {})).slice(0, 200),
            traceKey: updatedRecord.traceKey
        }, self.dry_run);
    }

    /**
     * Fetch JWT Token from Airtel API
     * @param {*} done 
     */
    fetchToken(done) {
        let self = this,
            apiOpts = {
                url: 'https://zapi.airtel.in/as/airtel-oauth/v1/oauth/token',
                method: 'POST',
                timeout: self.apiTimeout,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `username=${_.get(self.config, ['AIRTEL_PUBLISHER_CONFIG' , 'FETCH_TOKEN_API' , 'username' ], '')}&password=${_.get(self.config, ['AIRTEL_PUBLISHER_CONFIG' , 'FETCH_TOKEN_API' , 'password' ], '')}&client_id=PAYTM&grant_type=access_token`  /** VAULT integration required  */
            };
        // Locking it to have singleton behaviour
        self.fetchTokenLocked = true;

        var latencyStart = new Date().getTime();
        REQUEST(apiOpts, function (error, response, body) {

            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'AIRTEL_PREPAID_FETCH_TOKEN',
                'URL': _.get(apiOpts, 'url', null)
            });
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_FETCH_TOKEN', `STATCODE:${_.get(response, 'statusCode', _.get(error, 'code', '5XX'))}`]);

            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    self.L.error("fetchToken", `Error parsing token response body`, e);
                    body = null;
                }
            }

            if (response && _.get(response, 'statusCode') == 200 && body && _.get(body, 'token', null)) {
                self.token = _.get(body, 'token', null);
                self.L.log('fetchToken', `Token received successfully...`);
                self.L.verbose('fetchToken', `Token received : ${self.token}`);

                self.L.log('Emitting token fetched event');
                self.eventEmitter.emit('TOKEN_FETCHED');
                self.fetchTokenLocked = false;
                return done(null);
            } else {
                self.L.critical('fetchToken', `Request failed with statusCode:${_.get(response, 'statusCode')},error-${error},body-${JSON.stringify(body)}`);
                setTimeout(function () {
                    self.L.log('fetchToken', 'Retrying to fetch token...');
                    return self.fetchToken(done);
                }, 1000);
            }
        });
    }

    /**
     * 
     * @param {*} done 
     * @param {*} currentRecord 
     * @param {*} updatedRecord 
     */
    fetchBill(done, currentRecord, updatedRecord) {
        let
            self = this,
            apiOpts = {
                url: `https://zapi.airtel.in/s/app/wl-service/airtel-mobility-recharge/prepaid/v1/3P/recharge?siNumber=${currentRecord.recharge_number}&liteRequest=true`,
                timeout: self.apiTimeout,
                headers: {
                    'authorization': `Bearer ${self.token}`
                }
            };

        self.L.verbose("fetchBill", `apiOpts for ${updatedRecord.traceKey}`, JSON.stringify(apiOpts));

        var latencyStart = new Date().getTime();

        self.throttledRequest(apiOpts, (error, response, body) => {
            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': 'AIRTEL_PREPAID_FETCH_BILL'
            });

            utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_FETCH_BILL', `STATCODE:${_.get(response, 'statusCode', _.get(error, 'code', '5XX'))}`]);

            if (body && typeof body === 'string') {
                try {
                    body = JSON.parse(body);
                } catch (e) {
                    self.L.error("fetchBill", `Error parsing data for ${currentRecord.traceKey}`, e);
                    return done('Parsing Error');
                }
            }
            if (response && _.get(response, 'statusCode') == 200 && body) {
                if (_.get(body, 'airtelPrepaid', true) === false) {
                    // Number is not airtel prepaid...removing it
                    updatedRecord.status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE_INVALID_RECHARGE_NUMBER', 131);
                    updatedRecord.reason = 'INVALID_PREPAID_NUMBER: Invalid Airtel prepaid Number';
                    error = updatedRecord.reason;
                } else if (_.get(body, 'expiryFlag') === true || _.get(body, 'expiryFlag') === false) { // This is valid response else error case
                    self.handleResponse(updatedRecord, _.get(body, 'expiryFlag'));
                } else {
                    updatedRecord.reason = 'FETCH_BILL_API_RESPONSE_ERROR: Failed to get Plan Expiry';
                    self.promoteForRetry(updatedRecord);
                    error = updatedRecord.reason;
                }

                updatedRecord.customerOtherInfo = body; // save response
                self.L.log('fetchBill', `Plan Validity Response ${JSON.stringify(body)} for ${currentRecord.traceKey}`);
                return done(error);
            } else if (response && (_.get(response, 'statusCode') == 401 || _.get(response, 'statusCode') == 403)) { // Invalid token

                if (self.fetchTokenLocked === true) { // fetch token already in progress...wait for the event 
                    self.L.error('fetchBill', `Token Expired....Waiting for token to be generated for ${updatedRecord.traceKey}...`);

                    self.eventEmitter.on('TOKEN_FETCHED', function retryFetchBill() {
                        self.L.log('fetchBill', `Token fetched TOKEN_FETCHED event received...processing record again for ${updatedRecord.traceKey}`);
                        self.eventEmitter.removeListener('TOKEN_FETCHED', retryFetchBill);
                        return self.fetchBill(done, currentRecord, updatedRecord);
                    });
                } else {
                    self.L.error('fetchBill', `Token Expired....Getting new token ${updatedRecord.traceKey}`);
                    self.fetchToken(function (error) {
                        if (error) {
                            self.L.error('fetchBill', `Error while fetching token for ${updatedRecord.traceKey}`);
                            return done('Failure response received');
                        } else {
                            self.L.log('fetchBill', `Token fetched...processing record again for ${updatedRecord.traceKey}`);
                            return self.fetchBill(done, currentRecord, updatedRecord);
                        }
                    });
                }
            } else {
                updatedRecord.reason = `FETCH_BILL_API_ERROR`;
                self.promoteForRetry(updatedRecord);
                self.L.error('fetchBill', `Request failed with statusCode:${_.get(response, 'statusCode')},error-${error},body-${JSON.stringify(body)}`);
                return done('Failure response received');
            }
        });
    }

    promoteForRetry(updatedRecord) {
        let self = this;
        if (_.get(updatedRecord, 'current_retry_count', 0) >= self.MAX_RETRY_COUNT - 1) {
            // No more retries, handle this record now as error
            updatedRecord.retry_count = 0;
            updatedRecord.status = _.get(self.config, 'COMMON.bills_status.MAX_RETRY_REACHED', 3)
            self.handleResponse(updatedRecord, 'ERROR');
        } else {
            updatedRecord.status = _.get(self.config, 'COMMON.bills_status.RETRY', 2);
            updatedRecord.retry_count = updatedRecord.current_retry_count + 1;
            updatedRecord.next_bill_fetch_date = MOMENT().add(15, 'minutes').format('YYYY-MM-DD HH:mm:ss');
        }
    }
    resetPriorityAndQueryParams() {
        let self = this;
        self.priority =0;
        self.params ={
            nextBillFetchDateFrom : MOMENT().subtract(78, 'days').startOf('day').format('YYYY-MM-DD'),
            nextBillFetchDateTo : MOMENT().add(1, 'days').format('YYYY-MM-DD'),
            batchSize : this.dbBatchSize
        };
        self.L.log('resetPriorityAndQueryParams', `reseted...priority::  ${self.priority} and query params :: ${self.params}`);
    }
    startDummyLogs() {
        let self = this;

        let dummyLogs = setInterval(function () {
            self.L.log('startDummyLogs', 'dummy logs...');
        }, 5000);
    }
    startPriorityScheduler() {
        let self = this,
            resetTime = ('00:00:00').split(":");

        let startRule = new SCHEDULE.RecurrenceRule();
        startRule.hour = resetTime[0];
        startRule.minute = resetTime[1];
        SCHEDULE.scheduleJob(startRule, self.resetPriorityAndQueryParams.bind(self));

        self.L.log('startPriorityScheduler', 'for reset priority and  NextBillFetchDate !!');
    }
    getNextPriority() {
        let self = this;
        self.L.log('getNextPriority', 'nextPriority ::', (self.priority + 1) % Object.keys(self.priority_map_query).length);

        return (self.priority + 1) % Object.keys(self.priority_map_query).length;
    }
    getPriority() {
        let self = this;

        self.L.log('getPriority', 'priority ::',self.priority);

        return self.priority
    }

    /**
     * 
     * @param {*} done 
     * @param {*} dbRecordResp 
     */
     publishCtEvents(done, dbRecordResp) {
        let self = this;

        const customerId = _.get(dbRecordResp, 'customer_id', '');
        const operator = _.get(dbRecordResp, 'operator', '');
        const rechargeNumber = _.get(dbRecordResp, 'recharge_number', '');
        const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen')
        const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;

        let productId = _.get(dbRecordResp, 'product_id', '');
        productId = self.activePidLib.getActivePID(productId);
        
        // reminder util expects customerOtherInfo as JSON string
        let customerOtherInfo = _.get(dbRecordResp, 'customerOtherInfo', '');
        customerOtherInfo = customerOtherInfo ? JSON.stringify(customerOtherInfo) : {};
        _.set(dbRecordResp, 'customerOtherInfo', customerOtherInfo)
       
        if (!_.get(dbRecordResp, 'notification_status', 1)) {
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            return done(null)
        } 	 
        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {                    
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                self.billSubscriber.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AIRTEL_PREPAID", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.critical('publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:AIRTEL_PREPAID", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.log('publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ], error => {
            if(error) {
                self.L.error('publishCtEvents',`Exception occured Error Msg:: ${error} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            } else {
                self.L.log(`publishCtEvents`,`Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }

     suspendOperations(){
        this.stopService = true;
        this.L.info("AirtelPublisher::stopService");
    }
}

export default AirtelPublisher