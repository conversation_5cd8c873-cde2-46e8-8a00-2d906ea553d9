import REQUEST from 'request'
import MOMENT  from 'moment'
import _       from 'lodash'
import ASY<PERSON>   from 'async'

/*let apiKey = 'c65ed121cf0fbf6200e1a7e651988693',
    appKey = '12ca01fe1d14859397e413373a29ceeebdac4685'
    
    Was being used in past... when we were pushing metrics in Datadog
    */

class LatencyProvider {

    constructor(options) {
        this.L = options.L;
        this.L.log('Latency Provider initiated')
        this.latencies = {}
        this.metrics = [
            'INGW_UTILITIES_LATENCY_mean',
            'INGW_RECHARGE_LATENCY_mean',
            'INGW_BFSI_LATENCY_mean'
        ]
        // this.monitoringUrl = 'http://internal-centralprometheus-mumalb-422667485.ap-south-1.elb.amazonaws.com';
        this.monitoringUrl = 'https://prometheus-api.mypaytm.com/recharges-api-mum/';
        
    }

    keepUpdating() {
        let self = this,
            from = new Date(),
            to   = new Date();

        from.setSeconds(from.getSeconds() - 30);

        ASYNC.each(self.metrics,(metric,callback)=>{

        REQUEST({
            url: `${self.monitoringUrl}/api/v1/query_range?query=${metric}{REQUEST_TYPE=\"VALIDATION\"}&start=${from.toISOString()}&end=${to.toISOString()}&step=5s`,
            headers: {Authorization : "Basic ZGlnaXRhbHByb21ldGhldXM6ZGlnaXRhbDUzaHU2"}
        }, function(err, response, body) {
            if(err){
                self.L.error("error in fetching data from datadog",err);
            }
            if(typeof body == 'string') {
               try {
                  body = JSON.parse(body)
               }catch (ex) {
                  body = {};
                  self.L.error("error in parsing response from datadog",ex);
               }  
            }
            
            let gatewayDataBlocks = _.get(body, ['data','result'], []),
                newLatencies = {}
            gatewayDataBlocks.forEach(dataBlock => {
                let gatewayName = _.get(dataBlock,['metric','GATEWAY_NAME'],'');

                let points     = _.get(dataBlock,['values'],''),
                    avgLatency = null

                if(points.length > 0) {
                    let sum = 0
                    points.forEach(point => {
                        let val = parseInt(point[1])
                        if(!isNaN(val)){
                            sum += val
                        }
                    })
                    avgLatency = Math.round(sum/points.length)
                }
                newLatencies[gatewayName] = avgLatency
                self.latencies[gatewayName] = avgLatency
            })

             self.L.log('Latency fetched from datadog for gateways',JSON.stringify(newLatencies));
             callback();
            }
        )},(err)=>{
            setTimeout(() => {
                self.keepUpdating() //every 30 seconds
            }, 30000)
        });
    }

    getLatency(gateway) {
        let self=this,
            latency=this.latencies[gateway];
        if(isNaN(latency)){
            self.L.error("latency not found for gateway ",gateway, "latency= ",latency);
        }
        return latency;
    }
}

export default LatencyProvider
