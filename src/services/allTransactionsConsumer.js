'use strict';

import MOMENT from 'moment';
import _ from 'lodash';
import MODELS from '../models'
import OS from 'os'
import ASYNC from 'async'
import recentBillLibrary from '../lib/recentBills'
import utility from '../lib'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class allTransactionsConsumer {
    constructor(options) {
        let self = this;
        self.L = options.L;
        self.stopService = false;
        self.config = options.config;
        self.activePidLib = options.activePidLib;
        self.infraUtils = options.INFRAUTILS;
        self.transactionCounterModel = new MODELS.AllTransactionsCounter(options);
        self.recentBillLibrary = new recentBillLibrary(options);
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.greyScaleEnv = options.greyScaleEnv;
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : 500; //change
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCH_DELAY'], 5*60*1000) : 500;    
    }

    start() {
        let self = this;
        self.L.log("start", "configuring allTransactionsConsumer");

        // configure Kafka Publisher to push plan_validity_expiry events to Kafka
        self._initializeKafkaConsumer((error) => {
            if (error) {
                self.L.critical('allTransactionsConsumer :: start', 'Error while configuring Kafka consumer...', error);
            } else {
                self.L.log('allTransactionsConsumer :: start', 'Kafka consumer configured....');
            }
        });
    }

    _initializeKafkaConsumer(cb) {
        let self = this;
        try {
            self.consumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'),
                "groupId": 'all_transactions_consumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.RECENTBILL.RECHARGE_CONSUMER_TOPICS'),
                "id": "all-transactions-consumer-" + OS.hostname(),
                "fromOffset": "earliest",
                "batchSize": self.kafkaBatchSize,
                "autoCommit": false
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error)
                    self.L.critical("allTransactionsConsumer : consumer Configured cannot start.", error);
                else if (!error)
                    self.L.log("allTransactionsConsumer : consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error)
        }
    }

    _processKafkaData(records) {
        let self = this,
        rechargeData = null,
        startTime = new Date().getTime(),
        chunkSize = 30,
        lastMessage,
        recordsToProcess = [];
          
       if (!_.isEmpty(records) && _.isArray(records)) {
           lastMessage = records[records.length -1];
           self.consumer._pauseConsumer();
           self.L.log('allTransactionsConsumer::_processKafkaData received data from kafka ', records.length);
       } else {
            self.L.critical('allTransactionsConsumer::_processKafkaData error while reading kafka');
            return
       }
        
       self.kafkaConsumerChecks.findOffsetDuplicates("allTransactionsConsumer", records);

        records.forEach(row => {
            if(row && row.value) {
                try {
                    rechargeData = JSON.parse(row.value);
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ALL_TRANSACTIONS_CONSUMER", 'STATUS:INVALID_PAYLOAD']);
                    self.L.error("allTransactionsConsumer::_processKafkaData", "Failed to parse recharges data topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ALL_TRANSACTIONS_CONSUMER", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("allTransactionsConsumer::_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp );
            }
        });

        self.L.log('AllTransactionsCounter::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, chunkSize, self._prepareDataToInsert.bind(self), (err) => {
            if(err) {
                self.L.error("allTransactionsConsumer::_prepareDataToInsert Error: ", err );
            }
            self.consumer.commitOffset(lastMessage, (error) => {
                if (error) {
                    self.L.error('AllTransactionsCounter::_processBillsData::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }
                else {
                    self.L.log('AllTransactionsCounter::_processBillsData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }
                recordsToProcess = [];

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ', records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:AllTransactionsCounter", "TIME_TAKEN:" + executionTime]);


                if(self.stopService) {
                    self.L.log("Gracefull shutdown, stop resuming consumer ");
                    return;
                }
                setTimeout(() => {
                    // Resume consumer now
                    self.consumer._resumeConsumer();
                }, self.kafkaResumeTimeout);
            });
        });
   }

   /**
     *
     * @param {object} rechargeData contains recharge data value
     */
    _prepareDataToInsert(rechargeData, done) {
        let self = this,
        isDataValid = self.recentBillLibrary.isCreateBillDataValid(rechargeData),
        recharge_status = _.get(rechargeData, 'inStatusMap_responseCode', ''),
        customerId, operator, rechargeNumber;
        if (isDataValid && recharge_status == '00') {
            customerId = _.get(rechargeData, "customerInfo_customer_id", '')
            operator = _.get(rechargeData, "productInfo_operator", '')
            rechargeNumber = _.get(rechargeData, "userData_recharge_number", '')
        } else {
            customerId = _.get(rechargeData, "customerInfo_customer_id", '')
            this.L.log("skipping incomplete record - customerID ", customerId);
            return done();
        }
        ASYNC.waterfall([
            next => {
                // Fetching
                return self.transactionCounterModel.fetchCustomerDetails(next, customerId);
            },
            (customerRecord, next) => {
                if(customerRecord.length > 1) {
                    return done('_prepareDataToInsert:: Getting multiple records for a unique key');
                }
                return self._processCustomerInfo(next, customerRecord, customerId, operator, rechargeNumber);
            },
            (customerInfo,next) => {
                return self.transactionCounterModel.writeCustomerDetails(next, customerInfo);
            }
        ], (error) => {
            if (error) {
                self.L.error(`_prepareDataToInsert`, `Failed with error ${JSON.stringify(error)} for customerId ${customerId}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ALL_TRANSACTIONS_CONSUMER", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            }
            return done();
        });
    }

   _processCustomerInfo(cb, customerRecordfromDB, customerId, operator, rechargeNumber){
       let self = this;
       let customerInfo = {};
       const counterThreshold = _.get(self.config, ['DYNAMIC_CONFIG', "NOTIFICATION_EXCEPTIONS", "BLOCK_SMS", "COUNTER_THRESHOLD"], 6);
       const minDateThreshold = _.get(self.config, ['DYNAMIC_CONFIG', "NOTIFICATION_EXCEPTIONS", "BLOCK_SMS", "DAYS_THRESHOLD"], 30);

       try {
        _.set(customerInfo, 'customer_id', customerId);

        // No record found. Add default values
        if(customerRecordfromDB && customerRecordfromDB.length === 0) {
             _.set(customerInfo, 'is_retailer', false);
             _.set(customerInfo, 'counter', 1);
             _.set(customerInfo, 'min_date', MOMENT().format("YYYY-MM-DD"));
             _.set(customerInfo, 'whatsapp_notification_status', -1);
 
        } else if(customerRecordfromDB && customerRecordfromDB.length === 1) { // valid record found
             const counter = _.get(customerRecordfromDB[0], 'counter');
             const isRetailer = _.get(customerRecordfromDB[0], 'is_retailer');
             let minDate = MOMENT(_.get(customerRecordfromDB[0], 'min_date'), "YYYY-MM-DD");
             const whatsapp_notification_status = _.get(customerRecordfromDB[0], 'whatsapp_notification_status', -1);
             const debugkey = `customerId: ${customerId}, operator: ${operator}, rechargeNumber: ${rechargeNumber}`
             
             minDate = (minDate && minDate.isValid()) ? minDate : MOMENT().format("YYYY-MM-DD");
             
             _.set(customerInfo, 'whatsapp_notification_status', whatsapp_notification_status);
             
             if(MOMENT().diff(minDate, 'days') > minDateThreshold) {
                 _.set(customerInfo, 'is_retailer', false);
                 _.set(customerInfo, 'counter', 1);
                 _.set(customerInfo, 'min_date', MOMENT().format("YYYY-MM-DD"));
             } else if(counter >= counterThreshold) {
                 _.set(customerInfo, 'counter', 1);
                 _.set(customerInfo, 'is_retailer', true);
                 _.set(customerInfo, 'min_date', MOMENT().format("YYYY-MM-DD"));
                 utility._sendMetricsToDD(1, ["REQUEST_TYPE:ALL_TRANSACTIONS_CONSUMER", 'STATUS:RETAILER_STATUS_ADDED']);
                 self.L.log('AllTransactionsCounter::_processCustomerInfo setting customer as retailer', debugkey);
             } else {
                _.set(customerInfo, 'counter', counter+1);
                _.set(customerInfo, 'is_retailer', isRetailer);
                _.set(customerInfo, 'min_date', MOMENT(minDate).format("YYYY-MM-DD"));
             }
        }
        return cb(null ,customerInfo)
       } catch (error) {
           return cb(error)
       }
   }

    suspendOperations(){
        this.stopService = true;
        this.L.info("allTransactionsConsumer::stopService");
    }
}

export default allTransactionsConsumer;;


 /*
 initialise kafka consumer
 add kafka config for each topic
 processmessage -> get customer_id and date from each transaction
 get the table details
 add details if record comes empty
 do stuff based on wiki if details are there
*/