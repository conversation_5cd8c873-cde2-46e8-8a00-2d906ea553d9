"use strict";

import OS from 'os'
import _ from 'lodash'
import utility from '../lib'
import ASYNC from 'async'
import MOMENT from 'moment'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import cassandraBills from '../models/cassandraBills';
import Q from 'q'

/**
 * This service is used to consume recharge topic kafka and do it processing which includes :
 * -> write data in active_paytm_users table 
 * -> read and insert/update data in active_paytm_users_new table
 * -> publish data to expiredCAPublisher which as a goal publish data to nonru pipeline.
 */
class ActivePaytmUsersConsumer {
    /**
     * @param { object } options Contains configuration and dependencies
     * @param { object } options.L Paytm Logger (lgr) object
     * @param { object } options.config Local config object
     * @param { object } options.INFRAUTILS Contains util libraries like kafka
     */

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.client = options.cassandraDbClient;
        self.infraUtils = options.INFRAUTILS;

        self.greyScaleEnv = options.greyScaleEnv;
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : _.get(self.config, ['DYNAMIC_CONFIG', 'ACTIVE_PAYTM_USERS_CONFIG', 'COMMON', 'KAFKA_BATCHSIZE'], 500);
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 5*60*1000) : 500;
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.cassandraBills = new cassandraBills(options);
        self.allowedNumberOfPaymentDates = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'PAYMENT_DATE_LIST_SIZE'], 10);
        self.allowedNumberOfServicesForPaymentDates = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICES_WISE_PAYMENT_DATE_LIST_SIZE'], 3);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);    
    }

    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: activePaytmUsersConsumer", "Re-initializing variable after interval");
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : _.get(self.config, ['DYNAMIC_CONFIG', 'ACTIVE_PAYTM_USERS_CONFIG', 'COMMON', 'KAFKA_BATCHSIZE'], 500);
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 5*60*1000) : 500;
        self.allowedNumberOfPaymentDates = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'PAYMENT_DATE_LIST_SIZE'], 10);
        self.allowedNumberOfServicesForPaymentDates = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICES_WISE_PAYMENT_DATE_LIST_SIZE'], 3);
    }

    start() {
        let self = this;

        self.L.log("Start Configuration: ActivePaytmUsersConsumer is starting........");
        
        ASYNC.waterfall([
            next => {
                self.L.log('start', 'Going to initialize Kakfa Publisher');
                return self._startProducer(next);
            },
            next => {
                self.L.log('start', 'Going to initialize Kakfa Consumer');
                return self._initializeActivePaytmUsersConsumer(next);
            }
        ],(error) => {
            if (error) {
                self.L.error("ActivePaytmUsersConsumer", "Failed to initialize active paytm users consumer service.");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:KAFKA_CONFIGURATION']);
            }
        });
    }

    _startProducer(cb){
        let self = this;

        self.activePaytmUsersKafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.ACTIVE_PAYTM_USERS.HOSTS')
        });
        
        self.activePaytmUsersKafkaPublisher.initProducer('high', function (error) {
            if (error) {
                self.L.critical('_startProducer : error in initialising activePaytmUsersKafka Producer :: ', error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:ACTIVE_PAYTM_USERS']);
                return cb(error);
            } else {
                self.L.log("_startProducer : activePaytmUsersKafka KAFKA PRODUCER STARTED....");
                return cb(null);
            }
        });
    }

    _initializeActivePaytmUsersConsumer(cb) {
        let self = this;
        self.L.log("Service started on topics:: " + _.get(self.config.KAFKA, 'SERVICES.RECENTBILL.RECHARGE_CONSUMER_TOPICS'));
        try {
            self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'), 
                "groupId": 'active-paytm-users-consumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.RECENTBILL.RECHARGE_CONSUMER_TOPICS'),
                "id": "active-paytm-users-consumer-" + OS.hostname(),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error){
                    self.L.critical("_initializeActivePaytmUsersConsumer : ActivePaytmUsers Consumer cannot start, error: ", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:ACTIVE_PAYTM_USERS']);
                }
                else if (!error)
                    self.L.log("_initializeActivePaytmUsersConsumer : ActivePaytmUsers Consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error);
        }
    }

    _processKafkaData(records, resolveOffset, topic ,partition ,cb) {
        let self = this,
            lastMessage,
            recordsToProcess = [];

        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('activePaytmUsersConsumer::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('activePaytmUsersConsumer::_processKafkaData error while reading kafka');
            return cb();
        }

        records.forEach(function (data) {
            if (data && data.value) {
                try {             
                    let rechargeData = JSON.parse(data.value);
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:INVALID_JSON_PAYLOAD']);
                    self.L.error("_processKafkaData", "Failed to parse recharges data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
            }
        });

        self.L.log('activePaytmUsersConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, 1, self._processBillsData.bind(self), async (err) => {

            self.kafkaConsumerChecks.findOffsetDuplicates("ActivePaytmUsers", records, topic , partition);

            if(err) {
                self.L.error("activePaytmUsersConsumer::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }else{

                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
                
            }
        });   
    }

    _processBillsData(billsKafkaRow, done) {
        let self = this;
        if(self.validateKafkaPayload(billsKafkaRow)){
            let record = {
                customer_id: _.get(billsKafkaRow, 'customerInfo_customer_id', ''),
                service: _.get(billsKafkaRow, 'productInfo_service', ''),
                payment_date: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                source: 'userRecharge',
                recharge_number : _.get(billsKafkaRow, 'userData_recharge_number', ''),
                operator: _.get(billsKafkaRow, 'productInfo_operator', '')
            };

            ASYNC.waterfall([
                next => {
                    self.writeInOldTable((err) => {
                        return next();
                    }, record);
                },
                next => {
                    self.writeInNewTable((err, service_wise_payment_dates) => {
                        return next(null, service_wise_payment_dates);
                    }, record);
                },
                (service_wise_payment_dates, next) => {
                    self.publishInActivePaytmUsersKafka((err) => {
                        return next();
                    }, record, service_wise_payment_dates);
                }
            ],(error) => {
                return done();
            });
        } else {
            done();
        }
    }

    validateKafkaPayload(billsKafkaRow) {
        let self = this;

        if(_.get(billsKafkaRow, 'inStatusMap_responseCode', '') == "00") {
            let mandatoryFields = ['customerInfo_customer_id', 'productInfo_service', 'userData_recharge_number', 'productInfo_operator'];
            mandatoryFields.forEach((field) => {
                let value = _.get(billsKafkaRow, field, '');
                if (value === '') {
                    self.L.error('activePaytmUsersConsumer::validateKafkaPayload::', 'Mandatory field missing in kafka payload', field);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:MISSING_MANDATORY_FIELD', 'FIELD:' + field]);
                    return false;
                }
            });
            return true;
        } else {
            self.L.error('activePaytmUsersConsumer::validateKafkaPayload::', 'Invalid response code in kafka payload', _.get(billsKafkaRow, 'inStatusMap_responseCode', ''));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:INVALID_RESPONSE_CODE', 'RESPONSE_CODE:' + _.get(billsKafkaRow, 'inStatusMap_responseCode', '')]);
            return false;
        }
    }

    writeInOldTable(cb, record) {
        let self = this;
    
        self.cassandraBills.writeInActivePaytmUsers(record)
            .then(() => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:SUCCESS', 'TYPE:DB_INSERTION', 'SERVICE:' + record.service]);
                return cb(null);
            })
            .catch((error) => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:DB_INSERTION', 'SERVICE:' + record.service]);
                self.L.error('Error inserting data into Cassandra for ', JSON.stringify(record));
                return cb('activePaytmUsersConsumer::writeInOldTable::insert DB exception! for ' + JSON.stringify(record) + error);
            });
    }

    writeInNewTable(cb, record) {
        let self = this;

        ASYNC.waterfall([
            next => {
                self.cassandraBills.readActivePaytmUsersNewByCId(record.customer_id)
                .then((data) => {
                    return next(null, data);
                })
                .catch((error) => {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER",'STATUS:ERROR','TYPE:DB_INSERTION_IN_NEW_TABLE','OPERATION:SELECT', 'SERVICE:' + record.service]);
                    return next('activePaytmUsersConsumer::writeInNewTable::select DB exception! for ' + JSON.stringify(record) + error);
                });
            },
            (data, next) => {
                if (!_.isEmpty(data)) {
                    let filteredData = data.filter((row) => row.service === record.service);
                    if (!_.isEmpty(filteredData)) {
                        let row = filteredData[0];
                        let payment_date_list = row.payment_date_list;
                        let created_at = row.created_at;
                        let created_source = row.created_source;

                        if (payment_date_list && _.isArray(payment_date_list)) {
                            payment_date_list.push(record.payment_date);
                            if (payment_date_list.length > self.allowedNumberOfPaymentDates) {
                                payment_date_list = payment_date_list.slice(-self.allowedNumberOfPaymentDates);
                                return next(null, data, payment_date_list, created_source, created_at);
                            } else {
                                return next(null, data, payment_date_list, created_source, created_at);
                            }
                        } else {
                            payment_date_list = [record.payment_date];
                            self.L.log('activePaytmUsersConsumer::writeInNewTable::', 'payment_date_list was not an array so initialized with ', [record.payment_date]);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER",'STATUS:ERROR','TYPE:PAYMENT_DATE_COLUMN_IS_NOT_LIST', 'SERVICE:' + record.service]);
                            return next(null, data, payment_date_list, created_source, created_at);
                        }
                    } else {
                        return next(null, data, [record.payment_date], record.source, MOMENT().format('YYYY-MM-DD HH:mm:ss'));
                    }
                } else {
                    return next(null, data, [record.payment_date], record.source, MOMENT().format('YYYY-MM-DD HH:mm:ss'));
                }
            },
            (data, payment_date_list, created_source, created_at, next) => {
                let service_wise_payment_dates = {};
                let promises = data.map((row) => {
                    if (row.service !== record.service) {
                        service_wise_payment_dates[row.service] = row.payment_date_list;
                        let params = [row.customer_id, row.service, record.payment_date, row.payment_date_list, row.created_source, record.source, row.created_at, MOMENT().format('YYYY-MM-DD HH:mm:ss')];
                        return self.cassandraBills.writeInActivePaytmUsersNew(params)
                        .catch((error) => {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER",'STATUS:ERROR','TYPE:DB_INSERTION_IN_NEW_TABLE','OPERATION:WRITE', 'SERVICE:' + row.service]);
                            throw new Error('activePaytmUsersConsumer::writeInNewTable::insert DB exception! for other servcies of ' + JSON.stringify(record) + error);
                        });
                    } else {
                        return Promise.resolve();
                    }
                });

                // write out of loop for handling the case when writing first time for incoming service
                service_wise_payment_dates[record.service] = payment_date_list;
                self.L.log('activePaytmUsersConsumer::writeInNewTable::', 'service_wise_payment_dates', service_wise_payment_dates);
                let params = [record.customer_id, record.service, record.payment_date, payment_date_list, created_source, record.source, created_at, MOMENT().format('YYYY-MM-DD HH:mm:ss')];
                promises.push(
                    self.cassandraBills.writeInActivePaytmUsersNew(params)
                    .catch((error) => {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER",'STATUS:ERROR','TYPE:DB_INSERTION_IN_NEW_TABLE','OPERATION:WRITE', 'SERVICE:' + record.service]);
                        throw new Error('activePaytmUsersConsumer::writeInNewTable::insert DB exception! for' + JSON.stringify(record) + error);
                    })
                );

                Promise.all(promises)
                .then(() => {
                    return next(null, service_wise_payment_dates);
                })
                .catch((error) => {
                    return next(error, service_wise_payment_dates);
                });
            }
        ], function (error, service_wise_payment_dates) {
            if (error) {
                self.L.error('Error inserting data in new table for ', JSON.stringify(record),' error: ', error);
                return cb(error, service_wise_payment_dates);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:SUCCESS','TYPE:DB_INSERTION_IN_NEW_TABLE', 'SERVICE:' + record.service]);
                return cb(null, service_wise_payment_dates);
            }
        });
    }

    publishInActivePaytmUsersKafka(done, record, service_wise_payment_dates){
        let self = this,
            payload = {
                customer_id : record.customer_id,
                service : record.service,
                operator : record.operator,
                recharge_number : record.recharge_number,
                payment_date : record.payment_date,
                is_active_expired_user : true,
            };

        if (Object.keys(service_wise_payment_dates).length <= self.allowedNumberOfServicesForPaymentDates) {
            _.set(payload, 'service_wise_payment_dates', service_wise_payment_dates);
        } else {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:INFO', 'TYPE:SERVICES_WISE_PAYMENT_DATE_LIST_SIZE_EXCEEDED', 'SERVICE:' + record.service]);
        }

        self.activePaytmUsersKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.ACTIVE_PAYTM_USERS.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:ERROR', 'TYPE:KAFKA_PUBLISHER', 'SERVICE:' + record.service]);
                self.L.critical('publishInKafka :: ACTIVE_PAYTM_USERS', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                return done(error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:ACTIVE_PAYTM_USER", 'STATUS:SUCCESS', 'TYPE:KAFKA_PUBLISHER', 'SERVICE:' + record.service]);
                self.L.log('publishInKafka :: ACTIVE_PAYTM_USERS', 'Message published successfully in Kafka', ' on topic ACTIVE_PAYTM_USERS', JSON.stringify(payload));
                return done();
            }
        }, [200, 800]);   
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`activePaytmUsersConsumer :: suspendOperations : kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`activePaytmUsersConsumer :: suspendOperations : Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`activePaytmUsersConsumer :: suspendOperations : Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`activePaytmUsersConsumer :: suspendOperations : kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`activePaytmUsersConsumer :: suspendOperations : error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }

}

export default ActivePaytmUsersConsumer;
