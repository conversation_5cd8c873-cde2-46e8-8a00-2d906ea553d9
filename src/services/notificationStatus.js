import NOTIFICATION from '../models/notification'
import REQUEST from 'request'
import MOMENT from 'moment'
import <PERSON>Y<PERSON> from 'async'
import _ from 'lodash'

let L = null;

class NotificationStatus {
    constructor(options) {
        this.L = options.L;
        L = this.L;
        this.config = options.config;
        this.status_list = _.get(this.config, ['NOTIFICATION', 'status']);
        this.notificationApiConfig = _.get(this.config, ['NOTIFICATION', 'notificationapi']);
        this.dbInstance = options.dbInstance;
        this.stopService = false;
        this.notification = new NOTIFICATION(options);
    }

    start() {
        let self = this;
        L.log("start", "notification status service started");
        self.processNotifications();
    }

    processNotifications() {
        let self = this,
            timeInterval = MOMENT().subtract(_.get(self.notificationApiConfig, 'TIMEINTERVAL', 1),'minutes').format('YYYY-MM-DD HH:mm:ss'),
            batchSize = _.get(self.notificationApiConfig, 'BATCHSIZE', 100),
            chunkSize = _.get(self.notificationApiConfig, 'CHUNKSIZE', 20);

        self.notification.getSentNotifications(function _doUntilNoMoreRecords(error, data) {
            if (!error && data && data.length > 0) {
                self.execSteps(data, chunkSize, () => {
                    if(self.stopService) {
                        L.log('Service Stop Gracefully, not fetching data')
                        return;
                    }
                    self.notification.getSentNotifications(_doUntilNoMoreRecords, _.get(self.status_list,'SENT',null), timeInterval, batchSize);
                });
            }
            else {
                setTimeout(self.processNotifications.bind(self), 5*60*1000); //wait for 5 mins
            }
        }, _.get(self.status_list,'SENT',null), timeInterval, batchSize);
    }

    execSteps(records, chunkSize, done) {
        let self = this,
            startTime = new Date().getTime(),
            currentPointer = 0;

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer+chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(()=>{
                        callback();
                    }, 1000);
                });
            },
            (err) => {
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:NotificationStatus", "TIME_TAKEN:" + executionTime]);
                done();
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.waterfall([
                next => {
                    self.processNotificationBatch(records, next);
                },
                (result, next) => {
                    self.updateNotification(result, () => {
                        next();
                    })
                }
            ],
            err => {
                done();
        });
    }

    processNotificationBatch(records, done) {
        let self   = this,
            result = {};

        ASYNC.map(
            records,
            (record, next) => {
                self.getNotificationResponse(response => {
                    result[record.id] = response;
                    next();
                }, record);
            },
            err => {
                done(null, result);
            }
        )
    }

    getNotificationResponse(callback, row) {
        let self = this;
        let apiOpts = {
            "uri": _.get(self.notificationApiConfig, 'STATUSAPI', null) + '?job_id=' + row.job_id,
            "method": "GET",
        };

        REQUEST(apiOpts, (error, response, body) => {
            self.prepareResponse((responseObj) => {
                callback(responseObj);
            }, row, error, response, body);
        });
    }

    prepareResponse(callback, row, error, response, body) {
        let self = this;
        let responseObj = {};
        if (body && body.error) {
            responseObj.error_msg = "Status API ERROR: " + JSON.stringify(body.error) + (body.code ? body.code : '');
        } else if (error) {
            responseObj.error_msg = error + (response && response.statusCode ? response.statusCode : '');
        } else if (typeof body === 'string') {
            try {
                let jsonBody = JSON.parse(body);
                if (jsonBody && jsonBody.doc && jsonBody.doc.length > 0) {
                    let firstResponseArray = jsonBody.doc[0];
                    if (firstResponseArray.status && firstResponseArray.status.toLowerCase() === 'success') {
                        responseObj.status = _.get(self.status_list,'SUCCESS',null);
                        L.log('prepareResponse: Marking success for job_id: ' + row.job_id);
                    } else if (jsonBody.doc.length === _.get(self.notificationApiConfig, 'RETRYCOUNT', null)) {
                        responseObj.status = _.get(self.status_list,'FAILED',null);
                        responseObj.error_msg = firstResponseArray.error;
                        L.log('prepareResponse: Marking failure for job_id: ' + row.job_id);
                    } else {
                        responseObj.error_msg = firstResponseArray.error;
                    }
                }
            } catch (ex) {
                responseObj.error_msg = ex;
            }
            if (responseObj.error_msg) {
                L.error('prepareResponse: Status API ERROR', responseObj.error_msg);
            }
        }
        callback(responseObj);
    }

    updateNotification(results, done) {
        let self = this;
        ASYNC.eachOf(
            results,
            (result, id, next) => {
                let fields = [];
                let whereCondition = 'id = ?';
                let params = [];
                for (let key in result) {
                    fields.push(key);
                    params.push(result[key]);
                }
                params.push(id);

                self.notification.updateNotification((error, data) => {
                    if (!error) {
                        L.info('updateNotification: notification entries is updated in the database for id: ', id);
                    }
                    next();
                }, fields, whereCondition, params);
            },
            err => {
                done()
            }
        );
    }

    suspendOperations(){
        this.stopService = true;
        L.info("NotificationStatus::stopService");
    }
}

export default NotificationStatus;