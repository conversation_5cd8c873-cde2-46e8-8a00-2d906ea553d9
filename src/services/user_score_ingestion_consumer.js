/*
    Author: <PERSON><PERSON><PERSON>
    to start the consumer locally execute following command
    VAULT=0 node dist/app.js --userScoreingestionConsumer
*/

import _        from 'lodash'
import ASYNC    from 'async';
import MOMENT   from 'moment';
import OS       from 'os';
import Q        from 'q'

import utility              from '../lib'
import KafkaConsumer        from '../lib/KafkaConsumer';
import KafkaConsumerChecks  from '../lib/kafkaConsumerChecks';
import cassandraBills       from '../models/cassandraBills'
import BILLS                from '../models/bills';
import NonPaytmBillsModel   from '../models/nonPaytmBills';
import EncryptorDecryptor   from 'encrypt_decrypt';
import UserScoreHelper      from '../lib/userScoreHelper';

const _TIER = "_tier";
const _SCORE_100 = "_score_100";
// const aa = abcd;
const SCORES = 'scores';
const BUCKETS = 'buckets';
const NBFD_HOURS = "nbfdHours";
const DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";

class UserScoreIngestor {

    constructor(options) {
        let self = this;
        self.config = options.config;
        self.L = options.L;
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.bills = new BILLS(options);
        this.cassandraBills = new cassandraBills(options);
        this.nonPaytmBills = new NonPaytmBillsModel(options);
        this.infraUtils = options.INFRAUTILS;
        // this.refreshScoreConfig();
        this.cryptr = new EncryptorDecryptor();
        this.userScoreHelper = new UserScoreHelper(options);
        // self.bucket = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'COMMON', 'CONST_STRING_TIER'], _TIER);
        // self.score_100 = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'COMMON', 'CONST_STRING_SCORE_100'], _SCORE_100);
    }

    start() {

        let self = this;
        self.L.log("USER_SCORE_INGESTION_CONSUMER:: Starting service");

        ASYNC.waterfall([
            next => {
                
                self.startProducer(next);
            },
            next => {
                self.startConsumer(next);
            }
        ],(error) => {
            if (error) {
                self.L.critical('USER_SCORE_INGESTION_CONSUMER :: start', 'Error while starting service...', error);
                process.exit(0)
            } else {
                self.L.log('USER_SCORE_INGESTION_CONSUMER :: start', 'Service started....');
            }
        })
    }

    startProducer(cb) {

        let self = this;
        ASYNC.parallel([
            next => {
                self.reminderKafkaProducer = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                self.reminderKafkaProducer.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising reminderKafkaProducer :: ', error);
                    self.L.log("reminderKafkaProducer :: reminderKafkaProducer KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.nonPaytmKafkaProducer = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                self.nonPaytmKafkaProducer.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising nonPaytmKafkaProducer :: ', error);
                    self.L.log("nonPaytmKafkaProducer :: nonPaytmKafkaProducer KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            next => {
                self.userScoreIngestorFailedRecordsKafkaProducer = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                self.userScoreIngestorFailedRecordsKafkaProducer.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising userScoreIngestorFailedRecordsKafkaProducer :: ', error);
                    self.L.log("userScoreIngestorFailedRecordsKafkaProducer :: userScoreIngestorFailedRecordsKafkaProducer KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            }
        ], (error) => {
            return cb(error);
        })
    }

    startConsumer(cb) {

        let self = this;
        try {
            self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'), 
                "groupId": 'USER_SCORE_INGESTOR_CONSUMER',
                "topics": _.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR.TOPIC'),
                "id": "user-score-ingestor-consumer-" + OS.hostname(),
                "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.USER_SCORE_INGESTOR_CONSUMER.BATCHSIZE',1000000),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.USER_SCORE_INGESTOR_TIMEOUT',30*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error)
                    self.L.critical("startConsumer : consumer Configured cannot start.", error);
                else if (!error)
                    self.L.log("startConsumer : consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error)
        }
    }

    _processKafkaData(records,resolveOffset , topic , partition , cb) {

        let startTime = new Date().getTime();
        let self = this;
        const chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'COMMON', 'CHUNKSIZE'], 1);
        let lastMessage,
        rechargeData = null,
        recordsToProcess = [];

        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('_processKafkaData::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('_processKafkaData::_processKafkaData error while reading kafka');
            return cb();
        }

        records.forEach(row => {
            if(row?.value) {
                try {
                    rechargeData = JSON.parse(row.value);
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:INVALID_JSON_PAYLOAD']);
                    self.L.error("_processKafkaData::_processKafkaData", "Failed to parse recents data topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("_processKafkaData::_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp );
            }
        });

        self.L.log('_processKafkaData::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, chunkSize, self.processCustId.bind(self), async (err) => {

            self.kafkaConsumerChecks.findOffsetDuplicates("UserScoreIngestionConsumer", records,topic , partition);

            if(err) {
                self.L.error("_processKafkaData::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }else{

                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                
                    recordsToProcess = [];
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:USER_SCORE_INGESTION_CONSUMER", "TIME_TAKEN:" + executionTime, `PARTITION:${partition}`]);
    
                    setTimeout(() => {
                        // Resume consumer now
                        return cb();
                    }, self.kafkaResumeTimeout);
                
            }
        });
    }

    async processCustId(custIdData, done) {

        let self = this,
            category = custIdData["service"],
            cust_id = custIdData["customer_id"],
            paytype = custIdData["paytype"],
            score = custIdData["score"];

        self.L.log('processCustId::', `processing ${JSON.stringify(custIdData)}`);

        if(!cust_id || !category || _.isEmpty(category) || !paytype || _.isEmpty(paytype) || !score) {
            self.L.error("processCustId:: ","error while processing record:",JSON.stringify(custIdData));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR', "TYPE:USER_SCORE_CSV_INGESTOR_PAYLOAD_PROCESSING", "ERROR:INVALID_PAYLOAD"]);
            return done();
        }

        await self.processCategoryRecords(cust_id, category, paytype, score);
        done();
    }

    async addCustomerScoreDetailsInCustomerScoringTable(cust_id, category, paytype, score, bucket) {
        let self = this,
            updated_at = MOMENT().format(DATE_FORMAT),
            created_at = null,
            dwhPayload = {
                customer_id	: cust_id,
                service	: category,
                paytype,
                score,	
                bucket: bucket,			
                updated_at
            };
        try {
            let params = [cust_id, category, paytype, "dummpy_operator", "dummpy_recharge_number", score, bucket, updated_at];
            let userScoreTableRecord = await self.cassandraBills.getCustomerScoreDetailsFromCustomerScoringTable(cust_id, category, paytype);
            if(userScoreTableRecord.length == 0) {
                created_at = updated_at;
                _.set(dwhPayload, 'created_at', created_at);
            }
            await self.cassandraBills.addCustomerScoreDetailsInCustomerScoringTable(params, created_at);
            await self.publishToDwhKafka(dwhPayload);
        } catch(error){
            self.L.error('addCustomerScoreDetailsInCustomerScoringTable::', `error while processing record for cust_id:${cust_id}, categor:${category}, paytype:${paytype}. ERROR: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "REASON:ERROR_WHILE_INSERTING_RECORD_IN_CUST_SCORE_TABLE", "FUNCTION_NAME:addCustomerScoreDetailsInCustomerScoringTable"]);
        }
    }

    async processCategoryRecords(custId, category, payType, score) {
        let self = this;

        let {bucket, nbfdHours, thresholdScore, error} = self.userScoreHelper.getUserTier(category, payType, score);
        if(error == "invalid config") {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR', "ERROR:TIER_NOT_FOUND", "FUNCTION_NAME:processCategoryRecords"]);
            self.L.error('processActiveSqlRecords::', `bucket not found for cust_id:${custId} category:${category}, paytype:${payType}}`);
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: custId, service: category, paytype: payType, score},
                errorInfunction: "processCategoryRecords"
            })
            return;
        }
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSING', "FUNCTION_NAME:processCategoryRecords", `category:${category}`, `paytype:${payType}`, `customer_bucket:${bucket}`]);
        // await self.addCustomerScoreDetailsInCustomerScoringTable(custId, category, payType, score, bucket);


            await self.processSQLRecords(custId, category, payType, score, bucket, nbfdHours, thresholdScore);
            await self.migrateCassandraRecords(custId, category, payType, score, bucket, nbfdHours, thresholdScore)

        // self.migrateArchivedRecords(custIdData, category)
    }

    async processSQLRecords(custId, category, payType, score, bucket, nbfdHours, thresholdScore) {
        
        let self = this;

        /*
            archival 
                if(status == deleted && (notification status == 0 || bills_status == 7))
                    don't migrate
                else if(status == delete)
                    insert in bills_x table with nbfd = moment()

            update nbfd
                if(status == active)
        */
        self.L.info('processSQLRecords::', `processing record for cust_id: ${custId} category: ${category}`);

        let activeRecords = [], inActiveRecords = [];
        try {
            let records = await self.cassandraBills.get_cust_id_rn_mapping_records(custId, category);
            console.log(`processSQLRecords:: records from custIdRnMapping are ${JSON.stringify(records)} and their length is ${records.length}`);

            //handling user agent
            if(records.length > _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'COMMON', 'USER_AGENT_LIMIT'], 600)) {
                self.L.error('processSQLRecords::', `UserAgent found. Not processing record for cust_id:${custId} category:${category} paytype:${payType}.`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "REASON:USER_AGENT_FOUND", "FUNCTION_NAME:processSQLRecords"]);
                return;
            }

            for(let element of records) {
                let meta = null;
                try{    
                    meta = JSON.parse(element['meta']);
                } catch(error) {
                    self.L.error('processSQLRecords::', `Unable to parse meta. Not processing record for cust_id:${custId}, category:${category}, paytype:${payType}, recharge_number:${element['recharge_number']} and operator:${element['operator']}.`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "REASON:META_PARSING_ERROR", "FUNCTION_NAME:processSQLRecords"]);
                    continue;
                }
                if(element['paytype'] != payType || _.get(meta,'user_type',null) == null || _.get(meta,'user_type',null).toLowerCase() == "NONRU".toLowerCase()) continue;
                if(element['status'] == 'ACTIVE') {
                    activeRecords.push(element);
                } else if(element['status'] == 'DELETED') {
                    if(meta['deleted_bills_status'] == 7 || meta['deleted_bills_notification_status'] == 0) {
                        self.L.error('processSQLRecords::', `Inactive Record. Not processing record for cust_id:${custId}, category:${category}, paytype:${payType}, recharge_number:${element['recharge_number']} and operator:${element['operator']}.`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "REASON:INACTIVE_RECORD", "FUNCTION_NAME:processSQLRecords"]);
                        continue;
                    } else {
                        inActiveRecords.push(element);
                    }
                }
            }
            self.L.info('processSQLRecords::', `inActiveRecords: ${inActiveRecords.length}, activeRecords:${activeRecords.length} for cust_id:${custId}, category:${category}, paytype:${payType}`);
            if(activeRecords.length > 0) await self.processActiveSqlRecords([custId, activeRecords, category, payType, score, bucket, nbfdHours, thresholdScore]);
            if(inActiveRecords.length > 0) await self.processArchivedRecords([custId, inActiveRecords, category, payType, score, bucket, nbfdHours, thresholdScore]);
        } catch (error) {
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: custId, service: category, paytype: payType, score, bucket, nbfdHours, thresholdScore},
                errorInfunction: "processSQLRecords"
            })
            self.L.error('processSQLRecords::', `error while processing record for cust_id:${custId} category:${category} paytype:${payType}. ERROR: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "REASON:ERROR_IN_PROCESSING_IN_LOOP", "FUNCTION_NAME:processSQLRecords"]);
        }
        
    }

    async processActiveSqlRecords([custId, records, category, payType, userScore, bucket, nbfdHours, thresholdScore]) {
        
        let self = this;
        console.log(`inside processActiveSqlRecords function...`);

        let category_paytype_config = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'CATEGORIES_PAYTYPES_ALLOWED_FOR_PROCESSING', 'ACTIVE_SQL_RECORDS',`${self.userScoreHelper.stringToLowerAndFormat(category)}_${self.userScoreHelper.stringToLowerAndFormat(payType)}`], null); // {mobile_postpaid: "active", electricity_postpaid: "active"}
        if(category_paytype_config == null || category_paytype_config != "active")  
        {
            self.L.error("processActiveSqlRecords:: ","category and paytype not supported for record:",JSON.stringify({custId, category, payType}));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:SKIPPING_UPDATING_NBFD", "REASON:CATEGORY_PAYTYPE_NOT_SUPPORTED", `CATEGORY:${category}`, `PAYTYPE:${payType}`,"FUNCTION_NAME:processActiveSqlRecords"]);
            return;
        }
        for(let cassandraRecord of records) {

            try {

                console.log(`processActiveSqlRecords:: inside for loop try block`);

                // fetching bills_x table record
                let sqlRecord = await self.bills.getRecordByRechargeNumberCustIdFromBillsTable({
                    rechargeNumber : cassandraRecord["recharge_number"],
                    operator: cassandraRecord["operator"],
                    customerID: cassandraRecord["customer_id"]
                })

                console.log(`processActiveSqlRecords:: sqlRecord fetched from bills table are ${JSON.stringify(sqlRecord)}`);
                let record = sqlRecord.length > 0 ? sqlRecord[0] : null;

                let tableName;

                if(record != null) {
                   tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record["operator"]], null);
                }

                if(sqlRecord.length == 0) {
                    console.log(`processActiveSqlRecords:: inside first if-else block`);

                    if(userScore < thresholdScore) {

                        // will not do anything as user score is less than threshold
                        self.L.log('processActiveSqlRecords::', `Low priority customer. Skipping non sql record insertion in db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED', "ACTION:SKIPPING_INSERTING_NON_SQL_RECORDS_IN_DB", "REASON:LOW_PRIORITY_CUSTOMER","FUNCTION_NAME:processActiveSqlRecords"]);
                        continue;
                    } else {
                        
                        // onboarding record into the system.
                        self.L.log('processActiveSqlRecords::', `inserting non sql record in db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:INSERTING_NON_SQL_RECORDS_IN_DB"]);
                        
                        let dbParams = {
                            recharge_number : cassandraRecord['recharge_number'], 
                            customer_id : cassandraRecord['customer_id'], 
                            product_id: cassandraRecord['product_id'], 
                            operator: cassandraRecord['operator'], 
                            paytype: cassandraRecord['paytype'], 
                            service: cassandraRecord['service'], 
                            notification_status: 1,
                            customer_bucket: bucket,
                            user_score: userScore,
                            status : 0
                        }

                        await self.onboardRecordInSql(dbParams, null, "nonSqlRecords");

                        if(cassandraRecord['status'] == "DELETED") {
                            let query = 'set status = ? ';
                            let params = ['ACTIVE'];
                            self.L.log('processActiveSqlRecords::', `updating record in cassandra db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}`);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:UPDATING_USER_IN_CASSANDRA_DB"]);
                            await self.cassandraBills.update_cust_id_rn_mapping_record(cassandraRecord['customer_id'], cassandraRecord['service'], cassandraRecord['recharge_number'], cassandraRecord['operator'], params, query);
                        }

                    }
                } else if(userScore < thresholdScore) {
                    console.log(`processActiveSqlRecords:: inside second if-else block`);
                    
                    // delete record from system
                    // cron will automatically delete records with status 13

                    let query = `update ${tableName} set status = ? where id = ?`,
                    queryParams = [13, record.id];
                    
                    self.L.log('processActiveSqlRecords::', `deleting record from db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}`);
                    await self.bills.updateBillsTable(query,queryParams);

                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED', "ACTION:DELETING_USER_FROM_SYSTEM","REASON:USER_SCORE_LESS_THAN_THRESHOLD_SCORE"]);

                } else if(record.status == 7 || record.notification_status == 0) {
                    console.log(`processActiveSqlRecords:: inside third if-else block`);
                    
                    // skipping in_active / notification snoozed user
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED', "ACTION:SKIPPING_INACTIVE_NOTIFICATION_SNOOZED_USER"]);
                    self.L.info('processActiveSqlRecords::', `skipping record for cust_id:${custId}, categor:${category}, paytype:${payType}, recharge_number:${record['recharge_number']} table:${tableName} as notificationStatus:${record.notification_status} and status:${record.status}`);
                }else {

                    // updates nbfd in the bills_x table
                    console.log(`processActiveSqlRecords:: inside last if-else block`);

                    let update_next_bill_fetch_date = true,
                        {extra, newDBRecordNbfdAsPerUserScore} = self.calculateNBFD(record, nbfdHours);

                    if(newDBRecordNbfdAsPerUserScore == undefined) {
                        try {
                            extra = _.get(record, 'extra', null) == null ? {} : JSON.parse(record['extra']);
                        } catch(err) {
                            self.L.error("processActiveSqlRecords :: ",`error while parsing for cust_id:${custId} recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}, categor:${category}, extra:: `,extra);
                            extra = {}
                        }
                        if( _.get(extra,'user_score',null) == userScore && record['customer_bucket'] == bucket) {
                            self.L.log("processActiveSqlRecords :: ", `skipping this record as we record already processed in the current cycle for cust_id:${custId} recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}, categor:${category},`);
                            continue;
                        }
                        newDBRecordNbfdAsPerUserScore = record['next_bill_fetch_date'];
                        _.set(extra,'updated_source','USER_SCORE_INGESTION_CONSUMER_sqlRecords');
                        _.set(extra,'updated_data_source','USER_SCORE_INGESTION_CONSUMER_sqlRecords');
                        update_next_bill_fetch_date = false;
                    }
                    _.set(extra,'user_score',userScore);
                    let query = `update ${tableName} set customer_bucket = ?, extra = ? ${update_next_bill_fetch_date ? ", next_bill_fetch_date = ?" : ""} where id = ?`,
                        queryParams = [bucket, JSON.stringify(extra)];

                    if(update_next_bill_fetch_date == true) queryParams.push(newDBRecordNbfdAsPerUserScore.format(DATE_FORMAT));

                    queryParams.push(record['id']);

                    self.L.info('processActiveSqlRecords::',`updating record in sql DB. query: ${query}, params: ${queryParams}`);
                    await self.bills.updateBillsTable(query,queryParams);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED', "ACTION:UPDATED_NBFD_IN_DB",`category:${category}`, `paytype:${payType}`, `customer_bucket:${bucket}`]);
                }

            } catch(err) {
                await self.publishToInValidRecordsKafka({
                    payload: cassandraRecord,
                    errorInfunction: "processActiveSqlRecords"
                })
                self.L.error('processActiveSqlRecords::', `error while processing record for cust_id:${custId} recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}, categor:${category}, paytype:${payType}. ERROR: ${err}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "REASON:ERROR_IN_PROCESSING_IN_LOOP", "FUNCTION_NAME:processActiveSqlRecords"]);
                        
            }
        }
    }

    async processArchivedRecords([custId, inActiveRecords, category, payType, score, bucket, nbfdHours, thresholdScore]) {
        let self = this;

        let category_paytype_config = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'CATEGORIES_PAYTYPES_ALLOWED_FOR_PROCESSING', 'ARCHIVED_RECORDS',`${self.userScoreHelper.stringToLowerAndFormat(category)}_${self.userScoreHelper.stringToLowerAndFormat(payType)}`], null); // {mobile_postpaid: "active", electricity_postpaid: "active"}
        if(category_paytype_config == null || category_paytype_config != "active")  
        {
            self.L.error("processArchivedRecords:: ","category and paytype not supported for record:",JSON.stringify({custId, category, payType}));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:SKIPPING_MIGRATING", "REASON:CATEGORY_PAYTYPE_NOT_SUPPORTED", `CATEGORY:${category}`, `PAYTYPE:${payType}`,"FUNCTION_NAME:processArchivedRecords"]);
            return;
        }

        for(let cassandraRecord of inActiveRecords) {
            try{    
                if(score < thresholdScore) {

                    // will not do anything as user score is less than threshold
                    self.L.log('processArchivedRecords::', `Low priority customer. Skipping record insertion in db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED', "ACTION:SKIPPING_INSERTING_IN_DB", "REASON:LOW_PRIORITY_CUSTOMER", `CATEGORY:${category}`, `PAYTYPE:${payType}`,"FUNCTION_NAME:processArchivedRecords"]);
                    continue;
                } else {
                    
                    // onboarding record into the system.
                    let meta = null;

                    try{    
                        meta = JSON.parse(cassandraRecord['meta']);
                    } catch(error) {
                        self.L.error('processArchivedRecords::', `Unable to parse meta. Not processing record for cust_id:${custId}, category:${category}, paytype:${payType}, recharge_number:${cassandraRecord['recharge_number']} and operator:${cassandraRecord['operator']}.`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "REASON:META_PARSING_ERROR", "FUNCTION_NAME:processArchivedRecords"]);
                        continue;
                    }
                    
                    self.L.log('processArchivedRecords::', `inserting record in db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:INSERTING_USER_IN_SYSTEM"]);
                    
                    let dbParams = {
                        recharge_number : cassandraRecord['recharge_number'], 
                        customer_id : cassandraRecord['customer_id'], 
                        product_id: cassandraRecord['product_id'], 
                        operator: cassandraRecord['operator'], 
                        paytype: cassandraRecord['paytype'], 
                        service: cassandraRecord['service'], 
                        notification_status: 1,
                        customer_bucket: bucket,
                        user_score: score,
                        status : 0
                    }

                    await self.onboardRecordInSql(dbParams, null, "archival");

                    if(cassandraRecord['status'] == "DELETED") {
                        let query = 'set status = ?, updated_at = ? ';
                        let params = ['ACTIVE',MOMENT().format(DATE_FORMAT)];
                        self.L.log('processArchivedRecords::', `updating record in cassandra db for cust_id:${cassandraRecord['customer_id']}, categor:${category}, paytype:${payType}, recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:UPDATING_USER_IN_CASSANDRA_DB"]);
                        await self.cassandraBills.update_cust_id_rn_mapping_record(cassandraRecord['customer_id'], cassandraRecord['service'], cassandraRecord['recharge_number'], cassandraRecord['operator'], params, query);
                    }

                }
            } catch(err) {
                await self.publishToInValidRecordsKafka({
                    payload: cassandraRecord,
                    errorInfunction: "processArchivedRecords"
                })
                self.L.error('processArchivedRecords::', `Error for cust_id:${cassandraRecord['customer_id']} recharge_number:${cassandraRecord["recharge_number"]}, operator:${cassandraRecord['operator']}`,err);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR', "FUNCTION_NAME:processArchivedRecords"]);
            }
        }
    }

    async onboardRecordInSql(record, tableName = null, migratedFromSource, next_bill_fetch_date = MOMENT().format(DATE_FORMAT), actual_nbfd= null) {
        next_bill_fetch_date = (next_bill_fetch_date == null ? MOMENT().format(DATE_FORMAT) : next_bill_fetch_date);
        let self = this;
        tableName = tableName == null ? _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record["operator"]], null) : tableName;
        if(tableName == null) {
            self.L.error('onboardRecordInSql::', `tablename not found for cust_id:${record['customer_id']}, service:${record['service']}, paytype:${record['paytype']}, recharge_number:${record["recharge_number"]}, operator:${record['operator']}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR', "ERROR:TABLENAME_NOT_FOUND", "FUNCTION_NAME:onboardRecordInSql",`MIGRATED_FROM_SOURCE:${migratedFromSource}`]);
            return;
        }
        let recharge_number = record['recharge_number'],
            customer_id = record['customer_id'],
            product_id = record['product_id'],
            operator = record['operator'],
            paytype = record['paytype'],
            service = record['service'],
            status = record['status'] ? record['status'] : 0,
            notification_status = record['notification_status'] != null ? record['notification_status'] : 1,
            customer_bucket = record['customer_bucket'],
            extra = JSON.stringify({user_type:"RU",created_source:`USER_SCORE_INGESTION_CONSUMER_${migratedFromSource}`,updated_source:`USER_SCORE_INGESTION_CONSUMER_${migratedFromSource}`,updated_data_source:`USER_SCORE_INGESTION_CONSUMER_${migratedFromSource}`,user_score_actual_nbfd:actual_nbfd != null ? actual_nbfd : next_bill_fetch_date, user_score_nbfd_updatedAt: MOMENT().format(DATE_FORMAT), user_score: record['user_score'], user_migrated_from_source:migratedFromSource}),
            additional_query = "",
            additional_query_params = [];
        if(migratedFromSource == "nonRU") {
            
            // adding amount
            if(record['due_amount']) {
                additional_query = additional_query + ", amount";
                additional_query_params.push(record['due_amount']);
            }

            // adding bill_date
            if(record['bill_date']) {
                additional_query = additional_query + ", bill_date";
                additional_query_params.push(self.userScoreHelper.parseDateString(record['bill_date']));
            }

            // adding bill_fetch_date
            if(record['bill_fetch_date']){
                additional_query = additional_query + ", bill_fetch_date";
                additional_query_params.push(self.userScoreHelper.parseDateString(record['bill_fetch_date']));
            }

            // adding circle
            if(record['circle']) {
                additional_query = additional_query + ", circle";
                additional_query_params.push(record['circle']);
            }

            // adding due_date
            if(record['due_date']) {    
                additional_query = additional_query + ", due_date";
                additional_query_params.push(self.userScoreHelper.parseDateString(record['due_date']));
            }

            // adding payment_date
            if(record['payment_date']){    
                additional_query = additional_query + ", payment_date";
                additional_query_params.push(self.userScoreHelper.parseDateString(record['payment_date']));
            }

            // adding remind_later_date
            if(record['remind_later_date']){    
                additional_query = additional_query + ", remind_later_date";
                additional_query_params.push(self.userScoreHelper.parseDateString(record['remind_later_date']));
            }

            let dbExtra = JSON.parse(_.get(record,['extra'],'{}'));
            if(_.get(dbExtra,'source_subtype_2', null) != null) _.set(extra,'source_subtype_2', _.get(dbExtra,'source_subtype_2', null));
            if(_.get(dbExtra,'recon_id', null) != null) _.set(extra,'recon_id', _.get(dbExtra,'recon_id', null));
        }
        let query = `insert into ${tableName} (recharge_number, customer_id, product_id, operator, next_bill_fetch_date, paytype, service, notification_status, extra, customer_bucket, status ${additional_query}) values ?`,
        queryParams = [[[recharge_number, customer_id, product_id, operator, next_bill_fetch_date, paytype, service, notification_status, extra, customer_bucket, status, ...additional_query_params]]];
        await self.bills.updateBillsTable(query,queryParams);
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:SUCCESSFULLY_INSERTED_IN_DB', `category:${service}`, `paytype:${paytype}`, `customer_bucket:${customer_bucket}`, `user_migrated_from_source:${migratedFromSource}`,"FUNCTION_NAME:onboardRecordInSql"]);
        self.L.log('onboardRecordInSql::', `Record successfully inserted for cust_id:${record['customer_id']}, service:${record['service']}, paytype:${record['paytype']}, recharge_number:${record["recharge_number"]}, operator:${record['operator']}`);
    }

    calculateNBFD(record, nbfdHours) {
        let self = this;
        
        let now = MOMENT().startOf('second'),
            dbNbfd = self.parseDate(record['next_bill_fetch_date']) == null ? now : self.parseDate(record['next_bill_fetch_date']),
            dbRecordExtra = JSON.parse(_.get(record,['extra'],"{}")),
            actualNbfd = _.get(dbRecordExtra,'user_score_actual_nbfd',null) != null ? MOMENT(_.get(dbRecordExtra,'user_score_actual_nbfd',null), DATE_FORMAT).startOf('second') : null,
            billFetchDate = _.get(record, "published_date", null) != null? self.parseDate(_.get(record, "published_date", null)) : null,
            updateNbfd = null,
            todayStartOfDay = MOMENT().startOf('day'),
            todayEndOfDay = MOMENT().endOf('day'),
            tomorrowStartOfDay = MOMENT().add(1,'day').startOf('day'),
            updated_source = 'USER_SCORE_INGESTION_CONSUMER_sqlRecords',
            calculated_nbfd = null;

        if(actualNbfd == null) {
            self.L.log('calculateNBFD::', `nbfd not found in extra for cust_id:${record['customer_id']} recharge_number:${record["recharge_number"]}, operator:${record['operator']}`);
            actualNbfd = MOMENT(dbNbfd);
            calculated_nbfd = MOMENT(dbNbfd).add(nbfdHours,'hour')
            updateNbfd = true;
        } else {
            calculated_nbfd = MOMENT(actualNbfd).add(nbfdHours, 'hour');
            if(calculated_nbfd.diff(dbNbfd) == 0 || nbfdHours == null || nbfdHours == 0) {
                updateNbfd = false;
            } else{
                updateNbfd = true;
            }
        }
        if (updateNbfd) {
            if(calculated_nbfd.isBefore(todayStartOfDay)) {
                if(billFetchDate == null) {
                    calculated_nbfd = now;
                } else if(actualNbfd.isAfter(todayEndOfDay) && billFetchDate.isSameOrAfter(todayStartOfDay)) {
                    calculated_nbfd = tomorrowStartOfDay
                } else if(actualNbfd.isAfter(todayEndOfDay) && billFetchDate.isBefore(todayStartOfDay)) {
                    calculated_nbfd = now;
                } else if(actualNbfd.isAfter(todayStartOfDay) || todayStartOfDay.diff(actualNbfd) == 0) {
                    calculated_nbfd = actualNbfd;
                } else if(actualNbfd.isBefore(todayStartOfDay)) {
                    calculated_nbfd = now;
                    actualNbfd = now;
                }
            }
            _.set(dbRecordExtra,'updated_source',updated_source);
            _.set(dbRecordExtra,'updated_data_source',updated_source);
            _.set(dbRecordExtra,'user_score_actual_nbfd', actualNbfd.format(DATE_FORMAT));
            _.set(dbRecordExtra,'user_score_nbfd_updatedAt',now.format(DATE_FORMAT));

            return {extra: dbRecordExtra, newDBRecordNbfdAsPerUserScore: calculated_nbfd};
        }
        else {
            return {};
        }
        
    }

    parseDate(date) {
        let self = this;
        let nbfd = null;
        if(MOMENT(date,MOMENT.ISO_8601).isValid()) {
            let tempDate = MOMENT(date,MOMENT.ISO_8601).utc().startOf('second').format(DATE_FORMAT);
            nbfd = MOMENT(tempDate,DATE_FORMAT);
        } else if(MOMENT(date,DATE_FORMAT).isValid()) {
            nbfd = MOMENT(date,DATE_FORMAT).startOf('second');
        } else if(date == null || date == '') {
            nbfd = null;
        } else {
            nbfd = null;
            self.L.error("parseDate:: ","invalid date format:",date);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:ERROR',  "ACTION:parseDate", "REASON:INVALID_DATE_FORMAT"]);
        }
        return nbfd;
    }

    async migrateCassandraRecords(custId, category, payType="postpaid", score, bucket, nbfdHours, thresholdScore) {
        let self = this;
        let category_paytype_config = _.get(self.config, ['DYNAMIC_CONFIG', 'USER_SCORE_INGESTOR', 'CATEGORIES_PAYTYPES_ALLOWED_FOR_PROCESSING', 'NON_PAYTM_RECORDS',`${self.userScoreHelper.stringToLowerAndFormat(category)}_${self.userScoreHelper.stringToLowerAndFormat(payType)}`], null); // {mobile_postpaid: "active", electricity_postpaid: "active"}
        if(category_paytype_config == null || category_paytype_config != "active")  
        {
            self.L.error("migrateCassandraRecords:: ","category and paytype not supported for record:",JSON.stringify({custId, category, payType}));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED',  "ACTION:SKIPPING_MIGRATING", "REASON:CATEGORY_PAYTYPE_NOT_SUPPORTED", `CATEGORY:${category}`, `PAYTYPE:${payType}`,"FUNCTION_NAME:migrateCassandraRecords"]);
            return;
        }
        if(score < thresholdScore) {
            // will not do anything as user score is less than threshold
            self.L.log('migrateCassandraRecords::', `Low priority customer. Skipping non paytm record migration to RU for cust_id:${custId}, categor:${category}, paytype:${payType}}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 'STATUS:PROCESSED', "ACTION:SKIPPING_MIGRATING", "REASON:LOW_PRIORITY_CUSTOMER", `CATEGORY:${category}`, `PAYTYPE:${payType}`,"FUNCTION_NAME:migrateCassandraRecords"]);
            return;
        }
        try{
            let records = await self.fetchRecordsFromCustIdRechargeNumberMapping(custId, category, payType); // fetching records from bills_non_paytm using bills_recent_records
            if(records.length == 0) {
                self.L.error('migrateCassandraRecords::', `0 records found for cust_id:${custId} categor:${category}, paytype:${payType}}`);
                return;
            }
            for(let record of records) {
                try{
                    // if(!_.get(recentBillsData, 'isCreditCardOperator', null) && 
                    //     !self.serviceAllowedDeleteNonPaytm.includes(_.toLower(recentBillsData.service))
                    // ){
                    //     self.L.error('migrateCassandraRecords::', `not migrating for cust_id:${custId} categor:${category}, paytype:${payType}}, operator: ${record['operator']}`);
                    //     continue;
                    // }    
                    if(self.userScoreHelper.stringToLowerAndFormat(record['service']) == "mobile") {
                        record["recharge_number"] = self.cryptr.decrypt(record["recharge_number"]);
                    }
                    let payload = {
                        customerId: custId,
                        service: category,
                        paytype: payType,
                        productId: record['product_id'],
                        operator: record['operator'],
                        rechargeNumber: record["recharge_number"],
                        dbEvent: 'delete'
                    }
                    await self.publishToBillsNonPaytmKafka(payload);    //this will delete records from non paytm as we are publishing delete event to non Paytm
                    // let recordToBeInsertedInSql = _.cloneDeep(record);
                    let actual_nbfd = null;
                    if(record['next_bill_fetch_date'] != null){    
                        let {extra, newDBRecordNbfdAsPerUserScore} = self.calculateNBFD(record, nbfdHours);
                        if(newDBRecordNbfdAsPerUserScore != undefined) {
                            actual_nbfd = _.get(extra,'user_score_actual_nbfd');
                            record['next_bill_fetch_date'] = newDBRecordNbfdAsPerUserScore.format(DATE_FORMAT);
                        }
                    }
                    record['customer_bucket'] = bucket;
                    record['user_score'] = score;
                    record['status'] = 0;
                    record['notification_status'] = 1;
                    await self.onboardRecordInSql(record, null, "nonRU", record['next_bill_fetch_date'], actual_nbfd);
                } catch(err) {
                    self.L.error('migrateCassandraRecordsLoop::', `Error for cust_id:${custId} categor:${category}, paytype:${payType}, recharge_number:${record["recharge_number"]}}. Error: ${err}`);
                    await self.publishToInValidRecordsKafka({
                        payload: record,
                        errorInfunction: 'migrateCassandraRecordsInnerLoop'
                    })
                    continue;
                }
            }
        } catch(e) {
            self.L.error('migrateCassandraRecords::', `Error for cust_id:${custId} categor:${category}, paytype:${payType}}. Error: ${e}`);
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: custId, service: category, paytype: payType, score, bucket, nbfdHours, thresholdScore},
                errorInfunction: 'migrateCassandraRecords'
            })
        }

    }

    fetchRecordsFromCustIdRechargeNumberMapping(custId, category, payType) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.nonPaytmBills.readRecentRecords({customerId: custId, service: category, paytype:payType}, (err, records) => {
                if(err){
                    reject(err);
                } else {
                    resolve(records);
                }
            })
        })
    }

    publishToDwhKafka(payload) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.reminderKafkaProducer.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:USER_SCORE_INGESTION_PUBLISHER",
                        "TOPIC:USER_SCORE_INGESTOR",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.critical('nonPaytmKafkaPublisher :: publishNonRuBillFetch', 'Error while publishing message in Kafka on TOPIC USER_SCORE_INGESTOR - MSG:- ' + JSON.stringify(payload), error);
                    resolve();
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:USER_SCORE_INGESTION_PUBLISHER",
                        "TOPIC:USER_SCORE_INGESTOR",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.log('nonPaytmKafkaPublisher :: publishNonRuBillFetch', 'Message published successfully in Kafka', ' on topic USER_SCORE_INGESTOR', JSON.stringify(payload));
                    resolve();
                }
            })
        })
    }

    publishToBillsNonPaytmKafka(payload) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.nonPaytmKafkaProducer.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:NON_PAYTM_RECORDS_PUBLISHER",
                        "TOPIC:NON_PAYTM_RECORDS",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.critical('nonPaytmKafkaPublisher :: publishNonRuBillFetch', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    reject("Error while publishing message in Kafka");
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:NON_PAYTM_RECORDS_PUBLISHER",
                        "TOPIC:NON_PAYTM_RECORDS",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.log('nonPaytmKafkaPublisher :: publishNonRuBillFetch', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', JSON.stringify(payload));
                    resolve();
                }
            })
        })
    }

    publishToInValidRecordsKafka(payload) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.userScoreIngestorFailedRecordsKafkaProducer.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR_FAILED_RECORDS.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:USER_SCORE_INGESTOR_FAILED_RECORDS_PUBLISHER",
                        "TOPIC:USER_SCORE_INGESTOR_FAILED_RECORDS",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.critical('publishToInValidRecordsKafka :: publishToInValidRecordsKafka', 'Error while publishing message in Kafka on topic USER_SCORE_INGESTOR_FAILED_RECORDS - MSG:- ' + JSON.stringify(payload), error);
                    resolve();
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:USER_SCORE_INGESTOR_FAILED_RECORDS_PUBLISHER",
                        "TOPIC:USER_SCORE_INGESTOR_FAILED_RECORDS",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.log('publishToInValidRecordsKafka :: publishToInValidRecordsKafka', 'Message published successfully in Kafka', ' on topic USER_SCORE_INGESTOR_FAILED_RECORDS', JSON.stringify(payload));
                    resolve();
                }
            })
        })
    }

    suspendOperations(){
        let self        = this,
        deferred = Q.defer();
        self.L.log(`userScoreIngestionConsumer::suspendOperations kafka consumer shutdown initiated`);
    
        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`userScoreIngestionConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`userScoreIngestionConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`userScoreIngestionConsumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`userScoreIngestionConsumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
    
}

export default UserScoreIngestor;