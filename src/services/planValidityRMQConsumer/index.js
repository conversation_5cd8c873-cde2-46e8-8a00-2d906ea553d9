import RQ from 'rqueue';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator';
import MOMENT from 'moment';
import REQUEST from 'request';
import _ from 'lodash';
import utility from '../../lib';
import processD2hPVStrategy from './d2hStrategy'

class PlanValidityRMQC {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.processDthPVStrategy = new processD2hPVStrategy(options,this);
    }

    start() {
        let self = this;
        self.L.log("start", "configuring PlanValidityRMQConsumer");

        // configure Kafka Publisher to push plan_validity_expiry events to Kafka
        self._configureKafkaPublisher(function (error) {
            if (error) {
                self.L.critical('PlanValidityRMQConsumer :: start', 'Error while configuring Kafka Publisher...', error);
            } else {
                self.L.log('PlanValidityRMQConsumer :: start', 'Kafka Publisher configured....');
                // Now configure RMQ subscriber...
                return self._configureSubscriber();
            }
        });
    }

    _configureKafkaPublisher(cb) {
        let
            self = this;
        /**
         * Kafka Publisher to publish bill fetch to PLAN VALIDITY
         */
        self.kafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_SYNC_DB.HOSTS
        });
        self.kafkaPublisher.initProducer('high', function (error) {
            return cb(error);
        });
    }

    _configureSubscriber() {
        let self = this,
            ex = {
                NAME: _.get(self.config, 'RABBITMQ_CONFIG.DTH_EXCHANGE_NAME', 'ex_gw_to_reminder'),
                TYPE: _.get(self.config, 'RABBITMQ_CONFIG.CLM_EXCHANGE_TYPE', 'topic'),
                OPTIONS: {
                    durable: true,
                    internal: false,
                    autoDelete: false
                }
            },
            queue = {
                NAME: _.get(self.config, 'RABBITMQ_CONFIG.PLAN_VALIDITY_RMQ_CONSUMER', ''),
                // TODO: SET BINDINGkEY
                BINDINGKEY: _.get(self.config, 'RABBITMQ_CONFIG.DTH_BINDING_KEY', 'gateway.callback.reminder.*'),
                OPTIONS: {
                    exclusive: false,
                    durable: true,
                    autoDelete: false
                },
                CONSUMEOPTS: {
                    noAck: false,
                },
                PREFETCH: _.get(self.config, 'RABBITMQ_CONFIG.PREFETCH_MSG_COUNT', 20)
            };

        let rabbitConfig = _.get(self.config, 'RABBITMQ_CONFIG.RABBITMQ', null);
        self.fetchValidityConsumer = new RQ({ RABBITMQ: rabbitConfig }).getSimpleSubscriber(ex, queue);

        self.L.log("_configureSubscriber", "fetchValidityConsumer Configured");
        self.startDummyLogs();

        self.fetchValidityConsumer.on('message', function (data) {
            setTimeout(() => {
                self.processMessage(data);
            }, 500)
        });

        self.fetchValidityConsumer.start({}, function () {
            L.log("_configureSubscriber", "PlanValidityRMQConsumer started");
        });
    }

    _ackMessage(message) {
        let self = this;
        try {
            self.fetchValidityConsumer.ack(message);
        } catch (err) {
            L.critical('billSubscriber::_ackMessage', 'Error while ack message.', err);
        }
    }

    /*
   {"reqType": "VALIDATION", "catalogProductID": "63786652", 
    "productInfo": { "operator": "dishtv", "service": "dth", "paytype": "prepaid" },
    "customerDataResponse": {
        "rechargeNumber": "aqw123d", "amount": 100,
        "validityExpiryDate": "2021-12-12 08:00:00"
    }, "deducedStatus": true}
    */

    async processMessage(message) {
        let self = this;
        try {
            let data = JSON.parse(VALIDATOR.toString(_.get(message, 'content')));
            let record = self.getRecord(data);
            let isRecordValid = self.checkValidity(record);
            if (!isRecordValid) {
                return self._ackMessage(message);
            }
            self.L.log("PlanValidityRMQConsumer:: processMessage:: ", 'Processing Record ', record.debugKey);
            await self.executeStrategy(record);
            this._ackMessage(message);
            return;
        }
        catch (err) {
            this.L.critical('processMessage:: ', err);
            this._ackMessage(message);
            return;
        }
    }

    startDummyLogs() {
        let self = this;

        setInterval(()=> {
            self.L.log('Dummy logs for RMQ consumer');
        }, 30000);
    }

    getRecord(message) {
        let record = {
            timestamp_init: _.get(message, 'timestamp_init'),
            operator: _.get(message, 'productInfo.operator', ''),
            service: _.get(message, 'productInfo.service', ''),
            paytype: _.get(message, 'productInfo.paytype', ''),
            rechargeNumber: _.get(message, 'customerDataResponse.rechargeNumber', ''),
            amount: _.get(message, 'customerDataResponse.amount', 0),
            validityExpiryDate: _.get(message, 'customerDataResponse.validityExpiryDate', ''),
            mobileNumber: _.get(message, 'customerDataResponse.MobileNo', ''),
            userFlag: _.get(message, 'customerDataResponse.Flag', ''),
            productId: _.get(message, 'product_id', ''), // product_id value will be populated in coming future
            customerId: null,
        };

        record.debugKey = 'rech_num:' + record.rechargeNumber + '__operator:' + record.operator + '__paytype:' + record.paytype + '__custId:' +record.customerId;
        return record;
    }

    checkValidity(record) {
        let self = this;
        let operatorSideMandatoryParams = [ 'rechargeNumber', 'validityExpiryDate', 'amount', "productId"];
        let gatewaySideMandatoryParams = [ 'operator', 'service', 'paytype']
        let operatorSideMissingParams = operatorSideMandatoryParams.filter(param => !_.get(record, param, null));
        let gatewaySideMissingParams = gatewaySideMandatoryParams.filter(param => !_.get(record, param, null));

        if (operatorSideMissingParams.length > 0) {
            utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','REQUEST_TYPE:MISSING_PARAM_OPERATOR_SIDE', 'STATUS:ERROR']);
            self.L.log('checkValidity:: ', 'mandatory params missing from operator end ', operatorSideMissingParams, JSON.stringify(record));
            return false;
        }
        else if (gatewaySideMissingParams.length > 0) {
            utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','REQUEST_TYPE:MISSING_PARAM_GATEWAY_SIDE', 'STATUS:ERROR']);
            self.L.log('checkValidity:: ', 'mandatory params missing from gateway end', gatewaySideMissingParams, JSON.stringify(record));
            return false;
        }else if (MOMENT().diff(MOMENT(record.validityExpiryDate), 'days') > 0) {
            utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','REQUEST_TYPE:EXPIRED_VALIDITY', 'STATUS:ERROR']);
            self.L.log('checkValidity:: ', 'Expired validity_expiry_date ', JSON.stringify(record));
            return false;
        }

        return true;
    }

    executeStrategy(record) {
        let self = this;

        if (['dishtv', 'd2h (formerly videocon d2h)'].includes(record.operator) && record.paytype == 'prepaid') {
            utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','STATUS:D2H_STRATEGY']);
            return self.processDthPVStrategy.executeStrategy(record,self);
        }

        return self.defaultStrategy(record);
    }

    async defaultStrategy(record, message) {
        utility._sendMetricsToDD(1, ['SOURCE:PV_RMQ_CONSUMER','STATUS:RECORD_NOT_PROCESSED']);
        this.L.log('defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        return;
    }

    
    async publishInKafka(payload) {
        let topic = this.config.KAFKA.SERVICES.PLAN_VALIDITY_SYNC_DB.TOPIC;
        try {
            await this.kafkaPublisher.publishData([{
                topic: topic,
                messages: JSON.stringify(payload)
            }], null, [200, 800]);

            this.L.log('publishInKafka:: Message published successfully in Kafka', ` on topic ${topic}`, JSON.stringify(payload));
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_RMQ_CONSUMER', 'TOPIC:' + topic, 'STATUS:PUBLISHED', `OPERATOR:${payload.productInfo_operator}`]);
            return;
        } catch (error) {
            this.L.critical('publishInKafka:: Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_RMQ_CONSUMER', 'TOPIC:' + topic, 'STATUS:PUBLISHED_FAILED', `OPERATOR:${payload.productInfo_operator}`]);
            return;
        }
    }

    suspendOperations() {
        let
            self = this;

        self.L.info('PlanValidityRMQC :: suspendOperations', 'suspending operations');

        if (self.fetchValidityConsumer) {
            self.L.info('PlanValidityRMQC :: suspendOperations :: rmq', 'stop receiving messages');
            self.fetchValidityConsumer.cancel();
            /* Delaying the channel close so all message should acknowledged first */
            setTimeout(function () {
                self.L.info('PlanValidityRMQC :: suspendOperations :: rmq', 'closing channel and connections');
                self.fetchValidityConsumer.stop();
            }, 20 * 1000);
        }
    }
}


export default PlanValidityRMQC;;

// psedu code
/*
processMessage:
validate record
if yes: fetch customer details
if no customer details found - log, push to prometheus and discard.
transform record to publish in kafka
publish to kafka
else: log, push to promehteus and exit


fetchCustomerDetails:
select * from dth where recharge_number = '';
return customer_id, customer_mobile
else return null;

transform:
copy the structure from vi publisherConfig;

publishtokafka
 publish to kafka. log and push to prometheus.
 */