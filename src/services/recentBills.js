"use strict";

import OS from 'os'
import _ from 'lodash'
import recentBillLibrary from '../lib/recentBills'
import pgLibrary from '../lib/pg'
import BILLSUBSCRIBER from './billSubscriber'
import CATALOGVERTIC<PERSON><PERSON>CHARGE from '../models/catalogVerticalRecharge'
import MODELS from '../models'
import CassandraModel from '../models/cassandraBills'
import utility from '../lib'
import ASYNC from 'async'
import MOMENT from 'moment'
import digitalUtility from 'digital-in-util'
import Q from 'q'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import PrepaidFlowManager from '../lib/prepaidFlowManager.js';
import BILLS from '../models/bills';

import BillsLib from '../lib/bills'
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import Logger from '../lib/logger';
/**
 * Recent Bills service is used to insert recent bill data received from kafka
 * recharge topic and insert in mysql, mongo DB
 */
class RecentBills {
    /**
     * @param { object } options Contains configuration and dependencies
     * @param { object } options.L Paytm Logger (lgr) object
     * @param { object } options.config Local config object
     * @param { object } options.INFRAUTILS Contains util libraries like kafka
     */

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.infraUtils = options.INFRAUTILS;
        self.dbInstance = options.dbInstance;
        self.billsModel = new MODELS.Bills(options)
        self.cassandraModel = new CassandraModel(options)
        self.consentData = {};
        self.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        self.recentBillLibrary = new recentBillLibrary(options);
        self.activePidLib = options.activePidLib;
        self.pgLibrary = new pgLibrary(options);
        self.bills_operator_table = _.get(self.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.operators = _.get(self.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        self.excluded = _.get(self.config, ['RECENT_BILL_CONFIG', 'EXCLUDED'], []);
        self.excludedChannelIds = _.get(self.config, ['RECENT_BILL_CONFIG', 'COMMON', 'EXCLUDE_CHANNEL_ID'], []);
        self.recent_bills_operators = self.recentBillLibrary._initRecentBillSpecificData(self.bills_operator_table, self.operators);
        self.allowedPrepaidDthOperator = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []);
        self.billSubscriber = new BILLSUBSCRIBER(options);
        self.recordCount = true;
        self.cvrReloadInterval = _.get(self.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        self.commonLib = new utility.commonLib(options);
        self.recentsLayer = new utility.RecentsLayer(options);
        self.reminderUtils = new digitalUtility.ReminderUtils();
        self.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.prepaidFlowManager = new PrepaidFlowManager(options);
        self.billsLib = new BillsLib(options);
        self.EncryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);

        self.includedOperatorList = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        self.includedOperator = self.includedOperatorList.split(',').map((e) => e.trim());

        self.serviceAllowedDeleteNonPaytm = _.get(self.config, ['DYNAMIC_CONFIG','NON_PAYTM_CONFIG', 'NON_PAYTM_DELETE', 'INCLUDE_SERVICES'], ['financial services']);

        self.greyScaleEnv = options.greyScaleEnv;
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : 500;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCH_DELAY'], 5*60*1000) : 500;    
        self.cc_service = 'financial services'
        self.logger = new Logger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: recentBills", "Re-initializing variable after interval");
        self.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        self.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        self.excludedChannelIds = _.get(self.config, ['RECENT_BILL_CONFIG', 'COMMON', 'EXCLUDE_CHANNEL_ID'], []);
        self.recent_bills_operators = self.recentBillLibrary._initRecentBillSpecificData(self.bills_operator_table, self.operators);
        self.allowedPrepaidDthOperator = _.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []);
        self.includedOperatorList = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        self.includedOperator = self.includedOperatorList.split(',').map((e) => e.trim());
    }

    /*
     * Starting point for service, intializing consumer for recent bills service
     */
    start() {
        let self = this;

        self.L.log("Start Configuration: Recent bills operator details :" + JSON.stringify(self.recent_bills_operators));
        self.setConsentData((error) => {
            if (_.isEmpty(self.consentData) || error) {
                self.L.error("There was an error in setting Consent Data", error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:CVR_DATA']);
            } else {
                self.L.log('setConsentData:: Success');
                self.L.log('initialising Producer ');
                self.automatic_sync_publisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.automatic_sync_publisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising Producer :: ', error);
                    self.L.log("RECENTBILLS :: AUTOMATIC_SYNC KAFKA PRODUCER STARTED....");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:AUTOMATIC_SYNC']);
                });

                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    self.L.critical('nonPaytmKafkaPublisher:: error in initialising Producer :: ', error);
                    self.L.log("RECENTBILLS :: NON_PAYTM_RECORDS KAFKA PRODUCER STARTED....");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:NON_PAYTM']);
                });
                
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                self.ctKafkaPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising ctKafkaPublisher Producer :: ', error);
                    self.L.log("RECENTBILLS :: ctKafkaPublisher KAFKA PRODUCER STARTED....");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:CT_KAFKA']);
                });

                self.paytmFirstKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.PAYTM_FIRST_CC_EVENTS_PUBLISHER.HOSTS
                });
                self.paytmFirstKafkaPublisher.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising paytmFirstKafkaPublisher Producer :: ', error);
                    self.L.log("RECENTBILLS :: paytmFirstKafkaPublisher KAFKA PRODUCER STARTED....");
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:PFCC']);
                });

                self._initializeRecentBillConsumer((error) => {
                    if (error) {
                        self.L.error("RecentBills", "Failed to initialize recent Bills service");
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:RECENT_BILLS']);
                    }
                });
            }
            setInterval(self.setConsentData.bind(self), self.cvrReloadInterval, (err) => {
                if (err) {
                    self.L.critical('_refreshProductData : error while re-loading CVR data', err);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:CVR_DATA']);
                } else {
                    self.L.log('_refreshProductData : reload CVR data ');
                }
            });
        });
    }

    setConsentData(callback) {
        let self = this;

        let whereQuery = null;
        if (Object.keys(self.consentData).length) {
            whereQuery = `updated_at > "${MOMENT().subtract(2, 'day').subtract(1, 'hour').format("YYYY-MM-DD HH:mm:ss")}"`;
        }

        self.catalogVerticalRecharge.getCvrData(function (error, data) {
            try {
                if (error || !data) callback(error);
                else {
                    data.forEach(function (row) {
                        if (row && row.attributes) {
                            try {
                                let remindable = JSON.parse(row.attributes).remindable;
                                self.consentData[row.product_id] = remindable == _.get(self.config, 'COMMON.USER_CONSENT_REQUIRED', 2) ? true : false;
                            } catch(error){
                                self.L.critical('setConsentData',`Error while parsing attributes for product_id:${row && row.product_id},row: ${JSON.stringify(row)}`);
                            }
                        }
                    })
                    callback();
                }
            } catch (Exception) {
                callback(Exception);
            }
        }, whereQuery);
    }

    /**
     * @param {function} cb callback function
     * initializing bill consumer service
     */
    _initializeRecentBillConsumer(cb) {
        let self = this;
        self.L.log("Service started on topics::" + _.get(self.config.KAFKA, 'SERVICES.RECENTBILL.RECHARGE_CONSUMER_TOPICS'));
        try {
            self.consumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'),
                "groupId": "recentBill-consumer",
                "topics": _.get(self.config.KAFKA, 'SERVICES.RECENTBILL.RECHARGE_CONSUMER_TOPICS'),
                "id": 'recentBill_' + OS.hostname(),
                "fromOffset": "earliest",
                "autoCommit": false,
                "batchSize": self.kafkaBatchSize
            });

            self.consumer.initConsumer(self._processBillsData.bind(self), (error) => {
                if (error){
                    self.L.critical("_initializeRecentBillConsumer : RecentBill consumer Configured cannot start.", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:RECENT_BILLS']);
                }
                else if (!error)
                    self.L.log("_initializeRecentBillConsumer : RecentBill consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error);
        }
    }

    /**
     * @param {object} data contains recharge data value
     */

    _processBillsData(records) {
        let self = this,
            rechargeData = null,
            chunkSize = 30,
            lastMessage,
            recordsToProcess = [];

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.consumer._pauseConsumer();
        } else {
            self.L.critical('_processBillsData', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:INVALID_RECORDS']);
            return;
        }

        self.kafkaConsumerChecks.findOffsetDuplicates("RecentBills", records);

        records.forEach(function (data) {
            if (data && data.value) {
                try {
                    //Adding temp check to print first record
                    if (self.recordCount) {
                        self.recordCount = false;
                    }
                    let timestamp = _.get(data, 'timestamp', null);
                    rechargeData = JSON.parse(data.value);
                    rechargeData.timestamp = timestamp;
                    recordsToProcess.push(rechargeData);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:TRAFFIC', `PARTITION:${data.partition}`, `TOPIC:${data.topic}`]);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:INVALID_PAYLOAD']);
                    self.L.error("_processBillsData", "Failed to parse recharges data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("_processBillsData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
            }
        });

        self.L.log('_processBillsData:: ', `Processing ${recordsToProcess.length} out of ${records.length} Recents data !!`);
        ASYNC.eachLimit(recordsToProcess, chunkSize, self._prepareDataToInsert.bind(self), function () {
            self.consumer.commitOffset(lastMessage, (error) => {
                if (error) {
                    self.L.error('_processBillsData::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:OFFSET_COMMIT',`PARTITION:${data.partition}`, `TOPIC:${data.topic}`]);
                }
                else {
                    self.L.log('_processBillsData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }
                recordsToProcess = [];

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:RECENT_BILLS", "TIME_TAKEN:" + executionTime]);
              
                setTimeout(() => {
                    // Resume consumer now
                    self.consumer._resumeConsumer();
                }, self.kafkaResumeTimeout);

            });
        });
    }

    /**
     *
     * @param {object} rechargeData contains recharge data value
     */

    pushToKafkaForAutomaticSync(done, row, tableName) {
        let self = this;
        let customerId = _.get(row, 'customerId', null);
        let service = _.get(row, 'service', null);
        let paytype = _.get(row, 'paytype', null);
        let rechargeNumber = _.get(row, 'rechargeNumber', null);
        let operator = _.get(row, 'operator', null);

        ASYNC.waterfall([
            next => {
                if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(service,paytype, customerId)){
                    let encryptedRechargeNumber = self.EncryptionDecryptioinHelper.encryptData(rechargeNumber);
                    self.getRecordsFromTable(next, encryptedRechargeNumber, customerId, operator, service, tableName);
                }else{
                    next(null, []);
                }
            },
            (data, next) =>{
                if(!data || (_.isArray(data) && data.length == 0)){
                    self.getRecordsFromTable(next, rechargeNumber, customerId, operator, service, tableName);
                }else{
                    data = self.EncryptionDecryptioinHelper.parseDbResponse(data);
                    next(null, data);
                }
            },
            (data, next) => {
                if (!data.length) {
                    return done('No data found');
                } else if (_.get(data[0], 'is_automatic',0) == 0){ // push data having is_automatic == 1/2/3/4
                    return done(null);
                }
                self.publishInKafka(next, data);
            }
        ],
        function (error) {
            if (error) {
                self.L.critical('pushToKafkaAutomaticSync ::', 'error occurred while getting data from DB for data : ', JSON.stringify(row), error);
                return done('error occurred while getting data from DB');
            } else {
                return done(null)
            }
        });
    }


    getRecordsFromTable(done, rechargeNumber, customerId, operator, service, tableName) {
        let self=this;
        let query = `select * from ${tableName} where recharge_number='${rechargeNumber}' and customer_id=${customerId} and operator='${operator}' and service='${service}'`;
        let queryParams = [
            tableName
        ];
        let latencyStart = new Date();
        this.logger.log(`pushToKafkaForAutomaticSync :: query to get data from DB for data for customer_id : ${customerId}`, self.dbInstance.format(query, queryParams), service);
        self.dbInstance.exec(function (err, data) {
            utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'pushToKafkaForAutomaticSync'});
            if (err || !(data)) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:pushToKafkaForAutomaticSync`]);
                self.L.critical('pushToKafkaAutomaticSync ::', `error occurred while getting data from DB for data : RN:${rechargeNumber}_CID:${customerId}_OP:${operator} with error ${err}`);
                return done('error occurred while getting data from DB');
            } else return done(null,data);
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    publishInKafka(done, data) {
        let self = this;
        if (_.get(data[0], 'is_automatic',0) == 0) // push data having is_automatic == 1/2/3/4
            return done();
        let updatedData = self.commonLib.mapBillsTableColumns(data[0]);
        let
            payLoad = [{
                topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                messages: JSON.stringify(updatedData),
                key: _.get(updatedData, 'rechargeNumber', '')
            }]
        self.automatic_sync_publisher.publishData(payLoad, function (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:AUTOMATIC_SYNC`]);
                self.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payLoad), error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PUBLISHED', `TYPE:AUTOMATIC_SYNC`]);
                self.L.log('Message published successfully in Kafka', ' on topic AUTOMATIC_SYNC', JSON.stringify(payLoad));
            }
            return done(error);
        }, [200, 800]);
    }

    /**
     *
     * @param {object} rechargeData contains recharge data value
     */
     publishCtEvents(done, recentBillsData) {

        let self = this;
        const rows = _.get(recentBillsData, 'transactionHistory', []);
        let recordAlreadyExists = false;
        let amount;
        let publishToCTFlag = true;
        let currentPaidAmount = Math.abs(_.get(recentBillsData, 'amount', null));

        recentBillsData.productId = self.activePidLib.getActivePID(recentBillsData.productId);
        const productId = recentBillsData.productId;
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull');
        
        if (!_.get(recentBillsData, 'notificationStatus', 1)) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:SKIP', `TYPE:CT_EVENTS`, `REASON:NOTIFICATION_DISABLED`]);
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(recentBillsData, 'notificationStatus', 0)}`);
            publishToCTFlag=false;
        } 	

        for(let row of rows){
            if(row.recharge_number != recentBillsData.rechargeNumber){
                continue
            }

            if(row.reference_id && recentBillsData.referenceId && row.reference_id !== recentBillsData.referenceId){
                continue
            }

            if(row.customer_id == recentBillsData.customerId && row.recharge_number == recentBillsData.rechargeNumber) {
                recordAlreadyExists = true;
                if(row.amount + recentBillsData.amount > 0) {
                    eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_PARTIAL'], 'billPaymentPartial');
                    amount = row.amount + recentBillsData.amount;
                }
                break;
            }
        }

        // if new customer ID, doesn't exist in reminder, we add it here
        if(!recordAlreadyExists) {
            // assumption that amount for all custids of rech_num are same
            if(rows && rows.length > 0){
                if(rows[0].recharge_number === recentBillsData.rechargeNumber){
                    if((rows[0].reference_id && recentBillsData.referenceId && rows[0].reference_id === recentBillsData.referenceId)
                    || (!rows[0].reference_id && !recentBillsData.referenceId)){
                        if(rows[0].amount + recentBillsData.amount > 0) {
                            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_PARTIAL'], 'billPaymentPartial');
                            amount = rows[0].amount + recentBillsData.amount
                        }
                    }
                }
            }

            let newRecord = _.clone(recentBillsData);
            _.set(newRecord, 'transactionHistory', null) //to prevent circular JSON issue
            rows.push(newRecord)
        }

        if(self.includedOperator.includes(recentBillsData.operator)){
            amount = recentBillsData.amount;
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull');
        }

        if(recentBillsData.operatorMonthlyException){
            eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'RENT_PAYMENT_PENDING'], 'rentPaymentPending');
        }

        ASYNC.eachLimit(rows, 3, (dataRow, cb) => {
            dataRow.bill_date = recentBillsData.billDate ? MOMENT(recentBillsData.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.due_date = recentBillsData.dueDate ? MOMENT(recentBillsData.dueDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : dataRow.due_date;
            dataRow.amount = amount ? amount : recentBillsData.amount;
            dataRow.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
            dataRow.paymentDate = recentBillsData.paymentDate;
            dataRow.productId = productId;
            if(recentBillsData.nextBillFetchDate){
                dataRow.nextBillFetchDate = recentBillsData.nextBillFetchDate
            }

            if(!dataRow.is_automatic){
                dataRow.is_automatic = 0;
            }

            if( eventName == _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull') || !dataRow.status){
                dataRow.status = _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11)
            }

            if(!dataRow.customer_id){
                dataRow.customer_id = dataRow.customerId //fixing mismatch in naming conventions
                dataRow.recharge_number = dataRow.rechargeNumber;
            }
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if(dataRow.referenceId){
                dbDebugKey += `::ref_id:${dataRow.referenceId}`
            } else if(dataRow.reference_id) {
                dbDebugKey += `::ref_id:${dataRow.reference_id}`
            }
            
            if(dataRow.recharge_number != recentBillsData.rechargeNumber){
                return cb(null)
            }

            if(dataRow.reference_id && recentBillsData.referenceId && dataRow.reference_id !== recentBillsData.referenceId){
                return cb(null)
            }
            dataRow.debugKey = dbDebugKey;

            try{
                let userData = JSON.parse(_.get(dataRow, 'user_data'));
                if(_.get(dataRow, 'service')=='rent payment'){
                    _.set(dataRow, 'rechargeNumber7',_.get(userData, 'recharge_number_7'));
                }
                else if(_.get(dataRow, 'service')=='tuition fees'){
                    _.set(dataRow, 'rechargeNumber3',_.get(userData, 'recharge_number_3'));
                }
            }catch(jsonErr){
                this.L.critical('publishCtEvents', 'error parsing user data');
            }

            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {                  
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    if(self.commonLib.isCTEventBlocked(eventName)){
                        self.L.info(`Blocking CT event ${eventName}`)
                        return next()
                    }
        
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    ASYNC.parallel([
                        function (cb) {
                            if(publishToCTFlag){
                                self.ctKafkaPublisher.publishData([{
                                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                                    messages: JSON.stringify(mappedData)
                                }], (error) => {
                                    if (error) {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + dataRow.operator]);
                                        self.logger.error(`publishInKafka :: publishCtEvents Error while publishing message topic REMINDER_CT_EVENTS ${error}- MSG:- `, clonedData, _.get(clonedData, 'service', null));
                                    } else {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + dataRow.operator,`EVENT_NAME:${eventName}`]);
                                        self.logger.log('prepareKafkaResponse :: publishCtEvents Message published successfully in Kafka on topic REMINDER_CT_EVENTS', clonedData, _.get(clonedData, 'service', null));
                                    }
                                    cb(error);
                                }, [200, 800]);
                            } else cb();
                        },
                        function (cb) {
                            if(_.get(mappedData, 'service', null)=='financial services'){
                                /**
                                 * This next try catch code block is placed here beacuse we have to send paytm first team all CC events, 
                                 * we pick transactional history to send all the events and hence last_paid_amount in those is actually the last to last paid amount 
                                 * so giving PFCC correct amount and not changing logic for ct or anywhere else because it may be used that way also somewhere. 
                                 */
                                let dataTosendPFCC = _.cloneDeep(mappedData)
                                try{
                                    let extraObj = JSON.parse(_.get(dataTosendPFCC, 'extra', '{}'))
                                    if(!extraObj){
                                        extraObj = {};
                                    }
                                    _.set(extraObj, 'last_paid_amount', currentPaidAmount);
                                    _.set(dataTosendPFCC, 'extra', JSON.stringify(extraObj));
                                }
                                catch(err){
                                    self.L.error('Could not parse extra field from payload with error', err)
                                    return cb(null);
                                }
                                self.paytmFirstKafkaPublisher.publishData([{
                                    topic: _.get(self.config.KAFKA, 'SERVICES.PAYTM_FIRST_CC_EVENTS_PUBLISHER.TOPIC', ''),
                                    messages: JSON.stringify(dataTosendPFCC)
                                }], (error) => {
                                    if (error) {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', "TYPE:PFCC_EVENTS", "OPERATOR:" +dataRow.operator]);
                                        self.logger.critical(`publishInKafka :: publishPFCCEvents Error while publishing message in Kafka ${error} - MSG:- `, dataTosendPFCC, _.get(dataTosendPFCC, 'service', null));
                                    } else {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PUBLISHED', "TYPE:PFCC_EVENTS", "OPERATOR:" + dataRow.operator]);
                                        self.logger.log('prepareKafkaResponse :: publishPFCCEvents Message published successfully in Kafka on topic PAYTM_FIRST_CC', dataTosendPFCC, _.get(dataTosendPFCC, 'service', null));
                                    }
                                    cb(error);
                                }, [200, 800]);
                            }else{
                                cb(null);
                            }
                        },
                    ], function done(err) {
                        return next(err);
                    });
                }
            ], (err) => {
                if(err){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', "TYPE:publishCtEvents"]);
                    self.L.error('publishInKafka :: publishCtEvents', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' , err);
                }
                return cb(err)
            })
        })

        return done(null)
    }

    publishCtEventsForDeletedRecords(recentDataa) {

        let self = this;

        recentDataa.productId = self.activePidLib.getActivePID(recentDataa.product_id);
        const productId = recentDataa.productId;
        let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull');

        if (!_.get(recentDataa, 'notificationStatus', 1)) {
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(recentDataa, 'notificationStatus', 0)}`);
            return 
        } 	

            recentDataa.bill_date = recentDataa.billDate ? MOMENT(recentDataa.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            recentDataa.due_date = recentDataa.dueDate ? MOMENT(recentDataa.dueDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : recentDataa.due_date;
            recentDataa.updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
            

            if( eventName == _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILL_PAYMENT_FULL'], 'billPaymentFull') || !recentDataa.status){
                recentDataa.status = _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11)
            }

            if(!recentDataa.customer_id){
                recentDataa.customer_id = recentDataa.customerId //fixing mismatch in naming conventions
                recentDataa.recharge_number = recentDataa.rechargeNumber;
            }
            let dbDebugKey = `rech:${recentDataa.recharge_number}::cust:${recentDataa.customer_id}::op:${recentDataa.operator}`;
            if(recentDataa.referenceId){
                dbDebugKey += `::ref_id:${recentDataa.referenceId}`
            } else if(recentDataa.reference_id) {
                dbDebugKey += `::ref_id:${recentDataa.reference_id}`
            }
            recentDataa.debugKey = dbDebugKey;
            if(self.commonLib.isCTEventBlocked(eventName)){
                self.L.info(`Blocking CT event ${eventName}`)
                return 
            }

            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, recentDataa.customer_id, recentDataa);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, recentDataa);
                },
                next => {                  
                    let mappedData = self.reminderUtils.createCTPipelinePayload(recentDataa, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILLS", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + recentDataa.operator]);
                            self.logger.error(`publishInKafka :: publishCtEventsForDeletedRecords Error while publishing message topic REMINDER_CT_EVENTS ${error}- MSG:- `, clonedData, _.get(recentDataa, 'service', null));
                        } else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILLS", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + recentDataa.operator]);
                            self.logger.log('prepareKafkaResponse :: publishCtEventsForDeletedRecords Message published successfully in Kafka on topic REMINDER_CT_EVENTS', clonedData, _.get(recentDataa, 'service', null));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.error('publishInKafka :: publishCtEventsForDeletedRecords', 'Error while publishing message topic REMINDER_CT_EVENTS - MSG:- ' , err);
                return
            })
    }

    preProcessRecentsData(done, recentBillsData) {
        let self = this;

        if (_.get(recentBillsData, 'isCreditCardOperator', false)) {
            let referenceId = _.get(recentBillsData, 'referenceId');
            let tokenisedCreditCard = _.get(recentBillsData, 'tokenisedCreditCard');

            let parId = _.get(recentBillsData, 'parId');
            if (!tokenisedCreditCard && !referenceId) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:INVALID_REF_ID`, `OPERATOR:${_.get(recentBillsData, 'operator', null)}`]);
                return done(`Invalid referenceId received ${referenceId}`);
            } else if(tokenisedCreditCard && !parId) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:INVALID_PAR_ID`, `OPERATOR:${_.get(recentBillsData, 'operator', null)}`]);
                return done(`Invalid parId received ${parId}`);
            } else if (_.get(recentBillsData, 'operator', null) == 'paytmfirstcc') { // TODO Extra validation checks for paytmfirstcc
                if (isNaN(referenceId)) {
                    // this will be the CIN number we need
                    return done();
                } else {
                    // We will get CIN number from PG by API hit
                    self.pgLibrary.getCardIndexNumber(function (error, cardIndexNumber) {
                        if (error || !cardIndexNumber) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:GET_CARD_INDEX_NO_API`, `OPERATOR:${_.get(recentBillsData, 'operator', null)}`]);
                            self.L.critical('preProcessRecentsData', `Unable to fetch cardIndexNumber for ${JSON.stringify(recentBillsData)}`);
                            return done('Unable to fetch cardIndexNumber');
                        } else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:SUCCESS', `TYPE:GET_CARD_INDEX_NO_API`, `OPERATOR:${_.get(recentBillsData, 'operator', null)}`]);
                            _.set(recentBillsData, 'referenceId', cardIndexNumber);
                            return done();
                        }
                    }, referenceId);
                }
            } else {
                return done();
            }
        } else {
            return done();
        }
    }

    /**
     * Reset DB update params based on existing DB record
     * - notifyRecents
     * - notificationStatus
     * - is_automatic
     * - nextBillFetchDate
     * - billDate
     * - dueDate
     * - amount
     * @param {*} recentBillsData 
     */
      async resetDataFromTransactionHistory(recentBillsData,cb){
        let 
            self = this,
            rows = _.get(recentBillsData,'transactionHistory',[]);

        // in case of rent payment or tution fee -> Just update amount as transacted amount
        if(self.includedOperator.includes(recentBillsData.operator)) {
            recentBillsData.amount = Math.abs(recentBillsData.amount);
            return cb();
        }

     
        if(recentBillsData.service == 'financial services'){
            await self.findAndDeleteAllMatchingMCNs(recentBillsData)
                return cb();
        }

        for (let row of rows) {
            if (row.customer_id == recentBillsData.customerId && row.notification_status == 0) {
                _.set(recentBillsData, 'notifyRecents', true);
                _.set(recentBillsData, 'notificationStatus', 0); // not updating user preferred status
            }
            if (row.is_automatic !=0){
                if(row.customer_id == recentBillsData.customerId){
                    _.set(recentBillsData, 'is_automatic', _.get(row, 'is_automatic', 0));
                }else{
                    _.set(recentBillsData, 'is_automatic_diffCustId', _.get(row, 'is_automatic', 0));
                }
            }
            if (row.customer_id == recentBillsData.customerId &&                 
                _.get(recentBillsData,'isCreditCardOperator',null) &&
                row.customerOtherInfo ){  
                try{
                    _.set(recentBillsData, 'customerOtherInfo', JSON.parse(row.customerOtherInfo));  
                }catch(err)
                {
                    self.L.error("resetDataFromTransactionHistory", "Error in JSON parsing" + err);
                }

            }
            
            if(row.customer_id == recentBillsData.customerId && row.extra){    
                try{
                    let dbExtra = JSON.parse(_.get(row, 'extra', '{}'));
                    if(dbExtra) {
                        let extraFromRecord = JSON.parse(recentBillsData.extra);
                        if(dbExtra.blockedBy) extraFromRecord.blockedBy = dbExtra.blockedBy;
                        if(dbExtra.created_source) extraFromRecord.created_source = dbExtra.created_source;
                        if(_.get(dbExtra, 'errorCounters', null)) extraFromRecord.errorCounters = {};
                        recentBillsData.extra=JSON.stringify(extraFromRecord);
                    }
                }catch(err)
                {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PARSING_ERROR', `TYPE:REST_TRANSCATION_HISTORY`]);
                    self.L.error("resetDataFromTransactionHistory", "Error in JSON parsing" + err);
                }
            }

            if(row.customer_id == recentBillsData.customerId){
                if(_.get(row, 'product_id', null) && _.get(recentBillsData, 'productId', null)) {
                    if(_.get(row, 'product_id', null) !== _.get(recentBillsData, 'productId', null)) {
                        self.L.info(`Product ID changed from ${_.get(row, 'product_id', null)} to ${_.get(recentBillsData, 'productId', null)} for debugKey ${recentBillsData.debugKey}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PROCESSED', 'TYPE:PRODUCT_ID_CHANGED', "OPERATOR:" + recentBillsData.operator]);
                    }else{
                        self.L.info(`Product ID not changed for debugKey ${recentBillsData.debugKey}`);
                    }
                }else{
                    self.L.info(`Product ID not found for debugKey ${recentBillsData.debugKey} row PID: ${_.get(row, 'product_id', null)} record PID: ${_.get(recentBillsData, 'productId', null)}`);
                }
            }
        }

        if (recentBillsData.coolOff === true && rows && rows.length > 0 && !_.get(self.config, ['RECENT_BILL_CONFIG','OPERATORS', recentBillsData.operator, 'mandatoryUpdateNBFD' ], false) ) {
            for (let index = 0; index < rows.length; index++) { // history data for same rechage num. help to decide nextBillFetchDate, billDate & dueDate
                let billDate = rows[index].bill_date ? MOMENT(rows[index].bill_date).utc().format('YYYY-MM-DD') : null;
                let dueDate = rows[index].due_date ? MOMENT(rows[index].due_date).utc().format('YYYY-MM-DD') : null;
                let currentAutomaticStatus = _.get(recentBillsData, 'is_automatic', null) || _.get(recentBillsData, 'is_automatic_diffCustId', null);
                let nbfd = self.billSubscriber.getNBFDByCycle(recentBillsData,self.allowedPrepaidDthOperator,recentBillsData.operator, billDate, dueDate, currentAutomaticStatus);
                if (nbfd) {
                    if (recentBillsData.nextBillFetchDate != nbfd) {
                        self.L.log('_prepareDataToInsert', 'calcNBFD', `Result after DB bill cycle calc ${recentBillsData.nextBillFetchDate} -> ${nbfd} for ${recentBillsData.debugKey}`);

                        recentBillsData.nextBillFetchDate = nbfd;
                        recentBillsData.billDate = billDate;
                        recentBillsData.dueDate = dueDate;
                        recentBillsData.setNBFD = true;
                    }
                    break;
                }
            }
        }
        return cb();
    }

    // matchTokenisedCC(recentBillsData){
    //     const self = this;
    //     const rows = _.get(recentBillsData,'transactionHistory',[]);

    //     for(let row of rows){
    //         if(row.paytype != 'credit card'){
    //             continue;
    //         }
    //         if(row.recharge_number === recentBillsData.rechargeNumber && row.par_id == recentBillsData.parId
    //             && row.customer_id == recentBillsData.customerId){
    //             if (row.notification_status == 0) {
    //                 _.set(recentBillsData, 'notifyRecents', true);
    //                 _.set(recentBillsData, 'notificationStatus', 0);
    //             }
    //             if (row.is_automatic == 1) {
    //                 _.set(recentBillsData, 'is_automatic', 1);
    //             }
    //             if (row.customerOtherInfo){  
    //                 _.set(recentBillsData, 'customerOtherInfo', JSON.parse(row.customerOtherInfo));                    
    //             }
    //             return true;
    //         }
    //     }
    //     return false;
    // }

    // async findAndDeleteNonTokenisedMCN(recentBillsData){
    //     const self = this;
    //     const rows = _.get(recentBillsData,'transactionHistory',[]);
    //     try{
    //         for(let row of rows){
    //             if(row.par_id != '' && row.par_id != null){ //filter tokenised card if any
    //                 continue;
    //             }
    //             const last4_MCN = row.recharge_number.substr(-4);
    //             if(last4_MCN == recentBillsData.rechargeNumber.substr(-4)) {

    //                 let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'] , ''))
    //                 let bankName = _.toLower(_.get(attributes, ['bank_code'] , ''));
    //                 let cardNetwork = _.toLower(_.get(attributes, ['card_network'] , ''));
    //                 if(recentBillsData.bankName == bankName && recentBillsData.cardNetwork == cardNetwork){ 
    //                     utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:MCN_DELETED_NON_TOKENISED']);
    //                     await self.billsModel.deleteCreditCardMCN(row)
    //                 }
    //             }
    //         }
    //     } catch (error){
    //         self.L.error("findAndDeleteMatchingMCN:: Error while deleting creditcard history ", recentBillsData.rechargeNumber, error)
    //     }
    // }

    /**
     * 
     * @param {Object} recentBillsData 
     * @description Checks if we already have the new format MCN credit card
     * @returns {Boolean} true if new format card exists
     */
    // matchNewFormatCC(recentBillsData){
    //     const self = this;
    //     const rows = _.get(recentBillsData,'transactionHistory',[]);

    //     for(let row of rows){
    //         if(row.reference_id === recentBillsData.referenceId && row.recharge_number === recentBillsData.rechargeNumber){
    //             if (row.notification_status == 0) {
    //                 _.set(recentBillsData, 'notifyRecents', true);
    //                 _.set(recentBillsData, 'notificationStatus', 0);
    //             }
    //             if (row.is_automatic == 1) {
    //                 _.set(recentBillsData, 'is_automatic', 1);
    //             }
    //             if (row.customerOtherInfo){  
    //                 _.set(recentBillsData, 'customerOtherInfo', JSON.parse(row.customerOtherInfo));                    
    //             }
    //             return true;
    //         }
    //     }
    //     return false;
    // }

    /**
     * 
     * @param {Object} recentBillsData 
     * @description deletes older MCN format card if it exists
     * 
     */
    // async findAndDeleteMatchingMCN(recentBillsData){
    //     const self = this;
    //     const rows = _.get(recentBillsData,'transactionHistory',[]);
    //     try{
    //         for(let row of rows){
    //             if(row.reference_id === recentBillsData.referenceId) {
    //                 _.set(recentBillsData, 'amount', row.amount + _.get(recentBillsData, 'amount', 0))
    //                 if (row.notification_status == 0) {
    //                     _.set(recentBillsData, 'notifyRecents', true);
    //                     _.set(recentBillsData, 'notificationStatus', 0);
    //                 }
    //                 if (row.is_automatic == 1) {
    //                     _.set(recentBillsData, 'is_automatic', 1);
    //                 }
    //                 if (row.customerOtherInfo){  
    //                     _.set(recentBillsData, 'customerOtherInfo', JSON.parse(row.customerOtherInfo));                    
    //                 }
    //                 utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:MCN_DELETED']);
    //                 await self.billsModel.deleteCreditCardMCN(row)   
    //             }
    //         }
    //     } catch (error){
    //         self.L.error("findAndDeleteMatchingMCN:: Error while deleting creditcard history ", recentBillsData.rechargeNumber, error)
    //     }
    // }

    /**
     * 
     * @param {Object} recentBillsData 
     * @description deletes all card if it exists
     * 
     */

     async findAndDeleteAllMatchingMCNs(recentBillsData){
            const self = this;
            const historicalData = _.get(recentBillsData,'transactionHistory',[]);
            try{
            for(let row of historicalData){
                if(row.recharge_number === recentBillsData.rechargeNumber && row.reference_id === recentBillsData.referenceId){
                    try{
                        let dbExtra = JSON.parse(_.get(row, 'extra', '{}'));
                        if(dbExtra) {
                            let extraFromRecord = JSON.parse(recentBillsData.extra);
                            // if(dbExtra.blockedBy) extraFromRecord.blockedBy = dbExtra.blockedBy;
                            // if(dbExtra.created_source) extraFromRecord.created_source = dbExtra.created_source
                            // recentBillsData.extra=JSON.stringify(extraFromRecord);
                            if(_.get(dbExtra, 'errorCounters', null)) extraFromRecord.errorCounters = {};
                            recentBillsData.extra={...dbExtra, ...extraFromRecord};
                            recentBillsData.extra=JSON.stringify(recentBillsData.extra);
                            }
                        }
                        catch(err)
                        {
                            self.L.error("resetDataFromTransactionHistory", "Error in JSON parsing" + err);
                        }
                    recentBillsData.actualHistory=row;
                    continue;
                }
                    const last4_MCN = row.recharge_number.substr(-4);
                    if(last4_MCN == recentBillsData.rechargeNumber.substr(-4)) {
                        let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'] , '{}'))
                        let bankName = _.toLower(_.get(attributes, ['bank_code'] , ''));
                        let cardNetwork = _.toLower(_.get(attributes, ['card_network'] , ''));
                        if(recentBillsData.bankName == bankName ){ 
                            if (row.notification_status == 0) {
                                _.set(recentBillsData, 'notifyRecents', true);
                                _.set(recentBillsData, 'notificationStatus', 0);
                            }
                            if (row.is_automatic == 1 || row.is_automatic==3 ) {
                                _.set(recentBillsData, 'is_automatic', 1);
                            }
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:DELETE OLDER ENTRIES']);
                            self.L.log("findAndDeleteMatchingMCN:: deleting existing CC entry for ", row.recharge_number, row.reference_id);
                            self.publishCtEventsForDeletedRecords({...row});
                            await self.billsModel.deleteAllMatchingMCNs({...row});

                            if(_.get(row,'is_automatic',0) == 1) {
                                
                                row = self.commonLib.mapBillsTableColumns(row);
                                _.set(row, 'DELETE_AUTOMATIC', true);

                                let payLoad = [{
                                    topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                                    messages: JSON.stringify(row)
                                }];

                                self.automatic_sync_publisher.publishData(payLoad, function (error) {
                                    if (error) {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:AUTOMATIC_SYNC`]);
                                        self.logger.critical(`Error while publishing message in Kafka ${error}- MSG:- `, payLoad, _.get(row, 'service', null));
                                    } else {
                                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PUBLISHED', `TYPE:AUTOMATIC_SYNC`]);
                                        self.logger.log('Message published successfully in Kafka on topic AUTOMATIC_SYNC ', payLoad, _.get(row, 'service', null));
                                    }
                                }, [200, 800]);
                            }
                        }
                    }
            }
            } catch (error){
                self.L.error("findAndDeleteAllMatchingMCNs:: Error while deleting creditcard history ", recentBillsData.rechargeNumber, error)
            }
            return new Promise((resolve, reject) => {
                resolve();
            })
    }



    /**
     * 
     * @param {Object} recentBillsData 
     * @description deletes older tokenised card if it exists
     * 
     */
     async findAndDeleteTokenisedMCN(recentBillsData){
        const self = this;
        const rows = _.get(recentBillsData,'transactionHistory',[]);
        try{
            for(let row of rows){
                if(row.par_id == '' || row.par_id == null){ //filter non tokenised card if any
                    continue;
                }
                const last4_MCN = row.recharge_number.substr(-4);
                if(last4_MCN == recentBillsData.rechargeNumber.substr(-4)) {
                    let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', row.product_id, 'attributes'] , '{}'))
                    let bankName = _.toLower(_.get(attributes, ['bank_code'] , ''));
                    let cardNetwork = _.toLower(_.get(attributes, ['card_network'] , ''));
                    if(recentBillsData.bankName == bankName && recentBillsData.cardNetwork == cardNetwork){ 
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:MCN_DELETED_NON_TOKENISED']);
                        await self.billsModel.deleteCreditCardMCN(row)
                    }
                }
            }
        } catch (error){
            self.L.error("findAndDeleteMatchingMCN:: Error while deleting creditcard history ", recentBillsData.rechargeNumber, error)
        }
    }


    getBillAmount(recentBillsData){
        var self = this;
        for(let i=0; i<_.get(recentBillsData,'transactionHistory',[]).length;i++){
            let row=_.get(recentBillsData, `transactionHistory[${i}]`,null);
            if(row && (_.get(row,'status', 7)=='7' || _.get(row,'status',13)=='13')){
                continue;
            }
            else{
                return _.get(row, 'amount', null);
            }
        }
        return null;
    }
    getRNForCCCache(recentBillsData){
        var self= this;
        let recharge_number = _.get(recentBillsData, 'rechargeNumber', ''),
        customer_id = _.get(recentBillsData, 'customerId', '') ,
        bank_name = _.toLower(_.get(recentBillsData, 'bankName', '')) ,
        card_network = _.toLower(_.get(recentBillsData, 'cardNetwork',''));

        recharge_number = recharge_number.replace(/ /g,'');
        let last4MCN = recharge_number.substr(recharge_number.length -4);

        return last4MCN+'_'+customer_id+'_'+bank_name+'_'+card_network;
    }

    getRNForCCNonRU(recentBillsData){
        let recharge_number = _.get(recentBillsData, 'rechargeNumber', ''),
        customer_id = _.get(recentBillsData, 'customerId', '') ,
        bank_name = _.toLower(_.get(recentBillsData, 'bankName', ''));

        recharge_number = recharge_number.replace(/ /g,'');
        let last4MCN = recharge_number.substr(recharge_number.length -4);

        return last4MCN+'_'+customer_id+'_'+bank_name;
    }

    storePaymentsCache(recentBillsData, cb) {
        const self = this;
        
        // Helper function to create payload object
        const createPayload = (rechargeNum) => ({
            recharge_number: rechargeNum,
            customer_id: _.get(recentBillsData, 'customerId', null),
            service: _.get(recentBillsData, 'service', null), 
            operator: _.get(recentBillsData, 'operator', null),
            amount: _.get(recentBillsData, 'amount', null),
            circle: _.get(recentBillsData, 'circle', null),
            paytype: _.get(recentBillsData, 'paytype', null),
            product_id: _.get(recentBillsData, 'productId', null),
            payment_date: _.get(recentBillsData, 'paymentDate', null)
        });


        const createPayloadForNonRU = (rechargeNum,operator) => ({
            recharge_number: rechargeNum,
            customer_id: _.get(recentBillsData, 'customerId', null),
            service: _.get(recentBillsData, 'service', null), 
            operator: operator,
            amount: _.get(recentBillsData, 'amount', null),
            circle: _.get(recentBillsData, 'circle', null),
            paytype: _.get(recentBillsData, 'paytype', null),
            product_id: _.get(recentBillsData, 'productId', null),
            payment_date: _.get(recentBillsData, 'paymentDate', null)
        });
    
        // Get initial recharge number based on service type
        const initialRechargeNum = _.get(recentBillsData, 'service', null) === 'financial services' 
            ? self.getRNForCCCache(recentBillsData) 
            : _.get(recentBillsData, 'rechargeNumber', null);
    
        // Create array with initial payload
        const cassandraPayload = [createPayload(initialRechargeNum)];
    
        // Add alternate payload if needed
        const [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(
            cassandraPayload[0].recharge_number, 
            cassandraPayload[0].operator
        );

      
        
        if (isOperatorPrefixEnabled) {
            cassandraPayload.push(createPayload(alternateRechargeNumber));
        }
    
        if (_.get(recentBillsData, 'service', null) === 'financial services') {
            cassandraPayload.push(createPayloadForNonRU(self.getRNForCCNonRU(recentBillsData), _.toLower(_.get(recentBillsData, 'bankName', ''))));
        }

        // Create promise array for all payloads
        const promiseArray = cassandraPayload.map(payload => 
            self.cassandraModel.insertPaymentRemindLaterEvents(payload)
        );
    
        Promise.all(promiseArray)
            .then((result) => {
                return cb(null);
            })
            .catch(error => {
                self.L.error(`storePaymentsCache::`, `cache storing query failed with error ${error} for record having debug key rechargeNumber:${cassandraPayload[0].recharge_number}_customerId:${cassandraPayload[0].customer_id}`);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:RECENT_BILL", 
                    "STATUS:ERROR", 
                    'TYPE:CACHE_STORED',
                    `OPERATOR:${cassandraPayload[0].operator}`,
                    `SERVICE:${cassandraPayload[0].service}`
                ]);
                return cb(error);
            });
    }

    ignoreEventIfDuplicate(cb, rows ,recentBillsData){
        let self = this;
        try{
            let orderIdArray = [];
            for(let row of rows){
                let extra = JSON.parse(_.get(row, 'extra', '{}'));
                let dbOrderId = _.get(extra, 'order_id', null);
                if(dbOrderId){
                    orderIdArray.push(dbOrderId);
                }
            }
            if(orderIdArray.includes(_.get(recentBillsData, 'orderId', null))){
                return cb('duplicate event');
            }else{
                return cb(null,rows);
            }
        }catch(e){
            self.L.error(`ignoreEventIfDuplicate::`, `error while checking duplicate event ${e}`);
            return cb(null,rows);
        }
    }

    /**
     *
     * @param {object} rechargeData contains recharge data value
     */
    _prepareDataToInsert(rechargeData, done) {
        let self = this;
        if(_.toLower(_.get(rechargeData,'productInfo_paytype',null))=='credit card'){
            self.L.log(`Record recieved having debug key: customer_id:${_.get(rechargeData,'customerInfo_customer_id',null)}_recharge_number:${this.EncryptionDecryptioinHelper.encryptData(_.get(rechargeData,'userData_recharge_number',null))}_operator:${_.get(rechargeData,'productInfo_operator',null)}_amount:${this.EncryptionDecryptioinHelper.encryptData(_.get(rechargeData,'userData_amount',null))}_product_id:${_.get(rechargeData,'catalogProductID',null)}_status:${_.get(rechargeData,'inStatusMap_responseCode',null)}`)
        } else {
            self.L.log(`Record recieved having debug key: customer_id:${_.get(rechargeData,'customerInfo_customer_id',null)}_recharge_number:${_.get(rechargeData,'userData_recharge_number',null)}_operator:${_.get(rechargeData,'productInfo_operator',null)}_amount:${_.get(rechargeData,'userData_amount',null)}_product_id:${_.get(rechargeData,'catalogProductID',null)}_status:${_.get(rechargeData,'inStatusMap_responseCode',null)}`)
        }

        let
            status = self.recentBillLibrary._isRemindable(rechargeData, self.recent_bills_operators, self.excluded),
            userData = self.recentBillLibrary.getUserData(rechargeData),
            recharge_status = _.get(rechargeData, 'inStatusMap_responseCode', ''),
            service = _.get(rechargeData, 'productInfo_service', ''),
            isBillsDataValid = self.recentBillLibrary.isCreateBillDataValid(rechargeData),
            productId = _.get(rechargeData, 'catalogProductID', 0),
            remindable = self.consentData[productId],
            fromRecents = true, recentBillsData, tableName, debugKey, newFormatMCN, tokenisedCreditCard;

        // allowing pending trxs for rent payment
        const operatorMonthlyException = (self.includedOperator.includes(service) && recharge_status == '07');
        //const rentException = (service == 'rent payment' && recharge_status == '07');

        if ((status && recharge_status == '00' && isBillsDataValid) || operatorMonthlyException) {
            rechargeData.allowedPrepaidDthOperator=self.allowedPrepaidDthOperator;
            recentBillsData = self.recentBillLibrary.prepareRecentBillData(rechargeData, self.recent_bills_operators, userData, self.billSubscriber);
            newFormatMCN = _.get(recentBillsData, "newFormatMCN", null);
            tokenisedCreditCard = _.get(recentBillsData, "tokenisedCreditCard", null);
            tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', recentBillsData.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', recentBillsData.operator], null);

            if(_.toLower(_.get(rechargeData,'productInfo_paytype',null))=='credit card'){  
                if(tokenisedCreditCard)
                    debugKey = `customerId:${_.get(recentBillsData, "customerId")}_rechargeNumber:${this.EncryptionDecryptioinHelper.encryptData(_.get(recentBillsData, "rechargeNumber"))}_operator:${_.get(recentBillsData, "operator")}_productId:${_.get(recentBillsData, "productId")}_parNo:${this.EncryptionDecryptioinHelper.encryptData(_.get(recentBillsData, "parId", null))}`;
                else 
                    debugKey = `customerId:${_.get(recentBillsData, "customerId")}_rechargeNumber:${this.EncryptionDecryptioinHelper.encryptData(_.get(recentBillsData, "rechargeNumber"))}_operator:${_.get(recentBillsData, "operator")}_productId:${_.get(recentBillsData, "productId")}_referenceId:${this.EncryptionDecryptioinHelper.encryptData(_.get(recentBillsData, "referenceId", null))}`;
            } else
                debugKey = `customerId:${_.get(recentBillsData, "customerId")}_rechargeNumber:${_.get(recentBillsData, "rechargeNumber")}_operator:${_.get(recentBillsData, "operator")}_productId:${_.get(recentBillsData, "productId")}_referenceId:${_.get(recentBillsData, "referenceId", null)}`;

            if (!tableName) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:TABLE_NOT_FOUND`, `OPERATOR:${_.get(rechargeData,'productInfo_operator',null)}`]);
                self.L.error('_prepareDataToInsert', `table not found for ${debugKey}`);
                return done();
            }
            
            let paymentChannel = _.get(recentBillsData, ['paymentChannel'], '');
            
            if(self.excludedChannelIds && _.isArray(self.excludedChannelIds) && self.excludedChannelIds.indexOf(paymentChannel) > -1 ) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:EXCLUDED_CHANNEL_ID`,`PAYMENT_CHANNEL:${paymentChannel}`, `OPERATOR:${_.get(rechargeData,'productInfo_operator',null)}`]);
                self.L.log('_prepareDataToInsert', `payments events were disabled when paymentChannel=${paymentChannel} debug key ${debugKey}`);
                return done();
            }

            self.L.log('Processing data', debugKey);
            _.set(recentBillsData, 'debugKey', debugKey);
            //_.set(recentBillsData, 'rentException', rentException);
            _.set(recentBillsData, 'operatorMonthlyException', operatorMonthlyException);
        } else {
            self.L.log('_prepareDataToInsert', `skkiping due to either status/recharge_status or isBillsDataValid failed for having debug key: customer_id:${_.get(rechargeData,'customerInfo_customer_id',null)}_recharge_number:${_.toLower(_.get(rechargeData,'productInfo_paytype',null))=='credit card' ? this.EncryptionDecryptioinHelper.encryptData(_.get(rechargeData,'userData_recharge_number',null)) : _.get(rechargeData,'userData_recharge_number',null)}_operator:${_.get(rechargeData,'productInfo_operator',null)}_amount:${_.toLower(_.get(rechargeData,'productInfo_paytype',null))=='credit card' ? this.EncryptionDecryptioinHelper.encryptData(_.get(rechargeData,'userData_amount',null)) : _.get(rechargeData,'userData_amount',null)}_product_id:${_.get(rechargeData,'catalogProductID',null)}_status:${_.get(rechargeData,'inStatusMap_responseCode',null)}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', `TYPE:PROCESSING`, `REMINDABLE_STATUS:${status}`, `RECHARGE_STATUS:${recharge_status}`, `BILLSDATAVALIDATION:${isBillsDataValid}` ,`OPERATOR:${_.get(rechargeData,'productInfo_operator',null)}`]);
            return done();
        }

        /**
         * Flow:
         * 1. Pre process data
         * 2. Fetch history of bill payments
         * 3. set params
         * 4. Create and  Update records
         * 5. Push to Kafka for automatic sync
         * 6. Nudge Recents for disabled notifications
         */
        ASYNC.waterfall([
            next => {
                // invoked for credit card
                return self.preProcessRecentsData(next, recentBillsData);
            },
            next => {
                let customerId = _.get(recentBillsData, 'customerId');
                // Fetching all transaction history for incoming recharge number
                 if(service == 'financial services') {  
                    return self.billsModel.getBillByCustomer(next, tableName, customerId); 
                 }
                 return self.billsModel.getBillsOfSameRech(next, tableName, recentBillsData);
            },
            (rows,next) => {
                self.ignoreEventIfDuplicate(next, rows, recentBillsData);
            },
            (rows, next) => {
                self.L.log('_prepareDataToInsert', `Found ${rows.length} transaction history for same RN for:${debugKey}`)
                _.set(recentBillsData, 'transactionHistory', rows)

                self.resetDataFromTransactionHistory(recentBillsData, function(err,data){ // sets created_source using dbData
                    if(err){
                        self.L.error(`resetDataFromTransactionHistory', 'Error in deleting transactionHistory for ${debugKey}`, err);
                    }
                    return next();
                });  

            },

            async next => {
                if(_.get(recentBillsData, 'paymentChannel', null)=='SUBS 1'){
                    _.set(recentBillsData, 'is_automatic', 1);
                }
                let currentAutomaticStatus =  _.get(recentBillsData, 'is_automatic', null) || _.get(recentBillsData, 'is_automatic_diffCustId', null);
                if(!_.get(recentBillsData, 'setNBFD', false)){
                    let nbfdObj = self.recentBillLibrary.decideNextBillFetchDate(rechargeData, self.recent_bills_operators, self.billSubscriber, currentAutomaticStatus);
                    _.set(recentBillsData, 'nextBillFetchDate', nbfdObj.nextBillFetchDate);
                }

                try{
                    let highestPriorityAmongestRows = self.billsLib.getHighestPriorityAmongestRows(recentBillsData.transactionHistory);
                    let highestPublishedDateAmongestRows = self.billsLib.getHighestPublishedDateAmongestRows(recentBillsData.transactionHistory);
                    recentBillsData = self.billsLib.updateRecordWithOffsetNbfd(recentBillsData, highestPriorityAmongestRows, highestPublishedDateAmongestRows)
                }catch(e){
                    self.L.error(`_prepareDataToInsert`, `Error while setting nextBillFetchDate ${e}`);
                }


                //In case of is_automatic=1/2/3/4, bill fetch will not be stopped for lic user
                // if (_.toLower(recentBillsData.operator) == 'lic' && ! (_.get(recentBillsData, 'is_automatic', 0) || _.get(recentBillsData, 'is_automatic_diffCustId', 0))){                    
                //     return next('bill fetch stopped for lic user with Non Automatic Subscription');
                // }
                
                if(operatorMonthlyException){
                    return next();
                }

                let service = _.get(recentBillsData, 'service', null);
                let operator = _.get(recentBillsData, 'operator', null);
                let paidAmount = _.get(recentBillsData, 'amount', null);
                let billAmount,amountWithinDeviation=false;

                let maxDeviationAmount = _.get(self.config, ['RECENT_BILL_CONFIG','OPERATORS',operator,'MAX_DEVIATION_AMOUNT'], 
                _.get(this.config, ['RECENT_BILL_CONFIG','OPERATORS',service,'MAX_DEVIATION_AMOUNT'], 0));
                self.L.log("_prepareDataToInsert", `maxDeviationAmount for this operator/service is ${maxDeviationAmount} for ${debugKey}`);

                if(_.get(recentBillsData, 'transactionHistory', null) && _.isArray(_.get(recentBillsData, 'transactionHistory', null))){
                    billAmount = self.getBillAmount(recentBillsData)
                    if(billAmount!==null && billAmount+paidAmount > 0 && billAmount+paidAmount <= maxDeviationAmount){
                        self.L.log("_prepareDataToInsert",`billAmount : ${billAmount}, paidAmount : ${paidAmount}, difference of both is less than maxDeviationAmount : ${maxDeviationAmount}, hence amount will be updated as 0 in DB for ${debugKey}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:DEVIATION_AMOUNT_PAID', `OPERATOR:${operator}`, `SERVICE:${service}`]);
                        amountWithinDeviation=true; 
                        _.set(recentBillsData, 'amountWithinDeviation', amountWithinDeviation);
                    }
                }
                
                if (_.get(recentBillsData, 'isValidPrepaidElectricityOperator', false) === true) {
                    let prepaidRecord = _.cloneDeep(recentBillsData);
                    const response = await self.handleElectricityRecord(prepaidRecord);
                    if(!_.get(response, 'proceedWithPostpaidFlow', false)) {
                        return next();
                    } 
                }
                if (!remindable) {
                    self.billSubscriber.createRecentBill((error, data) => {
                        if (error) {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:CREATE_BILL']);
                            self.L.error('_prepareDataToInsert', 'Error in response', JSON.stringify(recentBillsData));
                            return next(error);
                        }
                        else {
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:CREATED']);
                            return next();
                        }
                    }, recentBillsData, fromRecents);
                }else{
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:REMINDABLE_PID', `OPERATOR:${operator}`, `SERVICE:${service}`]);
                    return next();
                }
                // // else {
                //     self.billSubscriber.updateRecentBill((error, data) => {
                //         if (error) {
                //             utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', 'TYPE:UPDATE_BILL']);
                //             self.L.error('_prepareDataToUpdate', 'Error in response', JSON.stringify(recentBillsData));
                //             return next(error);
                //         }
                //         else {
                //             utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:UPDATED']);
                //             return next();
                //         }
                //     }, recentBillsData, fromRecents);
                // }
            },
            next => {
                utility._sendLatencyToDD(rechargeData.timestamp, {
                    'REQUEST_TYPE': 'RECENT_BILL',
                    'URL': "recentBill",
                    'STATUS': 'CREATED',
                    'OPERATOR': recentBillsData.operator,
                    'TYPE': 'CREATED'
                });
                // TODO: Remove this once we have a proper way to handle rent payment at automatic side
                if(_.toLower(recentBillsData.service) == 'rent payment' || operatorMonthlyException){
                    return next();
                }
                // Push data to Kafka for Automatic Sync
                return self.pushToKafkaForAutomaticSync(next, recentBillsData, tableName);
            },
            next => {
                //async call for storing all payment events as cache, so that we can reject notifications to those who already paid there bills
                self.storePaymentsCache(recentBillsData,function(err){
                    return next();
                });
            },
            next => {
                // Push data to Kafka for CT events
                return self.publishCtEvents(next, recentBillsData);
            },
            next => {
                if(!_.get(recentBillsData, 'isCreditCardOperator', null) && !self.serviceAllowedDeleteNonPaytm.includes(_.toLower(recentBillsData.service))){
                    return next();
                }

                // if(!_.get(recentBillsData, 'isCreditCardOperator', null)  && !(_.get(recentBillsData, 'service', '') == 'mobile') && !(_.get(recentBillsData, 'service', null) == 'loan')){
                //     return next();
                // }
                return self.publishNonPaytmEvents(next, recentBillsData);
            },
            // next => {
            //     if(operatorMonthlyException){
            //         return next();
            //     }
                
            //     /* Update reminderNotificationEnabled=false in recents if notification_status= 2 in our DB*/
            //     if (_.get(recentBillsData, 'notifyRecents', false) === true) {
            //         self.L.log('_prepareDataToInsert', `Going to nudge recents for updating notification user preference for ${debugKey}`)
            //         let params = {
            //             "recharge_number": recentBillsData.rechargeNumber,
            //             "operator": recentBillsData.operator,
            //             "customer_id": recentBillsData.customerId,
            //             "paytype": recentBillsData.paytype,
            //             "service": recentBillsData.service,
            //             "reference_id": recentBillsData.referenceId
            //         }
            //         return self.recentsLayer.update(next, params, "reminderNotificationEnabled", false, "recentsTxn");
            //     } else {
            //         return next(null);
            //     }
            // }
        ], function (error) {
            if (error) {
                self.L.error(`_prepareDataToInsert`, `Failed with error ${error} for data ${debugKey}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            }
            return done();
        });
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`recentBills::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`recentBills::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`recentBills::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`recentBills::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`recentBills::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }

    getNonPaytmDeletePayload(record){
        let nonPaytmKafkaPayload = {
            customerId: record.customerId,
            service: record.service,
            paytype: record.paytype,
            productId: record.productId,
            operator: record.operator,
            rechargeNumber: record.rechargeNumber,
            dbEvent: 'delete'
        }
        return nonPaytmKafkaPayload;
    }

    publishNonPaytmEvents(callback, record) {
        let self = this;
        let nonPaytmKafkaPayload = [];
        const createPayload = (rechargeNum) => JSON.stringify({
            customerId: record.customerId,
            service: record.service,
            paytype: record.paytype,
            productId: record.productId,
            operator: record.operator,
            rechargeNumber: rechargeNum,
            dbEvent: 'delete'
        });

        // Add payload with original recharge number
        nonPaytmKafkaPayload.push(createPayload(record.rechargeNumber));

        // Check and add payload with alternate recharge number if enabled
        let [isOperatorPrefixEnabled, alternateRechargeNumber] = self.commonLib.getAlternateRechargeNumber(record.rechargeNumber, record.operator);
        if (isOperatorPrefixEnabled) {
            nonPaytmKafkaPayload.push(createPayload(alternateRechargeNumber));
        }

        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
            messages: nonPaytmKafkaPayload
        }], (error) => {
            if(error){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:ERROR', "TYPE:NON_PAYTM_EVENTS"]);
                self.L.critical('nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonPaytmKafkaPayload, error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RECENT_BILL", 'STATUS:PUBLISHED', "TYPE:NON_PAYTM_EVENTS", "OPERATOR:" + record.operator]);
                self.L.log('nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', nonPaytmKafkaPayload);
            }
            return callback(null)
        })
    }


    async handleElectricityRecord(record) {
        let self = this;
        let response = {'proceedWithPostpaidFlow' : false};
        try {
        const postpaidTableName = self.recentBillLibrary.getPostpaidTableNameFromConfig(record);
        if (!postpaidTableName) {
            self.L.log(`handlePrepaidElectricityRecord :: postpaid table doesn't exist - ${postpaidTableName}`);
            utility._sendMetricsToDD(1, [`REQUEST_TYPE:`, `SERVICE:${record.service}`, 'STATUS:CREATED', 'TYPE:PREPAID_ELECTRICITY_BILL']);
            return Promise.reject(`postpaid table doesn't exist`);
        }
        let prepaidTableName = postpaidTableName + '_prepaid';
        if (self.recentBillLibrary.checkIfIsPrepaidIsSet(record)) {
            self.L.log(`handleElectricityRecord :: isPrepaid flag is true`);
            await self.handlePrepaidFlagCase(record);
        } else {
            self.L.log(`handleElectricityRecord :: isPrepaid flag is false`);
            const shouldProceedWithPostpaidFlow = await self.handleNonPrepaidFlagCase(postpaidTableName, prepaidTableName, record);
            if (shouldProceedWithPostpaidFlow) {
                response.proceedWithPostpaidFlow = true;
            }
        }
        } catch (err) {
            self.L.error(`handleElectricityRecord :: Error processing record for service: ${record.service}, rechargeNumber: ${record.rechargeNumber}, operator: ${record.operator}, error: ${err}`);
            utility._sendMetricsToDD(1, [`REQUEST_TYPE:`, `SERVICE:${record.service}`, 'STATUS:ERROR', 'TYPE:PREPAID_ELECTRICITY_BILL']);
            return Promise.reject(err);
        }
        return response;
    }

  async handlePrepaidFlagCase(record) {
    let self = this;
    try {
        const postpaidTableName = self.recentBillLibrary.getPostpaidTableNameFromConfig(record);
        let postpaidRecords =_.get(record, 'transactionHistory', []);
        let prepaidTableName = postpaidTableName + '_prepaid';
        const prepaidRecordList = await self.billsModel.getBillByRechNumberServiceOperator(prepaidTableName, record);
        let existingPrepaidCustIds = prepaidRecordList.map(prepaidRecord => prepaidRecord.customer_id);
        self.L.log(` recentBills ~ handlePrepaidFlagCase ~ fetched prepaidRecords length : ${prepaidRecordList.length}, cust id list : ${existingPrepaidCustIds}`);
        if(!existingPrepaidCustIds.find(custId => custId === record.customerId)) {
            self.L.log(`handlePrepaidFlagCase :: incoming cust id is not found in DB : ${record.customerId}, creating a new record.`);
            await self.createPrepaidRecord(record, prepaidTableName);
        } else {
            self.L.log(` recentBills ~ handlePrepaidFlagCase ~ updating existing prepaid records, customer id : ${existingPrepaidCustIds}`);
            await self.updatePrepaidRecord(record, prepaidTableName);
        }
        self.checkAndUpdatePostpaidRecords(postpaidRecords, postpaidTableName, record);
    } catch (err) {
      self.L.error(`handlePrepaidFlagCase :: Error occurred: ${err.message}`);
      utility._sendMetricsToDD(1, [`REQUEST_TYPE:`, `SERVICE:${record.service}`, 'STATUS:ERROR', 'TYPE:PREPAID_ELECTRICITY_BILL']);
      return Promise.reject(err);
    }
    return Promise.resolve();
  }

  async handleNonPrepaidFlagCase(postpaidTableName, prepaidTableName, record) {
    let self = this;
    let postpaidRecordList =_.get(record, 'transactionHistory', []);
    self.L.log(`handleNonPrepaidFlagCase :: fetching prepaid records for table - ${postpaidTableName}`);
    const prepaidRecordList = await self.billsModel.getBillByRechNumberServiceOperator(prepaidTableName, record);
    let existingPrepaidCustIds = prepaidRecordList.map(prepaidRecord => prepaidRecord.customer_id);
    if(prepaidRecordList.length > 0) {
        if(!existingPrepaidCustIds.find(custId => custId === record.customerId)) {
            self.L.log(`handlePrepaidFlagCase :: incoming cust id is not found in DB : ${record.customerId}, creating a new record.`);
            await self.createPrepaidRecord(record, prepaidTableName);
        }else {
            self.L.log(` recentBills ~ handlePrepaidFlagCase ~ updating existing prepaid records, customer id : ${existingPrepaidCustIds}`);
            await self.updatePrepaidRecord(record, prepaidTableName);
        }
        self.checkAndUpdatePostpaidRecords(postpaidRecordList, postpaidTableName, record);
        return false;
    } else {
        self.L.log(`handleNonPrepaidFlagCase :: no prepaid record exist for this recharge number : ${record.rechargeNumber}, prepaid table : ${prepaidTableName}, proceeding with normal postpaid flow`);
        return true; // original postpaid flow should proceed
    }
  }

  async createPrepaidRecord(record, prepaidTableName, source) {
    const self = this;
      self.updateRecordAsPerPrepaidFlow(record, prepaidTableName);
      self.L.log(`createPrepaidRecord :: record after updating with prepaid flow - ${JSON.stringify(record)}`);
      return new Promise((resolve, reject) => {
        self.billSubscriber.createRecentBill((err, data) => {
          if (err) {
            self.L.error(`createPrepaidRecord :: Error in insert prepaid record ${prepaidTableName} table, ${JSON.stringify(record)}, error :: ${err}`);
            return reject(err);
          }
          self.L.log(`createPrepaidRecord :: successfully created record in prepaid table - ${prepaidTableName} for customerId - ${record.customerId}`);
          utility._sendMetricsToDD(1, [`REQUEST_TYPE:`, `SERVICE:${record.service}`, 'STATUS:CREATED', 'TYPE:PREPAID_ELECTRICITY_BILL']);
          return resolve(data);
        }, record, true);
      });
  }

  async updatePrepaidRecord(record, prepaidTableName) {
    const self = this;
    self.updateRecordAsPerPrepaidFlow(record, prepaidTableName);
    return new Promise((resolve, reject) => {
        self.billSubscriber.updateRecentBill((err, data) => {
          if (err) {
            self.L.error(`updatePrepaidRecord :: Error in insert prepaid record ${prepaidTableName} table, ${JSON.stringify(record)}, error :: ${err}`);
            return reject(err);
          }
          self.L.log(`updatePrepaidRecord :: successfully created record in prepaid table - ${prepaidTableName} for customerId - ${record.customerId}`);
          utility._sendMetricsToDD(1, [`REQUEST_TYPE:`, `SERVICE:${record.service}`, 'STATUS:CREATED', 'TYPE:PREPAID_ELECTRICITY_BILL']);
          return resolve(data);
        }, record, true);
      });
  }

  updateRecordAsPerPrepaidFlow(record, prepaidTableName) {
    const self = this;
    record.tableName = prepaidTableName;
    record.dueDate = record.prePaidDueDate;
    record.nextBillFetchDate = record.prePaidNextBillFetchDate;
    record.amount = record.prepaidAmount;
    self.recentBillLibrary.updateIsPrepaidFlag(record, "1");
    self.L.log(` recentBills ~ updateRecordAsPerPrepaidFlow ~ updated record.dueDate : ${record.dueDate}, record.nextBillFetchDate :  ${record.nextBillFetchDate}, record.amount : ${record.amount}, record.extra : ${JSON.stringify(record.extra)}`);
  }

  async checkAndUpdatePostpaidRecords(postpaidRecordList, postpaidTableName, record){
    let self=this;
    const recordsToUpdate = postpaidRecordList ? postpaidRecordList.filter(postpaidRecord => postpaidRecord.status != 13 && postpaidRecord.reason != 'PREPAID_IDENTIFIED'):[];
    if (recordsToUpdate.length > 0) {
      self.L.log(`Found postpaid record with status != 13 and reason != PREPAID_IDENTIFIED, updating.`);
      self.prepaidFlowManager.updatePostpaidTableWithStatus13AndReasonPrepaid(postpaidTableName, record);
    }
  }
}
export default RecentBills;
