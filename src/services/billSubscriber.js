import RQ from 'rqueue'
import <PERSON><PERSON><PERSON> from 'async'
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator'
import MOMENT from 'moment'
import REQUEST from 'request'
import _ from 'lodash'
import digitalUtility from 'digital-in-util'

import BILLS from '../models/bills'
import preparePayload from '../models/preparePayload'
import publishToKafka from '../models/publishToKafka'
import USERS from '../models/users'

import utility from '../lib'
import LOCALISATION from '../lib/localisation'
import ENCDECPUTIL from '../lib/EncryptionDecryptioinHelper'
import RemindableUsersLibrary from '../lib/remindableUser'
import BillsLibrary from '../lib/bills'
import CATALOGVERTIC<PERSON><PERSON>CHARGE from '../models/catalogVerticalRecharge';
import BillFetchAnalytics from '../lib/billFetchAnalytics.js'
import recentBillLibrary from '../lib/recentBills';

import Archive_records from '../crons/archive_records.js'
import Logger from '../lib/logger'

let L = null;
let operatorCountMapping = {};

/*
   The only subscriber service which receives the messages passed by Gateway services
   and updates the bills in the DB tables corresponding to the operator name.
   Things that should be passed to constructor
 */
class BillSubscriber {

    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.encDecpUtil = new ENCDECPUTIL(options);
        this.bills = new BILLS(options);
        this.preparePayload = new preparePayload(options);
        this.publishToKafka = new publishToKafka(options);
        this.users = new USERS(options);
        this.startTime = MOMENT(_.get(this.config, ['SUBSCRIBER_CONFIG', 'START_TIME'], '00:00:00'), "HH:mm:ss");
        this.endTime = MOMENT(_.get(this.config, ['SUBSCRIBER_CONFIG', 'END_TIME'], '06:00:00'), "HH:mm:ss");
        this.timeDiff = parseInt(MOMENT.duration(this.endTime.diff(this.startTime)).asMinutes());
        this.allowedNotificationStatuses = _.invert(_.get(this.config, 'COMMON.notification_status', {}));
        this.statusInWhichAmountHasToBeUpdated = [
            _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5),
            _.get(this.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6),
            _.get(this.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14)
        ];
        this.localisationParams = {
            service: _.get(this.config, ['SUBSCRIBER_CONFIG', 'LOCALISATION_SERVICE'], 'DIGITALRECHARGE'),
            language: _.get(this.config, ['SUBSCRIBER_CONFIG', 'LOCALISATION_LANGUAGE'], 'all'),
            interval: _.get(this.config, ['SUBSCRIBER_CONFIG', 'LOCALISATION_TIMEINTERVAL'], 15) // Map refreshes after 15 mins interval
        };
        this.operatorConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.localisation_client = new options.LOCALISATION_CLIENT();
        // this.localisation_client.init(function(data){L.info('init::',data);},this.localisationParams);
        this.remindableUsersLibrary = new RemindableUsersLibrary(options);
        this.operatorsWithRegisteredUserRecents = _.get(this.config, 'COMMON.operatorsWithRegisteredUserRecents', []);
        this.infraUtils = options.INFRAUTILS;
        this.operatorConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.commonLib = new utility.commonLib(options);
        this.billsLib = new BillsLibrary(options);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.billFetchAnalytics = new BillFetchAnalytics(options)
        this.recentBillLibrary = new recentBillLibrary(options);
        this.allowedServicesForCustomNBFDForOldBill = _.get(this.config, ['DYNAMIC_CONFIG', 'BILLS_SUBSCRIBER_CONFIG', 'COMMON', 'ALLOWED_SERVICES_FOR_CUSTOM_NBFD_FOR_OLD_BILL'], ["electricity"]);
        this.oldBillFetchDueDateAllowedService = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        this.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], ['electricity']);
        this.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.logger = new Logger(options);

        // converting this array to json onetime to avoid lookup everytime 
        let self = this;
        this.billDateBasedGatewaysArr = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        this.billDateBasedGateways = {};
        this.billDateBasedGatewaysArr.forEach(function (operator) {
            self.billDateBasedGateways[operator] = true;
        });
        this.greyScaleEnv = options.greyScaleEnv;
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: billSubscriber", "Re-initializing variable after interval");
        self.operatorConfig = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        self.billDateBasedGatewaysArr = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        self.billDateBasedGatewaysArr.forEach(function (operator) {
            self.billDateBasedGateways[operator] = true;
        });
        self.allowedServicesForCustomNBFDForOldBill = _.get(self.config, ['DYNAMIC_CONFIG', 'BILLS_SUBSCRIBER_CONFIG', 'COMMON', 'ALLOWED_SERVICES_FOR_CUSTOM_NBFD_FOR_OLD_BILL'], ["electricity"]);
        self.oldBillFetchDueDateAllowedService = _.get(self.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'ALLOWED_SERVICES'], ['electricity']);
        self.oldBillFetchDueDateBlacklistedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'OLD_BILL_FETCH_CONFIG', 'COMMON', 'BLACKLISTED_OPERATORS'], ['electricity']);
        self.ottOperatorList =  _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_OTT', 'COMMON', 'INCLUDE_OPERATOR'], 'disneyhotstar');
        self.ottOperators = this.ottOperatorList.split(',').map((e) => e.trim());
        self.lowBalancePrepaidElectricityAllowedOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
    }

    start() {
        let self = this;
        self.startDummyLogs();
        L.log("start", "configuring subscriber");
        let sourceObj = {
            'SOURCE' : 'BILL_SUBSCRIBER'
        };
        // configure Kafka Publisher to push bill fetch events to Kafka
        self._configureKafkaPublisher(function (error) {
            if (error) {
                L.critical('subscriber :: start', 'Error while configuring Kafka Publisher...', error);
            } else {
                L.log('subscriber :: start', 'Kafka Publisher configured....');
                // Now configure RMQ subscriber...
                return self._configureSubscriber();
            }
        }, sourceObj);
    } 

    _configureKafkaPublisher(cb, sourceObj) {
        let
            self = this;  
        try {
            let kafkaPublisherPromise = new Promise((resolve, reject)=>{
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */
                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error, message) {
                    if(error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:AUTOMATIC',
                            'STATUS:ERROR',
                            'TYPE:ERROR_EMITTED_BY_PRODUCER',
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                            `SOURCE:${_.get(sourceObj, ['SOURCE'] , '')}`,
                            `OPERATOR:${_.get(sourceObj, ['OPERATOR'] , '')}`
                        ]);
                        L.critical("_configureKafkaPublisher", `${'kafkaPublisher::Error MSG:'+error}`);
                        reject(error);
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:AUTOMATIC',
                            `STATUS:SUCCESS`,
                            `TYPE:${message}`,
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                            `SOURCE:${_.get(sourceObj, ['SOURCE'] , '')}`,
                            `OPERATOR:${_.get(sourceObj, ['OPERATOR'] , '')}`
                        ]);
                        L.log("_configureKafkaPublisher", `${'kafkaPublisher::Producer MSG:'+message}`);
                        resolve();
                    }
                });
            });

            let kafkaBillFetchPublisherPromise = new Promise((resolve, reject)=>{
                /**
                 * Kafka Publisher to publish bill fetch to Reminder Pipeline
                 */
                self.kafkaBillFetchPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                self.kafkaBillFetchPublisher.initProducer('high', function (error, message) {
                    if(error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
                            'STATUS:ERROR',
                            'TYPE:ERROR_EMITTED_BY_PRODUCER',
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                            `SOURCE:${_.get(sourceObj, ['SOURCE'] , '')}`,
                            `OPERATOR:${_.get(sourceObj, ['OPERATOR'] , '')}`
                        ]);
                        L.critical("_configureKafkaPublisher", `${'kafkaBillFetchPublisher::Error MSG:'+error}`);
                        reject(error);
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
                            `STATUS:SUCCESS`,
                            `TYPE:${message}`,
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                            `SOURCE:${_.get(sourceObj, ['SOURCE'] , '')}`,
                            `OPERATOR:${_.get(sourceObj, ['OPERATOR'] , '')}`
                        ]);
                        L.log("_configureKafkaPublisher", `${'kafkaBillFetchPublisher::Producer MSG:'+message}`);
                        resolve();
                    }
                });
            });

            let kafkaBillFetchRealTimePublisherPromise = new Promise((resolve, reject)=>{
                /**
                 * Kafka Publisher to publish real time bill fetch to Reminder Pipeline
                 */
                self.billFetchKafkaRealtime = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                self.billFetchKafkaRealtime.initProducer('high', function (error, message) {
                    if(error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
                            'STATUS:ERROR',
                            'TYPE:ERROR_EMITTED_BY_PRODUCER',
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                            `SOURCE:${_.get(sourceObj, ['SOURCE'] , '')}`,
                            `OPERATOR:${_.get(sourceObj, ['OPERATOR'] , '')}`
                        ]);
                        L.critical("_configureKafkaPublisher", `${'kafkaBillFetcRealTimehPublisher::Error MSG:'+error}`);
                        reject(error);
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
                            `STATUS:SUCCESS`,
                            `TYPE:${message}`,
                            'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                            `SOURCE:${_.get(sourceObj, ['SOURCE'] , '')}`,
                            `OPERATOR:${_.get(sourceObj, ['OPERATOR'] , '')}`
                        ]);
                        L.log("_configureKafkaPublisher", `${'kafkaBillFetchRealTimePublisher::Producer MSG:'+message}`);
                        resolve();
                    }
                });
            });

            let ctKafkaPublisherPromise = new Promise((resolve , reject)=>{
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });

                self.ctKafkaPublisher.initProducer('high', function (error, message) {
                    if(error) {
                        L.critical("_configureKafkaPublisher", `${'ctKafkaPublisher::Error MSG:'+error}`);
                        reject(error);
                    } else {
                        L.log("_configureKafkaPublisher", `${'ctKafkaPublisher::Producer MSG:'+message}`);
                        resolve();
                    }
                });
                
            })

            let cdcRecoveryPublisherPromise = new Promise((resolve , reject)=>{
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.cdcRecoveryPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CASSANDRA_CDC.HOSTS
                });

                self.cdcRecoveryPublisher.initProducer('high', function (error, message) {
                    if(error) {
                        L.critical("_configureKafkaPublisher", `${'cdcRecoveryPublisher::Error MSG:'+error}`);
                        reject(error);
                    } else {
                        L.log("_configureKafkaPublisher", `${'cdcRecoveryPublisher::Producer MSG:'+message}`);
                        resolve();
                    }
                });
                
            })

            let nonPaytmPublisherPromise = new Promise((resolve, reject) => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline
                 */
                self.nonPaytmPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });

                self.nonPaytmPublisher.initProducer('high', function (error, message) {
                    if (error) {
                        L.critical("_configureKafkaPublisher", `${'nonPaytmPublisher::Error MSG:' + error}`);
                        reject(error);
                    } else {
                        L.log("_configureKafkaPublisher", `${'nonPaytmPublisher::Producer MSG:' + message}`);
                        resolve();
                    }
                });

            })

            Promise.all([kafkaPublisherPromise, kafkaBillFetchPublisherPromise, ctKafkaPublisherPromise, kafkaBillFetchRealTimePublisherPromise, cdcRecoveryPublisherPromise, nonPaytmPublisherPromise]).then((values) => {
                L.log("_configureKafkaPublisher", `${'Promise.all::then:'+values}`);
                return cb();
              }).catch((error)=>{
                L.critical('_configureKafkaPublisher', 'Promise.all::catch:', error);
                return cb(error);
              });

        } catch (error) {
            L.critical('_configureKafkaPublisher', 'catch error - ', error);
            return cb(error);
        }
    }

    _configureKafkaBillFetchPublisher(done) {
        /**
         * Kafka Publisher to publish bill fetch to Reminder Pipeline
         */
        let self = this;
        self.kafkaBillFetchPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
        });
        self.kafkaBillFetchPublisher.initProducer('high', function (error) {
            return done(error);
        });
    }

    _configureKafkaBillFetchPublisherRealtime(done) {
        /**
         * Kafka Publisher to publish bill fetch to Reminder Realtime Pipeline
         */
        let self = this;
        self.billFetchKafkaRealtime = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
        });
        self.billFetchKafkaRealtime.initProducer('high', function (error) {
            if (error){
                self.L.critical('error in initialising billFetchKafka Producer :: ', error);
            }
            else{
                self.L.log("VALIDATION SYNC :: billFetchKafka realtime KAFKA PRODUCER STARTED....");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VALIDATION_SYSNC", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:REMINDER_BILLFETCH_REALTIME']);
            }
            return done(error)
        });
    }

    _configureCtPublisher(done) {
        let self = this;
        /**
         * Kafka publisher to publish events to CT publisher pipeline 
         */
        self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": self.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
        });
        this.ctKafkaPublisher.initProducer('high', function (error) {
            return done(error)
        });
    }

    _configureSubscriber() {
        let self = this,
            ex = {
                NAME: _.get(self.config, 'RABBITMQ_CONFIG.CLM_EXCHANGE_NAME', 'ex_clm'),
                TYPE: _.get(self.config, 'RABBITMQ_CONFIG.CLM_EXCHANGE_TYPE', 'topic'),
                OPTIONS: {
                    durable: true,
                    internal: false,
                    autoDelete: false
                }
            },
            queue = {
                NAME: _.get(self.config, 'RABBITMQ_CONFIG.BILL_REMINDER_QUEUE_NAME', 'q_bill_reminder'),
                BINDINGKEY: _.get(self.config, 'RABBITMQ_CONFIG.BILL_REMINDER_BINDINGKEY', 'gateway.*.CUSTOMERDATA'),
                OPTIONS: {
                    exclusive: false,
                    durable: true,
                    autoDelete: false
                },
                CONSUMEOPTS: {
                    noAck: false,
                },
                PREFETCH: self.greyScaleEnv ? _.get(self.config, 'RABBITMQ_CONFIG.GREYSCALE_PREFETCH_MSG_COUNT', 1) : _.get(self.config, 'RABBITMQ_CONFIG.PREFETCH_MSG_COUNT', 20)
            };

        let rabbitConfig = _.get(self.config, 'RABBITMQ_CONFIG.RABBITMQ', null)
        self.reminderSubscriber = new RQ({ RABBITMQ: rabbitConfig }).getSimpleSubscriber(ex, queue);

        L.log("_configureSubscriber", "Subscriber Configured");

        self.reminderSubscriber.on('message', function (data) {
            if(self.greyScaleEnv) {
                setTimeout(function(){
                    self.processMessage(data);
                },_.get(self.config, 'RABBITMQ_CONFIG.GREYSCALE_PROCESSING_DELAY', 1*60*1000));
            } else {
                self.processMessage(data);
            }
        });

        self.reminderSubscriber.start({}, function () {
            L.log("_configureSubscriber", "subscriber started");
        });
    }

    processMessage(data) {
        var self = this;
        let originalMessage = data;

        data = _.cloneDeep(originalMessage)
        try {
            data = JSON.parse(VALIDATOR.toString(_.get(data, 'content')));
            if (data) {
                ASYNC.waterfall([
                    (next) => {
                        self.dumpInSQL(data, (err, billData) => {
                            if (err) {
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CONSUMED', 'STATUS:RECORDS_UNPROCESSED']);
                            }
                            else {
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CONSUMED', 'STATUS:RECORDS_PROCESSED']);
                            }
                            next(null, billData)
                        })
                    },
                    (billData, next) => {
                        /*
                            Commenting the code for now, until we figure out how to pass customer specific details to Subscription service
                        */
                        /*
                            if(billData) {
                                return self._updateSubscriptionDB(billData, next)
                            }
                            */
                        next()
                    }
                ],
                    (err) => {
                        //send ack to rabbitmq in any case
                        self._ackMessage(originalMessage);
                    })
            }
            else {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CONSUMED', 'STATUS:RECORDS_UNPROCESSED']);
                self._ackMessage(originalMessage);
            }
        }
        catch (err) {
            L.critical('_configureSubscriber ::', 'Error while performing sync.', err);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:CONSUMED', 'STATUS:RECORDS_UNPROCESSED']);
            self._ackMessage(originalMessage);
        }
    }

    _ackMessage(message) {
        let self = this;
        try {
            self.reminderSubscriber.ack(message);
        } catch (err) {
            L.critical('billSubscriber::_ackMessage', 'Error while ack message.', err);
        }
    }

    _updateSubscriptionDB(billData, callback) {
        let self = this,
            payload = {
                rechargeNumber: billData.rechargeNumber,
                productId: billData.productId,
                amount: billData.amount,
                dueDate: billData.billDueDate,
                billFetchDate: billData.billFetchDate,
                nextBillFetchDate: billData.nextBillFetchDate
            }

        REQUEST({
            url: `${_.get(self.config, 'COMMON.subscriptionServiceUrl', '')}`,
            method: 'POST',
            headers: {
                'content-type': 'application/json'
            },
            body: JSON.stringify(payload)
        }, (err, resp, body) => {
            if (err || !body) {
                L.critical(`_updateSubscriptionDB: error occured while hitting the Subscription Service API for rechargeNumber: ${billData.rechargeNumber}, productId: ${billData.productId}`);
                return callback(err);
            }
            callback();
        })
    }

    dumpInSQL(data, cb, dbData) {
        let
            self = this;

        ASYNC.waterfall([
            next => {
                if(!dbData){
                    let
                        operator = _.get(data, 'productInfo.operator', null),
                        rechargeNumber = _.get(data, 'userData.recharge_number', null),
                        service = _.get(data, 'productInfo.service', ''),
                        customerId = _.get(data, 'customerInfo.customer_id', null),
                        tableName = self.getTableName(_.get(data, 'catalogProductID', null), operator)

                    return self.bills.getBillsRecord(next, tableName, operator, service, rechargeNumber, customerId);
                } else {
                    next(null,dbData);
                }
            },
            (record, next) => {
                if (_.get(record, 'length', 0) == 0) {
                    return next('No Record exists');
                }
                return self._dumpInSQL(data, record[0], next);
            }
        ], function (error) {
            if (error) {
                self.logger.error(`dumpInSQL :: Error while updating bill fetch data ${error}`, data, _.get(data, 'productInfo.service', ''));
            }
            return cb();
        });
    }
    /**
     * remove all instances of regex from data object
     * @param {object} data
     * @param {string} regex should be a valid regex
     */
    // 
    stringCleanup(data, regex) {
        if(!data || data === "undefined" || typeof data !== 'object') {
            return data
        }

        for(const [key, value] of Object.entries(data)) {
            if(value && typeof value === 'string') {
                data[key] = value.replace(regex, '')
            }
        }
        return data
    }

    _dumpInSQL(data, currentRecord, cb) {
        try {
        /* Although active PID is desired for this flow, Still Fetching active PID and updating it to be double sure */
        let self = this;
        /** data.oldCatalogProductID already mapped with old PId
         *  data.catalogProductID already mapped with new active PId
         */
        data.oldCatalogProductID = data.catalogProductID;
        data.catalogProductID = self.activePidLib.getActivePID(data.catalogProductID);
        /* Keeping existing and updated records in data */
        data.currentRecord = currentRecord;
        data.customerDataResponse = this.stringCleanup(data.customerDataResponse ,/\[ object object\]/gi)

        let
            /*name of the table in which the record resides*/
            tableName = self.getTableName(_.get(data, 'catalogProductID', null), _.get(data, 'productInfo.operator', null)),
            /*'recharge_number' and 'productId' will be needed in where clause of the update table query*/
            rechargeNumber = _.get(data, 'userData.recharge_number', null),
            productId = _.get(data, 'catalogProductID', null),

            /*operator name would be needed to get the meta_data from config e.g tableName, next_bill_frequency etc. etc.,bcoz all our configs are operator name based*/
            operator = _.get(data, 'productInfo.operator', ''),
            service = _.get(data, 'productInfo.service', ''),
            gateway = _.get(data,'currentGw',''),

            /*amount, billDueDate (if present) will be saved as it is in DB*/
            amount = utility.getFilteredAmount(_.get(data, 'customerDataResponse.currentBillAmount', '0')),
            parsedDueDateObj = utility.getFilteredDate(_.get(data, 'customerDataResponse.billDueDate', null)),
            billDueDate = parsedDueDateObj.value,
            isDateFmtValid = parsedDueDateObj.isDateFmtValid,

            /*billDate, customNextBillFetchDate, lastDueDate, billDateBasedGateways will be used to calculate the next_bill_fetch_date of the record*/
            billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []),
            parsedBillDateObj = utility.getFilteredDate(_.get(data, 'customerDataResponse.billDate', null)),
            billDateFromGateway = parsedBillDateObj.value,
            // billDateFromGateway = utility.getFilteredDate(_.get(data, 'customerDataResponse.billDate', null)).value,
            billDate = billDateBasedGateways.indexOf(operator) > -1 ? billDateFromGateway : null,

            customNextBillFetchDate = null,
            nextBillFetchDate = null,
            gatewayNbfd = utility.getFilteredDate(_.get(data, 'customerDataResponse.nextBillFetchDate', null)).value,
            lastDueDate = _.get(currentRecord, 'due_date', null) === null ?
                MOMENT('0001-01-01') :
                MOMENT(_.get(currentRecord, 'due_date')),

            lastBillDate = _.get(currentRecord, 'bill_date', null) === null ?
                MOMENT('0001-01-01') :
                MOMENT(_.get(currentRecord, 'bill_date')),

            lastBillFetchDate = _.get(currentRecord, 'bill_fetch_date', null) === null ?
                null :
                MOMENT(_.get(currentRecord, 'bill_fetch_date')),

            lastAmount = _.get(currentRecord, 'amount', null),
            paymentDone=_.get(data,'customerDataResponse.paymentDone',false),
            isConnectionError = _.get(data, 'validationGwResponse.connectionError', false),
            deducedStatus = _.get(data, 'validationGwResponse.deducedStatus', true),
            noBill = _.get(data, 'validationGwResponse.noBill', false),

            errorMessageCode = _.get(data, 'validationGwResponse.errorMessageCode', null) ? Number(_.get(data, 'validationGwResponse.errorMessageCode', null)) : NaN,
            frontendErrorMessage = _.get(data, 'validationGwResponse.frontendErrorMessage', ''),
            extraDetails = _.get(data, 'extra', {}),
            custInfoValues = _.get(data, 'customerDataResponse', null),
            serviceId = parseInt(_.get(data, 'serviceId', 0)),
            source = _.get(data, 'source'),
            lastStatus = _.get(data, 'status', 0),
            updatedAmount = null,
            updatedBillDate = null,
            updatedDueDate = null,
            productInfoMap = _.get(this.config, "productInfoMap", []),
            errorMessageCodeStr = String(_.get(data, 'validationGwResponse.errorMessageCode', '')),
            currentAutomaticStatus = _.get(currentRecord, 'is_automatic', 0);

        if(_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'EXCLUDE_AMOUNT_OPERATORS'], []) .indexOf(operator) > -1){
            amount  = null;
            //_.get(payLoad, 'customerDataResponse.currentBillAmount', null);
        }

        self.sendDateForAnalytics({            
            operator: _.get(data, 'productInfo.operator', 'NOT_RECEIVED'),
            productId: _.get(data, 'catalogProductID', 'NOT_RECEIVED'),
            billDueDate: _.get(data, 'customerDataResponse.billDueDate', 'NOT_RECEIVED'),
            billDate:  _.get(data, 'customerDataResponse.billDate', 'NOT_RECEIVED'),
            parsedDueDateObj : parsedDueDateObj,
            parsedBillDateObj : parsedBillDateObj,
            rechargeNumber: rechargeNumber
        });

        if (_.get(data, 'prepaidPublisherInstance', false)) {
            tableName = _.get(data, 'tableName', tableName);
        }

        try {
            if (_.get(data, 'source', null) != 'ffr' && errorMessageCode) {
                var keyComponents = [
                    'en-IN',
                    errorMessageCode,
                    'VALIDATION',
                    _.get(productInfoMap[productId], "verticalId", ''),
                    _.get(productInfoMap[productId], "merchant_id", '')
                ],
                    payload = {
                        'paytype_label': _.get(productInfoMap[productId], "paytype_label", ''),
                        'operator_label': _.get(productInfoMap[productId], "operator_label", ''),
                        'recharge_number': rechargeNumber
                    },
                    textMessage = LOCALISATION.getDefaultEnglishMessage(
                        self.localisation_client,
                        keyComponents,
                        true,
                        payload

                    );
                if (textMessage) frontendErrorMessage = textMessage
            }
            if (typeof extraDetails == 'string') {
                extraDetails = JSON.parse(extraDetails);
            }
            if (typeof extraDetails == 'string') {
                extraDetails = {};
                //L.critical('billSubscriber::_dumpInSQL', 'Error in parsing extra details.', 'customer_id:', _.get(currentRecord, 'customer_id', null), 'rechargeNumber:', rechargeNumber);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EXTRA_PARSING_ERROR', `OPERATOR:${operator}`, 'SOURCE:CC_PUBLISHER', 'FIELD:extra']);
            }
        } catch (ex) {
            L.error('billSubscriber::_dumpInSQL', 'Error in parsing extra details.', ex);
            extraDetails = {};
        }
        if (!extraDetails) {
            extraDetails = {};
        }
        let amountToLog = amount ? amount.toString() : '';
        let billDueDateToLog = billDueDate ? billDueDate.toString() : '';
        let lastAmountToLog = lastAmount ? lastAmount.toString() : '';
        if(service == 'financial services') {
            L.log('billSubscriber::_dumpInSQL Bill fetched for'
                + ' Recharge Number: ' + this.encDecpUtil.encryptData(rechargeNumber)
                + ' amount: ' + this.encDecpUtil.encryptData(amountToLog)
                + ' productId: ' + productId
                + ' Old productId : ' + data.oldCatalogProductID
                + ' isConnectionError: ' + isConnectionError
                + ' deducedStatus: ' + deducedStatus
                + ' noBill: ' + noBill
                + ' Bill Date: ' + billDateFromGateway
                + ' Due Date: ' + this.encDecpUtil.encryptData(billDueDateToLog)
                + ' paymentDone: ' + paymentDone
                + ' lastBillFetchDate: ' + (lastBillFetchDate != null && lastBillFetchDate.isValid() ? lastBillFetchDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
                + ' lastAmount: ' + this.encDecpUtil.encryptData(lastAmountToLog)
                + ' lastStatus:' + lastStatus);
        } else {
            L.log('billSubscriber::_dumpInSQL Bill fetched for'
                + ' Recharge Number: ' + rechargeNumber
                + ' amount: ' + amount
                + ' productId: ' + productId
                + ' Old productId : ' + data.oldCatalogProductID
                + ' isConnectionError: ' + isConnectionError
                + ' deducedStatus: ' + deducedStatus
                + ' noBill: ' + noBill
                + ' Bill Date: ' + billDateFromGateway
                + ' Due Date: ' + billDueDate
                + 'paymentDone: ' + paymentDone
                + ' lastBillDate: ' + (lastBillDate != null && lastBillDate.isValid() ? lastBillDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
                + ' lastDueDate: ' + (lastDueDate != null && lastDueDate.isValid() ? lastDueDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
                + ' lastBillFetchDate: ' + (lastBillFetchDate != null && lastBillFetchDate.isValid() ? lastBillFetchDate.format('DD-MM-YYYY HH:mm:ss') : 'NA')
                + ' lastAmount: ' + lastAmount
                + ' lastStatus:' + lastStatus);
        }


        if (!self._sanityChecks(tableName, operator, rechargeNumber, productId)) {
            L.error('_dumpInSQL:: ', '_sanityChecks failed')
            cb(new Error('_sanityChecks failed'));
            return false
        }
            if ((!parsedBillDateObj.isDateFmtValid || parsedBillDateObj.value == null) && _.get(self.config, ['DYNAMIC_CONFIG', 'SERVICE_CONFIG', service, 'ALLOW_INVALID_BILL_DATE'], ['loan']).indexOf(service) > -1) {
                billDate = billDateBasedGateways.indexOf(operator) > -1 ? MOMENT() : null;
            }
            if (!parsedDueDateObj.isDateFmtValid && _.get(self.config, ['DYNAMIC_CONFIG', 'SERVICE_CONFIG', service, 'ALLOW_INVALID_DUE_DATE'], ['loan']).indexOf(service) > -1) {
                billDueDate = null;
                isDateFmtValid = true;
            }


        //If amount > 0, billDueDate is not found and pastDueDate has expired => dueDate = NOW + 7
        var defaultNextBillDueDate = MOMENT().add(7, 'days');
        if (!gatewayNbfd) {
            customNextBillFetchDate = self._getCustomBillFetchDateAsPerOperator(serviceId, operator, billDueDate, rechargeNumber);
        }
        // constantnbfd covers nbfd from gateway and where nbfd is set to single day every month like 26
        let evaluatedNbfd = self._getConstantNextBillFetchDate(gatewayNbfd, operator);
        let paymentDate = _.get(currentRecord , 'payment_date',null)
        paymentDate = MOMENT(paymentDate).isValid()? MOMENT(paymentDate) : null
        // ---- calculate next bill fetch date & status----//
        let result = self._calcNextBillFetchDate({
            service,
            operator,
            amount,
            customNextBillFetchDate,
            evaluatedNbfd,
            billDate,
            billDueDate,
            lastDueDate,
            lastBillDate,
            isConnectionError,
            paymentDone,
            deducedStatus,
            noBill,
            isDateFmtValid,
            defaultNextBillDueDate,
            errorMessageCode,
            tableName,
            lastAmount,
            productId,
            oldProductId: data.oldcatalogProductID,
            serviceId,
            billDateBasedGateways,
            custInfoValues,
            paymentDate,
            gateway,
            extraDetails,
            errorMessageCodeStr,
            currentAutomaticStatus,
            currentRecord
        });

        let isSameBillAsUPMS = false;
        // If existing bill of UPMS and Same amount and due date received in validation
        if (!self._shouldUpdateBill(lastAmount, lastDueDate, amount, billDueDate, extraDetails, operator)) {
            isSameBillAsUPMS = true;
        }

            let updateMongo = true;
            let setDueDateNull = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, 'ALLOW_NULL_DUE_DATE'], _.get(self.config, ['DYNAMIC_CONFIG', 'SERVICE_CONFIG', service, 'ALLOW_NULL_DUE_DATE'], false));
        if (amount > 0 && billDueDate === null && lastDueDate && lastDueDate.diff(MOMENT()) < -1) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:DUEDATE_NOW_PLUS_7', `OPERATOR:${operator}`]);
            billDueDate = setDueDateNull ? null : defaultNextBillDueDate;
            updateMongo = false;
        }

        let billFetchDate = null,
            oldBillFetchDate = null;

        //Convert to required formats
        billDueDate = billDueDate && billDueDate.format('YYYY-MM-DD')
        billDate = billDate && billDate.format('YYYY-MM-DD')
        nextBillFetchDate = result.nextBillFetchDate && result.nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');

        if(service == 'financial services') {
            L.log('_dumpInSQL::calcNextBillFetchDate Recharge_number: ' + this.encDecpUtil.encryptData(rechargeNumber) + ' nextBillFetchDate: ' + nextBillFetchDate + ' status: ' + result.status + ' amount: ' + this.encDecpUtil.encryptData(amount) + ' billDueDate: ' + this.encDecpUtil.encryptData(billDueDate) + ' billDate: ' + billDate + ' isSameBillAsUPMS: ' + isSameBillAsUPMS);
        }  else {
            L.log('_dumpInSQL::calcNextBillFetchDate Recharge_number: ' + rechargeNumber + ' nextBillFetchDate: ' + nextBillFetchDate + ' status: ' + result.status + ' amount: ' + amount + ' billDueDate: ' + billDueDate + ' billDate: ' + billDate + ' isSameBillAsUPMS: ' + isSameBillAsUPMS);
        }
        
        if (errorMessageCode == 1033) {
            extraDetails.BBPSBillFetch = false;
        }   
        if (_.get(data, 'customerDataResponse.isGroupDisplayEnabled', null) === "0") {
            extraDetails.isGroupDisplayEnabled = false;
        }
        else if (_.get(data, 'customerDataResponse.isGroupDisplayEnabled', null) === "1") {
            extraDetails.isGroupDisplayEnabled = true;
        }
        else {
            extraDetails.isGroupDisplayEnabled = null;
        }
        if (_.get(data, 'customerDataResponse.isAmountEditable', null) === "0") {
            extraDetails.isAmountEditable = false;
        }
        else if (_.get(data, 'customerDataResponse.isAmountEditable', null) === "1") {
            extraDetails.isAmountEditable = true;
        }
        else {
            extraDetails.isAmountEditable = null;
        }
        extraDetails.errorMessageCode = errorMessageCode;
        extraDetails.frontendErrorMessage = frontendErrorMessage;
        extraDetails.updated_data_source = source != null ? source : "publisher";
        //Checking if we can get the updated amount from gateway.
        if (_.indexOf(self.statusInWhichAmountHasToBeUpdated, result.status) >= 0) {
            updatedAmount = result.amount;
            updatedBillDate = billDateFromGateway && billDateFromGateway.format('YYYY-MM-DD');
            updatedDueDate = billDueDate;
            if(!isSameBillAsUPMS){
                extraDetails.billSource = source != null ? source : "publisher";
            }

            //Last successful bill fetch date
            if (result.status == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)) {
                billFetchDate = MOMENT().format('YYYY-MM-DD HH:mm:ss');

                extraDetails.lastSuccessBFD = _.get(extraDetails, 'billFetchDate', null);
                extraDetails.billFetchDate = billFetchDate;
                extraDetails.lastDueDt = lastDueDate && lastDueDate.format('YYYY-MM-DD HH:mm:ss');
                extraDetails.lastBillDt = lastBillDate && lastBillDate.format('YYYY-MM-DD HH:mm:ss');
                extraDetails.lastAmount = amount;
                extraDetails.source_subtype_2 = 'FULL_BILL'
                if(_.get(custInfoValues,'earlyPaymentDate',null) != null){
                    let filteredDate = utility.getFilteredDate(_.get(custInfoValues,'earlyPaymentDate',null))
                    if(filteredDate.value != null)
                        custInfoValues.earlyPaymentDate = MOMENT(filteredDate.value).format('YYYY-MM-DD HH:mm:ss');
                }
            }
            // extraDetails.displayValues = displayValues;
        } else {
            updatedAmount = null;
            updatedBillDate = null;
            updatedDueDate = null;
            // extraDetails.displayValues = {};
        }

        if (result.status == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5) && (self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(_.get(currentRecord, 'service', null))) > -1) && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(_.get(currentRecord, 'operator', null))) > -1)) {
            if (_.get(currentRecord, 'old_bill_fetch_date', null) != null)
                oldBillFetchDate = MOMENT(_.get(currentRecord, 'old_bill_fetch_date', null)).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            else
                oldBillFetchDate = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
        }

        //common parameters object which would be saved in DB
        let billData = {
            id: _.get(currentRecord, 'id'),
            nextBillFetchDate: nextBillFetchDate,
            billFetchDate: billFetchDate,
            amount: updatedAmount,
            billDueDate: updatedDueDate,
            rechargeNumber: rechargeNumber,
            productId: productId,
            errorMsgCode: errorMessageCode,
            paymentDone: paymentDone,
            source: source,
            dbDueDate: lastDueDate && lastDueDate.format('YYYY-MM-DD'),
            dbAmount: lastAmount,
            serviceId: serviceId,
            dbStatus: lastStatus,
            customerId: _.get(data, 'customerInfo.customer_id', null),
            customerMobile: _.get(data, 'customerInfo.customer_mobile', null),

            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            operator: operator,
            service: service,
            paytype: _.get(currentRecord, 'paytype'),
            setDueDateNull:setDueDateNull,
            bankName: _.get(currentRecord, 'bank_name', null),
            oldBillFetchDate: oldBillFetchDate,
            prepaidPublisherInstance: _.get(data, 'prepaidPublisherInstance', false),
            tableName: _.get(data, 'tableName', tableName)
        };

        if (_.get(data, 'prepaidPublisherInstance', false)) {
            billData.prepaidPublisherInstance = true;
        }

        try{
            billData = self.billsLib.updateRecordWithOffsetNbfd(billData, _.get(currentRecord, 'customer_bucket',null), MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        }catch(e){
            self.L.error('Error in updating record with offset nbfd',e)
        }

        
        var subsDBUpdate = false;
        //Its a migrated operator, so saving it via new flow
        // if(_.get(self.config, ['MIGRATION','OPERATORS',operator], 0)) {
        billData.status = result.status
        billData.retryCount = 0
        billData.reason = _.get(result,'reason','')
        billData.billDate = updatedBillDate
        if(billData.status==_.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)){
            billData.billDate = updatedBillDate || MOMENT().format('YYYY-MM-DD');
        }
        subsDBUpdate = true
        if (_.get(currentRecord, 'bill_date', null) != null)
            billData.lastBillDate = MOMENT(_.get(currentRecord, 'bill_date', null)).format('YYYY-MM-DD HH:mm:ss');
        if (_.get(currentRecord, 'payment_date', null) != null)
            billData.lastPaymentDate = MOMENT(_.get(currentRecord, 'payment_date', null)).format('YYYY-MM-DD HH:mm:ss');
        // } else {
        //     L.error("billSubscriber", "Operator not migrated", operator);
        // }

        
    /** if (['tata power delhi distribution limited', 'gulbarga electricity supply company limited (gescom)', 'hubli electricity supply company ltd. (hescom)', 'mangalore electricity supply company ltd. (mescom)'].indexOf(operator) >= 0) {
            billData.status = result.status
            billData.retryCount = 0
            billData.reason = ''
            billData.billDate = updatedBillDate
        }
    */

        // if operator is credit card, add row id, isCreditCardOperator flag
        if (self.commonLib.isCreditCardOperator(service)) {
            _.set(billData, 'isCreditCardOperator', true);
            _.set(billData, 'id', _.get(currentRecord, 'id', null));
            _.set(billData, 'referenceId', _.get(currentRecord, 'reference_id', null));
            _.set(billData, 'min_due_amount', _.get(data, 'customerDataResponse.currentMinBillAmount', null));
        }

        if (_.get(data, 'channel_id', null) === "digital-reminder-realtime-subs 1" || _.get(data, 'channel_id', null) === "SUBS 1" || _.get(data, ['customerInfo','channel_id'], null) === "SUBS 1"){
            _.set(billData, 'is_automatic', 1);
        }

        let oldDueDate = _.get(currentRecord, 'due_date', null)? MOMENT(_.get(currentRecord, 'due_date')).utc().startOf('day') : null;
        // L.log('billSubscriber::_dumpInSQL', 'Old Due Date: ' + oldDueDate);
        let newDueDate = billDueDate? MOMENT(billDueDate).utc().startOf('day') : null;
        // L.log('billSubscriber::_dumpInSQL', 'New Due Date: ' + newDueDate);
        newDueDate = newDueDate? newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG','RU','DEFAULT_DIFF_DAYS'], 5), 'days'):null;
        if(oldDueDate && newDueDate && newDueDate.isAfter(oldDueDate,'day')) {
            L.log('billSubscriber::_dumpInSQL', 'Due date difference is more than threshold, resetting remind later date');
            _.set(billData, 'resetRemindLaterDate', true);
        }
        //Its not a migrated operator, so saving it via old flow
        // else {
        //billData.billFetchStartTime = _.get(data, 'billFetchTime', null)
        // }
        ASYNC.waterfall(
            [
                (next) => {
                    if (self.operatorsWithRegisteredUserRecents.indexOf(operator) > -1 && billData.status == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4) ) {

                        if (!billData.customerMobile) {

                            let details = {
                                customer_id: billData.customerId
                            };

                            self.remindableUsersLibrary._getUserDetails((err, res) => {
                                if (res) {
                                    _.set(data, 'customerInfo.customer_mobile', details.customer_mobile);
                                    _.set(billData, 'customerMobile', details.customer_mobile);
                                }
                                next();
                            }, details);

                        } else {
                            next();
                        }
                    } else {
                        next();
                    }
                },
                (next) => {
                    if (subsDBUpdate) {
                        self.getCommonDueDateAmountStatus(billData);
                        if(service == 'financial services'){
                            L.log('_dumpInSQL::getCommonDueDateAmountStatus Recharge_number: ' + this.encDecpUtil.encryptData(billData.rechargeNumber) + ' commonStatus:' + billData.commonStatus + '_commonDueDate:' + this.encDecpUtil.encryptData(billData.commonDueDate) + ' amount:' + this.encDecpUtil.encryptData(billData.amount));
                        } else {
                            L.log('_dumpInSQL::getCommonDueDateAmountStatus Recharge_number: ' + billData.rechargeNumber + ' commonStatus:' + billData.commonStatus + '_commonDueDate:' + billData.commonDueDate + ' amount:' + billData.amount);
                        }
                        ASYNC.series([
                            (next) => {
                                if(billData.commonStatus == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5) && self.oldBillFetchDueDateAllowedService.indexOf(service) != -1) {
                                    self.getRecordsFromDbAndUpdateDate(() => { 
                                        next();
                                    },
                                        tableName, billData);
                                } else {
                                    return next();
                                }
                            },
                            (next) => {
                                self.bills.updateBillForSameRNandCID(() => { 
                                    next();
                                },
                                    tableName, billData);
                            },
                            (next) => {
                                
                                if(service == "financial services"){
                                    /** Skip updating for Same rech number and diff CID for CC */
                                    return next()
                                }
                                self.bills.updateBillForSameRNandDiffCID(() => { 
                                    next();
                                },
                                    tableName, billData);
                            },
                            // (next) => {
                            //     /*  only update bills column in users collection
                            //         update data in mongoDB only when status = BILL_FETCHED
                            //     */
                            //     if (billData.commonStatus == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4) && self._checkRecordForEligibilityToUpdateRecents(data)) {
                            //         billData.operator = operator;
                            //         if (!updateMongo) billData.billDueDate = null;
                            //         self.users.updateBillsInRecents(() => {
                            //             next();
                            //         }, billData,'publisher');
                            //     } else {
                            //         next();
                            //     }
                            // },
                            (next) => {
                                /**
                                 * Publish bill fetch(status=4) records to Kafka for Automatic Records...
                                 */
                                if ((billData.commonStatus == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4) ||
                                    billData.commonStatus == _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13))) {
                                    self.prepareKafkaResponse(currentRecord, billData, function (err) {
                                        next();
                                    });
                                }
                                else if (billData.commonStatus == _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5) && (_.get(currentRecord, 'notification_status', 1)) && self.oldBillFetchDueDateAllowedService.indexOf(_.toLower(_.get(currentRecord, 'service', null))) > -1 && !(self.oldBillFetchDueDateBlacklistedOperators.indexOf(_.toLower(_.get(currentRecord, 'operator', null))) > -1) && _.get(billData, 'oldBillFetchDate', null) == MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss')) {
                                    self.prepareKafkaResponseForOldBill(currentRecord, billData, function (err) {
                                        next();
                                    });
                                }
                                else {
                                    next();
                                }
                            },
                            next => {
                                /**
                                 * Publish bill fetch(status=4) records to CT for reminder events...
                                 */
                                 if (billData.commonStatus == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4) && (_.get(currentRecord, 'notification_status', 1)) ) {
                                     self.prepareCTKafkaResponse(currentRecord, billData, function (err) {
                                     next();
                                 });
                             }
                             else {
                                 next();
                             }   
                            }
                        ], function done() {
                            next();
                        });

                    } else {
                        next();
                    }
                }
            ],
            (err) => {
                // L.log("billSubscriber::_dumpInSQL Executing callback");
                cb(err, billData);
            }
        )
        } catch(error) {
            //this.logger.error('billSubscriber::_dumpInSQL Error while dumping data in SQL.', '', error);
            L.error('billSubscriber::_dumpInSQL', 'Error while dumping data in SQL.', error);
            cb(error);
        }
    }
    

    /**
     * Get table name from configuration...
     * priority : productId --> operator
     * @param {*} catalogProductID 
     * @param {*} operator 
     */
    getTableName(catalogProductID, operator) {
        let
            self = this,
            tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', catalogProductID], null) ||
                _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);

        return tableName;
    }

    /**
     * Preparing Bill Fetch Data to publish to Kafka
     */
    prepareKafkaResponse(currentRecord, billsData, cb) {
        let self = this;
        let recharge_number = _.get(currentRecord, 'recharge_number', null);
        let operator = _.get(currentRecord, 'operator', null);
        let productId = _.get(billsData, 'productId', null);
        let service = _.get(currentRecord, 'service', null);
        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);

        if (_.get(billsData, 'prepaidPublisherInstance', false) && _.get(billsData, 'tableName', null)) {
            tableName = _.get(billsData, 'tableName', tableName);
        }

        if (!tableName || !service || !operator || !recharge_number || !productId) {
            self.L.critical('prepareKafkaResponse :: invalid inputs ', tableName, service, operator, recharge_number, productId);
            return cb();
        }

        let query = `select * from ${tableName} `;

        if (_.get(billsData, 'isCreditCardOperator', false) == true) {
            query += ` where id = ${billsData.id}`;
        }
        else {
            query += ` where operator=${JSON.stringify(operator)} and service=${JSON.stringify(service)} and recharge_number=${JSON.stringify(recharge_number)}`;
        }
        let queryParams = [];
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                self.L.critical('prepareKafkaResponse :: error getting data ', tableName, recharge_number, err);
                return cb();
            } else if (!data.length) {
                self.L.critical('prepareKafkaResponse :: data not found : ', tableName, recharge_number);
                return cb();
            } else {
                data = self.encDecpUtil.parseDbResponse(data, billsData.customerId);
                self.publishToAutomaticAndBillFetchKafka(billsData, data, cb);
            }
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    publishToAutomaticAndBillFetchKafka(billsData, data, cb) {
        let self=this;
        ASYNC.parallel([
            function (parallel) {
                self.publishToAutomaticKafka(billsData, data, parallel);
            },
            function (parallel) {
                self.publishToBillFetchKafka(billsData, data, parallel);
            }
        ], function () {
            return cb();
        });
    }

    getRecordToPublishOnAutomatic(dataArray) {
        let recordToPublish = null;
        let status13Records = [];
        //publish only one record if status is 4 or 13 and is_automatic is not 0 or 5 and, give priority to status 4.
        for (let i = 0; i < dataArray.length; i++) {
            if (_.get(dataArray[i], 'is_automatic', 0) !== 0 && _.get(dataArray[i], 'is_automatic', 0) !== 5) {
                let recordStatus = _.get(dataArray[i], 'status', 0);
                if(recordStatus === 4) {
                    recordToPublish = dataArray[i];
                    break;
                } else if(recordStatus === 13) {
                    status13Records.push(dataArray[i]);
                }
                
            }
        }
        if(!recordToPublish && status13Records.length > 0) {
            recordToPublish = status13Records[0];
        }
        return recordToPublish;
    }

    publishToAutomaticKafka(billsData, dataArray, cb) {
        let self = this;
        let recordToPublish = self.getRecordToPublishOnAutomatic(dataArray);

        if (recordToPublish) {
            recordToPublish = self.commonLib.mapBillsTableColumns(recordToPublish);
            if (billsData.commonStatus == 13) {
                recordToPublish.billArchive = true;
            }
            else if (billsData.commonStatus == 4) {
                recordToPublish.billGen = true;
            }
            self.kafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''),
                messages: JSON.stringify(recordToPublish),
                key: _.get(recordToPublish, 'rechargeNumber', '')
            }], function (error) {
                if (error) {
                    self.handlePublishError(error, recordToPublish, _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''), _.get(billsData,'operator',null));
                } else {
                    self.handlePublishSuccess(recordToPublish, _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC.AUTOMATIC_SYNC_TOPIC', ''), _.get(billsData,'operator',null));
                }
                return cb();
            }, [200, 800]);
        } else {
            return cb();
        }
    }

    publishToBillFetchKafka(billsData, data, callback) {
        let self = this;
        ASYNC.eachLimit(data, 3, function (row, cb) {
            let isAutomaticCondition = _.get(row, 'is_automatic', 0) !== 0 && _.get(row, 'is_automatic', 0) !== 5;
            row = self.commonLib.mapBillsTableColumns(row);
            if (!isAutomaticCondition && row.status == 4) {
                let payload = {
                    source: "reminderBillFetch",
                    notificationType: "BILLGEN",
                    data: row
                }

                if (_.get(billsData, 'prepaidPublisherInstance', null)) {
                    payload.notificationType = "PREPAID_LOW_BALANCE";
                }

                let toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen();
                if (!toBeNotifiedRealtime) {
                    payload.source = 'reminderBillFetch';
                    var topic = _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', '');
                    var publisher = self.kafkaBillFetchPublisher;
                } else {
                    payload.source = 'BillGenPublisherRealTime';
                    var topic = _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', '');
                    var publisher = self.billFetchKafkaRealtime;
                }

                utility.sendNotificationMetricsFromSource(payload);
                publisher.publishData([{
                    topic: topic,
                    messages: JSON.stringify(payload)
                }], function (error) {
                    if (error) {
                        self.handlePublishError(error, payload, topic, _.get(billsData,'operator',null));
                    } else {
                        self.handlePublishSuccess(payload, topic, _.get(billsData,'operator',null));
                    }
                    return cb();
                }, [200, 800]);
            } else {
                return cb();
            }
        }, function () {
            return callback();
        });
    }

    handlePublishError(error, payload, topic, operator) {
        let self=this;
        utility.sendNotificationMetricsFromSource(payload, "ERROR");
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
            'STATUS:ERROR',
            'TYPE:ERROR_WHILE_PUBLISHING',
            'TOPIC:' + topic,
            'OPERATOR:' + operator,
            `SOURCE:PUBLISHER`
        ]);
        self.logger.critical(`prepareKafkaResponse :: kafkaBillFetchPublisher Error while publishing message in Kafka  ${error} - MSG:- `, payload, _.get(payload, 'data.service', ''));
    }

    handlePublishSuccess(payload, topic, operator) {
        let self=this;
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:BILLGEN_NOTIFICATION',
            'STATUS:SUCCESS',
            'TYPE:PUBLISHED',
            'TOPIC:' + topic,
            'OPERATOR:' + operator,
            `SOURCE:PUBLISHER`
        ]);
        let loggerPayload = _.cloneDeep(payload);
        if(_.get(payload, 'data.service', '') == 'financial services') {
            _.set(loggerPayload, 'data.enc_amount', this.encDecpUtil.encryptData(payload.data.enc_amount));
            _.set(loggerPayload, 'data.enc_due_date', this.encDecpUtil.encryptData(payload.data.enc_due_date));
        }
        self.logger.log(`prepareKafkaResponse :: kafkaBillFetchPublisher Message published successfully in Kafka on topic ${topic}`, loggerPayload, _.get(payload, 'data.service', ''));
    }

    /**
         * Preparing Bill Fetch Data to publish to Kafka
         */
    prepareKafkaResponseForOldBill(currentRecord, billsData, cb) {
        let self = this;
        let recharge_number = _.get(currentRecord, 'recharge_number', null);
        let operator = _.get(currentRecord, 'operator', null);
        let productId = _.get(billsData, 'productId', null);
        let service = _.get(currentRecord, 'service', null);
        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);

        if (!tableName || !service || !operator || !recharge_number || !productId) {
            self.L.critical('prepareKafkaResponseForOldBill :: invalid inputs ', tableName, service, operator, recharge_number, productId);
            return cb();
        }

        let query = `select * from ${tableName} `;
        query += ` where operator=${JSON.stringify(operator)} and service=${JSON.stringify(service)} and recharge_number=${JSON.stringify(recharge_number)}`;
        let queryParams = [];
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                self.L.critical('prepareKafkaResponseForOldBill :: error getting data ', tableName, recharge_number, err);
                return cb();
            } else if (!data.length) {
                self.L.critical('prepareKafkaResponseForOldBill :: data not found : ', tableName, recharge_number);
                return cb();
            } else {
                ASYNC.eachLimit(data, 3, function (row, cb) {
                    row = self.commonLib.mapBillsTableColumns(row);
                    let amount = _.get(billsData, 'commonAmount', null);

                    if (amount <= 0) {
                        return cb();
                    }
                    else if (billsData.commonStatus == 13) {
                        row.billArchive = true;
                    } else if (billsData.commonStatus == 5) {
                        row.oldBillFound = true;
                    }
                    if (billsData.commonStatus == 5) {
                        let payload = {
                            source: "reminderBillFetch",
                            notificationType: "OLD_BILL_NOTIFICATION",
                            data: row
                        }
                        let toBeNotifiedRealtime = self.commonLib.decideTopicToPublishBillGen();
                        if (!toBeNotifiedRealtime) {
                            utility.sendNotificationMetricsFromSource(payload)
                            self.kafkaBillFetchPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                                messages: JSON.stringify(payload)
                            }], function (error) {
                                if (error) {
                                    utility.sendNotificationMetricsFromSource(payload, "ERROR")
                                    utility._sendMetricsToDD(1, [
                                        'REQUEST_TYPE:OLD_BILL_NOTIFICATION',
                                        'STATUS:ERROR',
                                        'TYPE:ERROR_WHILE_PUBLISHING',
                                        'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                                        'OPERATOR:' + operator,
                                        `SOURCE:PUBLISHER`
                                    ]);
                                    self.logger.critical(`prepareKafkaResponseForOldBill :: kafkaBillFetchPublisher Error while publishing message in Kafka ${error} - MSG:- `, payload, service);
                                } else {
                                    utility._sendMetricsToDD(1, [
                                        'REQUEST_TYPE:OLD_BILL_NOTIFICATION',
                                        'STATUS:SUCCESS',
                                        'TYPE:PUBLISHED',
                                        'TOPIC:' + _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
                                        'OPERATOR:' + operator,
                                        `SOURCE:PUBLISHER`
                                    ])
                                    self.logger.log('prepareKafkaResponseForOldBill :: kafkaBillFetchPublisher Message published successfully in Kafka on topic REMINDER_BILL_FETCH', payload, service);
                                }
                                return cb();
                            }, [200, 800]);
                        }
                        else {
                            payload.source = 'BillGenPublisherRealTime';
                            utility.sendNotificationMetricsFromSource(payload)
                            self.billFetchKafkaRealtime.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                                messages: JSON.stringify(payload)
                            }], function (error) {
                                if (error) {
                                    utility.sendNotificationMetricsFromSource(payload, "ERROR")
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER_OLD_BILL_NOTIFICATION", 'STATUS:ERROR', 'TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + payload.operator]);
                                    self.logger.critical(`prepareKafkaResponseForOldBill :: kafkaBillFetchPublisher Error while publishing message in Kafka ${error} - MSG:-`, payload, service);
                                } else {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER_OLD_BILL_NOTIFICATION", 'STATUS:PUBLISHED', 'TYPE:REMINDER_BILL_FETCH', "OPERATOR:" + payload.operator]);
                                    self.logger.log('prepareKafkaResponseForOldBill :: kafkaBillFetchPublisher Message published successfully in Kafka on topic REMINDER_BILL_FETCH_REALTIME', payload, service);
                                }
                                return cb();
                            }, [200, 800]);
                        }
                    } else {
                        return cb();
                    }
                }, function () {
                    return cb();
                });
            }
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    /**
     * Preparing Bill Fetch Data to publish to Kafka
     */
     prepareCTKafkaResponse(currentRecord, billsData, cb) {
        let self = this;
        let recharge_number = _.get(currentRecord, 'recharge_number', null);
        let operator = _.get(currentRecord, 'operator', null);
        let productId = _.get(billsData, 'productId', null);
        productId =  self.activePidLib.getActivePID(productId);
        let service = _.get(currentRecord, 'service', null);
        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
        let eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen');
        let billFetchStatus = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4);

         if (_.get(billsData, 'prepaidPublisherInstance', false) && _.get(billsData, 'tableName', null)) {
             tableName = _.get(billsData, 'tableName', tableName);
         }

        if (!tableName || !service || !operator || !recharge_number || !productId) {
            self.L.critical('prepareCTKafkaResponse :: invalid inputs ', tableName, service, operator, recharge_number, productId);
            return cb();
        }
        if(self.commonLib.isCTEventBlocked(eventName)){
            self.L.info(`Blocking CT event ${eventName}`)
            return cb()
        }

        let query = `select * from ${tableName} `;

        if (_.get(billsData, 'isCreditCardOperator', false) == true) {
            query += ` where id = ${billsData.id}`;
        }
        else {
            query += ` where operator=${JSON.stringify(operator)} and service=${JSON.stringify(service)} and recharge_number=${JSON.stringify(recharge_number)} and status=${billFetchStatus}`;
        }
        let queryParams = [];
        self.dbInstance.exec(function (err, data) {
            if (err || !(data)) {
                self.L.critical('prepareCTKafkaResponse :: error getting data ', tableName, recharge_number, err);
                return cb();
            } else if (!data.length) {
                self.L.log('prepareCTKafkaResponse :: data with bill fetched status not found : ', tableName, recharge_number);
                return cb();
            } else {
                data = self.encDecpUtil.parseDbResponse(data, billsData.customerId);
                ASYNC.eachLimit(data, 3, function (dataRow, cb) {
                    let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
                    ASYNC.waterfall([
                        next => {
                            self.commonLib.getRetailerData((error) => {
                                if(error) {
                                    return next(error)
                                } else {
                                    return next(null)
                                }
                            }, dataRow.customer_id, dataRow);
                        },
                        next => {
                            self.commonLib.getCvrData((error) => {
                                if(error) {
                                    return next(error)
                                } else {
                                    return next(null)
                                }
                            }, productId, dataRow);
                        },
                        next => {                  
                            let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                            let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                            self.ctKafkaPublisher.publishData([{
                                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                                messages: JSON.stringify(mappedData)
                            }], (error) => {
                                if (error) {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                                    self.logger.log(`billSubscriber::prepareCTKafkaResponse Error while publishing message in Kafka ${error} - MSG:-`, clonedData, service);
                                } else {
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator,`EVENT_NAME:${eventName}`]);
                                    self.logger.log('billSubscriber::prepareCTKafkaResponse Message published successfully in Kafka on topic REMINDER_CT_EVENTS', clonedData, service);
                                }
                                return next(error);
                            }, [200, 800]);
                        }
                    ], (err) => {
                        return cb(err)
                    })
        
                }, function () {
                    return cb();
                });
            }
        }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
    }

    _getCustomBillFetchDateAsPerOperator(serviceId, operator, billDueDate, rechargeNumber) {
        var self = this;
        if (_.get(self.config, 'COMMON.CUSTOM_BILL_FETCH_SERVICE_ID', []).indexOf(serviceId) > -1) {
            switch (_.get(self.config, ["CUSTOM_BILL_FETCH_DATE", operator], "")) {
                case "DAILY": {
                    return MOMENT().add(1, "days");
                }
                case "EACH_DAY_AFTER_DUE_DATE": {
                    if (billDueDate && billDueDate.diff(MOMENT()) > -1) {
                        return MOMENT(billDueDate).add(1, "days");
                    } else {
                        return MOMENT().add(1, "days");
                    }

                }
            }
        }
        //L.info('_getCustomBillFetchDateAsPerOperator:: ', 'not configured for serviceId::', serviceId, ' operator::', operator, ' rechargeNumber::', rechargeNumber);
        return null;
    }

    _getConstantNextBillFetchDate(gatewayNbfd, operator) {
        var self = this;
        if (gatewayNbfd) {
            return gatewayNbfd;
        } else if (_.get(self.operatorConfig, [operator, 'CONSTANT_NBFD'], null)) {
            let date = _.get(self.operatorConfig, [operator, 'CONSTANT_NBFD']);
            return (MOMENT() >= MOMENT().date(date).startOf('day')) ? MOMENT().add(1, 'months').date(date).startOf('day') : MOMENT().date(date).startOf('day');
        }
        return null
    }


    _sanityChecks(tableName, operator, rechargeNumber, productId) {
        let self = this;

        // if(_.get(self.config, ['MIGRATION','OPERATORS',operator], 0) && !tableName){
        if (!tableName) {
            //check if we have a dedicated table for those operators which are there in the migration config
            L.error('_sanityChecks:: ', 'No table is configured for the message, rechargeNumber: ', rechargeNumber, ', productId: ', productId, ', operator:', operator);
            return false;
        }

        if (!(rechargeNumber && productId)) {
            L.error('_sanityChecks:: ', 'Either rechargeNumber or ProductId is not there for the message', rechargeNumber, productId, operator);
            return false; //if we don't have any of the above things, then simply return from here
        }
        return true
    }

    _shouldUpdateBill(lastAmount, lastDueDate, validationAmount, validationDueDate, extra, operator) {
        let dueDateDiff = null;

        if (validationDueDate != null && lastDueDate != null && validationDueDate.isValid() && lastDueDate.isValid()) {
            dueDateDiff = validationDueDate.startOf('day').diff(lastDueDate.utc().startOf('day'), 'day');
        }
        let amountDiff = Math.abs(validationAmount - lastAmount);

        if(extra != null && _.get(extra,'billSource',null) == 'UPMS' && (dueDateDiff===0 && !amountDiff)){
            L.info('_dumpInSQL:: ', 'validation Amount and DueDate == UPMS Amount and and DueDate in DB, So skipping billSource update.')
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:PUBLISHER", 'STATUS:BILL_SOURCE_UPDATE_SKIP', 'TYPE:DB_RECORD_SAME_AMOUNT_AND_DUEDATE_UPMS', "OPERATOR:" + operator]);
            return false;
        }
        return true
    }

    _getNBFDAndStatus({ service, operator, amount, customNextBillFetchDate, evaluatedNbfd, billDate, billDueDate, lastDueDate, lastBillDate, isConnectionError, paymentDone, deducedStatus, noBill, isDateFmtValid, defaultNextBillDueDate, errorMessageCode, tableName, lastAmount, oldBillAmount, productId, oldProductId, serviceId, billDateBasedGateways, custInfoValues, paymentDate, gateway, extraDetails, errorMessageCodeStr, currentAutomaticStatus, nbfdStartOfDay }) {
        let self = this,
        nextBillFetchDate = null,   //default value for next bill fetch date
            status = null,
            reason = '',
            dateToBeUsed = billDate || billDueDate,
            nextBillFetchDateForRetry = self.getNextBillFetchDateForRetry(operator, lastBillDate, lastDueDate, productId, oldProductId, 3, billDateBasedGateways, currentAutomaticStatus),
            nextRetryFreq = self.getNextRetryFreq(operator, productId, oldProductId, 1, currentAutomaticStatus);

        let NBFD_DAYS_TO_ADD_CC = self.billsLib.getConfigByKeys({
            dynamicConfig:false,
            name:'SUBSCRIBER_CONFIG',
            node:'NEXT_BILL_FETCH_DATES',
            keyname:operator,
            default:30
        }, {
            currentAutomaticStatus: currentAutomaticStatus,
            prefix:'AUTOPAY_',
            prefixToKey: 'node'
        })

        if (isConnectionError) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:CONNECTION_ERROR`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            nextBillFetchDate = MOMENT().add(nextRetryFreq, 'days');
            status = _.get(self.config, 'COMMON.bills_status.CONNECTION_ERROR', 8)
        }
        //This following code is commentend as it may be used in future, currently this paymentDone flag is a non-reliable field.
        // else if(paymentDone){
        //     let billDelay = _.get(self.config, ['RECENT_BILL_CONFIG', 'OPERATORS', operator, 'firstBillDelay'], 5),
        //         billDelayTimeUnit = _.get(self.config, ['RECENT_BILL_CONFIG', 'OPERATORS', operator, 'BILL_DELAY_TIME_UNIT'], 'days');
        //     nextBillFetchDate =  billDelay < 0 ?  MOMENT().add(Math.abs(Number(billDelay)), 'months'): MOMENT().add(billDelay, billDelayTimeUnit);
        //     status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14);
        //     amount = 0;
        // }
        else if((errorMessageCode == 1030) && tableName == 'bills_creditcard') {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:BFR003`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            let noOfDays =_.get(self.config, ['DYNAMIC_CONFIG','CC_PUBLISHER_CONFIG','NBFD','days'],NBFD_DAYS_TO_ADD_CC);
            if(billDate){
                nextBillFetchDate = MOMENT(billDate).add(noOfDays, 'days');
            } else {
                noOfDays = _.get(self.config, ['DYNAMIC_CONFIG', 'CC_PUBLISHER_CONFIG', 'NBFD_BASED_ON_ERRORMSG', 'days'], 7);
                nextBillFetchDate = MOMENT().add(noOfDays, 'days');
            }
            status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
        }
        else if(self.isDisableBillFetchValid(service, operator, errorMessageCodeStr, extraDetails) && currentAutomaticStatus != 1) { // if CA number is invalid setting status 13 to Disable bill_fetch
            nextBillFetchDate = MOMENT('2080-01-01', 'YYYY-MM-DD'); // Just setting NBFD to future value to avoid status over riding and tracking purpose
            status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13);
            reason = _.get(self.config, ['SUBSCRIBER_CONFIG', 'REASON_INVALID_CA'], '');
        }
        else if (noBill || (!_.isNaN(errorMessageCode) && _.get(self.config, ['SUBSCRIBER_CONFIG', 'NO_BILL_ERROR_MESSAGE_CODES'], []).indexOf(errorMessageCode) >= 0)) { //if noBill is true, that means new bill is still not generated for the user
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NO_BILL`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            let dateToBeUsedForNoBill = billDateBasedGateways.indexOf(operator) > -1 ? lastBillDate : lastDueDate;
            status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6);
            if(dateToBeUsedForNoBill){
                //This is the case where operators nbfd is 30 days, so we are adding a month instead of 30days to maintain the correct bill cycle
                dateToBeUsedForNoBill = self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? MOMENT(dateToBeUsedForNoBill).add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : MOMENT(dateToBeUsedForNoBill).add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
            }
            else {
                dateToBeUsedForNoBill=null;
            }
            //dateToBeUsedForNoBill = dateToBeUsedForNoBill ? MOMENT(dateToBeUsedForNoBill).add(self.getFirstBillFetchInterval(operator), 'days') : null;

            let rescheduleTime = self.eligibleForReschedule('NO_BILL', operator);

            if (dateToBeUsedForNoBill && dateToBeUsedForNoBill.diff(MOMENT(), 'days') > 0) {
                nextBillFetchDate = dateToBeUsedForNoBill;
            }
            else if (rescheduleTime) {
                nextBillFetchDate = rescheduleTime;
                nbfdStartOfDay = false;
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILL_RESCHEDULE', `OPERATOR:${operator}`]);
            } else {
                nextBillFetchDate = nextBillFetchDateForRetry;
            }
        }
        else if (_.get(custInfoValues, 'invalid', 0) == 1) {  // Record is invalid, so marking it as not in use. It will be archived soon.
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NOT_IN_USE`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            nextBillFetchDate = MOMENT('2080-01-01', 'YYYY-MM-DD'); // Just setting NBFD to future value to avoid status over riding and tracking purpose
            status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13)
        }
        else if(!_.get(self.config, ['DYNAMIC_CONFIG','MULTIPLE_PID_OPERATOR', operator,'MULTIPLE_PIDS'], null) && gateway && _.get(self.config, ['DYNAMIC_CONFIG', 'GATEWAY_ERROR_MAPPING', gateway, 'INVALID_CA_ERROR_CODE'], null) != null && _.get(self.config, ['DYNAMIC_CONFIG', 'GATEWAY_ERROR_MAPPING', gateway, 'INVALID_CA_ERROR_CODE'], null) == errorMessageCode) { // if CA number is invalid setting status 13 to Disable bill_fetch
            L.info('_calcNextBillFetchDate:: ', 'CA number is invalid, disabling bill_fetch for operator::', operator, ' gateway::', gateway);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NOT_IN_USE`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`,`REASON:INVALID_CA`]);
            nextBillFetchDate = MOMENT('2080-01-01', 'YYYY-MM-DD'); // Just setting NBFD to future value to avoid status over riding and tracking purpose
            status = _.get(self.config, 'COMMON.bills_status.NOT_IN_USE', 13);
            reason = _.get(self.config, ['SUBSCRIBER_CONFIG', 'REASON_INVALID_CA'], '');
        }
        else if (!deducedStatus) { //if deducedStatus is false       
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:VALIDATION_FAILED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            nextBillFetchDate = nextBillFetchDateForRetry;
            status = _.get(self.config, 'COMMON.bills_status.VALIDATION_FAILED', 9)
        }
        else if (amount <= 0 && dateToBeUsed == null) { // Getting deduces status = true but not getting bill
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:VALIDATION_FAILED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILLDATE_DUEDATE', `OPERATOR:${operator}`]);
            nextBillFetchDate = nextBillFetchDateForRetry;
            status = _.get(self.config, 'COMMON.bills_status.VALIDATION_FAILED', 9)
        }
        else if(self.commonLib.isCreditCardOperator(service) && paymentDate && billDate && MOMENT(paymentDate) > MOMENT(billDate)){
            status = _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)
            nextBillFetchDate = self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : MOMENT(dateToBeUsed).add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
        }
        // In case billDate, dueDate is invalid,  nbfd= now() + NEXT_BILL_FETCH_DATES
        else if (_.get(self.operatorConfig, [operator, 'INVALID_BILLDATE_DUEDATE'], false)) {
            if (amount > 0) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:BILL_FETCHED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
                //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
                nextBillFetchDate = self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? MOMENT().add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : MOMENT().add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
                status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
            } else {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NO_BILL`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
                let rescheduleTime = self.eligibleForReschedule('NO_BILL', operator);
                if (rescheduleTime) {
                    nextBillFetchDate = rescheduleTime;
                    nbfdStartOfDay = false;
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILL_RESCHEDULE', `OPERATOR:${operator}`]);
                } else {
                    nextBillFetchDate = nextBillFetchDateForRetry;
                }
                status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)
            }
        }
        else if (!isDateFmtValid) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:WRONG_DUE_DATE`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            nextBillFetchDate = nextBillFetchDateForRetry;
            status = _.get(self.config, 'COMMON.bills_status.WRONG_DUE_DATE', 10)
        }
        //If bill fetch is successful & billDueDate is greater than 1 month then schedule nextBillFetchDate to new dueDate minus 1 month (default case)
        else if (self.billsLib.isEarlyBillFetch({billDueDate : billDueDate, lastDueDate : lastDueDate })) { 
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:EARLY_BILL_FETCH`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            let earlyBillFetchResult = self.billsLib.getEarlyBillFetchResultParams({
                billDueDate : billDueDate,
                currentAutomaticStatus : currentAutomaticStatus,
            });
            nextBillFetchDate = earlyBillFetchResult.nextBillFetchDate;
            status =  earlyBillFetchResult.status;
        }
        //First preference given to custom next bill fetch date
        else if (customNextBillFetchDate) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:BILL_FETCHED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            nextBillFetchDate = customNextBillFetchDate;
            status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
        }
        //If amount is negative OR due date is old then set next Bill fetch date according to NEXT_RETRY_FREQUENCY or 3 days (OLD BILL)
        //IF (negativeAmount & dueDate is Null -> payment done by user)
        //OR (if billDueDate is present & old billDueDate -> )
        //OR (if billDate is applicable AND if lastBillDate is same as billDate )
        else if (
            (billDueDate && billDueDate.diff(MOMENT(), 'days') < 0) ||
            (lastBillDate && billDate && lastBillDate.diff(billDate) == 0
                && oldBillAmount != null && amount != null && oldBillAmount == amount)) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:OLD_BILL`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            let rescheduleTime = self.eligibleForReschedule('OLD_BILL_FOUND', operator);
            if (dateToBeUsed) {
                //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
                nextBillFetchDate = self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : MOMENT(dateToBeUsed).add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
            }
            if (nextBillFetchDate && nextBillFetchDate.diff(MOMENT(), 'days') <= 0) {
                if (rescheduleTime) {
                    nextBillFetchDate = rescheduleTime;
                    nbfdStartOfDay = false;
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:OLD_BILL_FOUND_RESCHEDULE', `OPERATOR:${operator}`]);
                } else if (self.allowedServicesForCustomNBFDForOldBill.includes(service)) {
                    nextBillFetchDate = MOMENT().startOf('day').add(self.getNextBillFetchDateForOldBill(operator, amount, oldBillAmount, currentAutomaticStatus), 'days');
                }
                else {
                    nextBillFetchDate = nextBillFetchDateForRetry;
                }
            }
            status = _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)
        }
        // if nbfd from gateway or constant then after checking old bill condition.
        else if (evaluatedNbfd) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:BILL_FETCHED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            nextBillFetchDate = evaluatedNbfd;
            status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
        }
        //for < 0 billfetch interval, ignoring add months for dth to make sync with validation flow.

        //First priorty to be given to billDate and second to billDueDate.
        else if (dateToBeUsed) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:BILL_FETCHED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
            nextBillFetchDate = (self.getFirstBillFetchInterval(operator,currentAutomaticStatus) < 0 && service !='dth') || (service =='dth' && !(_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []).indexOf(_.toLower(operator))>-1)) ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.getFirstBillFetchInterval(operator,currentAutomaticStatus))), 'months') : MOMENT(dateToBeUsed).add(self.getFirstBillFetchInterval(operator,currentAutomaticStatus), 'days');
            status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
        }
        // If due date is not populated by operator the setting automatic 7 days later as Due date.
        else if (amount > 0 && billDueDate === null && lastDueDate && lastDueDate.diff(MOMENT()) < -1) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:BILL_FETCHED`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            //If amount > 0, billDueDate is not found and pastDueDate has expired => dueDate = NOW + 7 (nextBillDueDate)
            //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
            nextBillFetchDate = self.getFirstBillFetchInterval(operator,currentAutomaticStatus) < 0 ? MOMENT(defaultNextBillDueDate).add(Math.abs(Number(self.getFirstBillFetchInterval(operator,currentAutomaticStatus))), 'months') :  MOMENT(defaultNextBillDueDate).add(self.getFirstBillFetchInterval(operator,currentAutomaticStatus), 'days');
            status = _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)
        }
        else {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NO_BILL`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
            let rescheduleTime = self.eligibleForReschedule('NO_BILL', operator);
            if (rescheduleTime) {
                nextBillFetchDate = rescheduleTime;
                nbfdStartOfDay = false;
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:NO_BILL_RESCHEDULE', `OPERATOR:${operator}`]);
            }
            else{
                nextBillFetchDate = nextBillFetchDateForRetry;
            }
            status = _.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)
        }
        return [status, nextBillFetchDate, nbfdStartOfDay, reason]
    }

    applyRescheduleForStatus(status, nextBillFetchDate, operator, nbfdStartOfDay){
        let self = this;
        //rescheduling based on operator config for each error code wherever applicable
        if(!(status==_.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4) ||
        (status==_.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) || 
        (status==_.get(self.config, 'COMMON.bills_status.BILL_NOT_FOUND', 6)))){
            let billStatus,
            statusMap=_.get(self.config, 'COMMON.bills_status', null);
            for(const property in statusMap) {
                if(statusMap[property]==status){
                    billStatus=property;
                }
            }
            if(billStatus){
                let rescheduleTime = self.eligibleForReschedule(billStatus, operator);
                if (rescheduleTime) {
                    nextBillFetchDate = rescheduleTime;
                    nbfdStartOfDay = false;
                    utility._sendMetricsToDD(1, [`REQUEST_TYPE:${billStatus}_RESCHEDULE`, `OPERATOR:${operator}`]);
                }
            }
        }
        return [nextBillFetchDate, nbfdStartOfDay];
    }

    handlePastNBFD(nextBillFetchDate, status, operator, nextBillFetchDateForRetry, currentAutomaticStatus, service, dueDate) {
        let self = this;
        if (nextBillFetchDate.diff(MOMENT(), 'days') < 0) {
            let currentDate = MOMENT().format('YYYY-MM-DD');

            if (status != _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER', `STATUS:OLD_BILL`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`]);
                nextBillFetchDate = nextBillFetchDateForRetry;
                status = _.get(self.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)
            }
            else if (status == _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4)) {
                let nextBillFetchDateBasedonCurrDay;
                if ((self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 && service !== 'dth') ||
                    (service === 'dth' && !(_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []).indexOf(_.toLower(operator)) > -1))) {
                    nextBillFetchDateBasedonCurrDay = MOMENT(currentDate).add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months');
                } else {
                    nextBillFetchDateBasedonCurrDay = MOMENT(currentDate).add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
                }
                nextBillFetchDate = nextBillFetchDateBasedonCurrDay.isBefore(MOMENT(dueDate).add(1, 'days'))
                    ? nextBillFetchDateBasedonCurrDay
                    : MOMENT(dueDate).add(1, 'days');
            }
        }
        return [status, nextBillFetchDate]
    }

    handleCCBPastNBFD(service, operator, amount, billDate, billDueDate, nextBillFetchDate, status, currentAutomaticStatus){
        let self = this;
        let NBFD_DAYS_TO_ADD_CC = self.billsLib.getConfigByKeys({
            dynamicConfig:false,
            name:'SUBSCRIBER_CONFIG',
            node:'NEXT_BILL_FETCH_DATES',
            keyname:operator,
            default:30
        }, {
            currentAutomaticStatus: currentAutomaticStatus,
            prefix:'AUTOPAY_',
            prefixToKey: 'node'
        })

        if(service == "financial services"){
            let daysToBeAddedWhenNBFDIsLessThanCurrentDay = self.billsLib.getConfigByKeys({
                dynamicConfig:false,
                name:'SUBSCRIBER_CONFIG',
                node:'NEXT_BILL_FETCH_DATES',
                keyname:'NBFD_LESS_THAN_CURRENT_DAY',
                default: 7
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey:'node'
            })
            if(!billDate && billDueDate) {
                let daysToBeAddedWhenBillDateIsNull = self.billsLib.getConfigByKeys({
                    dynamicConfig:false,
                    name:'SUBSCRIBER_CONFIG',
                    node:'NEXT_BILL_FETCH_DATES',
                    keyname:'BILL_DATE_MISSING',
                    default: 8
                },{
                    currentAutomaticStatus:currentAutomaticStatus,
                    prefix: 'AUTOPAY_',
                    prefixToKey:'node'
                })
                nextBillFetchDate = MOMENT(billDueDate).add(daysToBeAddedWhenBillDateIsNull, 'days');

                if(nextBillFetchDate.diff(MOMENT().startOf('day')) < 0) {
                    nextBillFetchDate = MOMENT().add(daysToBeAddedWhenNBFDIsLessThanCurrentDay, 'days');
                    self.L.log("_calcNextBillFetchDate :: The nextBillFetchDate when assumednbfd is before the current day when billDate is null ", nextBillFetchDate);
                }
                self.L.log("_calcNextBillFetchDate :: The nextBillFetchDate when billDate is Null is ", nextBillFetchDate);
            } else if (billDate) {
                let assumednbfd = MOMENT(billDate).add(NBFD_DAYS_TO_ADD_CC, 'days');

                if(assumednbfd.diff(MOMENT().startOf('day')) <= 0) {
                    nextBillFetchDate = MOMENT().add(daysToBeAddedWhenNBFDIsLessThanCurrentDay, 'days');
                    self.L.log("_calcNextBillFetchDate :: The nextBillFetchDate when assumednbfd is before the current day ", nextBillFetchDate);
                }
            }
        }
        return nextBillFetchDate;
    }



    /*
       This function decides the next bill fetch date for the record
    */
    /*
        dateToBeUsed: billDate || dueDate || nextBillDueDate
        nextBillFetchDate : based on dateToBeUsed
     */
    _calcNextBillFetchDate({ service, operator, amount, customNextBillFetchDate, evaluatedNbfd, billDate, billDueDate, lastDueDate, lastBillDate, isConnectionError, paymentDone, deducedStatus, noBill, isDateFmtValid, defaultNextBillDueDate, errorMessageCode, tableName, lastAmount, oldBillAmount, productId, oldProductId, serviceId, billDateBasedGateways, custInfoValues, paymentDate, gateway, extraDetails, errorMessageCodeStr, currentAutomaticStatus, currentRecord}) {
        let self = this,
        nbfdStartOfDay = true,
        status = null, nextBillFetchDate = null,
        nextBillFetchDateForRetry = self.getNextBillFetchDateForRetry(operator, lastBillDate, lastDueDate, productId, oldProductId, 3, billDateBasedGateways, currentAutomaticStatus),
        reason = '';


            [status, nextBillFetchDate, nbfdStartOfDay, reason] = self._getNBFDAndStatus({
                service: service,
                operator: operator,
                amount: amount,
                customNextBillFetchDate: customNextBillFetchDate,
                evaluatedNbfd: evaluatedNbfd,
                billDate: billDate,
                billDueDate: billDueDate,
                lastDueDate: lastDueDate,
                lastBillDate: lastBillDate,
                isConnectionError: isConnectionError,
                paymentDone: paymentDone,
                deducedStatus: deducedStatus,
                noBill: noBill,
                isDateFmtValid: isDateFmtValid,
                defaultNextBillDueDate: defaultNextBillDueDate,
                errorMessageCode: errorMessageCode,
                tableName: tableName,
                lastAmount: lastAmount,
                oldBillAmount: oldBillAmount,
                productId: productId,
                oldProductId: oldProductId,
                serviceId: serviceId,
                billDateBasedGateways: billDateBasedGateways,
                custInfoValues: custInfoValues,
                paymentDate: paymentDate,
                gateway: gateway,
                extraDetails: extraDetails,
                errorMessageCodeStr: errorMessageCodeStr,
                currentAutomaticStatus: currentAutomaticStatus,
                nbfdStartOfDay: nbfdStartOfDay,
            });

        [nextBillFetchDate, nbfdStartOfDay] = self.applyRescheduleForStatus(status, nextBillFetchDate, operator, nbfdStartOfDay);

        [status, nextBillFetchDate] = self.handlePastNBFD(nextBillFetchDate, status, operator, nextBillFetchDateForRetry,currentAutomaticStatus, service, billDueDate);

        let operatorCount = _.get(operatorCountMapping, tableName, -1);
        operatorCountMapping[tableName] = (operatorCount + 1) % self.timeDiff;
        //setting the exact time for PRIMARY_CYCLE by deviding time in batches

        try{
            nextBillFetchDate = self.handleCCBPastNBFD(service, operator, amount, billDate, billDueDate, nextBillFetchDate, status, currentAutomaticStatus);
        }catch(e){
            self.L.error('_calcNextBillFetchDate:: ', 'Error in handling CCBP past NBFD, Error: ', e);
        }


        return {
            nextBillFetchDate: nbfdStartOfDay ? nextBillFetchDate.startOf('day') : nextBillFetchDate,
            status: status,
            amount: amount,
            reason: reason
        }
    }

    eligibleForReschedule(billStatus, operator) {
        let
            self = this,
            slotFrom = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, `${billStatus}_SLOT_FROM`], null),
            slotTo = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, `${billStatus}_SLOT_TO`], null),
            rescheduleTime = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', operator, `${billStatus}_RESCHEDULE_TIME`], null);

        if (!(slotFrom && slotTo && rescheduleTime)) return null;

        let
            slotFromArr = slotFrom.split(':'),
            slotToArr = slotTo.split(':'),
            rescheduleTimeArr = rescheduleTime.split(':');

        if (!(slotFromArr.length > 0 && slotToArr.length > 0 && rescheduleTimeArr.length > 0)) return null;

        slotFrom = MOMENT().set({ hour: _.get(slotFromArr, '0', '00'), minute: _.get(slotFromArr, '1', '00'), second: _.get(slotFromArr, '2', '00') });
        slotTo = MOMENT().set({ hour: _.get(slotToArr, '0', '00'), minute: _.get(slotToArr, '1', '00'), second: _.get(slotToArr, '2', '00') });

        if (MOMENT().isBetween(slotFrom, slotTo)) {
            return MOMENT().set({ hour: _.get(rescheduleTimeArr, '0', '00'), minute: _.get(rescheduleTimeArr, '1', '00'), second: _.get(rescheduleTimeArr, '2', '00') });
        } else {
            return null;
        }
    }

    getFirstBillFetchInterval(operator, currentAutomaticStatus=null) {

        let
            self = this;

        let    nextBillFetchDates = self.billsLib.getConfigByKeys({
                    dynamicConfig:true,
                    name:'OPERATOR_CONFIG',
                    node: operator,
                    keyname: 'NEXT_BILL_FETCH_DATES'
                },{
                    currentAutomaticStatus: currentAutomaticStatus,
                    prefix : 'AUTOPAY_',
                    prefixToKey: 'keyname'
                })

                || self.billsLib.getConfigByKeys({
                    dynamicConfig:false,
                    name:'SUBSCRIBER_CONFIG',
                    node: 'NEXT_BILL_FETCH_DATES',
                    keyname: operator
                },{
                    currentAutomaticStatus: currentAutomaticStatus,
                    prefix : 'AUTOPAY_',
                    prefixToKey: 'node'
                })

        self.L.log("getFirstBillFetchInterval:: ", "currentAutomaticStatus: ", currentAutomaticStatus, "nextBillFetchDates: ", nextBillFetchDates);
        if (_.isArray(nextBillFetchDates))
            return nextBillFetchDates[0];
        else if (nextBillFetchDates)
            return nextBillFetchDates;
        else
            return 20; // default value
    }

    getNextBillFetchDateForOldBill(operator, amount, oldBillAmount, currentAutomaticStatus=null) {
        let self = this, nextRetryFreq;
        if (amount > 0) {
            nextRetryFreq = self.billsLib.getConfigByKeys({
                dynamicConfig:true,
                name: 'OPERATOR_CONFIG',
                node: operator,
                keyname: 'NBFD_FOR_OLD_BILL_AMOUNT_GREATER_THAN_0',
                default: 5
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'keyname'
            })
        }
        else {
            nextRetryFreq = self.billsLib.getConfigByKeys({
                dynamicConfig:true,
                name: 'OPERATOR_CONFIG',
                node: operator,
                keyname: 'NBFD_FOR_OLD_BILL_AMOUNT_LESS_THAN_EQUAL_TO_0',
                default: 5
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'keyname'
            })
        }

        self.L.log("getNextBillFetchDateForOldBill:: ", "currentAutomaticStatus: ", currentAutomaticStatus, "nextRetryFreq: ", nextRetryFreq);
        return nextRetryFreq;
    }

    getNextBillFetchDateForRetry(operator, lastBillDate, lastDueDate, productId, oldProductId, defaultFreq, billDateBasedGateways, currentAutomaticStatus=null) {
        let
            self = this,
            nextBillFetchDate,
            dateToBeUsed = billDateBasedGateways.indexOf(operator) > -1 ? lastBillDate : lastDueDate,
            daysToAdd = 0,
            nextBillFetchDates = self.billsLib.getConfigByKeys({
                dynamicConfig:true,
                name:'OPERATOR_CONFIG',
                node:operator,
                keyname:'NEXT_BILL_FETCH_DATES',
                default:null
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix:'AUTOPAY_',
                prefixToKey: 'keyname'
            })
            || self.billsLib.getConfigByKeys({
                dynamicConfig:false,
                name:'OPERATOR_CONFIG',
                node:operator,
                keyname:'NEXT_BILL_FETCH_DATES',
                default:null
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix:'AUTOPAY_',
                prefixToKey: 'keyname'
            }),
            nextRetryFreq = self.getNextRetryFreq(operator, productId, oldProductId, defaultFreq, currentAutomaticStatus);

            self.L.log("getNextBillFetchDateForRetry:: ", "currentAutomaticStatus: ", currentAutomaticStatus, "nextRetryFreq: ", nextRetryFreq, "nextBillFetchDates: ", nextBillFetchDates);

        /*
            NEXT_BILL_FETCH_DATES = [25,32,40]
            |-----------------------------------------------|
                |         |        |        |   | | | | 
                0         25       32       40  Retry....
            (dateToBeUsed)          

            So here we will be adding 25,32 and 40 days subsequently in dateToBeUsed
            When we reach at max limit in NEXT_BILL_FETCH_DATES array , there after we will be adding NEXT_RETRY_FREQUENCY
        */
        if (_.isArray(nextBillFetchDates)) {
            let
                diffWithBaseDate = MOMENT().startOf('day').diff(MOMENT(dateToBeUsed).startOf('day'), 'days');
            for (let i = 0; i < nextBillFetchDates.length; i++) {
                if(nextBillFetchDates[i]=='-1' && 30 > diffWithBaseDate){
                    daysToAdd = nextBillFetchDates[i];
                    break;
                }
                else if (nextBillFetchDates[i] > diffWithBaseDate) {
                    daysToAdd = nextBillFetchDates[i];
                    break;
                }
            }

            if (daysToAdd) {
                nextBillFetchDate = daysToAdd < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(daysToAdd)), 'months') : MOMENT(dateToBeUsed).add(daysToAdd, 'days');
            } else {
                nextBillFetchDate = nextRetryFreq < 0 ? MOMENT().add(Math.abs(Number(nextRetryFreq)), 'months') :  MOMENT().add(nextRetryFreq, 'days');
            }
        } else {
            nextBillFetchDate = nextRetryFreq < 0 ? MOMENT().add(Math.abs(Number(nextRetryFreq)), 'months') : MOMENT().add(nextRetryFreq, 'days');
        }

        return nextBillFetchDate;
    }

    getNextRetryFreq(operator, productId, oldProductId, defaultFreq, currentAutomaticStatus=null) {
        let self = this,
            nextRetryFreq = self.billsLib.getConfigByKeys({
                dynamicConfig:true,
                name:'OPERATOR_CONFIG',
                node:operator,
                keyname: 'NEXT_RETRY_FREQUENCY',
                default:null
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'keyname'
            }) || self.billsLib.getConfigByKeys({
                dynamicConfig:false,
                name:'SUBSCRIBER_CONFIG',
                node:'NEXT_RETRY_FREQUENCY',
                keyname: productId,
                default:null
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'node'
            }) || self.billsLib.getConfigByKeys({
                dynamicConfig:false,
                name:'SUBSCRIBER_CONFIG',
                node:'NEXT_RETRY_FREQUENCY',
                keyname: oldProductId,
                default:null
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'node'
            }) || self.billsLib.getConfigByKeys({
                dynamicConfig:false,
                name:'SUBSCRIBER_CONFIG',
                node:'NEXT_RETRY_FREQUENCY',
                keyname: operator,
                default:defaultFreq
            },{
                currentAutomaticStatus:currentAutomaticStatus,
                prefix: 'AUTOPAY_',
                prefixToKey: 'node'
            })


                self.L.log("getNextRetryFreq:: ", "currentAutomaticStatus: ", currentAutomaticStatus, "nextRetryFreq: ", nextRetryFreq);

        return nextRetryFreq;
    }

    getCommonDueDateAmountStatus(billData) {
        let
            self = this,
            due_date = billData.dbDueDate,
            status = billData.dbStatus;

        billData.commonAmount = billData.dbAmount;
        billData.commonStatus = 1; // Keeping it as per current flow where status = PUBLISHED
        billData.commonDueDate = billData.dbDueDate;

        if (_.isNull(billData.billDueDate) && (billData.status == 4) && billData.setDueDateNull) {
            billData.commonDueDate = billData.billDueDate;
            !_.isNull(billData.amount) && !_.isNaN(billData.amount) ? billData.commonAmount = billData.amount : "";
            billData.commonStatus = billData.status;
            return;
        }

        if (!_.isNull(billData.billDueDate) && due_date != billData.billDueDate) {
            billData.commonStatus = billData.status;
            !_.isNull(billData.amount) && !_.isNaN(billData.amount) ? billData.commonAmount = billData.amount : "";
            if (_.get(billData, 'prepaidPublisherInstance', false)) {
                billData.commonAmount = null;
            }
            billData.commonDueDate = billData.billDueDate;
        }
        if (!_.isNull(billData.billDueDate) && due_date == billData.billDueDate && status != _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11)) {
            billData.commonStatus = billData.status;
            !_.isNull(billData.amount) && !_.isNaN(billData.amount) ? billData.commonAmount = billData.amount : "";
            billData.commonDueDate = billData.billDueDate;
        }
        let possibleUpdateStatus = [ 6, 13, _.get(self.config, 'COMMON.bills_status.EARLY_BILL_FETCH', 16) ];
        if (_.isNull(billData.billDueDate) && ( possibleUpdateStatus.indexOf(billData.status) > -1 || status != _.get(self.config, 'COMMON.bills_status.PAYMENT_DONE', 11))) {
            billData.commonStatus = billData.status;
            !_.isNull(billData.amount) && !_.isNaN(billData.amount) ? billData.commonAmount = billData.amount : "";
        }
        billData.commonDueDate = MOMENT(billData.commonDueDate).startOf('day').format('YYYY-MM-DD HH:mm:ss');
        return;
    }

    /*
    *   Function will get data on the basis of customer_id,
    *   product_id and recharge_number
    */
    getBill(cb, operator, customerId, productId, rechargeNumber) {

        var
            self = this,
            tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', productId], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', operator], null);

        if (tableName) {
            this.bills.getBill(function (error, data) {

                if (error) {
                    return cb(error);
                } else {
                    if (data.length === 0) {
                        return cb('No Data found!!!')
                    } else {
                        return cb(null, self.commonLib.mapBillsTableColumns(data[0]));
                    }
                }

            }, tableName, operator, customerId, productId, rechargeNumber)
        } else {
            L.error("getBill", "Operator not migrated:: ", operator);
            return cb('Operator not migrated.');
        }

    }

    /*
    *   Function will get CC data on the basis of customer_id,
    *   product_id and reference_id
    */
    getCCBill(cb, operator, customerId, productId, referenceId) {

        var
            self = this,
            tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', productId], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', operator], null);

        if (tableName) {
            this.bills.getCCBill(function (error, data) {

                if (error) {
                    return cb(error);
                } else {
                    if (data.length === 0) {
                        return cb('No Data found!!!')
                    } else {
                        return cb(null, self.commonLib.mapBillsTableColumns(data[0]));
                    }
                }

            }, tableName, operator, customerId, productId, referenceId)
        } else {
            L.error("getCCBill", "Operator not migrated:: ", operator);
            return cb('Operator not migrated.');
        }

    }

    /*
    *   Function will get notification data on the basis of customer_id or recharge_number from all bills tables,
    */
    getNotificationRecords(cb, key, value, tablesToScan) {
        let self = this;
        let notificationRecords = [];
        ASYNC.eachLimit(tablesToScan, 20, function (tableName, cb) {
            self.bills.getNotificationRecords(function (error, data) {
                if (!error) {
                    if (data && data.length && data.length > 0) {
                        self.L.log("billSubscriber::getNotificationRecords ", "Data Found for ", key, value, tableName)
                        notificationRecords.push(...data);
                    }
                }
                return cb();
            }, tableName, key, value);
        }, (err, res) => {
            if (err) {
                self.L.error("billSubscriber::getNotificationRecords ", "Error occured", err)
            }
            return cb(null, notificationRecords);
        });
    }
     
    /*
    *    Function will get notification data on the basis of customer_id or recharge_number from all bills and prepaid tables,
    */
    getAllNotificationRecords(cb, key, value, tablesToScan) {
        let self = this;
        tablesToScan.splice(tablesToScan.indexOf('bills_creditcard'), 1);
        let extraTables = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_TABLES', 'TABLES', 'EXTRA'], []);
        let notificationRecords = [];

        ASYNC.waterfall([
            next=>{
                ASYNC.eachLimit(tablesToScan, 20, function (tableName, callback) {
                    self.bills.getNotificationRecords(function (error, data) {
                        if (!error) {
                            if (data && data.length && data.length > 0) {
                                self.L.log("billSubscriber::getNotificationRecords ", "Data Found for ", key, value, tableName)
                                notificationRecords.push(...data);
                            }
                        }
                        return callback();
                    }, tableName, key, value);
                }, (err, res) => {
                    if (err) {
                        self.L.error("billSubscriber::getNotificationRecords ", "Error occured", err)
                    }
                    next(err,notificationRecords);
                    // return cb(null, notificationRecords);
                 });
            },(notificationRecords,next)=>{
        
                ASYNC.eachLimit(extraTables, 10, function (tableName, callback) {
                    self.bills.getNotificationPrepaidRecords(function (error, data) {
                        if (!error) {
                            if (data && data.length && data.length > 0) {
                                self.L.log("billSubscriber::getNotificationPrepaidRecords ", "Data Found for ", key, value, tableName)
                                notificationRecords.push(...data);
                            }
                        }
                        return callback();
                    }, tableName, key, value);
                }, (err, res) => {
                    if (err) {
                        self.L.error("billSubscriber::getNotificationPrepaidRecords ", "Error occured", err)
                    }
                    next(err,notificationRecords);
                });
            },(notificationRecords,next)=>{
                let CCtable = 'bills_creditcard';
                self.bills.getNotificationCCRecords(function (error, data) {
                    if (!error) {
                        if (data && data.length && data.length > 0) {
                            self.L.log("billSubscriber::getNotificationCCRecords ", "Data Found for ", key, value, CCtable)
                            notificationRecords.push(...data);
                        }
                    }
                    next(error);
                }, CCtable, key, value);

            }   

        ],function (error) {
            if (error) {
                self.L.error('getAllNotificationRecords', 'Error while fetch records', error);
            }
            return cb(error,notificationRecords);
        });
    }

    /*
   *   Function will get data on the basis of customer_id,
   *   product_id and recharge_number
   */
    getMultipleBill(cb, operator, customerIds, productIds, rechargeNumbers) {

        var tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', operator], null);
        if (tableName) {
            this.bills.getMultipleBill(function (error, data) {
                if (error) {
                    return cb(error);
                } else {
                    if (data.length === 0) {
                        return cb('No Data found!!!')
                    } else {
                        return cb(null, data);
                    }
                }
            }, tableName, operator, customerIds, productIds, rechargeNumbers)
        } else {
            L.error("getMultipleBill", "Operator not migrated:: ", operator);
            return cb('Operator not migrated.');
        }
    }

    /**
     * Calculate NBFD based on cycle for operator using billDate or billDueDate
     * @param {*} operator 
     * @param {*} billDate 
     * @param {*} billDueDate 
     */
    getNBFDByCycle(record,allowedPrepaidDthOperator,operator, billDate, billDueDate,currentAutomaticStatus=null) {
        let self = this,
            nbfd = null;

        if((_.toLower(record.paytype) == 'prepaid' && _.toLower(record.service) =='dth' && allowedPrepaidDthOperator.indexOf(_.toLower(record.operator))>-1)){
            nbfd =  MOMENT().add(1, 'days');
        } else if (billDate && self.billDateBasedGateways[operator]) {
            //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
            nbfd = self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? MOMENT(billDate, 'YYYY-MM-DD').add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : MOMENT(billDate, 'YYYY-MM-DD').add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
        } else if (billDueDate && !self.billDateBasedGateways[operator]) {
            //If an operator has ndfd as 30 days, we will add 1 month instead of 30 days to maintain correct bill cycle
            nbfd = self.getFirstBillFetchInterval(operator, currentAutomaticStatus) < 0 ? MOMENT(billDueDate, 'YYYY-MM-DD').add(Math.abs(Number(self.getFirstBillFetchInterval(operator, currentAutomaticStatus))), 'months') : MOMENT(billDueDate, 'YYYY-MM-DD').add(self.getFirstBillFetchInterval(operator, currentAutomaticStatus), 'days');
        }

        self.L.log("getNBFDByCycle:: ", "currentAutomaticStatus: ", currentAutomaticStatus, "nbfd: ", nbfd);

        if (nbfd && nbfd.diff(MOMENT(), 'days') > 0) {
            return nbfd.format('YYYY-MM-DD HH:mm:ss');
        } else {
            return null;
        }
    }

    getNextBillFetchDate(record) {
        let self = this;
        let billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        let dateToBeUsed = billDateBasedGateways.indexOf(record.operator) > -1 && record.billDate ? record.billDate : record.billDueDate;

        if (billDateBasedGateways.indexOf(record.operator) > -1 && !record.billDate) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_POSTPAID",
                `SERVICE:${_.get(record, 'category', null)}`,
                'STATUS:BILLDATE_ABSENT_BILLDATEBASEDGATEWAY',
                "OPERATOR:" + record.operator
            ]);
        }

        let nextBillFetchDate = self.getFirstBillFetchInterval(record.operator) < 0 ? MOMENT(dateToBeUsed).add(Math.abs(Number(self.getFirstBillFetchInterval(record.operator))), 'months') : MOMENT(dateToBeUsed).add(self.getFirstBillFetchInterval(record.operator), 'days');
        if (nextBillFetchDate < MOMENT()) {
            self.L.error(`getNextBillFetchDate:: Change NBFD, currently set in past debugKey: ${record.debugKey} NBFD: ${nextBillFetchDate}`);
            nextBillFetchDate = MOMENT().add(1, 'days');
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:BILLS_SUBSCRIBER",
                `SERVICE:${_.get(record, 'service', null)}`,
                'STATUS:NBFD_SETTING_IN_PAST',
                "OPERATOR:" + record.operator
            ]);
        }
        return nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');
    }

    createRecentBill(callback, recentBillsData, fromRecents = false) {
        let self = this;
        
        self.createBill((error, data) => {
            callback(error, data);
        }, recentBillsData, fromRecents);
    }

    /*
       *    Function will create data in Bills Tables on the basis of customer_id,
       *    operator,service and recharge_number as Key
    */

    //api -> dbRecord, reminderSync, recentBills
    createBill(responseCb, params, fromRecents = false) {

        var self = this;
        _.extend(params, {
            'retryCount': 0,
            'status':  _.get(this.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            'reason': _.get(params, 'reason', null),
            'extra': _.get(params, 'extra', null),
            'referenceId': _.get(params, 'referenceId', null),
            'parId': _.get(params, 'parId', null),
            'tin': _.get(params, 'tin', null),
            'isCreditCardOperator': self.commonLib.isCreditCardOperator(params.service),
        });

        if (params.isCreditCardOperator && !params.tokenisedCreditCard && !params.referenceId) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:CREATE_ERROR', `TYPE:INVALID_REF_ID`, `OPERATOR:${_.get(params, 'operator',null)}`]);
            L.error('createBill', `Invalid referenceId for RN:${this.encDecpUtil.encryptData(params.rechargeNumber)},custId:${params.customerId},operator:${params.operator}`);
            return responseCb('Invalid referenceId');
        }

        if (params.isCreditCardOperator && params.tokenisedCreditCard && !params.parId) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:CREATE_ERROR', `TYPE:INVALID_PAR_ID`, `OPERATOR:${_.get(params, 'operator',null)}`]);
            L.error('createBill', `Invalid parId for RN:${this.encDecpUtil.encryptData(params.rechargeNumber)},custId:${params.customerId},operator:${params.operator}`);
            return responseCb('Invalid parId');
        }
        if(params.service == 'financial services'){
            try{
                if(params.actualHistory && params.actualHistory.customerOtherInfo){
                params.customerOtherInfo=JSON.parse(_.get(params, 'actualHistory.customerOtherInfo'));
                params.customerOtherInfo.currentBillAmount = utility.getFilteredAmount(_.get(params, 'customerOtherInfo.currentBillAmount', 0)) + _.get(params, 'amount', 0);
                params.customerOtherInfo.currentMinBillAmount = utility.getFilteredAmount((_.get(params, 'customerOtherInfo.currentMinBillAmount', 0)) + _.get(params, 'amount', 0)) < 0 ? 0 : utility.getFilteredAmount(_.get(params, 'customerOtherInfo.currentMinBillAmount', 0)) + _.get(params, 'amount', 0);
                params.customerOtherInfo=JSON.stringify(params.customerOtherInfo);
                params.customerOtherInfoToBeUpdated=true;
            }
        }
            catch(err){
                L.error('createBill :: error occured while parsing', err);
            }
            
        }

        if (params.paymentDate != null) {
            // Third parameter makes sure that the payment date is passed in the specific format. Otherwise the date will be invalid
            var payDate = MOMENT(params.paymentDate, 'YYYY-MM-DD HH:mm:ss', true);

            if (!payDate.isValid()) {
                L.log('createBill', 'Clearing payment date', params.paymentDate);
                params.paymentDate = null;
            } else {
                params.paymentDate = payDate.format('YYYY-MM-DD HH:mm:ss');
            }
        }

        var amount = params.amount,
            isPaymentDone = false,
            self = this;
        //Handling recents flow. When a payment is done then we need to mark the status as payment done.
        if (amount && amount != null && (amount < 0 || (params.service_id == _.get(this.config, 'COMMON.PREPAID_SERVICE_ID', 4) && amount > 0))) {
            params.status = _.get(this.config, 'COMMON.bills_status.PAYMENT_DONE', 11);
            isPaymentDone = true;
        }
        if(params.paytype == 'prepaid' && params.service =='dth' && (_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], []).indexOf(_.toLower(params.operator))>-1)){
            params.status = _.get(this.config, 'COMMON.bills_status.PAYMENT_DONE', 11);
            isPaymentDone = true;
        }

        var notificationStatus = _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1));
        //Lodash treats null as a value and return null even if default value is provided.
        if (notificationStatus == null || !_.get(self.allowedNotificationStatuses, notificationStatus, null)) {
            params.notificationStatus = _.get(self.config, 'COMMON.notification_status.ENABLED', 1);
        }
        if(!(this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params))){
            _.set(params, 'nextBillFetchDate', self.getPrimaryCycleTimeForNBFD(params));
        }
        //For first time user only if bill date is present in recent data then NBFD - Bill Date + 30 days
        var NBFD_DAYS_TO_ADD_CC = _.get(self.config, ['SUBSCRIBER_CONFIG', 'NEXT_BILL_FETCH_DATES', params.operator], 30);

        if(params.billDate!=null && params.paytype == "credit card"){
            _.set(params, 'nextBillFetchDate', MOMENT(params.billDate).add(NBFD_DAYS_TO_ADD_CC, 'days').format('YYYY-MM-DD'));
        }

        var self = this,
            tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.productId], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);
            if(_.toLower(_.get(params, 'service', ''))=='paytm postpaid'){
                tableName='bills_paytmpostpaid'
            }

        if(this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params)){
            L.log(`createBill:: prepaid electricity record, updating tableName to ${params.tableName}, debugKey : ${params.debugKey}`);
            tableName = params.tableName;
        }

        let amountToLog = params.amount;
        if (_.toLower(params.paytype) === 'credit card') {
            amountToLog = this.encDecpUtil.encryptData(params.amount);
        }

        L.log('billSubscriber::create bill', 'Creating bill for operator: ' , params.operator , ' productId: ' , params.productId , ' customerId: ' , params.customerId, ' rechargeNumber: ' , params.rechargeNumber , ' amount: ' , amountToLog , ' status: ' + params.status , ' NBFD: ' , params.nextBillFetchDate , ' fromRecents: ' , fromRecents);

        if (tableName) {
            if (params.sourceType === 'reminderSync' || params.source === 'api') {
                let prepaidTableName = tableName + '_prepaid';
                self.L.info(`billSubscriber::create bill handling new flow with prepaid bills check for rechargeNumber :: ${_.get(params, 'rechargeNumber', null)}, source: ${_.get(params, 'sourceType', _.get(params, 'source', null))}`);
                self.handlePrepaidToPostpaidMigration(prepaidTableName, tableName, params, responseCb, fromRecents, isPaymentDone);
            } else {
                // Existing flow. No need to move records from prepaid to postpaid table
                self.L.info(`billSubscriber::create bill handling old flow for rechargeNumber ::`, `${_.get(params, 'rechargeNumber', null)}`, `source: ${_.get(params, 'sourceType', _.get(params, 'source', null))}`);
                self.processBillCreation(tableName, params, responseCb, fromRecents, isPaymentDone);
            }
        } else {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:ERROR', `TYPE:OPERATOR_NOT_MIGRATED`]);
            L.error("billSubscriber::create bill", "Operator not migrated:: ", params.operator);
            return responseCb('Operator not migrated.');
        }        
    }
    
    handlePrepaidToPostpaidMigration(prepaidTableName, tableName, params, responseCb, fromRecents, isPaymentDone) {
        let self = this;
        const rechargeNumber = _.get(params, 'rechargeNumber', null);
        
        self.bills.getBillsOfSameRech((err, dbRecord) => {
            if (err) {
                self.L.error(`Error fetching records from table for rechargeNumber: ${rechargeNumber}, Error: `, err);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER_PRE_POS", 'STATUS:FAILURE', `TYPE:getBillsOfSameRech`]);
                return responseCb(err);
            }

            if (!dbRecord || dbRecord.length === 0) {
                self.L.info(`No records in both tables, proceeding with Kafka record bills creation for rechargeNumber: ${rechargeNumber}`);
                return self.processBillCreation(tableName, params, responseCb, fromRecents, isPaymentDone);
            }

            const extraInfo = self.getExtra(dbRecord);
            const isPrepaid = _.get(extraInfo, 'isPrepaid', false);

            self.L.log(`dbRecord length: ${dbRecord.length}, rechargeNumber: ${rechargeNumber}, isPrepaid: ${isPrepaid}`);
            
            let prepaidToPostpaidRecords = [];
            let deleteIds = [];

            if (isPrepaid) {
                // If the dbRecord is prepaid record, then only we need to migrate and delete.
                dbRecord.forEach(record => {
                    if (record.customer_id !== params.customerId) {
                        record.is_automatic = 2;
                        prepaidToPostpaidRecords.push(record);
                    }
                    deleteIds.push(record.id);
                });
            }
    
            if (prepaidToPostpaidRecords.length > 0) {
                self.bills.prepaidToPostpaidInsertion((err) => {
                    if (err) {
                        self.L.error(`prepaidToPostpaidInsertion :: Error inserting records into postpaid table for rechargeNumber: ${rechargeNumber}, Error:`, err);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:FAILURE', `TYPE:INSERT_PREPAID_TO_POSTPAID`]);
                        return responseCb(err);
                    }
    
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:SUCCESS', `TYPE:INSERT_PREPAID_TO_POSTPAID`]);
                    self.deletePrepaidRecords(prepaidTableName, deleteIds, rechargeNumber, () => {
                        self.L.log(`prepaidToPostpaidInsertion :: received kafka record processBillCreation rechargeNumber: ${rechargeNumber}, sameCustIdInPostpaid: ${params.sameCustIdInPostpaid}`);
                        return self.handleBillCreation(params, responseCb, tableName, fromRecents, isPaymentDone);
                    });
                }, tableName, prepaidToPostpaidRecords);
            } else {
                //Use case when prepaid table record has same custId as kafka record OR no records in prepaid
                if (deleteIds.length > 0) {
                    self.deletePrepaidRecords(prepaidTableName, deleteIds, rechargeNumber, () => {
                        self.L.log(`prepaidToPostpaidInsertion :: received kafka record same custId in prepaid, rechargeNumber: ${rechargeNumber}, isSameCustIdInPostpaid: ${params.sameCustIdInPostpaid}`);
                        return self.handleBillCreation(params, responseCb, tableName, fromRecents, isPaymentDone);
                    });
                } else {
                    self.L.log(`handlePrepaidToPostpaidMigration :: NO ids to delete from prepaid, proceeding for bills creation rechargeNumber: ${rechargeNumber}`);
                    return self.handleBillCreation(params, responseCb, tableName, fromRecents, isPaymentDone);
                }
            }
        }, tableName, {
            rechargeNumber: params.rechargeNumber,
            service: params.service,
            operator: params.operator
        });
    }

    handleBillCreation(params, responseCb, tableName, fromRecents, isPaymentDone) {
        let self = this;
        self.L.log(`handleBillCreation :: rechargeNumber: ${params.rechargeNumber}, sameCustIdInPostpaid: ${params.sameCustIdInPostpaid}`);
        if (params.sameCustIdInPostpaid) {
            // Same cust id in prepaid and postpaid, no need to create bill, updation handled already in updateCondition.
            return responseCb(null);
        }
        return self.processBillCreation(tableName, params, responseCb, fromRecents, isPaymentDone);
    }

    deletePrepaidRecords(prepaidTableName, deleteIds, rechargeNumber, callback) {
        let self = this;
        if (deleteIds.length > 0) {
            self.L.log(`deletePrepaidRecords :: deleting records from prepaid table for rechargeNumber :: ${rechargeNumber}, deleteIds :: ${deleteIds}`);
            self.bills.deleteRecordsByIds((err) => {
                if (err) {
                    self.L.error('deletePrepaidRecords :: Error deleting records from prepaid table:', err);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:FAILURE', `TYPE:DELETE_PREPAID_RECORDS`]);
                    return callback(err);
                }
    
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:SUCCESS', `TYPE:DELETE_PREPAID_RECORDS`]);
                callback();
            }, prepaidTableName, deleteIds, rechargeNumber);
        } else {
            self.L.log(`No records to delete from prepaid table rechargeNumber :: ${rechargeNumber}`);
            callback();
        }
    }

    processBillCreation(tableName, params, responseCb, fromRecents, isPaymentDone) {
        let self = this;
        ASYNC.series([
            function (cb) {
                if (params.paymentChannel === 'BOU 1') {
                    self.L.log(`createBill:: Skipping createBill because of BOU 1 channelId for RN:${params.rechargeNumber}, custId:${params.customerId}, operator:${params.operator}, productId:${params.productId}`);
                    self.bills.updateBills(function (error, data) {
                        responseCb(error, data);
                        cb(error, data);
                    }, tableName, params, fromRecents);
                } else {
                    if (_.get(self.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'ALLOWED_DTH_OPERATOR'], [])
                        .indexOf(_.toLower(params.operator)) > -1) {
                        params.billDthService = true;
                        self.bills.createBill(function (error, data) {
                            responseCb(error, data);
                            cb(error, data);
                        }, tableName, params, fromRecents);
                    } else {
                        self.bills.createBill(function (error, data) {
                            responseCb(error, data);
                            cb(error, data);
                        }, tableName, params, fromRecents);
                    }
                }
            },
            function (cb) {
                self.L.log(`BillSubscriber:: createBill, params.updateAllCustIdRecords: ${params.updateAllCustIdRecords}, isPaymentDone - ${isPaymentDone}`);
                
                if (isPaymentDone && params.updateAllCustIdRecords) {
                    self.bills.updateBillsForSameRechargeNum(function (updatedError, updatedData) {
                        cb(updatedError, updatedData);
                    }, tableName, params);
                } else {
                    cb(null);
                }
            },
            // Uncomment the following function if needed in the future
            // function (cb) {
            //     // Only update BillReminder column in users collection
            //     self.users.updateBillReminderFlag(function (error, data) {
            //         cb(error, data);
            //     }, 'users', params, true);
            // }
        ], function done(err, results) {
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:ERROR', `TYPE:CREATE_BILL_ERROR`]);
                L.critical("billSubscriber::create bill", "Error in create API :: ", err);
            }
        });
    }

    getPrimaryCycleTimeForNBFD(params) {
        let self = this,
            nbfd = _.get(params, 'nextBillFetchDate'),
            bill_delay_time_unit = _.get(self.config,['RECENT_BILL_CONFIG', 'OPERATORS', params.operator,'BILL_DELAY_TIME_UNIT'], 'days');

        if(bill_delay_time_unit == 'days') {
            nbfd = MOMENT(nbfd).format('YYYY-MM-DD 01:00:00');
        }
        return nbfd;
    }

    // update recent bill 
    updateRecentBill(responseCb, params, fromRecents = false) {
        var
            self = this;
        _.extend(params, {
            'status': _.get(this.config, 'COMMON.bills_status.PAYMENT_DONE', 11)
        });

        if(!(this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params))){
            _.set(params, 'nextBillFetchDate', MOMENT(_.get(params, 'nextBillFetchDate')).format('YYYY-MM-DD 01:00:00'));
        }
    
        var tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.productId], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);

        if(this.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator) && self.recentBillLibrary.checkIfIsPrepaidIsSet(params)){
            L.log(`createBill:: prepaid electricity record, updating tableName to ${params.tableName}, debugKey : ${params.debugKey}`);
            tableName = params.tableName;
        }
        L.log('billSubscriber::update bill', 'Updating bill for operator: ' + params.operator + ' productId: ' + params.productId + ' customerId: ' + params.customerId, ' rechargeNumber: ' + params.rechargeNumber + ' amount: ' + params.amount + ' status: ' + params.status);

        if (tableName) {
            ASYNC.series([
                function (cb) {
                    self.bills.updateRecentBill(function (error, data) {
                        responseCb(error, data);
                        cb(error, data);
                    }, tableName, params, fromRecents);
                },
                function (cb) {
                    self.L.log(`BillSubscriber:: updateRecentBill, params.updateAllCustIdRecords: ${params.updateAllCustIdRecords}`);
                    if(params.updateAllCustIdRecords) {
                        self.bills.updateBillsForSameRechargeNum(function (updatedError, updatedData) {
                            return cb(updatedError, updatedData);
                        }, tableName, params);
                    } else {
                        return cb(null);
                    }
                },
                // function (cb) {
                //     //only update BillReminder column in users collection
                //     self.users.updateBillReminderFlag(function (error, data) {
                //         cb(error, data);
                //     }, 'users', params, true);
                // }
            ], function done(err, results) {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:ERROR', `TYPE:UPDATE_BILL_ERROR`]);
                    L.error("billSubscriber::update bill", "Error in updating the recent bill :: ", err);
                }
            });
        } else {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:ERROR', `TYPE:OPERATOR_NOT_MIGRATED`]);
            L.error("billSubscriber::update bill", "Operator not migrated:: ", params.operator);
            return responseCb('Operator not migrated.');
        }
    }

    /*
    *   Function will update data in Bills Tables on the basis of customer_id,
    *   operator,service and recharge_number as Key
    */
    updateBill(cb, params) {
        var self = this,
            tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.productId], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);
        if (tableName) {

            ASYNC.parallel([
                function (cb) {
                    self.bills.updateBill(function (error, data) {
                        cb(error, data);
                    }, tableName, params);
                },
                // function (cb) {
                //     self.users.fetchAndUpdateUserBill(function (error, data) {
                //         cb(error, data);
                //     }, 'users', params);
                // },
            ], function done(err, results) {
                return cb(err, results);
            });
        } else {
            L.error("getBill", "Operator not migrated:: ", params.operator);
            return cb('Operator not migrated.');
        }
    }

    getBillsData(record) {
        let self = this;
        let dbRecord,
            dbExtra = {},
            customerOtherInfo = {},
            oldBillFetchDate = null;
        if (record.isRecordExist) {
            try {
                dbRecord = _.get(record, 'dbData[0]', {});
                dbExtra = JSON.parse(_.get(dbRecord, 'extra', {}));
                if (!dbExtra) dbExtra = {};
            } catch (err) {
                self.L.error("getBillsData", "Error in JSON parsing" + err);
            }
        }
        customerOtherInfo = JSON.parse(_.get(record, 'customerOtherInfo', {}));
        if (!customerOtherInfo) customerOtherInfo = {};
        let custInfoValues = customerOtherInfo;
        let extraDetails = dbExtra;
        extraDetails.billSource = 'UPMS';
        extraDetails.updated_data_source = 'UPMS'
        if (_.get(record, 'status', null) == _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)) {
            if (_.get(dbRecord, 'old_bill_fetch_date', null) != null) {
                oldBillFetchDate = MOMENT(_.get(dbRecord, 'old_bill_fetch_date', null)).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
            else {
                oldBillFetchDate = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
        }
        else {
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(_.get(record, 'billFetchDate', null)).isValid() ? MOMENT(_.get(record, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss');
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null);
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        }
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        extraDetails.upmsPushDate = MOMENT(_.get(record, 'billFetchDate', null)).isValid() ? MOMENT(_.get(record, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss');
        extraDetails.lastNextBillFetchDate = _.get(dbRecord, 'next_bill_fetch_date', null);
        extraDetails.upmsRegistrationNumber = _.get(record, 'registrationNumber', null);
        extraDetails.upmsBillPaymentToken = _.get(record, 'refId', null);

        _.set(extraDetails, 'source_subtype_2', 'FULL_BILL');
        let billsData = {
            customerId: record.customerId,
            rechargeNumber: record.rechargeNumber,
            operator: record.operator,
            service: record.service,
            commonDueDate: record.billDueDate,
            billDate: record.billDate,
            nextBillFetchDate: self.getNextBillFetchDate(record),
            billFetchDate: extraDetails.billFetchDate,
            commonAmount: record.currentBillAmount,
            productId: record.productId,
            commonStatus: record.status,
            customerMobile: _.get(record, 'customerMobile', _.get(record, 'customer_mobile', null)),
            paytype: _.get(record, 'paytype', null),
            customerEmail: _.get(record, 'customerEmail', null),
            service_id: _.get(record, 'service_id', 0),
            is_automatic: _.get(record, 'is_automatic', 0),
            reason: null,
            retryCount: 0,
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            oldBillFetchDate: oldBillFetchDate
        };
        let existingDueDate = _.get(dbRecord, 'due_date') ? MOMENT(_.get(dbRecord, 'due_date')).utc().startOf('day') : null;
        this.L.log("existingDueDate", existingDueDate);
        let newDueDate = _.get(record, 'billDueDate') ? MOMENT(_.get(record, 'billDueDate')).utc().startOf('day') : null;
        this.L.log("newDueDate", newDueDate);
        _.set(billsData, 'remindLaterDate',_.get(dbRecord, 'remind_later_date', null));
        newDueDate = newDueDate ? newDueDate.subtract(_.get(this.config, ['DYNAMIC_CONFIG', 'REMIND_ME_LATER_CONFIG', 'RU', 'DEFAULT_DIFF_DAYS'], 5), 'days') : null;

        if(existingDueDate && newDueDate && newDueDate.isAfter(existingDueDate, 'day')) {
            this.L.log("[UpmsBillUpdate :: Setting remind later date as null because of new bill");
            _.set(billsData,"resetRemindLaterDate",true);
            _.set(billsData, 'remindLaterDate',null);

            if(_.get(dbRecord, 'remind_later_date', null) != null){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPMS_BILL_UPDATE', `SERVICE:${record.service}`, `OPERATOR:${record.operator}`, 'STATUS:SUCCESS', 'TYPE:RESET_REMIND_LATER_DATE']);
                this.L.log("[UpmsBillUpdate :: Setting remind later date as null for cust id " + record.customerId + ", recharge number " + record.rechargeNumber + ", operator " + record.operator);
            }
        }
        try{
            let highestPriorityAmongestRows = self.billsLib.getHighestPriorityAmongestRows(_.get(record,'dbData',[]));
            let highestPublishedDateAmongestRows = self.billsLib.getHighestPublishedDateAmongestRows(_.get(record,'dbData',[]));
            billsData = self.billsLib.updateRecordWithOffsetNbfd(billsData, highestPriorityAmongestRows, highestPublishedDateAmongestRows);
        }catch(e){
            self.L.error("getBillsData", "Error in updating record with offset nbfd" + e);
        }
        return billsData;
    }

    /**
         * Returns active users for which smsparsing data will be updated
         * @param {*} dbRecords
         */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }

    updateDbRecord(done, record) {
        let self = this;
        let billsData = record.billsData,
            dbRecord = _.get(record, 'dbData', {}),
            dbDueDate = _.get(dbRecord, '[0].due_date', null),
            dueDateDiff = null,
            amount = _.get(record, 'amount', null),
            dbAmount = _.get(dbRecord, '[0].amount', null),
            maxPaymentDate = _.get(record, 'maxPaymentDate', null),
            maxBillDate = _.get(record, 'maxBillDate', null),
            service = _.get(record, 'service', null);

        self.L.log('updateDbRecord:: starting updateDbRecord');
        if (record.activeRecordsInDB == 0) {
            self.L.log('updateRecord', `No actve records in DB, so skipping update for ${record.debugKey}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:BILLS_SUBCRIBER",
                `SERVICE:${_.get(record, 'service', null)}`,
                'STATUS:ERROR',
                'TYPE:NO_ACTIVE_RECORDS',
                "SOURCE:UPDATE_DB_RECORD",
                "OPERATOR:" + record.operator
            ]);
            return done(null);
        } else {
            _.set(billsData, 'is_automatic', null);
            self.L.log('updateRecord', `Updating records in sql DB for ${record.debugKey}`);

            if (_.get(record, 'billDueDate', null) != null && dbDueDate) {
                dueDateDiff = MOMENT(record.billDueDate).startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day'), 'day');
            }
            let amountDiff = Math.abs(amount - dbAmount);
            if (MOMENT(record.billDueDate) < MOMENT() && self.oldBillFetchDueDateAllowedService.includes(service) && dueDateDiff === 0 && maxPaymentDate && maxBillDate && MOMENT(maxPaymentDate) < MOMENT(maxBillDate)) {
                _.set(billsData, amountDiff?'updateForOldBillAmountDiff':'updateForOldBillAmountSame', true);
            }

            self.bills.updateBillForSameRechargeNumPostpaid((err) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:BILLS_SUBCRIBER",
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:ERROR',
                        "TYPE:UPDATE_SQL",
                        "SOURCE:UPDATE_DB_RECORD",
                        "OPERATOR:" + record.operator
                    ]);
                    return done({ message: err, errorCode: "2XX" });
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:BILLS_SUBCRIBER",
                        `SERVICE:${_.get(record, 'category', null)}`,
                        'STATUS:SUCCESS',
                        'TYPE:UPDATE_SQL',
                        "OPERATOR:" + record.operator,
                        "SOURCE:UPDATE_DB_RECORD"
                    ]);
                    return done(null);
                }
            }, record.tableName, billsData);
        }
    }

    publishInKafka(done, record, action) {
        let self = this;
        ASYNC.parallel([
            function (cb) {
                self.publishToKafka.publishToAutomaticSync(function (err) {
                    cb(err);
                }, record, self.kafkaPublisher)
            },
            function (cb) {
                self.publishToKafka.publishInBillFetchKafka(function (err) {
                    cb(err)
                }, record, self.kafkaBillFetchPublisher, self.billFetchKafkaRealtime)
            },
            function (cb) {
                self.publishToKafka.publishCtEvents(function (err) {
                    cb(err)
                }, record, self.ctKafkaPublisher)
            },
        ], function (error) {
            if (error) {
                self.L.error('Error occurred during parallel tasks:', error);
                done({ message: error, errorCode: "5XX" })
            }
            else
                done(null);
        });
    }

    prepareCustomerOtherInfo(params) {
        let custInfo = {
            customerName: _.get(params, 'customerName', null),
            billNumber: _.get(params, 'billNumber', null),
            billPeriod: _.get(params, 'billPeriod', null),
            additionalFees: _.get(params, 'additionalFees', null),
            refId: _.get(params, 'refId', null)
        }
        return JSON.stringify(custInfo);
    }

    createRecordForAnalytics(record, userType = null) {
        let recordForAnalytics = {};
        recordForAnalytics.source = "UPMS";
        recordForAnalytics.source_subtype_2 = "FULL_BILL";
        recordForAnalytics.user_type = userType;
        recordForAnalytics.customer_id = _.get(record, 'customerId', null);
        recordForAnalytics.service = _.get(record, 'service', null);
        recordForAnalytics.recharge_number = _.get(record, 'rechargeNumber', null);
        recordForAnalytics.operator = _.get(record, 'operator', null);
        recordForAnalytics.due_amount = _.get(record, 'currentBillAmount', null);
        recordForAnalytics.paytype = _.get(record, 'paytype', null);
        recordForAnalytics.updated_at = _.get(record, 'updatedAt', null);
        return recordForAnalytics;
    }

    /*
    *   Function will update data in Bills Tables on the basis of customer_id,recharge_number,operator,service
    */
    async updateBillV2(cb, params) {
        let self = this,
            tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.productId], null) || _.get(this.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);
        self.L.log(`updateBillV2:: tableName ${tableName}`);
        _.set(params, 'tableName', tableName);
        params.amount = _.get(params, 'currentBillAmount', null)
        params.customerOtherInfo = _.get(params, 'customerOtherInfo', null)
        if (tableName) {
            try {
                ASYNC.waterfall([
                    (next) => {
                        let startTime = new Date().getTime();
                        self.billsLib.processRecordForDuplicateCANUmber(params, (error) => {
                            if (error) {
                                self.L.error(`updateBillV2:: processRecordForDuplicateCANUmber`, `error while processing for: ${params.debugKey} with error ${error}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPMS_BILL_UPDATE', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'STATUS:ERROR', 'TYPE:DUPLICATE_CAN']);
                            }
                            else {
                                self.L.log(`updateBillV2:: processRecordForDuplicateCANUmber`, `processed successfully for: ${params.debugKey}`);
                            }
                            utility._sendLatencyToDD(startTime, {
                                'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'PROCESS_DUPLICATE_CAN'
                            });
                            return next();
                        });
                    },
                    (next) => {
                        let startTime = new Date().getTime();
                        self.billsLib.getForwardActionFlow(async (error, action) => {
                            if (error) {
                                self.L.error(`updateBillV2:: getForwardActionFlow`, `invalid action found for: ${params.debugKey} with error ${error}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPMS_BILL_UPDATE', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'STATUS:ERROR', 'TYPE:NO_ACTION']);
                                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(params, null), 'error while differentiating RU and NON-RU');
                                utility._sendLatencyToDD(startTime, {
                                    'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'GET_ACTION_FLOW_ERROR'
                                });
                                next(error, params);
                            }
                            else {
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPMS_BILL_UPDATE', `SERVICE:${params.service}`, `OPERATOR:${params.operator}`, 'STATUS:SUCCESS', 'TYPE:ACTION_FOUND']);
                                let billsData = self.getBillsData(params);
                                params.billsData = billsData;
                                self.L.log(`updateBillV2:: getForwardActionFlow`, `action: ${action}::for${params.debugKey}`);
                                utility._sendLatencyToDD(startTime, {
                                    'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'GET_ACTION_FLOW'
                                });
                                next(null, action, params);
                            }
                        }, params)
                    },
                    (action, processedRecord, next) => {
                        let startTime = new Date().getTime();
                        let nonPaytmPayload = self.preparePayload.preparePayloadForNonPaytmBillsConsumer(processedRecord);
                        self.publishToKafka.publishToNonPaytmBillsConsumer(async (err) => {
                            if (err) {
                                await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(processedRecord, 'NON-RU'), 'error while publishing to cassandra');
                                self.L.error(`updateBillV2 :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                utility._sendLatencyToDD(startTime, {
                                    'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'PUBLISH_NONPAYTM_ERROR'
                                });
                                next(null, action, processedRecord);
                            } else {
                                utility._sendLatencyToDD(startTime, {
                                    'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'PUBLISH_NONPAYTM'
                                });
                                self.L.log(`updateBillV2 ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                            }
                        }, nonPaytmPayload, 'UPMS', self.nonPaytmPublisher);
                    },
                    (action, processedRecord, next) => {
                        if (action == 'update') {
                            let startTime = new Date().getTime();
                            self.updateDbRecord(async (err) => {
                                if (err) {
                                    self.L.error(`updateBillV2 :: updateDbRecord`, `error while updating for : ${processedRecord.debugKey}, error: ${err}`);
                                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(processedRecord, 'RU'), 'error while updating DB');
                                    utility._sendLatencyToDD(startTime, {
                                        'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'UPDATE_DB_ERROR'
                                    });
                                    next(err, processedRecord);
                                } else {
                                    self.L.log(`updateBillV2:: :: updateDbRecord`, `updated successfully for : ${processedRecord.debugKey}`);
                                    utility._sendLatencyToDD(startTime, {
                                        'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'UPDATE_DB'
                                    });
                                    next(null, processedRecord, action);
                                }
                            }, processedRecord);
                        } else {
                            next(null, processedRecord, action);
                        }
                    },
                    (processedRecord, action, next) => {
                        if (action == 'update') {
                            let startTime = new Date().getTime();
                            self.publishInKafka(async (err) => {
                                if (err) {
                                    self.L.error(`updateBillV2: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                                    utility._sendMetricsToDD(1, [
                                        'REQUEST_TYPE:UPMS_BILL_UPDATE',
                                        `SERVICE:${_.get(params, 'service', null)}`,
                                        'SOURCE:PUBLISH_KAFKA',
                                        'STATUS:ERROR',
                                        'TYPE:' + err]);
                                    await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(processedRecord, 'RU'), 'error while publishing to kafka');
                                    utility._sendLatencyToDD(startTime, {
                                        'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'PUBLISH_KAFKA_ERROR'
                                    });
                                    next(err, processedRecord);
                                }
                                else {
                                    utility._sendLatencyToDD(startTime, {
                                        'REQUEST_TYPE': 'UPMS_BILL_UPDATE', 'SERVICE': params.service, 'OPERATOR': params.operator, 'STATUS': 'PUBLISH_KAFKA'
                                    });
                                    next(null, processedRecord);
                                }
                            }, processedRecord, action);
                        } else next(null, processedRecord);
                    },
                ], function (error, processedRecord) {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:UPMS_BILL_UPDATE',
                            `SERVICE:${_.get(params, 'service', null)}`,
                            'STATUS:PROCESS_RECORD_FAILURE',
                            'SOURCE:POSTPAID_SMS',
                            'TYPE:' + _.get(error, 'message', 'UNKNOWN_ERROR')
                        ]);
                        self.L.error(`updateBillV2 :: processRecords`, `Exception occured Error Msg:: ${error}`);
                    } else {
                        self.L.log(`updateBillV2 :: processRecords`, `Record processed `);
                    }
                    return cb(error);
                });
            } catch (err) {
                self.L.error('processRecord:: ', err);
                return cb({ message: err, errorCode: "2XX" });
            }
        }
        else {
            L.error("updateBillV2", "Operator not migrated::for", params.debugKey);
            utility._sendLatencyToDD(1, [
                'REQUEST_TYPE:UPMS_BILL_UPDATE',
                `SERVICE:${_.get(params, 'service', null)}`,
                'STATUS:OPERATOR_NOT_MIGRATED',
                'TYPE:' + params.operator,
                'SERVICE:' + params.service
            ]);
            return cb('Operator not migrated.');
        }
    }

    updateNotificationStatus(cb, params, tableName) {
        var self = this,
            notificationStatus = _.get(params, 'notificationStatus', null);
        ASYNC.parallel([
            function (cb) {
                self.bills.updateNotificationStatus(function (error, data) {
                    cb(error, data);
                }, tableName, params);
            },
            // function (cb) {
            //     self.users.updateBillReminderFlag(function (error, data) {
            //         cb(error, data);
            //     }, 'users', params, false);
            // }
        ], function done(err, results) {
            return cb(err, results[0]);
        });
    }

    deleteRecord(params, callback) {
        let self = this;
        self.bills.getRecordForDeletetion(params, (err, data) => {
            if (err || !data) {
                return callback(err);
            }

            if (data && data.length == 0) {
                return callback(null, []);
            }

            ASYNC.map(data, (record, cb) => {
            if(params.tableName == "plan_validity"){
                record.paytype = "prepaid";
            }

            try {
                record.extra = JSON.parse(record.extra);
                _.set(record, ['extra', 'referenceId'], _.get(record, 'reference_id', null));
                record.extra = JSON.stringify(record.extra);
            } catch (error) {
                self.L.error(`billSubscriber::deleteRecord :: Error in parsing record.extra for ${record.operator}, recharge_number ${record.recharge_number}, reference_id ${record.reference_id}, cust_id ${record.cust_id}, error ${JSON.stringify(error)}`);
            }

            // delete record from bills table .    
            self.bills.removeRecord(function (error, result) {
                    return cb(error, record);
            }, params.tableName, { id: record.id });
            },function(err,results){
                return callback(err, results);
            });
        });
    }

    updateReasonAsUserInitDeletion(params, callback) {
        let self = this;
        _.set(params, 'reason', 'userInitDeletion');
        _.set(params, 'status', 13);
        _.set(params, 'notificationStatus', 0);
        self.bills.updateReasonAsUserInitDeletion(params, params.tableName, (error, data) => {
            if(error){
                self.L.error(`updateReasonAsUserInitDeletion :: error occurred p- ${error}`);
            }
            callback(error, data);
        });
    }

    /*
        Update bill status to disable
    */
    updateBillStatus(cb, params) {
        var self = this,
            tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', params.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null);
        if (!tableName) {
            L.error("updateBillStatus:", "Operator not migrated for params:", params);
        } else {
            self.bills.updateBillStatus(function (error, data) {
                cb(error, data);
            }, tableName, params);
        }
    }
    updateAmount(cb, tableName, params) {
        var self = this;
        _.set(params, 'amount', 0);
        _.set(params, 'status', _.get(this.config, 'COMMON.bills_status.PAYMENT_DONE', 11));
        self.bills.updateAmount(function (error, data) {
            cb(error, data);
        }, tableName, params);
    }

    sendDataToCallBack(data, callbackURL, callback) {
        let self = this,
            payload = {
                bills: data
            }
        REQUEST({
            url: callbackURL,
            method: 'POST',
            headers: {
                'content-type': 'application/json'
            },
            body: JSON.stringify(payload)
        }, (err, resp, body) => {
            if (err || !body) {
                L.error(`sendDataToCallBack: error occured while sending data to callback`, err);
                return callback(err);
            }
            callback();
        })
    }

    isCreateBillParamsValid(params) {
        if (_.isEmpty(params.operator) || !_.get(params, 'customerId', null)
            || !_.get(params, 'productId', null) || _.isEmpty(params.rechargeNumber)) {
            return false;
        }
        return true;
    }

    _checkRecordForEligibilityToUpdateRecents(data) {
        let
            self = this,
            operator = _.get(data, 'productInfo.operator'),
            recharge_number = _.get(data, 'userData.recharge_number'),
            isEligible = true,
            user_registered_number = _.get(data, 'customerInfo.customer_mobile');

        isEligible = self.remindableUsersLibrary._shouldRemindUser(user_registered_number, recharge_number, operator, self.operatorsWithRegisteredUserRecents);

        return isEligible;
    }

    suspendOperations() {

        let
            self = this;

        self.L.info('billSubscriber :: suspendOperations', 'suspending operations');

        if (self.reminderSubscriber) {
            self.L.info('billSubscriber :: suspendOperations :: rmq', 'stop receiving messages');
            self.reminderSubscriber.cancel();

            /* Delaying the channel close so all message should acknowledged first */
            setTimeout(function () {
                self.L.info('billSubscriber :: suspendOperations :: rmq', 'closing channel and connections');
                self.reminderSubscriber.stop();
            }, 200 * 1000);
        }

    }

    /**
     * 
     * @param {*} tableName which needs to be updated
     * @param {*} params it'll be an object containing required fields for updating
     */

    async markAsPaidForCC(tableName, params){
        return new Promise((resolve,reject)=>{
        try{
            let self=this,
                bankNameDB,cardNetworkDB;
                self.L.log(`markAsPaidForCC`, `getting all bills by cust_id for ${_.get(params, 'customerID', null)}`);
            self.bills.getBillByCustomer(async function (error, records) {
                if (error) {
                    self.L.critical(`markAsPaidForCC`, `get bills by cust_id failed with error for ${_.get(params, 'customerId', null)} and error:${error}`);
                    return resolve({
                        "Message": "Error while fetching cards from cust_id",
                        "changedRows":0
                    });
                } else if (records && _.isArray(records) && records.length > 0) {
                    let lastcc = _.get(params, 'rechargeNumber', '').replace(/\s+/g, '').slice(-4);
                    let last4DigitsMatchingRecords = [];
                    let encryptedCCRecords = [], nonEncryptedCCRecords = [], notCcWhiteListedRecords = [];
                    let encryptedLast4DigitsMatchingRecords = [];


                    for (let index = 0; index < records.length; index++) {
                        let rechargeNumber = records[index].recharge_number.replace(/\s+/g, ''); // removing white space from RN. 1234 XXXX XX12 12 <-- to handle scenarioes like this

                        let productIdDb = _.get(records[index],'product_id',null);
                        productIdDb = self.activePidLib.getActivePID(productIdDb);

                       
                        if (rechargeNumber && rechargeNumber.substr(rechargeNumber.length - lastcc.length) == lastcc && productIdDb == _.get(params,'productID',null)) {
                            let attributes = JSON.parse(_.get(self.config, ['CVR_DATA', productIdDb, 'attributes'] , '{}'))
                            cardNetworkDB = _.get(attributes, ['card_network'], '');
                            bankNameDB = _.toLower(_.get(attributes, ['bank_code'] , '')); 

                            last4DigitsMatchingRecords.push(records[index].id);

                            let service = _.get(self.config,['CVR_DATA',productIdDb,'service'],'').toLowerCase(),
                                payType = _.get(self.config, ['CVR_DATA', productIdDb, 'paytype'], '').toLowerCase();

                            // console.log( _.get(self.config, ['CVR_DATA', productIdDb], ''))
                            if(self.encDecpUtil.isWhitelistedForCC(service, payType, _.get(params, 'customerID', null))){
                                if(records[index].is_encrypted == 1) {
                                    encryptedCCRecords.push(records[index]);
                                    encryptedLast4DigitsMatchingRecords.push(records[index].id);
                                } else {
                                    nonEncryptedCCRecords.push(records[index]);
                                }
                            } else {
                                notCcWhiteListedRecords.push(records[index]);
                            }

                        }
                    }
    
                    if (last4DigitsMatchingRecords.length === 0) {
                        //console.log("empty matching last4digit array");
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:MARK_AS_PAID_CC", 'STATUS:ERROR', `TYPE:NO_RECORD_FOUND_WITH_MATCHING_PID`]);
                        return resolve({
                            "Message": "No cards found",
                            "changedRows":0
                        });
                    } else if (last4DigitsMatchingRecords.length > 0) {
                        //console.log("length of matching last4digit array", last4DigitsMatchingRecords.length);
                        _.set(params, 'amount', 0);
                        _.set(params, 'status', _.get(this.config, 'COMMON.bills_status.MARKED_AS_PAID', 15));
                        _.set(params, 'bankName', bankNameDB);
                        _.set(params,'cardNetwork',cardNetworkDB);
                        try{
                            let result1 = {}, result2 = {}, result3 = {};
                            if(encryptedLast4DigitsMatchingRecords.length > 0) result1 = await self.bills.markAsPaidForCC(tableName, params, encryptedLast4DigitsMatchingRecords, encryptedCCRecords, true);
                            if(nonEncryptedCCRecords.length > 0) result2 = await self.bills.markAsPaidForCC(tableName, params, last4DigitsMatchingRecords, nonEncryptedCCRecords, false);
                            if(notCcWhiteListedRecords.length > 0) result3 = await self.bills.markAsPaidForCC(tableName, params, last4DigitsMatchingRecords, notCcWhiteListedRecords, false, true);
                            let result = {};
                            let keys = Object.keys(result1);
                            if(keys.length == 0 || keys.length < Object.keys(result2)) keys = Object.keys(result2);
                            if(keys.length == 0 || keys.length < Object.keys(result3)) keys = Object.keys(result3);
                            for(let i=0; i<keys.length; i++){
                                if(keys[i] == 'message' || keys[i] == "insertId") continue;
                                result[keys[i]] = _.get(result1, keys[i], 0) + _.get(result2, keys[i], 0) + _.get(result3, keys[i], 0);
                            }
                            return resolve(result);
                        }catch(err){
                            return reject(err);
                        }
                        
                    }
                } else {
                    return resolve({
                        "Message": "No cards found",
                        "changedRows":0
                    });
                }
            }, tableName, _.get(params, 'customerID', null));
        }catch (error){
            throw(error);
        }
    })

    }
    async markAsPaid(tableName, params) {
        try {
            let self = this;
            _.set(params, 'amount', 0);
            _.set(params, 'status', _.get(this.config, 'COMMON.bills_status.MARKED_AS_PAID', 15));
            if (self.lowBalancePrepaidElectricityAllowedOperators.includes(params.operator)) {
                const prepaidTableName = tableName + '_prepaid';
                const result1 = await self.bills.markAsPaid(prepaidTableName, params)
                if (result1.affectedRows != 0)//if record found in prepaid table then return result1
                    return result1;
                else {
                    const result = await self.bills.markAsPaid(tableName, params);
                    return result;
                }
            }
            else {
                const result = await self.bills.markAsPaid(tableName, params);
                return result;
            }
        } catch (error) {
            throw(error);
        }
    }

    startDummyLogs() {
        let self = this;

        let dummyLogs = setInterval(function () {
            self.L.log('startDummyLogs', 'dummy logs...');
        }, 30000);
    }

    getDateCategorization(date) {
        if(!date) {
            return {
              value : null,
              isDateFmtValid : true,
              type: "INVALID_DATE"
            }
        }
    
        if(typeof date !== 'string') {
          L.error("getDateCategorization:: invalid value received for date", date);
          return {
             value: null,
             isDateFmtValid: false,
             type: "INVALID_DATE"
          };
        }
    
        let formattedDate,
            //Fmt:  DD-MM-YYYY
            regex1 = new RegExp(/([0][1-9]|[1-2][0-9]|[3][0-1])-([0][1-9]|[1][0-2])-[2-9][0-9][0-9][0-9]/g),
            //Fmt:  YYYY-MM-DD
            regex2 = new RegExp(/[2-9][0-9][0-9][0-9]-([0][1-9]|[1][0-2])-([0][1-9]|[1-2][0-9]|[3][0-1])/g),
            //fmt: MMM D, YYYY
            regex3 = new RegExp(/(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ([0][1-9]|[1-9]|[1-2][0-9]|[3][0-1]), ([2-9][0-9][0-9][0-9])/g);

        let type;
       
        if(date.match(regex1) !== null) {
           formattedDate = MOMENT(date,'DD-MM-YYYY');    
        } else if(date.match(regex2) !== null) {
           formattedDate = MOMENT(date,'YYYY-MM-DD');
        } else if(date.match(regex3) !== null) {
          formattedDate = MOMENT(date,'MMM D, YYYY', true);
        } else {
                //Fmt:  DD-MM-YYYY
                regex1 = new RegExp(/([0-9][0-9])-([0-9][0-9])-[0-9][0-9][0-9][0-9]/g);
                //Fmt:  YYYY-MM-DD
                regex2 = new RegExp(/[0-9][0-9][0-9][0-9]-([0-9][0-9])-([0-9][0-9])/g);
                //fmt: MMM D, YYYY
                regex3 = new RegExp(/(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ([0-9][0-9]|[1-9]|[0-9][0-9]), ([0-9][0-9][0-9][0-9])/g);

                type = "DATE_MONTH_OUT_OF_BOUND";
                if(date.match(regex1) !== null) {
                    formattedDate = MOMENT(date,'DD-MM-YYYY');    
                } else if(date.match(regex2) !== null) {
                    formattedDate = MOMENT(date,'YYYY-MM-DD');
                } else if(date.match(regex3) !== null) {
                formattedDate = MOMENT(date,'MMM D, YYYY', true);
                } else {
                    formattedDate = null;
                    type = "INVALID_DATE_FORMAT";
                }

                // can be configured from DB
                if(formattedDate && formattedDate.isValid() && (Math.abs(MOMENT().format('YYYY') - formattedDate.format('YYYY')) > 1)) {
                return {
                    value : formattedDate,
                    isDateFmtValid : false,
                    type: "IN-ACTIONABLE_DATE"
                };
                } else {
                    return {
                        value : null,
                        isDateFmtValid : false,
                        type: type 
                    };
                }        
        }

        if(formattedDate && formattedDate.isValid()) {
            let currentYear = MOMENT().format('YYYY');
            let formattedDateYear = formattedDate.format('YYYY');

            // can be configured from DB
            type = (Math.abs(currentYear - formattedDateYear) <= 1) ? "VALID_DATE" : "IN-ACTIONABLE_DATE";
            
           return {
             value : formattedDate,
             isDateFmtValid : true,
             type: type
           };

        } else {
           return {
             value : null,
             isDateFmtValid : false,
             type: "INVALID_DATE_FORMAT"
           };
        }
    }

    sendDateForAnalytics(options){

        let self = this,
            operator = options.operator,
            productId = options.productId,
            billDueDate = options.billDueDate,
            billDate = options.billDate,
            parsedDueDateObj = options.parsedDueDateObj,
            parsedBillDateObj = options.parsedBillDateObj,
            rechargeNumber = options.rechargeNumber;

        let dueDate_categorization, billDate_categorization;

        
        dueDate_categorization = self.getDateCategorization(billDueDate);
        billDate_categorization = self.getDateCategorization(billDate);

        let metricKeyValues = [
            'REQUEST_TYPE:DATE_CATEGORIZATION',
            `OPERATOR:${operator}`,
            `PRODUCT_ID:${productId}`,

            `BILL_DUE_DATE:${billDueDate}`,
            `BILL_DATE:${billDate}`,
            `BILL_DUE_DATE_AFTER_FORMATING:${parsedDueDateObj.value}`,
            `BILL_DATE_AFTER_FORMATING:${parsedBillDateObj.value}`,

            `IS_BILL_DUE_DATE_FORMAT_VALID_ACC_TO_MOMENT:${parsedDueDateObj.isDateFmtValid}`,
            `IS_BILL_DATE_FORMAT_VALID_ACC_TO_MOMENT:${parsedBillDateObj.isDateFmtValid}`,

            `BILL_DUE_DATE_TYPE:${dueDate_categorization.type}`,
            `BILL_DATE_TYPE:${billDate_categorization.type}`

        ]
        utility._sendMetricsToDD(1, metricKeyValues);

        // L.log('billSubscriber::sendDateForAnalytics ' + ' Recharge Number: ' + rechargeNumber + '_' + metricKeyValues.join("_") );
    }

    getRecordsFromDbAndUpdateDate(cb, tableName, record) {
        let self = this;
        self.bills.getBillsOfSameRech(function (err, data) {
            if (err) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLS_SUBSCRIBER_GETRECORDS", 'STATUS:ERROR', "OPERATOR:" + record.operator]);
                L.critical('getRecordsFromDb :: ', `Error in fetching bills for same recharge number ${err}`);
            } else {
                if (data && _.isArray(data) && data.length > 0) {
                    if (data.length > 0) {
                        let maxPaymentDate = data.reduce((prev, current) => {
                            if (!current.payment_date || !MOMENT(current.payment_date).isValid()) {
                                return prev;
                            }
                            if (!prev.payment_date || !MOMENT(prev.payment_date).isValid()) {
                                return current;
                            }
                            return MOMENT(prev.payment_date).isSameOrAfter(MOMENT(current.payment_date)) ? prev : current;
                        }, {});
                        

                        let maxBillDate = data.reduce((prev, current) => {
                            if (!current.bill_date || !MOMENT(current.bill_date).isValid()) {
                                return prev;
                            }
                            if (!prev.bill_date || !MOMENT(prev.bill_date).isValid()) {
                                return current;
                            }
                            return MOMENT(prev.bill_date).isSameOrAfter(MOMENT(current.bill_date)) ? prev : current;
                        }, {});
                        let lastPaymentDate = _.get(maxPaymentDate, 'payment_date', null);
                        if (lastPaymentDate != null) {
                            _.set(record, 'lastPaymentDate', lastPaymentDate);
                        }
                        let lastBillDate = _.get(maxBillDate, 'bill_date', null);
                        if (lastBillDate != null) {
                            _.set(record, 'lastBillDate', lastBillDate);
                        }
                    } 
                }
            }
            cb(err, data);
        }, tableName, record);
    }
    
    isDisableBillFetchValid(service, operator, errorMessageCode, extraDetails) {
        let self = this,
        errCodeCountMap = {};
        if( _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', 'DISABLE_BILL_FETCH_ENABLED', 'ENABLE'], 0) != 0){
            if (operator && _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', operator, 'ERROR_CODE_COUNT_MAPPING'], _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', service, 'ERROR_CODE_COUNT_MAPPING'], null)) != null){
                errCodeCountMap = _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', operator, 'ERROR_CODE_COUNT_MAPPING'], _.get(self.config, ['DYNAMIC_CONFIG', 'DISABLE_BILL_FETCH_FOR_ERROR', service, 'ERROR_CODE_COUNT_MAPPING'], {}));
            }
            if((errorMessageCode).toUpperCase() in errCodeCountMap){
                let errCount = parseInt(errCodeCountMap[(errorMessageCode).toUpperCase()]);
                if(errCount > 0){
                    let errCounts = _.get(extraDetails, 'errorCounters', {});
                    if(_.get(errCounts, (errorMessageCode).toUpperCase()+'Count', null)){
                        errCounts[(errorMessageCode).toUpperCase()+'Count'] = errCounts[(errorMessageCode).toUpperCase()+'Count'] + 1;
                    } else {
                        errCounts[(errorMessageCode).toUpperCase()+'Count'] = 1;
                    }
                    errCounts[(errorMessageCode).toUpperCase()+'CountUpdatedAt'] = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                    _.set(extraDetails, 'errorCounters', errCounts)
                    L.info('_calcNextBillFetchDate:: ', errorMessageCode, ' error message code, increment counter for operator::', operator, ' service::', service);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NOT_IN_USE`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`,`REASON:DISABLE_BILLFETCH_COUNTER_INC`]);
                    if(_.get(errCounts, (errorMessageCode).toUpperCase()+'Count', 0) >= errCount){
                        L.info('_calcNextBillFetchDate:: ', errorMessageCode, ' error message code, disabling bill_fetch for operator::', operator, ' service::', service);
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:PUBLISHER',`STATUS:NOT_IN_USE`, `TYPE:STATUS_UPDATE`, `OPERATOR:${operator}`,`REASON:DISABLE_BILLFETCH`]);
                        return true;
                    }
                } else {
                    if(_.get(extraDetails, 'errorCounters', null)){
                        extraDetails.errorCounters = {};
                    }
                }
            } else {
                if(_.get(extraDetails, 'errorCounters', null)){
                    extraDetails.errorCounters = {};
                }
            }

        }
        return false;
    }

    getExtra(record) {
        let self = this;
        let extra = _.get(record[0], 'extra', null);
        
        if (!extra || extra == 'null') {
            return {};
        }
      
        if (typeof extra == 'string') {
            try {
                extra = JSON.parse(extra);
            } catch (error) {
                self.L.error("getExtra :: Failed to parse 'extra' as JSON:", error);
                extra = {};
            }
        }
        return extra;
    }

  toBeInsertedInPrepaidTable(params){
    return _.get(params, 'isValidPrepaidElectricityOperator', false) === true && !(_.isEmpty(_.get(params, 'prepaidTableName')));
  }

}


export default BillSubscriber
