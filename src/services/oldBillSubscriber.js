import VALIDATOR from 'validator'
import <PERSON>OMENT    from 'moment'
import _         from 'lodash'
import OLDBILLS     from '../models/oldBills'

let L = null;

/*
   Old Bill reminder service for distrbuting the request to the service files.

 */
class OldBillSubscriber {
   
    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.oldBills = new OLDBILLS(options);
    }

    /*
  	*	Function will get data on the basis of customer_id,
  	*	product_id and recharge_number
    */
   	getBill(cb, operator, customerId, productId, rechargeNumber ) {
      let self=this;

   		self.oldBills.getBill(function(error, data){
        if(error) {
        	return cb(error);		
        } else {
        	if(data.length === 0 ) {
            return cb('No Data found!!!')  
          } else {
            return cb(null,data[0]);
          }		
        }

      },operator, customerId,productId,rechargeNumber)
        	
    }

    /*
    * Function will get multiple bills data on the basis of
    * customer_id, product_id and recharge_number
    */
    getMultipleBill(cb, customerIds, productIds, rechargeNumbers) {
      let self=this;

      self.oldBills.getMultipleBill(function(error, data){
        if(error) {
          return cb(error);
        } else {
          if(data.length === 0 ) {
            return cb('No Data found!!!')
          } else {
            return cb(null,data);
          }
        }

      }, customerIds, productIds, rechargeNumbers)

    }


   	/*
	   *	Function will create data in Bills Tables on the basis of customer_id,
	   *	product_id and recharge_number as Key
    */
   	createBill(cb, params) {

   		this.oldBills.createBill(function(error, data){
        cb(error,data);  	
      },params);

   	}

   	/*
	*	Function will update data in Bills Tables on the basis of customer_id,
	*	product_id and recharge_number as Key
    */
   	updateBill(cb, params) {

   		this.oldBills.updateBill(function(error, data){
          cb(error,data);
      },params);

   	}


}

export default OldBillSubscriber