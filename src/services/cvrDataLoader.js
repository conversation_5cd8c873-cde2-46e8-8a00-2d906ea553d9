import _         from 'lodash'
import L         from 'lgr'
import Q           from 'q'
import CA<PERSON>LOGVERTIC<PERSON><PERSON>CHARGE from '../models/catalogVerticalRecharge'
import MOMENT from 'moment'

class CVRDataLoader {

    constructor (options) {
        this.config = options.config;
        this.L = options.L || L;
        this.catalogVerticalRecharge   = new CATALOGVERTICALRECHARGE(options);
    }

    start() {
        let 
            self = this,
            deferred = Q.defer();
            
        self.L.info("Going to load cvr data");
        self.loadDataFromCVR(function(error,data){   
            deferred.resolve();
        });

        setInterval(() => {
            self.L.info("Going to reload cvr data");
            self.loadDataFromCVR();
        }, _.get(self.config, ['COMMON', 'CVR_REFRESH_INTERVAL'], 30 * 60 * 1000));

        return deferred.promise;

    }

    loadDataFromCVR(callback){

        let 
            self    = this,
            cvrData = _.get(self.config, 'CVR_DATA'),
            conditions = null;

        if (cvrData) {
            conditions = `updated_at > "${MOMENT().subtract(1, 'hour').subtract(40, 'minute').format("YYYY-MM-DD HH:mm:ss")}"`;
        } else {
            cvrData = {}
        }   

        self.catalogVerticalRecharge.getSpecificDataFromCvr(function (error, data) {
            try {
                if (error) {
                    self.L.critical("Uable to load cvr data", error);
                }
                else {
                    data.forEach(function (row) {
                        cvrData[row.product_id] = row;
                    });

                    if (!conditions) {
                        _.set(self.config, 'CVR_DATA', cvrData);
                    }

                    self.L.info("CVR data loaded successfully");
                }

            } catch (exception) {
                self.L.critical(exception);
            }
            if (callback) {
                callback();
            }
        }, conditions);
    }


}

export default CVRDataLoader;