import ASYNC from 'async';
import _ from 'lodash';
import OS from 'os';
import BILLS from '../models/bills';
import utility from '../lib';
import cassandraBills from '../models/cassandraBills';
import { Kafka, CompressionTypes, CompressionCodecs } from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper'
import Logger from '../lib/logger';

CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;


class HistoricalRecords {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.stopService = false;
        this.dbInstance = options.dbInstance;
        this.infraUtils = options.INFRAUTILS;
        this.bills = new BILLS(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.encryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.greyScaleEnv = options.greyScaleEnv;
        this.excludedTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'TABLES', 'EXCLUDE'], null);
        this.includedTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'TABLES', 'INCLUDE'], '*');
        this.operatorTablesList = [...new Set(Object.values(_.get(this.config, ['OPERATOR_TABLE_REGISTRY'])))];
        // Extra tables from dynamic config
        this.extraTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'TABLES', 'EXTRA'], []);
        if (this.extraTablesList.length > 0) {
            this.extraTables = this.extraTablesList.split(',').map((e) => e.trim());
            // Push the extra tables from dynamic config into operatorTableList
            this.operatorTablesList = [...new Set(this.operatorTablesList.concat(this.extraTables))];
        }
        this.statusList = [...new Set(Object.values(_.get(this.config, ['COMMON', 'bills_status'])))];
        this.excludedTables = this.excludedTablesList ? this.excludedTablesList.split(',').map((e) => e.trim()) : [];
        this.includedTables = this.includedTablesList === '*' ? this.operatorTablesList : this.includedTablesList.split(',').map((e) => e.trim()).concat(this.extraTables);
        this.chunkSize = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'CHUNK', 'SIZE'], 10);
        this.cassandraBills = new cassandraBills(options);
        this.logger = new Logger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

        setInterval(() => {
            this.L.log('HistoricalRecords :: Consumer is running..!!');
        }, 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: historicalRecords", "Re-initializing variable after interval");
        self.excludedTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'TABLES', 'EXCLUDE'], null);
        self.includedTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'TABLES', 'INCLUDE'], '*');
        self.operatorTablesList = [...new Set(Object.values(_.get(this.config, ['OPERATOR_TABLE_REGISTRY'])))];
        self.extraTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'HISTORICAL_RECORDS', 'TABLES', 'EXTRA'], []);
        if (self.extraTablesList.length > 0) {
            self.extraTables = self.extraTablesList.split(',').map((e) => e.trim());
            // Push the extra tables from dynamic config into operatorTableList
            self.operatorTablesList = [...new Set(self.operatorTablesList.concat(self.extraTables))];
        }
        self.excludedTables = self.excludedTablesList ? self.excludedTablesList.split(',').map((e) => e.trim()) : [];
        self.includedTables = self.includedTablesList === '*' ? self.operatorTablesList : self.includedTablesList.split(',').map((e) => e.trim()).concat(self.extraTables);
    }

    start() {
        let self = this;

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('start :: historicalRecords', 'unable to configure kafka', error);
                process.exit(0);
            } else {
                self.L.log('start :: historicalRecords', 'Kafka Confugured successfully !!');
            }
        });
    }

    configureKafka(done) {
        let self = this;
        try {
            self.Kafka = new Kafka({
                "brokers": _.get(self.config.KAFKA, 'TOPICS.REMINDER_MAXWELL.HOSTS').split(','),
                "clientId": 'remiderMaxwellConsumer_' + OS.hostname() + '_' + process.pid,
            });
            self.consumer = self.Kafka.consumer({
                groupId: 'remiderMaxwell-consumer',
                sessionTimeout:120000
            });
            self.L.log("historicalRecords :: initializeKafkaConsumer", "Connecting to kafka consumer");
            
            self.consumer.connect()
            .then(() => {
                let topic = _.get(self.config.KAFKA, 'SERVICES.REMINDER_MAXWELL.TOPIC');
                return self.consumer.subscribe({
                    "topic": topic,
                    "fromBeginning": true
                })
            })
            .then(() => {
                this.consumer.run({
                    eachBatchAutoResolve: false,
                    autoCommitThreshold: 1,
                    eachBatch: async ({
                        batch,
                        resolveOffset,
                        heartbeat,
                        commitOffsetsIfNecessary                
                    }) => {
                        let consumerHeartbeat = setInterval(async function() {
                            try{
                                await heartbeat();
                                self.L.log('historicalRecords :: Consumer sent heartbeat...');
                            }catch(err){
                                utility._sendMetricsToDD(1 , ["REQUEST_TYPE:HISTORICAL_RECORDS","TYPE:ERROR_WHILE_HB","STATUS:CONSUMER_ERROR"])
                                self.L.error('historicalRecords :: Error while sending heartbeat:',err);
                            }
                        }, 2000);
                        try {
                            await new Promise((resolve, reject) => {
                                self.execSteps(batch.messages, resolveOffset, batch.topic, batch.partition, resolve);
                            });

                            await commitOffsetsIfNecessary();
                        } catch (err) {
                            self.L.error('historicalRecords :: processNotifications', 'Error occured while processing data : ', err);
                            utility._sendMetricsToDD(1 , ["REQUEST_TYPE:HISTORICAL_RECORDS","TYPE:ERROR_WHILE_PROCESSING","STATUS:CONSUMER_ERROR"])
                        }
                        clearInterval(consumerHeartbeat);
                        self.L.log('historicalRecords :: setInterval(consumerHeartbeat) cleared...');
                    },
                })
            }).catch(err => {
                self.L.error('historicalRecords :: initializeKafkaConsumer', 'Error occured while consuming data : ', err);
                done(err);
            });
        } catch (error) {
            self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            return done(error);
        }
    }

    execSteps(records, resolveOffset, topic, partition, cb) {
        let self = this,
            chunkSize = self.chunkSize,
            startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.consumer.pause([{topic, partitions:[partition]}]);
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        // // Check for duplicate messages consumed by the consumer
        // let currentBatchLastOffset = _.get(lastMessage, 'offset', null);
        // if (currentBatchLastOffset !== null && self.partitionLastOffsetTracker[partition] && Number(currentBatchLastOffset) < Number(self.partitionLastOffsetTracker[partition])) {
        //     self.L.log("execSteps :: ", `Duplicate message received for partition:${partition} and offset:${currentBatchLastOffset}`);
        //     utility._sendMetricsToDD(1, [
        //         'STATUS:DUPLICATED_OFFSET',
        //         'TOPIC:' + _.get(lastMessage, 'topic'),
        //         'REQUEST_TYPE:STORE_HISTORICAL_RECORDS'
        //     ]);
        // }
        // self.partitionLastOffsetTracker[partition] = currentBatchLastOffset;
        // self.L.log("partitionLastOffsetTracker :: ", self.partitionLastOffsetTracker);

        self.L.log('execSteps:: ', `Processing ${records.length} Bill Fetch data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:STORE_HISTORICAL_RECORDS', 'STATUS:CONSUMED']);
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 10);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("historicalRecords", records,topic , partition);

                // commit message offset
                resolveOffset(lastMessage.offset);
                self.L.log('execSteps :: ', 'Commit success for offset:', _.get(lastMessage, 'offset') + ', topic: ' + topic + ', partition: ' + partition + ', timestamp: ' + _.get(lastMessage, 'timestamp'));

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:storeHistoricalRecords", "TIME_TAKEN:" + executionTime]);
                if(self.stopService) {
                    self.L.log("Gracefull shutdown started, not resuming consumer");
                    return;
                }
                // Resume consumer now
                if(self.greyScaleEnv) {
                    setTimeout(function(){
                        self.consumer.resume([{topic, partitions:[partition]}]);
                        cb();
                    },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'HISTORICAL_RECORDS', 'BATCH_DELAY'],10));
                } else {
                    self.consumer.resume([{topic, partitions:[partition]}]);
                    cb();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.storeHistoricalRecords(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    storeHistoricalRecords(done, data) {
        let self = this;
        ASYNC.parallel([
            (callback) => {
                self.processPayloadForCustIdRnMappingIngestion(data, callback);
            },
            (callback) => {
                self.processPayloadForStoringHistoricalRecords(data, callback);
            }
        ], (err) => {
            if (err) {
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:STORE_HISTORICAL_RECORDS_PARALLEL_PROCESSING`,
                    `STATUS:ERROR`
                ]);
                self.logger.error(`storeHistoricalRecords:: err ${err} occurred for record`, data, data.service);
            }
            done();
        });
    }

    processPayloadForCustIdRnMappingIngestion(data, cb) {
        let self = this;
        self.validatePayloadForCustIdRnMappingIngestion(data, (err, record) => {
            if (err) {
                self.L.error('historicalRecords::', `validation failed for Cust ID RN ingestion`, err);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_INGESTION`,
                    `STATUS:VALIDATION_FAILED`,
                ]);
                return cb();
            }
            self.cassandraBills.dumpIntoCustIdRnMappingTable((err) => {
                if (err) {
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:CUSTID_RN_MAPPING_INGESTION`,
                        `STATUS:DB_ERROR`
                    ]);
                    self.L.error('historicalRecords::storeHistoricalRecords::', `err while creating entry in cust_id_rn_mapping table for record ${record.debug_key}`, err);
                    return cb();
                }
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:CUSTID_RN_MAPPING_INGESTION`,
                    `STATUS:SUCCESS`
                ]);
                self.L.log('historicalRecords::storeHistoricalRecords::', `succesfully created entry in cust_id_rn_mapping table for record ${record.debug_key}`);
                return cb();
            }, record);
        });
    }

    processPayloadForStoringHistoricalRecords(data, cb) {
        let self = this;
        try {
            const record = self.validatePayload(data);
            if (!record) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_HISTORICAL_RECORDS', 'STATUS:SKIP']);
                return cb();
            }
            self.L.log('storeHistoricalRecords :: historicalRecords', 'Insert Into bills_history', `Recharge Number - ${self.logger.containsXXXX(record.recharge_number) ? this.encryptionDecryptioinHelper.encryptData(record.recharge_number) : record.recharge_number}`, `Customer ID - ${record.customer_id}`, `Operator - ${record.operator}`);
            /**
            * Inserting data into bills_history table
            */
            self.bills.insertHistoricalRecords((error, data) => {
                if (error) {
                    self.logger.error('storeHistoricalRecords :: historicalRecords Unable to insert historical data', record, record.service);
                }
            }, record);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_HISTORICAL_RECORDS', 'STATUS:INSERT']);
            return cb();
        } catch (error) {
            self.L.error('storeHistoricalRecords :: historicalRecords', error.message);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_HISTORICAL_RECORDS', 'STATUS:ERROR']);
            return cb();
        }
    }

    validatePayloadForCustIdRnMappingIngestion(kafkaPayload, cb) {
        let self = this;
        try {
            let kafkaPayloadData = JSON.parse(_.get(kafkaPayload, 'value', null));
            /**
             * if event is insert - create entry in cust_id_rn_mapping table with 'ACTIVE' status
             * if event is delete - create entry in cust_id_rn_mapping table with 'DELETED' status
             */
            const allowedActions = ['insert', 'delete'];
            if (!kafkaPayloadData) {
                return cb('No data found in the payload', null);
            }
            let action = _.get(kafkaPayloadData, 'type', null);
            let tableName = _.get(kafkaPayloadData, 'table', null);
            let customer_id = _.get(kafkaPayloadData, 'data.customer_id', null);
            var service = _.get(kafkaPayloadData, 'data.service', null);
            let recharge_number = _.get(kafkaPayloadData, 'data.recharge_number', null);
            let operator = _.get(kafkaPayloadData, 'data.operator', null);
            let is_encrypted = _.get(kafkaPayloadData, 'data.is_encrypted', false);
            let product_id = _.get(kafkaPayloadData, 'data.product_id', null);

            if (!allowedActions.includes(action)) {
                return cb('update event found, skipping ingestion', null);
            }
            self.logger.log(`validatePayloadForCustIdRnMappingIngestion:: kafka payload received is`, kafkaPayloadData, service);
            if (!tableName || !self.operatorTablesList.includes(tableName)) {
                return cb('table not found in kafka record or doesnt exist in operator table registry', null);
            }
            if (_.isNull(customer_id)) {
                self.L.error('validatePayloadForCustIdRnMappingIngestion::', `validation failed - customer_id not found in kafka payload for record ${JSON.stringify(kafkaPayloadData)}`);
                return cb('customer_id not found in kafka payload', null);
            }
            if (_.isNull(service)) {
                self.L.error('validatePayloadForCustIdRnMappingIngestion::', `validation failed - service not found in kafka payload for record ${JSON.stringify(kafkaPayloadData)}`);
                return cb('service not found in kafka payload', null);
            }
            if (_.isNull(recharge_number)) {
                self.L.error('validatePayloadForCustIdRnMappingIngestion::', `validation failed - recharge_number not found in kafka payload for record ${JSON.stringify(kafkaPayloadData)}`);
                return cb('recharge_number not found in kafka payload', null);
            }
            if (_.isNull(operator)) {
                self.L.error('validatePayloadForCustIdRnMappingIngestion::', `validation failed - operator not found in kafka payload for record ${JSON.stringify(kafkaPayloadData)}`);
                return cb('operator not found in kafka payload', null);
            }

            let meta = {
                user_type: "RU",
                deleted_bills_status: action === 'insert' ? null : _.get(kafkaPayloadData, 'data.status', null),
                deleted_bills_notification_status: action === 'insert' ? null : _.get(kafkaPayloadData, 'data.notification_status', null)
            }

            let record = {
                customer_id: customer_id,
                service: service,
                recharge_number: recharge_number,
                operator: operator,
                paytype: _.get(kafkaPayloadData, 'data.paytype', null),
                product_id: _.get(kafkaPayloadData, 'data.product_id', null),
                status: action === 'insert' ? 'ACTIVE' : 'DELETED',
                meta: JSON.stringify(meta),
                is_encrypted: is_encrypted,
                debug_key: `${customer_id}_${service}_${self.logger.containsXXXX(recharge_number) ? this.encryptionDecryptioinHelper.encryptData(recharge_number) : recharge_number}_${operator}_${_.get(kafkaPayloadData, 'data.paytype', null)}_${_.get(kafkaPayloadData, 'data.product_id', null)}`
            }
            return cb(null, record);
        } catch (error) {
            self.logger.error('validatePayloadForCustIdRnMappingIngestion:: error occurred for', kafkaPayload, service);
            return cb('error occurred during parsing payload', null);
        }        
    }

    validatePayload(kafkaPayload) {
        let self = this;
        try {
            const record = self.convertKafkaPayloadToRecord(kafkaPayload);
            return record;
        } catch(error) {
            self.L.error('validatePayload :: historicalRecords', error.message);
            throw new Error(error);
        }
    }

    convertKafkaPayloadToRecord(kafkaPayload) {
        let self = this,
            kafkaPayloadData, data, action;
        try {
            kafkaPayloadData = JSON.parse(_.get(kafkaPayload, 'value', null));
            if (!kafkaPayloadData) {
                throw Error('No data found in the payload');
            }
            action = _.get(kafkaPayloadData, 'type', null);
            const tableName = _.get(kafkaPayloadData, 'table', null);
            /**
             * To check if payload falls under allowed actions
             */
            const allowedActions = ['insert', 'update'];
            if (!allowedActions.includes(action)) {
                self.L.log('convertKafkaPayloadToRecord :: historicalRecords', `Skip due to action delete ${tableName}`);
                return null;
            }
            /**
             * To check if status is updated & the new status is 1
             * In case, if the status field is updated and the new status is made 1, then skip the insertion to bills_history
             */
            if (
                kafkaPayloadData.old
                && kafkaPayloadData.old.status
                && parseInt(kafkaPayloadData.data.status) === 1
            ) {
                self.L.log('convertKafkaPayloadToRecord :: historicalRecords', `Skip due to status ${tableName}`);
                return null;
            }
            /**
             * To check if tablename exist in payload
             */
            if (!tableName) {
                throw Error('Table not found in kafka payload');
            }
            /**
             * To check if tables exist in operatorTableRegistry file
             */
            if (!self.operatorTablesList.includes(tableName)) {
                throw Error(`Table doesn't exist in operatorTableRegistry ${tableName}`);
            }
            /**
             * To check excluded tables in dynamic config
             */
            if (tableName && self.excludedTables.includes(tableName)) {
                self.L.log('convertKafkaPayloadToRecord :: historicalRecords', `Table excluded in dynamic config ${tableName}`);
                return null;
            }
            /**
             * To check included tables in dynamic config
             */
            if (!self.includedTables.includes(tableName)) {
                self.L.log('convertKafkaPayloadToRecord :: historicalRecords', `Table is not included in dynamic config ${tableName}`);
                return null;
            }
            data = _.get(kafkaPayloadData, 'data', {});
        } catch (error) {
            self.L.error('convertKafkaPayloadToRecord :: historicalRecords', error.message, JSON.stringify(kafkaPayload));
            throw new Error(error);
        }

        return {
            customer_id: _.get(data, 'customer_id', null),
            recharge_number: _.get(data, 'recharge_number', null),
            product_id: _.get(data, 'product_id', null),
            operator: _.get(data, 'operator', null),
            amount: _.get(data, 'amount', null),
            due_date: _.get(data, 'due_date', null),
            bill_date: _.get(data, 'bill_date', null),
            bill_fetch_date: _.get(data, 'bill_fetch_date', null),
            next_bill_fetch_date: _.get(data, 'next_bill_fetch_date', null),
            gateway: _.get(data, 'gateway', null),
            paytype: _.get(data, 'paytype', null),
            service: _.get(data, 'service', null),
            circle: _.get(data, 'circle', null),
            customer_mobile: _.get(data, 'customer_mobile', null),
            customer_email: _.get(data, 'customer_email', null),
            retry_count: _.get(data, 'retry_count', null),
            status: _.get(data, 'status', 0),
            reason: _.get(data, 'reason', null),
            extra: _.get(data, 'extra', null),
            published_date: _.get(data, 'published_date', null),
            user_data: _.get(data, 'user_data', null),
            notification_status: _.get(data, 'notification_status', null),
            payment_date: _.get(data, 'payment_date', null),
            service_id: _.get(data, 'service_id', 0),
            customerOtherInfo : _.get(data, 'customerOtherInfo', null),
            is_automatic: _.get(data, 'is_automatic', 0),
            payment_channel: _.get(data, 'payment_channel', null),
            reference_id: _.get(data, 'reference_id', null),
            is_encrypted: _.get(data, 'is_encrypted', null),
            enc_due_date: _.get(data, 'enc_due_date', null),
            enc_amount: _.get(data, 'enc_amount', null)
        };
    }

    suspendOperations(){
        this.stopService = true;
        this.L.info("HistoricalRecords::stopService");
    }
}

export default HistoricalRecords;