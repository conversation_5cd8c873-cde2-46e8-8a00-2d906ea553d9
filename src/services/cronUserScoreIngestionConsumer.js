import _        from 'lodash';
import ASYNC    from 'async';
import MOMENT   from 'moment';
import OS from 'os';

import utility from '../lib';
import KafkaConsumer from '../lib/KafkaConsumer';
import KafkaConsumerChecks  from '../lib/kafkaConsumerChecks';
import cassandraBills       from '../models/cassandraBills'
import UserScoreHelper      from '../lib/userScoreHelper';

const DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";

class CronUserScoreIngestionConsumer {

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.cassandraBills = new cassandraBills(options);
        self.userScoreHelper = new UserScoreHelper(options);
        self.infraUtils = options.INFRAUTILS;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'CRON_USER_SCORE_INGESTION', 'BATCH_DELAY'], 5*60*1000) : 500;
    }

    start() {
        let self = this;
        self.L.log('cronUserScoreIngestionConsumer::', `Starting service`);
        ASYNC.waterfall([
            (next) => {
                self.reminderKafkaProducer = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                self.reminderKafkaProducer.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising reminderKafkaProducer:: ', error);
                    self.L.log("cronUserScoreIngestionConsumer:: reminderKafkaProducer KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            },
            (next) => {
                self.userScoreIngestorFailedRecordsKafkaProducer = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                self.userScoreIngestorFailedRecordsKafkaProducer.initProducer('high', function (error) {
                    if (error)
                        self.L.critical('error in initialising userScoreIngestorFailedRecordsKafkaProducer :: ', error);
                    self.L.log("userScoreIngestorFailedRecordsKafkaProducer :: userScoreIngestorFailedRecordsKafkaProducer KAFKA PRODUCER STARTED....");
                    return next(error);
                });
            }, 
            (next) => {
                try {
                    self.consumer = new KafkaConsumer({
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'), 
                        "groupId": 'cronUserScoreIngestionConsumer',
                        "topics": _.get(self.config.KAFKA, 'SERVICES.CRON_USER_SCORE_INGESTOR.TOPIC'),
                        "id": "cron-user-score-ingestion-consumer-" + OS.hostname(),
                        "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.CRON_USER_SCORE_INGESTION_CONSUMER.BATCHSIZE',1000000),
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.CRON_USER_SCORE_INGESTION_CONSUMER_TIMEOUT',30*60*1000)
                    });
        
                    self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                        if (error)
                            self.L.critical("cronUserScoreIngestionConsumer:: consumer Configured cannot start.", error);
                        else if (!error)
                            self.L.log("cronUserScoreIngestionConsumer:: consumer Configured");
                        return next(error);
                    });
                } catch (error) {
                    return next(error)
                }
            }
        ], (error) => {
            if (error) {
                self.L.critical('cronUserScoreIngestionConsumer :: start', 'Error while starting service...', error);
                process.exit(0)
            } else {
                self.L.log('cronUserScoreIngestionConsumer:: start', 'Service started....');
            }
        })
    }

    _processKafkaData(records, resolveOffset, topic, partition, cb) {
        let startTime = new Date().getTime();
        let self = this;
        let chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'CRON_USER_SCORE_INGESTION_CONSUMER', 'COMMON', 'CHUNKSIZE'], 1);
        let lastMessage,
        rowData = null,
        recordsToProcess = [];
        
        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('cronUserScoreIngestionConsumer::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('cronUserScoreIngestionConsumer::_processKafkaData error while reading kafka');
            return cb();
        }
        
        records.forEach(row => {
            if(row && row.value) {
                try {
                    rowData = JSON.parse(row.value);
                    recordsToProcess.push(rowData);
                } catch (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:CRON_USER_SCORE_INGESTION', 
                        'STATUS:INVALID_JSON_PAYLOAD'
                    ]);
                    self.L.error("cronUserScoreIngestionConsumer::_processKafkaData", "Failed to parse user score ingestion data topic, partition, offset, timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CRON_USER_SCORE_INGESTION', 
                    'STATUS:INVALID_PAYLOAD'
                ]);
                self.L.error("cronUserScoreIngestionConsumer::_processKafkaData", "Unable to get valid data from kafka topic, partition, offset, timestamp ::", row.topic, row.partition, row.offset, row.timestamp);
            }
        });
        
        self.L.log('cronUserScoreIngestionConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} data !!`);
        
        ASYNC.eachLimit(recordsToProcess, chunkSize, self.processUserScoresData.bind(self), async (err) => {
            
            self.kafkaConsumerChecks.findOffsetDuplicates("CronUserScoreIngestion", records,topic , partition);
            
            if (err) {
                self.L.error("cronUserScoreIngestionConsumer::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            } else {
                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                recordsToProcess = [];
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CONSUMER_STATUS', 
                    'STATUS:SUCCESS', 
                    "SOURCE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                    "TIME_TAKEN:" + executionTime
                ]);
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }
        });
    }

    async processUserScoresData(userScoresData, done) {
        let self = this,
        category = userScoresData["category"],
        cust_id = userScoresData["cust_id"],
        paytype = userScoresData["paytype"],
        score = userScoresData["score"];
        self.L.log('processUserScoresData::', `processing ${JSON.stringify(userScoresData)}`);
        if(!cust_id || !category || _.isEmpty(category) || !paytype || _.isEmpty(paytype) || _.isNull(score)) {
            self.L.error("processUserScoresData:: ","error while processing record:",JSON.stringify(userScoresData));
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                'STATUS:ERROR', 
                "TYPE:PROCESSING_USER_SCORES_PAYLOAD", 
                "ERROR:INVALID_PAYLOAD"
            ]);
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: cust_id, service: category, paytype: paytype, score: score},
                errorInfunction: 'processUserScoresData'
            });
            return done();
        }
        if(_.get(self.config, ['DYNAMIC_CONFIG', 'CRON_USER_SCORE_INGESTION', 'CATEGORIES_PAYTYPES_BLOCKED_FROM_PROCESSING',`${category}_${paytype}`], 0) == 1)
        {
            self.L.error("processUserScoresData:: ","category and paytype are blocked for record:",JSON.stringify(userScoresData));
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                'STATUS:ERROR', 
                "TYPE:PROCESSING_USER_SCORES_PAYLOAD", 
                "ERROR:CATEGORY_PAYTYPE_BLOCKED"
            ]);
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: cust_id, service: category, paytype: paytype, score: score},
                errorInfunction: 'processUserScoresData'
            });
            return done();
        }
        await self.processRecord(cust_id, category, paytype, score);
        done();
    }

    async processRecord(custId, category, payType, score) {
        let self = this;
        let {bucket, nbfdHours, thresholdScore, error} = self.userScoreHelper.getUserTier(category, payType, score);
        if(error == "invalid config") {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                'STATUS:ERROR', 
                "ERROR:INVALID_CONFIG", 
                "FUNCTION_NAME:processRecord"
            ]);
            self.L.error('processRecord::', `invalid config found, cust_id:${custId} category:${category}, paytype:${payType}}`);
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: custId, service: category, paytype: payType, score: score},
                errorInfunction: 'processRecord'
            });
            return;
        }
        let bucketForMetrics = bucket == null ? 'NO_BUCKET': bucket;
        utility._sendMetricsToDD(1, [
            "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
            'STATUS:PROCESSING', 
            "FUNCTION_NAME:processCategoryRecords", 
            `CATEGORY:${category}`, 
            `PAYTYPE:${payType}`, 
            `CUSTOMER_BUCKET:${bucketForMetrics}`
        ]);
        await self.executeStepsForProcessing(custId, category, payType, score, bucket);
    }

    async executeStepsForProcessing(cust_id, category, paytype, score, bucket) {
        let self = this,
        updated_at = MOMENT().format(DATE_FORMAT),
        created_at = null,
        dwhPayload = {
            customer_id: cust_id,
            service: category,
            paytype: paytype,
            score: score,	
            bucket: bucket,			
            updated_at: updated_at
        };
        try {
            let params = [cust_id, category, paytype, "dummy_operator", "dummy_recharge_number", score, bucket, updated_at];
            let userScoreTableRecord = await self.cassandraBills.getCustomerScoreDetailsFromCustomerScoringTable(cust_id, category, paytype);
            if(userScoreTableRecord.length == 0) {
                created_at = updated_at;
                _.set(dwhPayload, 'created_at', created_at);
            }
            await self.cassandraBills.addCustomerScoreDetailsInCustomerScoringTable(params, created_at);
            await self.publishToKafkaForDownstreamProcessingAndDwhSync(dwhPayload);
        } catch(error){
            self.L.error('executeStepsForProcessing::', `error while processing record for cust_id:${cust_id}, category:${category}, paytype:${paytype}. ERROR: ${error}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                'STATUS:ERROR', 
                "ERROR:FAILED_TO_ADD_CUST_SCORES_IN_CASSANDRA", 
                "TYPE:EXECUTE_STEPS_FOR_PROCESSING"
            ]);
            await self.publishToInValidRecordsKafka({
                payload: {customer_id: cust_id, service: category, paytype: paytype, score: score, bucket: bucket},
                errorInfunction: 'executeStepsForProcessing'
            });
        }
    }

    publishToKafkaForDownstreamProcessingAndDwhSync(payload) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.reminderKafkaProducer.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:PUBLISH_IN_KAFKA_FOR_DOWNSTREAM_PROCESSING",
                        `TOPIC:${_.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR.TOPIC', '')}`,
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.critical('publishToKafkaForDownstreamProcessingAndDwhSync::', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                    resolve();
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:PUBLISH_IN_KAFKA_FOR_DOWNSTREAM_PROCESSING",
                        `TOPIC:${_.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR.TOPIC', '')}`,
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.log('publishToKafkaForDownstreamProcessingAndDwhSync::', 'Message published successfully in Kafka', ' on topic USER_SCORE_INGESTOR', JSON.stringify(payload));
                    resolve();
                }
            });
        });
    }

    publishToInValidRecordsKafka(payload) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.userScoreIngestorFailedRecordsKafkaProducer.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.USER_SCORE_INGESTOR_FAILED_RECORDS.TOPIC', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:CRON_USER_SCORE_INGESTOR_FAILED_RECORDS_PUBLISHER",
                        "TOPIC:USER_SCORE_INGESTOR_FAILED_RECORDS",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.critical('publishToInValidRecordsKafka :: publishToInValidRecordsKafka', 'Error while publishing message in Kafka on topic USER_SCORE_INGESTOR_FAILED_RECORDS - MSG:- ' + JSON.stringify(payload), error);
                    resolve();
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:CRON_USER_SCORE_INGESTION_CONSUMER", 
                        `SERVICE:${_.get(payload, 'service', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:CRON_USER_SCORE_INGESTOR_FAILED_RECORDS_PUBLISHER",
                        "TOPIC:USER_SCORE_INGESTOR_FAILED_RECORDS",
                        "PAYTYPE:" + `${_.get(payload, 'paytype', null)}`
                    ]);
                    self.L.log('publishToInValidRecordsKafka :: publishToInValidRecordsKafka', 'Message published successfully in Kafka', ' on topic USER_SCORE_INGESTOR_FAILED_RECORDS', JSON.stringify(payload));
                    resolve();
                }
            })
        })
    }
}

export default CronUserScoreIngestionConsumer;