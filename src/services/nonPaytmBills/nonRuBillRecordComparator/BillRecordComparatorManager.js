// src/services/nonPaytmBills/NonPaytmPostProcessor/BillRecordComparator.js

import AnalyticsManager from '../AnalyticsHandler.js';
import UtilityBillRecordComparator from './DefaultBillRecordComparator.js';
import MobilePrepaidBillRecordComparator from './MobilePrepaidBillRecordComparator.js';
import _ from 'lodash';

/**
 * Unified class for bill record comparison and processing
 */
class BillRecordComparator {
    constructor(options) {
        this.nonPaytmBillsModel = options.nonPaytmBillsModel;
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
        this.L = options.L;
        this.analyticsManager = new AnalyticsManager(options);
        this.config = options.config;
        this.strategies = new Map();
        this._initializeProcessors(options);
    }

    /**
     * Initialize all processors
     * @private
     */
    _initializeProcessors(options) {
        const strategies = [
            new MobilePrepaidBillRecordComparator(options),
            new UtilityBillRecordComparator(options)
        ];

        // Register strategies with their service and paytype combinations
        strategies.forEach(strategy => {
            const key = this._createStrategyKey(strategy.getServiceType(), strategy.getPaytype());
            this.strategies.set(key, strategy);

            this.L.log('BillRecordComparator',
                `Registered strategy: ${strategy.constructor.name} for key: ${key}`);
        });
    }

    /**
     * Create a unique key for strategy lookup
     * @private
     */
    _createStrategyKey(serviceType, paytype) {
        return `${serviceType}:${paytype || 'default'}`;
    }

    /**
     * Get the appropriate processor for the service type
     * @private
     */
    _getProcessor(billsKafkaRow) {
        const service = _.get(billsKafkaRow, 'service', '');
        const paytype = _.get(billsKafkaRow, 'paytype', '');
        const key = this._createStrategyKey(service, paytype);
        let strategy = this.strategies.get(key);

        if (!strategy) {
            this.L.log('BillRecordComparator',
                `No specific strategy found for key: ${key} so using utility:default strategy`);
            strategy = this.strategies.get('utility:default');
        }

        return strategy;
    }

    async process(billsKafkaRow, existingRecord) {
        this.L.log('BillRecordComparator::process', `Processing record for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
        try {
            // Get the appropriate processor
            const processor = this._getProcessor(billsKafkaRow);
            this.L.log('BillRecordComparator::process', `Processor found for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return await processor.processAndCompare(billsKafkaRow, existingRecord);
        } catch (error) {
            this.L.error('process',
                `Failed to process record for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}: ${error}`);
            throw error;
        }
    }

    async getTotalRecordsToProcess(billsKafkaRow, existingRecord) {
        try {
            // Get the appropriate processor
            const processor = this._getProcessor(billsKafkaRow);
            return await processor.getTotalRecordsToProcess(billsKafkaRow, existingRecord);
        } catch (error) {
            this.L.error('getTotalRecordsToProcess',
                `Failed to get total records for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}: ${error}`);
            throw error;
        }
    }
}

export default BillRecordComparator;