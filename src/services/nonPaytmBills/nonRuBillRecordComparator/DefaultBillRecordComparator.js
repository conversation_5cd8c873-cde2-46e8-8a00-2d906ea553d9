import Base<PERSON>illRecordComparator from './BaseBillRecordComparator';
import _ from 'lodash';
import MOMENT from 'moment';

const BILL_SOURCES = {
    SYSTEM_FETCH: ['SYSTEM_FETCH', '<PERSON><PERSON><PERSON>_FETCH'],
    UPMS: ['UPMS', 'UPMS_FETCH'],
    SMS_PARSED: ['SMS_PARSED', 'SMS']
};

class UtilityBillRecordComparator extends BaseBillRecordComparator {
    constructor(options) {
        super(options);
        this.nonPaytmBillsModel = options.nonPaytmBillsModel;
        this.L = options.L;
    }

    async process(billsKafkaRow, existingRecord) {
        try {
            this.L.log('UtilityBillRecordComparator',
                `Processing record with utility strategy for service: ${billsKafkaRow.service}`);
            billsKafkaRow = await this.postProcessExistingRecord(billsKafkaRow, existingRecord);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('UtilityBillRecordComparator',
                `Failed to process record: ${error}`);
            throw error;
        }
    }

    /**
     * Post-process existing database record
     * @param {Object} billsKafkaRow - New bill data
     * @param {Object} existingRecord - Existing database record
     * @returns {Promise<Object>} Post-processed record
     * @throws {Error} If post-processing fails
     */
    async postProcessExistingRecord(billsKafkaRow, existingRecord) {
        const self = this;
        try {
            if (!existingRecord) {
                return existingRecord;
            }
            // Prioritize bill source based on due date, amount and source
            billsKafkaRow = await this._prioritizeBillSource(billsKafkaRow, existingRecord);
            return billsKafkaRow;
        } catch (error) {
            self.L.error('postProcessExistingRecord', `Failed to post-process existing record: ${error}`);
            throw error;
        }
    }

    /**
     * Prioritize bill based on source, due date, amount and SMS date conditions
     * @param {Object} billsKafkaRow - New bill data
     * @param {Object} existingRecord - Existing database record
     * @returns {Promise<Object>} Updated bill data
     * @private
     */
    async _prioritizeBillSource(billsKafkaRow, existingRecord) {
        try {
            const currentSource = this._getSource(billsKafkaRow);
            const existingSource = this._getSource(existingRecord[0]);
            const currentDueDate = MOMENT(_.get(billsKafkaRow, 'dueDate', null)).utc().startOf('day');
            const existingDueDate = MOMENT(_.get(existingRecord[0], 'due_date', null)).utc().startOf('day');
            const debugKey = _.get(billsKafkaRow, 'debugKey', null);

            // If no existing source, use current source
            if (!existingSource) {
                return billsKafkaRow;
            }

            // Validate SMS date
            const { smsDate, currentDate } = this._getSmsAndCurrentDates(billsKafkaRow);
            if (smsDate.isBefore(currentDate)) {
                this._logAndThrowError('prioritizeBillSource', `Skipping record as smsDate is before currentDate for debugKey ${debugKey}`, 'smsDate is before currentDate');
            }

            // Handle different source combinations
            if (BILL_SOURCES.SYSTEM_FETCH.includes(existingSource)) {
                return this._handleSystemFetchSource(billsKafkaRow, currentSource, currentDueDate, existingDueDate, smsDate, debugKey);
            } else if (BILL_SOURCES.UPMS.includes(existingSource)) {
                return this._handleUPMSSource(billsKafkaRow, currentSource, currentDueDate, existingDueDate, smsDate, debugKey);
            } else if (BILL_SOURCES.SMS_PARSED.includes(existingSource)) {
                return this._handleSMSParsedSource(billsKafkaRow, currentSource, currentDueDate, existingDueDate, smsDate, debugKey);
            } else {
                this.L.log('prioritizeBillSource', `Proceeding with current source ${currentSource} for debugKey ${debugKey}`);
                return billsKafkaRow;
            }
        } catch (error) {
            this.L.error('prioritizeBillSource', `Failed to prioritize bill source: ${error} for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
            throw error;
        }
    }

    /**
     * Get source from record
     * @private
     */
    _getSource(record) {
        return _.get(record, ['extra', 'updated_data_source'], null) ||
            _.get(record, ['extra', 'bill_source'], null) ||
            _.get(record, 'source', null);
    }

    /**
     * Get SMS and current dates
     * @private
     */
    _getSmsAndCurrentDates(billsKafkaRow) {
        const customerOtherInfo = _.get(billsKafkaRow, 'customerOtherInfo', null);
        const smsDateTime = customerOtherInfo.smsDateTime;
        return {
            smsDate: MOMENT(smsDateTime).utc().startOf('day'),
            currentDate: MOMENT().utc().startOf('day')
        };
    }

    /**
     * Log and throw error
     * @private
     */
    _logAndThrowError(method, message, errorMessage) {
        this.L.log(method, message);
        throw new Error(errorMessage);
    }

    /**
     * Handle system fetch source
     * @private
     */
    _handleSystemFetchSource(billsKafkaRow, currentSource, currentDueDate, existingDueDate, smsDate, debugKey) {
        if (currentDueDate && existingDueDate) {
            if (BILL_SOURCES.SYSTEM_FETCH.includes(currentSource) ||
                BILL_SOURCES.UPMS.includes(currentSource) ||
                BILL_SOURCES.SMS_PARSED.includes(currentSource)) {
                return this._handleDueDateComparison(billsKafkaRow, currentSource, currentDueDate, existingDueDate, debugKey);
            } else
                return billsKafkaRow;
        } else if (existingDueDate) {
            return this._handleSmsDateComparison(billsKafkaRow, currentSource, smsDate, existingDueDate, debugKey);
        }
        return billsKafkaRow;
    }

    /**
     * Handle UPMS source
     * @private
     */
    _handleUPMSSource(billsKafkaRow, currentSource, currentDueDate, existingDueDate, smsDate, debugKey) {
        if (currentDueDate && existingDueDate) {
            if (BILL_SOURCES.SYSTEM_FETCH.includes(currentSource) ||
                BILL_SOURCES.UPMS.includes(currentSource) ||
                BILL_SOURCES.SMS_PARSED.includes(currentSource)) {
                return this._handleDueDateComparison(billsKafkaRow, currentSource, currentDueDate, existingDueDate, debugKey);
            }
            return billsKafkaRow;
        } else if (existingDueDate) {
            return this._handleSmsDateComparison(billsKafkaRow, currentSource, smsDate, existingDueDate, debugKey);
        }
        return billsKafkaRow;
    }

    /**
     * Handle SMS parsed source
     * @private
     */
    _handleSMSParsedSource(billsKafkaRow, currentSource, currentDueDate, existingDueDate, smsDate, debugKey) {
        if (currentDueDate) {
            if (BILL_SOURCES.SYSTEM_FETCH.includes(currentSource) ||
                BILL_SOURCES.UPMS.includes(currentSource)) {
                return billsKafkaRow;
            } else if (BILL_SOURCES.SMS_PARSED.includes(currentSource)) {
                return this._handleDueDateComparison(billsKafkaRow, currentSource, currentDueDate, existingDueDate, debugKey);
            }
            return billsKafkaRow;
        } else if (existingDueDate) {
            return this._handleSmsDateComparison(billsKafkaRow, currentSource, smsDate, existingDueDate, debugKey);
        }
        return billsKafkaRow;
    }

    /**
     * Handle due date comparison
     * @private
     */
    _handleDueDateComparison(billsKafkaRow, currentSource, currentDueDate, existingDueDate, debugKey) {
        if (currentDueDate.isAfter(existingDueDate)) {
            this.L.log('prioritizeBillSource', `Using current source ${currentSource} over ${existingSource} for debugKey ${debugKey}`);
            return billsKafkaRow;
        }
        this._logAndThrowError('prioritizeBillSource',
            `Skipping record as current dueDate is before existing dueDate for debugKey ${debugKey}`,
            'currentDueDate is before existingDueDate');
    }

    /**
     * Handle SMS date comparison
     * @private
     */
    _handleSmsDateComparison(billsKafkaRow, currentSource, smsDate, existingDueDate, debugKey) {
        if (smsDate.isAfter(existingDueDate)) {
            this.L.log('prioritizeBillSource', `Proceeding with current source ${currentSource} for debugKey ${debugKey}`);
            return billsKafkaRow;
        } else
            this._logAndThrowError('prioritizeBillSource',
                `Skipping record as smsDate is before existing dueDate for debugKey ${debugKey}`,
                'smsDate is before existingDueDate');
    }

    getServiceType() {
        return 'utility';
    }

    getPaytype() {
        return 'default';
    }
}

export default UtilityBillRecordComparator;