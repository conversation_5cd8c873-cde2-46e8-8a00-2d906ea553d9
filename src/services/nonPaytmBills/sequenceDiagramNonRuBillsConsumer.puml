@startuml NonPaytmBills Consumer Sequence

participant "NonPaytmBills" as NPB
participant "NonRuBillProcessor" as NRBP
participant "NonRuBillDataValidator" as NRBDV
participant "BillPreprocessingStrategy" as BPS
participant "DatabaseEventBaseStrategy" as DEBS
participant "BillRecordComparatorManager" as BRCM
participant "BaseBillRecordComparator" as BBC
participant "PostDbOperations" as PDO
participant "AnalyticsH<PERSON><PERSON>" as AH
participant "KafkaConsumer" as KC
participant "KafkaProducer" as KP
participant "NonPaytmBillsModel" as NPBM

== Initialization Phase ==

NPB -> NPB: constructor(options)
activate NPB
    NPB -> NPB: initializeOperatorWisePartialToFullConfig()
    NPB -> NPB: setVarFromDynamicConfig()
    NPB -> NPB: initializeProducers()
    NPB -> NRBP: new NonRuBillProcessor(options)
    activate NRBP
        NRBP -> NRBDV: new NonRuBillDataValidator(options)
        activate NRBDV
        deactivate NRBDV
        
        NRBP -> NRBP: _initializePreprocessingStrategies(options)
        note right: Initialize preprocessing strategies directly:\n- MobilePostpaidStrategy\n- MobilePrepaidStrategy\n- ElectricityStrategy\n- CreditCardStrategy\n- DTHStrategy\n- FasTagStrategy\n- RentPreProcessingStrategy\n- DefaultStrategy
        
        NRBP -> NRBP: _initializeDatabaseEventStrategies(options)
        note right: Initialize database event strategies:\n- UpsertStrategy\n- FastagUpsertStrategy\n- DeleteStrategy\n- FindAndUpdateStrategy\n- FindAndCreateStrategy
        
        NRBP -> BRCM: new BillRecordComparatorManager(options)
        activate BRCM
            BRCM -> BRCM: _initializeProcessors(options)
            note right: Initialize bill record comparators:\n- MobilePrepaidBillRecordComparator\n- DefaultBillRecordComparator
        deactivate BRCM
        
        NRBP -> AH: new AnalyticsHandler(options)
        activate AH
        deactivate AH
        
        NRBP -> NRBP: _createStrategyKey(service, paytype)
        note right: Create strategy mapping keys:\n- "mobile:postpaid" → MobilePostpaidStrategy\n- "mobile:prepaid" → MobilePrepaidStrategy\n- "electricity" → ElectricityStrategy\n- "creditcard" → CreditCardStrategy\n- "dth" → DTHStrategy\n- "fastag:prepaid" → FasTagStrategy\n- "rent" → RentPreProcessingStrategy
    deactivate NRBP
    
    NPB -> KC: initializeConsumer()
    note right: Initialize Kafka consumer and all required components
deactivate NPB

== Start Phase ==

NPB -> NPB: start()
activate NPB
    NPB -> NPB: startProducer()
    NPB -> NPB: startConsumer()
    note right: Start consuming messages from Kafka topics
deactivate NPB

== Message Processing Flow ==

KC -> NPB: _processKafkaData(records)
activate NPB
    NPB -> NPB: _processBillsData(billsKafkaRow)
    NPB -> NRBP: processBillData(billsKafkaRow)
    activate NRBP
        note right: Main processing method that orchestrates\nvalidation, preprocessing, and database operations

        == Validation Phase ==
        NRBP -> NRBDV: validateBillsData(billsKafkaRow, dbEvent)
        activate NRBDV
            NRBDV -> NRBDV: _validateDbEvent(dbEvent)
            NRBDV -> NRBDV: _checkMandatoryFields(billsKafkaRow)
            NRBDV -> NRBDV: _validateAmountLimits(billsKafkaRow)
            NRBDV -> NRBDV: _validateOldBillDueDate(billsKafkaRow)
        NRBDV --> NRBP: validationResult
        deactivate NRBDV

        alt validation successful
            == Preprocessing Phase ==
            NRBP -> NRBP: _preprocessBillData(billsKafkaRow)
            activate NRBP
                NRBP -> NRBP: _getPreprocessingStrategy(service, paytype)
                note right: Strategy selection:\n- mobile:postpaid → MobilePostpaidStrategy\n- mobile:prepaid → MobilePrepaidStrategy\n- electricity → ElectricityStrategy\n- creditcard → CreditCardStrategy\n- dth → DTHStrategy\n- fastag:prepaid → FasTagStrategy\n- rent → RentPreProcessingStrategy\n- default → DefaultStrategy

                NRBP -> BPS: process(billsKafkaRow)
                activate BPS
                    BPS -> BPS: _handleCommonPreprocessing(billsKafkaRow)
                    note right: Common preprocessing:\n- _updatePartialBillFlag()\n- _getToBeNotifiedForBillFetch()\n- _getToBeNotifiedForRealTime()\n- _getToBeSentToPublisherBillFetch()\n- _getToBeSentToPublisherMultiPid()\n- _getToBeNotifiedForCtAndPFCCE()\n- _updateAmbiguousStatus()\n- _updateCustomerOtherInfo()\n- _processExtraField()

                    BPS -> BPS: preprocess(billsKafkaRow)
                    note right: Service-specific preprocessing:\n- Mobile: Encrypt recharge number, handle data exhaustion\n- Electricity: Fetch PID mapping, update operator\n- Credit Card: Extract bank name, validate bank code\n- DTH: Encrypt subscriber ID\n- FasTag: Encrypt vehicle number, handle low balance\n- Rent: Process rent-specific data\n- Default: Minimal processing
                BPS --> NRBP: preprocessedData
                deactivate BPS
            deactivate NRBP

            == Database Operation Phase ==
            NRBP -> NRBP: _getDatabaseEventStrategy(dbEvent)
            note right: Database event routing:\n- upsert → UpsertStrategy\n- upsertWithRead → FastagUpsertStrategy\n- delete → DeleteStrategy\n- findAndUpdateData → FindAndUpdateStrategy\n- findAndCreate → FindAndCreateStrategy\n- updateMultipleRecordsWithSameRN → UpsertStrategy

            NRBP -> DEBS: execute(billsKafkaRow)
            activate DEBS
                note right: Example flow for UpsertStrategy

                DEBS -> NPBM: readBillsWithoutCustId(billsKafkaRow)
                activate NPBM
                NPBM --> DEBS: existingRecords
                deactivate NPBM

                alt hasCustomerIdMismatch || noExistingRecords
                    DEBS -> DEBS: _processRecordForNewRecord(billsKafkaRow)
                    DEBS -> NPBM: writeBatchRecordsNew(billsKafkaRow)
                    activate NPBM
                    NPBM --> DEBS: writeResult
                    deactivate NPBM
                else existingRecordsFound
                    DEBS -> DEBS: _processRecordForExistingRecord(existingRecord, billsKafkaRow)
                    DEBS -> NPBM: readBills(params)
                    activate NPBM
                    NPBM --> DEBS: existingRecord
                    deactivate NPBM

                    DEBS -> BRCM: process(billsKafkaRow, existingRecord)
                    activate BRCM
                        BRCM -> BRCM: _getProcessor(billsKafkaRow)
                        note right: Get appropriate comparator:\n- mobile:prepaid → MobilePrepaidBillRecordComparator\n- default → DefaultBillRecordComparator
                        
                        BRCM -> BBC: process(billsKafkaRow, existingRecord)
                        activate BBC
                            BBC -> BBC: _processCommonFeatures(billsKafkaRow, existingRecord)
                            BBC -> BBC: _updateStatusAndNotificationStatusNextBillFetchDate()
                            BBC -> BBC: _updateRemindLaterDate()
                            BBC -> BBC: _updateExtra()
                            BBC -> BBC: _parseExtraAndCustomerOtherInfo()
                            BBC -> BBC: _shouldUpdateBillBasedOnDueDateAndAmount()
                            
                            BBC -> BBC: process(billsKafkaRow, existingRecord)
                            note right: Service-specific comparison logic:\n- Mobile Prepaid: Handle Airtel migration, multiple customer IDs\n- Default: Source prioritization, SMS date validation
                        BBC --> BRCM: processedRecord
                        deactivate BBC
                    BRCM --> DEBS: processedRecord
                    deactivate BRCM

                    DEBS -> NPBM: writeBatchRecordsNew(billsKafkaRow)
                    activate NPBM
                    NPBM --> DEBS: writeResult
                    deactivate NPBM
                end

                == Post-Database Operations Phase ==
                DEBS -> PDO: execute(billsKafkaRow)
                activate PDO
                    note right: Flag-based post-processing using\npreprocessing results

                    PDO -> PDO: publishCassandraCDCvents(billsKafkaRow, cassandraCdcPublisher)
                    PDO -> PDO: _validateCDCInputs(billsKafkaRow, cassandraCdcPublisher)
                    PDO -> PDO: _prepareCDCData(billsKafkaRow)
                    PDO -> PDO: _processRechargeNumberDecryption(billData)
                    PDO -> KP: publishMessageToKafka(cassandraCdcPublisher)
                    note right: Always publish CDC events

                    alt toBeNotified == true
                        PDO -> PDO: _handleBillFetchNotification(billsKafkaRow, dbEvent)
                        PDO -> KP: publishMessageToKafka(nonRubillFetchKafkaPublisher)
                    end

                    alt toBeSentToPublisherBillFetch == true
                        PDO -> PDO: _handleNonRuBillFetchPublisher(billsKafkaRow, false)
                        PDO -> KP: publishMessageToKafka(nonRuPublisher)
                    end

                    alt toBeSentToPublisherMultiPid == true
                        PDO -> PDO: _handleNonRuBillFetchPublisher(billsKafkaRow, true)
                        PDO -> KP: publishMessageToKafka(nonRuPublisher)
                    end

                    alt toBeNotifiedForRealTime == true
                        PDO -> PDO: _handleRealTimeNotifications(billsKafkaRow)
                        PDO -> KP: publishMessageToKafka(nonRubillFetchKafkaPublisherRealtime)
                    end

                    alt toBeNotifiedForCtAndPFCCE == true
                        PDO -> PDO: _handleCtAndPFCCEvents(billsKafkaRow)
                        PDO -> PDO: _determineEventName(billsKafkaRow)
                        PDO -> KP: publishMessageToKafka(ctKafkaPublisher)
                        PDO -> KP: publishMessageToKafka(paytmFirstKafkaPublisher)
                    end

                    PDO -> PDO: _publishToUPMSRegistration(billsKafkaRow)
                    PDO -> KP: publishMessageToKafka(upmsPublisher)
                    note right: UPMS Registration based on service\nand operator eligibility
                PDO --> DEBS: postDbResult
                deactivate PDO
            DEBS --> NRBP: executionResult
            deactivate DEBS

        else validation failed
            NRBP -> AH: handleInvalidBillData(billsKafkaRow, validationError)
            activate AH
                AH -> AH: createRecordForAnalytics(billsKafkaRow, billStatus, 'NON_RU')
                note right: Track validation failures and send metrics
            AH --> NRBP: analyticsResult
            deactivate AH
        end

    NRBP --> NPB: processingResult
    deactivate NRBP

    NPB -> KC: commitOffset()
NPB --> KC: processingComplete
deactivate NPB

== Error Handling Flow ==

alt Processing Error occurs
    NRBP -> NRBP: catch(error)
    activate NRBP
        NRBP -> AH: handleInvalidBillData(billsKafkaRow, error.message)
        activate AH
            note right: Log error details, send metrics,\nand create analytics records
            AH -> AH: _getCustomerOtherInfo(customerOtherInfo)
            AH -> AH: _getExtra(record)
        AH --> NRBP: errorHandled
        deactivate AH
        note right: Utility.sendNonPaytmBillsMetrics\nfor error tracking
    deactivate NRBP
end

== Configuration Updates ==

NPB -> NPB: setVarFromDynamicConfig()
activate NPB
    note right: Periodic updates every 15 minutes:\n- Update multiplePidOperators\n- Refresh dynamic configuration\n- Update operational parameters
    NPB -> NRBP: setVarFromDynamicConfig()
    activate NRBP
        NRBP -> NRBP: update multiplePidOperators from config
    deactivate NRBP
deactivate NPB

== Analytics and Monitoring ==

note over AH: Analytics are integrated throughout the flow
AH -> AH: createRecordForAnalytics(record, billStatus, source)
activate AH
    AH -> AH: _getCustomerOtherInfo(customerOtherInfo)
    AH -> AH: _getExtra(record)
    note right: Create comprehensive analytics records\nfor monitoring and reporting
AH --> AH: analyticsRecord
deactivate AH

== Strategy Management ==

note over NRBP: Direct Strategy Management
NRBP -> NRBP: _createStrategyKey(service, paytype)
activate NRBP
    note right: Strategy key creation:\n- "mobile:postpaid" for mobile postpaid\n- "electricity" for electricity (no paytype)\n- "fastag:prepaid" for fastag prepaid
    NRBP -> NRBP: preprocessingStrategies.get(key)
    note right: Direct Map lookup for strategies\nNo factory pattern overhead
NRBP --> NRBP: strategyInstance
deactivate NRBP

note over BRCM: Bill Record Comparator Management
BRCM -> BRCM: _getProcessor(billsKafkaRow)
activate BRCM
    BRCM -> BRCM: _createStrategyKey(serviceType, paytype)
    BRCM -> BRCM: strategies.get(key)
    note right: Route to appropriate comparator:\n- MobilePrepaidBillRecordComparator for mobile:prepaid\n- DefaultBillRecordComparator for utility:default
BRCM --> BRCM: comparatorInstance
deactivate BRCM

@enduml