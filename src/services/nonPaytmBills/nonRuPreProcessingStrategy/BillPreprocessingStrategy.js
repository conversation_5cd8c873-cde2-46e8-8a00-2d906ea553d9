/**
 * Abstract base class for bill preprocessing strategies
 * Defines the interface that all concrete strategies must implement
 */
import _ from 'lodash';
import utility from '../../../lib';
import MOMENT from 'moment';


class BillPreprocessingStrategy {
    constructor(preprocessor) {
        if (this.constructor === BillPreprocessingStrategy) {
            throw new Error("Abstract class cannot be instantiated directly");
        }
        this.preprocessor = preprocessor;
    }

    /**
     * Abstract method to preprocess bill data
     * Must be implemented by concrete strategy classes
     * @param {Object} billsKafkaRow - Bill data to preprocess
     * @returns {Promise<Object>} Preprocessed bill data
     */
    async preprocess(billsKafkaRow) {
        const self = this;
        throw new Error("preprocess method must be implemented by concrete strategy");
    }

    /**
         * This method is final and should not be overridden by subclasses.
         * It provides a standard processing pipeline for all strategies.
         * @param {Object} billsKafkaRow - Bill data to process
         * @returns {Promise<Object>} Processed bill data
         */
    async process(billsKafkaRow) {
        // Common processing logic (if any) can go here.
        this.L.log("BillPreprocessor::process", `Processing bill with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        // Call the common preprocessing functionality
        billsKafkaRow = await this._handleCommonPreprocessing(billsKafkaRow);
        // By default, just call preprocess.
        return this.preprocess(billsKafkaRow);
    }

    /**
             * Handle all common preprocessing functionality for bill data
             * @param {Object} billsKafkaRow - Bill data to be preprocessed
             * @returns {Object} Preprocessed bill data
             */
    async _handleCommonPreprocessing(billsKafkaRow) {
        const self = this;
        //update the partial bill status
        self._updatePartialBillFlag(billsKafkaRow);

        //bill fetch notification to be sent
        _.set(billsKafkaRow, 'toBeNotified', self._getToBeNotifiedForBillFetch(billsKafkaRow));

        //real time notification to be sent
        _.set(billsKafkaRow, 'toBeNotifiedForRealTime', self._getToBeNotifiedForRealTime(billsKafkaRow));

        //publish to non ru bill fetch publisher
        _.set(billsKafkaRow, 'toBeSentToPublisherBillFetch', self._getToBeSentToPublisherBillFetch(billsKafkaRow, billsKafkaRow.isPrepaid));

        //publish to non ru multipid publisher
        _.set(billsKafkaRow, 'toBeSentToPublisherMultiPid', self._getToBeSentToPublisherMultiPid(billsKafkaRow, billsKafkaRow.isPrepaid));

        //ct and pfcce event to be sent
        _.set(billsKafkaRow, 'toBeNotifiedForCtAndPFCCE', self._getToBeNotifiedForCtAndPFCCE(billsKafkaRow));

        //publish to upms registration
        _.set(billsKafkaRow, 'toBeSentToPublisherBillPush', self._getToBeSentToPublisherBillPush(billsKafkaRow));

        //update ambiguous status
        self._updateAmbiguousStatus(billsKafkaRow);

        //updadate the customer other info
        self._updateCustomerOtherInfo(billsKafkaRow);

        //update the extra field
        self._processExtraField(billsKafkaRow);

        //format the bill_date,bill_fetch_date,due_date,next_bill_fetch_date
        await self._formatDates(billsKafkaRow);

        return billsKafkaRow;
    }

    /**
     * Process extra field in bill data
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Object} Bill data with processed extra field
     */
    _processExtraField(billsKafkaRow) {
        let extra = utility.parseExtra(_.get(billsKafkaRow, 'extra', null));
        if (billsKafkaRow.isValaidationSync) {
            extra.created_source = 'validationSync';
            extra.updated_source = 'validationSync';
            extra.updated_data_source = 'validationSync';
        } else {
            extra.created_source = 'sms';
            extra.updated_source = 'sms';
            extra.updated_data_source = _.get(extra, 'updated_data_source', 'sms');
        }
        extra.source_subtype_2 = this._updateSourceSubtype2(billsKafkaRow);
        billsKafkaRow.extra = utility.stringifyExtra(extra);
        return billsKafkaRow;
    }

    /**
         * Update source_subtype_2 field in extra data
         * @param {Object} billsKafkaRow - New bill data
         * @returns {string} Updated source_subtype_2 value
         * @private
         */
    _updateSourceSubtype2(billsKafkaRow) {
        let partialRecordFound = _.get(billsKafkaRow, 'partialRecordFound', false);
        if (partialRecordFound) {
            return 'PARTIAL_BILL';
        } else {
            return 'FULL_BILL';
        }
    }

    _isArchivalCronsExpiredUser(billsKafkaRow) {
        return _.get(billsKafkaRow, 'source', null) && billsKafkaRow.source == 'archivalCronsExpiredUser';
    }

    _isOperatorAllowedForSmartFetch(billsKafkaRow) {
        return this.allowedOperatorForSmartFetch && this.allowedOperatorForSmartFetch.includes(billsKafkaRow.operator.toLowerCase());
    }

    _getToBeNotifiedForBillFetch(billsKafkaRow) {
        if (_.get(billsKafkaRow, 'dbEvent', '') == "delete") {
            this.L.log("BillPreprocessor::getToBeNotifiedForBillFetch",
                `Skipping notification for delete event with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        // Skip notification if it's an archival cron expired user
        if (this._isArchivalCronsExpiredUser(billsKafkaRow)) {
            this.L.log("BillPreprocessor::getToBeNotified",
                `Skipping notification for archival cron expired user with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip notification if it's a validation sync and not validity expired
        if (billsKafkaRow.isValaidationSync && !billsKafkaRow.isValidityExpired) {
            this.L.log("BillPreprocessor::getToBeNotified",
                `Skipping notification for validation sync with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip notification if action is noAction
        if (billsKafkaRow.action && billsKafkaRow.action === 'noAction' && dbEvent !== 'upsert') {
            this.L.log("BillPreprocessor::getToBeNotified",
                `Skipping notification for noAction with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip notification if real time data is exhausted
        if (_.get(billsKafkaRow, 'isRealTimeDataExhausted', false)) {
            this.L.log("BillPreprocessor::getToBeNotified",
                `Skipping notification for real time data exhausted with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        // If none of the above conditions are met, notification should be sent
        this.L.log("BillPreprocessor::getToBeNotified", `Notification will be sent for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return true;
    }

    _getToBeNotifiedForRealTime(billsKafkaRow) {
        if (_.get(billsKafkaRow, 'dbEvent', '') == "delete") {
            this.L.log("BillPreprocessor::getToBeNotifiedForBillFetch",
                `Skipping notification for delete event with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        // If real time data is exhausted, real-time notification should be sent
        if (_.get(billsKafkaRow, 'isRealTimeDataExhausted', false)) {
            this.L.log("BillPreprocessor::getToBeNotifiedForRealTime",
                `Real-time notification will be sent for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return true;
        }
        this.L.log("BillPreprocessor::getToBeNotifiedForRealTime",
            `Real-time notification will not be sent for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return false;
    }

    _getToBeNotifiedForCtAndPFCCE(billsKafkaRow) {
        // Skip if it's an archival cron expired user
        if (this._isArchivalCronsExpiredUser(billsKafkaRow)) {
            this.L.log("BillPreprocessor::getToBeNotifiedForCtAndPFCCE",
                `Skipping CT and PFCCE events for archival cron expired user with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip if it's a validation sync
        if (billsKafkaRow.isValaidationSync) {
            this.L.log("BillPreprocessor::getToBeNotifiedForCtAndPFCCE",
                `Skipping CT and PFCCE events for validation sync with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip if action is noAction
        if (billsKafkaRow.action && billsKafkaRow.action === 'noAction') {
            this.L.log("BillPreprocessor::getToBeNotifiedForCtAndPFCCE",
                `Skipping CT and PFCCE events for noAction with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip if real time data is exhausted
        if (_.get(billsKafkaRow, 'isRealTimeDataExhausted', false)) {
            this.L.log("BillPreprocessor::getToBeNotifiedForCtAndPFCCE",
                `Skipping CT and PFCCE events for real time data exhausted with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // Skip if it's a prepaid bill
        if (_.get(billsKafkaRow, 'isPrepaid', false)) {
            this.L.log("BillPreprocessor::getToBeNotifiedForCtAndPFCCE",
                `Skipping CT and PFCCE events for prepaid bill with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }

        // If none of the above conditions are met, CT and PFCCE events should be sent
        this.L.log("BillPreprocessor::getToBeNotifiedForCtAndPFCCE",
            `CT and PFCCE events will be sent for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return true;
    }

    _isPublishableBill(billsKafkaRow) {
        const self = this;
        const allowedOperatorForBillFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'BILL_FETCH_CONFIG', 'OPERATORS'], null);
        return allowedOperatorForBillFetch &&
            allowedOperatorForBillFetch.includes(_.get(billsKafkaRow, 'operator', null).toLowerCase()) &&
            _.get(billsKafkaRow, 'source', null) != "reminderNonRuBillFetch"
            && (_.get(billsKafkaRow, 'dbEvent', '') == "upsert");
    }

    _isMultiplePidOperatorAllowed(billsKafkaRow) {
        // Check if operator is in multiple PID operators list
        const isMultiplePidOperator = this.multiplePidOperators &&
            this.multiplePidOperators.includes(_.get(billsKafkaRow, 'operator', null).toLowerCase());

        if (isMultiplePidOperator) {
            return true;
        }

        // Check Bajaj Finance loan specific logic for demerger operators
        if (_.get(billsKafkaRow, 'demergerOperatorsList', null) != null) {
            const custId = _.get(billsKafkaRow, 'customerId', null);

            if (billsKafkaRow.operator === "bajaj finance loan") {
                const isAllowed = custId % 100 <= this.allowedRolloutPercentageBaja;
                this.L.log('BillPreprocessor :: _isMultiplePidOperatorAllowed', {
                    msg: isAllowed ? 'Bajaj Finance loan allowed for multi-PID' : 'Bajaj Finance loan not allowed for multi-PID',
                    customerId: custId,
                    allowedRolloutPercentage: this.allowedRolloutPercentageBaja
                });
                return isAllowed;
            }
            return true; // Allow all other demerger operators
        }

        return false;
    }

    _getToBeSentToPublisherBillFetch(billsKafkaRow, isPrepaid) {
        if (_.get(billsKafkaRow, 'dbEvent', '') == "delete") {
            this.L.log("BillPreprocessor::getToBeNotifiedForBillFetch",
                `Skipping notification for delete event with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        if (isPrepaid) {
            this.L.log("BillPreprocessor::getToBeSentToPublisherBillFetch",
                `Bill will not be sent to non-RU bill fetch publisher for prepaid bill with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        if (this._isArchivalCronsExpiredUser(billsKafkaRow)) {
            this.L.log("BillPreprocessor::getToBeSentToPublisherBillFetch",
                `Bill will be sent to non-RU bill fetch publisher for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return true;
        }

        if (this._isPublishableBill(billsKafkaRow)) {
            if (this._isMultiplePidOperatorAllowed(billsKafkaRow)) {
                this.L.log("BillPreprocessor::getToBeSentToPublisherBillFetch",
                    `Bill will be sent to non-RU bill fetch publisher for multiple PID operator with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
                return false;
            } else if (_.get(billsKafkaRow, 'partialRecordFound', false)) {
                this.L.log("BillPreprocessor::getToBeSentToPublisherBillFetch",
                    `Bill will be sent to non-RU bill fetch publisher for single PID operator with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
                return true;
            }
        }

        this.L.log("BillPreprocessor::getToBeSentToPublisherBillFetch",
            `Bill will not be sent to non-RU bill fetch publisher for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return false;
    }

    _getToBeSentToPublisherMultiPid(billsKafkaRow, isPrepaid) {
        if (_.get(billsKafkaRow, 'dbEvent', '') == "delete") {
            this.L.log("BillPreprocessor::getToBeNotifiedForBillFetch",
                `Skipping notification for delete event with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        if (billsKafkaRow.isPrepaid) {
            this.L.log("BillPreprocessor::getToBeSentToPublisherMultiPid",
                `Bill will not be sent to multi-PID publisher for prepaid bill with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return false;
        }
        if (this._isPublishableBill(billsKafkaRow) && this._isMultiplePidOperatorAllowed(billsKafkaRow)) {
            this.L.log("BillPreprocessor::getToBeSentToPublisherMultiPid",
                `Bill will be sent to multi-PID publisher for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return true;
        }
        if (this._isMultiplePidOperatorAllowed(billsKafkaRow)) {
            this.L.log("BillPreprocessor::getToBeSentToPublisherMultiPid",
                `Bill will be sent to multi-PID publisher for demerger operators with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return true;
        }

        this.L.log("BillPreprocessor::getToBeSentToPublisherMultiPid",
            `Bill will not be sent to multi-PID publisher for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return false;
    }

    _getToBeSentToPublisherBillPush(billsKafkaRow) {
        let dbEvent = _.get(billsKafkaRow, 'dbEvent', null);
        if (dbEvent == 'upsert') {
            return true;
        }
        return false;
    }

    /**
     * Sets the product ID and operator information for a bill
     * @param {Object} billsKafkaRow - The bill data to process
     * @returns {Object} The updated bill data
     */
    _updateAmbiguousStatus(billsKafkaRow) {
        let self = this;
        try {
            let extra = utility.parseExtra(billsKafkaRow.extra),
                operator = _.toLower(_.get(billsKafkaRow, 'operator', null)),
                multiPidOperators = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'MULTIPLE_PID_OPERATORS', 'OPERATORS'], null);
            if (multiPidOperators &&
                multiPidOperators.includes(operator)) {
                extra.ambiguous = { state: true, board: true };
            } else if (_.get(billsKafkaRow, 'demergerOperatorsList', null) !== null) {
                //FETCh the attributes from the productId
                let attributes, boardFromAttributes;
                attributes = utility.parseExtra(_.get(self.config, ['CVR_DATA', billsKafkaRow.productId, 'attributes'], '{}'));
                boardFromAttributes = _.toLower(_.get(attributes, ['board'], null));
                let board = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'UPPCL_CONFIG_BOARD', operator], 'Uttar Pradesh Power (UPPCL)');
                if (boardFromAttributes) {
                    board = board;
                }
                extra.ambiguous = { state: true, board: board };
                billsKafkaRow.extra = utility.stringifyExtra(extra);
            }
        } catch (error) {
            self.L.error('setProductIdAndOperator', `Failed to set product ID and operator: ${error}`);
            throw error;
        }
    }

    /**
     * Update bill status based on whether it's a partial bill or not
     * @param {Object} billsKafkaRow - Bill data to update
     */
    _updatePartialBillFlag(billsKafkaRow) {
        const self = this;
        const partialRecordAllowedServices = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'PARTIAL_RECORD_ALLOWED', 'SERVICES'], null);
        if (partialRecordAllowedServices && partialRecordAllowedServices.includes(_.toLower(_.get(billsKafkaRow, 'service', "")))) {
            if ((_.get(billsKafkaRow, 'dueDate', null) == null || _.get(billsKafkaRow, 'amount', null) == null)) {
                this.L.log('BillPreprocessor::updatePartialBillFlag', `Partial record found for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
                billsKafkaRow.partialRecordFound = true;
            }
        }
    }

    /**
     * Updates and processes the customerOtherInfo field in the bill data
     *
     * This method handles the parsing, validation, and updating of the customerOtherInfo field,
     * which contains additional customer-related information such as SMS details, bill metadata,
     * and other contextual data that needs to be preserved and processed.
     *
     * @param {Object} billsKafkaRow - The bill data to process
     * @param {string|Object} billsKafkaRow.customerOtherInfo - Customer other info data (JSON string or object)
     * @param {string} billsKafkaRow.debugKey - Debug key for logging
     * @param {string} billsKafkaRow.service - Service type
     * @param {boolean} billsKafkaRow.isValaidationSync - Flag indicating if this is a validation sync record
     */
    _updateCustomerOtherInfo(billsKafkaRow) {
        const self = this;
        try {
            const customerOtherInfo = utility.parseCustomerOtherInfo(billsKafkaRow.customerOtherInfo);
            if (_.get(billsKafkaRow, 'partialRecordFound', false)) {
                customerOtherInfo.partialRecordFound = true;
            }
            billsKafkaRow.customerOtherInfo = utility.stringifyCustomerOtherInfo(customerOtherInfo);
        } catch (error) {
            self.L.error('updateCustomerOtherInfo', `Failed to update customerOtherInfo for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}, error: ${error}`);
        }
        return billsKafkaRow;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        throw new Error("getServiceType method must be implemented by concrete strategy");
    }

    /**
     * Get the paytype this strategy handles (optional)
     * @returns {string|null} Paytype or null if not applicable
     */
    getPaytype() {
        return null;
    }

    /**
     * Check if this strategy can handle the given service and paytype
     * @param {string} service - Service type
     * @param {string} paytype - Payment type
     * @returns {boolean} True if strategy can handle the service/paytype
     */
    canHandle(service, paytype) {
        const strategyService = this.getServiceType();
        const strategyPaytype = this.getPaytype();

        if (strategyService !== service) {
            return false;
        }

        // If strategy has specific paytype requirement, check it
        if (strategyPaytype && strategyPaytype !== paytype) {
            return false;
        }

        return true;
    }

    /**
 * Format date fields in bill data
 * @param {Object} billsKafkaRow - Bill data to format dates
 * @returns {Object} Bill data with formatted dates
 */
    _formatDates(billsKafkaRow) {
        const self = this;
        try {
            const dateFields = ['billDate', 'billFetchDate', 'dueDate', 'bill_date', 'bill_fetch_date', 'due_date'];

            dateFields.forEach(field => {
                if (_.get(billsKafkaRow, field, null) != null) {
                    const dateValue = billsKafkaRow[field];
                    const formattedDate = MOMENT(dateValue).format('YYYY-MM-DD HH:mm:ss');
                    // Use moment to parse and format the date
                    if (formattedDate !== 'Invalid date') {
                        billsKafkaRow[field] = formattedDate;
                    } else {
                        billsKafkaRow[field] = dateValue;
                    }
                }
            });

            self.L.log('BillPreprocessor::_formatDates', `Formatted dates for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        } catch (error) {
            self.L.error('BillPreprocessor::_formatDates', `Failed to format dates for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}, error: ${error}`);
        }
        return billsKafkaRow;
    }
}

// Prevent overriding of process method in subclasses
Object.defineProperty(BillPreprocessingStrategy.prototype, 'process', {
    writable: false,
    configurable: false,
});

export default BillPreprocessingStrategy;
