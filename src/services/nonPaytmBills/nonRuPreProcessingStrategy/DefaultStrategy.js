import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';

/**
 * Default strategy for preprocessing bills when no specific strategy is found
 */
class DefaultStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.config = options.config;
        this.L = options.L;
    }
    /**
     * Preprocess default bill data
     * @param {Object} billsKafkaRow - Default bill data
     * @returns {Object} Preprocessed default bill data
     */
    async preprocess(billsKafkaRow) {
        // Default preprocessing - just return the data as-is
        this.L.log('DefaultStrategy', `Using default preprocessing for service: ${billsKafkaRow.service}, debugKey: ${billsKafkaRow.debugKey}`);
        return billsKafkaRow;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'default';
    }

    /**
     * Check if this strategy can handle the given service and paytype
     * This is the fallback strategy, so it can handle any service
     * @param {string} service - Service type
     * @param {string} paytype - Payment type
     * @returns {boolean} Always returns true as this is the fallback
     */
    canHandle(service, paytype) {
        return true; // Default strategy can handle any service as fallback
    }
}

export default DefaultStrategy;
