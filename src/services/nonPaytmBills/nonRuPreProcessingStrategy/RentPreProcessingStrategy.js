import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import _ from 'lodash';
/**
 * Strategy for preprocessing rent payment data
 */
class RentPreProcessingStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.L = options.L;
    }

    /**
     * Preprocess rent payment data
     * @param {Object} billsKafkaRow - Bill data to preprocess
     * @returns {Promise<Object>} Preprocessed bill data
     * @throws {Error} If preprocessing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            if (billsKafkaRow.dbEvent === 'delete') {
                billsKafkaRow.rechargeNumber = _.get(billsKafkaRow, 'customerId') + '_dummy';
            }
            this.L.log('RentPreProcessingStrategy', `Successfully preprocessed rent payment data for debugKey: ${billsKafkaRow.debugKey} with dbEvent: ${billsKafkaRow.dbEvent}`);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('RentPreProcessingStrategy', `Failed to preprocess rent payment data: ${error} for debugKey: ${billsKafkaRow.debugKey}`);
            throw error;
        }
    }

    _getToBeSentToPublisherBillPush(billsKafkaRow) {
        //if the bill is not encrypted, then it should be sent to publisher
        this.L.log("RentPreProcessingStrategy::_getToBeSentToPublisherBillPush", `Bill will not be sent to publisher for debugKey: ${billsKafkaRow.debugKey}`);
        return false;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'rent payment';
    }

    /**
     * Get the paytype this strategy handles
     * @returns {string|null} Paytype or null if not applicable
     */
    getPaytype() {
        return null;
    }
}

export default RentPreProcessingStrategy;