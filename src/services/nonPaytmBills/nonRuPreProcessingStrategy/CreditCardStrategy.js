import _ from 'lodash';
import utility from '../../../lib/index.js';
import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import MOMENT from 'moment'

/**
 * Strategy for preprocessing credit card bills
 */
class CreditCardStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.config = options.config;
        this.L = options.L;
    }

    /**
     * Preprocess credit card bill data
     * @param {Object} billsKafkaRow - Credit card bill data
     * @returns {Object} Preprocessed credit card bill data
     * @throws {Error} When bank code is missing or attributes parsing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            const bankName = this._extractAndValidateBankName(billsKafkaRow);
            // Update billsKafkaRow with bank information
            billsKafkaRow.operator = bankName;
            billsKafkaRow.bankName = bankName;
            this.L.log('CreditCardStrategy', `Successfully preprocessed credit card bill for debugKey: ${billsKafkaRow.debugKey}`);
            return billsKafkaRow;
        } catch (error) {
            const err = `Error while parsing attributes for PID: ${billsKafkaRow.debugKey}`;
            this.L.error('CreditCardStrategy', `Error while parsing attributes for PID for debugKey: ${billsKafkaRow.debugKey}`);
            throw new Error(err);
        }
    }

    /**
     * Extract and validate bank name from bill data
     * @param {Object} billsKafkaRow - Credit card bill data
     * @returns {string} Validated bank name
     * @throws {Error} When bank code is missing
     * @private
     */
    _extractAndValidateBankName(billsKafkaRow) {
        const attributes = utility.parseExtra(_.get(this.config, ['CVR_DATA', billsKafkaRow.productId, 'attributes'], '{}'));
        const bankName = _.toLower(_.get(attributes, ['bank_code'], ''));
        if (!bankName || bankName === '') {
            this.L.error("CreditCardStrategy:: bank code unavailable", billsKafkaRow, "financial services");
            throw new Error('bank code unavailable');
        }
        return bankName;
    }

    _getToBeSentToPublisherBillPush(billsKafkaRow) {
        //if the bill is not encrypted, then it should be sent to publisher
        this.L.log("CreditCardStrategy::_getToBeSentToPublisherBillPush", `Bill will not be sent to publisher for debugKey: ${billsKafkaRow.debugKey}`);
        return false;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'financial services';
    }

    getPaytype() {
        return 'credit card';
    }
}

export default CreditCardStrategy;
