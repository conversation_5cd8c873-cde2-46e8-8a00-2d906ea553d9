/**
 * Strategy Pattern Implementation for Bill Preprocessing
 *
 * This module exports all the bill preprocessing strategies.
 * The Strategy pattern allows for easy extension and modification of bill
 * preprocessing logic. Strategy management is now handled directly in
 * BillPreprocessor to reduce complexity.
 *
 * @example
 * // Using strategies directly in BillPreprocessor
 * import MobilePostpaidStrategy from './MobilePostpaidStrategy.js';
 *
 * class BillPreprocessor {
 *   constructor(options) {
 *     this.strategies = new Map();
 *     this.strategies.set('mobile:postpaid', new MobilePostpaidStrategy(this));
 *   }
 *
 *   async preprocessBillData(billsKafkaRow) {
 *     const strategy = this.getStrategy(service, paytype);
 *     return await strategy.preprocess(billsKafkaRow);
 *   }
 * }
 */

// Export the base strategy class
export { default as BillPreprocessingStrategy } from './BillPreprocessingStrategy.js';

// Export all concrete strategies
export { default as MobilePostpaidStrategy } from './MobilePostpaidStrategy.js';
export { default as MobilePrepaidStrategy } from './MobilePrepaidStrategy.js';
export { default as ElectricityStrategy } from './ElectricityStrategy.js';
export { default as CreditCardStrategy } from './CreditCardStrategy.js';
export { default as DTHStrategy } from './DTHStrategy.js';
export { default as FasTagStrategy } from './FasTagStrategy.js';
export { default as DefaultStrategy } from './DefaultStrategy.js';

/**
 * Strategy Pattern Benefits:
 *
 * 1. **Open/Closed Principle**: Easy to add new bill types without modifying existing code
 * 2. **Single Responsibility**: Each strategy handles one specific bill type
 * 3. **Testability**: Each strategy can be unit tested independently
 * 4. **Maintainability**: Changes to one bill type don't affect others
 * 5. **Extensibility**: New strategies can be added by implementing the base interface
 *
 * Adding a New Strategy:
 * 1. Create a new class extending BillPreprocessingStrategy
 * 2. Implement the required methods (preprocess, getServiceType, getPaytype)
 * 3. Register it in BillPreprocessor's initializeStrategies method
 * 4. Export it from this index file
 */
