import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import NonPaytmBillsModel from '../../../models/nonPaytmBills.js';
import EncryptorDecryptor from 'encrypt_decrypt';

/**
 * Strategy for preprocessing mobile postpaid bills
 */
class MobilePostpaidStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
        this.config = options.config;
        this.cryptr = new EncryptorDecryptor();
        this.L = options.L;
    }

    /**
     * Preprocess mobile postpaid bill data
     * @param {Object} billsKafkaRow - Mobile postpaid bill data
     * @returns {Promise<Object>} Preprocessed mobile postpaid bill data
     * @throws {Error} If preprocessing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            // Encrypt recharge number
            billsKafkaRow.rechargeNumber = this.cryptr.encrypt(billsKafkaRow.rechargeNumber);
            billsKafkaRow.is_encrypted_done = true;
            this.L.log('MobilePostpaidStrategy', `Successfully preprocessed mobile postpaid bill for debugKey: ${billsKafkaRow.debugKey}`);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('MobilePostpaidStrategy', `Failed to preprocess mobile postpaid bill: ${error}`);
            throw error;
        }
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'mobile';
    }

    /**
     * Get the paytype this strategy handles
     * @returns {string} Paytype
     */
    getPaytype() {
        return 'postpaid';
    }
}

export default MobilePostpaidStrategy;
