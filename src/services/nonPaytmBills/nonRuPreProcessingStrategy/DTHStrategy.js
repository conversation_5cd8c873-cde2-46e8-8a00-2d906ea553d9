import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import EncryptorDecryptor from 'encrypt_decrypt';
/**
 * Strategy for preprocessing DTH bills
 */
class DTHStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.config = options.config;
        this.cryptr = new EncryptorDecryptor();
        this.L = options.L;
    }

    /**
     * Preprocess DTH bill data
     * @param {Object} billsKafkaRow - DTH bill data
     * @returns {Object} Preprocessed DTH bill data
     * @throws {Error} If preprocessing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            // Encrypt recharge number
            billsKafkaRow.rechargeNumber = this.cryptr.encrypt(billsKafkaRow.rechargeNumber);
            billsKafkaRow.is_encrypted_done = true;
            this.L.log('DTHStrategy', `Successfully preprocessed DTH bill for debugKey: ${billsKafkaRow.debugKey}`);
            return billsKafkaRow;
        } catch (error) {
            this.L.error('DTHStrategy', `Failed to preprocess DTH bill: ${error}`);
            throw error;
        }
    }

    _getToBeSentToPublisherBillPush(billsKafkaRow) {
        //if the bill is not encrypted, then it should be sent to publisher
        this.L.log("DTHStrategy::_getToBeSentToPublisherBillPush", `Bill will not be sent to publisher for debugKey: ${billsKafkaRow.debugKey}`);
        return false;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'dth';
    }
}

export default DTHStrategy;
