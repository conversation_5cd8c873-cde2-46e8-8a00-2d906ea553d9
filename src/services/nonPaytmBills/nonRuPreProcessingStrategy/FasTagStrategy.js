import _ from 'lodash';
import BillPreprocessingStrategy from './BillPreprocessingStrategy.js';
import MOMENT from 'moment';
import utility from '../../../lib';

/**
 * Strategy for preprocessing FASTag bills
 * Handles FASTag recharge bills with specific vehicle number encryption,
 * low balance detection, and bank-specific processing
 */
class FasTagStrategy extends BillPreprocessingStrategy {
    constructor(options) {
        super(options);
        this.L = options.L;
    }

    /**
     * Preprocess FASTag bill data
     * @param {Object} billsKafkaRow - FASTag bill data
     * @returns {Promise<Object>} Preprocessed FASTag bill data
     * @throws {Error} If preprocessing fails
     */
    async preprocess(billsKafkaRow) {
        try {
            const debugKey = _.get(billsKafkaRow, 'debugKey', null);
            this.L.log('FasTagStrategy', `Starting FASTag preprocessing for debugKey: ${debugKey}`);
            await this._handleLowBalanceProcessing(billsKafkaRow);
            this.L.log('FasTagStrategy', `Successfully preprocessed FASTag bill for debugKey: ${debugKey}`);
            return billsKafkaRow;

        } catch (error) {
            this.L.error('FasTagStrategy',
                `Failed to preprocess FASTag bill for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}, error: ${error}`);
            throw error;
        }
    }

    /**
     * Determines if notification should be sent for FASTag recharge bills.
     * @param {Object} billsKafkaRow - Bill data
     * @returns {boolean} Whether notification should be sent
     */
    _getToBeNotifiedForBillFetch(billsKafkaRow) {
        // Skip notification if service is fastag recharge
        this.L.log("FasTagStrategy::getToBeNotifiedForBillFetch",      
            `Skipping notification for fastag recharge with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return false; // No notification for FASTag recharge
    }

    /**
     * Determines if real-time notification should be sent for FASTag recharge bills.
     * @param {Object} billsKafkaRow - Bill data
     * @returns {boolean} Whether real-time notification should be sent
     */
    _getToBeNotifiedForRealTime(billsKafkaRow) {
        if (
            _.get(billsKafkaRow, 'dbEvent') === 'upsertWithoutRead' ||
            (
                _.get(billsKafkaRow, 'dbEvent') === 'upsertWithRead' &&
                _.get(billsKafkaRow, 'isLowBalance', false)
            )
        ) {
            this.L.log("FasTagStrategy::getToBeNotifiedForRealTime",
                `Real-time notification will be sent for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            return true;
        }
        return false;
    }

    /**
     * Determines if CT and PFCCE events should be sent for FASTag recharge bills.
     * @param {Object} billsKafkaRow - Bill data
     * @returns {boolean} Whether CT and PFCCE events should be sent
     */
    _getToBeNotifiedForCtAndPFCCE(billsKafkaRow) {
        this.L.log("FasTagStrategy::getToBeNotifiedForCtAndPFCCE",
            `Skipping CT and PFCCE events for fastag recharge with debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
        return false;
    }

    /**
     * Handle low balance processing for FASTag
     * @param {Object} billsKafkaRow - FASTag bill data
     * @private
     */
    async _handleLowBalanceProcessing(billsKafkaRow) {
        const isLowBalance = this._checkIfFastagBalanceIsLow(billsKafkaRow);
        let currentExtra = utility.parseExtra(_.get(billsKafkaRow, 'extra', '{}'));
        let smsDateTime = _.get(currentExtra, 'smsDateTime', null);
        let currentDate = MOMENT().startOf('day'); 
        if (smsDateTime) {
            // Convert smsDateTime to moment object, handling both timestamp and date string formats
            const smsMoment = typeof smsDateTime === 'number' ?
                MOMENT(smsDateTime * 1000) : // Convert seconds to milliseconds
                MOMENT(smsDateTime).isValid() ?
                    MOMENT(smsDateTime) :
                    MOMENT(parseInt(smsDateTime) * 1000); // Convert string seconds to milliseconds
            if (smsMoment.isBefore(currentDate)) {
                this.L.log('FasTagStrategy',
                    `Ignoring Fastag low balance record - SmsDate Time is less than current date for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error("smsDate less than current date");
            }
        }
        if (!isLowBalance && _.get(billsKafkaRow, 'operator', null) == 'dummy bank') { // this is to handle dummy bank, as we dont' want to process oprator as dummy bank
            this.L.log('FasTagStrategy', `Dummy bank record found, ignoring it  ${billsKafkaRow}`);
            throw new Error("dummy_operator_found");
        }
        return;
    }

    /**
     * Check if FASTag balance is low
     * @param {Object} billsKafkaRow - FASTag bill data
     * @returns {boolean} True if balance is low
     * @private
     */
    _checkIfFastagBalanceIsLow(billsKafkaRow) {
        let self = this;
        let customerOtherInfo = utility.parseCustomerOtherInfo(billsKafkaRow.customerOtherInfo);
        const isLowBalance = _.get(billsKafkaRow, 'isLowBalance', false) || _.get(customerOtherInfo, 'isLowBalance', false);
        if (isLowBalance) {
            _.set(billsKafkaRow, 'isLowBalance', true);
            self.L.log('FasTagStrategy',
                `Low balance detected for debugKey: ${billsKafkaRow.debugKey}`);
        } else {
            if (_.get(billsKafkaRow, 'operator', '').toLowerCase() == 'dummy bank') {
                self.L.log('FasTagStrategy', `Dummy bank record found, ignoring it  ${billsKafkaRow}`);
                throw new Error("dummy_operator_found");
            }
        }
        return isLowBalance;
    }

    _getToBeSentToPublisherBillPush(billsKafkaRow) {
        //if the bill is not encrypted, then it should be sent to publisher
        this.L.log("FasTagStrategy::_getToBeSentToPublisherBillPush", `Bill will not be sent to publisher for debugKey: ${billsKafkaRow.debugKey}`);
        return false;
    }

    /**
     * Get the service type this strategy handles
     * @returns {string} Service type
     */
    getServiceType() {
        return 'fastag recharge';
    }

    /**
     * Get the paytype this strategy handles
     * @returns {string} Paytype - FASTag is typically prepaid
     */
    getPaytype() {
        return 'prepaid';
    }
}

export default FasTagStrategy;
