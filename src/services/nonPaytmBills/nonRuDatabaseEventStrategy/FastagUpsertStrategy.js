import DatabaseEventBaseStrategy from './DatabaseEventBaseStrategy';
import PostDbOperations from '../nonRuPostDbOperations';
import BillFetchAnalytics from '../../../lib/billFetchAnalytics'
import utility from '../../../lib';
import _ from 'lodash';
import NonPaytmBillsModel from '../../../models/nonPaytmBills';
import BillRecordComparator from '../nonRuBillRecordComparator/BillRecordComparatorManager';
/**
 * Strategy for upserting data with read first
 */
class FastagOperatorUpsertStrategy extends DatabaseEventBaseStrategy {

    constructor(options) {
        super(options);
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
        this.postDbOperations = new PostDbOperations(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.config = options.config;
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
        this.billRecordComparator = new BillRecordComparator(options);
        this.L = options.L;
    }
    /**
     * Execute upsert with read strategy
     * @param {Object} billsKafkaRow - Bill data
     * @param {Function} next - Callback function
     * @returns {Promise<void>}
     */
    async execute(billsKafkaRow) {
        let self = this;
        let isFastagLowBalance = _.get(billsKafkaRow, 'isLowBalance', false), isDuplicate = false;
        try {
            let recentRecords = await this.nonPaytmBillsModel.readRecentBills(billsKafkaRow);
            let operators = _.get(self.config, ['DYNAMIC_CONFIG', 'FASTTAG_SMS_PARSING_CONFIG', 'OPERATOR_LIST_MAPPING', 'OPERATOR_LIST'], []);  //update operator list here from config
            operators = operators.map(operator => operator.toLowerCase());
            if (isFastagLowBalance) {
                operators = [];
            }
            //read the existing record with operators
            let existingRecord = await this.nonPaytmBillsModel.readBillsWithOperators(billsKafkaRow, operators);
            let updateDueTable = false, updateGenTable = false, existingRecordDueTable = [], existingRecordGenTable = [];
            if (existingRecord.length > 0) {
                if (isFastagLowBalance) {
                    isDuplicate = self._checkSmsTimeDuplication(existingRecord[0], billsKafkaRow);
                } else {
                    existingRecord.every(async row => {
                        if (_.get(row, 'operator', null) == 'dummy bank') {
                            await self.nonPaytmBillsModel.deleteRecentAndBillsRecords(row);
                            return true; // will continue the execution
                        } else { // if data exist with any other bank we will not proceed with current data so marking duplicate.
                            isDuplicate = true;
                            self.L.error(`execute`, `Duplicate entry with other bank for paylaod  ${billsKafkaRow} with db data ${row}`);
                            return false;
                        }
                    })
                }

            }

            if (isDuplicate) {
                this.L.log(`execute:: Duplicate entry with other bank for payload for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error("duplicate_entry_with_other_bank");
            } else {
                await this.nonPaytmBillsModel.writeBatchRecordsNew(
                    billsKafkaRow,
                    recentRecords,
                    updateDueTable,
                    updateGenTable,
                    existingRecordDueTable,
                    existingRecordGenTable,
                    this.cassandraCdcPublisher,
                    existingRecord
                );
            }
            await this.postDbOperations.execute(billsKafkaRow);
        } catch (error) {
            this.L.error('execute', `Failed to execute: ${error} for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}`);
            throw error;
        }
    }

    _checkSmsTimeDuplication(existingRecord, newRecord) {
        const { existingSmsTime, newSmsTime } = this._extractSmsTimes(existingRecord, newRecord);
        if (newSmsTime) {
            if (existingSmsTime && Number(newSmsTime) <= Number(existingSmsTime)) {
                this.L.log(`checkSmsTimeDuplication:: Ignoring Fastag low balance record - SmsDate Time is less than existing SmsDate Time for debugKey ${_.get(newRecord, 'debugKey', null)}`);
                return true;
            }
        }
        return false;
    }
    _extractSmsTimes(existingRecord, newRecord) {
        let existingExtra = utility.parseExtra(_.get(existingRecord, 'extra', '{}'));
        let currentExtra = utility.parseExtra(_.get(newRecord, 'extra', '{}'));
        let existingSmsTime = _.get(existingExtra, 'smsDateTime', null);
        let newSmsTime = _.get(currentExtra, 'smsDateTime', null);
        return { existingSmsTime, newSmsTime };
    }
}

export default FastagOperatorUpsertStrategy; 