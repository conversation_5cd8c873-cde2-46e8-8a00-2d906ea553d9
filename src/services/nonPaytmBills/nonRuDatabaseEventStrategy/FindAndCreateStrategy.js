import DatabaseEventBaseStrategy from './DatabaseEventBaseStrategy';
import NonPaytmBillsModel from '../../../models/nonPaytmBills';
import utility from '../../../lib';
import PostDbOperations from '../nonRuPostDbOperations';

/**
 * Strategy for inserting data only if it doesn't exist
 */
class FindAndCreateStrategy extends DatabaseEventBaseStrategy {
    constructor(options) {
        super(options);
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
        this.postDbOperations = new PostDbOperations(options);
    }

    /**
     * Execute find and create strategy
     * @param {Object} billsKafkaRow - Bill data
     * @param {Function} next - Callback function
     * @returns {Promise<void>}
     */
    async execute(billsKafkaRow) {
        try {
            await this._findAndCreateData(billsKafkaRow);
            await this.postDbOperations.execute(billsKafkaRow);
            return;
        } catch (error) {
            this.L.error('findAndCreateData', `Failed to find and create data: ${error} for debugKey: ${billsKafkaRow.debugKey}`);
            throw error;
        }
    }

    async _findAndCreateData(billsKafkaRow) {
        const self = this;

        let existingRecord = await self.nonPaytmBillsModel.readBills(billsKafkaRow);
        if (existingRecord.length == 1) {
            self.L.log('findAndCreateData', `Data is present in the DB for debugKey ${billsKafkaRow.debugKey}`);
            throw new Error('data is present in the DB');
        }
        else {
            billsKafkaRow = self._formatDataToUpdate(billsKafkaRow);
            await self.nonPaytmBillsModel.writeBatchRecordsNew(billsKafkaRow, null, null, null, null, null, self.cassandraCdcPublisher);
        }
    }

    _formatDataToUpdate(billsKafkaRow) {
        let self = this;
        try {
            let extra = utility.parseExtra(billsKafkaRow.extra);
            let customerOtherInfo = utility.parseCustomerOtherInfo(billsKafkaRow.customerOtherInfo);
            extra.last_paid_amount = Math.abs(billsKafkaRow.amount);
            billsKafkaRow.extra = utility.stringifyExtra(extra);
            billsKafkaRow.customerOtherInfo = utility.stringifyCustomerOtherInfo(customerOtherInfo);
        }
        catch (error) {
            self.L.error(`Ignore record :: Error for record for debugKey ${billsKafkaRow.debugKey}`, error);
            return null;
        }
        return billsKafkaRow;
    }
}

export default FindAndCreateStrategy;    