import DatabaseEventBaseStrategy from './DatabaseEventBaseStrategy';
import _ from 'lodash';
import NonPaytmBillsModel from '../../../models/nonPaytmBills';
import CRYPTO from 'crypto';
import PostDbOperations from '../nonRuPostDbOperations';

/**
 * Strategy for deleting data
 */
class DeleteStrategy extends DatabaseEventBaseStrategy {
    constructor(options) {
        super(options);
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
        this.postDbOperations = new PostDbOperations(options);
    }

    /**
     * Execute delete strategy
     * @param {Object} billsKafkaRow - Bill data
     * @returns {Promise<void>}
     */
    async execute(billsKafkaRow) {
        try {
            await this._deleteData(billsKafkaRow);
            await this.postDbOperations.execute(billsKafkaRow);
            return;
        } catch (error) {
            this.L.error('deleteData', `Failed to delete data: ${error}`);
            throw error;
        }
    }

    async _deleteData(billsKafkaRow) {
        let payloadForProcessing = [billsKafkaRow], self = this;
        if (_.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', billsKafkaRow.operator, 'DEMERGER_MAPPED_OPERATOR'], null)) {
            let clonedData = _.cloneDeep(billsKafkaRow);
            clonedData.operator = _.get(self.config, ['DYNAMIC_CONFIG', 'OPERATOR_CONFIG', billsKafkaRow.operator, 'DEMERGER_MAPPED_OPERATOR'], null);
            payloadForProcessing.push(clonedData);
        }

        try {
            for (const payload of payloadForProcessing) {
                try {
                    await self._deleteDataExtended(payload);
                } catch (err) {
                    self.L.error('deleteDataExtended :: Error while deleting Data in DB', err);
                    throw err;
                }
            }
            return;
        } catch (err) {
            self.L.error('deleteDataExtended :: Error in processing payloads', err);
            throw err;
        }
    }

    async _deleteDataExtended(billsKafkaRow) {
        const self = this;
        let existingRecord = await self.nonPaytmBillsModel.readBills(billsKafkaRow);
        if (existingRecord.length === 1) {
            self.L.log(`_deleteDataExtended :: Record found for debugKey ${billsKafkaRow.debugKey}`);
            billsKafkaRow.nextBillFetchDate = _.get(existingRecord[0], 'next_bill_fetch_date', null);
            billsKafkaRow.bucketId = await self._generateBucketNameUsingHash(billsKafkaRow, existingRecord[0]);
        }
        self.nonPaytmBillsModel.deleteUsingRecentRecords(billsKafkaRow, self.cassandraCdcPublisher, (err) => {
            if (err) {
                self.L.error(`deleteDataExtended :: Error while deleting record: ${err} for debugKey: ${billsKafkaRow.debugKey}`);
                throw new Error('Error while deleting record');
            } else {
                self.L.log(`deleteDataExtended :: Record deleted for debugKey ${billsKafkaRow.debugKey}`);
                return;
            }
        });
        return;
    }

    async _generateBucketNameUsingHash(params, currentRecord) {
        const self = this;
        let customer_id = _.get(params, 'customerId', _.get(currentRecord, 'customer_id', null)),
            recharge_number = _.get(params, 'rechargeNumber', _.get(currentRecord, 'recharge_number', null));
        try {
            const inputString = `${customer_id}${recharge_number}`;
            const hash = CRYPTO.createHash('md5').update(inputString).digest('hex');

            let value = 40;
            const hashValue = parseInt(hash, 16) % value;
            const resultString = hashValue.toString();
            return resultString;
        } catch (err) {
            self.L.error(`error in generating bucketname`, err);
            return null;
        }
    }
}

export default DeleteStrategy; 