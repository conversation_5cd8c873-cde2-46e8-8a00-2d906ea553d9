import NonPaytmBillsModel from '../../../models/nonPaytmBills.js';
import DatabaseEventBaseStrategy from '../nonRuDatabaseEventStrategy/DatabaseEventBaseStrategy.js';
import _ from 'lodash';
import AnalyticsHandler from '../AnalyticsHandler.js';
import MOMENT from 'moment';
import utility from '../../../lib';

class FindAndUpdateStrategy extends DatabaseEventBaseStrategy {
    constructor(options) {
        super(options);
        this.nonPaytmBillsModel = new NonPaytmBillsModel(options);
        this.analyticsHandler = new AnalyticsHandler(options);
        this.cassandraCdcPublisher = options.cassandraCdcPublisher;
    }

    async execute(billsKafkaRow) {
        const self = this;
        let existingRecord = [];
        // Process records with controlled concurrency
        try {
            existingRecord = await self.nonPaytmBillsModel.readBills(billsKafkaRow);
            //log after matching the service operator
            await self._processRecord(billsKafkaRow, existingRecord);
        } catch (error) {
            self.L.error('execute',
                `Failed to process records for debugKey ${billsKafkaRow.debugKey}: ${error}`);
            throw error;
        }
        self.L.log('execute', 'Finished processing all records');

    }

    async _processRecord(billsKafkaRow, existingRecord) {
        const self = this;
        try {
            self.L.log('processRecord', `Processing record for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            if (existingRecord.length === 1) {
                self.L.log('processRecord', `No existing record found for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
                await self._updateDbRecord(billsKafkaRow, existingRecord[0]);
                return;
            } else if (existingRecord.length > 1) {
                self.L.log('processRecord', `Multiple existing records found for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error("Multiple existing records found for debugKey");
            } else {
                self.L.log('processRecord', `No existing record found for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
            }
        } catch (error) {
            self.L.error('processRecord',
                `Failed to process record for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}: ${error.message} `);
            try {
                await this.analyticsHandler.handleInvalidBillData(billsKafkaRow, error.message);
            } catch (analyticsError) {
                self.L.error('processRecord',
                    `Failed to handle invalid bill data for debugKey ${_.get(billsKafkaRow, 'debugKey', null)}: ${analyticsError.message}`);
            }
        }
    }

    async _updateDbRecord(billsKafkaRow, existingRecord) {
        const self = this;
        try {
            self._validateExistingRecord(billsKafkaRow, existingRecord);
            let updatedRecord = self._getDataToUpdate(billsKafkaRow, existingRecord);
            //update the amount or other fields in the db
            if (updatedRecord) {
                await self.nonPaytmBillsModel.updateAmountOfNonPaytmRecordsNew(updatedRecord, existingRecord, self.cassandraCdcPublisher);
            } else {
                self.L.log('updateDbRecord', `No data to update for debugKey: ${_.get(billsKafkaRow, 'debugKey', null)}`);
                throw new Error("No data to update");
            }
        } catch (error) {
            self.L.error('updateDbRecord', `Failed to update db record: ${error}`);
            throw error;
        }
    }

    _getDataToUpdate(billsKafkaRow, existingRecord) {
        let self = this;
        let updatedDbRecord = _.clone(existingRecord);
        updatedDbRecord.debugKey = _.get(billsKafkaRow, 'debugKey', null);
        let amountToConsider = null;
        let originalAmount = _.get(updatedDbRecord, ['customer_other_info', 'currentBillAmount'], null);
        let intAmount = _.get(updatedDbRecord, 'due_amount', null);
        if (!intAmount) {
            intAmount = _.get(updatedDbRecord, 'amount', null);
        }
        if (originalAmount != null && intAmount != null && Math.abs(originalAmount - intAmount) < 1) {
            amountToConsider = originalAmount;
        }
        else {
            amountToConsider = intAmount;
        }

        updatedDbRecord.extra = utility.parseExtra(_.get(updatedDbRecord, 'extra', '{}'));
        updatedDbRecord.customer_other_info = utility.parseCustomerOtherInfo(_.get(updatedDbRecord, 'customer_other_info', '{}'));
        billsKafkaRow.customerOtherInfo = utility.parseCustomerOtherInfo(billsKafkaRow.customerOtherInfo);
        billsKafkaRow.extra = utility.parseExtra(_.get(billsKafkaRow, 'extra', '{}'));
        updatedDbRecord.amount = Math.round((amountToConsider - _.get(billsKafkaRow, 'amount', 0)) * 100) / 100;
        updatedDbRecord.payment_date = _.get(billsKafkaRow, ['customerOtherInfo', 'paymentDate'], null);
        updatedDbRecord.status = _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14);
        updatedDbRecord.extra.last_paid_amount = billsKafkaRow.amount;
        updatedDbRecord.extra.updated_data_source = _.get(billsKafkaRow.extra, 'updated_data_source', null);
        updatedDbRecord.source_subtype_2 = 'FULL_BILL';
        updatedDbRecord.customer_other_info.paymentDate = updatedDbRecord.payment_date;
        updatedDbRecord.customer_other_info.currentBillAmount = updatedDbRecord.amount;
        updatedDbRecord.customer_other_info.currentMinBillAmount = (updatedDbRecord.customer_other_info.currentMinBillAmount - _.get(billsKafkaRow, 'amount', 0)) < 0 ? 0 : updatedDbRecord.customer_other_info.currentMinBillAmount - _.get(billsKafkaRow, 'amount', 0);
        updatedDbRecord.customer_other_info.smsSenderID = _.get(billsKafkaRow, ['customerOtherInfo', 'smsSenderId'], null)
        updatedDbRecord.customer_other_info.debugKey = _.get(billsKafkaRow, ['customerOtherInfo', 'debugKey'], null);
        updatedDbRecord.nextBillFetchDate = _.get(existingRecord, 'next_bill_fetch_date', null);
        updatedDbRecord.nextBillFetchStartDate = _.get(existingRecord, 'next_bill_fetch_date', null);
        //extra object add ---
        updatedDbRecord.extra = JSON.stringify(updatedDbRecord.extra);
        updatedDbRecord.customer_other_info = JSON.stringify(updatedDbRecord.customer_other_info);
        return updatedDbRecord;
    }

    _validateExistingRecord(billsKafkaRow, existingRecord) {
        const self = this;
        try {
            const graceDays = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_DAYS'], 5)
            const graceAmount = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_CC_NOTIFY', 'GRACE_AMOUNT'], 5)
            let existingCustomerOtherInfo = utility.parseCustomerOtherInfo(_.get(existingRecord, 'customer_other_info', '{}'));
            let customerOtherInfo = utility.parseCustomerOtherInfo(billsKafkaRow.customerOtherInfo);
            let extra = utility.parseExtra(_.get(billsKafkaRow, 'extra', '{}'));
            const paidAmount = _.get(extra, 'last_paid_amount', null) ? Math.round(extra.last_paid_amount) : null;
            let smsAmount = Math.round(_.get(billsKafkaRow, 'amount', 0));
            let dbpaymentDate = _.get(existingCustomerOtherInfo, 'paymentDate', null);
            dbpaymentDate = dbpaymentDate ? MOMENT(dbpaymentDate).utc() : null;
            let dbDueDate = _.get(existingRecord, 'due_date', null) ? MOMENT(_.get(existingRecord, 'due_date', 0)).utc() : null;
            let paymentDate = _.get(customerOtherInfo, 'paymentDate', null);
            paymentDate = paymentDate ? MOMENT(paymentDate).utc() : null;
            let billFetchDate = _.get(existingRecord, 'bill_fetch_date', null);

            if ((billFetchDate && MOMENT(paymentDate).utc().diff(MOMENT(billFetchDate).utc()) < 0) ||
                (dbpaymentDate && MOMENT(paymentDate).utc().diff(MOMENT(dbpaymentDate).utc()) < 0)) {
                self.L.critical(`Ignore record:: Old payment event for debugKey ${billsKafkaRow.debugKey}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                throw new Error("Old payment event received");
            }

            if (dbDueDate && paymentDate.diff(dbDueDate, 'days') - 30 < 0) {
                if (paidAmount == null || dbpaymentDate == null || (dbpaymentDate && paymentDate.diff(dbpaymentDate, 'days') > graceDays)) {
                    self.L.log('validateExistingRecord', `Validation success for debugKey ${billsKafkaRow.debugKey}`);
                    return;
                }
                if ((paymentDate.diff(dbpaymentDate, 'days') < graceDays) && (Math.abs(paidAmount - smsAmount) > graceAmount)) {
                    self.L.log('validateExistingRecord', `Validation success for debugKey ${billsKafkaRow.debugKey}`);
                    return;
                }
                self.L.critical(`Ignore record:: Bill already paid in last x days or Bill has not generated yet for debugKey ${billsKafkaRow.debugKey}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                throw new Error("Bill already paid in last x days or Bill has not generated yet");
            }
            else {
                self.L.critical(`Ignore record:: Bill already paid in last x days or Bill has not generated yet for debugKey ${billsKafkaRow.debugKey}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
                throw new Error("Bill already paid in last x days or Bill has not generated yet");
            }
            return;
        }
        catch (error) {
            self.L.error(`Ignore record:: Error for record for debugKey ${billsKafkaRow.debugKey}`, error.message);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_CC_BILL_PAID', 'STATUS:CC_BILL_PAID']);
            throw error;
        }
    }
}

module.exports = FindAndUpdateStrategy;