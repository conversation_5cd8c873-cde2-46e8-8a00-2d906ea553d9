"use strict";

import OS from 'os'
import _ from 'lodash'
import <PERSON><PERSON><PERSON> from 'async'
import NotificationModel from '../models/notificationService'
import NotificationLibrary from '../lib/notificationService'
import PATH from 'path'
import FS from 'fs'
import MOMENT from 'moment'

import Q from 'q'

/**
 * Notification service is used to send notification at order level instead of item level
 * Service will fetch messages from configured kafka topics and  club them to a single 
 * notification, We are also keeping data of pending items for 24 hours and notify them 
 * after status update
 * 
 * For now we are not sending any notification for pending items  
 */
class Notification {
    /**
     * @param { object } options Contains configuration and dependencies
     * @param { object } options.L Paytm Logger (lgr) object
     * @param { object } options.config Local config object
     * @param { object } options.INFRAUTILS Contains util libraries like kafka and redis
     */

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.infraUtils = options.INFRAUTILS;

        //Initializing Active or Inactive notification configuration
        self.notification = {};
        self.notification['active'] = {};
        self.notification['inactive'] = {};
        self.notification['merchant'] = {};

        self.defaultConfig = _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'DEFAULT_TEMPLATE'], {});

        //IN status codes allowed for notification {recharge case}
        self.IN_STATUS_CODES = _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'IN_STATUS_CODE'], ['00', '08', '10', '11', '404', '420', '1000', '1000', '2000', '3000']);
        self.productMap = {};
        self.merchantConfiguration = {};

        //Setting ES Index
        self.ES_INDEX = _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'ES_INDEX'], {
            "MIS_INDEX": "recharge_mis"
        });


        //Redis initialization 
        self.redis = new self.infraUtils.cache("REDIS", self.config.REDIS);
        self.redisPrefix = _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'REDIS_PREFIX'], "NSRD_EDU_");
        self.redisTtl = _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'REDIS_TTL'], 1 * 25 * 60 * 60 * 1000);
        self.redisItemKeys = _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'REDIS_ITEM_KEYS'], []);

        //Initializing Library or model objects
        self.notificationModel = new NotificationModel(options, self.redis);
        self.notificationLibrary = new NotificationLibrary(options);

        //Elastic search query to fetch order items
        self.esFetchExpiredItems = PATH.join(__dirname, _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'ES_QUERY_FILE'], '../query/fetchExpiredItems.json'));
        self.timeStampFile = PATH.join('/tmp', _.get(options.config, ['NOTIFICATION_SERVICE_CONFIG', 'TIME_STAMP_FILE'], 'notificationServiceTimeStamp.log'));

        //Initalizing Query we will load these once and keep using with fields value updates
        self.esFetchExpiredItemsQuery = "";

        //Initalizing start or end time for es query to fetch pending records taking window of 2 min
        self.startTimeStamp = "";
    }

    /*
     * Starting point of service, intializing notification consumer and refresh Map workflow
     */
    start() {
        let self = this;
        self.L.info("Notification Service : start(): Starting Notification Service");

        ASYNC.waterfall([
            (callback) => {
                // connect with redis server
                this.redis.connect((err, data) => {
                    callback(err);
                })
            },
            (callback) => {
                self.L.info("Notification Service: start(): Load all ES query and also initialize index if not there");
                try {
                    self.esFetchExpiredItemsQuery = JSON.parse(FS.readFileSync(self.esFetchExpiredItems));
                    self.readTimeStamp((error) => {
                        callback(error);
                    });
                } catch (ex) {
                    callback(ex);
                }
            },
            (callback) => {
                self._loadNotificationConfiguration((error) => {
                    callback(error);
                })
            },
            (callback) => {
                self._refreshNotificationConfiguration((error) => {
                    callback(error);
                })
            },
            (callback) => {
                self._processPendingNotification((error) => {
                    callback(error);
                })
            },
            (callback) => {
                self._initializeNotificationConsumer((error) => {
                    callback(error);
                });
            }
        ], (error) => {
            if (error) {
                self.L.critical("Notification Service:start(): Failed to start notification consumer", error);
                process.exit(0);
            }
        })
    }

    /**
     * @param {*} cb callback function 
     */
    readTimeStamp(cb) {
        let self = this,
            timeStamp = "";

        self.L.info("Notification Service: readTimeStamp(): Reading time stamp file ");
        try {
            FS.exists(self.timeStampFile, (exists) => {
                if (exists) {
                    timeStamp = FS.readFileSync(self.timeStampFile, 'utf8').replace(/\n/g, '');
                    timeStamp = MOMENT(timeStamp, "YYYY-MM-DD HH:mm:ss", true);

                    if (timeStamp.isValid()) {
                        self.startTimeStamp = timeStamp;
                    } else {
                        timeStamp = MOMENT().subtract(1, "days").subtract(2, "minutes").format("YYYY-MM-DD HH:mm:ss");
                        self.startTimeStamp = timeStamp;
                        FS.writeFileSync(self.timeStampFile, self.startTimeStamp);
                    }
                } else {
                    timeStamp = MOMENT().subtract(1, "days").subtract(2, "minutes").format("YYYY-MM-DD HH:mm:ss");
                    self.startTimeStamp = timeStamp;
                    FS.writeFileSync(self.timeStampFile, self.startTimeStamp)
                }
                cb();
            });
        } catch (ex) {
            self.L.critical("Notification Service: readTimeStamp(): Failed to read time stamp file =>", ex);
            cb(ex);
        }
    }

    /**
     * Here we will load all configiration from notification_configuration,
     * and notification_template table 
     * Initially we will load data in memory later in every 15 minutes we keep updating
     * existing data 
     * 
     * @param {*} cb callback function 
     */

    _loadNotificationConfiguration(cb) {
        let self = this,
            merchantData, notificationConfigiration, notificationTemplates;

        self.L.info("Notification Service: _loadNotificationConfiguration(): Loading all notification configurations");

        ASYNC.waterfall([
            //Method will load operator map from CVR table
            (callback) => {
                self.L.info("Notification Service: _loadNotificationConfiguration(): Loading catalog vertical recharge table product map");
                self.notificationModel.createOperatorProductMap((error, productMap) => {
                    if (!error && !_.isEmpty(productMap)) {
                        self.productMap = productMap;
                    }
                    callback(error);
                })
            },

            //Method will load merchant configuration 
            (callback) => {
                self.L.info("Notification Service: _loadNotificationConfiguration(): Loading merchant configuration table ");
                self.notificationModel.loadMerchantConfiguration((error, merchantMap) => {
                    merchantData = merchantMap;
                    callback(error);
                })
            },

            //Method will load notification configuration table data
            (callback) => {
                self.L.info("Notification Service: _loadNotificationConfiguration(): Loading notification configuration");
                self.notificationModel.loadNotificationConfiguration((error, configiration) => {
                    notificationConfigiration = configiration;
                    callback(error);
                });
            },

            //Method will load template configuration table data
            (callback) => {
                self.L.info("Notification Service: _loadNotificationConfiguration(): Loading template configuration");
                self.notificationModel.loadNotificationTemplateConfiguration((error, templates) => {
                    notificationTemplates = templates;
                    callback(error);
                });
            },

            //Method will prepare data in required format
            (callback) => {
                if (_.isArray(notificationConfigiration) && _.isArray(notificationTemplates)) {
                    self.L.info("Notification Service: _loadNotificationConfiguration(): No of configuration and templates loaded :", notificationConfigiration.length, notificationTemplates.length);

                    let notification = self.notificationLibrary.prepareNotificationConfiguration(notificationConfigiration, notificationTemplates, self.config, merchantData);

                    if (!_.isEmpty(notification)) {
                        self.notification['active'] = _.get(notification, 'active', null) ? _.get(notification, 'active', null) : self.notification['active'];
                        self.notification['inactive'] = _.get(notification, 'inactive', null) ? _.get(notification, 'inactive', null) : self.notification['inactive'];
                        self.notification['merchant'] = _.get(notification, 'merchant', null) ? _.get(notification, 'merchant', null) : self.notification['merchant'];
                    }
                } else {
                    self.L.error("Notification Service: _loadNotificationConfiguration(): Failed to Refresh notification configuration");
                }
                callback();
            }
        ], (error) => {
            if (error) {
                self.L.critical("Notification Service: _loadNotificationConfiguration(): Failed to load notification or template configuration", error);
            }
            cb(error);
        })
    }

    /**
     * @param {function} cb callback function
     * Here we will keep reloading notification configuration in every 15 minutes
     */

    _refreshNotificationConfiguration(cb) {
        let self = this;
        self.L.info("Notification Service:_refreshNotificationConfiguration(): starting reload process for template configuration");

        setInterval(() => {
            self._loadNotificationConfiguration((error) => {
                if (!error) {
                    self.L.info("Notification Service:_refreshNotificationConfiguration(): notification configuration realoded successfully");
                } else {
                    self.L.critical("Notification Service:_refreshNotificationConfiguration(): Error in reloading notification configuration", error);
                }
            });
        }, _.get(self.config, ['NOTIFICATION_SERVICE_CONFIG', 'REFRESH_INTERVAL'], 15 * 60 * 1000));
        return cb();
    }

    /**
     * Method will start process in every 2 minutes to send all pending notification (item level)
     * We are also deleting all entries from REDIS
     * @param {*} cb | callback function
     */
    _processPendingNotification(cb) {
        let self = this;
        self.L.info("Notification Service:_processPendingNotification(): Starting Process to Send Pending Notification");

        setInterval(() => {
            self._sendPendingNotification((error) => {
                if (!error) {
                    self.L.info("Notification Service:_processPendingNotification(): All Notification sent successfully Expired in last 2 minute ");
                } else {
                    self.L.critical("Notification Service:_processPendingNotification(): Error in sending pending notification", error);
                }
            });
        }, _.get(self.config, ['NOTIFICATION_SERVICE_CONFIG', 'PENDING_INTERVAL'], 2 * 60 * 1000));
        return cb();
    }

    /**
     * Method will fetch all pending notification expired in last two minutes
     * Send notification for all items and 
     * @param {*} cb | callback function
     */
    _sendPendingNotification(cb) {
        let self = this,
            pending_orders, order_items, order_ids;

        self.L.info("Notification Service: _sendPendingNotification(): Starting with pending notification expired in last 24 hours");

        ASYNC.waterfall([

            //Fetching pending Records from ES
            (callback) => {
                let query = self.esFetchExpiredItemsQuery,
                    esIndex = `${self.ES_INDEX.MIS_INDEX}_${MOMENT().format("MM_YYYY")},${self.ES_INDEX.MIS_INDEX}_${MOMENT().subtract(1, 'months').format("MM_YYYY")}`,
                    gte = MOMENT(self.startTimeStamp),
                    lte = MOMENT().subtract(1, "days");

                self.startTimeStamp = lte; //Updating start time stamp
                _.set(query, "query.bool.filter[0].range.timestamps_init.gte", gte.unix());
                _.set(query, "query.bool.filter[0].range.timestamps_init.lte", lte.unix());

                self.L.info("Notification Service: _sendPendingNotification : ES query :", JSON.stringify(query));
                self.notificationModel.getEsData((error, pendingOrders) => {
                    if (!error) {
                        pending_orders = pendingOrders;
                        self.L.info("Notification: _sendPendingNotification(): Pending Records fetched successfully ", pending_orders.length);
                    } else {
                        self.L.error("Notification: _sendPendingNotification(): Failed to fetch Pending Records ", error);
                    }
                    callback(error);
                }, esIndex, query);
            },

            //Fetch item data for all order ids from redis
            (callback) => {
                self.L.info("Notification Service: _sendPendingNotification():  fetching all records form redis respective to order ids");

                self._fetchRedisRecords((error, orderItems, orderIds) => {
                    order_items = orderItems;
                    order_ids = orderIds;

                    callback(error);
                }, pending_orders);
            },

            //Here we will send notification 
            (callback) => {

                if (_.isArray(order_items) && order_items.length > 0) {
                    self.L.info("Notification Service: _sendPendingNotification():  Pending records to notify ", order_items.length);

                    let customerInfoMap = self.notificationLibrary.appendCustomerInformation(pending_orders);
                    let order_item_combined = self.notificationLibrary.clubOrders(customerInfoMap, order_items, self.IN_STATUS_CODES, self.productMap);
                    let notifyItems = self.notificationLibrary.collectAllTemplates(order_item_combined, self.notification, self.config);

                    if (_.isArray(notifyItems) && notifyItems.length > 0) {
                        self.L.info("Notification Service: _sendPendingNotification(): notifications count =>", notifyItems.length);

                        self._sendNotificationRequest((error) => {
                            callback(error);
                        }, notifyItems);
                    } else {
                        self.L.info("Notification Service: _sendPendingNotification(): no item to nofify");
                        callback();
                    }
                } else {
                    callback();
                }
            },

            //Here we will delete items from redis
            (callback) => {

                if (_.isArray(order_ids) && order_ids.length > 0) {
                    self.L.info("Notification Service: _sendPendingNotification(): Deleting order ids from redis=>", order_ids.length);
                    self._deleteRedisData((error) => {
                        callback(error);
                    }, order_ids);
                } else {
                    self.L.info("Notification Service: _sendPendingNotification(): No item for deletion ");
                    callback();
                }

                self.updateTimeStamp(); // here we will update time stamp for next process
            }
        ], (error) => {
            if (error) {
                self.L.critical("Notification Service: _sendPendingNotification(): Failed to send pending notification", error);
            }
            cb(error);
        })
    }

    /**
     * Method is used to update existing time stamp for next iteration
     */
    updateTimeStamp() {
        let self = this,
            updatedTimeStamp = MOMENT(self.startTimeStamp).format("YYYY-MM-DD HH:mm:ss");
        self.startTimeStamp = updatedTimeStamp;

        self.L.info("Notification Service: updateTimeStamp(): Time Stamp updated =>", updatedTimeStamp);
        try {
            FS.writeFileSync(self.timeStampFile, updatedTimeStamp);
        } catch (ex) {
            self.L.critical("Notification Service: updateTimeStamp(): Failed to updated time stamp in log file", ex);
        }
    }


    /**
     * Method will data from Redis 
     * @param {*} cb | callback function
     * @param {*} order_ids | contains array or order ids
     */
    _deleteRedisData(cb, order_ids) {
        let self = this;
        self.L.info('Notification Service: _deleteRedisData(): Method will start deleting pending orders from redis');

        ASYNC.each(
            order_ids,
            (order_id, next) => {
                self.notificationModel.deleteRedisData(next, {
                    "orderInfo_order_id": order_id
                }, self.redisPrefix);
            },
            (err, result) => {
                if (err) {
                    self.L.error('Notification Service: _deleteRedisData()', 'Error while fetching data from redis ', err);
                }
                cb(err);
            }
        );
    }

    /**
     * Method will fetch pending order items from redis 
     * @param {*} cb | callback function
     * @param {*} pending_orders | pending order list
     */
    _fetchRedisRecords(cb, pending_orders) {
        let self = this,
            orderIds = [],
            orderItems = [];

        orderIds = _.uniq(_.map(pending_orders, 'orderInfo_order_id'));

        self.L.info("Notification Service : _fetchRedisRecords(): collecting  data for orders =>", orderIds.length);
        // iterate for each record in parallel

        ASYNC.each(
            orderIds,
            (order_id, next) => {
                self.notificationModel.getRedisData((error, items) => {
                    items.forEach(item => {
                        _.set(item, "orderInfo_order_id", order_id);
                    });

                    orderItems.push.apply(orderItems, items);
                    next(error);
                }, {
                    "orderInfo_order_id": order_id
                }, self.redisPrefix);
            },
            (err, result) => {
                if (err) {
                    self.L.error('Notification Service: _fetchRedisRecords()', 'Error while fetching data from redis ', err);
                }
                cb(err, orderItems, orderIds);
            }
        );

    }

    /**
     * Method will send notification in ASYNC
     * @param {*} cb | callback function
     * @param {*} notifiableItems | notification items
     */
    _sendNotificationRequest(cb, notifiableItems) {
        let self = this;
        // iterate for each record in parallel
        ASYNC.each(
            notifiableItems,
            (item, next) => {
                self.notificationLibrary.sendNotification(next, item);
            },
            (err, result) => {
                if (err) {
                    self.L.error('Notification Service: _sendNotificationRequest()', 'Error while processing record for notification', err);
                }
                cb(); //Finished with current notification
            }
        );
    }

    /**
     * @param {function} cb callback function
     * initializing kafka notification consumer 
     */
    _initializeNotificationConsumer(cb) {
        let self = this;

        self.L.info("Notification:_initializeNotificationConsumer():Service started on topics::" + _.get(self.config.KAFKA, 'SERVICES.NOTIFICATION.CONSUMER_TOPICS'))
        try {
            self.consumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'),
                "groupId": 'notification_' + OS.hostname(),
                "topics": _.get(self.config.KAFKA, 'SERVICES.NOTIFICATION.CONSUMER_TOPICS'),
                "id": "notification-consumer",
                "fromOffset": "earliest"
            });

            self.consumer.initConsumer(self._processNotificationData.bind(self), (error) => {
                if (!error)
                    self.L.info("Notification Service:_initializeNotificationConsumer() : Notification consumer Configured");
                return cb(error);
            });
        } catch (error) {
            cb(error);
        }
    }

    /**
     * Method will process data based on request type received Exp RECHARGE, VALIDATION
     * @param {object} data contains recharge topic data 
     */
    _processNotificationData(data) {
        let self = this,
            rechargeData = null;

        if (data && data.value && typeof (data.value) == 'string') {
            try {
                rechargeData = JSON.parse(data.value);
                self.L.info("Notification Service: _processNotificationData(): Start processing recharges data for notification", JSON.stringify(data));
                
                self._processRechargeData(rechargeData);
            } catch (error) {
                self.L.error("Notification Service: _processNotificationData():", "Failed to parse recharges data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
            }
        } else {
            self.L.error("Notification Service: _processNotificationData():", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
        }
    }

    /**
     * Method will  only process reqType = RECHARGE data for notification
     * @param {*} rechargeData contains kafka topic recharge data
     */
    _processRechargeData(rechargeData) {
        let self = this,
            status = self.notificationLibrary.isRechargeDataNotifiable(rechargeData, self.IN_STATUS_CODES),
            order_id = _.get(rechargeData, 'orderInfo_order_id', null),
            item_id = _.get(rechargeData, 'orderInfo_item_id', null),
            notification_status = false,
            deleteStatus = false,
            createKey = true;

        if (status != 2) {
            self.L.info("Notification Service: _processRechargeData(): Start Notification process for order_id", order_id);

            ASYNC.waterfall([
                (callback) => {
                    if (status == 0) {
                        let reqest_status = self.notificationLibrary.isStatus(rechargeData, self.IN_STATUS_CODES);

                        notification_status = true;
                        if (reqest_status && order_id) {
                            deleteStatus = false;
                            self.L.info("Notification Service: _processRechargeData(): Recharge data contains only one item");
                            callback(null, rechargeData, []); //passing order item array blank
                        } else {
                            self.L.error("either order_id or request type is invalid: order_id, request_type ", order_id, reqest_status);
                            callback("either order_id or request type is invalid: order_id, request_type ");
                        }
                    } else {
                        self.L.info("Notification Service : _processRechargeData(): Checking data in Redis for notification order_id=>", order_id);

                        self.notificationModel.getRedisData((error, order_items) => {
                            if (!error && _.isArray(order_items)) {
                                self.L.info("Notification Service: _processRechargeData(): No of items fetched from Redis:", order_items.length);

                                //Adding order id at item level
                                order_items.forEach(item => {
                                    _.set(item, "orderInfo_order_id", order_id);
                                });

                                notification_status = self.notificationLibrary.canNotify(order_items, rechargeData);
                                deleteStatus = notification_status ? true : false;

                                if (_.isArray(order_items) && order_items.length > 0 && !notification_status) createKey = false;
                            }
                            callback(error, rechargeData, order_items);
                        }, rechargeData, self.redisPrefix);
                    }
                },

                //Here we will send notification or also set data in Redis
                (rechargeData, order_items, callback) => {
                    self.L.info("Notification Service: _processRechargeData():  order notification_status :", notification_status);

                    if (notification_status) {
                        order_items.push(rechargeData); //Pushing current recharge data also in item list 
                        let customerInfoMap = self.notificationLibrary.appendCustomerInformation([rechargeData]);

                        let order_item_combined = self.notificationLibrary.clubOrders(customerInfoMap, order_items, self.IN_STATUS_CODES, self.productMap);
                        let notifyItems = self.notificationLibrary.collectAllTemplates(order_item_combined, self.notification, self.config);

                        if (_.isArray(notifyItems) && notifyItems.length > 0) {
                            self.L.info("Notification Service: _processRechargeData(): sending notifications ", notifyItems.length);

                            self._sendNotificationRequest((error) => {
                                callback(error, rechargeData);
                            }, notifyItems);
                        } else {
                            self.L.info("Notification Service: _processRechargeData(): No item for notifications ");
                            callback(null, rechargeData);
                        }
                    } else {
                        self.notificationModel.setRedisData((err) => {
                            if (!err) {
                                self.L.info("Notification Service: _processRechargeData(): recharge Data set in Redis Successfully: order_id, item_id: ", order_id, item_id);
                            } else {
                                self.L.error("Notification Service: _processRechargeData(): Failed to Set recharge data in Redis=>", err);
                            }
                            callback(null, rechargeData)
                        }, rechargeData, order_items, self.redisPrefix, self.redisTtl, self.redisItemKeys, createKey);
                    }
                },

                //Here we will delete items from ES in case of multiple items
                (rechargeData, callback) => {
                    if (deleteStatus) {
                        self.L.info("Notification Service: _processRechargeData(): Deleting order items");
                        self.notificationModel.deleteRedisData((error) => {
                            callback(error);
                        }, rechargeData, self.redisPrefix);
                    } else {
                        self.L.info("Notification Service: _processRechargeData(): No item for deletion ");
                        callback(null);
                    }
                }
            ], (error) => {
                if (error) {
                    self.L.critical("Notification Service : _processRechargeData(): Failed to send notification", error);
                }
            })
        } else {
            self.L.error("Notification: _processRechargeData(): Recharge topic data not in notifiable state: order_id = ", order_id);
        }
    }
    
    suspendOperations(){
        var self        = this,
        deferred = Q.defer();

        self.L.log(`Notification::suspendOperations kafka consumer shutdown initiated`);
    
        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`Notification::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`Notification::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`Notification::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`Notification::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default Notification;