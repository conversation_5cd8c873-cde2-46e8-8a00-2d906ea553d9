import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator';
import MOMENT from 'moment';
import REQUEST from 'request';
import <PERSON>Y<PERSON> from 'async';
import _ from 'lodash';
import utility from '../lib';
import OS, { type } from 'os';
import Q from 'q';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';

class SMSParsingValidator {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_VALIDATOR', 'BATCHSIZE'], 10) : 100;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_VALIDATOR', 'DELAY'], 5 * 60 * 1000) : 0;
        this.commonLib = new utility.commonLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.topicConfigMap = {};
        this.utility = require('../lib');
        this.activePidLib = options.activePidLib;
    }

    async start() {
        let self = this;
        self.L.log('start', 'Going to configure Kafka..');

        await self.configureKafka();
        self.L.log('SMS_PARSING_VALIDATOR :: start', 'Kafka Configured successfully !!');
    }

    async configureKafka() {
        const self = this;
        try {
            self.L.log('configureKafka', 'Configuring Kafka publishers and consumers');

            // Configure Kafka publishers
            await this.configureKafkaPublishers();

            return new Promise((resolve, reject) => {
                self.consumer = new KafkaConsumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_VALIDATOR.HOSTS'),
                    "groupId": 'smsParsingValidator-consumer',
                    "topics": _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'COMMON', 'kafkaTopics'], ["SMS_PARSER_ELECTRICITY","SMS_PARSING_LOANEMI"]),
                    "id": `smsParsingValidator-consumer_${OS.hostname()}_${process.pid}`,
                    "maxBytes": _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.GENERAL_SMS_PARSER.BATCHSIZE', 1000000),
                    sessionTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT', 2 * 60 * 1000),
                    maxProcessTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.GENERAL_SMS_PARSER_TIMEOUT', 30 * 60 * 1000)
                });

                self.consumer.initConsumer((records, resolveOffset, topic, partition, cb) => {
                    this.processRecords(records, resolveOffset, topic, partition, cb);
                }, (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'TYPE:CONSUMER_INIT']);
                        self.L.error("configureKafka", `Failed to initialize Kafka consumer: ${error}`);
                        reject(error);
                    } else {
                        let topics=_.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'COMMON', 'kafkaTopics'], ["SMS_PARSER_ELECTRICITY","SMS_PARSING_LOANEMI"]);
                        self.L.log("configureKafka", `Kafka consumer initialized successfully for topics: ${topics.join(', ')}`);
                        resolve();
                    }
                });
            });

        } catch (error) {
            self.L.error('configureKafka', `Error configuring Kafka: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'TYPE:KAFKA_CONFIG']);
            throw error;
        }
    }

    async configureKafkaPublishers() {
        let self = this;

        // Configure DWH Kafka publisher
        self.dwhSmsPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_VALIDATOR_DWH.HOSTS')
        });

        await new Promise((resolve, reject) => {
            self.dwhSmsPublisher.initProducer('high', (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'TYPE:DWH_PUBLISHER']);
                    self.L.error('configureKafkaPublishers', `Error initializing DWH publisher: ${error}`);
                    reject(error);
                } else {
                    self.L.log('configureKafkaPublishers', 'DWH publisher initialized successfully');
                    resolve();
                }
            });
        });
        
        // Configure FFR Hit Topic publisher
        const ffrHitHosts = _.get(self.config.KAFKA, 'TOPICS.FFR_HIT_TOPIC.HOSTS', _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_VALIDATOR_DWH.HOSTS'));
        self.L.log('configureKafkaPublishers', `Initializing FFR Hit Publisher with hosts: ${ffrHitHosts}`);
        
        self.ffrHitPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": ffrHitHosts
        });

        await new Promise((resolve, reject) => {
            self.ffrHitPublisher.initProducer('high', (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'TYPE:FFR_HIT_PUBLISHER']);
                    self.L.error('configureKafkaPublishers', `Error initializing FFR Hit publisher: ${error}`);
                    reject(error);
                } else {
                    self.L.log('configureKafkaPublishers', 'FFR Hit publisher initialized successfully');
                    resolve();
                }
            });
        });
        
        // Verify that the publishers are ready
        self.L.log('configureKafkaPublishers', `Publishers initialized: DWH=${!!self.dwhSmsPublisher}, FFR=${!!self.ffrHitPublisher}`);
    }

    async processRecords(records, resolveOffset, topic, partition, cb) {
        let self = this;
        let startTime = new Date().getTime();
        let lastMessage;

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            //self.consumer._pauseConsumer();
        } else {
            self.L.critical('SMS_PARSING_VALIDATOR :: processRecords', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return cb();
        }

        self.L.log('SMS_PARSING_VALIDATOR :: processRecords', `Processing ${records.length} records from topic ${topic}`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:SMS_PARSING_VALIDATOR', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW', `TOPIC:${topic}`]);

        try {
            let currentPointer = 0;
            const chunkSize = 10; // Process 10 records at a time

            // Process records in chunks
            await new Promise((resolve, reject) => {
                ASYNC.whilst(
                    () => currentPointer < records.length,
                    (callback) => {
                        let nextChunk = records.slice(currentPointer, currentPointer + chunkSize);
                        currentPointer += chunkSize;

                        self.processBatch(nextChunk, () => {
                            setTimeout(() => callback(), 2);
                        });
                    },
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });

            // Check for duplicate offsets
            self.kafkaConsumerChecks.findOffsetDuplicates("SMSParsingValidator", records, topic, partition);

            // Commit offset
            await resolveOffset(lastMessage.offset);
            self.L.log('SMS_PARSING_VALIDATOR :: processRecords', `Commit success for offset: ${lastMessage.offset}, topic: ${topic}, partition: ${partition}`);

            // Log execution time
            let endTime = new Date().getTime();
            let executionTime = Math.round((endTime - startTime) / 1000);
            self.L.log('SMS_PARSING_VALIDATOR :: processRecords', `Execution time: ${executionTime} seconds`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:SMS_PARSING_VALIDATOR", "TIME_TAKEN:" + executionTime]);

            

            // Add delay between batches if configured
            setTimeout(() => cb(), self.kafkaBatchDelay);

        } catch (error) {
            self.L.error('SMS_PARSING_VALIDATOR :: processRecords', `Error processing records: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'SOURCE:MAIN_FLOW', `ERROR:${error.message}`]);
            return cb();
        }
    }

    processBatch(records, done) {
        let self = this;
        let currentPointer = 0;

        ASYNC.whilst(
            () => currentPointer < records.length,
            (callback) => {
                let record = records[currentPointer];
                currentPointer += 1;

                self.processRecord(record, () => {
                    setTimeout(() => callback(), 1);
                });
            },
            (err) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:BATCH_PROCESSING_ERROR', 'ERROR:' + err.message]);
                    self.L.error("SMS_PARSING_VALIDATOR :: processBatch", `Error processing batch: ${err}`);
                }
                return done();
            }
        );
    }

    processRecord(record, done) {
        let self = this;
        let published_time = Number(_.get(record, 'timestamp', null));

        try {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                'STATUS:TRAFFIC',
                `PARTITION:${_.get(record, 'partition', null)}`,
                `TOPIC:${_.get(record, 'topic', null)}`
            ]);

            // Parse the record
            let parsedRecord = JSON.parse(_.get(record, 'value', null));
            self.L.log('SMS_PARSING_VALIDATOR ::', `Parsed record: ${JSON.stringify(parsedRecord)}`);

            if (!parsedRecord.data) {
                self.L.critical('SMS_PARSING_VALIDATOR :: processRecord', `Invalid Kafka record received. 'data' key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                return done();
            }

            // Process each data item in the record
            ASYNC.eachSeries(
                parsedRecord.data,
                (smsData, next) => {
                    self.L.log('SMS_PARSING_VALIDATOR ::', `smsData: ${JSON.stringify(smsData)}`);
                    // Get predicted category
                    const predictedCategory = _.get(smsData, 'predicted_category', '').toLowerCase();

                    let level_2_category=_.get(smsData, 'level_2_category', null);

                    let level_2_category_from_config=_.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR','LEVEL_2_CATEGORY',predictedCategory], ['1']);
                    let ignore_level_2_category=_.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR','COMMON','IGNORE_LEVEL_2_CATEGORY'], ['1']);
                    self.L.log('SMS_PARSING_VALIDATOR ::', `smsData: level_2_category`,level_2_category);
                    if(level_2_category_from_config && !level_2_category_from_config.includes(level_2_category) && !ignore_level_2_category.includes(predictedCategory)){
                        return next();
                    }

                    self.L.log('SMS_PARSING_VALIDATOR ::', `smsData:processed level_2_category`,level_2_category);
                    
                    
                    const customerId=(typeof smsData.cId === 'number') ? smsData.cId : (typeof smsData.cId === 'string' && VALIDATOR.isNumeric(smsData.cId)) ? parseInt(smsData.cId) : null;
                    
                    if (!customerId) {
                        self.L.critical('SMS_PARSING_VALIDATOR :: processRecord', `Invalid customerId in Kafka record`, record);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        return next();
                    }
                    let kafkaTopic= _.get(parsedRecord, 'kafka_topic[0]','');
                    let operator = _.toLower(smsData.operator);
                    /*let operator = _.toLower(smsData.operator);
                        //operator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`, 'GENERIC_DWH_OPERATOR_MAPPING', operator], _.get(smsData, 'operator', null)));
                        const primaryMappingConfig = `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`;
        
                        const fallbackMapping = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'FALLBACK_MAPPINGS', primaryMappingConfig], null);
                        
                        self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', 
                            `Looking up operator mapping for ${operator} in primary config ${primaryMappingConfig} with fallbacks: ${fallbackMapping}`);
                        
                        let mappedOperator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', primaryMappingConfig, 'GENERIC_DWH_OPERATOR_MAPPING', operator], null));
                        
                        if (!mappedOperator) {
                            if(fallbackMapping) {
                                mappedOperator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', fallbackMapping, 'DWH_OPERATOR_MAPPING', operator], null));
                                self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', `Fallback mapping found for operator: ${mappedOperator}`);

                                if(!mappedOperator){
                                    mappedOperator =  _.get(self.config, ['DYNAMIC_CONFIG',fallbackMapping, operator, 'RU_MAPPED_OPERATOR'], null);
            
                                }
                                if(!mappedOperator){
                                    mappedOperator =  _.get(self.config, ['DYNAMIC_CONFIG',fallbackMapping, operator, 'DEMERGER_OPERATOR'], null);
            
                                }
    

                            }

                        }
                        
                        // If still no mapping found, use the original operator
                        operator = mappedOperator || operator;
                        self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', `Final mapped operator: ${operator}`);*/
                        
                    const enabledCategories = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR','COMMON', 'ENABLED_CATEGORIES'], []);
                    const isCategoryEnabled = enabledCategories.includes(predictedCategory);

                    if (!isCategoryEnabled) {
                        self.L.log('SMS_PARSING_VALIDATOR :: processRecord',
                            `Skipping validation for predicted category: ${predictedCategory} - Category not enabled`);

                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                            'STATUS:SKIPPED',
                            "REASON:CATEGORY_DISABLED",
                            "CATEGORY:" + predictedCategory
                        ]);

                        return next();
                    }

                    const enabledCustomerIdsServiceWise = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'ENABLED_CUSTOMER_IDS', predictedCategory], []);
                    const enabledCustomerIdsOperatorWise = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'ENABLED_CUSTOMER_IDS', operator], []);

                    const isExplicitlyEnabled =
                        _.includes(enabledCustomerIdsServiceWise, customerId) ||
                        _.includes(enabledCustomerIdsServiceWise, customerId.toString()) ||
                        _.includes(enabledCustomerIdsOperatorWise, customerId) ||
                        _.includes(enabledCustomerIdsOperatorWise, customerId.toString());

                    if (isExplicitlyEnabled) {
                        self.L.log('SMS_PARSING_VALIDATOR :: processRecord',
                            `Processing validation for customer ID: ${customerId} in category: ${predictedCategory} - Customer ID explicitly enabled`);
                    }
                    else {
                        
                        const percentageRolloutServiceWise = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'PERCENTAGE_ROLLOUT', predictedCategory], 1);
                        const percentageRolloutOperatorWise = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'PERCENTAGE_ROLLOUT', operator], 15);

                        if (percentageRolloutServiceWise <= 0 && percentageRolloutOperatorWise<=0 && !isExplicitlyEnabled) {
                            self.L.log('SMS_PARSING_VALIDATOR :: processRecord',
                                `Skipping validation for customer ID: ${customerId} in category: ${predictedCategory} - Not in rollout percentage`);

                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                                'STATUS:SKIPPED',
                                "REASON:NOT_IN_PERCENTAGE_ROLLOUT",
                                "CATEGORY:" + predictedCategory
                            ]);

                            return next();
                        }

                        const customerIdString = customerId.toString();
                        const lastTwoDigits = customerIdString.length >= 2
                            ? parseInt(customerIdString.slice(-2))
                            : parseInt(customerIdString);

                        if (lastTwoDigits >= percentageRolloutOperatorWise && lastTwoDigits>=percentageRolloutServiceWise && !isExplicitlyEnabled) {
                            self.L.log('SMS_PARSING_VALIDATOR :: processRecord',
                                `Skipping validation for customer ID: ${customerId} in category: ${predictedCategory} - ` +
                                `Last 2 digits ${lastTwoDigits} outside rollout percentage:operatorwise ${percentageRolloutOperatorWise} and  servicewise ${percentageRolloutServiceWise}`);

                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                                'STATUS:SKIPPED',
                                "REASON:NOT_IN_PERCENTAGE_ROLLOUT",
                                "CATEGORY:" + predictedCategory
                            ]);

                            return next();
                        }

                        self.L.log('SMS_PARSING_VALIDATOR :: processRecord',
                            `Processing validation for customer ID: ${customerId} in category: ${predictedCategory} - ` +
                            `Last 2 digits ${lastTwoDigits} within rollout percentage:operatorwise ${percentageRolloutOperatorWise} and servicewise${percentageRolloutServiceWise}`);
                    }

                    // Add processing start time and source topic for tracking
                    _.set(smsData, 'processing_start_time', new Date().getTime());
                    _.set(smsData, 'published_time', published_time);
                    _.set(smsData, 'source_topic', _.get(parsedRecord, 'kafka_topic[0]'));

                    // Instead of calling validateWithFFR, parse the SMS data and publish to FFR_HIT_TOPIC
                    self.parseSMSData(smsData, (err, parsedData) => {
                        if (err) {
                            self.L.error('SMS_PARSING_VALIDATOR :: parseSMSData', `Error parsing SMS data: ${err}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                                'STATUS:ERROR',
                                'TYPE:PARSING_EXCEPTION',
                                'ERROR:' + err.message
                            ]);
                            return next();
                        }
                        
                        // Publish to FFR_HIT_TOPIC
                        const ffrHitPayload = {
                            smsData: smsData,
                            parsedData: parsedData
                        };
                        
                        // Add more detailed logging before publishing
                        self.L.log('SMS_PARSING_VALIDATOR :: publishToFfrHitTopic', 
                            `Attempting to publish to FFR_HIT_TOPIC with payload size: ${JSON.stringify(ffrHitPayload).length} bytes`);
                        
                        // Check if the publisher is initialized
                        if (!self.ffrHitPublisher) {
                            self.L.error('SMS_PARSING_VALIDATOR :: publishToFfrHitTopic', 
                                'FFR Hit Publisher is not initialized');
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                                'STATUS:ERROR',
                                "TYPE:FFR_HIT_PUBLISHER_NOT_INITIALIZED"
                            ]);
                            return next();
                        }
                        
                        self.ffrHitPublisher.publishData([{
                            topic: "FFR_HIT_TOPIC",
                            messages: JSON.stringify(ffrHitPayload)
                        }], (error) => {
                            if (error) {
                                // Improved error logging with more details
                                self.L.error('SMS_PARSING_VALIDATOR :: publishToFfrHitTopic', 
                                    `Error publishing to FFR_HIT_TOPIC: ${JSON.stringify(error)}`, 
                                    error.stack || 'No stack trace');
                                
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                                    'STATUS:ERROR',
                                    "TYPE:FFR_HIT_TOPIC_PUBLISH_FAILURE",
                                    "SERVICE:" + parsedData.service,
                                    "OPERATOR:" + parsedData.operator,
                                    "ERROR_TYPE:" + (error.name || "Unknown")
                                ]);
                            } else {
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                                    'STATUS:SUCCESS',
                                    "TYPE:FFR_HIT_TOPIC_PUBLISH",
                                    "SERVICE:" + parsedData.service,
                                    "OPERATOR:" + parsedData.operator
                                ]);
                                self.L.log('SMS_PARSING_VALIDATOR :: publishToFfrHitTopic', 'Data published successfully to FFR_HIT_TOPIC',JSON.stringify(ffrHitPayload));
                            }
                            next();
                        });
                    });
                },
                (err) => {
                    if (err) {
                        self.L.error('SMS_PARSING_VALIDATOR :: processRecord', `Error processing record data: ${err}`);

                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                            'STATUS:ERROR',
                            "TYPE:RECORD_PROCESSING_FAILURE",
                            "ERROR:" + err
                        ]);
                    }
                    done();
                }
            );

        } catch (error) {
            self.L.critical('SMS_PARSING_VALIDATOR :: processRecord', `Invalid Kafka record received: ${error}`, record);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return done();
        }
    }

    parseSMSData(smsData, callback) {
        let self = this;

        try {
            let category = _.get(smsData, 'predicted_category', 'UNKNOWN').toLowerCase();
            let parsedData = {};
            let config = null;

            parsedData.customerId = (typeof smsData.cId === 'number') ? smsData.cId : (typeof smsData.cId === 'string' && VALIDATOR.isNumeric(smsData.cId)) ? parseInt(smsData.cId) : null;
            parsedData.smsDateTime = _.get(smsData, 'smsDateTime');
            parsedData.rawCategory = category;

            let kafkaTopic= _.get(smsData, 'source_topic','');

            config = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', kafkaTopic], null);
            if (!config) {
                config = _.get(self.config, ['DYNAMIC_CONFIG', 'GENERIC_SMS_PARSING_CONFIG', category], null);
            }

            let noDetailsPathRequired= _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR','COMMON', 'NO_DETAILS_PATH_REQUIRED'], ['1']);

            


            let detailsPath;
            let detailsObject;
            let fieldMappings;


            if (_.has(config, 'detailsPath')) {

                detailsPath = config.detailsPath;
                detailsObject = _.get(smsData, detailsPath, {});
                fieldMappings = _.get(config, 'fieldMappings', {});


                parsedData.service = category;
                parsedData.category = category;
            }
            else if(noDetailsPathRequired.includes(category)){
                fieldMappings = _.get(config, 'fieldMappings', {});


                parsedData.service = category;
                parsedData.category = category;

            } else{
                self.L.error('SMS_PARSING_VALIDATOR :: parseSMSData',
                    `Invalid config format for category: ${category}. Missing detailsPath or details_key.`);

                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:ERROR',
                    'TYPE:INVALID_CONFIG_FORMAT',
                    'CATEGORY:' + category
                ]);

                return callback(`Invalid config format for category: ${category}`);
            }

            self.L.verbose('SMS_PARSING_VALIDATOR :: parseSMSData',
                `Extracting data for category: ${category} using details path: ${detailsPath}`,
                JSON.stringify(detailsObject));

            self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData::fieldMappings 2',fieldMappings);


            if (!_.isEmpty(fieldMappings)) {

                if (_.has(config, 'fieldMappings')) {
                    parsedData.rechargeNumber = _.get(detailsObject, fieldMappings.rechargeNumber, null);
                    parsedData.operator = _.get(detailsObject, fieldMappings.operator, null);
                    parsedData.amount = utility.getFilteredAmount(_.get(detailsObject, fieldMappings.amount, '0'));
                    parsedData.dueDate = utility.getFilteredDate(_.get(detailsObject, fieldMappings.dueDate, null)).value;
                    parsedData.billDate = utility.getFilteredDate(_.get(detailsObject, fieldMappings.billDate, null)).value;
                    parsedData.paymentDate = utility.getFilteredDate(_.get(detailsObject, fieldMappings.paymentDate, null)).value;
                    self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData::fieldMappings 1',fieldMappings);
                    
                    if(!detailsObject && noDetailsPathRequired.includes(category)){
                        self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData::fieldMappings',fieldMappings);
                        parsedData.rechargeNumber = _.get(smsData, fieldMappings.rechargeNumber, null);
                        parsedData.operator = _.get(smsData, fieldMappings.operator, null);
                        parsedData.amount = utility.getFilteredAmount(_.get(smsData, fieldMappings.amount, '0'));
                        parsedData.dueDate = utility.getFilteredDate(_.get(smsData, fieldMappings.dueDate, null)).value;
                        parsedData.billDate = utility.getFilteredDate(_.get(smsData, fieldMappings.billDate, null)).value;
                        parsedData.paymentDate = utility.getFilteredDate(_.get(smsData, fieldMappings.paymentDate, null)).value;
                        self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData::fieldMappings',parsedData);

                    }
                }
                


            }

            else {
                self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData',
                    `No field mappings found for category: ${category}, trying direct extraction from detailsPath`);


                parsedData.rechargeNumber = _.get(detailsObject, 'recharge_number', null);
                parsedData.operator = _.get(detailsObject, 'operator', null);
                parsedData.amount = _.get(detailsObject, 'due_amount', null) ? utility.getFilteredAmount(_.get(detailsObject, 'due_amount', null)) : null;
                parsedData.dueDate = utility.getFilteredDate(_.get(detailsObject, 'due_date')).value;
                parsedData.billDate = utility.getFilteredDate(_.get(detailsObject, 'bill_generation_date')).value;
                parsedData.paymentDate = utility.getFilteredDate(_.get(detailsObject, 'payment_date')).value;



                self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData',
                    `Direct extraction results: rechargeNumber=${parsedData.rechargeNumber}, operator=${parsedData.operator}, amount=${parsedData.amount}, dueDate=${parsedData.dueDate}`);
            }



            const requiredFields = ['customerId', 'rechargeNumber', 'operator', 'amount','dueDate'];
            const missingFields = requiredFields.filter(field => !parsedData[field]);

            if (missingFields.length > 0) {
                self.L.error('SMS_PARSING_VALIDATOR :: parseSMSData',
                    `Missing required fields: ${missingFields.join(', ')} for category: ${category}`);

                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:ERROR',
                    'TYPE:MISSING_REQUIRED_FIELDS',
                    'CATEGORY:' + category,
                    'MISSING_FIELDS:' + missingFields.join('_')
                ]);

                return callback(`Missing required fields: ${missingFields.join(', ')}`);
            }


            let operator = _.toLower(parsedData.operator);
            let productId = null;
     
            //operator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`, 'GENERIC_DWH_OPERATOR_MAPPING', operator], _.get(parsedData, 'operator', null)));
            const primaryMappingConfig = `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`;
        
            const fallbackMapping = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'FALLBACK_MAPPINGS', primaryMappingConfig], null);
            
            self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', 
                `Looking up operator mapping for ${operator} in primary config ${primaryMappingConfig} with fallbacks: ${fallbackMapping}`);
            
            let mappedOperator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', primaryMappingConfig, 'GENERIC_DWH_OPERATOR_MAPPING', operator], null));
            
            if (!mappedOperator) {
                if(fallbackMapping) {
                    mappedOperator = _.toLower(_.get(self.config, ['DYNAMIC_CONFIG', fallbackMapping, 'DWH_OPERATOR_MAPPING', operator], null));
                    self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', `Fallback mapping found for operator: ${mappedOperator}`);
                    if(!mappedOperator){
                        mappedOperator =  _.get(self.config, ['DYNAMIC_CONFIG',fallbackMapping, operator, 'RU_MAPPED_OPERATOR'], null);

                    }
                    if(!mappedOperator){
                        mappedOperator =  _.get(self.config, ['DYNAMIC_CONFIG',fallbackMapping, operator, 'DEMERGER_OPERATOR'], null);
    
                    }

                }


            }
            operator = mappedOperator || operator;
            self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', `Final mapped operator: ${operator}`);
            
            if (operator) {

                const primaryMappingConfigForProductid = `GENERIC_${kafkaTopic}_SMS_PARSING`;
        
                const fallbackMappingForProductId = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'FALLBACK_MAPPINGS', primaryMappingConfig], null);
            
                self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', 
                    `Looking up operator mapping for ${operator} in primary config ${primaryMappingConfigForProductid} with fallbacks: ${fallbackMappingForProductId}`);

                productId = _.get(self.config, ['DYNAMIC_CONFIG',primaryMappingConfigForProductid, operator, 'PRODUCT_ID'], null);
            
            
                if (!productId) {
                    if(fallbackMappingForProductId) {
                        productId = _.get(self.config, ['DYNAMIC_CONFIG',fallbackMappingForProductId, operator, 'PRODUCT_ID'], null);

                    }
                }
                
            }
            if(!productId){
                let rechargeNumber2 = _.get(detailsObject, fieldMappings.rechargeNumber2, null);
                let pidMapKey = (operator + (rechargeNumber2 != null && rechargeNumber2 != '' ? `_${rechargeNumber2}` : '')).replace(/ /g, '_');

                const primaryMappingConfigForProductid = `DWH_${kafkaTopic}_SMS_PARSING_CONFIG`;
        
                const fallbackMappingForProductId = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'FALLBACK_MAPPINGS', primaryMappingConfig], null);
            
                self.L.log('SMS_PARSING_VALIDATOR :: parseSMSData', 
                    `Looking up operator mapping for ${operator} in primary config ${primaryMappingConfigForProductid} with fallbacks: ${fallbackMappingForProductId}`);

                productId = _.get(self.config, ['DYNAMIC_CONFIG',primaryMappingConfigForProductid, operator, 'PRODUCT_ID'], null);
            
            
                if (!productId) {
                    if(fallbackMappingForProductId) {
                        productId = _.get(self.config, ['DYNAMIC_CONFIG',fallbackMappingForProductId, operator, 'PRODUCT_ID'], null);

                    }
                }
                
                productId= _.get(self.config, ['DYNAMIC_CONFIG',primaryMappingConfigForProductid, 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', pidMapKey], _.get(self.config, ['DYNAMIC_CONFIG',primaryMappingConfigForProductid, 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null));
                if (!productId) {
                    if(fallbackMappingForProductId) {
                        productId= _.get(self.config, ['DYNAMIC_CONFIG',fallbackMappingForProductId, 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', pidMapKey], _.get(self.config, ['DYNAMIC_CONFIG',fallbackMappingForProductId, 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null));
                    }
                }

            }

            





            let activePid = self.activePidLib.getActivePID(productId);
            self.L.log('processRecord', `Found active Pid ${activePid} against PID ${productId}`);
            productId = activePid ? activePid : productId;



            if (!productId) {

                self.L.error('SMS_PARSING_VALIDATOR :: parseSMSData',
                    `Could not find productId for operator: ${operator}`);

                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:ERROR',
                    'TYPE:MISSING_PRODUCT_ID',
                    'OPERATOR:' + operator
                ]);

                return callback(`Could not find productId for operator: ${operator}`);

            }

            parsedData.productId = productId;
            parsedData.mappedOperator=operator;

            // Format due date
            if (parsedData.dueDate) {
                parsedData.dueDate = MOMENT(parsedData.dueDate).format('YYYY-MM-DD');
            }

            // Log successful parsing
            self.L.verbose('SMS_PARSING_VALIDATOR :: parseSMSData',
                `Successfully parsed data for category: ${category}`,
                JSON.stringify(parsedData));

            return callback(null, parsedData);

        } catch (error) {
            self.L.error('SMS_PARSING_VALIDATOR :: parseSMSData', `Error parsing SMS data: ${error.stack || error}`);

            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                'STATUS:ERROR',
                'TYPE:PARSING_EXCEPTION',
                'ERROR:' + error.message
            ]);

            return callback(error);
        }
    }

    async suspendOperations() {
        const self = this;
        try {
            self.L.log('suspendOperations', 'Initiating shutdown of SMS Parsing Validator Service');

            // Close the Kafka consumer if it exists
            if (this.consumer) {
                self.L.log('suspendOperations', 'Closing Kafka consumer...');
                await new Promise((resolve, reject) => {
                    this.consumer.close((error) => {
                        if (error) {
                            self.L.error('suspendOperations', `Error closing Kafka consumer: ${error}`);
                            reject(error);
                        } else {
                            self.L.log('suspendOperations', 'Kafka consumer closed successfully');
                            resolve();
                        }
                    });
                });
            }

            self.L.log('suspendOperations', 'Service shutdown completed successfully');
        } catch (error) {
            self.L.error('suspendOperations', `Error during shutdown: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'TYPE:SHUTDOWN_ERROR']);
            throw error;
        }
    }
}

export default SMSParsingValidator; 