import ASYNC from 'async'
import _ from 'lodash'
import OS from 'os'
import utility from '../lib'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import Logger from "../lib/logger";
import notificationFallbackConsumer from './notificationFallbackConsumer';
import SmsParsingLagDashboard from '../lib/smsParsingLagDashboard'
import notify from './notify';
import MOMENT from 'moment';

import Q from 'q'
class whatsappNotificationFallbackConsumer {
    constructor(options) {
        let self = this;
        self.config = options.config;
        self.L = options.L;
        self.infraUtils = options.INFRAUTILS;
        self.greyScaleEnv = options.greyScaleEnv;
        self.commonLib = new utility.commonLib(options);
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.L = new Logger(options);
        self.notificationFallbackConsumer = new notificationFallbackConsumer(options);
        self.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        self.status_list = _.get(this.config, ['NOTIFICATION', 'status']);
        self.notify = new notify(options);
        self.setVarFromDynamicConfig();
        self.start();
    }

    async start(){
        let self = this;
        try{
            await self.startProducers();
            await self.startConsumer();
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::start error ${error}`);
            process.exit(0);
        }
    }

    async startProducers(){
        let self = this;
        return new Promise(async (resolve, reject) => {
            try{
                await self.notificationFallbackConsumer.startNotificationFallbackPublishers();

                await self.notificationFallbackConsumer.startNotifyKafkaProducer();

                self.dwhProducer = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.NOTIFICATION_REJECTS.HOSTS
                });
                await self.startSpecificProducer(self.dwhProducer, 'dwhProducer');

                resolve();
            }catch(e){
                self.L.error(`whatsappNotificationFallbackConsumer::startProducers error ${e}`);
                reject(e);
            }
        });
    }

    async startSpecificProducer(producer, producerName){
        let self = this;
        return new Promise((resolve, reject) => {
            try{
                producer.initProducer('high', function (error) {
                    if (error){
                        self.L.error(`error in initialising ${producerName} Producer :: ${error}`);
                        reject(error);
                    }else{
                        self.L.info(`${producerName} KAFKA PRODUCER STARTED....`);
                        resolve();
                    }
                });
            }catch(e){
                self.L.error(`whatsappNotificationFallbackConsumer::startSpecificProducer error ${e}`);
                reject(e);
            }
        });
    }


    startConsumer(){
        const self = this;
        return new Promise((resolve, reject) => {
            try {
                self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.NOTIFICATION_FALLBACK.HOSTS'), 
                "groupId": 'whatsappNotificationFallbackConsumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.NOTIFICATION_FALLBACK.WHATSAPP_TOPIC'),
                "id": "whatsappNotificationFallbackConsumer-" + OS.hostname(),
                "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.WHATSAPP_NOTIFICATION_FALLBACK.BATCHSIZE',1000000),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.WHATSAPP_NOTIFICATION_FALLBACK_TIMEOUT',30*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error)
                    self.L.error(`whatsappNotificationFallbackConsumer : consumer Configured cannot start. ${JSON.stringify(error)}`);
                else if (!error)
                    self.L.info(`whatsappNotificationFallbackConsumer : consumer Configured`);
                    resolve();
                });
            } catch (error) {
                self.L.error(`whatsappNotificationFallbackConsumer::startConsumer error ${JSON.stringify(error)}`);
                reject(error);
            }
        });
    }

    setVarFromDynamicConfig() {
        let self = this;
        self.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'BATCHSIZE'], 2) : 500;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'BATCH_DELAY'], 5*60*1000) : 500;    
        self.whitelistedEvents = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'WHITELISTED_EVENTS', 'EVENTS'], ['sent', 'delivered', 'read', 'failed','clicked']);
        self.whitelistedErrorsForSMSInvoke = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'WHITELISTED_ERRORS_FOR_SMS_INVOKE', 'ERRORS'], ['ALL']);
        self.whitelistedEventsForUserResponse = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'WHITELISTED_EVENTS_FOR_USER_RESPONSE', 'EVENTS'], ['clicked']);
        self.staticSmsUrl = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'STATIC_SMS_URL', 'URL'], 'https://m.paytm.me/rentcon?rcon=2');
        self.RU_CLIENT_ID = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'RU_CLIENT_ID'], 'digital-reminder-ru');
        self.typeForWhatsappNotificationFallback = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'COMMON', 'TYPE'], 'SMS');
        self.categoryIdForWhatsappNotificationFallback = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'COMMON', 'CATEGORY_ID'], 1);
        self.sourceIdForWhatsappNotificationFallback = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'COMMON', 'SOURCE_ID'], 42);
        setTimeout(() => {
            self.L.log(`setVarFromDynamicConfig Updating service params from dynamic config`);
            self.setVarFromDynamicConfig()
        }, 15 * 60 * 1000);
    }

    _processKafkaData(records,resolveOffset , topic , partition , cb) {
        let startTime = new Date().getTime();
        const self = this;
        const chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'COMMON', 'CHUNKSIZE'], 50);
        let lastMessage,
        notificationFallbackData = null,
        recordsToProcess = [];

        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.info(`whatsappNotificationFallbackConsumer::_processKafkaData received data from kafka ${records.length}`);
        } else {
            self.L.error(`whatsappNotificationFallbackConsumer::_processKafkaData error while reading kafka`);
            return cb();
        }

        records.forEach(row => {
            if(row && row.value) {
                try {
                    let whatsappNotificationFallbackPublishedTime = Number(_.get(row, 'timestamp', null));
                    notificationFallbackData = JSON.parse(row.value);
                    self.L.info(`whatsappNotificationFallbackConsumer::_processKafkaData notificationFallbackData ${JSON.stringify(notificationFallbackData)}`);

                    _.set(notificationFallbackData, 'whatsappNotificationFallbackPublishedTime', whatsappNotificationFallbackPublishedTime);
                    _.set(notificationFallbackData, 'whatsappNotificationFallbackAcknowledgeTime', new Date().getTime());
                    recordsToProcess.push(notificationFallbackData);

                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:TRAFFIC', 'PARTITION:' + partition, 'TOPIC:' + topic]);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:INVALID_JSON_PAYLOAD']);
                    self.L.error(`whatsappNotificationFallbackConsumer::_processKafkaData Failed to parse recents data topic,partition,offset,timestamp :: ${row.topic}, ${row.partition}, ${row.offset}, ${row.timestamp}, ${error}`);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:INVALID_PAYLOAD']);
                self.L.error(`whatsappNotificationFallbackConsumer::_processKafkaData Unable to get valid data from kafka topic,partition,offset,timestamp :: ${row.topic}, ${row.partition}, ${row.offset}, ${row.timestamp}`);
            }
        });

        self.L.info(`whatsappNotificationFallbackConsumer::_processKafkaData Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, chunkSize, self._processBillsData.bind(self), async (err) => {

            self.kafkaConsumerChecks.findOffsetDuplicates("whatsappNotificationFallbackConsumer",records,topic,partition);

            if(err) {
                self.L.error(`whatsappNotificationFallbackConsumer::_prepareDataToInsert Error: ${err}`);
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }else{

                await resolveOffset(lastMessage.offset)
                self.L.info(`whatsappNotificationFallbackConsumer::_processKafkaData Commit success for offset : ${_.get(lastMessage, 'offset')} topic : ${topic} partition : ${partition} timestamp : ${_.get(lastMessage, 'timestamp')}`);
                
                    recordsToProcess = [];
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.info(`whatsappNotificationFallbackConsumer::_processKafkaData per chunkSize record Execution time : ${executionTime} seconds ${records.length}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:WHATSAPP_NOTIFICATION_FALLBACK", "TIME_TAKEN:" + executionTime]);
    
                    setTimeout(() => {
                        // Resume consumer now
                        return cb();
                    }, self.kafkaResumeTimeout);
            }
        });
    }

    /*
    {
        "errors":null,
        "messageId":"wamid.HBgMOTE4OTQ5MDM0Mzg4FQIAERgSNEE5RUYyNjUwODMzRUYyNDkyAA==",
        "jobId":"1234567890",
        "templateId":"1234567890",
        "timestamp":1740747756,
        "phoneNumber":"y2kcCMu6zDaLdsGXrwbFPg==",
        "expirationTimeStamp":"1740834180",
        "category":"utility",
        "type":"whatsapp",
        "event":"sent"
    }
    */

    async _processBillsData(notificationFallbackData, cb){
        let self = this;
        try{
            if(_.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'COMMON', 'BLOCK_PROCESSING_NOTIFICATION_FALLBACK_DATA'], true)){
                return cb();
            }
            self.preProcessNotificationFallbackData(notificationFallbackData);

            self.isRuPayload(notificationFallbackData);
            
            self.isEventWhitelisted(notificationFallbackData);
            
            let dwhLogFlow = self.isDwhLogFlow(notificationFallbackData);

            if(dwhLogFlow){
                await self.invokeDwhSyncFlow(notificationFallbackData);
            }else{ 
                await self.invokeSMSFlow(notificationFallbackData);
            }

            await self.publishLatencies(notificationFallbackData, dwhLogFlow);

            cb();
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::_processBillsData ${error}`);
            return cb(null);
        }
    }
    preProcessNotificationFallbackData(notificationFallbackData){
        let self = this;
        try{
            let timestamp = _.get(notificationFallbackData, 'timestamp', null);
            if(_.toString(timestamp).length == 10){
                timestamp = timestamp * 1000;
            }
            _.set(notificationFallbackData, 'timestamp', timestamp);

            let errors = _.get(notificationFallbackData, 'errors', null);
            if(errors && typeof errors!= 'string'){
                _.set(notificationFallbackData, 'errors', JSON.stringify(errors));
            }
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::preProcessNotificationFallbackData ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:preProcessNotificationFallbackData']);
            throw error;
        }
    }

    setTimestamps(notificationRecord){
        let self = this;
        try{
            //data is getting fetched from cassandra, need to set created_at and updated_at in format YYYY-MM-DD HH:mm:ss
            //created_at will be existing created_at present in data
            //updated_at will be current timestamp

            _.set(notificationRecord, 'created_at', MOMENT(_.get(notificationRecord, 'created_at')).format("YYYY-MM-DD HH:mm:ss"));
            _.set(notificationRecord, 'updated_at', MOMENT().format("YYYY-MM-DD HH:mm:ss"));
            if(MOMENT(_.get(notificationRecord, 'send_at', null)).isValid()){
                _.set(notificationRecord, 'send_at', MOMENT(_.get(notificationRecord, 'send_at')).format("YYYY-MM-DD HH:mm:ss"));
            }
            return notificationRecord;
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::setTimestamps ${error}`);
            return notificationRecord;
        }     
    }
    async invokeSMSFlow(notificationFallbackData){
        let self = this;
        return new Promise(async (resolve, reject) => {
            try{
                let validatedPayload = await self.notificationFallbackConsumer.validateFallbackPayload(notificationFallbackData, true);
                if (validatedPayload) {
                    let notificationRecord = validatedPayload[0], templateName = validatedPayload[1], newTemplateId = validatedPayload[2];

                    _.set(notificationRecord, 'error_msg', _.get(notificationFallbackData, 'errors', null));
                    if(typeof _.get(notificationRecord, 'data', null) == 'string'){
                        notificationRecord['data'] = JSON.parse(notificationRecord['data']);
                    }

                    notificationRecord = self.setTimestamps(notificationRecord);
                    await new Promise((resolve, reject) => {
                        self.notificationFallbackConsumer.updateNotification(() => {
                            self.L.log(`whatsappNotificationFallbackConsumer :: _processNotifyFallbackData Setting Notification's failed fallback status for jobId: ${notificationRecord['jobId']}`);
                            resolve();
                        }, ['status', 'error_msg'], [ _.get(this.status_list, 'FAILED_FALLBACK', 10), _.get(notificationFallbackData, 'errors', null)], notificationRecord, _.get(this.status_list, 'FAILED_FALLBACK', 10), false);
                    });

                    notificationRecord['template_id'] = newTemplateId;
                    await self.processFallBackPayload(notificationRecord, templateName, notificationFallbackData);
                }
                resolve();
            }catch(error){
                self.L.error(`whatsappNotificationFallbackConsumer::invokeSMSFlow ${error}`);
                reject(error);
            }
        });
    }
    async publishLatencies(notificationFallbackData, dwhLogFlow){
        let self = this;
        try{
            let latencies = {
                "cnsPublishedTime": _.get(notificationFallbackData, 'whatsappNotificationFallbackPublishedTime', null),
                "ruAckTime": _.get(notificationFallbackData, 'whatsappNotificationFallbackAcknowledgeTime', null),
                "eventTimestamp": _.get(notificationFallbackData, 'timestamp', null),
                "processingEndTime": dwhLogFlow ? new Date().getTime() : null,
            }
            return self.smsParsingLagDashboard.publishWhatsappNotificationFallbackLatencies(latencies, 'whatsappNotificationFallbackConsumer');
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::publishLatencies ${error}`);
            throw error;
        }
    }
    async processFallBackPayload(notificationRecord, templateName, notificationFallbackData){
        let self = this;
        try{
            if(_.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_FALLBACK', 'COMMON', 'IS_SMS_FLOW_BLOCKED'], true)){
                return Promise.resolve();
            }
            if(typeof notificationRecord['data'] == 'string'){
                notificationRecord['data'] = JSON.parse(notificationRecord['data']);
            }
            let notificationData = notificationRecord['data'] || {}, newNotificationData = {};

            if (notificationData && !notificationData['dynamicParams'] && !notificationData['notificationReceiver']) {
                let smsNotiData = self.notify.getSmsNotiData(notificationData, templateName,
                    notificationRecord['category_id'], 'SMS');
                notificationData = smsNotiData['json'];
            }

            if (!notificationData || _.isEmpty(notificationData)) {
                return Promise.resolve();
            }

            newNotificationData['templateName'] = templateName;
            newNotificationData['dynamicParams'] = notificationData['dynamicParams'];
            newNotificationData['notificationReceiver'] = notificationData['notificationReceiver']
            newNotificationData['notificationReceiver']['notificationReceiverType'] = 'MOBILENUMBER';


            newNotificationData['dynamicParams']['sms_short_link'] = await self.createTinyUrl(notificationData['dynamicParams']['smsDeeplinkDynamicPart']);
            newNotificationData['dynamicParams']['type'] = self.typeForWhatsappNotificationFallback;
            newNotificationData['dynamicParams']['template_id'] = notificationRecord['template_id'];
            newNotificationData['dynamicParams']['templateName']= templateName;

            newNotificationData['additional_data'] = notificationData['additional_data'];

            notificationData = {};// clearing the now irrelevant data from memory

            notificationRecord['data'] = newNotificationData;
            


            notificationRecord['send_at'] = MOMENT().format("YYYY-MM-DD HH:mm:ss");
            notificationRecord['created_at'] = notificationRecord['updated_at'] = null;
            notificationRecord['type'] = self.typeForWhatsappNotificationFallback;
            notificationRecord['category_id'] = self.categoryIdForWhatsappNotificationFallback;
            notificationRecord['source_id'] = self.sourceIdForWhatsappNotificationFallback;
            notificationRecord['previous_job_id'] = notificationRecord['job_id'];

            let timestamps = {
                "cnsPublishedTime": _.get(notificationFallbackData, 'whatsappNotificationFallbackPublishedTime', null),
                "ruAckTime": _.get(notificationFallbackData, 'whatsappNotificationFallbackAcknowledgeTime', null),
                "eventTimestamp": _.get(notificationFallbackData, 'timestamp', null),
                "notify_onBoardTime": new Date().getTime(),
            }

            notificationRecord['timestamps'] = timestamps;

            self.L.info(`whatsappNotificationFallbackConsumer::processFallBackPayload notificationRecord ${JSON.stringify(notificationRecord)}`);

            await self.notificationFallbackConsumer.createAndPublishNotification(notificationRecord);
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::processFallBackPayload ${error}`);
            throw error;
        }
    }

    async createTinyUrl(smsDeeplinkDynamicPart){
        let self = this;
        try{
            let staticPart = self.staticSmsUrl;
            let dynamicPart = smsDeeplinkDynamicPart;
            if(staticPart && dynamicPart){
                return new Promise((resolve, reject) => new utility.TinyUrl().createShortUrl((err, shortLink) => {
                    resolve(shortLink);
                }, _.get(self.config, ['TINYURL_CONFIG'], null), staticPart + dynamicPart ));
            }else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:createTinyUrl']);
                throw new Error('Static or dynamic part of url is null')
            }
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::createTinyUrl ${error}`);
            throw error;
        }
    }
    isDwhLogFlow(notificationFallbackData){
        let self = this;
        try{
            let event = _.toLower(_.get(notificationFallbackData, 'event', null)),
                errors = _.get(notificationFallbackData, 'errors', null);
            if(self.whitelistedErrorsForSMSInvoke.includes(errors) || (errors && self.whitelistedErrorsForSMSInvoke.includes('ALL'))){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:WHITELISTED_ERROR']);
                return false;
            }else if(self.whitelistedEventsForUserResponse.includes(event)){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:USER_RESPONSE_EVENT']);
                return true;
            }else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:INVALID_EVENT']);
                throw new Error(`whatsappNotificationFallbackConsumer::takeActionAccordingToEvent Invalid event ${event}`);
            }
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::takeActionAccordingToEvent ${error}`);
            throw error;
        }
    }

    isEventWhitelisted(notificationFallbackData){
        let self = this;
        try{
            let event = _.toLower(_.get(notificationFallbackData, 'event', null));
            let errors = _.get(notificationFallbackData, 'errors', null);
            if(self.whitelistedEvents.includes(event) || errors != null){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:WHITELISTED_EVENT']);
                return true;
            }else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:NOT_WHITELISTED_EVENT']);
                throw new Error(`whatsappNotificationFallbackConsumer::isEventWhitelisted Invalid event ${event}`);
            }
        }catch(error){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:isEventWhitelisted']);
            self.L.error(`whatsappNotificationFallbackConsumer::isEventWhitelisted ${error}`);
            throw error;
        }
    }

    getDwhPayload(notificationFallbackData){
        let self = this;
        try{
            let payload = {
                "job_id": _.get(notificationFallbackData, 'jobId', null),
                "template_id": _.get(notificationFallbackData, 'templateId', null),
                "type": _.toUpper(_.get(notificationFallbackData, 'type', null)),
                "user_response": _.toLower(_.get(notificationFallbackData, 'buttonText', null)),
                "created_at": MOMENT().format("YYYY-MM-DD HH:mm:ss"),
                "updated_at": MOMENT().format("YYYY-MM-DD HH:mm:ss"),
                "event_timestamp":MOMENT(_.get(notificationFallbackData, 'timestamp', null)).isValid() ? MOMENT(_.get(notificationFallbackData, 'timestamp', null)).format("YYYY-MM-DD HH:mm:ss") : null,
                "status": _.get(self.status_list, 'USER_RESPONSE', 11),
                "data": {
                    "additional_data": {
                        "templateName": _.get(notificationFallbackData, 'templateName', null),
                    }
                }
            }
            return self.notify.prepareKafkaPayloadForDwhSync(payload);
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::getDwhPayload ${error}`);
            throw error;
        }
    }
    async invokeDwhSyncFlow(notificationFallbackData){
        let self = this;
        return new Promise((resolve, reject) => {
            try{
                let payload = self.getDwhPayload(notificationFallbackData);

                let publisherObject = [{
                    topic: _.get(self.config, ['KAFKA', 'SERVICES', 'CASSANDRA_NOTIFICATION_DWH', 'TOPIC'], null),
                    messages: JSON.stringify(payload),
                    key: _.get(notificationFallbackData, 'jobId', null),
                }]
                self.dwhProducer.publishData(publisherObject, function(err) {
                    self.handleDwhProducerResponse(err, publisherObject);
                    resolve();
                }, [200, 800]); 
            }catch(error){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:ERROR', 'TYPE:invokeDwhSyncFlow']);
                self.L.error(`whatsappNotificationFallbackConsumer::invokeDwhSyncFlow ${error}`);
                reject(error);
            }
        });
    }
    
    handleDwhProducerResponse(err, publisherObject){
        let self = this;
        try{
            if (err) {
                self.L.error(`invokeDwhSyncFlow:: err publishing data in kafka topic ${publisherObject[0].topic} for payload ${JSON.stringify(publisherObject[0].messages)}`, err);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK`,
                    `STATUS:ERROR`,
                    `TOPIC:${_.get(self.config, ['KAFKA', 'SERVICES', 'CASSANDRA_NOTIFICATION_DWH', 'TOPIC'], 'UNKNOWN')}`,
                    `FLOW:invokeDwhSyncFlow`,
                    `TYPE:CASSANDRA_NOTIFICATION_DWH_SYNC`
                ]);
            } else {
                self.L.log(`invokeDwhSyncFlow:: msg published successfully in kafka topic ${publisherObject[0].topic} with payload: ${JSON.stringify(publisherObject[0].messages)}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK`,
                    `STATUS:PUBLISHED`,
                    `TOPIC:${_.get(self.config, ['KAFKA', 'SERVICES', 'CASSANDRA_NOTIFICATION_DWH', 'TOPIC'], 'UNKNOWN')}`,
                    `FLOW:invokeDwhSyncFlow`,
                    `TYPE:CASSANDRA_NOTIFICATION_DWH_SYNC`
                ]);
            }
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::handleDwhProducerResponse ${error}`);
        }
    }
    isRuPayload(notificationFallbackData){
        let self = this;
        try{
            let templateId = _.get(notificationFallbackData, 'templateId', null);
            let clientId = _.get(notificationFallbackData, 'clientId', null);

            if(clientId == self.RU_CLIENT_ID){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:RU_TEMPLATE']);
                return true;
            }else if(_.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', templateId, 'MAPPED_TEMPLATE_ID'], null)){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:RU_TEMPLATE']);
                return true;
            }else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:WHATSAPP_NOTIFICATION_FALLBACK", 'STATUS:INVALID_TEMPLATE']);
                throw new Error("whatsappNotificationFallbackConsumer::isRuPayload Invalid template");
            }
            
        }catch(error){
            self.L.error(`whatsappNotificationFallbackConsumer::isRuPayload ${error}`);
            throw error;
        }
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`whatsappNotificationFallbackConsumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            if (self.kafkaNotificationConsumer) {
                self.kafkaNotificationConsumer.close(function(error, res){
                    if(error){
                        self.L.error(`whatsappNotificationFallbackConsumer::stopConsumer :: Error Stopping Consumer Err : ${JSON.stringify(error)}`);
                        return error;
                    }
                    self.L.info(`whatsappNotificationFallbackConsumer::stopConsumer :: Consumer Stopped! Response : ${JSON.stringify(res)}`);
                })
            }
            else {
                self.L.log('whatsappNotificationFallbackConsumer::suspendOperations', 'No active Kafka consumer to close');
            }
            
        })
        .then(function(){
            self.L.log(`whatsappNotificationFallbackConsumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`whatsappNotificationFallbackConsumer::suspendOperations error in shutting kafka consumer ${JSON.stringify(err)}`);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}


export default whatsappNotificationFallbackConsumer;
