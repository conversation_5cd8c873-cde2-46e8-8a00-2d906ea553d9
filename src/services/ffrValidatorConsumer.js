import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator';
import <PERSON>OMENT from 'moment';
import REQUEST from 'request';
import <PERSON>Y<PERSON> from 'async';
import _ from 'lodash';
import utility from '../lib';
import OS, { type } from 'os';
import Q from 'q';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';

class FFRValidatorConsumer {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'FFR_VALIDATOR', 'BATCHSIZE'], 10) : 100;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'FFR_VALIDATOR', 'DELAY'], 5 * 60 * 1000) : 0;
        this.commonLib = new utility.commonLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.topicConfigMap = {};
        this.utility = require('../lib');
        this.activePidLib = options.activePidLib;
    }

    async start() {
        let self = this;
        self.L.log('start', 'Going to configure Kafka..');

        await self.configureKafka();
        self.L.log('FFR_VALIDATOR :: start', 'Kafka Configured successfully !!');
    }

    async configureKafka() {
        const self = this;
        try {
            self.L.log('configureKafka', 'Configuring Kafka publishers and consumers');

            // Configure Kafka publishers
            await this.configureKafkaPublishers();

            return new Promise((resolve, reject) => {
                self.consumer = new KafkaConsumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.FFR_HIT_TOPIC.HOSTS'),
                    "groupId": 'ffrValidator-consumer',
                    "topics": ["FFR_HIT_TOPIC"],
                    "id": `ffrValidator-consumer_${OS.hostname()}_${process.pid}`,
                    "maxBytes": _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.FFR_VALIDATOR.BATCHSIZE', 1000000),
                    sessionTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT', 2 * 60 * 1000),
                    maxProcessTimeout: _.get(self.config, 'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.FFR_VALIDATOR_TIMEOUT', 30 * 60 * 1000)
                });

                self.consumer.initConsumer((records, resolveOffset, topic, partition, cb) => {
                    this.processRecords(records, resolveOffset, topic, partition, cb);
                }, (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:ERROR', 'TYPE:CONSUMER_INIT']);
                        self.L.error("configureKafka", `Failed to initialize Kafka consumer: ${error}`);
                        reject(error);
                    } else {
                        self.L.log("configureKafka", `Kafka consumer initialized successfully for topic: FFR_HIT_TOPIC`);
                        resolve();
                    }
                });
            });

        } catch (error) {
            self.L.error('configureKafka', `Error configuring Kafka: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:ERROR', 'TYPE:KAFKA_CONFIG']);
            throw error;
        }
    }

    async configureKafkaPublishers() {
        let self = this;

        // Configure DWH Kafka publisher
        self.dwhSmsPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_VALIDATOR_DWH.HOSTS')
        });

        await new Promise((resolve, reject) => {
            self.dwhSmsPublisher.initProducer('high', (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:ERROR', 'TYPE:DWH_PUBLISHER']);
                    reject(error);
                } else {
                    resolve();
                }
            });
        });
    }

    async processRecords(records, resolveOffset, topic, partition, cb) {
        let self = this;
        let startTime = new Date().getTime();
        let lastMessage;

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
        } else {
            self.L.critical('FFR_VALIDATOR :: processRecords', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return cb();
        }

        self.L.log('FFR_VALIDATOR :: processRecords', `Processing ${records.length} records from topic ${topic}`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:FFR_VALIDATOR', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW', `TOPIC:${topic}`]);

        try {
            let currentPointer = 0;
            const chunkSize = 10; // Process 10 records at a time

            // Process records in chunks
            await new Promise((resolve, reject) => {
                ASYNC.whilst(
                    () => currentPointer < records.length,
                    (callback) => {
                        let nextChunk = records.slice(currentPointer, currentPointer + chunkSize);
                        currentPointer += chunkSize;

                        self.processBatch(nextChunk, () => {
                            setTimeout(() => callback(), 2);
                        });
                    },
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });

            // Check for duplicate offsets
            self.kafkaConsumerChecks.findOffsetDuplicates("FFRValidator", records, topic, partition);

            // Commit offset
            await resolveOffset(lastMessage.offset);
            self.L.log('FFR_VALIDATOR :: processRecords', `Commit success for offset: ${lastMessage.offset}, topic: ${topic}, partition: ${partition}`);

            // Log execution time
            let endTime = new Date().getTime();
            let executionTime = Math.round((endTime - startTime) / 1000);
            self.L.log('FFR_VALIDATOR :: processRecords', `Execution time: ${executionTime} seconds`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:FFR_VALIDATOR", "TIME_TAKEN:" + executionTime]);

            // Add delay between batches if configured
            setTimeout(() => cb(), self.kafkaBatchDelay);

        } catch (error) {
            self.L.error('FFR_VALIDATOR :: processRecords', `Error processing records: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:ERROR', 'SOURCE:MAIN_FLOW', `ERROR:${error.message}`]);
            return cb();
        }
    }

    processBatch(records, done) {
        let self = this;
        let currentPointer = 0;
        const targetTPS = 10; // 10 transactions per second
        const delayBetweenRecords = Math.floor(1000 / targetTPS); // milliseconds between records

        // Process records one by one with controlled delay
        const processNextRecord = () => {
            if (currentPointer >= records.length) {
                // All records processed
                if (done) done();
                return;
            }

            const record = records[currentPointer++];
            
            self.processRecord(record, () => {
                // Schedule next record with delay to maintain TPS
                setTimeout(processNextRecord, delayBetweenRecords);
            });
        };

        // Start processing
        processNextRecord();
    }

    processRecord(record, done) {
        let self = this;

        try {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:FFR_VALIDATOR",
                'STATUS:TRAFFIC',
                `PARTITION:${_.get(record, 'partition', null)}`,
                `TOPIC:${_.get(record, 'topic', null)}`
            ]);

            // Parse the record
            let parsedRecord = JSON.parse(_.get(record, 'value', null));
            self.L.log('FFR_VALIDATOR ::', `Parsed record: ${JSON.stringify(parsedRecord)}`);

            if (!parsedRecord.smsData || !parsedRecord.parsedData) {
                self.L.critical('FFR_VALIDATOR :: processRecord', `Invalid Kafka record received. 'smsData' or 'parsedData' key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                return done();
            }

            const smsData = parsedRecord.smsData;
            const parsedData = parsedRecord.parsedData;

            self.validateWithFFR(smsData, parsedData, (err) => {
                if (err) {
                    self.L.error('FFR_VALIDATOR :: validateWithFFR', `Error validating with FFR: ${err}`);

                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:FFR_VALIDATOR",
                        'STATUS:ERROR',
                        "TYPE:VALIDATION_FAILURE",
                        "ERROR:" + err
                    ]);
                } else {
                    // Log success if no error
                    self.L.verbose('FFR_VALIDATOR :: validateWithFFR',
                        `Successfully validated data for smsUUID: ${_.get(smsData, 'smsUUID')}`);
                }
                done();
            });

        } catch (error) {
            self.L.critical('FFR_VALIDATOR :: processRecord', `Invalid Kafka record received: ${error}`, record);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:FFR_VALIDATOR", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return done();
        }
    }

    validateWithFFR(smsData, parsedData, done) {
        let self = this;
        self.L.log('FFR_VALIDATOR ::validateWithFFR', `smsData: ${JSON.stringify(smsData)}, parsedData: ${JSON.stringify(parsedData)}`);

        ASYNC.waterfall([
            // Step 1: Hit FFR API with the parsed data
            (next) => {
                self.hitFfrValidationApi(parsedData, (err, ffrResponse) => {
                    if (err) {
                        self.L.error('FFR_VALIDATOR :: validateWithFFR', `Error hitting FFR API: ${err}`);
                        return next(err);
                    }
                    next(null, ffrResponse);
                });
            },

            // Step 2: Compare SMS data with FFR response
            (ffrResponse, next) => {
                self.compareDataWithFFR(parsedData, ffrResponse, (err, comparisonResults) => {
                    if (err) {
                        self.L.error('FFR_VALIDATOR :: validateWithFFR', `Error comparing data: ${err}`);
                        return next(err);
                    }
                    next(null, ffrResponse, comparisonResults);
                });
            },

            // Step 3: Push metrics based on comparison results
            (ffrResponse, comparisonResults, next) => {
                self.pushComparisonMetrics(parsedData, comparisonResults, (err) => {
                    if (err) {
                        self.L.error('FFR_VALIDATOR :: validateWithFFR', `Error pushing metrics: ${err}`);
                        return next(err);
                    }
                    next(null, ffrResponse, comparisonResults);
                });
            },

            // Step 4: Publish results to DWH
            (ffrResponse, comparisonResults, next) => {
                self.publishToDWH(smsData, parsedData, ffrResponse, comparisonResults, (err) => {
                    if (err) {
                        self.L.error('FFR_VALIDATOR :: validateWithFFR', `Error publishing to DWH: ${err}`);
                        // Don't fail the process if DWH publishing fails
                    }
                    next(null);
                });
            }
        ],
            (err) => {
                if (err) {
                    self.L.error('FFR_VALIDATOR :: validateWithFFR', `Error in validation flow: ${err}`);
                }
                done(err);
            });
    }
    hitFfrValidationApi(parsedData, callback) {

        let self = this;
        let retryCount = 0;
        //const maxRetries = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'MAX_FFR_RETRIES'], 2);
        //const retryDelay = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'FFR_RETRY_DELAY_MS'], 1000);
        function makeRequest() {
            try {
                // Prepare API options
                let apiOpts = {
                    "uri": _.get(self.config, ['FFR', 'VALIDATION_URL'], null),
                    "method": "POST",
                    "timeout": 60000,
                    'json': {
                        'cart_items': [
                            {
                                price: parsedData.amount,
                                product_id: parsedData.productId,
                                quantity: 1,
                                fulfillment_req: {
                                    recharge_number: parsedData.rechargeNumber,
                                    amount: parsedData.amount
                                }
                            }
                        ],
                        'channel_id': "digital-reminder",
                    }
                };
                if (parsedData.customerId) {
                    _.set(apiOpts, 'json.customer_id', parsedData.customerId);
                }
                self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                    `Calling FFR for recharge_number ${parsedData.rechargeNumber} and operator ${parsedData.operator}`,
                    JSON.stringify(apiOpts));
                const latencyStart = new Date().getTime();
                REQUEST(apiOpts, (error, response, body) => {
                    // Send latency metrics
                    utility._sendLatencyToDD(latencyStart, {
                        'REQUEST_TYPE': 'FFR_VALIDATION',
                        'URL': '/v1/recharge/validate',
                        'OPERATOR': parsedData.operator,
                        'SERVICE': parsedData.service,
                        'RETRY_COUNT': retryCount,
                        'batch':'NO_BATCH'
                    });
                    // Send status code metrics
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:FFR_VALIDATION',
                        'STATCODE:' + _.get(response, 'statusCode', '5XX'),
                        'OPERATOR:' + parsedData.operator,
                        'SERVICE:' + parsedData.service,
                        'RETRY_COUNT:' + retryCount,
                        'batch:NO_BATCH'
                    ]);
                    // Log latency
                    self.L.log('SMS_PARSING_VALIDATOR :: hitFfrValidationApi',
                        `FFR_VALIDATION_API_latency::operator:${parsedData.operator}_recharge_number:${parsedData.rechargeNumber}_latency:${new Date().getTime() - latencyStart}ms`);
                    // Parse response body if it's a string
                    if (body && typeof body === 'string') {
                        try {
                            body = JSON.parse(body);
                        } catch (e) {
                            self.L.error("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                                `Error parsing response for recharge_number ${parsedData.rechargeNumber}`, e);
                            // Retry if we haven't exceeded max retries
                            /*if (retryCount < maxRetries) {
                                retryCount++;
                                self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                                    `Retrying FFR call (${retryCount}/${maxRetries}) after parse error for recharge_number ${parsedData.rechargeNumber}`);
                                return setTimeout(makeRequest, retryDelay);
                            }*/
                            return callback('FFR_VALIDATION_API_RESPONSE_PARSE_ERROR');
                        }
                    }
                    // Handle request errors
                    if (error || _.get(response, 'statusCode', 0) !== 200) {
                        let errorStamp = error || `Invalid Status Code ${_.get(response, 'statusCode', null)}`;
                        self.L.error("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                            `Error for recharge_number ${parsedData.rechargeNumber}`, errorStamp);
                        // Retry if we haven't exceeded max retries
                        /*if (retryCount < maxRetries) {
                            retryCount++;
                            self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                                `Retrying FFR call (${retryCount}/${maxRetries}) after request error for recharge_number ${parsedData.rechargeNumber}`);
                            return setTimeout(makeRequest, retryDelay);
                        }*/
                        return callback('FFR_VALIDATION_API_REQUEST_FAILURE');
                    }
                    // Success case
                    self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                        `Received response for recharge_number ${parsedData.rechargeNumber}`,
                        JSON.stringify(body));
                    // Check allowBillFetch
                    const validationGwResponse = _.get(body, 'cart_items.0.validationGwResponse', {});
                    if (_.get(validationGwResponse, 'allowBillFetch', null) === "NO") {
                        self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                            `allowBillFetch is NO for recharge_number ${parsedData.rechargeNumber}`);
                        // Handle waitTime logic
                        const waitTime = _.get(validationGwResponse, 'billFetchWaitTime', 0);
                        if (waitTime > 0) {
                            self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                                `Wait time ${waitTime}ms specified for recharge_number ${parsedData.rechargeNumber}`);
                            // If waitTime is reasonable, retry after waiting
                            const maxWaitTime = _.get(self.config, ['DYNAMIC_CONFIG', 'SMS_PARSING_VALIDATOR', 'MAX_WAIT_TIME_MS'], 30000);
                            if (waitTime <= maxWaitTime) {
                                return setTimeout(() => makeRequest(), waitTime);
                            }
                        }
                        return callback('FFR_VALIDATION_DISALLOWED_BILL_FETCH');
                    }
                    // Extract and return the FFR response data
                    const ffrResponse = {
                        customerDataResponse: _.get(body, 'cart_items.0.customerDataResponse', {}),
                        validationGwResponse: validationGwResponse,
                        frontendErrorMessage: _.get(validationGwResponse, 'frontendErrorMessage', null),
                        error_info:_.get(body,'error_info',null)
                    };
                    return callback(null, ffrResponse);
                });
            } catch (error) {
                self.L.error('SMS_PARSING_VALIDATOR :: hitFfrValidationApi', `Exception: ${error.stack || error}`);
                // Retry if we haven't exceeded max retries
                /*if (retryCount < maxRetries) {
                    retryCount++;
                    self.L.log("SMS_PARSING_VALIDATOR :: hitFfrValidationApi",
                        `Retrying FFR call (${retryCount}/${maxRetries}) after exception for recharge_number ${parsedData.rechargeNumber}`);
                    return setTimeout(makeRequest, retryDelay);
                }*/
                return callback(error);
            }
        }
        // Start the request process
        makeRequest();
    }
    compareDataWithFFR(parsedData, ffrResponse, callback) {
        let self = this;
        try {
            const comparisonResults = {
                amountMismatch: false,
                dueDateMismatch: false,
                ffrAmount: null,
                ffrDueDate: null,
                ffrBillDate: null,
                smsAmount: parsedData.amount,
                smsDueDate: parsedData.dueDate,
                noBill: _.get(ffrResponse, 'validationGwResponse.noBill', false),
                ffrErrorMsgCode: _.get(ffrResponse, 'validationGwResponse.errorMessageCode', null),
                ffrDeducedStatus: _.get(ffrResponse, 'validationGwResponse.deducedStatus', true),
                ffrConnectionError: _.get(ffrResponse, 'validationGwResponse.connectionError', false)
            };
            // Get amount and due date from FFR response
            const ffrAmount = utility.getFilteredAmount(_.get(ffrResponse, 'customerDataResponse.currentBillAmount', null));
            comparisonResults.ffrAmount = ffrAmount;
            let ffrDueDate = _.get(ffrResponse, 'customerDataResponse.billDueDate', null);
            if (ffrDueDate) {
                ffrDueDate = utility.getFilteredDate(ffrDueDate).value;
                if (ffrDueDate) {
                    ffrDueDate = MOMENT(ffrDueDate).format('YYYY-MM-DD');
                }
            }
            comparisonResults.ffrDueDate = ffrDueDate;
            let ffrBillDate = _.get(ffrResponse, 'customerDataResponse.billDate', null);
            if (ffrBillDate) {
                ffrBillDate = utility.getFilteredDate(ffrBillDate).value;
                if (ffrBillDate) {
                    ffrBillDate = MOMENT(ffrBillDate).format('YYYY-MM-DD');
                }
            }
            comparisonResults.ffrBillDate = ffrBillDate;
            //check if one is null and other is present
            if((!ffrAmount && parsedData.amount)||(ffrAmount && !parsedData.amount)){ 
                comparisonResults.amountMismatch = true;
                self.L.log('SMS_PARSING_VALIDATOR :: compareDataWithFFR',
                    `AMOUNT_MISMATCH detected: SMS=${parsedData.amount}, FFR=${ffrAmount}, operator=${parsedData.operator}`);
            }
            // Check for amount mismatch
            if (ffrAmount && parsedData.amount &&
                Math.abs(parseFloat(ffrAmount) - parseFloat(parsedData.amount)) > 0.01) {
                comparisonResults.amountMismatch = true;
                self.L.log('SMS_PARSING_VALIDATOR :: compareDataWithFFR',
                    `AMOUNT_MISMATCH detected: SMS=${parsedData.amount}, FFR=${ffrAmount}, operator=${parsedData.operator}`);
            }
            // Check for due date mismatch
            if((!ffrDueDate && parsedData.dueDate) ||(ffrDueDate && !parsedData.dueDate)){
                comparisonResults.dueDateMismatch = true;
                self.L.log('SMS_PARSING_VALIDATOR :: compareDataWithFFR',
                    `DUE_DATE_MISMATCH detected: SMS=${parsedData.dueDate}, FFR=${ffrDueDate}, operator=${parsedData.operator}`);

            }
            if (ffrDueDate && parsedData.dueDate && ffrDueDate !== parsedData.dueDate) {
                comparisonResults.dueDateMismatch = true;
                self.L.log('SMS_PARSING_VALIDATOR :: compareDataWithFFR',
                    `DUE_DATE_MISMATCH detected: SMS=${parsedData.dueDate}, FFR=${ffrDueDate}, operator=${parsedData.operator}`);
            }
            
            return callback(null, comparisonResults);
        } catch (error) {
            self.L.error('SMS_PARSING_VALIDATOR :: compareDataWithFFR', `Error comparing data: ${error}`);
            return callback(error);
        }
    }
    pushComparisonMetrics(parsedData, comparisonResults, callback) {
        let self = this;
        try {
            // Push amount mismatch metric
            if (comparisonResults.amountMismatch) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:AMOUNT_MISMATCH',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service,
                    'SMS_AMOUNT:' + comparisonResults.smsAmount,
                    'FFR_AMOUNT:' + comparisonResults.ffrAmount
                ]);
            }
            // Push due date mismatch metric
            if (comparisonResults.dueDateMismatch) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:DUE_DATE_MISMATCH',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service,
                    'SMS_DUE_DATE:' + comparisonResults.smsDueDate,
                    'FFR_DUE_DATE:' + comparisonResults.ffrDueDate
                ]);
            }
            // If no mismatches, send success metric
            if (!comparisonResults.amountMismatch && !comparisonResults.dueDateMismatch) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:MATCH_SUCCESS',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service
                ]);
            }
            if (comparisonResults.noBill) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:NO_BILL',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service
                ]);
            }
            if (comparisonResults.ffrErrorMsgCode) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:FFR_ERROR_MSG_CODE',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service
                ]);
            }
            if (!comparisonResults.ffrDeducedStatus) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:FFR_FALSE_DEDUCED_STATUS',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service
                ]);
            }
            if (comparisonResults.ffrConnectionError) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_VALIDATOR',
                    'STATUS:FFR_CONNECTION_ERROR',
                    'OPERATOR:' + parsedData.operator,
                    'SERVICE:' + parsedData.service
                ]);
            }
            return callback(null);
        } catch (error) {
            self.L.error('SMS_PARSING_VALIDATOR :: pushComparisonMetrics', `Error pushing metrics: ${error}`);
            return callback(error);
        }
    }
    publishToDWH(originalSmsData, parsedData, ffrResponse, comparisonResults, callback) {
        let self = this;
        try {
            if (!self.dwhSmsPublisher) {
                self.L.error('SMS_PARSING_VALIDATOR :: publishToDWH', 'DWH publisher not initialized');
                return callback('DWH publisher not initialized');
            }

            // Determine mismatch type
            let mismatchType = "NO_MISMATCH";
            if (comparisonResults.amountMismatch && comparisonResults.dueDateMismatch) {
                mismatchType = "AMOUNT_AND_DUE_DATE_MISMATCH";
            } else if (comparisonResults.amountMismatch) {
                mismatchType = "AMOUNT_MISMATCH";
            } else if (comparisonResults.dueDateMismatch) {
                mismatchType = "DUE_DATE_MISMATCH";
            }
            let error_info=ffrResponse.error_info;
            if (error_info && typeof error_info === 'string') {
                try {
                    error_info = JSON.parse(error_info);
                } catch (e) {
                    self.L.error('SMS_PARSING_VALIDATOR :: publishToDWH', `Error parsing error_info: ${e}`);
                }
            }
            // Format dates using Moment for consistency
            const billDate = parsedData.billDate ? MOMENT(parsedData.billDate).format('YYYY-MM-DD') : null;
            const dueDate = parsedData.dueDate ? MOMENT(parsedData.dueDate).format('YYYY-MM-DD') : null;
            const paymentDate = parsedData.paymentDate ? MOMENT(parsedData.paymentDate).format('YYYY-MM-DD') : null;
            const ffrDueDate = comparisonResults.ffrDueDate ? MOMENT(comparisonResults.ffrDueDate).format('YYYY-MM-DD') : null;
            const ffrBillDate = comparisonResults.ffrBillDate ? MOMENT(comparisonResults.ffrBillDate).format('YYYY-MM-DD') : null;
            // Construct DWH payload in new format
            const dwhPayload = {
                "misMatchType": mismatchType,
                "ffr_due_amount": comparisonResults.ffrAmount ? comparisonResults.ffrAmount.toString() : null,
                "ffr_bill_date": ffrBillDate || null,
                "ffr_due_date": ffrDueDate,
                "sms_due_amount": parsedData.amount ? parsedData.amount.toString() : null,
                "sms_bill_date": billDate,
                "sms_due_date": dueDate,
                "sms_level_2_category": _.get(originalSmsData, 'level_2_category', null), // Default value as per example
                "sms_date_time": parsedData.smsDateTime ? parsedData.smsDateTime.toString() : null,
                "sms_sender_id": _.get(originalSmsData, 'smsSenderID', null),
                "sms_receiver": _.get(originalSmsData, 'smsReceiver', null),
                "front_end_error_msg": _.get(ffrResponse, 'frontendErrorMessage', null),
                "mapped_operator": parsedData.mappedOperator,
                "sms_operator": parsedData.operator,
                "sms_predicted_category": parsedData.rawCategory,
                "sms_uuid": _.get(originalSmsData, 'smsUUID', null),
                "sms_rec_sub_id": _.get(originalSmsData, 'smsRecSubId', null),
                "sms_kafka_topic": _.get(originalSmsData, 'source_topic', null),
                "recharge_number": parsedData.rechargeNumber,
                "customer_id": parsedData.customerId,
                "sms_service": parsedData.service,
                "product_id": parsedData.productId,
                "validation_date": MOMENT().format('YYYY-MM-DD'),
                "operator_response_code": error_info.operatorResponseCode,
                "updated_at": MOMENT()
            };

            
            dwhPayload.ffr_deduced_status = comparisonResults.ffrDeducedStatus;
            dwhPayload.no_bill = comparisonResults.noBill;
            dwhPayload.ffr_error_msg_code = comparisonResults.ffrErrorMsgCode;
            dwhPayload.ffr_connection_error = comparisonResults.ffrConnectionError;
            // Log DWH payload for debugging
            self.L.log('SMS_PARSING_VALIDATOR :: publishToDWH', 'Publishing to DWH:', JSON.stringify(dwhPayload));
            // Publish to DWH
            self.dwhSmsPublisher.publishData([{
                topic: "SMS_PARSING_VALIDATOR_DWH",
                messages: JSON.stringify(dwhPayload)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                        'STATUS:ERROR',
                        "TYPE:DWH_PUBLISH_FAILURE",
                        "SERVICE:" + parsedData.service,
                        "OPERATOR:" + parsedData.operator
                    ]);
                    self.L.error('SMS_PARSING_VALIDATOR :: publishToDWH', 'Error publishing to DWH', error);
                    return callback(error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_VALIDATOR",
                        'STATUS:SUCCESS',
                        "TYPE:DWH_PUBLISH",
                        "SERVICE:" + parsedData.service,
                        "OPERATOR:" + parsedData.operator,
                        "HAS_MISMATCH:" + (comparisonResults.amountMismatch || comparisonResults.dueDateMismatch)
                    ]);
                    self.L.log('SMS_PARSING_VALIDATOR :: publishToDWH', 'Data published successfully to DWH');
                    return callback(null);
                }
            });
        } catch (error) {
            self.L.error('SMS_PARSING_VALIDATOR :: publishToDWH', `Error preparing DWH payload: ${error.stack || error}`);
            return callback(error);
        }
    }
    async suspendOperations() {
        const self = this;
        try {
            self.L.log('suspendOperations', 'Initiating shutdown of SMS Parsing Validator Service');
            // Close the Kafka consumer if it exists
            if (this.consumer) {
                self.L.log('suspendOperations', 'Closing Kafka consumer...');
                await new Promise((resolve, reject) => {
                    this.consumer.close((error) => {
                        if (error) {
                            self.L.error('suspendOperations', `Error closing Kafka consumer: ${error}`);
                            reject(error);
                        } else {
                            self.L.log('suspendOperations', 'Kafka consumer closed successfully');
                            resolve();
                        }
                    });
                });
            }
            self.L.log('suspendOperations', 'Service shutdown completed successfully');
        } catch (error) {
            self.L.error('suspendOperations', `Error during shutdown: ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_VALIDATOR", 'STATUS:ERROR', 'TYPE:SHUTDOWN_ERROR']);
            throw error;
        }
    }
    

}
export default FFRValidatorConsumer;

