"use strict";

import OS from 'os'
import _ from 'lodash'
import utility from '../lib'
import ASYNC from 'async'
import MOMENT from 'moment'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import cassandraBills from '../models/cassandraBills';
import Q from 'q'

/**
 * This service is used to consume kafka from activePaytmUsersConsumer which 
 * read the cust_id_rn_mapping table based on kafka data and smart fetch eligible services
 * and then check if the kafka data is publishable or not based on some configurable conditions 
 * and then for each elgible row fetch from db prepare payload and publish in nonru pipeline
 */
class ExpiredCAPublisher {

    /**
     * @param { object } options Contains configuration and dependencies
     * @param { object } options.L Paytm Logger (lgr) object
     * @param { object } options.config Local config object
     * @param { object } options.INFRAUTILS Contains util libraries like kafka
     */
    constructor(options) {
        let self = this;

        self.L = options.L;
        self.config = options.config;
        self.client = options.cassandraDbClient;
        self.infraUtils = options.INFRAUTILS;
        self.greyScaleEnv = options.greyScaleEnv;

        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.cassandraBills = new cassandraBills(options);
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : _.get(self.config, ['DYNAMIC_CONFIG', 'ACTIVE_PAYTM_USERS_CONFIG', 'COMMON', 'KAFKA_BATCHSIZE'], 500);
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 5*60*1000) : 500;

        self.allowedServicesForSmartFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'ALLOWED_SERVICES'], ['electricity']); 
        self.serviceDependencyMapping = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_TO_SERVICE_MAPPING'], {});
        self.serviceWiseNumberOfTransactions = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_THRESHOLD_TRANSACTIONS'], {});
        self.serviceWiseNumberOfDays = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_THRESHOLD_DAYS'], {});
        self.smartFetchServiceConfigForNonRU = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'SERVICES'], []);
        self.smartFetchOperatorConfigForNonRU = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], []);
        self.serviceWiseQueryLimit = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_QUERY_LIMIT'], 100);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);    
    }

    initializeVariable() {
        let self = this;
        self.L.log("ExpiredCAPublisher :: initializeVariable : Re-initializing variable after interval");

        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : _.get(self.config, ['DYNAMIC_CONFIG', 'ACTIVE_PAYTM_USERS_CONFIG', 'COMMON', 'KAFKA_BATCHSIZE'], 500);
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 5*60*1000) : 500;

        self.allowedServicesForSmartFetch = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'ALLOWED_SERVICES'], ['electricity']);
        self.serviceDependencyMapping = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_TO_SERVICE_MAPPING'], {});
        self.serviceWiseNumberOfTransactions = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_THRESHOLD_TRANSACTIONS'], {});
        self.serviceWiseNumberOfDays = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_THRESHOLD_DAYS'], {});
        self.smartFetchServiceConfigForNonRU = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'SERVICES'], []);
        self.smartFetchOperatorConfigForNonRU = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONFIG', 'SMART_FETCH_CONFIG', 'OPERATORS'], []);
        self.serviceWiseQueryLimit = _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_QUERY_LIMIT'], 100);
    }

    start() {
        let self = this;
        self.L.log("ExpiredCAPublisher :: start : Start Configuration: ExpiredCAPublisher is starting........");

        ASYNC.waterfall([
            next => {
                self._startProducer(next);
            },
            next => {
                self._startConsumer(next);
            }
        ],(error) => {
            if (error) {
                self.L.error("ExpiredCAPublisher :: start : Failed to initialize expired ca publihser service.");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:KAFKA_CONFIGURATION']);
            }
        });
    }

    _startProducer(cb){
        let self = this;

        self.nonruKafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.NON_PAYTM_RECORDS.HOSTS')
        });

        self.nonruKafkaPublisher.initProducer('high', function (error) {
            if (error) {
                self.L.critical('ExpiredCAPublisher :: _startProducer : error in initialising expiredCAPublisher Producer :: ', error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:KAFKA_PRODUCER', 'TOPIC:NON_PAYTM_RECORDS_DWH']);
                return cb(error);
            } else {
                self.L.log("ExpiredCAPublisher :: _startProducer : expiredCAPublisher KAFKA PRODUCER STARTED....");
                return cb(error);
            }
        });
    }

    _startConsumer(cb) {
        let self = this;

        try {
            self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.ACTIVE_PAYTM_USERS.HOSTS'), 
                "groupId": 'expired-ca-publisher',
                "topics": [_.get(self.config.KAFKA, 'SERVICES.ACTIVE_PAYTM_USERS.TOPIC')],
                "id": "expired-ca-publisher-" + OS.hostname(),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error){
                    self.L.critical("ExpiredCAPublisher :: _startConsumer : ExpiredCAPublisher Consumer cannot start, error: ", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:ACTIVE_PAYTM_USERS']);
                    return cb(error);
                }
                else {
                    self.L.log("ExpiredCAPublisher :: _startConsumer : ExpiredCAPublisher Consumer Configured");
                    return cb(error);
                }
            });
        } catch (error) {
            return cb(error);
        }
    }

    _processKafkaData(records, resolveOffset, topic ,partition ,cb) {
        let self = this,
            lastMessage,
            recordsToProcess = [];

        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('ExpiredCAPublisher :: _processKafkaData : received data from kafka ', records.length);
        } else {
            self.L.critical('ExpiredCAPublisher :: _processKafkaData : error while reading kafka');
            return cb();
        }

        records.forEach(function (data) {
            if (data && data.value) {
                try {             
                    let rechargeData = JSON.parse(data.value);
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:INVALID_JSON_PAYLOAD']);
                    self.L.error("ExpiredCAPublisher :: _processKafkaData : ", "Failed to parse recharges data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("ExpiredCAPublisher :: _processKafkaData : ", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
            }
        });

        self.L.log('ExpiredCAPublisher :: _processKafkaData : ', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);
        ASYNC.eachLimit(recordsToProcess, 1, self._processBillsData.bind(self), async (err) => {
            self.kafkaConsumerChecks.findOffsetDuplicates("ExpiredCAPublisher", records, topic , partition);

            if(err) {
                self.L.error("ExpiredCAPublisher :: _processKafkaData : Error: ", err );
                setTimeout(() => {
                    return cb();
                }, self.kafkaResumeTimeout);
            }else{
                await resolveOffset(lastMessage.offset)
                self.L.log('ExpiredCAPublisher :: _processKafkaData : ', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                setTimeout(() => {
                    return cb();
                }, self.kafkaResumeTimeout);
                
            }
        });   
    }

    _processBillsData(billsKafkaRow, done) {
        let self = this,
            kafkaData = {
                customer_id: _.get(billsKafkaRow, 'customer_id', ''),
                service: _.get(billsKafkaRow, 'service', ''),
                operator: _.get(billsKafkaRow, 'operator', ''),
                recharge_number: _.get(billsKafkaRow, 'recharge_number', ''),
                payment_date: _.get(billsKafkaRow, 'payment_date', ''),
                is_active_expired_user: _.get(billsKafkaRow, 'is_active_expired_user', false),
                service_wise_payment_dates: _.get(billsKafkaRow, 'service_wise_payment_dates', {})
            };

        ASYNC.waterfall([
            next => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:INFO', 'TYPE:TOTAL_TRAFFIC']);
                if(self.validateKafkaData(kafkaData)){
                    return next(null);
                } else {
                    return next('ExpiredCAPublisher :: _processBillsData : Invalid kafka data!');
                }
            },
            next => {
                if (!kafkaData.service_wise_payment_dates || _.isEmpty(kafkaData.service_wise_payment_dates)) {
                    self.L.log('ExpiredCAPublisher :: _processBillsData : reading cassandra again as service_wise_payment_dates is empty for customerId', kafkaData.customer_id);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:INFO', 'TYPE:SERVICE_WISE_PAYMENT_DATES_EMPTY', 'SERVICE:' + kafkaData.service]);
                    self.cassandraBills.readActivePaytmUsersNewByCId(kafkaData.customer_id)
                    .then((data) => {
                        self.updateKafkaPayload(kafkaData, data);
                        return next(null);
                    })
                    .catch((error) => {
                        self.L.critical('ExpiredCAPublisher :: _processBillsData : select DB exception in reading active_paytm_users_new for ', kafkaData.customer_id, error);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER",'STATUS:ERROR','TYPE:DB_SELECT','TABLE:active_paytm_users_new']);
                        return next('ExpiredCAPublisher :: _processBillsData : select DB exception in reading active_paytm_users_new!');
                    });
                } else {
                    return next(null);
                }
            },
            next => {
                if (!self.allowedServicesForSmartFetch || _.isEmpty(self.allowedServicesForSmartFetch)) {
                    self.L.error('ExpiredCAPublisher :: _processBillsData : No allowed services found for smart fetch!');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:NO_SERVICE_ALLOWED_FOR_SMART_FETCH']);
                    return next('ExpiredCAPublisher :: _processBillsData : No allowed services found for smart fetch!');
                } else {
                    let params = [kafkaData.customer_id, self.allowedServicesForSmartFetch, self.allowedServicesForSmartFetch.length * self.serviceWiseQueryLimit];
                    
                    self.cassandraBills.readCustIdRNMappingByCustIdServices(params)
                    .then((data) => {
                        return next(null, data);
                    })
                    .catch((error) => {
                        self.L.critical('ExpiredCAPublisher :: _processBillsData : select DB exception in reading cust_id_rn_mapping for ', JSON.stringify(params), error);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:DB_SELECT', 'TABLE:cust_id_rn_mapping']);
                        return next('ExpiredCAPublisher :: _processBillsData : select DB exception in reading cust_id_rn_mapping!');
                    });
                }
            },
            (dbRecords, next) => {
                if (dbRecords && _.isArray(dbRecords) && !_.isEmpty(dbRecords)) {
                    let queryLimit = self.allowedServicesForSmartFetch.length * self.serviceWiseQueryLimit;
                    if (dbRecords.length === queryLimit) { 
                        self.L.log('ExpiredCAPublisher :: _processBillsData : Skipping publishing as cust_id_rn_mapping data is more than', queryLimit, 'for customer_id', kafkaData.customer_id);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:AGENT_DATA', 'SIZE:' + dbRecords.length]);
                        return next();
                    } else {
                        self.L.log('ExpiredCAPublisher :: _processBillsData : Found cust_id_rn_mapping data of size', dbRecords.length, 'for customer_id', kafkaData.customer_id);
                        utility._sendMetricsToDD(dbRecords.length, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:INFO', 'TYPE:DB_SELECT', 'TABLE:cust_id_rn_mapping']);
                        ASYNC.each(dbRecords, (dbRecord, callback) => {
                            let service_payment_date_list = self.checkAndGetServicePaymentDates(dbRecord, kafkaData);
                            if (service_payment_date_list) {
                                let payload = self.preparePayload(dbRecord, kafkaData, service_payment_date_list);
                                self.publishInNonruKafka(() => {
                                    callback();
                                }, payload);
                            } else {
                                callback();
                            }
                        }, () => {
                            return next();
                        });
                    }
                } else {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:CUSTOMER_NOT_FOUND']);
                    self.L.log('ExpiredCAPublisher :: _processBillsData : CUSTOMER_NOT_FOUND for customerId', kafkaData.customer_id);
                    return next();
                }
            }
        ], function (error) {
            done();
        });
    }

    validateKafkaData(kafkaData) {
        let self = this,
            mandatoryFields = ['customer_id', 'service', 'operator', 'recharge_number', 'payment_date', 'is_active_expired_user'];

        mandatoryFields.forEach((field) => {
            let value = _.get(kafkaData, field, '');
            if (value === '' || (field === 'is_active_expired_user' && value === false)) {
                self.L.error('ExpiredCAPublisher :: validateKafkaData : Mandatory field missing in kafka data', field);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:INVALID_KAFKA_DATA', 'FIELD:' + field]);
                return false;
            }
        });

        return true;   
    }

    updateKafkaPayload(kafkaData, activeData) {
        let self = this;
        let service_wise_payment_dates = {};
        if (!_.isEmpty(activeData)) {
            activeData.forEach((row) => {
                let service = _.get(row, 'service', '');
                let payment_date_list = _.get(row, 'payment_date_list', []);
                service_wise_payment_dates[service] = payment_date_list;
            });
        }
        _.set(kafkaData, 'service_wise_payment_dates', service_wise_payment_dates);
    }

    checkAndGetServicePaymentDates(dbRecord, kafkaData) {
        let self = this;
        
        if ('DELETED' !== dbRecord.status) {
            self.L.log('ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as record status is not DELETED');
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:SKIP', 'TYPE:KAFKA_PUBLISHER', 'REASON:NOT_DELETED', 'SERVICE:' + dbRecord.service, 'OPERATOR:' + dbRecord.operator]);
            return null;
        } else if (kafkaData.service === dbRecord.service && kafkaData.recharge_number === dbRecord.recharge_number && kafkaData.operator === dbRecord.operator) {
            self.L.log('ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as record is same as payment data');
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:SKIP', 'TYPE:KAFKA_PUBLISHER', 'REASON:DUPLICATE', 'SERVICE:' + dbRecord.service, 'OPERATOR:' + dbRecord.operator]);
            return null;
        } else if ((self.smartFetchServiceConfigForNonRU && self.smartFetchServiceConfigForNonRU.includes(dbRecord.service)) || 
                    (self.smartFetchOperatorConfigForNonRU && self.smartFetchOperatorConfigForNonRU.includes(dbRecord.operator))) {
            let numberOfTransactions = _.get(self.serviceWiseNumberOfTransactions, dbRecord.service, 1);
            let numberOfDays = _.get(self.serviceWiseNumberOfDays, dbRecord.service, -1);

            let mappedService = _.get(self.serviceDependencyMapping, dbRecord.service, null);
            let service_wise_payment_dates = _.get(kafkaData, 'service_wise_payment_dates', null);
            let paymentDates = mappedService && service_wise_payment_dates ? _.get(service_wise_payment_dates, mappedService, []) : [];

            if (numberOfDays === -1 || _.isEmpty(paymentDates)) {
                self.L.log('ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as no valid configs found for checking active customer');
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:SKIP', 'TYPE:KAFKA_PUBLISHER', 'REASON:CONFIG_NOT_FOUND', 'SERVICE:' + dbRecord.service, 'OPERATOR:' + dbRecord.operator]);
                return null;
            } else {
                let lastEligibleDate = MOMENT().subtract(numberOfDays, 'day');
                let filteredPaymentDates = paymentDates.filter((date) => {
                    return MOMENT(date).isSameOrAfter(lastEligibleDate, 'day');
                });
                if (filteredPaymentDates.length >= numberOfTransactions) {
                    filteredPaymentDates = filteredPaymentDates
                        .sort((a, b) => MOMENT(a).valueOf() - MOMENT(b).valueOf()) 
                        .slice(-numberOfTransactions);
                    let service_payment_date_list = {};
                    service_payment_date_list[mappedService] = filteredPaymentDates;
                    return service_payment_date_list;
                } else {
                    self.L.log('ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as no valid payment dates found to mark as active customer');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:SKIP', 'TYPE:KAFKA_PUBLISHER', 'REASON:NOT_ELIGIBLE', 'SERVICE:' + dbRecord.service, 'OPERATOR:' + dbRecord.operator]);
                    return null;
                }
            }
        } else {
            self.L.log('ExpiredCAPublisher :: checkAndGetServicePaymentDates : Not publising as service or operator is not eligible for smart fetch');
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:SKIP', 'TYPE:KAFKA_PUBLISHER', 'REASON:NOT_ALLOWED', 'SERVICE:' + dbRecord.service, 'OPERATOR:' + dbRecord.operator]);
            return null;
        }
    }

    preparePayload(dbRecord, kafkaData, service_payment_date_list) {
        let self = this, 
            extraInfo = {};

        self.L.verbose(`ExpiredCAPublisher :: preparePayload : `, `Preparing payload for service ${dbRecord.service}, operator ${dbRecord.operator}, recharge_number ${dbRecord.recharge_number}, and product_id ${dbRecord.product_id}`);
        extraInfo.eventState = "bill_gen";
        extraInfo.billSource = "archivalCronsExpiredUser";
        extraInfo.updated_data_source = "archivalCronsExpiredUser";
        extraInfo.created_source = "archivalCronsExpiredUser";

        return {
            customerId: dbRecord.customer_id,
            rechargeNumber: dbRecord.recharge_number,
            operator: dbRecord.operator,
            paytype: dbRecord.paytype,
            service: dbRecord.service,
            productId: dbRecord.product_id,
            status: _.get(self.config, 'COMMON.bills_status.PENDING', 1),
            categoryId: _.get(self.config, ['CVR_DATA', dbRecord.product_id, 'category_id'], null),
            notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            source: "archivalCronsExpiredUser",
            extra: JSON.stringify(extraInfo),
            dbEvent: "upsert",
            partialSmsFound: true,
            is_active_expired_user: kafkaData.is_active_expired_user,
            latest_payment_date: kafkaData.payment_date,
            service_payment_date_list: service_payment_date_list
        }
    }

    publishInNonruKafka(done, payload){
        let self = this;

        self.nonruKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS.TOPIC', ''),
            messages: JSON.stringify(payload)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:ERROR', 'TYPE:KAFKA_PUBLISHER', 'SERVICE:' + payload.service, 'OPERATOR:' + payload.operator]);
                self.L.critical('ExpiredCAPublisher :: publishInNonruKafka : NON_PAYTM_RECORDS', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                return done(error);
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:EXPIRED_CA_PUBLISHER", 'STATUS:SUCCESS', 'TYPE:KAFKA_PUBLISHER', 'SERVICE:' + payload.service, 'OPERATOR:' + payload.operator]);
                self.L.log('ExpiredCAPublisher :: publishInNonruKafka : NON_PAYTM_RECORDS', 'Message published successfully in Kafka', JSON.stringify(payload));
                return done();
            }
        }, [200, 800]);   
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`ExpiredCAPublisher :: suspendOperations : kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`ExpiredCAPublisher :: suspendOperations : Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`ExpiredCAPublisher :: suspendOperations : Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`ExpiredCAPublisher :: suspendOperations : kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`ExpiredCAPublisher :: suspendOperations : error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
    
}

export default ExpiredCAPublisher;
