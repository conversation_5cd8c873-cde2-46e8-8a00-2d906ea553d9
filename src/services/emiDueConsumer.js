import _ from 'lodash'
import utility from '../lib'
// import RecentsLayerLib from '../lib/recentsLayer'
import VA<PERSON><PERSON><PERSON>OR from 'validator'
import ASYNC from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import Q from 'q'

const env = (process.env.NODE_ENV || 'development').toLowerCase();

class EmiDueConsumer {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.operator = 'Hero FinCorp';
        // this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', this.operator], null),
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        // TODO entry 
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'EMI_DUE_DETAILS', 'BATCHSIZE'], 2) : 500; 
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'EMI_DUE_DETAILS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariable(){
        this.tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', this.operator], null)
    }


    start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('EmiDueConsumer :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('EmiDueConsumer :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Publisher for REMINDER_BILL_FETCH topic');
                self.kafkaBillFetchPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                self.kafkaBillFetchPublisher.initProducer('high', function (error) {
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                 self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : EMI_DUE_DETAILS');

                // Initialize consumer of topic REMINDER_BILL_FETCH
                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.EMI_DUE_DETAILS.HOSTS'),
                    "groupId": "emiDueBills-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.EMI_DUE_DETAILS.EMI_DUE_DETAILS_TOPIC'),
                    "id": `emiDueBills-consumer_${OS.hostname()}_${process.pid}`, 
                    "fromOffset": "latest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };
                self.kafkaEMIDueConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkaEMIDueConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : EMI_DUE_DETAILS Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaEMIDueConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} EMI DUE Bills data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:EMI_DUE_DETAILS_TRAFFIC']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaEMIDueConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:EMI_DUE_CONSUMER", "TIME_TAKEN:" + executionTime]);
                  
                    setTimeout(function () {
                        self.kafkaEMIDueConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processRecords(() => {
                    return next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processRecords(done, record) {
        let self = this;
        try {
            let [error, processedRecord] = self.validateAndProcessRecord(record);
            if (error) {
                self.L.error(`processRecords`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:VALIDATION_FAILED']);
                return done(error);
            }
    
            self.L.log(`Processing ${JSON.stringify(record)} record for`, processedRecord.debugKey);
            
            let dbRecord;
            ASYNC.waterfall([
                next => {
                    dbRecord = self.getDbRecordToUpdate(processedRecord);
                    return self.createAndUpdateBills(next, dbRecord);
                },
                next => {
                    return self.sendNotification(next, dbRecord);
                },
                next => {
                    return self.publishCtEvents(next, dbRecord);
                }
            ], function (error) {
                return done(null);
            });
        } catch(err) {
            self.L.critical(`processRecords`,`Exception occured for record:${record}`,err)
            return done(null);
        }
    }

    createAndUpdateBills(done, params) {
        let self = this,
            tableName = self.tableName;

        if (tableName) {
            ASYNC.series([
                function (cb) {
                    self.bills.createBill(function (error, data) {
                        // done(error, data);
                        return cb(error, data);
                    }, tableName, params, false, 'emiDueService');
                }
            ], function (error) {
                if (error) {
                    self.L.critical("createAndUpdateBills::create bill", "Error in create api :: ", error);
                }
                return done(error);
            });
        } else {
            self.L.error("createAndUpdateBills::create bill", "Operator not migrated:: ", params.operator);
            return done('Operator not migrated.');
        }
    }

    /**
     * Send Notification : Send PayLoad to Kafka Topic
     * @param {*} done 
     * @param {*} record 
     */
    sendNotification(done, record) {
        let
            self = this,
            payload = {
                source: "emiDueBillFetch",
                notificationType: "BILLGEN",
                data: self.commonLib.mapBillsTableColumns(record)
            };

        self.kafkaBillFetchPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {                
                utility._sendMetricsToDD(1, ['STATUS:ERROR','REQUEST_TYPE:EMI_DUE_DETAILS_BILLGEN_NOTIFICATION', `OPERATOR:${_.get(record, 'operator', null)}`]);
                self.L.critical('sendNotification :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            } else {
                utility._sendMetricsToDD(1, ['STATUS:PUBLISHED','REQUEST_TYPE:EMI_DUE_DETAILS_BILLGEN_NOTIFICATION']);
                self.L.log('sendNotification :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
            }
            return done(error);
        }, [200, 800]);
    }
    /**
     * 
     * @param {*} processedRecord 
     */
     getDbRecordToUpdate(processedRecord) {
        let self = this,
            dateFormat = 'YYYY-MM-DD HH:mm:ss',
            productId = _.get(processedRecord, 'productId', null),
            operator =  _.get(self.config, ['CVR_DATA', productId, 'operator'], 
                           _.get(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG', env, self.operator  , 'operator'] , null)
                        ),
            updatedDbRecord = { 
                customerId : _.get(processedRecord, 'customerId', null),
                rechargeNumber : _.get(processedRecord, 'rechargeNumber', null),
                productId : productId,
                operator : operator,
                amount : _.get(processedRecord, 'amount', 0),
                dueDate : null, 
                billDate : MOMENT().format(dateFormat),                                //
                bill_fetch_date : MOMENT().format(dateFormat),
                service_id : 0,                                                         // Verify in notification create service
                customerOtherInfo :  _.clone(processedRecord.rawKafkaPayload),          // Max char limit 
                paytype : _.get(self.config, ['CVR_DATA', productId, 'paytype'], null),
                service : _.get(self.config, ['CVR_DATA', productId, 'service'], null),
                circle :  _.get(self.config, ['CVR_DATA', productId, 'circle'], null),
                gateway : null,                                                         // During insert operation 1.)rule engine -> 2.)OPERATOR_GATEWAY_REGISTRY 
                customerMobile : _.get(processedRecord, 'customerMobile', null),
                customerEmail : _.get(processedRecord, 'customerEmail', null),
                nextBillFetchDate : null, 
                paymentChannel : null,
                retryCount : 0,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                reason : '',
                extra : '',
                published_date : MOMENT().format(dateFormat),
                user_data : JSON.stringify({'recharge_number_2' : _.get(processedRecord, 'loanNumber', null)}),
                notification_status : 1,
                paymentDate : null,
                is_automatic : 0
            };
        updatedDbRecord.customerOtherInfo = JSON.stringify(updatedDbRecord.customerOtherInfo);
        /**
         * Assumption we are not updating across customer ids for same recharge number
         */
        updatedDbRecord.updateAllCustIdRecords = false;
        // use for how much time we did not send duplicate notification
        updatedDbRecord.time_interval = _.get(processedRecord, 'time_interval', null); 

        return updatedDbRecord;
    }

    /**
     * 
     * @param {*} record 
     */
    validateAndProcessRecord(record) {
        if (!record) return ['Invalid record', record];
        let self = this,
            productId = _.get(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , env, self.operator , 'product_id'] , null);

        productId = (typeof productId === 'number') ? productId : (typeof productId === 'string' && VALIDATOR.isNumeric(productId)) ? VALIDATOR.toInt(productId) : null;

        try {
            if (!_.has(record, 'value')) return ['Invalid Kafka record received', record];
            
            record = JSON.parse(_.get(record, 'value', null));
        } catch (error) {
            if (error) {
                self.L.critical('validateAndProcessRecord', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECONSUMER_RECORD_VALIDATION', 'STATUS:ERROR' , 'TYPE:PARSING_ERROR']);
                return ['Kafka record Parsing Error', record];
            }
        }

        let processedRecord = {
            customerId: (typeof record.cust_id === 'number') ? record.cust_id : (typeof record.cust_id === 'string' && VALIDATOR.isNumeric(record.cust_id)) ? VALIDATOR.toInt(record.cust_id) : null,
            rechargeNumber : record.application_id,
            amount: self.parseAmount(_.get(record, 'emi_due', null)),
            customerEmail : _.get(record, 'Email_Address', null),
            customerMobile : _.get(record, 'Mobile_No', null),
            productId : productId,  
            currentMinBillAmount : self.parseAmount(_.get(record, 'emi_amount', null)),
            loanNumber : _.get(record, 'loan_no', null),
            time_interval : _.get(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , env, self.operator , 'time_interval'] , null),
            rawKafkaPayload : record
        };

        let mandatoryParams = ['customerId', 'rechargeNumber', 'amount' ,  'productId'];

        let invalidParams = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:MANDATORY_PARAM_MISSING', `MISSING_PARAM:${key}`]);
                invalidParams.push(key);
            }
        });
        
        // checking amount exists and has value > 0
        if(typeof processedRecord['amount'] != 'number' || processedRecord['amount'] <= 0) invalidParams.push('amount');

        processedRecord.debugKey = `customerId:${processedRecord.customerId}_rechargeNumber:${processedRecord.rechargeNumber}_amount:${processedRecord.amount}_customerEmail:${processedRecord.customerEmail}_customerMobile:${processedRecord.customerMobile}`;

        if (invalidParams.length > 0) return [`Mandatory Params ${invalidParams} is Missing / Invalid`, record];
        else {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECONSUMER_RECORD_VALIDATION', 'STATUS:SUCCESS', 'TYPE:MAIN_FLOW_EXECUTION']);
            return [null, processedRecord];     
        }
    }

    /**
     * 
     * @param {*} amountStr 
     */
    parseAmount(amountStr) { 
        if (amountStr === 0 ) return 0;
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && !isNaN(amountStr) && amountStr !== ' ') return parseFloat(VALIDATOR.toFloat(amountStr).toFixed(2));
        
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        return parsedAmount;
    }

    /**
     * 
     * @param {*} done 
     * @param {*} processedRecord 
     */
     publishCtEvents(done, dbRecordResp) {
        let self = this;
        const customerId = _.get(dbRecordResp, 'customerId', '');
        const operator = _.get(dbRecordResp, 'operator', '');
        const rechargeNumber = _.get(dbRecordResp, 'rechargeNumber', '');
        const eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILLGEN'], 'reminderBillGen')
        const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;

        let productId = _.get(dbRecordResp, 'productId', '');
        productId = self.activePidLib.getActivePID(productId);
        
        if (!_.get(dbRecordResp, 'notification_status', 1)) {
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            return done(null)
        } 	

        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {                  
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                self.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EMI_DUE", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.critical('publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka on topic REMINDER_CT_EVENTS - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EMI_DUE", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ], error => {
            if(error) {
                self.L.error('publishCtEvents',`Exception occured Error Msg:: ${error} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            } else {
                self.L.log(`publishCtEvents`,`Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`emiDueConsumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.kafkaEMIDueConsumer.close(function(error, res){
                if(error){
                    self.L.error(`emiDueConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`emiDueConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`emiDueConsumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`emiDueConsumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
    
}

export default EmiDueConsumer;