'use strict';

import MOMENT from 'moment';
import _ from 'lodash';
import MODELS from '../models'
import OS from 'os'
import ASYNC from 'async'
import RecentsLayerLib from '../lib/recentsLayer'
import utility from '../lib'
import recentBillLibrary from '../lib/recentBills'
import Q from 'q'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class updateRecentsConsumer {
    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.activePidLib = options.activePidLib;
        self.infraUtils = options.INFRAUTILS;
        self.billsModel = new MODELS.Bills(options)
        self.recentBillLibrary = new recentBillLibrary(options);
        self.recentsLayer = new RecentsLayerLib(options);
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.greyScaleEnv = options.greyScaleEnv;
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : 500; //change
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCH_DELAY'], 5*60*1000) : 500;    
    }

    start() {
        let self = this;
        self.L.log("start", "configuring updateRecentsConsumer");
        // configure Kafka Publisher to push plan_validity_expiry events to Kafka
        self._initializeKafkaConsumer((error) => {
            if (error) {
                self.L.critical('updateRecentsConsumer :: start', 'Error while configuring Kafka consumer...', error);
            } else {
                self.L.log('updateRecentsConsumer :: start', 'Kafka consumer configured....');
            }
        });
    }

    _initializeKafkaConsumer(cb) {
        let self = this;
        try {
            self.consumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'), //TODO confirm from recents team
                "groupId": 'updateRecentsConsumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.UPDATE_RECENTS.TOPIC'),
                "id": "update-recents-consumer-" + OS.hostname(),
                "fromOffset": "earliest",
                "batchSize": self.kafkaBatchSize,
                "autoCommit": false
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error)
                    self.L.critical("updateRecentsConsumer : consumer Configured cannot start.", error);
                else if (!error)
                    self.L.log("updateRecentsConsumer : consumer Configured");
                return cb(error);
            });
        } catch (error) {
            return cb(error)
        }
    }

    _processKafkaData(records) {
        let self = this,
        rechargeData = null,
        chunkSize = 30,
        lastMessage,
        recordsToProcess = [];
        if (!_.isEmpty(records) && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.consumer._pauseConsumer();
            self.L.log('updateRecentsConsumer::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('updateRecentsConsumer::_processKafkaData error while reading kafka');
            return
        }

        self.kafkaConsumerChecks.findOffsetDuplicates("updateRecentsConsumer", records);

        let startTime = new Date().getTime();  
        records.forEach(row => {
            if(row && row.value) {
                try {
                    rechargeData = JSON.parse(row.value);
                    recordsToProcess.push(rechargeData);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_RECENTS_CONSUMER", 'STATUS:INVALID_JSON_PAYLOAD']);
                    self.L.error("updateRecentsConsumer::_processKafkaData", "Failed to parse recents data topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:UPDATE_RECENTS_CONSUMER", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("allTransactionsConsumer::_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", row.topic, row.partition, row.offset, row.timestamp );
            }
        });

        self.L.log('updateRecentsConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, chunkSize, self._processRecentsData.bind(self), (err) => {
            if(err) {
                self.L.error("updateRecentsConsumer::_prepareDataToInsert Error: ", err );
            }
            self.consumer.commitOffset(lastMessage, (error) => {
                if (error) {
                    self.L.error('updateRecentsConsumer::_processRecentsData::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }
                else {
                    self.L.log('updateRecentsConsumer::_processRecentsData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                }
                recordsToProcess = [];
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'Execution time: ' , executionTime , 'seconds' );
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:UPDATE_RECENTS_CONSUMER", "TIME_TAKEN:" + executionTime]);
                setTimeout(() => {
                    // Resume consumer now
                    self.consumer._resumeConsumer();
                }, self.kafkaResumeTimeout);
            });
        });
   }

   /**
     *
     * @param {object} rechargeData contains recharge data value
     */
    _processRecentsData(recentsDataRow, done) {
        let self = this;
        let operationType = _.get(recentsDataRow, 'operationType', '');
        if(operationType !== 'insert') {
            return done();
        }

        let recentsPayloadRecord = self.convertKafkaPayloadToRecord(recentsDataRow);
        let validationResponse = self._validateRecentsData(recentsPayloadRecord);
        
        if (validationResponse) {
            self.L.error(`processRecord:: Invalid Record record: ${JSON.stringify(recentsPayloadRecord)} error:${validationResponse}`);
            return done();
        }

        ASYNC.waterfall([
            next => {
                return self.billsModel.getRecordForRecentsConsumer(next, recentsPayloadRecord);
            },
            (recordFromBillTable, next) => {
                if(recordFromBillTable.length === 0){
                    return done(`_processRecentsData:: No records found for ${recentsPayloadRecord.debugKey}`)
                }
                let recordsToUpdate =  self.validateRecordsToUpdate(recordFromBillTable, recentsPayloadRecord)
                if(!recordsToUpdate) {
                    return done();
                }
                return next();
                // return self._updateRecents(next, recordsToUpdate);
            }
        ], (error) => {
            if (error) {
                self.L.error(`_processRecentsData`, `Failed with error ${JSON.stringify(error)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:", 'STATUS:ERROR','SOURCE:MAIN_FLOW_EXECUTION']);
            }
            return done();
        });
    }

    convertKafkaPayloadToRecord(payload) {
        let self = this;
        let operationType = _.get(payload, 'operationType', '');
        if(operationType !== 'insert') {
            return 
        }
        let recentsPayloadParams = {
            customerID : _.get(payload, "fullDocument.customer_id", ''),
            rechargeNumber : _.get(payload, "fullDocument.recharge_number", ''),
            operator : _.get(payload, "fullDocument.operator", ''),
            paytype : _.get(payload, "fullDocument.paytype", ''),
            referenceId : _.get(payload, "fullDocument.operatorRecentData.creditCardId", null),
            panUniqueReference : _.get(payload, "fullDocument.operatorRecentData.panUniqueReference", null),
            tin : _.get(payload, "fullDocument.operatorRecentData.tin", null),
            timestamp : MOMENT(_.get(payload, "fullDocument.updated_at.$date", '')).format('YYYY-MM-DD HH:mm:ss'),
            amount : _.get(payload, "fullDocument.price", ''),
            service : _.get(payload, "fullDocument.service", ''),
            tokenisedCreditCard : false
        }
        if(recentsPayloadParams.panUniqueReference != null && recentsPayloadParams.panUniqueReference != ''){ // needs both to verify
            recentsPayloadParams.tokenisedCreditCard = true
        }

        return recentsPayloadParams;
    }

    _validateRecentsData(recentsRecord) {
        let self = this;
        if(!recentsRecord) {
            return 'Invalid record!'
        }

        let response = '';
        let debugKey = `rech_num:${recentsRecord.rechargeNumber}::operator:${recentsRecord.operator}::custId:${recentsRecord.customerID}`;
        _.set(recentsRecord, 'debugKey', debugKey)
        let mandatoryParams = ['rechargeNumber', 'operator', 'service', 'paytype', 'customerID'];
        let fieldsNotPresent = [];

        if(recentsRecord.paytype === 'credit card'){
            if(recentsRecord.tokenisedCreditCard) {
                mandatoryParams.push('panUniqueReference')
                mandatoryParams.push('tin')
            } else {
                mandatoryParams.push('referenceId')
            }
        }

        // sample config wil be : { postpaid: 1, credit card: 1 }
        let paytypeConfig =  _.get(self.config, ['DYNAMIC_CONFIG', "UPDATE_RECENTS", "PAYTYPES"], { postpaid: 1, 'credit card': 1 });
        let approvedPaytypes = Object.keys(paytypeConfig).filter(key => paytypeConfig[key] == 1)

        for (let field of mandatoryParams) {
            if (!recentsRecord[field]) {
                fieldsNotPresent.push(field);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_RECENTS_CONSUMER', 'TYPE:MANDATORY_PARAM_MISSING']);
            }
        }

        // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            response = `Mandatory fields not present:: ${debugKey} Missing params:: ${fieldsNotPresent.join(',')}`;
            return response
        }

        // check for approved paytype
        if(approvedPaytypes && _.isArray(approvedPaytypes) && approvedPaytypes.indexOf(recentsRecord.paytype) == -1){
            response = 'Not an approved paytype';
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_RECENTS_CONSUMER', 'TYPE:WRONG_PAYTYPE']);
            return response
        }
    }

    validateRecordsToUpdate(recordFromBillTable, recentsPayloadRecord){
        let self = this;
        let recordsToUpdate = [];
        let buffer = _.get(self.config, ['DYNAMIC_CONFIG', "UPDATE_RECENTS", "BUFFER", "MINUTES"], 5);
        let recentsCustomerId = recentsPayloadRecord.customerID;
        let paymentDateFromRecord = _.get(recordFromBillTable[0], 'payment_date', null);
        let paymentDate = paymentDateFromRecord ? MOMENT.utc(paymentDateFromRecord).format('YYYY-MM-DD HH:mm:ss') : null; //utc required to offset timezone
        let amount = _.get(recordFromBillTable[0], 'amount', 0);
        let dueDate = MOMENT.utc(_.get(recordFromBillTable[0], 'due_date')).format('YYYY-MM-DD HH:mm:ss');
        let billDate = MOMENT.utc(_.get(recordFromBillTable[0], 'bill_date')).format('YYYY-MM-DD');

        //using payment date from record with recharge number and custID 
        recordFromBillTable.forEach(record => {
            let billsCustId = _.get(record, 'customer_id', '');
            if(billsCustId == recentsCustomerId) {
                paymentDateFromRecord = _.get(record, 'payment_date', null);
                paymentDate = paymentDateFromRecord ? MOMENT.utc(paymentDateFromRecord).format('YYYY-MM-DD HH:mm:ss'): null;
                amount = _.get(record, 'amount', 0);
                dueDate = MOMENT.utc(_.get(record, 'due_date')).format('YYYY-MM-DD HH:mm:ss');
                billDate = MOMENT.utc(_.get(record, 'bill_date')).format('YYYY-MM-DD');
            }
        })

        let timeDiff = paymentDate ? MOMENT(paymentDate).diff(recentsPayloadRecord.timestamp, 'minutes') : 0;

        let customerOtherInfo = _.get(recordFromBillTable[0], 'customerOtherInfo', '');
        if(isNaN(Date.parse(dueDate))){
            dueDate = null
        }
        if(isNaN(Date.parse(billDate))){
            billDate = null
        }
        
        if(!timeDiff || timeDiff + buffer< 0) {
            // instead of 0, should go till 5 min old
            self.L.log(`validateRecordsToUpdate:: skipping record ${recentsPayloadRecord.debugKey} due to stale record in bills table`)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_RECENTS_CONSUMER', 'TYPE:STALE_BILL_RECORD']);
            return;
        }
        if(amount <= 0) {
            self.L.log(`validateRecordsToUpdate:: skipping record ${recentsPayloadRecord.debugKey} due to amount <= 0`)
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_RECENTS_CONSUMER', 'TYPE:SKIPPING_ZERO_AMOUNT']);
            return;
        } else {
            recentsPayloadRecord.amount = amount;
        }
        recordFromBillTable.forEach(record => {
            let recordToUpdate  = {...recentsPayloadRecord};
            recordToUpdate.customerID = record.customer_id; // need to update all customer IDs
            recordToUpdate.dueDate = dueDate;
            recordToUpdate.billDate = billDate;

            if(recordToUpdate.paytype == 'credit card'){
                try{
                    customerOtherInfo = JSON.parse(customerOtherInfo);
                    recordToUpdate.min_due_amount = customerOtherInfo.currentMinBillAmount;
                    recordToUpdate.original_min_due_amount = customerOtherInfo.currentMinBillAmount;
                } catch (err) {
                    self.L.error(`validateRecordsToUpdate:: Error ${err} while parsing customerOtherInfo for ${recentsPayloadRecord.debugKey}`)
                    return;
                }
            }
            recordsToUpdate.push(recordToUpdate) //notice the s
        })

        return recordsToUpdate;
    }

   _updateRecents(next, recordsToUpdate){
        let self = this;
        if(!recordsToUpdate) {
            return next();
        }

        ASYNC.each(recordsToUpdate, (recordToUpdate, cb) => {
            let params = {
                "recharge_number": recordToUpdate.rechargeNumber,
                "operator": recordToUpdate.operator,
                "customer_id": recordToUpdate.customerID,
                "paytype": recordToUpdate.paytype,
                "service": recordToUpdate.service,
            }
    
            let fieldValue = {
                due_date: recordToUpdate.dueDate,
                bill_date: recordToUpdate.billDate,
                amount: _.get(recordToUpdate, 'amount', null),
                original_due_amount: _.get(recordToUpdate, 'amount', null),
                label: _.get(recordToUpdate, 'amount', null) && _.get(recordToUpdate, 'dueDate', null) ? `Bill Payment of Rs ${_.get(recordToUpdate, 'amount', null)} due on ${MOMENT(recordToUpdate.dueDate).format('DD MMM YYYY')}` : null
            };
    
            if(recordToUpdate.paytype == 'credit card'){
                fieldValue.min_due_amount = recordToUpdate.min_due_amount;
                fieldValue.original_min_due_amount = recordToUpdate.original_min_due_amount;
                if(recordToUpdate.panUniqueReference != null){
                    params.panUniqueReference = recordToUpdate.panUniqueReference;
                    params.tin = recordToUpdate.tin
                } else {
                    params.reference_id = recordToUpdate.referenceId;
                }
            }

            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_RECENTS_CONSUMER', 'TYPE:SUCCESS']);
            self.recentsLayer.update(cb, params, "bills", [fieldValue], "BillReminder");
        }, (err) => {
            if(err) {
                self.L.error("_updateRecents:: Error while API call to update recents", err)
            }
        });
        return next();
    }
    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`updateRecentsConsumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`updateRecentsConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`updateRecentsConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`updateRecentsConsumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`updateRecentsConsumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default updateRecentsConsumer;
