import ASYNC from 'async';
import _ from 'lodash';
import OS from 'os';
import BILLS from '../models/bills';
import utility from '../lib';
import { Kafka, CompressionTypes, CompressionCodecs } from 'kafkajs';
import SnappyCodec from 'kafkajs-snappy';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import Q from 'q'
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import Logger from '../lib/logger';


CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;


class notifyRejectedBills {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.stopService = false;
        this.dbInstance = options.dbInstance;
        this.infraUtils = options.INFRAUTILS;
        this.bills = new BILLS(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.encryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new Logger(options);
        this.greyScaleEnv = options.greyScaleEnv;
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.chunkSize = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'CHUNK', 'SIZE'], 10);
        this.enabledServicesPaytypes = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'ENABLED_SERVICES_PAYTYPES', 'SERVICE_PAYTYPE_LIST'], ['FINANCIAL_SERVICES_CREDIT_CARD']);
        this.percentageLiveCustomers = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'PERCENTAGE_LIVE_CUSTOMERS', 'PERCENTAGE'], 0);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

        setInterval(() => {
            this.L.log('notifyRejectedBills :: Consumer is running..!!');
        }, 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: notifyRejectedBills", "Re-initializing variable after interval");
        self.notificationConfig = _.get(self.config, 'NOTIFICATION');
        self.chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'CHUNK', 'SIZE'], 10);
        self.enabledServicesPaytypes = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'ENABLED_SERVICES_PAYTYPES', 'SERVICE_PAYTYPE_LIST'], ['FINANCIAL_SERVICES_CREDIT_CARD']);
        self.percentageLiveCustomers = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'PERCENTAGE_LIVE_CUSTOMERS', 'PERCENTAGE'], 0);
        let variables = {
            'chunkSize': self.chunkSize,
            'enabledServicesPaytypes': self.enabledServicesPaytypes,
            'percentageLiveCustomers': self.percentageLiveCustomers,
        }
        self.L.verbose("initializeVariable :: notifyRejectedBills", "Re-initialized variables", JSON.stringify(variables));
    }

    start() {
        let self = this;

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('start :: notifyRejectedBills', 'unable to configure kafka', error);
                process.exit(0);
            } else {
                self.L.log('start :: notifyRejectedBills', 'Kafka Confugured successfully !!');
            }
        });
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            function (callback) {
                self.nonruBillFetchKafkaRealtime = new self.infraUtils.kafka.producer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS')
                });
                self.nonruBillFetchKafkaRealtime.initProducer('high', function (error) {
                    if (!error)
                        self.L.log("nonruBillFetchKafkaRealtime::", "publisher Configured");
                    else{
                        self.L.error("nonruBillFetchKafkaRealtime::","Error while configuring Publusher",error)
                    }
                    return callback(error);
                }); 
            },
            function(callback){
                self.kafkaConsumer = new KafkaConsumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.BILL_FETCH_ANALYTICS.HOSTS'),
                    "groupId": "notifyRejectedBills-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.BILL_FETCH_ANALYTICS.TOPIC'),
                    "id": 'notifyRejectedBills_' + OS.hostname() + '_' + process.pid,
                    sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                    maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
                });
                self.kafkaConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (error) {
                        self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                    }
                    self.L.log(`configureKafka consumer of topic : ${ _.get(self.config.KAFKA, 'SERVICES.BILL_FETCH_ANALYTICS.TOPIC')} Configured`);
                    return callback(error);
                });
            }
        ],
        function(error){
            return done(error);
        });
    }

    execSteps(records, resolveOffset, topic, partition, cb) {
        let self = this,
            chunkSize = self.chunkSize,
            startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return cb();
        }

        self.L.log('execSteps:: ', `Processing ${records.length} rejected bills data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:STORE_NOTIFY_REJECTED_BILLS', 'STATUS:CONSUMED']);
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 10);
                });
            },
            async (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("notifyRejectedBills", records,topic , partition);

                // commit message offset
                await resolveOffset(lastMessage.offset);
                self.L.log('execSteps :: ', 'Commit success for offset:', _.get(lastMessage, 'offset') + ', topic: ' + topic + ', partition: ' + partition + ', timestamp: ' + _.get(lastMessage, 'timestamp'));

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:notifyRejectedBills", "TIME_TAKEN:" + executionTime]);
                if(self.stopService) {
                    self.L.log("Gracefull shutdown started, not resuming consumer");
                    return cb();
                }
                // Resume consumer now
                if(self.greyScaleEnv) {
                    setTimeout(function(){
                        cb();
                    },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NOTIFY_REJECTED_BILLS', 'BATCH_DELAY'],10));
                } else {
                    cb();
                }
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processRejectedBills(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processRejectedBills(done, data) {
        let self = this;
        try {
            let record = self.validateData(data); 
            self.L.log('notifyRejectedBills :: notifyRejectedBills', 'Processing record', _.get(record, 'debugKey', null));
            let templatesForRejectedBills = self.getRejectedBillTemplates(record);
            let payloadForNotification = self.getPayloadForNotification(record, templatesForRejectedBills);

            self.publishToNonruNotificationCreate(payloadForNotification, (error) => {
                done();
            });
        } catch (error) {
            self.L.error('storenotifyRejectedBills :: notifyRejectedBills', error.message);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NOTIFY_REJECTED_BILLS', 'STATUS:ERROR_OVERALL']);
            return done();
        }
    }

    publishToNonruNotificationCreate(data, done) {
        let self=this,
        payload = [{
            topic: _.get(self.config, 'KAFKA.SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME'),
            messages: JSON.stringify(data)
        }]

        self.nonruBillFetchKafkaRealtime.publishData(payload, function (error) {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS", 'STATUS:ERROR', `TYPE:NONRU_REMINDER_BILL_FETCH_REALTIME`]);
                self.logger.critical(`Error while publishing message in Kafka ${error}- MSG:- `, data, _.get(data, 'data.service', null));
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS", 'STATUS:PUBLISHED', `TYPE:NONRU_REMINDER_BILL_FETCH_REALTIME`]);
                self.logger.log('Message published successfully in Kafka on topic NONRU_REMINDER_BILL_FETCH_REALTIME', payload, _.get(data, 'data.service', null));
            }
            return done(null);
        }, [200, 800]);
    }

    getPayloadForNotification(record, templates) {
        let self = this;
        let payload = {
            source: "notifyRejectedBills",
            notificationType: "BILLGEN",
            data : {
                customer_id: _.get(record, 'customer_id', null),
                recharge_number: _.get(record, 'recharge_number', null),
                operator: _.get(record, 'operator', null),
                amount: _.get(record, 'due_amount', null),
                due_date: _.get(record, 'due_date', null),
                bill_date: _.get(record, 'bill_date', null),
                bill_fetch_date: _.get(record, 'bill_fetch_date', null),
                paytype: _.get(record, 'paytype', null),
                service: _.get(record, 'service', null),
                source : _.get(record, 'source', null),
                source_subtype_2 : _.get(record, 'source_subtype_2', null),
                user_type : _.get(record, 'user_type', null),
                reject_reason : _.get(record, 'reject_reason', null),
                sender_id : _.get(record, 'sender_id', null),
                sms_date_time : _.get(record, 'sms_date_time', null),
                dwh_class_id : _.get(record, 'dwh_class_id', null),
                additional_info : _.get(record, 'additional_info', null),
                created_at : _.get(record, 'created_at', null),
                updated_at : _.get(record, 'updated_at', null),
                sms_id : _.get(record, 'sms_id', null),
                rawlastcc : _.get(record, 'rawlastcc', null),
                product_id : _.get(record, 'product_id', -1), //keeping default value as -1 to bypass non null pid conditions in notification flow 
                category_id : _.get(record, 'category_id',null),
                time_interval : _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'NOTIFICATION_FREQUENCY', 'DUPLICACY_CHECK_IN_MINUTES'], 44640),  //31days
                customer_other_info : JSON.stringify({"rawlastcc": _.get(record, 'rawlastcc', null)}),
                templates: templates
            }
        };

        return payload;
    }

    getRejectedBillTemplates(record) {
        let self = this;
        let templates = {};
        for (let key in self.notificationConfig.type) {
            if (this.notificationConfig.type[key]) {
                templates[key] = self.getTemplateId(record, key);
            }
        }
        self.L.verbose("getTemplates, templates: ", templates, " for debug_key: ", _.get(record, 'debugKey', null));
        return templates;
    }

    getTemplateId(record, key) {
        let self = this,templateId;
        let error_code = _.get(record, 'error_code', null);

        let templateKeyBasedOnErrorCode = `REJECTED_BILL_${_.get(record, 'service_paytype',null)}_${error_code}_${key}`;
        let genericTemplateKey = `REJECTED_BILL_${_.get(record, 'service_paytype',null)}_${key}`;

        if(key=='PUSH'){
            self.L.verbose("templateKeyBasedOnErrorCode: ", templateKeyBasedOnErrorCode);
            self.L.verbose("genericTemplateKey: ", genericTemplateKey);
        }

        if(self.isMandatoryParamsPresentForSpecilizedTemplate(record)){
            templateId =  _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS' ,'REJECTED_BILL_TEMPLATES', templateKeyBasedOnErrorCode],
            _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'REJECTED_BILL_TEMPLATES', genericTemplateKey], null));
        }else{
            templateId = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'REJECTED_BILL_TEMPLATES', genericTemplateKey], null);
        }
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:NOTIFICATION_TEMPLATE_FOUND' ,`TYPE:${templateId}`, `SERVICE_PAYTYPE:${_.get(record, 'service_paytype',null)}`, `CHANNEL:${key}`]);
        return templateId;
    }

    isMandatoryParamsPresentForSpecilizedTemplate(record){
        let self= this;
        let error_code = _.get(record, 'error_code', null);
        let mandatoryParams = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'MANDATORY_PARAMS_FOR_ERROR_CODE', error_code], [])
        // There can be a case where key is present but value is null
        let isMandatoryParamsPresent = mandatoryParams.every((param) => {
            return _.get(record, param, null);
        });
        if(isMandatoryParamsPresent){
            return true;
        }else{
            return false;
        }
    }

    validateData(data) {
        let self = this,
            record;
        try {
            //not changing verbose logs as it is used for debugging
            record = self.convertKafkaPayloadToRecord(data);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS", 'STATUS:TRAFFIC_CATEGORY_WISE', `TYPE:${_.get(record, 'service_paytype',null)}`]);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'Record after conversion', JSON.stringify(record));
        } catch (error) {
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', error.message, JSON.stringify(data));
            throw new Error(error);
        }

        // Mandatory params check
        let mandatoryParams = ['customer_id', 'paytype', 'service', 'reject_reason', 'recharge_number'];
        let missingParams = mandatoryParams.filter(param => {
            const value = _.get(record, param);
            return value === undefined || value === null || value ==='';
        });
        if (missingParams.length) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:MISSING_MANDATORY_PARAMS']);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'Missing mandatory params', JSON.stringify(record));
            throw new Error(`Missing mandatory params : ${missingParams.join(', ')} for debugKey : ${_.get(record, 'debugKey', null)}`);
        }

        if (!_.includes(self.enabledServicesPaytypes, _.get(record, 'service_paytype',null))) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:SERVICE_NOT_ENBALED']);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'Service_paytype not enabled', JSON.stringify(record));
            throw new Error(`Service_paytype not enabled : ${_.get(record, 'service_paytype',null)} for debugKey : ${_.get(record, 'debugKey', null)}`);
        }

        let category_id = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'CATEGORY_ID_MAP', _.get(record, 'service_paytype',null)],null);
        if(!category_id){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:CATEGORY_ID_NOT_FOUND']);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'CategoryId not found for this service_paytype', JSON.stringify(record));
            throw new Error(`CategoryId not found for this service_paytype : ${_.get(record, 'debugKey', null)}`);
        }else{
            _.set(record, 'category_id', category_id);
        }

        // Notification enabled for percentage of live customers
        if (!self.enableNotificationForPercentage(_.get(record, 'customer_id', null))) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:CUSTOMER_NOT_ENBALED']);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'Notification not enabled for this customer', JSON.stringify(record));
            throw new Error(`Notification not enabled for this customer : ${_.get(record, 'debugKey', null)}`);
        }

        let enabaledSources = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'ENABLED_SOURCES', _.get(record, 'service_paytype', null)], []);
        if(!_.get(record, 'source', null) || !enabaledSources.includes(_.get(record, 'source',null))){
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:SOURCE_NOT_ENBALED']);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'Source not enabled', JSON.stringify(record));
            throw new Error(`Source not enabled: ${_.get(record, 'debugKey', null)}`);
        }

        /*
        * Reject reason present in list can have sub string of reject reason
        * Reject reason should be present in error message list
        */
        let errorMessages = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'ENABLED_ERROR_MESSAGES', _.get(record, 'service_paytype', null)], []);
        let rejectReason = _.get(record, 'reject_reason', null);
        //we want to save which error message was present in reject reason
        let errorMessage = errorMessages.find(message => rejectReason.includes(message));
        if (!errorMessage) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:ERROR_MESSAGE_NOT_ENBALED']);
            self.L.verbose(`storenotifyRejectedBills :: notifyRejectedBills Reject reason ${rejectReason} not enabled  ${JSON.stringify(record)} and reject_reasons enabled are ${errorMessages}`);
            throw new Error(`Reject reason not enabled : ${rejectReason} for debugKey : ${_.get(record, 'debugKey', null)}`);
        }
        _.set(record, 'standard_error_message', errorMessage);

        // Error message to error code mapping
        let errorCode = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_REJECTED_BILLS', 'ERROR_MESSAGE_TO_ERROR_CODE_MAPPING', errorMessage], null);
        if (!errorCode) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFY_REJECTED_BILLS",'STATUS:VALIDATION_FAILURE' ,'TYPE:ERROR_CODE_MISSING']);
            self.L.verbose('storenotifyRejectedBills :: notifyRejectedBills', 'Error code not found for reject reason', JSON.stringify(record));
            throw new Error(`Error code not found for reject reason : ${rejectReason} and standard_error_message : ${errorMessage} for debugKey : ${_.get(record, 'debugKey', null)}`);
        }else{
            _.set(record, 'error_code', errorCode);
        }
        return record;
    }

    enableNotificationForPercentage(customerId) {
        let self = this;
        let threshold = 100 / self.percentageLiveCustomers;
        return customerId % threshold === 0;
    }

    convertKafkaPayloadToRecord(kafkaPayload) {
        let self = this,
            data;
        try {
            data = JSON.parse(_.get(kafkaPayload, 'value', null));
            self.L.verbose('convertKafkaPayloadToRecord :: notifyRejectedBills', 'Data after parsing', JSON.stringify(data));
        } catch (error) {
            self.L.error('convertKafkaPayloadToRecord :: notifyRejectedBills', error.message, JSON.stringify(kafkaPayload));
            throw new Error(error);
        }
        let service = _.get(data, 'service', '')? _.get(data, 'service', '').replace(/\s/g, '_') : '';
        let paytype = _.get(data, 'paytype', '')? _.get(data, 'paytype', '').replace(/\s/g, '_') : '';
        return {
            customer_id: _.get(data, 'customer_id', null),
            recharge_number: _.get(data, 'recharge_number', null),
            operator: _.get(data, 'operator', null),
            amount: _.get(data, 'due_amount', null),
            due_date: _.get(data, 'due_date', null),
            bill_date: _.get(data, 'bill_date', null),
            bill_fetch_date: _.get(data, 'bill_fetch_date', null),
            paytype: _.get(data, 'paytype', null),
            service: _.get(data, 'service', null),
            source : _.get(data, 'source', null),
            source_subtype_2 : _.get(data, 'source_subtype_2', null),
            user_type : _.get(data, 'user_type', null),
            reject_reason : _.get(data, 'reject_reason', null),
            sender_id : _.get(data, 'sender_id', null),
            sms_date_time : _.get(data, 'sms_date_time', null),
            dwh_class_id : _.get(data, 'dwh_class_id', null),
            additional_info : _.get(data, 'additional_info', null),
            created_at : _.get(data, 'created_at', null),
            updated_at : _.get(data, 'updated_at', null),
            sms_id : _.get(data, 'sms_id', null),
            rawlastcc : _.get(data, 'rawlastcc', null),
            service_paytype : `${_.toUpper(service)}_${_.toUpper(paytype)}`,
            debugKey : `CID_${_.get(data, 'customer_id', null)}_RN_${_.toLower(_.get(data, 'service', null)) == 'financial services' ? self.encryptionDecryptioinHelper.encryptData(_.get(data, 'recharge_number', null)) : _.get(data, 'recharge_number', null)}_OP_${_.get(data, 'operator', null)}`
        };
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`notify rejected bill consumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.kafkaConsumer.close(function(error, res){
                if(error){
                    self.L.error(`notify rejected bill consumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`notify rejected bill consumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`notify rejected bill consumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`notify rejected bill consumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default notifyRejectedBills;