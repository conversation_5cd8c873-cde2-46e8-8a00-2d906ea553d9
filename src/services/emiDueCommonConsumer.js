import _ from 'lodash'
import utility from '../lib'
// import RecentsLayerLib from '../lib/recentsLayer'
import VA<PERSON><PERSON><PERSON>OR from 'validator'
import ASYNC from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';
import Q from 'q'

const env = (process.env.NODE_ENV || 'development').toLowerCase();

class EmiDueCommonConsumer {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.operator = null;
        // this.recentsLayerLib = new RecentsLayerLib(options); // TODO
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        // TODO entry 
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'EMI_DUE_COMMON_DETAILS', 'BATCHSIZE'], 2) : 500; 
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'EMI_DUE_COMMON_DETAILS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
    }

    start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('EmiDueCommonConsumer :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('EmiDueCommonConsumer :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Publisher for REMINDER_BILL_FETCH topic');
                self.kafkaBillFetchPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                self.kafkaBillFetchPublisher.initProducer('high', function (error) {
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                 self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : EMI_DUE_COMMON_DETAILS');

                // Initialize consumer of topic REMINDER_BILL_FETCH
                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.EMI_DUE_COMMON_DETAILS.HOSTS'),
                    "groupId": "emiDueCommonBills-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.EMI_DUE_COMMON_DETAILS.EMI_DUE_COMMON_DETAILS_TOPIC'),
                    "id": `emiDueCommonBills-consumer_${OS.hostname()}_${process.pid}`, 
                    "fromOffset": "latest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };
                self.kafkaEMIDueCommonConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkaEMIDueCommonConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : EMI_DUE_COMMON_DETAILS Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

            let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaEMIDueCommonConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} EMI DUE COMMON Bills data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:EMI_DUE_COMMON_DETAILS_TRAFFIC']);
        
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaEMIDueCommonConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now
                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:EMI_DUE_COMMON_CONSUMER", "TIME_TAKEN:" + executionTime]);
                    
                    setTimeout(function () {
                        self.kafkaEMIDueCommonConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processRecords(() => {
                    return next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    
    processRecords(done, record) {
        let self = this;
            
        try {
            let error = null,
                processedRecord = null;

            [error, record] = self.validateRecord(record);

            if (error) { 
                self.L.error(`processRecords`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:VALIDATION_FAILED']);
                return done(error);
            }
        
            [error, record] = self.checkOperatorEligibility(record);

            if (error) {
                self.L.error(`processRecords`, `Operator is not eligible ${JSON.stringify(record)} with error ${error}`);
                return done(error);
            }

            [error, processedRecord] = self.processRecord(record);
            if (error) {
                self.L.error(`processRecords`, `Unable to process record ${JSON.stringify(record)} with error ${error}`);
                return done(error);
            }


            self.L.log(`Processing ${JSON.stringify(record)} record for`, processedRecord.debugKey);

            let dbRecord;
            ASYNC.waterfall([
                next => {
                    dbRecord = self.getDbRecordToUpdate(processedRecord);
                    return self.createAndUpdateBills(next, dbRecord);
                },
                next => {
                    return self.sendNotification(next, dbRecord);
                },
                next => {
                    return self.publishCtEvents(next, dbRecord);
                }
            ], function (error) {
                return done(null);
            });
        } catch(err) {
            self.L.critical(`processRecords`,`Exception occured for record:${record}`,err)
            return done(null);
        }
    }

    createAndUpdateBills(done, params) {
        let self = this,
            tableName = _.get(this.config, ['OPERATOR_TABLE_REGISTRY', self.operator], null);

        if (tableName) {
            ASYNC.series([
                function (cb) {
                    self.bills.createBill(function (error, data) {
                        // done(error, data);
                        return cb(error, data);
                    }, tableName, params, false, 'emiDueService');
                }
            ], function (error) {
                if (error) {
                    self.L.critical("createAndUpdateBills::create bill", "Error in create api :: ", error);
                }
                return done(error);
            });
        } else {
            self.L.error("createAndUpdateBills::create bill", "table not found for::", params.operator);
            return done('Operator not migrated.');
        }
    }

    /**
     * Send Notification : Send PayLoad to Kafka Topic
     * @param {*} done 
     * @param {*} record 
     */
    sendNotification(done, record) {
        let
            self = this,
            payload = {
                source: "emiDueBillFetch",  /** To be discussed -> helps in notification table for tracking */
                notificationType: "BILLGEN",
                data: self.commonLib.mapBillsTableColumns(record)
            };

        self.kafkaBillFetchPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE.REMINDER_BILL_FETCH', ''),
            messages: JSON.stringify(payload)
        }], function (error) {
            if (error) {                
                utility._sendMetricsToDD(1, ['STATUS:ERROR','REQUEST_TYPE:EMI_DUE_COMMON_DETAILS_BILLGEN_NOTIFICATION', `OPERATOR:${_.get(record, 'operator', null)}`]);
                self.L.critical('sendNotification :: kafkaBillFetchPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
            } else {
                utility._sendMetricsToDD(1, ['STATUS:PUBLISHED','REQUEST_TYPE:EMI_DUE_COMMON_DETAILS_BILLGEN_NOTIFICATION' , `OPERATOR:${_.get(record, 'operator', null)}` ]);
                self.L.log('sendNotification :: kafkaBillFetchPublisher', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
            }
            return done(error);
        }, [200, 800]);
    }
    /**
     * 
     * @param {*} processedRecord 
     */
     getDbRecordToUpdate(processedRecord) {
        let self = this,
            dateFormat = 'YYYY-MM-DD HH:mm:ss',
            productId = _.get(processedRecord, 'productId', null),
            operator =  self.operator,
            updatedDbRecord = { 
                customerId : _.get(processedRecord, 'customerId', null),
                rechargeNumber : _.get(processedRecord, 'rechargeNumber', null),
                productId : productId,
                operator : operator,
                amount : _.get(processedRecord, 'amount', 0),
                dueDate : null, 
                billDate : MOMENT().format(dateFormat),                                
                bill_fetch_date : MOMENT().format(dateFormat),
                service_id : 0,                                                         
                customerOtherInfo :  _.clone(processedRecord.rawKafkaPayload),          
                paytype : _.get(self.config, ['CVR_DATA', productId, 'paytype'], null),
                service : _.get(self.config, ['CVR_DATA', productId, 'service'], null),
                circle :  _.get(self.config, ['CVR_DATA', productId, 'circle'], null),
                gateway : null,                                                         // During insert operation 1.)rule engine -> 2.)OPERATOR_GATEWAY_REGISTRY 
                customerMobile : _.get(processedRecord, 'customerMobile', null),
                customerEmail : _.get(processedRecord, 'customerEmail', null),
                nextBillFetchDate : null, 
                paymentChannel : null,
                retryCount : 0,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                reason : '',
                extra : '',
                published_date : MOMENT().format(dateFormat),
                user_data : JSON.stringify({'recharge_number_2' : _.get(processedRecord, 'recharge_number_2', null)}), // To Do verify
                notification_status : 1,
                paymentDate : null,
                is_automatic : 0
            };
        updatedDbRecord.customerOtherInfo = JSON.stringify(updatedDbRecord.customerOtherInfo);
        /**
         * Assumption we are not updating across customer ids for same recharge number
         */
        updatedDbRecord.updateAllCustIdRecords = false;
        // use for how much time we did not send duplicate notification
        updatedDbRecord.time_interval = _.get(processedRecord, 'time_interval', null); 

        return updatedDbRecord;
    }

    /**
     * 
     * @param {*} record 
     */
     validateRecord(record){
        if (!record) return ['Invalid record', record];
        let self = this;
        try {
            if (!_.has(record, 'value')) return ['Invalid Kafka record received', record];
            
            record = JSON.parse(_.get(record, 'value', null));
        } catch (error) {
            if (error) {
                self.L.critical('validateAndProcessRecord', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_RECORD_VALIDATION', 'STATUS:ERROR' , 'TYPE:PARSING_ERROR']);
                return ['Kafka record Parsing Error', record];
            }
        }
        return [null , record];
    }

    checkOperatorEligibility(record) {
        let self = this;

        /** To DO
         *  add "reminder_operator" key in kafka record
         *  reminder_operator : <"aditya birla finance limited"/"fullerton india credit company limited"/"fullerton india housing finance limited">
        */

        self.operator = _.get(record, 'reminder_operator', null); 
        
        if (!self.operator) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_OPERATOR_VALIDATION', `OPERATOR:${self.operator}`, 'STATUS:ERROR', 'TYPE:OPERATOR_NAME_NOT_FOUND']);
            return ['Error in finding product_id/operator', record];    
        }

        if(!_.has(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , env, self.operator]) ) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_OPERATOR_VALIDATION', `OPERATOR:${self.operator}`, 'STATUS:ERROR', 'TYPE:OPERATOR_CONFIG_NOT_FOUND']);
            return [`Operator ${self.operator} config. not available...skipping it` , record ];
        }

        if( _.has(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , 'common', 'BLACKLIST_EMI_DUE_OPERATORS' , self.operator ] ) ) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_OPERATOR_VALIDATION', `OPERATOR:${self.operator}`, 'STATUS:ERROR', 'TYPE:BLACKLIST_OPERATOR']);
            return [`Operator ${self.operator} is blacklisted...skipping it` , record ];
        }

        if(!_.get(self.config, ['OPERATOR_TABLE_REGISTRY', self.operator], null)) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_OPERATOR_VALIDATION', `OPERATOR:${self.operator}`, 'STATUS:ERROR', 'TYPE:OPERATOR_TABLE_REGISTRY_NOT_FOUND']);
            return [`table not found for:: ${self.operator}` , record ];    
        }
        utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_OPERATOR_VALIDATION', `OPERATOR:${self.operator}`, 'STATUS:SUCCESS', 'TYPE:MAIN_FLOW_EXECUTION']);
        return [null, record];
    }

    mapTableColumnsWithKafkaPayload() {
        let self = this;
        /** these config. can be shift to DB later on */
        let mapper_table_column_with_kafka = _.get(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , env, self.operator , 'mapper_table_column_with_kafka'] , null);
        /**
        {
            "customerId" :  {
                "kafkaKey" : "cust_id",
                "mandatoryParams" : true,
                "numeric" : true
            },
            "rechargeNumber" : {
                "kafkaKey" : "loan_agreement_no",
                "mandatoryParams" : true
            },
            // phase 1 -> application_id
            // phase 2 -> loan_agreement_no
            "amount" : {
                "kafkaKey" : "<emi_due>",
                "mandatoryParams" : true,
                "parseAmount" : true
            },
            // phase 1 -> 
            // phase 2 -> 
            "customerEmail" : {
                "kafkaKey" : "cust_mailing_add_email"
            },
            "customerMobile" : {
                "kafkaKey" : "customer_add_mobile"
            },
            "currentMinBillAmount" : {
                "kafkaKey" : "<emi_amount/loan_amount>",
                "parseAmount" : true
            },
            // phase 1 -> 
            // phase 2 -> 
            "recharge_number_2" : {
                "kafkaKey" : "loan_agreement_no" // To DO
            }
            // phase 1 -> 
            // phase 2 -> 
        }
        */
        self.L.log('mapper_table_column_with_kafka ', JSON.stringify(mapper_table_column_with_kafka) );
        return mapper_table_column_with_kafka;
    }

    getNumericDataAfterValidation(number) {
        return (typeof number === 'number') ? number : (typeof number === 'string' && VALIDATOR.isNumeric(number)) ? VALIDATOR.toInt(number) : null;
    }

    /**
     * 
     * @param {*} record 
     */
    processRecord(record) {
        let self = this,
            productId = _.get(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , env, self.operator , 'product_id'] , null),
            processedRecord = {},  
            tableColumnAndKafkaMapper = self.mapTableColumnsWithKafkaPayload(),
            mandatoryParamsMap = {
                'productId'        : 1 , 
                'customerId'       : 1 ,
                'rechargeNumber'   : 1
            };
        productId = (typeof productId === 'number') ? productId : (typeof productId === 'string' && VALIDATOR.isNumeric(productId)) ? VALIDATOR.toInt(productId) : null;
        
        for (let processedRecordKey in tableColumnAndKafkaMapper) {
            let kafkaKey =   _.get(tableColumnAndKafkaMapper , [processedRecordKey , 'kafkaKey' ] ,null);
            let value = _.get( record , [kafkaKey] , null);
            
            if(_.get(tableColumnAndKafkaMapper , [processedRecordKey , 'numeric' ] ,false)) {
                value = self.getNumericDataAfterValidation(value);
            } 
            if(_.get(tableColumnAndKafkaMapper , [processedRecordKey , 'parseAmount' ] ,false)) {
                value = self.parseAmount(value);
            }
            if(_.get(tableColumnAndKafkaMapper , [processedRecordKey , 'parseDate' ] ,false)) {
                let parsedDateOfBirthObj = self.getParsedDate(value, "YYYY-MM-DD");
                value = parsedDateOfBirthObj.isDateFmtValid ? parsedDateOfBirthObj.value : null;
            }
            if(typeof value == "number" && processedRecordKey == "rechargeNumber") {
                value = value.toString();
            }
            if(_.get(tableColumnAndKafkaMapper , [processedRecordKey , 'mandatoryParams' ] ,false)) {
                mandatoryParamsMap[processedRecordKey] = 1;
            }
            processedRecord[processedRecordKey] = value;
        }
        
        processedRecord["productId"] = productId;
        processedRecord["time_interval"] = _.get(self.config, ['COMMON' , 'EMI_DUE_CONSUMER_CONFIG' , env, self.operator , 'time_interval'] , null);
        processedRecord["rawKafkaPayload"] = record;
        
        let invalidParams = [];
        for (let mandatoryParam in mandatoryParamsMap) {
            if (!processedRecord[mandatoryParam]) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_RECORD_VALIDATION', `OPERATOR:${self.operator}`, 'STATUS:ERROR', 'TYPE:MANDATORY_PARAM_MISSING', `MISSING_PARAM:${mandatoryParam}`]);
                invalidParams.push(mandatoryParam);
            }
        }
        // checking amount exists and has value > 0
        if(typeof processedRecord['amount'] != 'number' || processedRecord['amount'] <= 0) invalidParams.push('amount');

        processedRecord.debugKey = `customerId:${processedRecord.customerId}_rechargeNumber:${processedRecord.rechargeNumber}_amount:${processedRecord.amount}${processedRecord.customerEmail ? ('_customerEmail:' + processedRecord.customerEmail) : '' }${processedRecord.customerMobile ? ('_customerMobile:' + processedRecord.customerMobile)  : ''}`;

        if (invalidParams.length > 0) return [`Mandatory Params ${invalidParams} is Missing / Invalid`, record];
        else {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:EMIDUECOMMONCONSUMER_RECORD_VALIDATION',`OPERATOR:${self.operator}`, 'STATUS:SUCCESS', 'TYPE:MAIN_FLOW_EXECUTION']);
            return [null, processedRecord];     
        }
    }

    /**
     * 
     * @param {*} amountStr 
     */
    parseAmount(amountStr) { 
        if (amountStr === 0 ) return 0;
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && !isNaN(amountStr) && amountStr !== ' ') return parseFloat(VALIDATOR.toFloat(amountStr).toFixed(2));
        
        if (typeof amountStr === 'string') {
            let commaRegex = new RegExp(",","g")
            amountStr = amountStr.replace(commaRegex, "");
            
            if (!isNaN(amountStr) && amountStr !== ' ') {
                return parseFloat(VALIDATOR.toFloat(amountStr).toFixed(2));
            }
        }
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        return parsedAmount;
    }

    /**
     * 
     * @param {*} done 
     * @param {*} processedRecord 
     */
     publishCtEvents(done, dbRecordResp) {
        let self = this;
        const customerId = _.get(dbRecordResp, 'customerId', '');
        const operator = _.get(dbRecordResp, 'operator', '');
        const rechargeNumber = _.get(dbRecordResp, 'rechargeNumber', '');
        const eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG','CT_EVENTS', 'BILLGEN'], 'reminderBillGen')
        const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;

        let productId = _.get(dbRecordResp, 'productId', '');
        productId = self.activePidLib.getActivePID(productId);
        
        if (!_.get(dbRecordResp, 'notification_status', 1)) {
            self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${_.get(dbRecordResp, 'notification_status', 0)} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            return done(null)
        } 	

        ASYNC.waterfall([
            next => {
                self.commonLib.getRetailerData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if(error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {                  
                let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                self.ctKafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                    messages: JSON.stringify(mappedData)
                }], (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EMI_DUE_COMMON", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.critical('publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka on topic REMINDER_CT_EVENTS - MSG:- ' + JSON.stringify(clonedData), error);
                    } else {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:EMI_DUE_COMMON", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                        self.L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                    }
                    return next(error);
                }, [200, 800]);
            }
        ], error => {
            if(error) {
                self.L.error('publishCtEvents',`Exception occured Error Msg:: ${error} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
            } else {
                self.L.log(`publishCtEvents`,`Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }

    /**
     * 
     * @param {*} date 
     * @param {*} dateFormat 
     * @returns 
     */
     getParsedDate(date, dateFormat = "YYYY-MM-DD") {
        let self = this;
        
        try {
            if(!date) {
                return {
                    value : null,
                    isDateFmtValid : false
                }
            }
        
            if(typeof date !== 'string') {
                self.L.error("getParsedDate:: invalid value received for date", date);
                return {
                    value: null,
                    isDateFmtValid: false
                };
            }
        
            let formattedDate, isDateFmtValid,
                //Fmt:  DD-MM-YYYY
                regex1 = new RegExp(/([0][1-9]|[1-2][0-9]|[3][0-1])-([0][1-9]|[1][0-2])-[1-9][0-9][0-9][0-9]/g),
                //Fmt:  YYYY-MM-DD
                regex2 = new RegExp(/[1-9][0-9][0-9][0-9]-([0][1-9]|[1][0-2])-([0][1-9]|[1-2][0-9]|[3][0-1])/g);
            
            if(date.match(regex1) !== null && date.split("-").length == 3) {
                formattedDate = [...date.split("-")];
                formattedDate = (dateFormat === "YYYY-MM-DD") ? (formattedDate[2]+"-"+formattedDate[1]+"-"+formattedDate[0]) : date;
                isDateFmtValid = true;
            } else if (date.match(regex2) !== null && date.split("-").length == 3) {
                formattedDate = [...date.split("-")];
                formattedDate = (dateFormat === "YYYY-MM-DD") ? date :  (formattedDate[2]+"-"+formattedDate[1]+"-"+formattedDate[0]) ;
                isDateFmtValid = true;
            } else {
                formattedDate = null;
                isDateFmtValid = false;
            }
            return {
                value : formattedDate,
                isDateFmtValid : isDateFmtValid
            };
        } catch (error) {
            self.L.error(`getParsedDate:: invalid value received for date ${date} Error msg:`, error);
            return {
                value: null,
                isDateFmtValid: false
             };
        }
        
    }
    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`emiDueCommonConsumer::suspendOperations kafka consumer shutdown initiated`);

        Q()
        .then(function(){
            self.kafkaEMIDueCommonConsumer.close(function(error, res){
                if(error){
                    self.L.error(`emiDueCommonConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`emiDueCommonConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`emiDueCommonConsumer::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`emiDueCommonConsumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }
}

export default EmiDueCommonConsumer;