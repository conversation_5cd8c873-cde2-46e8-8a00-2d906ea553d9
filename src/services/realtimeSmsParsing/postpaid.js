import <PERSON><PERSON><PERSON><PERSON>OR from 'validator';
import MOMENT from 'moment';
import REQUEST from 'request';
import <PERSON>YNC from 'async'
import _ from 'lodash';
import utility from '../../lib';
import OS from 'os';
import prepaidSmsparsing from '../smsParsingBillPayment/prepaidSmsParsing';
import postpaidSmsParsing from '../smsParsingBillPayment/postpaidSmsParsing';
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills'
import DigitalCatalog from '../../lib/digitalReminderConfig'
import INFRAUTILS from 'infra-utils'
import digitalUtility from 'digital-in-util'
import { promisify } from 'util'
import Q from 'q'

class realtimeSmsParsing {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.prepaidSmsparsing = new prepaidSmsparsing(options, this);
        this.postpaidSmsParsing = new postpaidSmsParsing(options, this);
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.digitalCatalogLib = DigitalCatalog;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        let { MONGO_DB, MYSQL, KAFKA } = this.config.VIL_SYNC_DB;
        this.mongoDbInstance = new INFRAUTILS.mongo(this.config.MONGO.HIDDEN_SLAVE);
        this.mongoCollection = 'users';
        this.retryCountForMongo = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'RETRY_COUNT'], MONGO_DB.RETRY_COUNT);
        this.commonLib = new utility.commonLib(options);
        this.mongoDbTps = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS'], MONGO_DB.TPS);
        this.timeThresholdForMongo = parseInt(1000 / this.mongoDbTps);  //this.mongoDbTps = 3 or 4;
        this.lastMongoFetchTime = this.getTimeInMs() - this.timeThresholdForMongo;
        this.mongoDbFetchRecordsFailureRetryInterval = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FAILURE_RETRY_INTERVAL'], MONGO_DB.FAILURE_RETRY_INTERVAL);
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
    }

    async start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('REALTIME_SMS_PARSING :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('REALTIME_SMS_PARSING :: start', 'Kafka Configured successfully !!');
            }
        });
        this.L.log("start", "Mongo DB connectivity!!")

        // await promisify(this.mongoDbInstance.connect.bind(this.mongoDbInstance))();

        this.L.log("Mongo DB Connected!!")
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */
                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:AUTOMATIC_SYNC', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:CT_EVENTS', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:NON_PAYTM', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.planValidityKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_SYNC_DB.HOSTS
                });
                this.planValidityKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:PLAN_VALIDITY_SYNC_DB', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.billFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                this.billFetchKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : REALTIME_SMS_PARSING');

                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REALTIME_SMS_PARSING_POSTPAID.HOSTS'),
                    "groupId": "realtimeSmsParsing-consumer-postpaid",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.REALTIME_SMS_PARSING_POSTPAID.TOPIC'),
                    "id": `realtimeSmsParsing-consumer_${OS.hostname()}_${process.pid}`,
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };
                self.kafkarealtimeSmsParsingConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkarealtimeSmsParsingConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error) {
                        self.L.log("configureKafka", "consumer of topic : REALTIME_SMS_PARSING Configured");
                    }else{
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:CONSUMER', 'TOPIC:REALTIME_SMS_PARSING', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        self.RUreadsKafkaTime = new Date().getTime();

        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkarealtimeSmsParsingConsumer._pauseConsumer();
        } else {
            self.L.critical('REALTIME_SMS_PARSING:execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            return;
        }
        self.L.log('REALTIME_SMS_PARSING:execSteps:: ', `Processing ${records.length} SMS Parsing Bill Payment data !!`);
        // utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:REALTIME_SMS_PARSING', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            (err) => {
                self.kafkarealtimeSmsParsingConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('REALTIME_SMS_PARSING:execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:OFFSET_COMMIT', `TOPIC:${ _.get(lastMessage, 'topic',null)}`, `PARTITION:${_.get(lastMessage, 'partition',null)}`, 'SOURCE:MAIN_FLOW']);
                    }
                    else {
                        self.L.log('REALTIME_SMS_PARSING:execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now


                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time: ', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:REALTIME_SMS_PARSING", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        self.kafkarealtimeSmsParsingConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                if (err) {
                    self.L.error("Invalid records found! :", JSON.stringify(records));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                }
                return done()
            }
        );
    }
    processData(record, done) {
        let self = this;
        self.L.verbose("Record received: ", JSON.stringify(record));
        try {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:TRAFFIC', `TOPIC:${_.get(record,'topic',null)}`,`PARTITION:${_.get(record,'partition',null)}`, 'SOURCE:MAIN_FLOW']);

            record = JSON.parse(_.get(record, 'value', null));
            if (!record.data) {
                self.L.critical('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        }
        catch (error) {
            if (error) {
                self.L.critical('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            }
            return done();
        }


        if (!_.isArray(_.get(record, 'data', null))) {
            self.L.critical('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received`, JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            return done();
        }

        else{
            let record_data = _.get(record, 'data', null);

            if(record_data.length < 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                self.L.log(`realtimeSmsParsing :: Empty sms Data found`);
                return done();
            }

            let classifier_id = _.toString(_.get(record_data[0],'rtspClassId',null));
            let classifier_name = _.get(record_data[0], 'rtspClassName', null);
            let app_version = _.get(record_data[0],'appVersion',null);

            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:TRAFFIC_APP_VERSION_WISE', `CLASSIFIER_ID:${classifier_id}`, `CLASSIFIER_NAME:${classifier_name}`,`APP_VERSION:${app_version}`,'SOURCE:MAIN_FLOW']);
            // if(_.get(self.config, ['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING', 'POSTPAID_CLASSIFIERS','BILL_GEN_LIST'],[]).includes(classifier_id)){
                self.L.log(`realtimeSmsParsing :: executing postpaidSmsParsing flow as per data classifier_id : ${classifier_id} and classifier_name : ${classifier_name}`);
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        self.postpaidSmsParsing.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            // }
            // else {
            //     return ASYNC.map(
            //         record.data,
            //         (smsData, next) => {
            //             self.defaultStrategy(() => {
            //                 return next();
            //             }, smsData);
            //         },
            //         err => {
            //             utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            //             self.L.error("Invalid records found! :", JSON.stringify(record));
            //             return done();
            //         }
            //     )
            // }
        }
    }

    defaultStrategy(done, record) {
        this.L.log('REALTIME_SMS_PARSING:defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:NO_STRATEGY', 'SOURCE:MAIN_FLOW']);
        return done();
    }

    getTimeInMs(date) {
        return date ? new Date(date).getTime() : new Date().getTime();
    }

    suspendOperations() {

        var self = this,
            deferred = Q.defer();
        self.L.log(`realtimeSmsParsing::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.kafkarealtimeSmsParsingConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`realtimeSmsParsing::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`realtimeSmsParsing::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`realtimeSmsParsing::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`realtimeSmsParsing::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }
}


export default realtimeSmsParsing;
