import <PERSON><PERSON><PERSON><PERSON>OR from 'validator';
import MOMENT from 'moment';
import REQUEST from 'request';
import <PERSON>YNC from 'async'
import _ from 'lodash';
import utility from '../../lib';
import OS from 'os';
import prepaidSmsparsing from '../smsParsingBillPayment/prepaidSmsParsing';
import postpaidSmsParsing from '../smsParsingBillPayment/postpaidSmsParsing';
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills'
import DigitalCatalog from '../../lib/digitalReminderConfig'
import INFRAUTILS from 'infra-utils'
import digitalUtility from 'digital-in-util'
import { promisify } from 'util'
import Q from 'q'

class realtimeSmsParsing {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;
        this.prepaidSmsparsing = new prepaidSmsparsing(options, this);
        this.postpaidSmsParsing = new postpaidSmsParsing(options, this);
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.digitalCatalogLib = DigitalCatalog;
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        let { MONGO_DB, MYSQL, KAFKA } = this.config.VIL_SYNC_DB;
        this.mongoDbInstance = new INFRAUTILS.mongo(this.config.MONGO.HIDDEN_SLAVE);
        this.mongoCollection = 'users';
        this.retryCountForMongo = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'RETRY_COUNT'], MONGO_DB.RETRY_COUNT);
        this.commonLib = new utility.commonLib(options);
        this.mongoDbTps = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS'], MONGO_DB.TPS);
        this.timeThresholdForMongo = parseInt(1000 / this.mongoDbTps);  //this.mongoDbTps = 3 or 4;
        this.lastMongoFetchTime = this.getTimeInMs() - this.timeThresholdForMongo;
        this.mongoDbFetchRecordsFailureRetryInterval = _.get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FAILURE_RETRY_INTERVAL'], MONGO_DB.FAILURE_RETRY_INTERVAL);
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.timestamps = {};
    }

    async start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('REALTIME_SMS_PARSING :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('REALTIME_SMS_PARSING :: start', 'Kafka Configured successfully !!');
            }
        });
        this.L.log("start", "Mongo DB connectivity!!")

        // await promisify(this.mongoDbInstance.connect.bind(this.mongoDbInstance))();

        this.L.log("Mongo DB Connected!!")
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka Publisher to publish bill fetch to Automatic
                 */
                self.kafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": self.config.KAFKA.TOPICS.AUTOMATIC_SYNC.HOSTS
                });
                self.kafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:AUTOMATIC_SYNC', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:CT_EVENTS', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:NON_PAYTM', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.planValidityKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_SYNC_DB.HOSTS
                });
                this.planValidityKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:PLAN_VALIDITY_SYNC_DB', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.billFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                this.billFetchKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update plan validity service
                 */
                self.commonPvPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_NOTIFICATION_REALTIME.HOSTS
                    //_.get(this.config.PLAN_VALIDITY_NOTIFICATION, ["CONSUMER_SCHEDULER", 3], null)
                });
                this.commonPvPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : REALTIME_SMS_PARSING');

                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REALTIME_SMS_PARSING_PREPAID.HOSTS'),
                    "groupId": "realtimeSmsParsing-consumer-prepaid",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.REALTIME_SMS_PARSING_PREPAID.TOPIC'),
                    "id": `realtimeSmsParsing-consumer_${OS.hostname()}_${process.pid}`,
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };
                self.kafkarealtimeSmsParsingConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkarealtimeSmsParsingConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error) {
                        self.L.log("configureKafka", "consumer of topic : REALTIME_SMS_PARSING Configured");
                    }else{
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:CONSUMER', 'TOPIC:REALTIME_SMS_PARSING', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        self.RUreadsKafkaTime = new Date().getTime();

        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkarealtimeSmsParsingConsumer._pauseConsumer();
        } else {
            self.L.critical('REALTIME_SMS_PARSING:execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            return;
        }


        self.L.log('REALTIME_SMS_PARSING:execSteps:: ', `Processing ${records.length} SMS Parsing Bill Payment data !!`);
        // utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:REALTIME_SMS_PARSING', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            (err) => {
                self.kafkarealtimeSmsParsingConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('REALTIME_SMS_PARSING:execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:OFFSET_COMMIT', `TOPIC:${ _.get(lastMessage, 'topic',null)}`, `PARTITION:${_.get(lastMessage, 'partition',null)}`, 'SOURCE:MAIN_FLOW']);
                    }
                    else {
                        self.L.log('REALTIME_SMS_PARSING:execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now


                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time: ', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:REALTIME_SMS_PARSING", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        self.kafkarealtimeSmsParsingConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                if (err) {
                    self.L.error("Invalid records found! :", JSON.stringify(records));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                }
                return done()
            }
        );
    }

    validateDataPackRecord(record){
        var self = this;
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
            _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
        let dateFormat = 'YYYY-MM-DD HH:mm:ss';
        
        let expiry_date = _.get(record, 'dueDate', null);
        if(expiry_date){
            if(MOMENT(expiry_date,'YYYY-MM-DD').diff(MOMENT().format('YYYY-MM-DD'), 'days')!=0){
                return ['Validity expiry date is not today',null];
            }
        }
        let RN = _.get(record, 'rechargeNumber',_.get(record, 'smsReceiver','')); 

        let rech_num = (typeof RN === 'number') ? RN : (typeof RN === 'string' && VALIDATOR.isNumeric(RN)) ? VALIDATOR.toInt(RN) : null;
            if (rech_num) {
                rech_num = rech_num.toString();
                if (rech_num.length > 10) {
                    rech_num = rech_num.slice(-10);
                }
                // rech_num = Number(rech_num);
            }
        let processedRecord= {
            customerId : _.get(record, 'cId', null),
            rechargeNumber : rech_num,
            dataConsumed: self.parseNumber(_.get(record,'dataConsumed', null)),
            dueDate: expiry_date? MOMENT(expiry_date).format(dateFormat): null,
            operator: _.toLower(_.get(record, 'operator', null)),
            service: 'mobile',
            paytype: 'prepaid',
            msgId : _.get(record, 'msg_id', ''),
            rtspClassId: _.get(record, 'rtspClassId', null),
            dwhClassId: _.get(record, 'dwhClassId', null),
            rtspClassName: _.get(record, 'rtspClassName', null),
            senderId: _.get(record, 'smsSenderID', null),
        }
        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(processedRecord, 'isRuSmsParsing', true);
        }

        let mandatoryParams = ['customerId', 'rechargeNumber', 'operator', 'dataConsumed'];
        let invalidParams = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) invalidParams.push(key);
        });
        processedRecord.debugKey = `operator:${processedRecord.operator}_custId:${processedRecord.customerId}_rechargeNo:${processedRecord.rechargeNumber}`;

        if (invalidParams.length > 0) {
            return [`Mandatory Params ${invalidParams} is Missing / Invalid`, processedRecord];
        }
        return [null,processedRecord];
    }

    getTemplateId(type, record, notificationType, dueDate, dataConsumed){
        let self=this;
        let serviceBasedKey = `DATA_EXHAUST_${_.toUpper(_.get(record, 'service'))}_${dataConsumed}${notificationType}_${type}`;
        
        self.L.log("getTemplateId, serviceBasedKey: ", serviceBasedKey);
        let templateId =    _.get(this.operatorTemplateMappingConfig, [_.toLower(_.get(record, 'operator', null)), `DATA_EXHAUST_${dataConsumed}${notificationType}_${type}`],
                                _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKey],
                                    _.get(this.notificationConfig, ['templateid', `DATA_EXHAUST_${dataConsumed}${notificationType}_${type}` ],
                                        _.get(this.operatorTemplateMappingConfig, [_.toLower(_.get(record, 'operator', null)), `DATA_EXHAUST_${notificationType}_${type}`], 
                                            _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `DATA_EXHAUST_${_.toUpper(_.get(record, 'service'))}_${notificationType}_${type}`], 
                                                _.get(this.notificationConfig, ['templateid', `DATA_EXHAUST_${notificationType}_${type}` ], null)
                                                )
                                            )
                                        )
                                    )
                                );
        self.L.log("getTemplateId::", `operator:${_.get(record, 'operator', null)}_notificationType:${notificationType}_dueDate:${dueDate}_dataConsumed:${dataConsumed}_type:${type}_templateId:${templateId}`);
        return templateId;
    }

    fetchDataTemplates(cb, record){
        let self = this,
        notificationType = 'DUEDATE',
        dataConsumed= _.get(record, 'dataConsumed',null)
        if(dataConsumed==null){
            return cb('Missing dataConsumed field');
        }
        dataConsumed= dataConsumed+"_";
        _.set(record, 'service', 'mobile');

        let templates = {};

        for (let key in this.notificationConfig.type) {
           
            if (this.notificationConfig.type[key]) {
                templates[key] = this.getTemplateId(key, record, notificationType, _.get(record,'dueDate',null),dataConsumed);
            }
        }
        return cb(null,templates);
    }

    formatPayloadToIngest(error,record,extraDetails){
        let self = this;
        let ruOnboarded = false;
        let source_kafka_topic = _.get(self.config.KAFKA, 'SERVICES.REALTIME_SMS_PARSING_PREPAID.TOPIC');
        try{
            return  {
                "recharge_number" : _.get(record, 'rechargeNumber', null),
                "customer_id" :  (typeof _.get(record,'cId',null) === 'number') ? _.get(record,'cId',null) : (typeof _.get(record,'cId',null) === 'string' && VALIDATOR.isNumeric(_.get(record,'cId',null))) ? VALIDATOR.toInt(_.get(record,'cId',null)) : null,
                "operator" : _.get(record, 'operator', _.get(record, 'smsOperator', null)),
                "product_id" : _.get(extraDetails, 'productId', null),
                "service" : "mobile",
                "paytype" : "prepaid",
                "classifier_id" : _.get(record, 'rtspClassId', null),
                "classifier_name" : _.get(record, 'rtspClassName', null),
                "template_body" : _.get(record, 'templateBody', null),
                "status" : (error)? 0:1,
                "error_message" : error,
                "source" : "REALTIME_SMS_PARSING_TELECOM",
                "source_kafka_topic": `${source_kafka_topic}`,
                "sender_id" : _.get(record, 'smsSenderID', null),
                "payload" :  record,
                "amount" : utility.getFilteredAmount(_.get(record, 'telecom_details.due_amount', _.get(record, 'amount', '0'))),
                "dataConsumed" : utility.getFilteredAmount(_.get(record, 'dataConsumed', null)),
                "ruOnboarded" : ruOnboarded,
                "due_date" : _.get(record, 'dueDate', null)? MOMENT(_.get(record,'dueDate', null)).format('YYYY-MM-DD HH:mm:ss'):null,
            }
        }
        catch(err){
            self.L.error("formatPayloadToIngest:: ","Error while creating payload: ", err);
            return null;
        }
    }

    publishInCTForCTCategory(done,dataRow){
        let self = this;
        const productId = _.get(dataRow, 'productId', 0);
        let classId = _.get(dataRow, 'rtspClassId', null);
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS_BASED_ON_CLASSID', classId], 'defaultEvent');

            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customerId, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {        
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                                'STATUS:ERROR', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                `CLASSID:${_.get(dataRow, 'rtspClassId', null)}`, 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${_.get(dataRow,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                                `APP_VERSION:${_.get(dataRow,'appVersion', null)}`
                            ]);
                            self.L.critical('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                                'STATUS:PUBLISHED', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                `CLASSID:${_.get(dataRow, 'rtspClassId', null)}`, 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${_.get(dataRow,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                                `APP_VERSION:${_.get(dataRow,'appVersion', null)}`
                            ]);
                            self.L.log('postpaidSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.log('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return done(err)
            })
        }


    publishInKakfaForDataPack(done, record){
            let self = this;
        ASYNC.parallel([
            function (cb) {
                self.publishInBillFetchKafkaForDataPack(function(err){
                    cb(err)
                },record)
            },
            function (cb) {
                self.publishInCTForDataPack(function(err){
                    cb(err)
                },record)
            },
        ], function(error) {
            if (error) {
                self.L.error('Error occurred during parallel tasks:', error);
            }
            done(error);
        });
    }

    publishInCTForDataPack(done,dataRow){
        let self = this;
        const productId = _.get(dataRow, 'productId', 0);
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', `DATA_EXHAUSTED_${_.get(dataRow, 'dataConsumed', null)}`], 'smsparsedDataExpiring');
            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customerId, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {                    
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                                'STATUS:ERROR', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS",
                                `CLASSID:${_.get(dataRow, 'rtspClassId', null)}`,  
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${_.get(dataRow,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                                `APP_VERSION:${_.get(dataRow,'appVersion', null)}`
                            ]);
                            self.L.critical('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                                'STATUS:PUBLISHED', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                `CLASSID:${_.get(dataRow, 'rtspClassId', null)}`,  
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${_.get(dataRow,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                                `APP_VERSION:${_.get(dataRow,'appVersion', null)}`
                            ]);
                            self.L.log('postpaidSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.log('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return done(err)
            })
        }

    publishInBillFetchKafkaForDataPack(done,processedRecord){
        let self = this;
            let payload = {
                source: "dataExhaust",
                notificationType: "DUEDATE",
                data: {
                    customerId: processedRecord.customerId,
                    rechargeNumber: processedRecord.rechargeNumber,
                    productId: processedRecord.productId,
                    operator: processedRecord.operator,
                    amount: processedRecord.amount,
                    dataConsumed: processedRecord.dataConsumed,
                    bill_fetch_date: MOMENT(),
                    paytype: "prepaid",
                    service: processedRecord.service,
                    circle: processedRecord.circle,
                    customer_mobile:  null,
                    customer_email: null,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    userData: null,
                    billDate: null,
                    notification_status: 1,
                    dueDate: processedRecord.dueDate,
                    customerOtherInfo: JSON.stringify(processedRecord),
                    planBucket: processedRecord.planBucket,
                    templates: processedRecord.templates
                }
            }
            _.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());
            utility.sendNotificationMetricsFromSource(payload)
            self.billFetchKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.REMINDER_BILLFETCH_PIPELINE_REALTIME.REMINDER_BILL_FETCH_REALTIME', ''),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: SMS_PARSING_PREPAID", 
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:REMINDER_BILL_FETCH', 
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: REMINDER_BILL_FETCH', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        'TOPIC:REMINDER_BILL_FETCH', 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: REMINDER_BILL_FETCH', 'Message published successfully in Kafka', ' on topic REMINDER_BILL_FETCH', JSON.stringify(payload));
                }
                return done(null);
            }, [200, 800]);
    }

    parseAmount(amountStr) {  
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && VALIDATOR.isNumeric(amountStr)) return VALIDATOR.toFloat(amountStr);
        // case of "Rs.x.y" i.e. "Rs.101.54"
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        if(parsedAmount) return parsedAmount;
        //case of "x.y" i.e. "101.54"
        let foundMatch2 = amountStr.match(new RegExp(/([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount2 = (foundMatch2 && foundMatch2[1]) ? VALIDATOR.toFloat(foundMatch2[1]) : null;
        return parsedAmount2;
    }

    validateRecordCtEventsStrategy(record){
        var self = this;
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
            _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
        let dateFormat = 'YYYY-MM-DD HH:mm:ss';
        let RN = _.get(record, 'rechargeNumber',_.get(record, 'smsReceiver','')); 

        let rech_num = (typeof RN === 'number') ? RN : (typeof RN === 'string' && VALIDATOR.isNumeric(RN)) ? VALIDATOR.toInt(RN) : null;
            if (rech_num) {
                rech_num = rech_num.toString();
                if (rech_num.length > 10) {
                    rech_num = rech_num.slice(-10);
                }
                // rech_num = Number(rech_num);
            }
        let processedRecord= {
            customerId : _.get(record, 'cId', null),
            rechargeNumber : rech_num,
            operator: _.toLower(_.get(record, 'operator', null)),
            service: 'mobile',
            paytype: 'prepaid',
            msgId : _.get(record, 'msg_id', ''),
            rtspClassId: _.get(record, 'rtspClassId', null),
            rtspClassName: _.get(record, 'rtspClassName', null),
            dwhClassId: _.get(record, 'dwhClassId', null),
            senderId: _.get(record, 'smsSenderID', null),
            dueDate : _.get(record, 'dueDate', null)? MOMENT(_.get(record,'dueDate', null)).format('YYYY-MM-DD HH:mm:ss'):null,
            amount : self.parseAmount(_.get(record, 'amount',null)),
        }
        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(processedRecord, 'isRuSmsParsing', true);
        }

        let mandatoryParams = ['customerId', 'rechargeNumber', 'operator'];
        let invalidParams = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) invalidParams.push(key);
        });
        processedRecord.debugKey = `operator:${processedRecord.operator}_custId:${processedRecord.customerId}_rechargeNo:${processedRecord.rechargeNumber}`;

        if (invalidParams.length > 0) {
            return [`Mandatory Params ${invalidParams} is Missing / Invalid`, processedRecord];
        }
        return [null,processedRecord];
    }

    executeCtEventsStrategy(cb,record){
        var self = this;
        self.RUreadsKafkaTime = new Date().getTime();
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
            _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
        ASYNC.waterfall([
            next => {
                if(!record){
                    return next("record not found",null);
                }
                let [error,processedRecord] = self.validateRecordCtEventsStrategy(record);
                if (error) {
                    self.L.log(`SMS_PARSING_PREPAID:PS_PARSING::VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:VALIDATION_FAILURE', 
                        `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                        'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                        'TYPE:' + error,
                        `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    next(error,processedRecord);
                }else next(null,processedRecord);
            },
            (processedRecord,next) => {
                self.prepaidSmsparsing.get_circle_of_record(function (error, circle) {
                    if (error || !circle) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                            'STATUS:ERROR', 
                            `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                            'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                            'TYPE:NO_CIRCLE',
                            `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        next(error || "Circle Not Found",processedRecord);
                    }else{
                        processedRecord.circle = circle;
                    next(null,processedRecord);
                    }
                }, processedRecord.operator, processedRecord.rechargeNumber);
            },
            (processedRecord,next) => {
                self.prepaidSmsparsing.get_productId_of_record(function (error, productId) {
                    if (error || !productId){
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                            'STATUS:ERROR', 
                            `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                            'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                            'TYPE:NO_PID',
                            `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        next(error || "productId not found",processedRecord);
                    }else{
                        processedRecord.productId = productId;
                        //console.log("get_productId_of_record ", self.operator_circle_productId_map, productId, processedRecord.circle);
                        next(null, processedRecord);
                    }
                }, processedRecord.circle, processedRecord.operator);
            },
            (processedRecord,next) => {
                self.publishInCTForCTCategory(function(err){
                    if(err){
                        next(err,processedRecord)
                    }else next(null,processedRecord);
                },processedRecord)
            },
            (processedRecord, next) => {
                let source = _.get(processedRecord, 'isRuSmsParsing', false)? "SMS_MOBILE_PREPAID_REALTIME":"SMS_MOBILE_PREPAID";
                self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                    next(null,processedRecord);
                },source,self.timestamps, processedRecord.operator,processedRecord);
            }
        ], async function(error,processedRecord) {
        //     if(_.get(record, 'isRuSmsParsing', false)){
        //         let payloadToIngest = self.formatPayloadToIngest(error,record,processedRecord);
        //         await self.smsParsingSyncCCBillLib.ingestIncomingPayloads(payloadToIngest)
        //     .catch((err)=>{
        //         if(err){
        //             self.L.error(`SMS_PARSING_PREPAID:PS_PARSING::ingestIncomingPayloads`, `coudlnt save record with error ${err}`);
        //         }
        //     })
        // }
            if (error) {
                self.L.log('SMS_PARSING_PREPAID:PS_PARSING:processRecords', `Exception occured Error Msg:: ${error}  record::`, record);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_PREPAID', 'STATUS:VALIDATION_FAILURE', 'TYPE:' + error]);
            } else {
                self.L.log(`SMS_PARSING_PREPAID:PS_PARSING:processRecords`, `Record processed `, record);
            }
            return cb();
        });




        /*
        var self=this;
        let [error,processedRecord]= self.validateRecordCtEventsStrategy(record);
        let classId= _.get(record, 'rtspClassId', null);
        let ctEventName = _.get(self.config, ['DYNAMIC_CONFIG','SMS_PARSING_PREPAID','CT_EVENT_NAMES',classId],null);
        if(ctEventName==null){
            self.L.error("executeCtEventsStrategy:: Couldnt fetch ct event name from class id for record ", JSON.stringify(record))
            return cb(null);
        }else{

        }
        */
    }
    executeDataPackStrategy(cb,record){
        var self = this;
        self.RUreadsKafkaTime = new Date().getTime();
    ASYNC.waterfall([
        next => {
            if(!record){
                return next("telecom details not found",null);
            }
            let [error,processedRecord] = self.validateDataPackRecord(record);
            if (error) {
                self.L.log(`SMS_PARSING_PREPAID:PS_PARSING::VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                    'STATUS:VALIDATION_FAILURE',
                    `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                    'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                    'TYPE:' + error,
                    `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                ]);
                next(error,processedRecord);
            }else next(null,processedRecord);
        },
        (processedRecord,next) => {
            self.prepaidSmsparsing.get_circle_of_record(function (error, circle) {
                if (error || !circle) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:ERROR', 
                        `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                        'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                        'TYPE:NO_CIRCLE',
                        `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    next(error || "Circle Not Found",processedRecord);
                }else{
                    processedRecord.circle = circle;
                next(null,processedRecord);
                }
            }, processedRecord.operator, processedRecord.rechargeNumber);
        },
        (processedRecord,next) => {
            self.prepaidSmsparsing.get_productId_of_record(function (error, productId) {
                if (error || !productId){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:ERROR', 
                        `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                        'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                        'TYPE:NO_PID',
                        `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    next(error || "productId not found",processedRecord);
                }else{
                    processedRecord.productId = productId;
                next(null, processedRecord);
                }
            }, processedRecord.circle, processedRecord.operator);
        },
        (processedRecord,next) => {
            self.fetchDataTemplates(function(err,result){
                if(err){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:NOTIFICATION_TEMPLATE_NOT_FOUND', 
                        `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                        'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                        `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.error("fetchDataTemplates:: error in fetching templates ", err);
                }else{
                    _.set(processedRecord,'templates', result);
                } 
                next(null,processedRecord);
            },processedRecord)
        },
        (processedRecord,next) => {
            self.publishInKakfaForDataPack(function(err){
                if(err){
                    next(err,processedRecord)
                }else next(null,processedRecord);
            },processedRecord)
        },
        (processedRecord, next) => {
            let source = _.get(processedRecord, 'isRuSmsParsing', false)? "SMS_MOBILE_PREPAID_REALTIME":"SMS_MOBILE_PREPAID";
                self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                    next(null,processedRecord);
                },source,self.timestamps, processedRecord.operator,processedRecord);
        }
    ], async function(error,processedRecord) {
    //     if(_.get(record, 'isRuSmsParsing', false)){
    //         let payloadToIngest = self.formatPayloadToIngest(error,record,processedRecord);
    //         await self.smsParsingSyncCCBillLib.ingestIncomingPayloads(payloadToIngest)
    //     .catch((err)=>{
    //         if(err){
    //             self.L.error(`SMS_PARSING_PREPAID:PS_PARSING::ingestIncomingPayloads`, `coudlnt save record with error ${err}`);
    //         }
    //     })
    // }
        if (error) {
            self.L.log('SMS_PARSING_PREPAID:PS_PARSING:processRecords', `Exception occured Error Msg:: ${error}  record::`, record);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_PREPAID', 'STATUS:VALIDATION_FAILURE', 'TYPE:' + error]);
        } else {
            self.L.log(`SMS_PARSING_PREPAID:PS_PARSING:processRecords`, `Record processed `, record);
        }
        return cb();
    });
    }

    parseNumber(amountStr) {  
        let self =this;
        try{
            if (!amountStr) return null;
            if (typeof amountStr === 'number') return Math.floor(amountStr);
            if (typeof amountStr === 'string' && VALIDATOR.isNumeric(amountStr)) return VALIDATOR.toFloat(amountStr);
            //case of "x.y" i.e. "101.54"
            let foundMatch = amountStr.match(new RegExp(/([-+]?\d*(?:\.\d*)?)/));
            let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
            return  Math.floor(parsedAmount);
        }
        catch(err){
            self.L.error("parseNumber:: ", "Error in parsing number: ", err)
            return null;
        }
    }

    segregateValidityAndNoValidty(done, record,ref){
        let self=this;
        let t_time = MOMENT();
        let validityDate=null;
        if(!_.get(record, 'dueDate',null)){
            self.executeCtEventsStrategy(function(err){
                return done();
            },record)
        }else{
            if (MOMENT(_.get(record, 'dueDate',null), 'YYYY-MM-DD', true).isValid()) {   //  2022-08-31
                validityDate = MOMENT(_.get(record, 'dueDate',null), 'YYYY-MM-DD');
            }
            else if (MOMENT(_.get(record, 'dueDate',null), 'DD-MM-YYYY', true).isValid()) {      // 20-02-2022   2022-08-31
                validityDate = MOMENT(_.get(record, 'dueDate',null), 'DD-MM-YYYY');
            }
            else if (MOMENT(_.get(record, 'dueDate',null), 'DD-MMM-YYYY', true).isValid()) {    //20-feb-2022
                validityDate = MOMENT(_.get(record, 'dueDate',null), 'DD-MMM-YYYY');
            }
            if(!validityDate || t_time.unix() > validityDate.unix()){
                self.executeCtEventsStrategy(function(err){
                    return done();
                },record)
            }else{
                self.prepaidSmsparsing.executeStrategy(function(err){
                    return done();
                },record,ref)
            }
        }
    }
    processData(record, done) {
        let self = this;
        self.L.info("Record received: ", record);
        try {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:TRAFFIC', `TOPIC:${_.get(record,'topic',null)}`,`PARTITION:${_.get(record,'partition',null)}`, 'SOURCE:MAIN_FLOW']);
            record = JSON.parse(_.get(record, 'value', null));
            if (!record.data) {
                self.L.critical('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        }
        catch (error) {
            if (error) {
                self.L.critical('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            }
            return done();
        }


        if (!_.isArray(_.get(record, 'data', null))) {
            self.L.critical('REALTIME_SMS_PARSING:processData', `Invalid Kafka record received`, JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
            return done();
        }

        else{
            let record_data = _.get(record, 'data', null);
            if(record_data.length < 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                self.L.log(`realtimeSmsParsing :: Empty sms Data found`);
                return done();
            }
            let rtspClassId = _.toString(_.get(record_data[0],'rtspClassId',null));
            let rtspClassName = _.get(record_data[0], 'rtspClassName', null);
            let app_version = _.get(record_data[0],'appVersion',null);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:TRAFFIC_APP_VERSION_WISE', `CLASSIFIER_ID:${rtspClassId}`, `CLASSIFIER_NAME:${rtspClassName}`,`APP_VERSION:${app_version}`,'SOURCE:MAIN_FLOW']);
            if(_.get(self.config, ['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING', 'PREPAID_CLASSIFIERS','DATA_PACK_IDS'],[]).includes(rtspClassId)){
                self.L.log(`realtimeSmsParsing :: executing prepaid data packs flow as per data classifier_id : ${rtspClassId}`);
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        self.executeDataPackStrategy(() => {
                            return next();
                        }, smsData);
                    },
                    err => {
                        if(err){
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else if(_.get(self.config, ['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING', 'PREPAID_CLASSIFIERS','VALIDITY_EXPIRY_IDS'],[]).includes(rtspClassId)){
                self.L.log(`realtimeSmsParsing :: executing prepaidSmsParsing flow as per data classifier_id : ${rtspClassId}`);
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        self.segregateValidityAndNoValidty(() => {
                            return next();
                        }, smsData, self);
                    },
                    err => {
                        if(err){
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else if(_.get(self.config, ['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING', 'PREPAID_CLASSIFIERS','RECHARGE_DONE_IDS'],[]).includes(rtspClassId)){
                self.L.log(`realtimeSmsParsing :: executing prepaidSmsParsing flow as per data classifier_id : ${rtspClassId}`);
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        self.prepaidSmsparsing.executeStrategy(() => {
                            return next();
                        }, smsData, self);
                    },
                    err => {
                        if(err){
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else if(_.get(self.config, ['DYNAMIC_CONFIG', 'REALTIME_SMS_PARSING', 'PREPAID_CLASSIFIERS','CT_EVENT_IDS'],[]).includes(rtspClassId)){
                self.L.log(`realtimeSmsParsing :: executing postpaidSmsParsing flow as per data classifier_id : ${rtspClassId}`);
                    return ASYNC.map(
                        record.data,
                        (smsData, next) => {
                            self.executeCtEventsStrategy(() => {
                                return next();
                            }, smsData,self);
                        },
                        err => {
                            if(err){
                                self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                            }
                            return done();
                        }
                    )
            }
            else {
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        self.defaultStrategy(() => {
                            return next();
                        }, smsData);
                    },
                    err => {
                        if(err){
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:ERROR', 'TYPE:INVALID_JSON', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
        }
    }

    defaultStrategy(done, record) {
        this.L.log('REALTIME_SMS_PARSING:defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:REALTIME_SMS_PARSING", 'STATUS:NO_STRATEGY', 'SOURCE:MAIN_FLOW']);
        return done();
    }


    /**
    * run mongoDb query on recents after specified time, 
    *  retry if query faied after specified time
    */
    async mongoThrottleWapper(queryObj, retryCount = 0) {
        try {
            let sleep = (this.getTimeInMs() - this.lastMongoFetchTime);

            if (sleep < this.timeThresholdForMongo) {
                await this.commonLib.calledAfterTime(this.timeThresholdForMongo - sleep);
            }

            let data = await this.runMongoQuery(queryObj);

            this.L.verbose("mongoThrottleWapper: data from mongo", data);

            this.lastMongoFetchTime = this.getTimeInMs();

            return data;
        } catch (error) {
            this.L.error(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
            this.lastMongoFetchTime = this.getTimeInMs();

            /**
             * you can apply checks on error message, what ever here we are fetching data, so there should be no issue on fetching
             * if there is possible some other issue 
             */

            if (retryCount < this.retryCountForMongo) {
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            } else {
                this.L.critical(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
                await this.commonLib.calledAfterTime(this.mongoDbFetchRecordsFailureRetryInterval);
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            }
        }
    }

    runMongoQuery(queryObj) {
        return new Promise((resolve, reject) => {

            this.L.verbose("runMongoQuery: ", queryObj);

            this.mongoDbInstance.fetchDataFromCollection((err, results) => {
                if (err) {
                    let stats = { type: "MONGO_QUERY_FAILED", STATE: "ERROR", count: 1 }
                    this.publishStats(stats);
                    reject(err);
                } else {
                    resolve(results);
                }
            }, this.mongoCollection, queryObj);
        });
    }

    getTimeInMs(date) {
        return date ? new Date(date).getTime() : new Date().getTime();
    }

    suspendOperations() {

        var self = this,
            deferred = Q.defer();
        self.L.log(`realtimeSmsParsing::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.kafkarealtimeSmsParsingConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`realtimeSmsParsing::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`realtimeSmsParsing::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`realtimeSmsParsing::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`realtimeSmsParsing::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }
}


export default realtimeSmsParsing;
