import MOMENT from 'moment'
import _ from 'lodash'
import <PERSON><PERSON><PERSON> from 'async'
import REQUEST from 'request'
import BILLS from '../models/bills'
import PLAN_VALIDITY from '../models/planValidity'
import BILL_SUBSCRIBER from './billSubscriber'
import utility from '../lib'
import throttledRequest from 'throttled-request'
import events from 'events'
import SCHEDULE from 'node-schedule'
import digitalUtility from 'digital-in-util'
import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge';

class AirtelKafkaPublisher {

    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.bills = new BILLS(options);
        this.dbBatchSize = _.get(options.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'BATCH_SIZE', 'value'], 500);
        this.servicePeriod = 15; //time in seconds
        this.operator = 'airtel'
        this.service = 'mobile'
        this.recordsPublished = 0
        this.commonLib = new utility.commonLib(options);
        this.dry_run = _.get(options, 'dry_run', 0); // If dry_run = 1, dont alter/push in DB/kafka just log it
        this.publishedInSession = {};
        this.MAX_RETRY_COUNT = 3;
        this.MAX_PRIORITY = 6
        this.MAX_TABLES = 9
        this.infraUtils = options.INFRAUTILS;
        this.MAX_PUBLISHED_COUNT = _.get(options.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'PUBLISHER_DAILY_LIMIT', 'value'], 25e6);;
        this.mode = 'asc' //'asc' or 'desc'
        /** Starting ID , -1 means last ID in the table */
    this.redis                          = new this.infraUtils.cache("REDIS", this.config.REDIS);
        this.state = { 
            id : (this.mode == "desc"? -1 : 0),
            priority : 0,
            tableNo : 0,
            totalPublished:0
        }

        this.gracefulExit = ()=>{}

        

        /**
         * Status Meanings
         * 4 - Users whose validity is expired and due date is known , we have to send them notification
         * 
         * 14 - Users who have undergone a true->false transition and we know that they have recharged somewhere
         * 
         * 1 - Users for which we know the previous day(D-2) status and they have not undergone a true->false or false->true transition yet
         * 
         * 
         * 0 - New users or users whose previous Day(D-2) status is not known
         * 
         */
        this.priority_map_query ={
            0:`SELECT * FROM <%= tableName %> WHERE status=14 and  (next_bill_fetch_date >= CURDATE()) and (next_bill_fetch_date < CURDATE()+INTERVAL 1 DAY) and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`,
            1: `SELECT * FROM <%= tableName %> WHERE status=14 and next_bill_fetch_date > ? and next_bill_fetch_date < ? and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`,
            2:`SELECT * FROM <%= tableName %> WHERE status=4 and  (next_bill_fetch_date >= CURDATE()) and (next_bill_fetch_date < CURDATE()+INTERVAL 1 DAY) and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`,
            3: `SELECT * FROM <%= tableName %> WHERE status=4 and next_bill_fetch_date > ? and next_bill_fetch_date < ? and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`,
            4:`SELECT * FROM <%= tableName %> WHERE status=1 and  (next_bill_fetch_date >= CURDATE()) and (next_bill_fetch_date < CURDATE()+INTERVAL 1 DAY) and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`,
            5: `SELECT * FROM <%= tableName %> WHERE status=1 and next_bill_fetch_date > ? and next_bill_fetch_date < ? and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`,
           
            6: `SELECT * FROM <%= tableName %> WHERE status=0 and next_bill_fetch_date > ? and next_bill_fetch_date < ? and id ${this.mode == "desc" ? "<" : ">" }  <%= idCondition %> order by id ${this.mode} LIMIT ?`   
        }
        /** true means include nbfd in params , false means dont */
        this.priority_type_map={
            0:false,
            1:true,
            2:false,
            3:true,
            4:false,
            5:true,
            6:true
        }
        this.resetState();
        this.stopService = false;

        this.L.info('Publisher::', this.tableName, this.operator, 'initialising Publisher...');
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

    }


    initializeVariable(){
        let self = this;
        this.MAX_PUBLISHED_COUNT = _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'PUBLISHER_DAILY_LIMIT', 'value'], 25e6);
        this.dbBatchSize = _.get(self.config, ['DYNAMIC_CONFIG', 'AIRTEL_PREPAID_CONFIG', 'BATCH_SIZE', 'value'], 500);

    }


    getStartingID(){
        return (this.mode == "desc"? -1 : 0)
    }


    /**
     * Kafka publisher
     */
    configureKafkaPublisher(done) {
        let self = this;

        self.kafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.AIRTEL_BILL_FETCH.HOSTS')
        });
        self.kafkaPublisher.initProducer('high', function (error) {
            if (!error)
                self.L.log("airtelKafkaPublisher::", "publisher Configured");
            else{
                self.L.error("airtelKafkaPublisher::","Error while configuring Publusher",error)
            }
            return done(error);
        });
    }
    /*
      Function which will start the publisher for a table
    */
    start() {
        let self = this;
       
        ASYNC.waterfall([
            next=>{
                self.L.info('airtelKafkaPublisher::', 'connecting to redis');
                
                this.redis.connect((err, data) => {

                    if(err)self.L.error("airtelKafkaPublisher::",err)
                    next(err);
                });
            },
            next=>{
                self.L.info('airtelKafkaPublisher::', 'connecting to Kafka');
                this.configureKafkaPublisher(next)
            },
            next=>{
                self.L.info('airtelKafkaPublisher::', 'getting Saved state from redis');
                self.getSavedState(next)
            },
            (next)=>{
                self.publishedInSession = {} //intialise a fresh session object
                self.L.log('start', 'Starting reset priority Scheduler...');
                self.startPriorityScheduler();
                self.L.info('airtelKafkaPublisher::', 'starting the service loop');
                try{
                    self.gracefulExit =  next;
                    self._execSteps();
                    
                }catch(err){
                    self.L.error("airtelKafkaPublisher::",err)

                }
                
            }
        ],function(error , data){
            if(error)self.L.critical("airtelKafkaPublisher:: critical error while initializing",error)
        })
       
        
    }


    executeNextState(){
        let self = this;
        self.L.log("executeNextState  , current State::",JSON.stringify(self.state));


        if(self.stopService){
            self.L.info("Stopping service")
            return self.gracefulExit()
        }
        if(self.state.isResetted == true){
            self.resetState()
            self.state.isResetted = false;
            return self._execSteps()
        }

        if( self.state.totalPublished >= self.MAX_PUBLISHED_COUNT ){
            self.L.info("executeNextState:","Limit exceeded for today, will resume again tomorrow")
            return setTimeout(()=>self.executeNextState(),self.servicePeriod*1000)
        }

        /** Today's job is complete , call this function recursively  so that it waits for next reset event  */
        if( (self.state.priority == this.MAX_PRIORITY && self.state.tableNo == this.MAX_TABLES)){
            self.L.info("executeNextState:","all priorities published for today , will resume again tomorrow")
            return setTimeout(()=>self.executeNextState(),self.servicePeriod*1000)
        }
        /** WE reaching here means all records from a given table is exhaused so we increment the table Number*/
        if(self.state.tableNo < this.MAX_TABLES){
            self.state.id  = self.getStartingID();
            self.state.tableNo++;
            return self._execSteps()
        }
        else{
            /** If table number is 9 or greater than 9 this means all tables are exhausted and we can move to next priority */
            setTimeout(function () {
                self.L.info("executeNextState::","---------------Moving on to next priority---------------------------------")
                
                self.state.priority++;
                self.state.tableNo = 0
                self.state.id = self.getStartingID()
                return self._execSteps()
            }, self.servicePeriod * 1000);  //converting period in milli seconds
        }

        
    }

    _execSteps() {
        let self = this;
        self.L.info('_execSteps:: current state::', JSON.stringify(self.state), 'performing steps for publishing...')

        self.recordsPublished = 0;

        let totalFreshRecordsCount = 0;
        
        ASYNC.waterfall([
            // next => {
            //     if (self.token) { // To avoid token generation continuously
            //         return next();
            //     } else {
            //         return self.fetchToken(next);
            //     }
            // },
            next => {
                
                self.fetchRecords(function __keepFetchingUntilDone(err, recordsCountInLastBatch) {
                    
                    if (err) {
                        self.L.error('_execSteps', 'Error processing fresh records')
                        //No matter what happened, don't stop at error and proceed further
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER', 'STATUS:ERROR']);
                        return next();
                    }
                    //If batch size is equal to records fetched that means there can be more records.
                    else if (recordsCountInLastBatch >= self.dbBatchSize) {
                        self.L.log('_execSteps._processFreshRecords::', " state ::",  JSON.stringify(self.state), 'recordsCountInLastBatch', recordsCountInLastBatch);
                        if (recordsCountInLastBatch > 0) {
                            utility._sendMetricsToDD(recordsCountInLastBatch, ['REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER', 'STATUS:PUBLISHED',]);
                        }
                        totalFreshRecordsCount += recordsCountInLastBatch;
                        if(self.state.isResetted == true || self.state.totalPublished >= self.MAX_PUBLISHED_COUNT )return next()
                        self.fetchRecords(__keepFetchingUntilDone)
                    }
                    else { //No records left, so proceeding to next step
                        totalFreshRecordsCount += recordsCountInLastBatch
                        if (recordsCountInLastBatch > 0) {
                            utility._sendMetricsToDD(recordsCountInLastBatch, ['REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER', 'STATUS:PUBLISHED']);
                        }
                        self.L.log('_execSteps._processFreshRecords::', " state ::",  JSON.stringify(self.state), 'totalRecordsInStep:', totalFreshRecordsCount);
                        return next();
                    }
                });
            },
        ], function (error) {
            if (error) {
                self.L.error('Error in _execSteps', error);
            }
            return self.executeNextState();
        });
    }

    /*
       Function to process records
    */
    fetchRecords(done) {
        let self = this,
            startTime = new Date().getTime();
        self.publishedInBatch = {}
        if(self.state.isResetted == true || self.stopService ==  true) {
            self.L.log("State has been reset so move on to next priority");
            return done(null, 0);
        }
        self.L.log("fetchRecords",`Going to fetch new batch for fresh records...`);

        ASYNC.waterfall([
            //fetch records from table
            next => {
            
                let tableName = self.getTableName()
                let idCondition=self.state.id;
                if(self.state.id  == -1){
                    idCondition = idCondition =  `(SELECT MAX(id) from ${tableName})`;
                }
                let query = _.template(self.priority_map_query[self.getPriority()])({
                    idCondition ,
                    tableName,
                })
                let params = [
                    self.dbBatchSize,
                ]
                if(self.priority_type_map[self.getPriority()] == true){
                    params = [
                        
                         MOMENT().subtract(30, 'days').startOf('day').format('YYYY-MM-DD'),
                         (self.state.priority == 6 ) ?  MOMENT().add(1, 'days').format('YYYY-MM-DD') :  MOMENT().format('YYYY-MM-DD'),
                         self.dbBatchSize,
                    ]
                }
                return self.bills.v2fetchAirtelRecords(tableName, params, query, next);
            },
            (records , next)=>{
                if(records.length == 0)return next(null , 0)
                let curTime = MOMENT()
                let tableName = self.getTableName()
                

                let mappedRecords = _.map(records , function(ele , index ){
                    return JSON.stringify({
                        ...ele,
                        tableName,
                        publishedTime : curTime
                    })
                })
                self.kafkaPublisher.publishData([{
                    topic: _.get(self.config.KAFKA, 'SERVICES.AIRTEL_BILL_FETCH.TOPIC', ''),
                    messages: mappedRecords
                }], error => {
                    if(error){
                        utility._sendMetricsToDD(1, [`REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER`, 'STATUS:ERROR','TABLE:'+tableName]);
                        self.L.error("airtelKafkaPublisher::", error, JSON.stringify(mappedRecords))
                        /** If there was error in publishing we have to retry so we dont update ID*/
                        return next(null , records.length)
                    } else { 
                        utility._sendMetricsToDD(mappedRecords.length, [`REQUEST_TYPE:AIRTEL_PREPAID_PUBLISHER`, 'STATUS:PUBLISHED','TABLE:'+tableName]);
                        self.state.totalPublished=self.state.totalPublished+records.length
                        self.L.log("airtelKafkaPublisher:","records published:" ,mappedRecords.length)
                    }
                    self.state.id = self.getNextId(records)
                    
                    next(null , records.length)
            }, [200, 800])
                
            },
            (count,next)=>{
                // if(count == 0 )return next(null , 0)
                /** Update State on REDIS */
                this.saveState((err)=>{
                    next(err , count)
                })
            }
        ], (err, count) => {
            if (err) {
                return done(err)
            }
            done(null, count)
        });
    }

    getTableName(){
        return "bills_airtelprepaid"+this.state.tableNo
    }

    saveState(cb){
        let self = this
        /** This function will save the state */
        this.L.info("airtelKafkaPublisher::","saving state on redis",JSON.stringify(this.state))
        this.redis.updateData((error, result) => {
            if(error){
                this.L.info("airtelKafkaPublisher::","error while saving state on redis",error)
            }
            cb()
        }, {
            key     : this.getRedisKey(),
            value   : JSON.stringify(self.state),
            ttl     : 24*60*60*1000
        })
    }
    
    getSavedState(callback){
        let self = this;
        self.redis.getData((err , redisData)=>{
            if(err || redisData == null){
                self.L.info("getSavedState::","No saved state found on redis",err)
            }
            else{
                if(redisData!=null && redisData.priority!=null && redisData.id!=null && redisData.tableNo!=null )
                {
                    self.L.info("getSavedState::","Loaded state from redis",redisData)
                    self.state = redisData
                }else{
                    self.L.info("getSavedState: Error while fetching state",err)
                }
            }
            /** Call the callback anyways */
            callback()
        },{key:self.getRedisKey()})
    }

    getRedisKey(){
        return "airtelPublisher_state_"+MOMENT().format("DDMMYYYY")
    }

    resetState() {
        let self = this;
        self.state = {
            id : (this.mode == "desc"? -1 : 0),
            priority : 0,
            tableNo : 0,
            isResetted : true,
            totalPublished : 0
        }
        self.L.log('resetState', `reseted...state::  ${JSON.stringify(self.state)}`);
    }


    startPriorityScheduler() {
        let self = this,
            resetTime = ('00:00:00').split(":");

        let startRule = new SCHEDULE.RecurrenceRule();
        startRule.hour = resetTime[0];
        startRule.minute = resetTime[1];
        SCHEDULE.scheduleJob(startRule, ()=>{
            self.state.isResetted = true;
        });

        self.L.log('startPriorityScheduler', 'for reset priority and  NextBillFetchDate !!');
    }


    getPriority() {
        let self = this;

        self.L.log('getPriority', 'priority ::',self.state.priority);

        return self.state.priority
    }

    getNextId(records){
        let self = this;
        if(_.isArray(records) && !_.isEmpty(records))
            return _.get(records[records.length - 1 ],'id',self.state.id)
        else return self.state.id
    }
   

     suspendOperations(){
        this.stopService = true;
        this.L.info("AirtelPublisher::stopService");
    }
}

export default AirtelKafkaPublisher