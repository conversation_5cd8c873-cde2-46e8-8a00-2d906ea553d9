import BILLS from '../../models/bills';
import utility from '../../lib';
import MOMENT from 'moment';
import ASYNC from 'async';
import RecentsLayerLib from '../../lib/recentsLayer';
import _ from 'lodash';
import <PERSON><PERSON><PERSON><PERSON>OR from 'validator';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import moment from 'moment';
import BillFetchAnalytics from '../../lib/billFetchAnalytics'
import InternalCustIdNonRUFlowTagger from '../../lib/InternalCustIdNonRUFlowTagger';

class houseRentSmsParsing{
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.greyScaleEnv = options.greyScaleEnv;
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA  
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
    }

    executeStrategy(done, record, ref) {
        let self = this;
        this.parent = ref;
        self.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA

        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('1. executeStrategy:: start executing on rent parsed sms');

        try { 
            if (!record) {
                self.L.log(`executeStrategy:: null smsData`);
                return done();
            }
            self.processRecord(record, function (err) {
                if (err) {
                    self.L.error(`RENT_SMS_PARSING_BILL_PAYMENT :: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                        `SERVICE:${category}`, 
                        'STATUS:PROCESSING_ERROR', 
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ])
                }
                return done();
            });
        }
        catch (err) {
            self.L.error(`RENT_SMS_PARSING_BILL_PAYMENT :: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                `SERVICE:${category}`, 
                'STATUS:PROCESSING_ERROR', 
                'SOURCE:POSTPAID_SMS',
                `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            return done();
        }

    }

    initializeAnalyticsPayload(){
        return  {
            source: "SMS_PARSING_DWH",
            source_subtype_2: "FULL_BILL",
            user_type: "NON_RU",
            customer_id: null,
            service: "rent payment",
            recharge_number: null,
            operator: null,
            due_amount: null,
            sender_id: null,
            updated_at: null,
            sms_date_time: null,
            sms_id: null,
            sms_class_id: null,
            paytype: null
        }
    }

    async processRecord(record, done) {
        let self = this;
        let analyticsPayload = self.initializeAnalyticsPayload();
        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('2. processRecord:: start processing current record', JSON.stringify(record));

        try{
            ASYNC.waterfall([
                (next)=>{
                    self.validateAndProcessRecord(async (errorResponse,processedRecord)=>{
                        let operator = _.get(processedRecord, "operator", "NoOpertor");
                        if (errorResponse) {
                            self.L.error(`RENT_SMS_PARSING_BILL_PAYMENT :: VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${errorResponse}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                                `SERVICE:${category}`, 
                                'STATUS:ERROR', 
                                'TYPE:VALIDATION_FAILURE', 
                                'OPERATOR:' + operator, 
                                'REASON:' + errorResponse,
                                `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(errorResponse,processedRecord);
                        }else {
                            self.L.log(`RENT_SMS_PARSING_BILL_PAYMENT :: VALIDATION_SUCCESS`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                                `SERVICE:${category}`, 
                                'STATUS:SUCCESS',
                                'TYPE:VALIDATION_SUCCESS', 
                                'OPERATOR:' + operator,
                                `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(null,processedRecord);
                        }
                    }, record, analyticsPayload);
                },
                (processedRecord, next)=>{
                        // if(processedRecord.recordFoundOfSameCustId != undefined && !processedRecord.recordFoundOfSameCustId){
                        //     self.L.log(`RENT_SMS_PARSING_BILL_PAYMENT :: updateCassandra | Record found for same RN,but with new custId`);
                        //     utility._sendMetricsToDD(1, [
                        //         'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                        //         `SERVICE:${category}`, 
                        //         'STATUS:RECORD_NOT_FOUND_OF_SAME_CID',
                        //         'TYPE:NON_PAYTM_EVENTS',
                        //         `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        //         `APP_VERSION:${_.get(record,'appVersion', null)}`
                        //     ]);
                        // }
                        self.updateCassandra((err)=>{
                             if(err){
                                self.L.error(`RENT_SMS_PARSING_BILL_PAYMENT :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                next(err,processedRecord);
                             }else{
                                self.L.log(`RENT_SMS_PARSING_BILL_PAYMENT ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, processedRecord);
                             }
                        }, processedRecord);
                },
                // (processedRecord, next)=>{
                //     self.publishCtEvents(function(err){
                //         if(err){
                //             self.L.error(`RENT_SMS_PARSING_BILL_PAYMENT: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                //             utility._sendMetricsToDD(1, [
                //                 'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                //                 `SERVICE:${category}`, 
                //                 'SOURCE:PUBLISH_KAFKA',
                //                 'STATUS:ERROR', 
                //                 'TYPE:' + err]);
                //             next(err,processedRecord); 
                //         }
                //         else{
                //             next(null ,processedRecord)
                //         }
                //     },processedRecord)
                // },
                (processedRecord, next)=>{
                    let service = _.get(processedRecord, 'service', null);
                    let source = _.get(processedRecord, 'isRuSmsParsing', false)? `SMS_${service}_REALTIME`:`SMS_${service}`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        next(null,processedRecord);
                    },source,self.timestamps, processedRecord.operator, processedRecord);
                },
            ], async function (error,processedRecord) {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                            `SERVICE:${category}`, 
                            'STATUS:PROCESS_RECORD_FAILURE', 
                            'SOURCE:POSTPAID_SMS', 
                            'TYPE:' + error,
                            `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        self.L.verbose(`RENT_SMS_PARSING_BILL_PAYMENT :: processRecords`, `Exception occured Error Msg:: ${error}`);
                        return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(analyticsPayload, error)
                        .then(() =>{
                            return done();
                        })
                        
                    } else {
                        self.L.log(`RENT_SMS_PARSING_BILL_PAYMENT :: processRecords`, `Record processed `);
                    }
                return done();
            });
        }catch (err) {
            self.L.error('processRecord:: ', err);
            return done();
        }

    }

    validateAndProcessRecord(done,record,analyticsPayload = {}) {
        let self = this;
        self.L.log('3. validateAndProcessRecord :: convert payload to record and validate');
        if (!record) return done('invalid_record', record);
        self.timestamps = {};

        try{
        let smsDateTime_fromPayload = Number(record.smsDateTime);    
        const   timestamp = new Date(record.timestamp).getTime(),
                smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                deviceDateTime = new Date(record.deviceDateTime).getTime(),
                uploadTime = new Date(record.uploadTime).getTime(),
                collector_timestamp = new Date(record.collector_timestamp).getTime(),
                dwhKafkaPublishedTime = new Date(record.published_time).getTime();

        _.set(self.timestamps,'data_smsDateTime',smsDateTime);
        _.set(self.timestamps,'data_timestamp',timestamp);
        _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
        _.set(self.timestamps,'data_uploadTime',uploadTime);
        _.set(self.timestamps,'collector_timestamp',collector_timestamp);
        _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
        _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
        _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
        _.set(self.timestamps, 'dwhKafkaPublishedTime', dwhKafkaPublishedTime);
        
        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000;
            }
        }else{
            _.set(record, 'smsDateTime', MOMENT().format('YYYY-MM-DD'));
        }

        let productId = _.get(self.config,['DYNAMIC_CONFIG', 'RENT_SMS_PARSING', 'all', 'PRODUCT_ID'] );

        let customerId =  (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
            amount = _.get(record, 'amount', null) ?  utility.getFilteredAmount( _.get(record, 'amount', null) ): null,
            billDate = MOMENT(record.smsDateTime),
            operator =  _.get(this.config,['CVR_DATA',productId,'operator']),
            category = self.getServiceCategoryFromRecord(record);
 
        operator = _.toLower(operator);
        analyticsPayload.customer_id = customerId;
        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                analyticsPayload.sms_date_time = record.smsDateTime * 1000;
                analyticsPayload.sms_date_time = MOMENT(analyticsPayload.sms_date_time).format('YYYY-MM-DD HH:mm:ss');
            }
            else{
                _.set(analyticsPayload, 'sms_date_time', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
            }
        }else{
            _.set(analyticsPayload, 'sms_date_time', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        }
        analyticsPayload.sender_id = _.get(record, 'smsSenderID', '');
        analyticsPayload.sms_id = _.get(record, 'msg_id', '');
        analyticsPayload.sms_class_id = _.get(record, "level_2_category", null);
        analyticsPayload.operator = operator;
        analyticsPayload.paytype = 'postpaid';
        analyticsPayload.due_amount = amount;
        // setting dummy rechargeNumber for sms rent payment as it is not present in sms
        _.set(record, 'rechargeNumber', _.get(record, 'cId')+'_dummy');
        analyticsPayload.recharge_number = _.get(record, 'rechargeNumber', '');
        if(!record.dueDate){
            return done("DueDate is null");
        }
        let dueDate =  self.getDueDate(record);
    
        let processedRecord = {
            "operator": operator,
            "customerId": customerId,
            "rechargeNumber": _.get(record, 'rechargeNumber'),
            "gateway": null,
            "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            "billDate": billDate ? billDate.endOf('day').format('YYYY-MM-DD') : null, 
            "dueDate": dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            "amount": amount,
            "status": _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            "paytype": 'postpaid',
            "cache": null,
            "customerMobile": _.get(record, 'smsReceiver', null),
            "customerEmail": null,
            'paymentChannel': null,
            "retryCount": 0,
            "reason": null,
            "extra": { "sms_body": _.get(record, 'smsBody', null),  "level_2_category": _.get(record, 'level_2_category', null) },
            "customer_type":null, 
            "paymentDate": null,
            "user_data":null,
            "msgId" : _.get(record, 'msg_id', ''),
            "sender_id": _.get(record, 'smsSenderID', null),
            "sms_id" : _.get(record, 'sms_id', null),
            "sms_date_time" : _.get(record, 'smsDateTime', null),
            "dwh_classId" : _.get(record, 'dwhClassId', null),
            "category": category,
            "appVersion":  _.get(record, 'appVersion', null),
            "productId": productId,
            "rechargeNumber3":  _.get(record, 'lastCC', null),
            "rechargeNumber2":  _.get(record, 'competitor', null),
            "dwhKafkaPublishedTime": _.get(record, 'published_time', null),
        };
   
        if(_.get(processedRecord, 'productId', null)){
            try {
                _.set(processedRecord, 'paytype', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'paytype'])),null),
                _.set(processedRecord, 'service', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'service'])), null);
            } catch(err) {
                self.L.error("Couldn't set paytype and service from cvr for record ", JSON.stringify(processedRecord))
            }
        }
        analyticsPayload.operator = operator;
        analyticsPayload.paytype = _.get(processedRecord, 'paytype', null);
        analyticsPayload.due_amount = amount;
        let debugKey = `rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`;
            _.set(processedRecord, 'debugKey', debugKey);

        let mandatoryParams = ['customerId', 'rechargeNumber', 'operator', 'productId'];
        
        let fieldsNotPresent = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) fieldsNotPresent.push(key);
        });

        // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            return done(`Mandatory Params ${fieldsNotPresent} is Missing / Invalid`, processedRecord);
        }
        else if(_.get(processedRecord, 'amount') == null || _.get(processedRecord, 'amount')==0){
            return done('Invalid Amount|| Amount is null or zero', processedRecord);
        }
        else if (processedRecord.dueDate && (!MOMENT(processedRecord.dueDate).isValid() || MOMENT(processedRecord.dueDate).diff(MOMENT().endOf('day'), 'days') < 0)) {
            return done('DueDate is invalid || DueDate in past', processedRecord);
        }

        let activePid = self.activePidLib.getActivePID(processedRecord.productId);
            self.L.verbose('processRecord', `Found active Pid ${activePid} against PID ${processedRecord.productId}`);
            processedRecord.oldProductId = processedRecord.productId; // Keeping track of original PID
            processedRecord.productId = activePid;    // Replacing active PID

        self.L.log('3. validateAndProcessRecord :: payload after processing',processedRecord);

        return done(null, processedRecord);
        }
        catch(err){
            return done(err);
        }
    }

    getDueDate(record){
        let self = this,
            dueDate = utility.getFilteredDate( _.get(record, 'dueDate') ).value,
            thresholdAmount = _.get(self.config,['DYNAMIC_CONFIG', 'RENT_SMS_PARSING', 'COMMON', 'THRESHOLD_AMOUNT'], 5000),
            currDate = moment(),
            amount = _.get(record, 'amount', null) ?  utility.getFilteredAmount( _.get(record, 'amount', null) ): null;

        if( amount > thresholdAmount ){
            while(dueDate.isBefore(currDate)){
                dueDate = dueDate.add( _.get(self.config,['DYNAMIC_CONFIG', 'RENT_SMS_PARSING', 'COMMON', 'DUE_DAYS_BEYOND_THRESHOLD'], 30 ), 'days'); 
            }
        }else{
           dueDate = dueDate.add( _.get(self.config,['DYNAMIC_CONFIG', 'RENT_SMS_PARSING', 'COMMON', 'DUE_DAYS_WITHIN_THRESHOLD'], 3 ), 'days'); 
           if(dueDate.isBefore(currDate)){
                dueDate = moment();
           }
        }
        return dueDate;
    }

    async updateCassandra(done, processedRecord) {
        let self = this;
        self.L.log('5. updateCassandra:: starting updateCassandra');
        try {
            let extra = _.get(processedRecord, 'extra', {});
            extra.eventState = "bill_gen";
            extra.billSource = "sms_parsed";
            extra.updated_data_source = "SMS_PARSING_DWH";

            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId,
                rechargeNumber: processedRecord.rechargeNumber,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                amount: processedRecord.amount,
                dueDate : MOMENT(processedRecord.dueDate).isValid() ? MOMENT(processedRecord.dueDate).format() : null,
                billDate : MOMENT().format(),   
                billFetchDate : MOMENT(processedRecord.billFetchDate).format(),
                paytype: processedRecord.paytype,
                service: processedRecord.service,
                circle: null,
                categoryId: _.get(self.config, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
                customer_mobile:  _.get(processedRecord, 'customerMobile', null),
                customer_email: null,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),  
                customerOtherInfo: JSON.stringify(_.clone(processedRecord)),
                extra : JSON.stringify(extra),            
                dbEvent: "upsert",
                rechargeNumber3: processedRecord.rechargeNumber3,
                rechargeNumber2: processedRecord.rechargeNumber2,
                dwhKafkaPublishedTime: _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                nonpaytm_onBoardTime: new Date().getTime(),
            }

            let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);

            self.parent.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                    return done('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('RENT_SMS_PARSING_BILL_PAYMENT :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', nonRuDataToPublish);
                    return done(null);
                }
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            done(error);
        }
    }

    /**
     * Returns active users for which smsparsing data will be updated
     * @param {*} dbRecords 
     */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }

    getServiceCategoryFromRecord(record) {
        let key = _.get(record, 'predicted_category', null) ? _.get(record, 'predicted_category', null).toLowerCase() : null,
            serviceCategory = null;
        
        switch (key) {
            case 'rent':
                serviceCategory = 'rent';
                break;
            default:
                // Set default in case of when category won't be coming in DWH flow
                serviceCategory = 'rent'
                break;
        }
        return serviceCategory;
    }

    /**
     * @param {*} record 
     */
    // async publishCtEvents(done,record) {
    //     let self = this;
    //     self.L.log(`11. publishCtEvents:: Record Category - ${record.category}`);
    //     const productId = _.get(record, 'productId', 0);
    //     let dbData = _.get(record, 'dbData', []);
    //     ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
    //         let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen');

    //         let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
    //         if ((dataRow.status == 13 || dataRow.status == 7)) {
    //             self.L.log('publishInKafka', `Skipping pulish CT for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice record`);
    //             return cb();
    //         }
    //         let billsData = record.billsData;
    //         dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
        
    //         if(dataRow.notification_status == 0){
    //             self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
    //             return cb();                         
    //         }
            
    //         if(dataRow.notification_status == null)
    //             dataRow.notification_status = 1
    //         dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
    //         dataRow.amount = record.amount;
    //         dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
    //         dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
    //         dataRow.status = _.get(billsData, 'commonStatus', 0);
    //         dataRow.extra = _.get(billsData, 'extra', null);
    //         dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
    //         dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
    //         dataRow.rtspClassId = _.get(record, 'rtspClassId', null);
    //         dataRow.dwhClassId = _.get(record, 'dwhClassId', null);

    //         ASYNC.waterfall([
    //             next => {
    //                 self.commonLib.getRetailerData((error) => {
    //                     if(error) {
    //                         return next(error)
    //                     } else {
    //                         return next(null)
    //                     }
    //                 }, dataRow.customer_id, dataRow);
    //             },
    //             next => {
    //                 self.commonLib.getCvrData((error) => {
    //                     if(error) {
    //                         return next(error)
    //                     } else {
    //                         return next(null)
    //                     }
    //                 }, productId, dataRow);
    //             },
    //             next => {                    
    //                 let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
    //                 let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
    //                 self.parent.ctKafkaPublisher.publishData([{
    //                     topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
    //                     messages: JSON.stringify(mappedData)
    //                 }], (error) => {
    //                     if (error) {
    //                         utility._sendMetricsToDD(1, [
    //                             "REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 
    //                             `SERVICE:${_.get(record, 'category', null)}`, 
    //                             'STATUS:ERROR', 
    //                             "TYPE:KAFKA_PUBLISH",
    //                             "TOPIC:CT_EVENTS", 
    //                             "OPERATOR:" + dataRow.operator,
    //                             `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
    //                             `APP_VERSION:${_.get(record,'appVersion', null)}`
    //                         ]);
    //                         self.L.critical('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
    //                     } else {
    //                         utility._sendMetricsToDD(1, [
    //                             "REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 
    //                             `SERVICE:${_.get(record, 'category', null)}`, 
    //                             'STATUS:PUBLISHED', 
    //                             "TYPE:KAFKA_PUBLISH",
    //                             "TOPIC:CT_EVENTS", 
    //                             "OPERATOR:" + dataRow.operator,
    //                             `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
    //                             `APP_VERSION:${_.get(record,'appVersion', null)}`
    //                         ]);
    //                         self.L.log('postpaidSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
    //                     }
    //                     return next(error);
    //                 }, [200, 800]);
    //             }
    //         ], (err) => {
    //             if(err)
    //                 self.L.log('postpaidSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
    //             return cb(err)
    //         })
    //     }, (error, res) => {
    //         if (error) {
    //             self.L.error("postpaidSmsParsing :: publishCtEvents ", "Error occured", error);
    //         }
    //         return done(error);
    //     });
    // }
}
export default houseRentSmsParsing;
