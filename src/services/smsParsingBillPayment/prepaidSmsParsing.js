import utility from '../../lib';
import MOMENT from 'moment';
import _ from "lodash";
import DigitalCatalog from '../../lib/digitalCatalog'
import VALIDATOR from 'validator';
import USERS from '../../models/users'
import ASYNC from 'async';
import PLAN_VALIDITY from '../../models/planValidity';
import RecentsLayerLib from '../../lib/recentsLayer';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import BILLS from '../../models/bills';
import BillFetchAnalytics from '../../lib/billFetchAnalytics';
import InternalCustIdNonRUFlowTagger from '../../lib/InternalCustIdNonRUFlowTagger';

const PLAN_BUCKET = 'Special Recharge';
const VI_GROUP = ['vodafone idea', 'vodafone', 'idea', 'vi', 'vi india'];
const OPERATOR_DEFAULT_AMOUNT= {'vodafone idea': 299, 'jio': 239, 'airtel': 239};
const OPERATOR_LIST = ['vodafone idea', 'jio', 'airtel'];
class prepaidSmsparsing {
    constructor(options) {
        this.L = options.L;
        this.rechargeConfig = options.rechargeConfig;
        this.users = new USERS(options);
        this.dbInstance = options.dbInstance;
        this.activePidLib = options.activePidLib;
        this.config = options.config;
        this.digitalCatalog = new DigitalCatalog(options);
        this.planValidityModel = new PLAN_VALIDITY(options);
        this.dbBatchSize = 1000;
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.servicePeriod = 2 * 60; //time in seconds
        this.tableName = 'plan_validity';
        this.dbName = 'recharge_analytics';
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.grey_config = _.get(this.config,[ 'DYNAMIC_CONFIG', 'GREYSCALE_CONFIG']);
        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.smsParsingBillsDwhRealtime = _.get(options, 'smsParsingBillsDwhRealtime', false);
        this.allowed_operators = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATORS'], null);
        this.allowed_circles_for_vodaphone_idea = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'VODAFONE', 'CIRCLES'], null);
        this.minAmount= _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'MIN_AMOUNT'], 10);
        this.maxAmount= _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'MAX_AMOUNT'], 500000);
        this.validityExpiryIds = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','VALIDITY_EXPIRY_IDS'],[5,6]);
        this.partialBillIds = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','PARTIAL_BILL_IDS'],[5,6,7]);
        this.ctEventIds = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','CT_EVENT_IDS'],[3,4,7,8,9,10]);
        this.dataRechargeIds = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','DATA_IDS'],[1,2]);
        this.notificationTimeOut = _.get(options.config, ["NOTIFICATION", "TEMPLATE_ID_BY_SERVICE", "DATA_EXHAUST_NOTIFICATION_TIMEOUT"], 30);
        this.PARTIAL_BILL_RECO_MAP = {5: "EXPIRING_SOON", 6: "EXPIRED", 7: "INCOMING_STOPPED"};
        this.dwhClassId8DueDays=_.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATOR_DWH_CLASS_ID_8_DUE_DAYS'], 0);

        this.DWH_CLASS_CT_NAME_MAP = {1: 'smsparsedDataExpiring', 2: 'smsparsedDataExpired', 3: 'smsparsedDataRecharged', 4: 'smsparsedDataHungry', 5: 'prepaidRecharged', 6: 'prepaidRecharged', 7: 'smsparsedIncomingStopped', 8: 'smsparsedPlanRecharged', 9: 'smsparsedNewSIM', 10: 'smsparsedAgentRelated'};
        this.operator_circle_productId_map = {};
        this.amount_plan_bucket_map = {};
        this.OPERATOR_DEFAULT_EXPIRED_DAYS = {};
        this.OPERATOR_DEFAULT_EXPIRING_DAYS = {};
        this.OPERATOR_DEFAULT_DIFF_DAYS = {};
        this.OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG = {};
        OPERATOR_LIST.forEach(function(operator_name){
            OPERATOR_DEFAULT_AMOUNT[operator_name] = _.get(options.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_AMOUNT'], 299);

        });
        this.flush_operator_circle_productId_map();
        //  this.OAuth = new OAuth({ batchSize: _.get(this.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: this.rechargeConfig });
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.realtimePayloadIngestionTable = "sms_parsing_payload_ingestion"
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.saveForAnalyticsInCassandraDbAndKafka = options.saveForAnalyticsInCassandraAndKafka ? true : false;
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        this.initializeOperatorWisePartialToFullConfig();
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);

    }

    initializeVariable(){
        let self = this;
        self.allowed_operators = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATORS'], null);
        self.allowed_circles_for_vodaphone_idea = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'VODAFONE', 'CIRCLES'], null);
        self.minAmount= _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'MIN_AMOUNT'], 20);
        self.maxAmount= _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'MAX_AMOUNT'], 500000);
        self.validityExpiryIds = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','VALIDITY_EXPIRY_IDS'],[5,6]);
        self.ctEventIds = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','CT_EVENT_IDS'],[3,4,7,8,9,10]);
        self.dataRechargeIds = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'PREPAID_CLASSIFIERS','DATA_IDS'],[1,2]);
        self.notificationTimeOut = _.get(self.config, ["NOTIFICATION", "TEMPLATE_ID_BY_SERVICE", "DATA_EXHAUST_NOTIFICATION_TIMEOUT"], 30);
        self.grey_config = _.get(self.config,[ 'DYNAMIC_CONFIG', 'GREYSCALE_CONFIG']);
        self.dwhClassId8DueDays=_.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', 'COMMON', 'OPERATOR_DWH_CLASS_ID_8_DUE_DAYS'], 0);
       // self.operator_circle_productId_map = {};

        OPERATOR_LIST.forEach(function(operator_name){
            OPERATOR_DEFAULT_AMOUNT[operator_name] = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_AMOUNT'], 299);
            self.OPERATOR_DEFAULT_EXPIRED_DAYS[operator_name] = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_EXPIRED_DAYS'], null);
            self.OPERATOR_DEFAULT_EXPIRING_DAYS[operator_name] = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_EXPIRING_DAYS'], null);
            self.OPERATOR_DEFAULT_DIFF_DAYS[operator_name] = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_DIFF_DAYS'], 5);
            self.OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG[operator_name] = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'PARTIAL_TO_FULL_PERCENT'], null);

        })
    }

    initializeOperatorWisePartialToFullConfig() {
        OPERATOR_LIST.forEach((operator_name) => {
            this.OPERATOR_DEFAULT_EXPIRED_DAYS[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_EXPIRED_DAYS'], null);
            this.OPERATOR_DEFAULT_EXPIRING_DAYS[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_EXPIRING_DAYS'], null);
            this.OPERATOR_DEFAULT_DIFF_DAYS[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'DEFAULT_DIFF_DAYS'], 5);
            this.OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG[operator_name] = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAIDSMSPARSING', operator_name, 'PARTIAL_TO_FULL_PERCENT'], null);
        });
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return _.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH';
    }

    
    executeStrategy(done, record, ref) {
        let self = this;
        this.parent = ref;
        self.timestamps = {};
        self.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        try {
            if (!record) {
                return done();
            }
            self.processRecords(record, function (err) {
                if (err) {
                    self.L.error(`SMS_PARSING_PREPAID:PS_PARSING:: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                        'STATUS:PROCESSING_ERROR', 
                        'SOURCE:PS_PARSING',
                        
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                }

                return done();
            });
        }
        catch (err) {
            self.L.error(`SMS_PARSING_PREPAID:PS_PARSING:: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                'STATUS:PROCESSING_ERROR', 
                'SOURCE:PS_PARSING'
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            return done();
        }

    }

    flush_operator_circle_productId_map() {
        let self = this;
        setInterval(() => {
            self.operator_circle_productId_map = {};
            self.amount_plan_bucket_map = {};
        }, 600 * 60 * 1000);
    }

    async ingestIncomingPayloads(errorResponse, processedRecord, record){
        let self=this;
        let due_date =null;
        if(_.get(processedRecord, 'validityDate', null)){
            due_date = _.get(processedRecord, 'validityDate', null).format('YYYY-MM-DD HH:mm:ss')
        } else if(_.get(record, ['telecomDetails', 'expiry_date'], null)){
            due_date = MOMENT(_.get(record, ['telecomDetails', 'expiry_date'], null)).format('YYYY-MM-DD HH:mm:ss')
        }

        let ruOnboarded = _.get(processedRecord, 'ruOnboarded', false);
        let source_kafka_topic = _.get(self.config.KAFKA, 'SERVICES.REALTIME_SMS_PARSING_PREPAID.TOPIC');
        let dbRecord = {
            "recharge_number" : _.get(record, 'rechargeNumber', null),
            "customer_id" : (typeof _.get(record,'cId',null) === 'number') ? _.get(record,'cId',null) : (typeof _.get(record,'cId',null) === 'string' && VALIDATOR.isNumeric(_.get(record,'cId',null))) ? VALIDATOR.toInt(_.get(record,'cId',null)) : null,
            "operator" : _.get(record, ['telecom_details','operator'], _.get(record, 'smsOperator', null)),
            "product_id" : _.get(processedRecord, 'productId', null),
            "service" : "mobile",
            "paytype" : "prepaid",
            "classifier_id" : _.get(record, 'rtspClassId', null),
            "classifier_name" : _.get(record, 'rtspClassName', null),
            "template_body" : _.get(record, 'templateBody', null),
            "status" : (errorResponse)? 0:1,
            "error_message" : errorResponse,
            "source" : "REALTIME_SMS_PARSING_TELECOM",
            "source_kafka_topic": `${source_kafka_topic}`,
            "sender_id" : _.get(processedRecord, 'senderId', null),
            "payload" :  record,
            "amount" : utility.getFilteredAmount(_.get(record, 'telecom_details.due_amount', _.get(record, 'amount', '0'))),
            "dataConsumed" : utility.getFilteredAmount(_.get(record, 'dataConsumed', null)),
            "ruOnboarded" : ruOnboarded,
            "due_date" : due_date
        }

        /*
         "recharge_number" : _.get(record, 'smsReceiver', null),
                "customer_id" : _.get(record, 'cId', null),
                "operator" : _.get(record, ['telecom_details','operator'], _.get(record, 'smsOperator', null)),
                "product_id" : _.get(extraDetails, 'productId', null),
                "service" : "mobile",
                "paytype" : "prepaid",
                "classifier_id" : _.get(record, 'rtspClassId', null),
                "classifier_name" : _.get(record, 'rtspClassName', null),
                "template_body" : _.get(record, 'templateBody', null),
                "status" : (error)? 0:1,
                "error_message" : error,
                "source" : "REALTIME_SMS_PARSING_TELECOM",
                "source_kafka_topic": `${source_kafka_topic}`,
                "sender_id" : _.get(record, 'smsSenderID', null),
                "payload" :  record,
                "amount" : _.get(record, 'planAmount', null),
                "dataConsumed" : _.get(record, ['telecom_details','dataConsumedInPercentage'], null),
                "ruOnboarded" : ruOnboarded,
                "due_date" : _.get(record, ['telecom_details','due_date'], null)? MOMENT(_.get(record, ['telecom_details','due_date'], null)).format('YYYY-MM-DD HH:mm:ss'):null,
           
        */
        
        return new Promise((resolve)=>{
            self.bills.ingestRealtimePayloads(dbRecord,self.realtimePayloadIngestionTable,function(err,data){
                if(err){
                    self.L.error(`ingestCCPayloadInDB :: Error while inserting payload in table ${self.realtimePayloadIngestionTable} err:: ${err} for record ${JSON.stringify(dbRecord)}`);
                }else{
                    self.L.log(`ingestCCPayloadInDB :: Payload inserted successfully in table ${self.realtimePayloadIngestionTable} for record ${JSON.stringify(dbRecord)}`);
                }
                return resolve(null);
            })
        })
    }
    fetchDataTemplates(cb, record){
        let self = this,
        notificationType = 'DUEDATE',
        dataConsumed= _.get(record, 'dataConsumed',null)
        if(dataConsumed==null){
            return cb('Missing dataConsumed field');
        }
        dataConsumed= dataConsumed+"_";
        _.set(record, 'service', 'mobile');

        let templates = {};

        for (let key in this.notificationConfig.type) {
           
            if (this.notificationConfig.type[key]) {
                templates[key] = this.getTemplateId(key, record, notificationType, _.get(record,'dueDate',null),dataConsumed);
            }
        }
        return cb(null,templates);
    }

    getTemplateId(type, record, notificationType, dueDate, dataConsumed){
        let self=this;
        let cleanedDataConsumed = parseFloat(dataConsumed.replace('%', ''));
        let serviceBasedKey = `DATA_EXHAUST_${_.toUpper(_.get(record, 'service'))}_${cleanedDataConsumed}_${notificationType}_${type}`;
        
        self.L.log("getTemplateId, serviceBasedKey: ", serviceBasedKey);
        
        let templateId =    _.get(this.operatorTemplateMappingConfig, [_.toLower(_.get(record, 'operator', null)), `DATA_EXHAUST_${cleanedDataConsumed}_${notificationType}_${type}`],
                                _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', serviceBasedKey],
                                    _.get(this.notificationConfig, ['templateid', `DATA_EXHAUST_${cleanedDataConsumed}_${notificationType}_${type}` ],
                                        _.get(this.operatorTemplateMappingConfig, [_.toLower(_.get(record, 'operator', null)), `DATA_EXHAUST_${notificationType}_${type}`], 
                                            _.get(this.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `DATA_EXHAUST_${_.toUpper(_.get(record, 'service'))}_${notificationType}_${type}`], 
                                                _.get(this.notificationConfig, ['templateid', `DATA_EXHAUST_${notificationType}_${type}` ], null)
                                                )
                                            )
                                        )
                                    )
                                );
        self.L.log("getTemplateId::", `operator:${_.get(record, 'operator', null)}_notificationType:${notificationType}_dueDate:${dueDate}_cleanedDataConsumed:${cleanedDataConsumed}_type:${type}_templateId:${templateId}`);
        return templateId;
    }

    preparePayloadForNotificationReject(processedRecord){
        let payload = {
            "recharge_number" : _.get(processedRecord, 'rechargeNumber', null),
            "customer_id" : _.get(processedRecord, 'customerId', null),
            "operator" : _.get(processedRecord, 'operator', null),
            "product_id" : _.get(processedRecord, 'productId', null),
            "service" : _.get(processedRecord, 'service', null),
            "paytype" : _.get(processedRecord, 'paytype', null),
            "bill_source": "RU",
            "message_id":_.get(processedRecord, 'msgId', null),
        }
        return payload;
    }
    

    async publishInBillFetchKafkaForDataExhaust(done,processedRecord, options = {}){
        let self = this;
        let extra ;
        if(_.get(processedRecord, 'is_notified_at_fe', false)){
            self.L.log("publishInBillFetchKafkaForDataExhaust: ", "Notification already sent to user at FE, hence not publishing to bill fetch kafka for custID and RN:", processedRecord.customerId, processedRecord.rechargeNumber);
            utility._sendMetricsToDD(1,[
                `REQUEST_TYPE:SMS_PARSING_PREPAID`, 
                'STATUS:NOTIFIED_ALREADY', 
                'TYPE:KAFKA_PUBLISH', 
                'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME', 
                `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                "OPERATOR:" + _.get(processedRecord,'operator',null), 
                `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`, 
                `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`]);

            let error = "NOTIFIED_AT_FE_AND_NOT_PUBLISHED_IN_BILL_FETCH_KAFKA";
            let payloadToSendNotificationReject = self.preparePayloadForNotificationReject(processedRecord);

            await self.parent.notify.insertRejectedNotificationsViaPromise(error, payloadToSendNotificationReject);
            return done(null);
        }else{
            if(_.get(options, "oldFlow", false)){
                extra = null;
            }
            else{
                extra = {};
                extra.is_data_exhaust = _.get(processedRecord, 'isRealTimeDataExhausted', false);
                extra.data_exhaust_value = _.get(processedRecord, 'exhausted_data', _.get(processedRecord, 'dataConsumed', null));
                extra.data_exhaust_date = _.get(processedRecord, 'exhausted_date', null);
                extra.recon_id = _.get(processedRecord, 'recon_id', null);
            }
            let payload = {
                source: "dataExhaust",
                notificationType: "BILLDUE",
                data: {
                    customer_id: processedRecord.customerId,
                    recharge_number: processedRecord.rechargeNumber,
                    product_id: processedRecord.productId,
                    operator: processedRecord.operator,
                    due_amount: processedRecord.amount,
                    data_consumed: processedRecord.dataConsumed,
                    bill_fetch_date: MOMENT(),
                    paytype: "prepaid",
                    service: processedRecord.service,
                    circle: processedRecord.circle,
                    customer_mobile:  null,
                    customer_email: null,
                    status: self.config.COMMON.bills_status.BILL_FETCHED,
                    user_data: null,
                    bill_date: null,
                    notification_status: 1,
                    due_date: processedRecord.dueDate,
                    customer_other_info: JSON.stringify(processedRecord),
                    plan_bucket: processedRecord.planBucket,
                    templates: processedRecord.templates,
                    isRealTimeDataExhausted: processedRecord.isRealTimeDataExhausted,
                    time_interval: processedRecord.time_interval,
                    extra: extra,
                    dwhKafkaPublishedTime : _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                }
            }
            _.set(payload, ['data', 'billFetchReminder_onBoardTime'],new Date().getTime());
            if(_.get(processedRecord,"isRealTimeDataExhausted",true))_.set(payload, ['data', 'isRnDecrypted'],true);
            utility.sendNotificationMetricsFromSource(payload)
            self.parent.dataExhaustKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME', ''),
                messages: JSON.stringify(payload),
                key : _.get(payload, 'data.customer_id' ,'')
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: SMS_PARSING_PREPAID", 
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME', 
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: NONRU_REMINDER_BILL_FETCH_REALTIME', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        'TOPIC:NONRU_REMINDER_BILL_FETCH_REALTIME', 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: NONRU_REMINDER_BILL_FETCH_REALTIME', 'Message published successfully in Kafka', ' on topic NONRU_REMINDER_BILL_FETCH_REALTIME', JSON.stringify(payload));
                }
                return done(null);
            }, [200, 800]);
        }
    }

    getTableName(params){
        let self = this;
        let recharge_number = params.rechargeNumber
        let i = recharge_number.slice(recharge_number.length -1 )
        if(/^[0-9]$/.test(i))
            return "bills_airtelprepaid"+i;
        else return null;
    }

    fetchRecordsFromAirtelPrepaidTable(next, processedRecord) {
        let self = this;
        let tableName = self.getTableName(processedRecord);
        self.bills.getRecordsFromAirtelPrepaidTable(tableName, processedRecord, function(error, result){
            if(error){
                self.L.error("fetchRecordsFromAirtelPrepaidTable:: error in fetching records from airtel prepaid table", error);
                return next(null, processedRecord);
            }else if(_.isArray(result) && result.length > 0){
                self.L.log("fetchRecordsFromAirtelPrepaidTable:: result: ", JSON.stringify(result));
                let [maxDueDate, listOfOtherCustomerIds] = self.filterRecordsFromAirtelPrepaidTable(result, processedRecord);
                let airtelPrepaidPublisherDetails = self.setAirtelPrepaidPublisherDetails(processedRecord, maxDueDate, listOfOtherCustomerIds);
                self.L.log("fetchRecordsFromAirtelPrepaidTable:: airtelPrepaidPublisherDetails: ", JSON.stringify(airtelPrepaidPublisherDetails));
                _.set(processedRecord, ['airtelPrepaidPublisherDetails'], airtelPrepaidPublisherDetails);
                return next(null, processedRecord);
            }else{
                self.L.log("fetchRecordsFromAirtelPrepaidTable:: no records found in airtel prepaid table", result);
                return next(null, processedRecord);
            }
        });
    }

    setAirtelPrepaidPublisherDetails(processedRecord, maxDueDate, listOfOtherCustomerIds){
        let self = this;
        let operator = _.get(processedRecord, 'operator', null);
        let circle = _.get(processedRecord, 'circle', 'all circles');
        let max_due_date = maxDueDate;
        let list_of_other_customer_ids = listOfOtherCustomerIds;
        return {
            "operator": operator,
            "circle": circle,
            "max_due_date": max_due_date,
            "list_of_other_customer_ids": list_of_other_customer_ids
        }
    }

    filterRecordsFromAirtelPrepaidTable(result, processedRecord){
        let self = this;
        /*
            filter conditions - 
            1. Record should not have status 0 and status between 130 to 139
        */
        result = result.filter(function(record){
            if(record.status != 0 && !(record.status >= 130 && record.status <= 139)){
                return true;
            }
            return false;
        });

        if(result.length == 0){
            return [null, []];
        }

        self.L.log("filterRecordsFromAirtelPrepaidTable:: result: ", JSON.stringify(result));

        let maxDueDate = _.maxBy(result, 'due_date');
        if (maxDueDate && maxDueDate.due_date) {
            maxDueDate = maxDueDate.due_date;
        } else {
            maxDueDate = null;
        }

        let listOfOtherCustomerIds = result.map(function(record){
            return record.customer_id;
        });
        
        return [maxDueDate, listOfOtherCustomerIds];
    }

    markAirtelRecordsAsInactive(next, processedRecord){
        let self = this;
        let airtelPrepaidPublisherDetails = _.get(processedRecord, 'airtelPrepaidPublisherDetails', null);
        if(airtelPrepaidPublisherDetails){
            let listOfOtherCustomerIds = _.get(processedRecord, 'airtelPrepaidPublisherDetails.list_of_other_customer_ids', null);
            if(listOfOtherCustomerIds && _.isArray(listOfOtherCustomerIds) && listOfOtherCustomerIds.length > 0){
                let rechargeNumber = _.get(processedRecord, 'rechargeNumber', null);
                let tableName = self.getTableName(processedRecord);
                return self.bills.markAirtelRecordsAsInactive(tableName, rechargeNumber, listOfOtherCustomerIds, next);
            }
            else{
                return next(null, processedRecord);
            }
        }
        else{
            return next(null, processedRecord);
        }
    }

    async processRecords(record, done) {
        let self = this;
        ASYNC.waterfall([
            async (next) => {
                let [error, processedRecord] = await self.validateAndProcessRecord(record);
                if (error) {
                    let operator = record.telecomDetails && record.telecomDetails.operator || record.operator || "NoOpertor";
                    self.L.log(`SMS_PARSING_PREPAID:PS_PARSING::VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:VALIDATION_FAILURE', 
                        `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                        'OPERATOR:' + operator, 
                        'TYPE:' + error,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    next(error,processedRecord);
                }
                else {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:SUCCESS',
                        'TYPE:VALIDATION_SUCCESS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    // self.L.log(`SMS_PARSING_PREPAID:PS_PARSING:Processing ${JSON.stringify(record)} record for`, processedRecord.debugKey);
                    next(null,processedRecord);
                }
            },
            (processedRecord ,next) => {
                self.L.log("processRecords::ProcessedRecord:: ", JSON.stringify(processedRecord));
                self.get_circle_of_record(function (error, circle) {
                    if (error || !circle) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                            'STATUS:ERROR', 
                            `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                            'TYPE:NO_CIRCLE',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null), error || "Circle Not Found", next, processedRecord);
                        return next(error || "Circle Not Found", processedRecord);
                    }
                    processedRecord.circle = circle;
                    next(null,processedRecord);
                }, processedRecord.operator, processedRecord.rechargeNumber);
            },
            (processedRecord, next) => {
                if(processedRecord.isDefaultAmount) return next(null, processedRecord);
                
                self.get_plan_details_of_record(function (error, plan_bucket) {
                    if(!processedRecord.dwhClassId)    // old version packect
                    {
                        if (error || plan_bucket != PLAN_BUCKET) {
                            processedRecord['planAmount'] = OPERATOR_DEFAULT_AMOUNT[processedRecord.operator];
                            processedRecord['isDefaultAmount'] = true;
                            plan_bucket = PLAN_BUCKET;
                        }
                    }else{
                        if(error && self.validityExpiryIds.includes(processedRecord.dwhClassId)){
                            processedRecord['planAmount'] = OPERATOR_DEFAULT_AMOUNT[processedRecord.operator];
                            processedRecord['isDefaultAmount'] = true;
                            plan_bucket = PLAN_BUCKET;
                        }
                    }
                    processedRecord.plan_bucket = plan_bucket;
                    next(null, processedRecord);
                }, processedRecord.operator, processedRecord.planAmount, processedRecord.service, processedRecord.circle);
            },

            (processedRecord ,next) => {
                self.get_productId_of_record(function (error, productId) {
                    if (error || !productId){
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                            'STATUS:ERROR', 
                            `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                            'TYPE:NO_PID',
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null), error || "productId Not Found", next, processedRecord);
                        return next(error || "productId not found", processedRecord);
                    } 
                    processedRecord.productId = productId;
                    //console.log("get_productId_of_record ", self.operator_circle_productId_map, productId, processedRecord.circle);
                    next(null,processedRecord);
                }, processedRecord.circle, processedRecord.operator);
            },

            (processedRecord,next) => {
                if(!_.get(processedRecord,"isRealTimeDataExhausted",false))return next(null, processedRecord);
                self.fetchDataTemplates(function(err,result){
                    if(err){
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                            'STATUS:NOTIFICATION_TEMPLATE_NOT_FOUND', 
                            `CLASSID:${_.get(record, 'rtspClassId', null)}`, 
                            'OPERATOR:' + _.get(record, ['telecom_details','operator'],"NoOpertor"), 
                            `ORIGIN:${_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        self.L.error("fetchDataTemplates:: error in fetching templates ", err);
                    }else{
                        _.set(processedRecord,'templates', result);
                    } 
                    next(null,processedRecord);
                },processedRecord)
            },
            (processedRecord, next) => {
                if(!_.get(processedRecord,"isRealTimeDataExhausted",false))return next(null, processedRecord);
                let source = _.get(processedRecord, 'isRuSmsParsing', false)? "SMS_MOBILE_PREPAID_REALTIME":"SMS_MOBILE_PREPAID";
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        self.L.log("smsParsingBillPayment:: smsParsingLagDashboard metrics pushed, err:",err);
                        next(null,processedRecord);
                    },source,self.timestamps, processedRecord.operator,processedRecord);
            },
            (processedRecord ,next) => {
                if(!processedRecord.dwhClassId) return next(null, processedRecord);
                if((processedRecord.partialBillState && !processedRecord.is_wo_rech_num) || ((self.validityExpiryIds.includes(processedRecord.dwhClassId) || processedRecord.dwhClassId == 8) && !processedRecord.is_wo_validity && !processedRecord.is_wo_rech_num)){
                    return next(null, processedRecord)
                }
                //calculate rollout flag  and add in below if condition
                if(!processedRecord.is_wo_validity && !processedRecord.is_wo_rech_num && processedRecord.isRealTimeDataExhausted){
                    let GREY_CONFIG_RECO = Number(_.get(this.grey_config, ['MOBILE_DATA_EXHAUST_RECO', "PERCENTAGE"], null));
                    self.L.info("GRAY SCALE CONFIG PICKED FOR RECO: ", GREY_CONFIG_RECO, " PERCENTAGE for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
                    self.L.info("Record received is DataExhausted reco event for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
        
                    let customerId = processedRecord.customerId;
                    if (!_.get(this.grey_config, ['MOBILE_DATA_EXHAUST_RECO', "PERCENTAGE"], null) || customerId % 100 >= GREY_CONFIG_RECO) {
                        self.L.info("Record is not under grey range custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
                        processedRecord.shouldPublishUsingOldFlow = true;
                        return next(null, processedRecord);
                    }
                    else{
                        return next(null, processedRecord);
                    }
                }
                let ct_data = self.create_ct_event_payload(processedRecord);
                self.publishCtEvents(async function(){
                    let error = "CT_EVENT_CLASS";
                    if(processedRecord.is_wo_validity) error = "NO_Validity_CT" 
                    if(processedRecord.is_wo_rech_num) error = "NO_RN_CT"
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) await self.saveForAnalytics(processedRecord, error, 2, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null)
                    return next(error, processedRecord, true);
                }, ct_data, processedRecord);
            },
            (processedRecord, next) => {
                if (processedRecord.shouldPublishUsingOldFlow) {
                    return self.publishInBillFetchKafkaForDataExhaust(function (err) {
                        if (err) {
                            return next(err, processedRecord);
                        } else {
                            let ct_data = self.create_ct_event_payload(processedRecord);
                            self.publishCtEvents(async function () {
                                let error = "CT_EVENT_CLASS";
                                if (processedRecord.is_wo_validity) error = "NO_Validity_CT"
                                if (processedRecord.is_wo_rech_num) error = "NO_RN_CT"
                                if (self.saveForAnalyticsInCassandraAndKafka(processedRecord)) await self.saveForAnalytics(processedRecord, error, 2, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null)
                                return next(error, processedRecord, true);
                            }, ct_data, processedRecord);

                        }
                    }, processedRecord, { 'oldFlow': true });
                }
                else{
                    return next(null, processedRecord);
                }
            },

            (processedRecord, next) => {
                if(_.toLower(_.get(processedRecord, 'operator', null)) == 'airtel'){
                    return self.fetchRecordsFromAirtelPrepaidTable(next, processedRecord);
                }
                else{
                    return next(null, processedRecord);
                }
            },
           
            (processedRecord ,next) => {
                self.getRecordsFromPlanValidity(function (error, dbRecordResp) {
                    if (error) {
                        // self.L.log('SMS_PARSING_PREPAID:PS_PARSING:processRecords::getRecordsFromPlanValidity', `Unable to get valid RechargeNumber for ${processedRecord.debugKey} with reason:${error}`);
                        //utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_PREPAID", 'STATUS:NO_ACTION', 'SOURCE:PS_PARSING']);
                        if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null), error, next, processedRecord);
                        return next(error,processedRecord);
                    }
                    next(null, dbRecordResp,processedRecord)

                }, processedRecord)
            },
            (dbRecordResp,processedRecord, next) => {
                if (true || dbRecordResp.length) {   // records found in plan validity table skip this step
                    return next(null, dbRecordResp, false,processedRecord);
                }
                self.getRecordsFromRecentMongo(function (error, isRecordFound) {
                    next(null, dbRecordResp, isRecordFound,processedRecord);
                }, processedRecord);
            },
            (dbRecordResp, isRecordFoundInMongo,processedRecord, next) => {
                if (isRecordFoundInMongo) {
                    return next(null,processedRecord);
                }
                self.processingOnDbRecords(function (err) {
                    if (err) return next(err,processedRecord);
                    next(null,processedRecord);
                }, dbRecordResp, processedRecord);
            },
            (processedRecord, next) => {
                if(_.toLower(_.get(processedRecord, 'operator', null)) == 'airtel'){
                    return self.markAirtelRecordsAsInactive(next, processedRecord);
                }
                else{
                    return next(null, processedRecord);
                }
            },
            (processedRecord ,next) => {
                let source = self.smsParsingBillsDwhRealtime ? "SMS_MOBILE_PREPAID_DWH_RT" :(_.get(processedRecord, 'isRuSmsParsing', false)? "SMS_MOBILE_PREPAID_REALTIME":"SMS_MOBILE_PREPAID");
                self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                    next(null,processedRecord);
                },source,self.timestamps, processedRecord.operator,processedRecord);
            }
        ], async function (error,processedRecord, is_only_ct_event_published) {
            // if (_.get(record, 'isRuSmsParsing', false)) {
            //     await self.ingestIncomingPayloads(error, processedRecord, record)
            //         .catch((err) => {
            //             if (err) {
            //                 self.L.error(`SMS_PARSING_PREPAID:PS_PARSING::ingestIncomingPayloads`, `coudlnt save record with error ${err}`);
            //             }
            //         })
            // }
            if (error && !is_only_ct_event_published) {
                self.L.log('SMS_PARSING_PREPAID:PS_PARSING:processRecords', `Exception occured Error Msg:: ${error}  debugKey::`, processedRecord.debugKey);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_PREPAID', 'STATUS:VALIDATION_FAILURE', 'SOURCE:' + processedRecord.operator, 'TYPE:' + error,`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);

            } else if(is_only_ct_event_published){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_PREPAID', 'STATUS:CT_EVENT_ONLY', 'SOURCE:' + processedRecord.operator, 'TYPE:' + error,`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
            }
            else{
                self.L.log(`SMS_PARSING_PREPAID:PS_PARSING:processRecords`, `Record processed having debug key`, processedRecord.debugKey);
            }
            return done();
        });
    }

    async checkBasicValidation(record){
        let self = this;
        if (!record) return ['invalid_record', record];
        if(_.get(record, 'isRuSmsParsing', false)==false && !record.telecom_details){
            if(self.saveForAnalyticsInCassandraAndKafka(record))await self.saveForAnalytics(record, "is not a RuSmsParsing record. record.telecom_details is not present", 1, null, null)
            return ['invalid_record', record];
        }else{
            return [null, record];
        }
    }

    setTimestamps(record){
        let self = this;
        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000;
            }
        }
        
        // set timestamps to checkk preformance delay on dashboard
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime(),
                    dwhKafkaPublishedTime = new Date(record.published_time).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
            _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
            _.set(self.timestamps, 'dwhKafkaPublishedTime', dwhKafkaPublishedTime);
        
            return record;
    }

    async validateDataExpiryMessages(record, dwhClassId, telecomDetails){
        let self = this;
        let dataConsumed =  _.get(telecomDetails, 'data_expiry_percentage', null);
        let dataConsumedAllowed = ["90%", "100%"];
        let isRealTimeDataExhausted = false;
        if(self.dataRechargeIds.includes(dwhClassId) && dataConsumed && !dataConsumedAllowed.includes(dataConsumed)){
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                'STATUS:VALIDATION_FAILURE',
                'TYPE:DATA_LESS_THAN_90',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(record)) await self.saveForAnalytics(record, "DataLess90", 1, null, null)
            return ['DataLess90', record, null, null];
        }else{
            if(dataConsumedAllowed.includes(dataConsumed)){
                isRealTimeDataExhausted = true;
            }
            return [null, record, dataConsumed, isRealTimeDataExhausted];
        }
    }

    async getDwhClassIdAndTelecomDetails(record){
        let self = this;
        let telecomDetails = _.get(record,'telecom_details',null);
        let dwhClassId = _.get(record, 'level_2_category', null);
        if(dwhClassId) dwhClassId = Number(dwhClassId); 
        return {dwhClassId, telecomDetails};
    }

    async getRemainingDaysAndExpiryDate(record, telecomDetails){
        let self = this;
        let remainingDays = (typeof _.get(telecomDetails,'remaining_days',null) === 'number') ? _.get(telecomDetails,'remaining_days',null) : (typeof _.get(telecomDetails,'remaining_days',null) === 'string' && VALIDATOR.isNumeric(_.get(telecomDetails,'remaining_days',null))) ? VALIDATOR.toInt(_.get(telecomDetails,'remaining_days',null)) : null;
        let expiry_date = _.get(telecomDetails, 'expiry_date', _.get(record,'dueDate',null));
        let expiry_time = _.get(telecomDetails, 'expiry_time', null);
        return {remainingDays, expiry_date, expiry_time};
    }

    async getValidityDate(record, dwhClassId, telecomDetails){
        let self = this;
        let validityDate;
        // let isDwhSmsParsingManual;
        let is_wo_validity= false;

        let {remainingDays, expiry_date, expiry_time} = await self.getRemainingDaysAndExpiryDate(record, telecomDetails);


        if (expiry_date) {
            if (MOMENT(expiry_date, 'YYYY-MM-DD', true).isValid()) {   //  2022-08-31
                validityDate = MOMENT(expiry_date, 'YYYY-MM-DD');
            }

            else if (MOMENT(expiry_date, 'DD-MM-YYYY', true).isValid()) {      // 20-02-2022   2022-08-31
                validityDate = MOMENT(expiry_date, 'DD-MM-YYYY');
            }
            else if (MOMENT(expiry_date, 'DD-MMM-YYYY', true).isValid()) {    //20-feb-2022
                validityDate = MOMENT(expiry_date, 'DD-MMM-YYYY');
            }
        }
        //  else if (this.validityExpiryIds.includes(dwhClassId)) {
        //     let operator = _.toLower((_.get(record, 'isRuSmsParsing', false)==false)?_.get(telecomDetails,'operator', _.get(record, 'operator', _.get(record, 'smsOperator',null))):_.get(record,'operator',''));
        //     let isPartialToFullConfig = true;
        //     let customerId = _.get(record, 'cId', null);

        //     if(self.OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG[operator] && customerId%100 >= self.OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG[operator]){
        //         isPartialToFullConfig = false;
        //     }

        //     self.L.info("validateAndProcessRecord::operator", operator, "isPartialToFullConfig", isPartialToFullConfig, "customerId", customerId, "OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG", self.OPERATOR_PARTIAL_TO_FULL_BILL_CONFIG[operator])
        //     if(dwhClassId==6 && self.OPERATOR_DEFAULT_EXPIRED_DAYS[operator] && isPartialToFullConfig){
        //         validityDate = MOMENT(Number(record.smsDateTime)).startOf('day');
        //         validityDate.subtract(self.OPERATOR_DEFAULT_EXPIRED_DAYS[operator], 'days');
        //         isDwhSmsParsingManual = true;
        //         is_wo_validity = false;
        //     }else if(dwhClassId==5 && self.OPERATOR_DEFAULT_EXPIRING_DAYS[operator] && isPartialToFullConfig){
        //         validityDate = MOMENT(Number(record.smsDateTime));
        //         validityDate.add(self.OPERATOR_DEFAULT_EXPIRING_DAYS[operator], 'days');
        //         isDwhSmsParsingManual = true;
        //         is_wo_validity = false;
        //     }
        //     if(isDwhSmsParsingManual) {
        //         utility._sendMetricsToDD(1, [
        //             'REQUEST_TYPE:SMS_PARSING_PREPAID_MANUAL_DATE_EVENT',
        //             'STATUS:RECEIVED_EVENT',
        //             'TYPE:TOTAL_EVENT_COUNT',
        //             'DWH_CLASS_ID:'+dwhClassId,
        //             `OPERATOR:${operator}`,
        //             `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
        //         ])
        //     }
        // }
        else if (remainingDays && record.smsDateTime) {
            validityDate = MOMENT(record.smsDateTime);
            validityDate.add(remainingDays, 'days');
        }else if(self.dataRechargeIds.includes(dwhClassId) && record.smsDateTime){
            validityDate = MOMENT(record.smsDateTime);
        }else{
            validityDate = null;
            is_wo_validity = true;
        }

        validityDate = self.setValidityDateWithExpiryTime(validityDate, expiry_time, record);
        return {validityDate, is_wo_validity};
        }

    setValidityDateWithExpiryTime(validityDate, expiry_time, record){
        let self = this;
        try{
            let telecomDetails = _.get(record, 'telecom_details', null);
            let operator = _.toLower(_.get(telecomDetails,'operator', _.get(record, 'operator', _.get(record, 'smsOperator',null))));
            if(validityDate && expiry_time && operator && operator == 'jio'){
                let expiry_time_moment = MOMENT(expiry_time, 'HH:mm:ss');
                if(expiry_time_moment.isValid()){
                    // Extract hours and minutes for time comparison
                    let hours = expiry_time_moment.hours();
                    let minutes = expiry_time_moment.minutes();
                    let totalMinutes = hours * 60 + minutes;
                    
                    // Check if time is between 00:00:00 (0 minutes) and 06:00:00 (360 minutes)
                    if(totalMinutes >= 0 && totalMinutes <= 360){
                        validityDate = validityDate.subtract(1, 'days');
                    }
                }
            }
            return validityDate;
        }catch(e){
            self.L.error('setValidityDateWithExpiryTime::Error setting validity date with expiry time::Err:Msg', e);
            return validityDate;
        }
    }

    async processValidityDate(record, dwhClassId, telecomDetails, validityDate, isRealTimeDataExhausted, is_wo_validity){
        let self = this;
        if (!validityDate) {
            if(!isRealTimeDataExhausted && (!telecomDetails.telecom_model_version || self.dataRechargeIds.includes(dwhClassId))) { 
                if(self.saveForAnalyticsInCassandraAndKafka(record))await self.saveForAnalytics(record, "ValidityNotFound", 1, null, null)
                return ['ValidityNotFound', record, null]
            };
            is_wo_validity = true;
        }
        return [null, record, is_wo_validity];
    }

    async processWithoutValidityDate(record, dwhClassId, telecomDetails, validityDate){
        let self = this;
        if(validityDate){
            validityDate = validityDate.endOf('day');

            let t_time = MOMENT();


            if (t_time.unix() > validityDate.unix()) {
                if(!telecomDetails.telecom_model_version || self.dataRechargeIds.includes(dwhClassId)) {
                    if(self.saveForAnalyticsInCassandraAndKafka(record))await self.saveForAnalytics(record, "ValidityMoreThan_1_year", 1, null, null)
                    return ['ValidityAlreadyExpired', record, null];
                }
            }

            t_time.add(1, 'years');  // adding 1 years so if validity greater than 1 year ignore packet

            if (validityDate.unix() > t_time.unix()){
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                    'STATUS:VALIDATION_FAILURE',
                    'TYPE:VALIDATION_MORE_THAN_1_YEAR',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(record))await self.saveForAnalytics(record, "ValidityMoreThan_1_year", 1, null, null)
                return ['ValidityMoreThan_1_year', record, null];
            } 


            if (dwhClassId == 8 &&
                validityDate.diff(MOMENT(Number(record.smsDateTime)), 'days') <= self.dwhClassId8DueDays) {

                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_PREPAID',
                    'STATUS:VALIDATION_FAILURE',
                    'TYPE:INVALID_DUE_DATE_GENERATED_DWH_CLASS_ID_8',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                if (self.saveForAnalyticsInCassandraAndKafka(record)) await self.saveForAnalytics(record, "INVALID_DUE_DATE_GENERATED_DWH_CLASS_ID_8", 1, null, null)
                return ['INVALID_DUE_DATE_GENERATED_DWH_CLASS_ID_8', record, null];
            }
        }
        return [null, record, validityDate];
        }

    getRechargeNumber(record, telecomDetails){
        let self = this;
        let RN = _.get(telecomDetails, 'mobile_number', null);
        if(!RN || RN == 'null'){
            RN =  _.get(record, 'smsReceiver', '');
        }

        let rech_num = (typeof RN === 'number') ? RN : (typeof RN === 'string' && VALIDATOR.isNumeric(RN)) ? VALIDATOR.toInt(RN) : null;
            if (rech_num) {
                rech_num = rech_num.toString();
                if (rech_num.length > 10) {
                    rech_num = rech_num.slice(-10);
                }
            }
        return rech_num;
        }
        
    getProcessedRecord(record, dwhClassId, telecomDetails, rech_num, validityDate, is_wo_validity, isRealTimeDataExhausted, dataConsumed, partialBillState, dateFormat){
        let self = this;
        let processedRecord = {
            customerId: (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
            rechargeNumber: rech_num? rech_num.toString():null,
            planAmount: self.parseAmount(_.get(telecomDetails, 'plan_amount', _.get(record, 'amount',null))),
            validityDate: !is_wo_validity ? MOMENT(validityDate, dateFormat) : null,
            operator: _.toLower((_.get(record, 'isRuSmsParsing', false)==false)?_.get(telecomDetails,'operator', _.get(record, 'operator', _.get(record, 'smsOperator',null))):_.get(record,'operator','')),
            service: 'mobile',
            paytype: 'prepaid',
            isDefaultAmount: false,
            msgId : _.get(record, 'msg_id', ''),
            rtspClassId: _.get(record, 'rtspClassId', null),
            rtspClassName: _.get(record, 'rtspClassName', null),
            sender_id: _.get(record, 'smsSenderID', null),
            sms_id : _.get(record, 'msg_id', null),
            sms_date_time : _.get(record, 'smsDateTime', null),
            dwh_classId : dwhClassId,
            is_wo_validity : is_wo_validity,
            is_wo_rech_num : rech_num ? false: true,
            dataConsumed: dataConsumed,
            smsDateTime: _.get(record, 'smsDateTime', null),
            dwhKafkaPublishedTime: _.get(record, 'published_time', null),
            is_notified_at_fe: _.get(record, 'notificationShown', false) && isRealTimeDataExhausted,
        }
        return processedRecord;
    }

    // Private helper methods
async _getBaseProcessedRecord(record, dwhClassId, telecomDetails, rech_num, validityDate, isRealTimeDataExhausted, dataConsumed, dateFormat, partialBillState) {
    return {
        customerId: (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
        rechargeNumber: rech_num ? rech_num.toString() : null,
        planAmount: this.parseAmount(_.get(telecomDetails, 'plan_amount', _.get(record, 'amount', null))),
        validityDate: MOMENT(validityDate, dateFormat).isValid() ? MOMENT(validityDate, dateFormat) : null,
        operator: _.toLower((_.get(record, 'isRuSmsParsing', false) == false) ? _.get(telecomDetails, 'operator', _.get(record, 'operator', _.get(record, 'smsOperator', null))) : _.get(record, 'operator', '')),
        service: 'mobile',
        paytype: 'prepaid',
        isDefaultAmount: false,
        msgId: _.get(record, 'msg_id', ''),
        rtspClassId: _.get(record, 'rtspClassId', null),
        rtspClassName: _.get(record, 'rtspClassName', null),
        sender_id: _.get(record, 'smsSenderID', null),
        sms_id: _.get(record, 'msg_id', null),
        sms_date_time: _.get(record, 'smsDateTime', null),
        dwh_classId: dwhClassId,
        is_wo_rech_num: rech_num ? false : true,
        dataConsumed: dataConsumed,
        smsDateTime: _.get(record, 'smsDateTime', null),
        dwhKafkaPublishedTime: _.get(record, 'published_time', null),
        is_notified_at_fe: _.get(record, 'notificationShown', false) && isRealTimeDataExhausted,
        partialBillState: partialBillState,
    };
}

    async _processDataExpiryDetails(processedRecord, record, dataConsumed, isRealTimeDataExhausted) {
        let self = this;
        
        if (!isRealTimeDataExhausted) {
            self.L.info("Record received is not DataExhausted event for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
            return processedRecord;
        }

        if (record.smsDateTime) {
            let smsDateTimeDiffFromNow = Date.now() - record.smsDateTime;
                if (smsDateTimeDiffFromNow > (1500 * 60 * 1000)) {
                    _.set(processedRecord, "isRealTimeDataExhausted", false);
                    self.L.info("Record is older than 1500 mins for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID_DATA_EXHAUST',
                        'STATUS:IGNORED',
                        'TYPE:FIFTEEN_HUNDRED_MINUTES_OLD',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                    ]);
            } else {
                    self.L.info("Record is valid data exhaust payload for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
                        _.set(processedRecord, "exhausted_data", dataConsumed);
                        _.set(processedRecord, "exhausted_date", MOMENT().format('YYYY-MM-DD HH:mm:ss'));
                        _.set(processedRecord, "isRealTimeDataExhausted", true);
                        _.set(processedRecord, 'time_interval', self.notificationTimeOut);
                }
            }

        // Process grey scale config
            let GREY_CONFIG = Number(_.get(this.grey_config, ['MOBILE_DATA_EXHAUST', "PERCENTAGE"], null));
            self.L.info("GRAY SCALE CONFIG PICKED: ", GREY_CONFIG, " PERCENTAGE for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);

            let customerId = processedRecord.customerId;
            if (GREY_CONFIG && customerId % 100 >= GREY_CONFIG) {
                _.set(processedRecord, 'isRealTimeDataExhausted', false);
                self.L.info("Record is not under grey range custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
            }

            if (_.get(processedRecord, "isRealTimeDataExhausted")) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_PREPAID_DATA_EXHAUST',
                    'STATUS:RECEIVED_EVENT',
                    'TYPE:COUNT',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
            }

        return processedRecord;
    }

    async _setProcessedRecordFlags(processedRecord, record, dwhClassId, telecomDetails) {
        let self = this;

        // Set manual parsing flag
        // if (isDwhSmsParsingManual) {
        //     _.set(processedRecord, 'isDwhSmsParsingManual', true);
        // }

        // Set DWH class for version 2.0 parser
        if (telecomDetails && telecomDetails.telecom_model_version) {
            processedRecord.dwhClassId = dwhClassId;
        }

        // Set parsing type flags
        if (_.get(record, 'isRuSmsParsing', false) == true) {
            _.set(processedRecord, 'isRuSmsParsing', true);
        } else if (self.smsParsingBillsDwhRealtime == true) {
            _.set(processedRecord, 'isDwhSmsParsingRealtime', true);
        }

        // // Process partial bill grey scale config
        // let partailBillGreyPercentage = Number(_.get(this.grey_config, ['PARTIAL_BILL', "PERCENTAGE"], null));
        // self.L.info("GRAY SCALE CONFIG PICKED FOR PARTIAL BILL: ", partailBillGreyPercentage, " PERCENTAGE for custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);

        // let customerId = processedRecord.customerId;
        // if (_.get(this.grey_config, ['PARTIAL_BILL', "PERCENTAGE"], null) != null && customerId % 100 >= partailBillGreyPercentage) {
        //     self.L.info("Record is not under grey range custId: ", processedRecord.customerId, " Recharge Number: ", processedRecord.rechargeNumber);
        // } else if (partialBillState) {
        //     _.set(processedRecord, "partialBillState", partialBillState);
        // }

        // Check SMS datetime
        processedRecord = self.checkIfSmsDateTimeIsOlderThanToday(processedRecord);

        return processedRecord;
    }

    checkIfSmsDateTimeIsOlderThanToday(processedRecord){
        let self = this;
        if(processedRecord.smsDateTime){
            let smsDateTime = MOMENT(Number(processedRecord.smsDateTime));
            let todayStartOfDay = MOMENT().startOf('day');
            if(smsDateTime.isBefore(todayStartOfDay)){
                processedRecord.isSmsDateTimeOlderThanToday = true;
            }
            self.L.log(`checkIfSmsDateTimeIsOlderThanToday:: smsDateTime: ${smsDateTime}, todayStartOfDay: ${todayStartOfDay} for recharge number : ${processedRecord.rechargeNumber} and customerId : ${processedRecord.customerId}`);
        }
        return processedRecord;
    }

    sendMetrics(processedRecord, dwhClassId, record){
        let self = this;
        if(_.get(processedRecord, "isDwhSmsParsingManual")) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_PREPAID_MANUAL_DATE_EVENT',
                'STATUS:RECEIVED_EVENT',
                'TYPE:VALID_EVENT_COUNT',
                'DWH_CLASS_ID:'+dwhClassId,
                `OPERATOR:${_.get(processedRecord, "operator", "NO_OPERATOR")}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ])
        }

        if (_.get(processedRecord, "partialBillState")) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_PREPAID_PARTIAL_BILL',
                'STATUS:RECEIVED_EVENT',
                'TYPE:COUNT',
                'DWH_CLASS_ID:'+dwhClassId,
                `OPERATOR:${_.get(processedRecord, "operator", "NO_OPERATOR")}`,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
        }
    }

    async mandatoryParamsValidation(processedRecord, dwhClassId, record){
        let self = this;
       let mandatoryParams = ['customerId', 'operator'];
      
       if(!self.validityExpiryIds.includes(dwhClassId)){
            mandatoryParams.push('rechargeNumber');
       }

        let invalidParams = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) invalidParams.push(key);
        });

        processedRecord.debugKey = `operator:${processedRecord.operator}_custId:${processedRecord.customerId}_rechargeNo:${processedRecord.rechargeNumber}`;

        if (invalidParams.length > 0) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                'STATUS:VALIDATION_FAILURE',
                'TYPE:MANDATORY_PARAMS_MISSING',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) await self.saveForAnalytics(processedRecord, `Mandatory Params ${invalidParams} is Missing / Invalid`, 2, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null)
            return [`Mandatory Params ${invalidParams} is Missing / Invalid`, record];
        }
        return [null, record];
        }

    async operatorValidation(processedRecord, record){
        let self = this;
        processedRecord.operator = processedRecord.operator.toLowerCase();
        if (VI_GROUP.indexOf(processedRecord.operator) >= 0) {
            processedRecord.operator = 'vodafone idea';
        }

        if (self.allowed_operators && self.allowed_operators.indexOf(processedRecord.operator) < 0) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                'STATUS:VALIDATION_FAILURE',
                'TYPE:OPERATOR_NOT_ALLOWED',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))await self.saveForAnalytics(processedRecord, `OperatorNotAllowed`, 2, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", null)
            return [`OperatorNotAllowed`, processedRecord];
        }
       
        if(processedRecord.operator){
            let amt = processedRecord['planAmount'];

            let is_float =  amt === +amt && amt !== (amt|0);

            if(!amt || is_float || amt < self.minAmount  || amt > self.maxAmount ){
                self.L.log('Invalid amount ', amt);
                processedRecord['planAmount'] = OPERATOR_DEFAULT_AMOUNT[processedRecord.operator];
                processedRecord['isDefaultAmount'] = true;
                processedRecord.plan_bucket = PLAN_BUCKET;
            }
        }
        return [null, processedRecord];
    }

    async processRecordDetails(record, dwhClassId, telecomDetails, validityDate, isRealTimeDataExhausted, dataConsumed, dateFormat, is_wo_validity) {
        let self = this;
        try {
            let partialBillState = null;

            let rech_num = self.getRechargeNumber(record, telecomDetails);

            if(is_wo_validity && self.partialBillIds.includes(dwhClassId)){
                partialBillState = self.PARTIAL_BILL_RECO_MAP[dwhClassId];
            }


            // Step 1: Get base processed record
            let processedRecord = await self._getBaseProcessedRecord(record, dwhClassId, telecomDetails, rech_num, validityDate, isRealTimeDataExhausted, dataConsumed, dateFormat, partialBillState);
    
            // Step 2: Process data expiry messages
            processedRecord = await self._processDataExpiryDetails(processedRecord, record, dataConsumed, isRealTimeDataExhausted);
    
            // Step 3: Set all required flags
            processedRecord = await self._setProcessedRecordFlags(processedRecord, record, dwhClassId, telecomDetails);

            _.set(processedRecord, 'is_wo_validity', is_wo_validity);
    
            return processedRecord;
        } catch (error) {
            self.L.error('processRecordDetails', 'Error processing record details:', error);
            throw error;
        }
    }

    async validateValidityDates(record, dwhClassId, telecomDetails, validityDate, isRealTimeDataExhausted, is_wo_validity) {
        let self = this;
        try {
            // First validate basic validity date
            let [error1, updatedRecord, is_wo_validity] = await self.processValidityDate(
                record, 
                dwhClassId, 
                telecomDetails, 
                validityDate, 
                isRealTimeDataExhausted,
                is_wo_validity
            );
            if (error1) return [error1, record, null, is_wo_validity];
    
            // Then process additional validity date checks
            let [error2, finalRecord, finalValidityDate] = await self.processWithoutValidityDate(
                updatedRecord,
                dwhClassId, 
                telecomDetails,
                validityDate,
            );
            if (error2) return [error2, finalRecord, null, is_wo_validity];
    
            return [null, finalRecord, finalValidityDate, is_wo_validity];
        } catch (error) {
            self.L.error('validateValidityDates', 'Error validating validity dates:', error);
            return ['validity_date_validation_error', record, null, is_wo_validity];
        }
    }
    

    async validateAndProcessRecord(record) {
        let self = this;
        try{
            let dateFormat = 'YYYY-MM-DD HH:mm:ss',
                isRealTimeDataExhausted = false,
                invalidRecord = null,
                invalidParams = null,
                invalidValidityDate = null,
                invalidDataExpiry = null,
                dataConsumed = null;

            [invalidRecord, record] = await self.checkBasicValidation(record);
            if(invalidRecord) return [invalidRecord, record];

            record = self.setTimestamps(record);

            let {dwhClassId, telecomDetails} = await self.getDwhClassIdAndTelecomDetails(record);
            
            [invalidDataExpiry, record, dataConsumed, isRealTimeDataExhausted] = await self.validateDataExpiryMessages(record, dwhClassId, telecomDetails);
            if(invalidDataExpiry) return [invalidDataExpiry, record];

            let {validityDate, is_wo_validity} = await self.getValidityDate(record, dwhClassId, telecomDetails);

            self.L.log("validateAndProcessRecord:: validityDate: ", validityDate, " is_wo_validity: ", is_wo_validity);
            
            [invalidValidityDate, record, validityDate, is_wo_validity] = await self.validateValidityDates(record, dwhClassId, telecomDetails, validityDate, isRealTimeDataExhausted, is_wo_validity);
            if(invalidValidityDate) return [invalidValidityDate, record];

            let processedRecord = await self.processRecordDetails(record, dwhClassId, telecomDetails, validityDate, isRealTimeDataExhausted, dataConsumed, dateFormat, is_wo_validity);

            [invalidParams, processedRecord] = await self.validateRecord(processedRecord, dwhClassId, record);
            if(invalidParams) return [invalidParams, record];

            self.sendMetrics(processedRecord, dwhClassId, record);

        return [null, processedRecord];
        }catch(error){
            self.L.error('validateAndProcessRecord', 'Error in validating and processing record', error);
            return ['invalid_record', record];
        }
    }

    async validateRecord(processedRecord, dwhClassId, record) {
        let self = this;
        try {
            // Call mandatory params validation first
            let [mandatoryError, validatedRecord] = await self.mandatoryParamsValidation(processedRecord, dwhClassId, record);
            if(mandatoryError) return [mandatoryError, validatedRecord];
    
            // Then call operator validation
            let [operatorError, validatedProcessedRecord] = await self.operatorValidation(processedRecord, record);
            if(operatorError) return [operatorError, validatedProcessedRecord];
    
            return [null, validatedProcessedRecord];
        } catch(error) {
            self.L.error('validateRecord', 'Error in validation wrapper:', error);
            return ['validation_error', record];
        }
    }

    async get_plan_details_of_record(cb, operator, amount, service, circle) {
        let self=this;
        if (self.amount_plan_bucket_map[operator + '_' + circle + '_' + amount]) {
            return cb(null, self.amount_plan_bucket_map[operator + '_' + circle + '_' + amount])
        }
        self.L.verbose("get_plan_details_of_record:: getting plan details for operator :"+operator+" amount :"+amount+" circle :"+circle);
        try {
            let categoryId = this.config.DIGITAL_CATALOG.DCAT_CATEGORY_MAP[service];
            //console.log("get_plan_details_of_record input  ", operator, amount, service, circle)
            let { data: planData, status: planApiStatus } = await this.digitalCatalog.getPlanDetail({ categoryId, operator, circle, amount });
            if (!planData || !planData.plan_bucket) return cb("plan bucket not found");
            self.amount_plan_bucket_map[operator + '_' + circle + '_' + amount] = planData.plan_bucket;
            cb(null, planData.plan_bucket); 
        } catch (error) {
            this.L.error(" get_plan_details_of_record error:", error);
            cb(error);
        }
    }

    get_productId_of_record(cb, circle, operator) {
        //  let categoryId = this.config.DIGITAL_CATALOG.DCAT_CATEGORY_MAP[service];
        let self = this;
        self.L.verbose("get_productId_of_record:: getting product_id for operator :"+operator+" circle :"+circle);
        if (self.operator_circle_productId_map[operator + '_' + circle]) {
            return cb(null, self.operator_circle_productId_map[operator + '_' + circle])
        }
        self.digitalCatalog.get_product_list(function (e, res) {
            if (e || !res || !res.products || !res.products.length) return cb(e || "No response for ProductId");
            let product = res.products[0];
            self.operator_circle_productId_map[operator + '_' + circle] = product.productId;
            return cb(null, product.productId);
        }, operator, circle);
    }
    
    get_circle_of_record(cb, operator, recharge_number) {
        let self = this;
        self.L.verbose("get_circle_of_record:: getting circle for operator :"+operator+" recharge_number :"+recharge_number);

        if (operator == 'airtel' || operator == 'jio') {
            return cb(null, 'All Circles');
        }
        if(!recharge_number) return cb("No recharge number");
        self.digitalCatalog.get_circle_from_mnp(function (e, res) {

            try {
                res = res && JSON.parse(res);
            } catch (ex) {
                if(operator == 'vodafone idea'){
                    return cb(null, 'All Circles');
                }
                else 
                    return cb("No Circle Found");
            }

            if (e || !res || !res.Circle) {
                if(operator == 'vodafone idea'){
                    return cb(null, 'All Circles');
                }
                else
                    return cb("No Circle Found");
            }
            /**
             *  Removed opertor mismatch condition and allowed circle conditions as per IN-49119 
             */
            let mnp_operator = res.Operator;
            mnp_operator = mnp_operator.toLowerCase();
            if (operator != mnp_operator) {
                self.L.log("Opertor mismatched from MNP");
                return cb(null, 'All Circles');
            }
            // if (operator == 'vodafone idea' && self.allowed_circles_for_vodaphone_idea && self.allowed_circles_for_vodaphone_idea.indexOf(res.Circle) < 0) {
            //     self.L.log('This circle is not Allowed' + res.Circle);
            //     return cb(null, 'All Circles');
            // }
            cb(null, res.Circle);
        }, recharge_number);
    }

    parseAmount(amountStr) {  
        if (!amountStr) return null;
        if (typeof amountStr === 'number') return amountStr;
        if (typeof amountStr === 'string' && VALIDATOR.isNumeric(amountStr)) return VALIDATOR.toFloat(amountStr);
        // case of "Rs.x.y" i.e. "Rs.101.54"
        let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
        if(parsedAmount) return parsedAmount;
        //case of "x.y" i.e. "101.54"
        let foundMatch2 = amountStr.match(new RegExp(/([-+]?\d*(?:\.\d*)?)/));
        let parsedAmount2 = (foundMatch2 && foundMatch2[1]) ? VALIDATOR.toFloat(foundMatch2[1]) : null;
        return parsedAmount2;
    }

    getRecordsFromPlanValidity(done, processedRecord) {
        let self = this,
            plan_bucket = PLAN_BUCKET,
            query = 'SELECT * FROM plan_validity WHERE recharge_number = ? and operator = ? and plan_bucket = ?',
            queryParams = [processedRecord.rechargeNumber, processedRecord.operator, plan_bucket];

        self.L.verbose('getRecordsFromPlanValidity', self.dbInstance.format(query, queryParams));
        self.L.log('getRecordsFromPlanValidity', 'Fetching getRecordsFromPlanValidity from plan_validity...');

        self.dbInstance.exec(function (err, result) {
            if (err && err.code && err.code == 'ER_LOCK_DEADLOCK') {
                // Retrying query again in case of deadlock
                self.L.error('getValidity', 'Deadlock detected...for', queryParams);
                self.dbInstance.exec(function (err, result) {
                    if (err) {
                        self.L.critical("getRecordsFromPlanValidity-retry", "Error occurred while fetching record ", err);
                        return done(err);
                    } else {
                        self.L.log("getRecordsFromPlanValidity-retry", "Data fetched...");
                        self.L.verbose("getRecordsFromPlanValidity :: got db records", JSON.stringify(result));
                        return done(null, result);
                    }
                }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);
            }
            else if (err) {
                self.L.error("getRecordsFromPlanValidity", "Error occurred while fetching record ", err);
                return done(err);
            } else {
                self.L.log("getRecordsFromPlanValidity", "Data fetched...", JSON.stringify(result));
                return done(null, result);
            }
        }, 'RECHARGE_ANALYTICS_SLAVE', query, queryParams);

    }

    async getRecordsFromRecentMongo(done, processedRecord) {
        let queryObj = {
            query: {
                'recharge_number': processedRecord.rechargeNumber,
                'customer_id': processedRecord.customerId,
                'operator': processedRecord.operator
            },
            limit: 1
        }
        // console.log("mongo query ", queryObj);
        let data = await this.parent.mongoThrottleWapper(queryObj);
      //  console.log("mongo dtaaa ", data);
        if (data && data.length) {
            let order_id = null;
            let mongoRecord = data[0];
           // console.log("Mongo record ", mongoRecord);
            order_id = mongoRecord.order_id
          
            if(!order_id) return done();
            this.create_plan_validity_publisher_data(processedRecord, order_id);
            done(null, true);
        } else {
            done()
        }
    }

    updateCircleAndProductId(processedRecord, record) {
        let self = this;
                if(_.get(record, "customer_id") == _.get(processedRecord, "customerId")){
                    if(_.get(processedRecord, "circle", "null").toLowerCase() != _.get(record, "circle", "null").toLowerCase() && _.get(record, "product_id") && _.get(record, "circle")){
                        if (_.get(processedRecord, "circle", "null") != _.get(record, "circle", "null")) {
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_PREPAID',
                                'STATUS:PRODUCT_ID_UPDATE',
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                            ]);
                        }
                        self.L.info('Circle is updated from ', _.get(processedRecord, "circle"), ' to ', _.get(record, "circle"), processedRecord.debugKey);
                        processedRecord.productId = _.get(record, "product_id");
                        processedRecord.circle = _.get(record, "circle");
                    }
        }
        return processedRecord;
                }

    checkUpdateConditionForManualParsing(processedRecord, record, isPlanValidityUpdate) {
        let self = this;
                if(record_date.isAfter(processedRecord.validityDate, 'day') && processedRecord.dwhClassId == 5) isPlanValidityUpdate = true;
                
                if(_.get(processedRecord, 'isDwhSmsParsingManual', false) == true){
                    let calculatedDate = MOMENT(Number(processedRecord.smsDateTime)).startOf('day');
                    calculatedDate.subtract(self.OPERATOR_DEFAULT_DIFF_DAYS[processedRecord.operator], 'days');
                    if(record_date.isAfter(calculatedDate, 'day')) {
                        self.L.log(" processing on DB Records isAfter | Expiry date from plan validity table : "+record.validity_expiry_date+" is after calculated date : "+calculatedDate.format('YYYY-MM-DD')+" for operator : "+processedRecord.operator+" and recharge number : "+processedRecord.rechargeNumber+" and sms date : "+MOMENT(Number(processedRecord.smsDateTime)).format('YYYY-MM-DD')+" and smsDateTime : "+processedRecord.smsDateTime+" and smsDateTime : "+processedRecord.smsDateTime);
                        isPlanValidityUpdate = false;
                    } else {
                        self.L.log(" processing on DB Records Before case | Expiry date from plan validity table : "+record.validity_expiry_date+" is after calculated date : "+calculatedDate.format('YYYY-MM-DD')+" for operator : "+processedRecord.operator+" and recharge number : "+processedRecord.rechargeNumber+" and sms date : "+MOMENT(Number(processedRecord.smsDateTime)).format('YYYY-MM-DD')+" and smsDateTime : "+processedRecord.smsDateTime+" and smsDateTime : "+processedRecord.smsDateTime);

                        isPlanValidityUpdate = true;
                    }
                } 
        return isPlanValidityUpdate;
    }

    setMaxValidityBetweenAirtelPrepaidAndSmsRecord(processedRecord, record, isPlanValidityUpdate, isDefaultAmount) {
        let self = this;
        try {
            let db_amount = _.get(record, 'amount', null);
            // Early return if SMS is older than today
            if (_.get(processedRecord, 'isSmsDateTimeOlderThanToday', false)) {
                _.set(processedRecord, 'validityDate', null);
                _.set(processedRecord, 'planAmount', null);
                return [processedRecord, isPlanValidityUpdate];
            }else{
    
                const currentDate = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss');
                
                // Helper function to validate and format date
                const getValidFutureDate = (date) => {
                    if (!date) return null;
                    const momentDate = MOMENT(date).isValid() ? 
                        MOMENT(date).format('YYYY-MM-DD HH:mm:ss') : null;
                    return momentDate && MOMENT(momentDate).diff(MOMENT(currentDate), 'day') >= 0 ? 
                        momentDate : null;
                };
        
                // Get all valid dates
                const dates = {
                    sms: getValidFutureDate(processedRecord.validityDate),
                    db: getValidFutureDate(record.validity_expiry_date), //date coming from db in TZ format example 2025-05-16T00:00:00.000Z
                    airtel: getValidFutureDate(_.get(processedRecord, 'airtelPrepaidPublisherDetails.max_due_date'))
                };
        
                // Find max date from valid dates
                const validDates = Object.values(dates).filter(Boolean);
                const maxDueDate = validDates.length ? 
                    MOMENT(Math.max(...validDates.map(d => MOMENT(d)))).format('YYYY-MM-DD HH:mm:ss') : 
                    null;

                
                /*
                we have calculated maxDueDate from sms, airtel and db.

                if amount coming is default amount -> then we need to use validityDate and amount from db.
                if amount coming is not default amount -> then we need to use maxDueDate from sms, airtel and db.
                */

                let pvUpdate = false;

                if(isDefaultAmount){
                    if(dates.db){
                    _.set(processedRecord, 'validityDate', MOMENT(dates.db));
                    }else{
                        _.set(processedRecord, 'validityDate', null);
                    }
                    _.set(processedRecord, 'planAmount', db_amount);
                }
                else{
                    _.set(processedRecord, 'validityDate', MOMENT(maxDueDate));
                    pvUpdate = true;
                }

                _.set(processedRecord, 'partialBillState', processedRecord.validityDate ? null : processedRecord.partialBillState);

                if(pvUpdate && maxDueDate){
                    if(!dates.db){
                        isPlanValidityUpdate = true;
                    }
                    else{
                        const maxFormatted = MOMENT(processedRecord.validityDate).format('YYYY-MM-DD HH:mm:ss');
                        const dbFormatted = MOMENT(dates.db).format('YYYY-MM-DD HH:mm:ss');
                        isPlanValidityUpdate = MOMENT(maxFormatted).isAfter(MOMENT(dbFormatted));
                    }
                }
            
                // Log results
                self.L.log('setMaxValidityBetweenAirtelPrepaidAndSmsRecord', {
                    smsDueDate: dates.sms,
                    dbDueDate: dates.db,
                    airtelPublisherDueDate: dates.airtel,
                    maxDueDate,
                    isPlanValidityUpdate
                });
        
                return [processedRecord, isPlanValidityUpdate];
        }
    
        } catch (err) {
            self.L.error('setMaxValidityBetweenAirtelPrepaidAndSmsRecord', err);
            return [processedRecord, isPlanValidityUpdate];
        }
    }

    setFlagsForRecordUpdationInPv(records, processedRecord,isRecordExist,isSameCustomer,isPlanValidityUpdate,ids, isDefaultAmount){
        let self = this;
        if (records && _.isArray(records) && records.length > 0) {
            isRecordExist = true;
            records.forEach(function (record) {
                processedRecord.plan = record.category_name;    // for update plan in MongoDB (recent api)
                
                processedRecord = self.updateCircleAndProductId(processedRecord, record);

                [processedRecord, isPlanValidityUpdate] = self.setMaxValidityBetweenAirtelPrepaidAndSmsRecord(processedRecord, record, isPlanValidityUpdate, isDefaultAmount);
                console.log("*********** ******** processedRecord :", processedRecord);

                ids.push(record.id);
                if (record.customer_id == processedRecord.customerId){ 
                    isSameCustomer = true;
                    processedRecord.custIdExistInPv = record.customer_id;
                }else{
                    processedRecord.recordCustomerId = record.customer_id
                }
                // if (record.operator == processedRecord.operator) isSameOperator = true;
            });
        }
        return [isRecordExist, isSameCustomer, isPlanValidityUpdate, ids, processedRecord];
    }

    checkIfUserExistsPVWithOldSmsDateTime(processedRecord){
        let self = this;
        if(_.get(processedRecord, 'isSmsDateTimeOlderThanToday', false)){
            _.set(processedRecord, 'validityDate', null);
            _.set(processedRecord, 'planAmount', null);
            _.set(processedRecord, 'partialBillState', null);
            return processedRecord;
        }
        return processedRecord;
    }

    processingOnDbRecords(done, records, processedRecord) {
        let self = this,
            isSameCustomer = false,
            // isSameOperator = true,
            isPlanValidityUpdate = false,
            ids = [],
            isRecordExist = false;

        processedRecord = self.checkIfUserExistsPVWithOldSmsDateTime(processedRecord);
        
        [isRecordExist, isSameCustomer, isPlanValidityUpdate, ids, processedRecord] = self.setFlagsForRecordUpdationInPv(records, processedRecord,isRecordExist,isSameCustomer,isPlanValidityUpdate,ids, _.get(processedRecord, 'isDefaultAmount', false));

        _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
        ASYNC.parallel([
            async (next) => {
                if(isRecordExist && _.get(processedRecord, 'isRealTimeDataExhausted', false) && !_.get(processedRecord, 'isSmsDateTimeOlderThanToday', false)){
                    self.L.log("processingOnDbRecords::ASYNC.parallel  isRealTimeDataExhausted for processedRecord :" + processedRecord);
                    self.updatePlanValidityForDataExhaust(next, records,processedRecord, ids); 
                }else{
                self.L.log("processingOnDbRecords:: isRecordExist :"+isRecordExist+" isPlanValidityUpdate :"+isPlanValidityUpdate+" isDefaultAmount :"+_.get(processedRecord,'isDefaultAmount',null));
                    if (!isRecordExist || !isPlanValidityUpdate || processedRecord.isDefaultAmount || _.isNull(processedRecord.validityDate)) return next();

               self.L.log("processingOnDbRecords::ASYNC.parallel  updatePlanValidty");
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                    'STATUS:PV_UPDATE', 
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                ]);
                _.set(processedRecord, 'ruOnboarded', true);
                self.updatePlanValidty(next, records,processedRecord, ids); // this update in mongo also
                }
            },

            async (next) => {
                if(processedRecord.isRealTimeDataExhausted && !processedRecord.isSmsDateTimeOlderThanToday){
                    let ct_data = self.create_ct_event_payload(processedRecord);
                    return self.publishCtEvents((err, data)=>{
                        return next(err);
                    }, ct_data,processedRecord);
                }
                else{
                    return next();
                }
            },
                
            async (next) => {
                if (isSameCustomer){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STATUS:SAME_CUSTOMER_EXIST', 
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`,
                        `PARTIAL_BILL:${_.get(processedRecord, 'partialBillState',null)}`,
                        `OPERATOR:${_.get(processedRecord, "operator",null)}`
                    ]);
                    
                    if(processedRecord.isRealTimeDataExhausted && !processedRecord.isSmsDateTimeOlderThanToday){
                        self.L.log("processingOnDbRecords::Publishing to notification topic for data exhausted event");
                        return self.publishInBillFetchKafkaForDataExhaust((err, data)=>{
                           return next(err);
                        },processedRecord);
                    }
                    else if(processedRecord.partialBillState && !processedRecord.isSmsDateTimeOlderThanToday){
                        let ct_data = self.create_ct_event_payload(processedRecord);
                        return self.publishCtEvents((err, data)=>{
                            return next(null);
                        }, ct_data,processedRecord);
                    }
                    else{
                        return next();
                    }
                }else{
                    return next();
                }
            },
            async (next) => {
                if(isSameCustomer && _.get(processedRecord, 'isRealTimeDataExhausted', false)){
                    return next();
                }else{
                    self.updateCassandra(function(err,data){
                        console.log("Published to nonpaytm ---- ")
                        if(self.saveForAnalyticsInCassandraAndKafka(processedRecord) && (err != null || err != undefined) )return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", "NON_RU"), err, next);
                        return next(err);
                    }, processedRecord); // in both cases (isRecordExist == false) and if(isRecordExist = true and isSameCustomer = false) 
                }
            },
            // async (next) => {
            //     if (isRecordExist && processedRecord.isDefaultAmount){
            //         let clonnedData = _.clone(processedRecord);
            //         _.set(clonnedData, 'planAmount', null); 
            //         let ct_data = self.create_ct_event_payload(clonnedData);
            //         self.publishCtEvents(function(err,data){
            //             return next(err);
            //         }, ct_data, processedRecord);
            //     }else return next();
            // },
            // async (next) => {
            //     if (
            //         (!isRecordExist 
            //             || !isPlanValidityUpdate 
            //             || processedRecord.isDefaultAmount 
            //             || _.isNull(processedRecord.validityDate
            //             || processedRecord.isSmsDateTimeOlderThanToday
            //             )) &&  processedRecord.isDwhSmsParsingManual){
            //         self.L.log("processingOnDbRecords::ASYNC.parallel  PARTIAL_TO_FULL_BILL_PLAN_VALIDITY_NOT_UPDATED");
            //         utility._sendMetricsToDD(1, [
            //             'REQUEST_TYPE:SMS_PARSING_PREPAID', 
            //             'TYPE:PARTIAL_TO_FULL_BILL_PLAN_VALIDITY_NOT_UPDATED', 
            //             `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
            //             `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`,
            //             `OPERATOR:${_.get(processedRecord,'operator', null)}`
            //         ]);
            //     }
            //     next();
            // },

            // async (next) => {
            //     if (!(!isRecordExist 
            //         || !isPlanValidityUpdate 
            //         || processedRecord.isDefaultAmount 
            //         || _.isNull(processedRecord.validityDate
            //         || processedRecord.isSmsDateTimeOlderThanToday
            //         )) &&  processedRecord.isDwhSmsParsingManual){
            //         self.L.log("processingOnDbRecords::ASYNC.parallel  PARTIAL_TO_FULL_BILL_PLAN_VALIDITY_UPDATED");
            //         utility._sendMetricsToDD(1, [
            //             'REQUEST_TYPE:SMS_PARSING_PREPAID', 
            //             'TYPE:PARTIAL_TO_FULL_BILL_PLAN_VALIDITY_UPDATED', 
            //             `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
            //             `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`,
            //             `OPERATOR:${_.get(processedRecord,'operator', null)}`
            //         ]);
            //     }
            //     next();
            // },
            async (next) => {
                console.log("*********** ******** processingOnDbRecords::ASYNC.parallel  isRecordExist :"+isRecordExist+" isPlanValidityUpdate :"+isPlanValidityUpdate);
                if (isRecordExist && !isPlanValidityUpdate){
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'STAUTS:ERROR',
                        'TYPE:DB_DUE_DATE_EQUAL_LESS_THAN_DUE_DATE', 
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    // if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", isRecordExist ? "RU" : "NON_RU"), 'Db due date equal or less to record due date', next);
                    self.L.error('Db due date equal or less to record due date', 'Db due date equal or less to record due date');
                    return next();
                }
                next();
            }

        ], (err) => {
            if (err) {
                self.L.error('ASYNC PARALLEL', err);
                done(err)
            }
            else {
                done()
            }
        });
    }

    async publishToPvKafka(processedRecord,ct_data, records){
        let self = this;
        return new Promise((reject, resolve)=>{
            let dueDate= processedRecord.validityDate.format('YYYY-MM-DD');
            let currentDate = MOMENT().format('YYYY-MM-DD');
            let dayValue = -(MOMENT(dueDate).diff(currentDate, 'days'));
            if(MOMENT(dueDate).diff(currentDate, 'days') < _.get(self.config, ['REALTIME_SMS_PARSING','DUE_DATE_NOTIFICATION','EARLIEST_DAY_VALUE'],10) &&
            MOMENT(dueDate).diff(currentDate, 'days') > _.get(self.config, ['REALTIME_SMS_PARSING','DUE_DATE_NOTIFICATION','LATEST_DAY_VALUE'],-7)){
                self.L.log("publishToPvKafka:: ", 'going to publish on COMMON_PV_NOTIFICATION_REALTIME kafka')
            }else{
                self.L.log("publishToPvKafka:: ", 'Not publishing on COMMON_PV_NOTIFICATION_REALTIME kafka due to failing eligibility')
                return resolve(null);
            }            
            ASYNC.eachLimit(records, 3, (dataRow, cb) => {
                let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if(dataRow.notification_status == 0){
                self.L.error(`stop publishing data on publishToPvKafka via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb(null);                         
            }
            let payload = {
                requestType: "prepaidsmsParsingBillDue",
                source: "planexpire",
                dayValue: dayValue,
                customer_id: dataRow.customer_id,
                recharge_number: processedRecord.rechargeNumber,
                service: processedRecord.service,
                circle: processedRecord.circle,
                operator: processedRecord.operator,
                amount: processedRecord.planAmount,
                validity_expiry_date: processedRecord.validityDate,
                product_id: processedRecord.productId,
                bill_fetch_date: MOMENT(),
                paytype: "prepaid",
                customer_mobile:  dataRow.cust_mobile,
                customer_email: dataRow.cust_email,
                status: self.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM,
                latest_recharge_date: dataRow.latest_recharge_date,
                userData: null,
                billDate: null,
                notification_status: dataRow.notification_status,
                planBucket: processedRecord.plan_bucket,   
                extra : JSON.stringify({"recon_id": _.get(processedRecord, 'recon_id', null), "sms_date_time": _.get(processedRecord, "sms_date_time"), "updated_data_source":  self.getUpdatedDataSourceValue(processedRecord)}),
                dwhKafkaPublishedTime : _.get(processedRecord, 'dwhKafkaPublishedTime', null),
            }
            _.set(payload,'billFetchReminder_onBoardTime',new Date().getTime());
            utility.sendNotificationMetricsFromSource(payload,"INITIATED","PV")
            self.parent.commonPvPublisher.publishData([{
                topic: _.get(this.config.PLAN_VALIDITY_NOTIFICATION, ["CONSUMER_SCHEDULER", "common_realtime", "TOPIC"], null),
                messages: JSON.stringify(payload)
            }], (error) => {
                if (error) {
                    utility.sendNotificationMetricsFromSource(payload,"ERROR","PV")
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE: SMS_PARSING_MOBILE_PREPAID", 
                        'STATUS:ERROR',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:COMMON_PV_NOTIFICATION', 
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('publishInKafka :: COMMON_PV_NOTIFICATION', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_MOBILE_PREPAID", 
                        'STATUS:PUBLISHED',
                        'TYPE:KAFKA_PUBLISH',
                        'TOPIC:COMMON_PV_NOTIFICATION', 
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                        "OPERATOR:" + _.get(processedRecord,'operator',null),
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('prepareKafkaResponse :: COMMON_PV_NOTIFICATION', 'Message published successfully in Kafka', ' on topic COMMON_PV_NOTIFICATION', JSON.stringify(payload));
                }
                return cb(null);
            }, [200, 800]);

        }, (error, res) => {
            if (error) {
                self.L.error("postpaidSmsParsing :: publishToPvKafka ", "Error occured", error);
                return reject(error);
            }else return resolve(null);
        });
        })

    }

    async updatePlanValidityForDataExhaust(done, records, processedRecord, ids) {
    let self = this;
    self.L.info("updatePlanValidity::records", JSON.stringify(records));

    let promises = records.map(record => {
        return new Promise(async (resolve, reject) => {
            try {
                let extra = {};
                try {
                    extra = JSON.parse(record.extra) || {};
                } catch (error) {
                    self.L.warn('updatePlanValidity', 'Unable to parse record.extra, creating a new object: ', error);
                }
                // Append extra keys here
                extra.is_data_exhaust = "true";
                extra.data_exhaust_value = _.get(processedRecord, 'exhausted_data', _.get(processedRecord, 'dataConsumed', null));
                extra.data_exhaust_date = _.get(processedRecord, 'exhausted_date', null);
                extra.sms_id = _.get(processedRecord, 'sms_id', null);
                extra.sms_date_time = _.get(processedRecord, 'smsDateTime', null);
                extra.updated_data_source = this.getUpdatedDataSourceValue(processedRecord);
                // Fetch recon_id from record.extra and save it in processedRecord
                processedRecord.recon_id = extra.recon_id;

                let query = `UPDATE plan_validity SET extra=? WHERE id=?`;
                let params = [JSON.stringify(extra), record.id];

                self.L.log('Updating PlanValidity for the id: ' + record.id);

                await self.dbInstance.exec(function (error, data) {
                    if (error) {
                        self.L.error('updatePlanValidity', 'error in updating plan validity: ', error);
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                            'TYPE:ERROR_DATA_EXHAUSTED_PLAN_VALIDITY_UPDATE', 
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                            `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                        ]);
                        reject(error);
                    } else {
                        self.L.info('updatePlanValidity', 'Plan Validity updated successfully for the id: ' + record.id);
                        self.L.log("processingOnDbRecords:: sending metric to DD for isRealTimeDataExhausted for id" + record.id);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_PREPAID', 
                        'TYPE:DATA_EXHAUSTED_PLAN_VALIDITY_UPDATED', 
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                        resolve(data);
                    }
                }, 'RECHARGE_ANALYTICS', query, params);
            } catch (error) {
                self.L.error('updatePlanValidity', 'Unexpected error in updating plan validity: ', error);
                reject(error);
            }
        });
    });

    Promise.all(promises)
        .then(()=> {
            self.L.info('updatePlanValidityForDataExhaust', 'Plan Validity updated successfully for all records');
            done();
        })
        .catch((e) => {
            self.L.error('updatePlanValidityForDataExhaust', 'Plan Validity not updated',e);
            done();
        })
}

getUpdatedDataSourceValue (processedRecord){
    let updatedSource;
    if (_.get(processedRecord, 'isDwhSmsParsingManual', false) == true){
        updatedSource = "SMS_PARSING_DWH_MANUAL"; 
    }
    else if (_.get(processedRecord, 'isDwhSmsParsingRealtime', false) == true) {
        updatedSource = "SMS_PARSING_DWH_REALTIME";
    }
     else if (_.get(processedRecord, 'isRuSmsParsing', false) == true) {
        updatedSource = "SMS_PARSING_REALTIME";
    } else {
        updatedSource = "SMS_PARSING_DWH";
    }
    return updatedSource;
    
}

    async updatePlanValidty(done,records, processedRecord, ids) {
        let recon_id =  utility.generateReconID( processedRecord.rechargeNumber , processedRecord.operator , processedRecord.planAmount , processedRecord.validityDate , null);
        _.set(processedRecord, 'recon_id', recon_id);
        let self = this,
            validity = processedRecord.validityDate.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            query = `update plan_validity set amount=?, validity_expiry_date=?, status=? `;
            let updatedSource = this.getUpdatedDataSourceValue(processedRecord);
            let sms_id = _.get(processedRecord, "sms_id", null);
            let sms_date_time = _.get(processedRecord, "sms_date_time", null);
            if(_.get(processedRecord, 'isRuSmsParsing', false)==true || _.get(processedRecord, 'isDwhSmsParsingRealtime', false) == true){
                query += `,extra=JSON_SET(JSON_REMOVE(COALESCE(extra,'{}'), '$.is_data_exhaust', '$.data_exhaust_value', '$.data_exhaust_date'),${_.get(processedRecord, 'isDwhSmsParsingRealtime', false) == true ? "'$.isDwhSmsParsingRealtime'" : "'$.isRuSmsParsing'"},'true' , '$.recon_id' , '${recon_id}' ,'$.user_type','RU', '$.source_subtype_2','FULL_BILL','$.updated_data_source','${updatedSource}','$.paytype','prepaid','$.sms_id','${sms_id}','$.sms_date_time','${sms_date_time}')`
            }else{
                query += `,extra=JSON_SET(JSON_REMOVE(COALESCE(extra,'{}'), '$.is_data_exhaust', '$.data_exhaust_value', '$.data_exhaust_date'), '$.recon_id' , '${recon_id}' ,'$.user_type','RU', '$.source_subtype_2','FULL_BILL','$.updated_data_source','${updatedSource}','$.paytype','prepaid','$.sms_id','${sms_id}','$.sms_date_time','${sms_date_time}')`
            }
                query+= ` WHERE id in (?)`;
            
        self.L.log('updating PlanValidty for the recharge number: ' + processedRecord.rechargeNumber + "  validity date " + validity);

        self.dbInstance.exec(async function (error, data) {
            if (error || !(data)) {
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) await self.saveForAnalytics(processedRecord, error, 2, (!processedRecord.validityDate || !processedRecord.rechargeNumber) ? "PARTIAL_BILL" : "FULL_BILL", "RU");
                self.L.critical('updateUserData', 'error in updating user data: ', error);
            }
            let billData = {
                dueDate: processedRecord.validityDate.format('YYYY-MM-DD'),
                amount: processedRecord.planAmount,
                rechargeNumber: processedRecord.rechargeNumber,
                service: processedRecord.service,
                paytype: processedRecord.paytype,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                plan_bucket: PLAN_BUCKET,
                plan: processedRecord.plan
            };
            self.L.verbose("billData updateBillsInRecents", billData);
            // self.users.updateBillsInRecents(async () => {
                _.set(self.timestamps,'RUupdateRecentTime',new Date().getTime());
                let ct_data = self.create_ct_event_payload(processedRecord);
                await self.publishToPvKafka(processedRecord, ct_data,records)
                .catch((error)=>{
                    if(error){
                        self.L.log('Error in publishing COMMON_PV_NOTIFICATION',error);
                    }
                })
                
                self.publishCtEvents(done, ct_data,processedRecord);                              
            // }, billData, 'smsUpdate');

        }, 'RECHARGE_ANALYTICS', query, [
            processedRecord.planAmount,
            validity,
            self.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM,
            ids.join()
        ]);
    }

    // updateOperator(done, processedRecord) {
    //      will update opretor in Recent Api and MNP in phase 2
    //     return done();
    // }

    publishCtEvents(done, dbRecordResp,processedRecord) {
        let self = this;

        const customerId = _.get(dbRecordResp, 'customerId', '');
        const operator = _.get(dbRecordResp, 'operator', '');
        const rechargeNumber = _.get(dbRecordResp, 'rechargeNumber', '');
        let eventName = 'prepaidRecharged';
        
        if(processedRecord.dwhClassId){
            eventName = self.DWH_CLASS_CT_NAME_MAP[processedRecord.dwhClassId] || 'smsParsedPrepaidRecharge';
            if(self.validityExpiryIds.includes(processedRecord.dwhClassId)){
            if (processedRecord.is_wo_rech_num) eventName = 'smsParsedPrepaidRechargedWORN';
            else if (processedRecord.is_wo_validity) eventName = 'PrepaidRechargedWODate';
            }
        }

        if(_.get(processedRecord, 'isRuSmsParsing',null) && _.get(processedRecord, 'rtspClassId',null)==8){
            eventName = 'smsParsedPlanRecharged';
        }
        const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;

        console.log("publishCtEvents ", processedRecord, eventName);

        let productId = dbRecordResp.productId;
        //  productId = self.activePidLib.getActivePID(productId);
        if(self.commonLib.isCTEventBlocked(eventName)){
            self.L.info(`Blocking CT event ${eventName}`)
            return done()
        }
        ASYNC.waterfall([
            next => {
                if(_.get(self.config ,['DYNAMIC_CONFIG','CT_CONFIG','CT_EVENT_FILTER',eventName],null) == 1 ){
                    self.commonLib.validateCTEventCondition((error) => {
                        if (error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, customerId, rechargeNumber,eventName);
                }else{
                    next(null)
                }
            },
            next => {
                self.commonLib.getRetailerData((error) => {
                    if (error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, customerId, dbRecordResp);
            },
            next => {
                self.commonLib.getCvrData((error) => {
                    if (error) {
                        return next(error)
                    } else {
                        return next(null)
                    }
                }, productId, dbRecordResp);
            },
            next => {
                        let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
                        let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                        self.parent.ctKafkaPublisher.publishData([{
                            topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                            messages: JSON.stringify(mappedData)
                        }], (error) => {
                            if (error) {
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                                    'STATUS:ERROR', 
                                    "TYPE:CT_EVENTS", 
                                    "SOURCE:PS_PARSING", 
                                    `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                                    "OPERATOR:" + operator,
                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                    `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                                ]);
                                // self.L.critical('SMS_PARSING_PREPAID:PS_PARSING:publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                            } else {
                                utility._sendMetricsToDD(1, [
                                    "REQUEST_TYPE:SMS_PARSING_PREPAID", 
                                    'STATUS:PUBLISHED', 
                                    "TYPE:CT_EVENTS", 
                                    `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`, 
                                    "SOURCE:PS_PARSING", 
                                    "OPERATOR:" + operator,
                                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                                    `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`,`EVENT_NAME:${eventName}`
                                ]);
                                self.L.log('SMS_PARSING_PREPAID:PS_PARSING:prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                            }
                            next(error);
                        }, [200, 800]);
                    },
        ], error => {
            if (error) {
                self.L.log('SMS_PARSING_PREPAID:PS_PARSING:publishCtEvents', `Exception occured Error Msg:: ${error} for record::${JSON.stringify(processedRecord)} debugKey::`, dbDebugKey);
            } else {
                self.L.log(`SMS_PARSING_PREPAID:PS_PARSING:publishCtEvents`, `Record processed having debug key`, dbDebugKey);
            }
            return done(error);
        })
    }

    async updateCassandra(callback, processedRecord) {
        let self = this;
        try {

            //     let customer_id = processedRecord.customerId,
            //         queryString = '?fetch_strategy=BASIC&user_id=' + customer_id;
            //     //let OAuth = new OAuth({ batchSize: _.get(self.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50), rechargeConfig: self.rechargeConfig });

            //  //   let response = await OAuth.fetchCustomerDetail(queryString);
            let response = null;//{ basicInfo: { phone: 9876543210, emai: '<EMAIL>' } };
            let realtimenotify=false;
            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId,
                rechargeNumber: processedRecord.rechargeNumber,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                amount: processedRecord.planAmount,
                bill_fetch_date: MOMENT(),
                paytype: "prepaid",
                service: processedRecord.service,
                circle: processedRecord.circle,
                customer_mobile: response ? _.get(response, ['basicInfo', 'phone'], null) : null,
                customer_email: response ? _.get(response, ['basicInfo', 'email'], null) : null,
                status: self.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM,
                userData: null,
                billDate: MOMENT(),
                notificationStatus: 1,  // ASK by default value 
                dueDate: processedRecord.validityDate,
                customerOtherInfo: JSON.stringify(processedRecord),
                planBucket: processedRecord.plan_bucket,
                dbEvent: "updateMultipleRecordsWithSameRN",
                dwhClassId: _.get(processedRecord, 'dwhClassId', null),
                rtspClassId: _.get(processedRecord, 'rtspClassId', null),
                source:_.get(self,'smsParsingBillsDwhRealtime', false)==true? 'SMS_PARSING_REALTIME_PREPAID':'SMS_PARSING_DWH_PREPAID',
                dwhKafkaPublishedTime : _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                is_notified_at_fe: _.get(processedRecord, 'is_notified_at_fe', false),
                isSmsDateTimeOlderThanToday: _.get(processedRecord, 'isSmsDateTimeOlderThanToday', false),
                airtelPrepaidPublisherDetails: _.get(processedRecord, 'airtelPrepaidPublisherDetails', null),
                isSmsParsedCustIdPresentInSql: _.get(processedRecord, 'custIdExistInPv', false),
            }
            _.set(dataToBeInsertedInDB, ['extra', 'plan_bucket'],_.get(processedRecord, 'plan_bucket', null));
            _.set(dataToBeInsertedInDB, ['extra', 'updated_data_source'], "SMS_PARSING_DWH");
            _.set(dataToBeInsertedInDB, ['extra', 'created_source'], "SMS_PARSING_DWH");
            
            _.set(dataToBeInsertedInDB, "partialBillState", _.get(processedRecord, "partialBillState", null));
            _.set(dataToBeInsertedInDB, ["extra", "partialBillState"], _.get(processedRecord, "partialBillState", null));
            _.set(dataToBeInsertedInDB, ["extra", "partialBillDate"], MOMENT(new Date()).format("YYYY-MM-DD HH:mm:ss"));

            if(_.get(processedRecord, 'isRealTimeDataExhausted', false)){
                _.set(dataToBeInsertedInDB, 'isRealTimeDataExhausted', true);
                _.set(dataToBeInsertedInDB, ['extra', 'isRealTimeDataExhausted'], true);
                _.set(dataToBeInsertedInDB, ['extra', 'is_data_exhaust'], true);
                _.set(dataToBeInsertedInDB, ['extra', 'data_exhaust_value'], _.get(processedRecord, 'exhausted_data', _.get(processedRecord, 'dataConsumed', null)));
                _.set(dataToBeInsertedInDB, ['extra', 'data_exhaust_date'], _.get(processedRecord, 'exhausted_date', null));
                _.set(dataToBeInsertedInDB,'templates',_.get(processedRecord,'templates',null));
                _.set(dataToBeInsertedInDB,'dataConsumed',_.get(processedRecord,'dataConsumed',null));
                _.set(dataToBeInsertedInDB, "time_interval", _.get(processedRecord, "time_interval"));
                _.set(dataToBeInsertedInDB, "nonpaytm_onBoardTime",new Date().getTime());
            }
            
            if(_.get(processedRecord, 'isDwhSmsParsingManual', false)){
                _.set(dataToBeInsertedInDB, ['extra', 'isDwhSmsParsingManual'], true);
                _.set(dataToBeInsertedInDB, ['extra', 'created_source'], "SMS_PARSING_DWH_MANUAL");
                _.set(dataToBeInsertedInDB, ['extra', 'updated_data_source'], "SMS_PARSING_DWH_MANUAL");
            }
            else if(_.get(processedRecord, 'isRuSmsParsing', false)){
                if(_.get(processedRecord, 'isDefaultAmount', false)==false){
                    _.set(dataToBeInsertedInDB, 'toBeNotified', true);
                }
                if(_.get(processedRecord, 'rtspClassId', null)==8){
                    _.set(dataToBeInsertedInDB, 'smsparsedPlanRecharged', true);
                }
                _.set(dataToBeInsertedInDB, ['extra', 'isRuSmsParsing'], true);
                _.set(dataToBeInsertedInDB, ['extra', 'created_source'], "SMS_PARSING_REALTIME");
                _.set(dataToBeInsertedInDB, ['extra', 'updated_data_source'], "SMS_PARSING_REALTIME");
            } else if(_.get(processedRecord, 'isDwhSmsParsingRealtime', false)) {
                if(_.get(processedRecord, 'isDefaultAmount', false)==false){
                    _.set(dataToBeInsertedInDB, 'toBeNotified', true);
                }
                _.set(dataToBeInsertedInDB, ['extra', 'created_source'], "SMS_PARSING_DWH_REALTIME");
                _.set(dataToBeInsertedInDB, ['extra', 'isDwhSmsParsingRealtime'], true);
                _.set(dataToBeInsertedInDB, ['extra', 'updated_data_source'], "SMS_PARSING_DWH_REALTIME");
            }
            let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);
            self.parent.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID",
                        'STATUS:ERROR',
                        `SOURCE:PS_PARSING`,
                        "TYPE:NON_PAYTM_EVENTS",
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`
                            `IS_DWH_SMS_PARSING_MANUAL:${_.get(processedRecord, 'isDwhSmsParsingManual', false)}`,
                    ]);
                    self.L.critical('SMS_PARSING_PREPAID:PS_PARSING:nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(dataToBeInsertedInDB), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_PREPAID",
                        'STATUS:PUBLISHED',
                        "TYPE:NON_PAYTM_EVENTS",
                        `SOURCE:PS_PARSING`,
                        `CLASSID:${_.get(processedRecord, 'rtspClassId', null)}`,
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord, 'appVersion', null)}`,
                        `IS_DWH_SMS_PARSING_MANUAL:${_.get(processedRecord, 'isDwhSmsParsingManual', false)}`,
                        `IS_REAL_TIME_DATA_EXHAUSTED:${_.get(processedRecord, 'isRealTimeDataExhausted', false)}`,
                    ]);
                    self.L.log('SMS_PARSING_PREPAID:PS_PARSING:nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_EVENTS', nonRuDataToPublish);
                }
                callback(error);
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            callback(error);
        }
    }

    create_plan_validity_publisher_data(processedRecord, order_id) {
        var t_time = new Date();
        let self = this;
        let validity_expiry_date = processedRecord.validityDate.endOf('day').format('YYYY-MM-DD HH:mm:ss')
        var obj = {
            currentGw: processedRecord.operator,
            catalogProductID: processedRecord.productId,
            reqType: 'SMS_READ',
            rechargeAttempt: 1,
            userData_recharge_number: processedRecord.rechargeNumber,
            userData_amount: processedRecord.planAmount,
            userData_totalItemsPrice: processedRecord.planAmount,
            userData_recharge_number_length: 10,
            userData_recharge_number_2_length: 0,
            productInfo_operator: processedRecord.operator,
            productInfo_circle: processedRecord.circle,
            productInfo_service: 'mobile',
            productInfo_paytype: 'prepaid',
            productInfo_producttype: 'recharge',
            orderInfo_order_id: order_id,
            orderInfo_item_id: order_id,
        
            orderInfo_order_item_count: 1,
            orderInfo_totalItemsAmount: 15,
            orderInfo_br_id: 459633,
            orderInfo_c_sid: 1,
            orderInfo_cat_id: 17,
            orderInfo_cust_id: processedRecord.customerId,
            orderInfo_previousResponseCode: 'IR',
            customerInfo_channel_id: 'ANDROIDAPP 10.7.0',
            customerInfo_customer_id: processedRecord.customerId,
            customerInfo_customer_email: '',
            customerInfo_customer_phone: '',
            customerInfo_customer_type: 1,
            timestamps_init: t_time,
            rechargeGwResponse_connectionErrorCode: '',
            rechargeGwResponse_connectionErrorMessage: '',
            rechargeGwResponse_gwTxnErrorCode: '',
            rechargeGwResponse_gwTxnErrorMessage: '',
            rechargeGwResponse_gwRefId: '',
            inStatusMap_responseCode: '00',
            inStatusMap_transactionStatus: 'SUCCESS',
            inStatusMap_gwErrorMsg: '',
            gwCallbackData_gw_cb_errorcode: 'S',
            paymentInfo_cardScheme: '',
            paymentInfo_riskInfo: '',
            offUsclientRefID: '',
            metaData: { plan_bucket: PLAN_BUCKET, validity: validity_expiry_date },//'{"plan_id":340515390,"producttype":"Recharge","additionalData":{},"recharge_benefits":{"sms":"NA","data":"1 GB","talktime":"NA","validity":"Existing Plan","couponDescription":"Data: 1GB | For users with an active validity plan"},"check_existing_order":true,"post_order_view_type":"storefront_view"}',
            recentData: {},
            userInfoResponse: {},
            originalPid: processedRecord.productId,
            catalogPID: processedRecord.productId,
            retriedSource: null,
            triedPids: [processedRecord.productId]

        }
        self.parent.planValidityKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.PLAN_VALIDITY_SYNC_DB.TOPIC', ''),
            messages: JSON.stringify(obj)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_PREPAID", 'STATUS:ERROR', `SOURCE:PS_PARSING`, "TYPE:PLAN_VALIDITY_SYNC_DB"]);
                self.L.critical('SMS_PARSING_PREPAID:PS_PARSING:PLAN_VALIDITY_SYNC_DB', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(obj), error);
                return done('Error while publishing message in Kafka');
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_PREPAID", 'STATUS:PUBLISHED', "TYPE:PLAN_VALIDITY_SYNC_DB", `SOURCE:PS_PARSING`, "OPERATOR:" + obj.operator]);
                self.L.log('SMS_PARSING_PREPAID:PS_PARSING:PLAN_VALIDITY_SYNC_DB', 'Message published successfully in Kafka', ' on topic PLAN_VALIDITY_SYNC_DB', JSON.stringify(obj));
            }
        })
    }

    create_ct_event_payload(processedRecord) {
        var t_date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        var response = {
            customerId: processedRecord.recordCustomerId ? processedRecord.recordCustomerId :  processedRecord.customerId,
            rechargeNumber: processedRecord.rechargeNumber,
            productId: processedRecord.productId,
            operator: processedRecord.operator,
            amount: processedRecord.isDefaultAmount ? null : processedRecord.planAmount,
            billDate: t_date,
            dueDate: processedRecord.validityDate,
            paytype: processedRecord.paytype,
            service: processedRecord.service,
            circle: processedRecord.circle,
            customerMobile: processedRecord.rechargeNumber,
            customerOtherInfo: JSON.stringify(processedRecord),
            validityExpiryDate: processedRecord.validityDate,
            orderDate: t_date,
            planBucket: processedRecord.isDefaultAmount ? null : processedRecord.plan_bucket,
            categoryName: null,
            rtspClassId: _.get(processedRecord, 'rtspClassId', null),
            dwhClassId: _.get(processedRecord, 'dwhClassId', null),
            dataConsumed: _.get(processedRecord, 'dataConsumed', null),
        }
        return response;
    }

    async saveForAnalytics (record, error, payloadType, source_subtype_2, user_type) {
        let self = this
        try {
            if(payloadType == 1) {
                await self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, source_subtype_2, user_type), error);
            } else if (payloadType == 2){
                await self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(record, source_subtype_2, user_type), error);
            }
        } catch (e) {
            self.L.critical('saveForAnalytics :: Error :: ', error);
        }
    }

    async saveAndPublishBillFetchAnalyticsData(record, error, cb, cbParam) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record,error);
        } catch(e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        if(cbParam) {
            cb(error,cbParam);
        } else {
            cb(error);
        }
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source           = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type        = user_type;
        recordForAnalytics.customer_id      = _.get(record, 'cId', null);
        recordForAnalytics.service          = 'mobile';
        recordForAnalytics.recharge_number  = self.reduceLengthOfRechargeNumber(recordForAnalytics.service, record);
        recordForAnalytics.operator         = _.get(record, 'smsOperator', null);
        recordForAnalytics.due_amount       = _.get(record, ['telecom_details','plan_amount'], null);
        recordForAnalytics.additional_info  = null;
        recordForAnalytics.sms_id           = _.get(record, 'msg_id', null);
        recordForAnalytics.paytype          = "prepaid"
        recordForAnalytics.updated_at       = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id        = _.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time    = self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null));
        recordForAnalytics.sms_class_id     = _.get(record, 'level_2_category', null);
        recordForAnalytics.due_date         = _.get(record, ['telecom_details','validity_date'], null);
        recordForAnalytics.bill_date        = _.get(record, ['telecom_details','bill_date'], null);
        recordForAnalytics.bill_fetch_date  = _.get(record, ['telecom_details','bill_fetch_date'],  MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    createRecordForAnalytics1(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source           = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type        = user_type;
        recordForAnalytics.customer_id      = _.get(record, 'customerId', null);
        recordForAnalytics.service          = 'mobile';
        recordForAnalytics.recharge_number  = self.reduceLengthOfRechargeNumber(recordForAnalytics.service, record);
        recordForAnalytics.operator         = _.get(record, 'operator', null);
        recordForAnalytics.due_amount       = _.get(record, 'planAmount', null);
        recordForAnalytics.additional_info  = null;
        recordForAnalytics.sms_id           =_.get(record, 'msgId', null);
        recordForAnalytics.paytype          = 'prepaid';
        recordForAnalytics.updated_at       =_.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id        =_.get(record, 'sender_id', null);
        recordForAnalytics.sms_date_time    =self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null));
        recordForAnalytics.sms_class_id     =((typeof _.get(record, 'dwh_classId', null)) == "string") ? _.get(record, 'dwh_classId', null) : `${_.get(record, 'dwh_classId', null)}`
        recordForAnalytics.due_date         =_.get(record, 'validityDate', null);
        recordForAnalytics.bill_date        =_.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date  =_.get(record, 'billFetchDate',  MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    reduceLengthOfRechargeNumber(category, record) {
        let self = this;
        let rechargeNumber = _.get(record, 'rechargeNumber', null),
            isSmsReceiverPresent = false;
        if (!rechargeNumber && category == 'mobile') {
            rechargeNumber = _.get(record, 'telecom_details.mobile_number', null);
            if(!rechargeNumber || rechargeNumber == 'null') rechargeNumber = _.get(record, 'smsReceiver', null);
            isSmsReceiverPresent = true;
        }
        if(rechargeNumber && category == "mobile") {
            rechargeNumber = rechargeNumber.toString();
            if (rechargeNumber.length >= 10) {
                rechargeNumber = rechargeNumber.slice(-10)
                return rechargeNumber;
            }
        }
        return rechargeNumber;
    }

    saveForAnalyticsInCassandraAndKafka(record) {
        let self = this;
        if(self.saveForAnalyticsInCassandraDbAndKafka){   
            let self = this;
            let classId = _.get(record, 'dwh_classId', null);
            if(!classId) _.get(record, 'level_2_category', null);

            if(classId) {
                classId = (typeof classId == "string" ) ? classId : `${classId}`;
                let allowedClassIdsForAnalytics = _.get(self.config, ['DYNAMIC_CONFIG', 'SMSPARSING', 'AllowedDwhClassIdsForAnalytics','classIds'],["1","5","6","8","11"]);
                if(allowedClassIdsForAnalytics.includes(classId)) return true;
            }
        }
        return false;
    }

    getEpochminiToTimestampString(time) {
        // if(typeof time == "number" || (Number(time) != NaN && Number(time) > 0)){
        //     return MOMENT(Number(time)).format('YYYY-MM-DD HH:mm:ss');
        // }
        // return MOMENT().format('YYYY-MM-DD HH:mm:ss') 
        return time;
    }

}
export default prepaidSmsparsing;