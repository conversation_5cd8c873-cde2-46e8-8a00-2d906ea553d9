import ASYNC from 'async'
import _ from 'lodash';
import utility from '../../lib';
import OS from 'os';
import houseRentSmsParsing from  './houseRentSmsParsing';
import SmsParsingSyncCCBillLibrary from '../../lib/smsParsingSyncCCBills';
import DigitalCatalog from '../../lib/digitalReminderConfig'
import KafkaConsumerChecks from '../../lib/kafkaConsumerChecks';

class RentSmsParsing {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;

        this.houseRentSmsParsing = new houseRentSmsParsing(options, this);
        this.smsParsingSyncCCBillLib = new SmsParsingSyncCCBillLibrary(options);
        this.digitalCatalogLib = DigitalCatalog;
       
        this.refereshIntervalForCategoryData = 15 * 60 * 1000; // DCAT getCategoryProductDetail API refresh interval 
        
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
       
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;

        this.commonLib = new utility.commonLib(options);
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
    }

    async start() {
        let self = this;
        self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SMS_PARSING_RENT_BILL_PAYMENT :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('SMS_PARSING_RENT_BILL_PAYMENT :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:CT_EVENTS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:NON_PAYTM_RECORDS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : SMS_PARSING_RENT');

                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RENT_SMS_PARSING_BILL_PAYMENT.HOSTS'),
                    "groupId": "smsParsingRentBillPayment-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.RENT_SMS_PARSING_BILL_PAYMENT.TOPIC'),
                    "id": `smsParsingBillPayment-consumer_${OS.hostname()}_${process.pid}`,
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };
                self.kafkasmsParsingBillPaymentConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkasmsParsingBillPaymentConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:ERROR','TYPE:RENT_SMS_PARSING_BILL_PAYMENT_CONSUMER','SOURCE:MAIN_FLOW']);
                    }
                    if (!error) {
                        self.L.log("configureKafka", `consumer of topic : RENT_SMS_PARSING_BILL_PAYMENT Configured`);
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        self.RUreadsKafkaTime = new Date().getTime();

        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkasmsParsingBillPaymentConsumer._pauseConsumer();
        } else {
            self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT:execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return;
        }

        self.L.log('RENT_SMS_PARSING_BILL_PAYMENT:execSteps:: ', `Processing ${records.length} RENT SMS Parsing Bill Payment data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("RentSmsParsing", records);

                self.kafkasmsParsingBillPaymentConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        utility._sendMetricsToDD(1, ['REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 'STATUS:ERROR', 'TYPE:COMMIT_OFFSET', 'SOURCE:MAIN_FLOW', `TOPIC:${_.get(lastMessage, 'topic',null)}`,`PARTITION:${_.get(lastMessage, 'partition',null)}` ]);
                        self.L.error('RENT_SMS_PARSING_BILL_PAYMENT:execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('RENT_SMS_PARSING_BILL_PAYMENT:execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time: ', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:RENT_SMS_PARSING_BILL_PAYMENT_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        self.kafkasmsParsingBillPaymentConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
    
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    self.L.error("Invalid records found! :", JSON.stringify(records));
                }
                return done()
            }
        );
    }
    processData(record, done) {
        let self = this;
        self.L.info("Record received: ",record);
        let published_time = Number(_.get(record, 'timestamp', null));
        try {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:TRAFFIC', `PARTITION:${_.get(record, 'partition', null)}`, `TOPIC:${_.get(record, 'topic', null)}`]);
            record = JSON.parse(_.get(record, 'value', null));
            if (!record.data) {
                self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        }
        catch (error) {
            if (error) {
                self.L.info('error is', error);
                self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            }
            return done();
        }


        let kafkaTopic = _.get(record, 'kafka_topic', null);
        if (!_.isArray(kafkaTopic) || !_.isArray(_.get(record, 'data', null))) {
            self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return done();
        }
        else if (kafkaTopic[0] == "SMS_PARSING_RENT") {
          
              let record_data = _.get(record, 'data', null);

            if(record_data.length < 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:EMPTY_DATA', 'SOURCE:MAIN_FLOW']);
                self.L.log(`RentSMSParsingBillPayment :: Empty sms Data found`);
                return done();
            }

            let level_2_category = _.get(record_data[0], 'level_2_category', null);

            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT', 
                `SERVICE:SMS_RENT`, 
                'STATUS:TRAFFIC', 
                'TYPE:LEVEL_2_CATEGORY',
                'LEVEL_2_CATEGORY:' + level_2_category, 
                `ORIGIN:SMS_PARSING_DWH`
            ]);

            if(level_2_category != 1 && level_2_category != 2){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:LEVEL_2_CATEGORY_NOT_1', 'SOURCE:MAIN_FLOW']);
                self.L.log(`RentSMSParsingBillPayment :: level_2_category is not 1 and 2`);
                return done();
            }
            
            self.L.log(`RentsmsParsingBillPayment :: executing postpaidSmsParsing flow as per data level_2_category : ${level_2_category}`);
            if(level_2_category == 1){
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        _.set(smsData, 'published_time', published_time);
                        self.houseRentSmsParsing.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else if(level_2_category == 2){
                return ASYNC.map(
                    record.data,
                    (smsData,next) => {
                        _.set(smsData, 'published_time', published_time);
                        self.houseRentSmsParsing.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if(err){
                            self.L.critical('RENT_SMS_PARSING_BILL_PAYMENT:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else{
                return done();
             }
        }
        else {
            return ASYNC.map(
                record.data,
                (smsData, next) => {
                    self.defaultStrategy(() => {
                        return next();
                    }, smsData);
                },
                err => {
                    self.L.error("Invalid records found! :", JSON.stringify(record));
                    return done();
                }
            )
        }
    }

    defaultStrategy(done, record) {
        this.L.log('RENT_SMS_PARSING_BILL_PAYMENT:defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:RENT_SMS_PARSING_BILL_PAYMENT", 'STATUS:NO_STRATEGY', 'SOURCE:MAIN_FLOW']);
        return done();
    }
}

export default RentSmsParsing;
