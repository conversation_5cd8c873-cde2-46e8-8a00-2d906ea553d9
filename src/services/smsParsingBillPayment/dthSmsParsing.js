import BILLS from '../../models/bills';
import recentBillLibrary from '../../lib/recentBills';
import utility from '../../lib';
import MOMENT from 'moment';
import ASYNC from 'async';
import _ from 'lodash';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'validator';
import BILLS_SUBSCRIBER from './../billSubscriber';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import DynamicSmsParsingRegexExecutor from './dynamicSmsParsingRegexExecutor'
import PLAN_VALIDITY from '../../models/planValidity';
import BillFetchAnalytics from '../../lib/billFetchAnalytics'
import BillsLibrary from '../../lib/bills'
import InternalCustIdNonRUFlowTagger from '../../lib/InternalCustIdNonRUFlowTagger';
class DthSmsParsing {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.bills = new BILLS(options);
        this.dbInstance = options.dbInstance;
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.planValidityModel = new PLAN_VALIDITY(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.billsLib = new BillsLibrary(options);
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.smsParsingBillsDwhRealtime = _.get(options, 'smsParsingBillsDwhRealtime', false);
        this.regexExecutor = new DynamicSmsParsingRegexExecutor(options);
        this.billFetchAnalytics = new BillFetchAnalytics(options);
        this.saveForAnalyticsInCassandraDbAndKafka = options.saveForAnalyticsInCassandraAndKafka ? true : false;
        this.PLAN_BUCKET = 'dthDefaultBucket'
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    getOriginOfPayloadCurrentlyBeingProcessed(record) {
        let self = this;
        if(self.isDwhSmsParsingRealtime) {
            return "DTH_SMS_PARSING_DWH_REALTIME"
        }
        return 'DTH_SMS_PARSING_DWH';
    }

    initializeVariable(){
        this.L.verbose("Reinitializing variables")
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
    }

    executeStrategy(done, record, ref) {
        let self = this;
        this.parent = ref;
        self.timestamps = {};
        self.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA

        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('1. executeStrategy:: start executing on dth parsed sms');

        try {
            if (!record) {
                self.L.log(`executeStrategy:: null smsData`);
                return done();
            }
            self.processRecord(record, function (err) {
                if (err) {
                    self.L.error(`SMS_PARSING_DTH :: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_DTH', 
                        `SERVICE:${category}`, 
                        'STATUS:PROCESSING_ERROR', 
                        'SOURCE:DTH_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ])
                }
                return done();
            });
        }
        catch (err) {
            self.L.error(`SMS_PARSING_DTH :: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH', 
                `SERVICE:${category}`, 
                'STATUS:PROCESSING_ERROR', 
                'SOURCE:DTH_SMS',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            return done();
        }

    }

    async processRecord(record, done) {
        let self = this;
        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('2. processRecord:: start processing current record', JSON.stringify(record));

        try{
            ASYNC.waterfall([
                (next)=>{
                    self.validateAndProcessRecord(async (errorResponse,processedRecord)=>{
                        let operator = processedRecord.operator || "NoOpertor";
                        if (errorResponse) {
                            self.L.error(`SMS_PARSING_DTH :: VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${errorResponse}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_DTH', 
                                `SERVICE:${category}`, 
                                'STATUS:ERROR', 
                                'TYPE:VALIDATION_FAILURE', 
                                'OPERATOR:' + operator, 
                                'REASON:' + errorResponse,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(errorResponse,processedRecord);
                        }else {
                            self.L.log(`SMS_PARSING_DTH :: VALIDATION_SUCCESS`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_DTH', 
                                `SERVICE:${category}`, 
                                'STATUS:SUCCESS',
                                'TYPE:VALIDATION_SUCCESS', 
                                'OPERATOR:' + operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(null,processedRecord);
                        }
                    }, record);
                },
                (processedRecord, next)=>{
                    self.getForwardActionFlow((err,action)=>{
                        if(err){
                            self.L.error(`SMS_PARSING_DTH :: getForwardActionFlow`, `invalid action found for: ${processedRecord.debugKey} with error ${err}`);
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_DTH', `SERVICE:${category}`, 'STATUS:ERROR','TYPE:NO_ACTION',`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                            next(err,processedRecord)
                        }else{
                                let billsData = self.getBillsData(processedRecord);
                                processedRecord.billsData = billsData;

                            self.L.log(`SMS_PARSING_DTH :: getForwardActionFlow`, `action: ${action}`);
                            next(null ,action ,processedRecord);
                        }
                    },processedRecord)
                },
                (action, processedRecord, next)=>{
                    if(action == 'update'){
                        self.L.log(`SMS_PARSING_DTH ::perfoming action ${action} for ${action} for data ${processedRecord.debugKey}`);
                        self.updateDbRecord((err)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_DTH :: updateDbRecord`, `error while updating for : ${processedRecord.debugKey}, error: ${err}`);
                                next(err,processedRecord);
                            }else{
                                // _.set(processedRecord, 'ruOnboarded', true);
                                self.L.log(`SMS_PARSING_DTH :: updateDbRecord`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                            }
                        },processedRecord);
                    }else {
                        self.L.log(`SMS_PARSING_DTH :: not onboarded on reminder,  Record processing`);
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next)=>{
                    if(action == 'plan_validity_update'){
                        self.L.log(`SMS_PARSING_DTH ::perfoming plan_validity action ${action} for data ${processedRecord.debugKey}`);
                        self.updateDbRecord((err)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_DTH :: updateDbRecord`, `error while updating for : ${processedRecord.debugKey}, error: ${err}`);
                                next(err,processedRecord);
                            }else{
                                self.L.log(`SMS_PARSING_DTH :: updateDbRecord`, `plan validity updated successfully for : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                            }
                        },processedRecord);
                    }else {
                        self.L.log(`SMS_PARSING_DTH :: non plan_validity user smsData || old SMS Data,  Record processed `);
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next)=>{
                    if(action == 'findAndUpdateToCassandra' || !_.get(processedRecord,'recordFoundOfSameCustId',true)){    
                        self.L.log(`SMS_PARSING_DTH ::perfoming non paytm action ${action} for data `, JSON.stringify(processedRecord));
                        if(processedRecord.recordFoundOfSameCustId != undefined && !processedRecord.recordFoundOfSameCustId){
                            self.L.log(`SMS_PARSING_DTH :: updateCassandra | Record found for same RN,but with new custId`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_DTH', 
                                `SERVICE:${category}`, 
                                'STATUS:RECORD_NOT_FOUND',
                                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                                'TYPE:NON_PAYTM_EVENTS',
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                        }
                        self.updateCassandra((err)=>{
                             if(err){
                                self.L.error(`SMS_PARSING_DTH :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                next(err,processedRecord);
                             }else{
                                self.L.log(`SMS_PARSING_DTH ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, action, processedRecord);
                             }
                        }, processedRecord);
                    }else{
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next)=>{
                    if(action == 'plan_validity_update' || action == 'update'){
                    self.publishInKafka((err)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_DTH: ERROR in publishInKafka for : ${processedRecord.debugKey} with error: ${err}`);
                                utility._sendMetricsToDD(1, [
                                    'REQUEST_TYPE:SMS_PARSING_DTH', 
                                    `SERVICE:${category}`, 
                                    'SOURCE:PUBLISH_KAFKA',
                                    'STATUS:ERROR', 
                                    'TYPE:' + err]);
                                next(err,processedRecord);
                            }else next(null ,processedRecord);
                        },processedRecord,action);
                    }else{
                        next(null ,processedRecord);
                    }
                },
                (processedRecord, next)=>{
                    let service = _.get(processedRecord, 'service', null);
                    let source;
                    source = _.get(processedRecord, 'isRuSmsParsing', false)? `SMS_${service}_REALTIME`:`SMS_${service}`;
                    if(self.smsParsingBillsDwhRealtime) source = `SMS_${service}_DWH_RT`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        next(null,processedRecord);
                    },source,self.timestamps, processedRecord.operator, processedRecord);
                },
            ], async function (error,processedRecord) {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_DTH', 
                            `SERVICE:${category}`, 
                            'STATUS:PROCESS_RECORD_FAILURE', 
                            'SOURCE:DTH_SMS', 
                            'TYPE:' + error,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        self.L.verbose(`SMS_PARSING_DTH :: processRecords`, `Exception occured Error Msg:: ${error}`);
                    } else {
                        
                        self.L.log(`SMS_PARSING_DTH :: processRecords`, `Record processed `,processedRecord.debugKey);
                    }
                return done();
            });
        }catch (err) {
            self.L.error('processRecord:: ', err);
            return done();
        }

    }


    validateAndProcessRecord(done,record) {
        let self = this;
        self.L.log('3. validateAndProcessRecord :: convert payload to record and validate');
        if (!record) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH',
                'STATUS:ERROR', 
                'TYPE:RECORD_NULL',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            self.L.log('4. validateAndProcessRecord :: convert payload to record and validate');
            return done('invalid_record', record);
        }
            if(!_.get(record,'isDwhSmsParsing',false)){
            if(self.saveForAnalyticsInCassandraAndKafka(record)) return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics(record, null, null),"!record.dth_cable_cylinder_details && (!_.get(record,'isDwhSmsParsing',false) && !_.get(record,'isDwhSmsParsingRealtime',false)",done, record);
            self.L.log('5. validateAndProcessRecord :: convert payload to record and validate');
            return done('invalid_record', record);
        }
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);

        if (_.get(record, 'smsDateTime', null)) {
            if (record.smsDateTime.toString().length === 10) {
                record.smsDateTime = record.smsDateTime * 1000;
            }
        }else{
            _.set(record, 'smsDateTime', MOMENT().format('YYYY-MM-DD'));
        }

        let customerId =  (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
            amount =  _.get(record, 'amount', null),
            dueDate = utility.getFilteredDate( _.get(record, 'dueDate', null)).value,
            billDate = MOMENT(record.smsDateTime),
            operator = _.toLower(_.get(record, 'isRuSmsParsing', (_.get(record,'isDwhSmsParsing',false))) ? _.get(record, 'operator', '') : _.get(record, 'smsOperator', '')),
            category = self.getServiceCategoryFromRecord(record);
        if(category == "DTH") operator = _.get(record, 'operator', _.get(record, 'smsOperator', ''));
        console.log("printing the operator before mapping :: ",operator);

        let rechargeNumber = _.get(record, 'rechargeNumber', null);
        if(_.get(record,'isDwhSmsParsing',false)){
            operator = _.get(self.config, ['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'DWH_OPERATOR_MAPPING', 'OPERATOR_MAP' , _.toLower(operator)], _.get(record,'operator',null));
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH', 
                `SERVICE:DTH`, 
                'STATUS:TRAFFIC', 
                'TYPE:OPERATOR_AFTER_OPERATOR_MAPPING',
                'OPERATOR:' + operator,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
        }

        operator = _.toLower(operator);
        
        // let demergerOperatorsList = _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'DEMERGER_OPERATOR', operator], null);

        console.log("printing the operator after mapping :: ",operator);

        let processedRecord = {
            "operator": operator,
            "customerId": customerId,
            "rechargeNumber": rechargeNumber,
            "gateway": null,
            "billFetchDate": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            "billDate": billDate ? billDate.endOf('day').format('YYYY-MM-DD') : null, 
            "dueDate": dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            "amount": amount,
            "status": _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4),
            "paytype": 'prepaid',
            "cache": null,
            "service_id": _.get(self.recent_bills_operators, [operator, 'serviceId'], 0),
            "customerMobile": null,
            "customerEmail": null,
            'paymentChannel': null,
            "retryCount": 0,
            "reason": null,
            "extra": null,
            "customer_type":null, 
            "paymentDate": null,
            "user_data":null,
            "msgId" : _.get(record, 'msg_id', ''),
            "rtspClassId" : _.get(record, 'rtspClassId', null),
            "dwh_classId" : _.get(record, 'level_2_category', null),
            "rtspClassName" : _.get(record, 'rtspClassName', null),
            "sender_id": _.get(record, 'smsSenderID', null),
            "sms_id" : _.get(record, 'msg_id', null),
            "sms_date_time" : _.get(record, 'smsDateTime', null),
            "category": category,
            "appVersion":  _.get(record, 'appVersion', null),
            "smsDateTime": _.get(record, 'smsDateTime', null)
        };

        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(processedRecord, 'isRuSmsParsing', true);
        }else if(_.get(record,'isDwhSmsParsing',false) == true){
            _.set(processedRecord, 'isDwhSmsParsing', true);
        }
        
        self.L.log('3. validateAndProcessRecord :: payload after processing',JSON.stringify(processedRecord));
        let pid = _.get(self.config, ['DYNAMIC_CONFIG', 'DTH_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null);
        self.L.log('3. validateAndProcessRecord :: get pid from config',pid);
        _.set(processedRecord, 'productId' , pid);
        self.L.log('3. validateAndProcessRecord :: after setting pid',processedRecord);
        let activePid = self.activePidLib.getActivePID(processedRecord.productId);
            self.L.verbose('processRecord', `Found active Pid ${activePid} against PID ${processedRecord.productId}`);
            processedRecord.oldProductId = processedRecord.productId; // Keeping track of original PID
            processedRecord.productId = activePid;    // Replacing active PID

        self.L.log('3. validateAndProcessRecord :: pid updated is',_.get(processedRecord, 'productId', null));
        if(_.get(processedRecord, 'productId', null)){
            try {
                _.set(processedRecord, 'paytype', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'paytype'])),null),
                _.set(processedRecord, 'service', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'service'])), null);
            } catch(err) {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_DTH',
                    `SERVICE:${category}`,
                    'STATUS:ERROR',
                    'OPERATOR:' + processedRecord.operator,
                    'TYPE:SETTING_PAYTYPE_SERVICE'
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
                ]);
                self.L.error("Couldn't set paytype and service from cvr for record ", JSON.stringify(processedRecord))
            }
        }

        let isValidRechargeNumber = this.regexExecutor.checkValidityOfRechargeNumberByRegex(processedRecord);
        if(!isValidRechargeNumber){
            self.publishFailedRecordInKafka(record, function(){});
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) 
            return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", null),"rechargeNumber Invalid : Invalid (Regex)",done, processedRecord);
            return done('rechargeNumber Invalid (Regex)', processedRecord);
        }

        let debugKey = `rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`;
            _.set(processedRecord, 'debugKey', debugKey);

        let mandatoryParams = ['customerId', 'rechargeNumber', 'operator', 'productId', 'dueDate'];
        
        let fieldsNotPresent = [];
        mandatoryParams.forEach(function (key) {
            if (!processedRecord[key]) fieldsNotPresent.push(key);
        });

            // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH', 
                `SERVICE:${category}`, 
                'STATUS:ERROR', 
                'OPERATOR:' + processedRecord.operator,
                'TYPE:MANDATORY_PARAMS_NOT_PRESENT',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO_DUEDATE" : "DUEDATE", null),`Mandatory Params ${fieldsNotPresent} is Missing / Invalid`,done, processedRecord);
            return done(`Mandatory Params ${fieldsNotPresent} is Missing / Invalid`, processedRecord);
        }  else if (processedRecord.dueDate && (!MOMENT(processedRecord.dueDate).isValid() || MOMENT(processedRecord.dueDate).diff(MOMENT().endOf('day'), 'days') < 0)) {
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH', 
                `SERVICE:${category}`, 
                'STATUS:ERROR',
                'OPERATOR:' + processedRecord.operator,
                'TYPE:DUE_DATE_INVALID_OR_IN_PAST',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO_DUEDATE" : "DUEDATE", null),'DueDate is invalid || DueDate in past',done, processedRecord);
            return done('DueDate is invalid || DueDate in past', processedRecord);
        }

        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.operator], null);

        if (!tableName){
            self.L.error(`processRecord:: ${processedRecord.operator} not migrated`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH', 
                `SERVICE:${category}`, 
                "STATUS:ERROR",
                'TYPE:TABLE_NOT_FOUND', 
                'OPERATOR:' + processedRecord.operator,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            _.set(processedRecord, 'isMigrated', false);
            _.set(processedRecord, 'tableName', "plan_validity");
        }else{
            self.L.log(`processRecord:: table_name found for operator: ${processedRecord.operator}:${tableName}`);
            _.set(processedRecord, 'tableName', tableName);
            _.set(processedRecord, 'isMigrated', true);
        }
        
        let nextBillFetchDate = self.getNextBillFetchDate(processedRecord);
        self.L.log(`SMS_PARSING_DTH: getNextBillFetchDate for sms Data, NBFD: ${nextBillFetchDate}`);
        _.set(processedRecord, 'nextBillFetchDate', nextBillFetchDate);
        self.L.log('4. validateAndProcessRecord :: payload after processing',JSON.stringify(processedRecord));

        return done(null, processedRecord);

    }
    
    getForwardActionFlow(done, processedRecord){
        let self = this;
        self.L.log('4. getForwardActionFlow:: starting getForwardActionFlow',JSON.stringify(processedRecord));
        let records_found = false;
        if(processedRecord.isMigrated){
            self.getRecordsFromDb((err, recordsFound) =>{
                if(err){
                    self.L.error(`SMS_PARSING_DTH: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_DTH',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        'TYPE:ERROR_GETTING_RECORD_FROM_DB', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        'SOURCE:getForwardActionFlow',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                    ]);
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", null),`ERROR in getRecordsFromDb with error: ${err}`,done);
                    return done(err);
                
                }else{
                    return self.getCommonAction(recordsFound,processedRecord,done)
                    // records_found = recordsFound;
                }
            }, processedRecord);
        }else{
            self.getRecordsFromPlanValidity((err, recordsFound) => {
                if(err){
                    self.L.error(`SMS_PARSING_DTH: ERROR in getRecordsFromPlanValidity with error: ${err} for ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_DTH',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        'TYPE:ERROR_GETTING_RECORD_FROM_PLAN_VALIDITY', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        'SOURCE:getForwardActionFlow',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                    ]);
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", null),`ERROR in getRecordsFromDb>plan_validity with error: ${err}`,done);
                    return done(err);
                
                }else{
                    return self.getCommonAction(recordsFound,processedRecord,done)
                }
            },processedRecord)

        }

    }

    getCommonAction(records_found,processedRecord,done){
        let self = this;
        if(records_found){
            // self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound :` , `for the processedRecord: ${processedRecord.debugKey}`);
            self.L.log(' processRecord:: recordsFound :',JSON.stringify(processedRecord));
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_DTH',
                `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                'STATUS:SUCCESS', 
                `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                'TYPE:RECORD_FOUND_IN_DB', 
                'SOURCE:DTH_SMS',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
            ]);

            if (_.isNull(processedRecord.dueDate)) {
                self.L.log(`processRecord:: DueDate found as null, so skipping this record ${processedRecord.debugKey}`);    
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_DTH',
                    `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                    'STATUS:ERROR', 
                    `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                    'TYPE:DUE_DATE_NULL', 
                    'SOURCE:getForwardActionFlow',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord)) return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "RU"),`DueDate found as null`,done);
                return done('DueDate found as null');
            }

            if(_.get(processedRecord,'recordFoundOfSameCustId',null) == false){
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_DTH',
                    `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                    'STATUS:ERROR', 
                    `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                    'TYPE:RECORD_FOUND_OF_DIFFERENT_CUSTOMER_ID', 
                    'SOURCE:getForwardActionFlow',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`
                ]);
            }

            let dbRecord = _.get(processedRecord, 'dbData', {}),
                dbDueDate = (processedRecord.isMigrated) ?_.get(dbRecord, '[0].due_date', null):_.get(dbRecord, '[0].validity_expiry_date', null),
                dueDateDiff =  MOMENT(processedRecord.dueDate).startOf('day').diff(MOMENT(dbDueDate).utc().startOf('day')),
                dbStatus = _.get(dbRecord, '[0].status', null);

            if(MOMENT(dbDueDate).isValid() && (!dueDateDiff || dueDateDiff < 1)){
                self.L.log(`SMS_PARSING_DTH: getRecordsFromDb , smsDueDate:${MOMENT(processedRecord.dueDate).startOf('day').format('YYYY-MM-DD')} <= dbDueDate:${MOMENT(dbDueDate).utc().startOf('day').format('YYYY-MM-DD')}`);
                processedRecord.dueDate = dbDueDate;
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_DTH",
                    `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                    "STATUS:ERROR",
                    `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                    'TYPE:DUE_DATE_IN_DB_GREATER', 
                    'SOURCE:DTH_SMS',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "RU"),'smsDueDate less than dbDueDate',done);
                return done('smsDueDate less than dbDueDate');
            }
            else if(dbStatus== _.get(self.config , ['COMMON','bills_status','DISABLED'],7) || dbStatus== _.get(self.config , ['COMMON','bills_status','NOT_IN_USE'],13)){
                self.L.log(`SMS_PARSING_DTH: getRecordsFromDb , dbStatus: ${dbStatus}`);
                processedRecord.dueDate = dbDueDate;
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_DTH",
                    `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                    "STATUS:ERROR",
                    `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                    'TYPE:INACTIVE_RECORD', 
                    'SOURCE:DTH_SMS',
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                    `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "RU"),'inactive record in db',done);
                return done('inactive record in db');
            }
            return done(null, 'update', processedRecord);
            
        }else{
            self.L.log(`processRecord:: No recordsFound in DB for the processedRecord: ${processedRecord.debugKey}`);
            return done(null, 'findAndUpdateToCassandra' , processedRecord);
        }
    }
    updateDbRecord(done,record) {
            let self = this;
            let billsData = record.billsData;
            self.L.log('6. updateDbRecord:: starting updateDbRecord');
            if(record.activeRecordsInDB == 0){
                self.L.log('updateRecord', `No actve records in DB, so skipping update for ${record.debugKey}`);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_DTH", 
                    `SERVICE:${_.get(record, 'category', null)}`, 
                    'STATUS:ERROR',
                    'TYPE:NO_ACTIVE_RECORDS', 
                    "OPERATOR:" + record.operator,
                    `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                    `APP_VERSION:${_.get(record,'appVersion', null)}`
                ]);
                if(self.saveForAnalyticsInCassandraAndKafka(record))return self.saveAndPublishBillFetchAnalyticsDataWithoutError(self.createRecordForAnalytics1(record, (record.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "RU/NON_RU"),'No actve records in DB, so skipping update',done);
                return done(null);
            }else if(record.isMigrated){
                _.set(billsData, 'is_automatic', null);
                let extra = _.get(billsData, 'extra', {});
                try{
                    if(typeof extra == 'string'){
                        extra = JSON.parse(extra)
                        _.set(extra, 'updated_source', 'sms');
                        _.set(extra, 'updated_data_source', self.getOriginOfPayloadCurrentlyBeingProcessed(record));
                        extra = JSON.stringify(extra);
                    }else{
                        _.set(extra, 'updated_source', 'sms');
                        _.set(extra, 'updated_data_source', self.getOriginOfPayloadCurrentlyBeingProcessed(record));
                    }
                }catch(e){
                    self.L.log('error in parsing extra in updateBillForSameRNandCID',e)
                }
    
                _.set(billsData, 'extra', extra);
                self.L.log('updateRecord', `Updating records in sql DB for ${record.debugKey}`);
                _.set(self.timestamps,'RUupdatesDbTime',new Date().getTime());
                self.bills.updateBillForSameRechargeNumPostpaid((err) => {
                    if (err) {
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_DTH", 
                            `SERVICE:${_.get(record, 'category', null)}`, 
                            'STATUS:ERROR', 
                            "TYPE:UPDATE_SQL",
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        if(self.saveForAnalyticsInCassandraAndKafka(record))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(record, (record.dueDate ==null || record.amount == null) ? "NO DUEDATE" : "DUEDATE", "RU"),err,done);
                        return done(err);
                    } else {
                        _.set(self.timestamps,'RUupdateRecentTime',new Date().getTime());
                        utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_DTH", 
                            `SERVICE:${_.get(record, 'category', null)}`, 
                            'STATUS:SUCCESS', 
                            'TYPE:UPDATE_SQL', 
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        return done()
                    }
                }, record.tableName, billsData);
            }else{
                self.L.log('updateRecord', `Updating records in plan_validity for ${record.debugKey}`);
                let query = `update plan_validity set validity_expiry_date = ? WHERE recharge_number = ? and operator = ? and plan_bucket = ? `
                let queryParam = [record.dueDate,record.rechargeNumber,record.operator,self.PLAN_BUCKET];
                self.bills.updatePlanValidityTable(query,queryParam)
                .then(function () {
                    self.L.log('updateRecord', `Updated records in plan_validity for ${record.debugKey} succeed`);
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_DTH", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:SUCCESS', 
                        'TYPE:UPDATE_PLAN_VALIDITY', 
                        "OPERATOR:" + record.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    return done(null)
               }).catch(function (error) {
                self.L.log('updateRecord', `Updating records in plan_validity for ${record.debugKey} error`);
                    utility._sendMetricsToDD(1, [
                            "REQUEST_TYPE:SMS_PARSING_DTH", 
                            `SERVICE:${_.get(record, 'category', null)}`, 
                            'STATUS:ERROR', 
                            "TYPE:UPDATE_PLAN_VALIDITY",
                            "OPERATOR:" + record.operator,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        if(self.saveForAnalyticsInCassandraAndKafka(record))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(record, (record.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "NON_RU"),error,done);
                        return done(error);
                });
            }
    }


    async updateCassandra(done, processedRecord) {
        let self = this;
        self.L.log('5. updateCassandra:: starting updateCassandra');
        try {
            let extra = {};
            extra.eventState = "bill_gen";
            extra.billSource = "sms_parsed";
            extra.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);
            extra.created_source = self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord);

            if(_.get(processedRecord,'isRuSmsParsing', false)){
                extra.isRuSmsParsing = true;
            }
            if(_.get(processedRecord,'isDwhSmsParsing',false)){
                extra.isDwhSmsParsing = true;
            } else if(_.get(processedRecord,'isDwhSmsParsingRealtime',false)) {
                extra.isDwhSmsParsingRealtime = true;
            }

            let dataToBeInsertedInDB = {
                customerId: processedRecord.customerId,
                rechargeNumber: processedRecord.rechargeNumber,
                productId: processedRecord.productId,
                operator: processedRecord.operator,
                amount: processedRecord.amount,
                dueDate : MOMENT(processedRecord.dueDate).isValid() ? MOMENT(processedRecord.dueDate).format('YYYY-MM-DD HH:mm:ss') : null,
                billDate :  MOMENT().format('YYYY-MM-DD HH:mm:ss'),   
                billFetchDate : MOMENT(processedRecord.billFetchDate).format('YYYY-MM-DD HH:mm:ss'),
                paytype: processedRecord.paytype,
                service: processedRecord.service,
                circle: processedRecord.circle,
                categoryId: _.get(self.config, ['CVR_DATA', processedRecord.productId, 'category_id'], null),
                customer_mobile:  null,
                customer_email: null,
                status : _.get(self.config, 'COMMON.bills_status.BILL_FETCHED', 4), 
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),  
                customerOtherInfo: _.get(processedRecord,['billsData','customerOtherInfo'],'{}'),   
                extra : JSON.stringify(extra),                        
                dbEvent: "upsert",
                dwhClassId: _.get(processedRecord, 'dwhClassId', null),
                rtspClassId: _.get(processedRecord, 'rtspClassId', null),
                source: self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)
            }

            if(processedRecord.partialRecordFound){
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:SMS_PARSING_DTH', 
                    `SERVICE:${processedRecord.service}`, 
                    "STATUS:SUCCESS",
                    'TYPE:PARTIAL_RECORD', 
                    'OPERATOR:' + processedRecord.operator,
                    `ORIGIN:${_.get(processedRecord,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH'}`,
                ]);
                _.set(dataToBeInsertedInDB, 'partialSmsFound',true);
            }

            // if(_.get(processedRecord,'service',null)=="mobile"){
            //     _.set(dataToBeInsertedInDB, 'toBeNotified', true);
            // }
            //publish in billfetch

            let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);
            self.parent.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_DTH", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('SMS_PARSING_DTH :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                    if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "NON_RU"),'Error while publishing message in Kafka',done);
                    return done('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_DTH", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('SMS_PARSING_DTH :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', nonRuDataToPublish);
                    return done(null);
                }
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            if(self.saveForAnalyticsInCassandraAndKafka(processedRecord))return self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(self.createRecordForAnalytics1(processedRecord, (processedRecord.dueDate ==null) ? "NO DUEDATE" : "DUEDATE", "NON_RU"),`updateCassandra error: ${error}`,done);
            done(error);
        }
    }

    getRecordsFromDb(done,record){
        let self = this;
        self.L.log('getRecordsFromDb :: starting');
        self.bills.getBillsOfSameRech((err, data) => {
            if (err) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:SMS_PARSING_DTH_GETRECORDS", 
                    'STATUS:ERROR', 
                    "OPERATOR:" + record.operator
                ]);
                return done(err, false);
            }
            _.set(record, 'recordFoundOfSameCustId', false);
            if (!data || !_.isArray(data) || data.length < 1) return done(null, false);
                
            let recordOfSameCustId = data.filter((dataValue) => dataValue.customer_id == record.customerId).length > 0 ? true : false;

            _.set(record, 'noOfFoundRecord', data.length);
            _.set(record, 'is_automatic', data[0].is_automatic);
            _.set(record, 'recordFoundOfSameCustId', recordOfSameCustId);
            _.set(record, 'dbData', self.billsLib.getSortedDbData(data));
            _.set(record, 'activeRecordsInDB', self.getActiveRecords(data));
            _.set(record, 'isRecordExist', true);

            return done(null, true);
        }, record.tableName, record);
            
    }
    getRecordsFromPlanValidity(done, processedRecord) {
        let self = this;   
        processedRecord.planBucket = self.PLAN_BUCKET

        self.L.log('getRecordsFromPlanValidity', 'Fetching getRecordsFromPlanValidity from plan_validity...');

        self.planValidityModel.getValidityBykey(function(err,result){
            if(err){
                self.L.critical("getRecordsFromPlanValidity", "Error occurred while fetching record ", err);
                return done(err,false);

            }else{
                self.L.verbose("getRecordsFromPlanValidity :: got db records", JSON.stringify(result));
                return done(null, self.recordFoundInPlanBucket(processedRecord, result));
            }

        },processedRecord)

    }
    recordFoundInPlanBucket(processedRecord,result){
        let self = this;
        _.set(processedRecord, 'recordFoundOfSameCustId', false);
        if (!result || !_.isArray(result) || result.length < 1) 
        return false;

        let recordOfSameCustId = result.filter((dataValue) => dataValue.customer_id == processedRecord.customerId).length > 0 ? true : false;

            _.set(processedRecord, 'noOfFoundRecord', result.length);
            _.set(processedRecord, 'recordFoundOfSameCustId', recordOfSameCustId);
            _.set(processedRecord, 'dbData', result);
            _.set(processedRecord, 'activeRecordsInDB', result.length);
            _.set(processedRecord, 'isRecordExist', true);
        return true;

    }


    getBillsData(record) { 
        let self = this;
        let dbRecord,
            dbExtra = {},
            dbCustomerOtherInfo = {};
        if(record.isRecordExist){
            try{
            dbRecord = _.get(record, 'dbData[0]', {});
            dbExtra = JSON.parse(_.get(dbRecord, 'extra', {}));
            if(!dbExtra) dbExtra = {};
            dbCustomerOtherInfo = JSON.parse(_.get(dbRecord , 'customerOtherInfo', '{}'));
            if(!dbCustomerOtherInfo) dbCustomerOtherInfo = {};
            }catch(err){
                self.L.error("getBillsData", "Error in JSON parsing" + err);
            }
        }
        let custInfoValues = dbCustomerOtherInfo;
        let extraDetails = dbExtra;
        extraDetails.billSource = 'sms_parsed';
        extraDetails.updated_source = 'sms';
        extraDetails.updated_data_source = self.getOriginOfPayloadCurrentlyBeingProcessed(record);
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(record.smsTimeStamp).isValid() ? MOMENT(record.smsTimeStamp).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', _.get(dbRecord, 'validity_expiry_date', null));
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(extraDetails,'isRuSmsParsing', true);
        }
        if(_.get(record,'isDwhSmsParsing',false) == true){
            _.set(extraDetails,'isDwhSmsParsing',true)
        } else if(_.get(record,'isDwhSmsParsingRealtime',false) == true) {
            _.set(extraDetails,'isDwhSmsParsingRealtime',true)
        }
        if(_.get(record, 'partialRecordFound', false)){
            _.set(extraDetails, 'source_subtype_2', 'PARTIAL_BILL');
        }else{
            _.set(extraDetails, 'source_subtype_2', 'FULL_BILL');
        }
        if(_.get(extraDetails, 'errorCounters', null)){
            extraDetails.errorCounters = {};
        }
        custInfoValues.msgId = _.get(record,'msgId','');
        custInfoValues.sms_id = _.get(record,'sms_id','');
        custInfoValues.sms_date_time = _.get(record,'sms_date_time','');
        custInfoValues.sender_id = _.get(record,'sender_id','');
        custInfoValues.dwh_classId = _.get(record,'dwh_classId',null);
        let billsData = {
            user_data: record.user_data,
            nextBillFetchDate: record.nextBillFetchDate,
            billFetchDate: extraDetails.billFetchDate,
            commonAmount: record.amount,
            commonDueDate: record.dueDate,
            rechargeNumber: record.rechargeNumber,
            billDate: record.billDate,
            productId: record.productId,
            commonStatus: record.status,
            customerId: record.customerId,
            customerMobile: _.get(record, 'customerMobile', null),
            operator: record.operator,
            circle: record.circle,
            service: record.service,
            gateway: record.gateway,
            retryCount: 0,
            reason: null,
            paytype: _.get(record, 'paytype', null),
            customerEmail: _.get(record, 'customerEmail', null),
            service_id: _.get(record, 'service_id', 0),
            is_automatic: _.get(record, 'is_automatic', 0),
            msgId : _.get(record, 'msgId', ''),
            extra: JSON.stringify(extraDetails, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            }),
            customerOtherInfo: JSON.stringify(custInfoValues, function (key, value) {
                if (key && typeof (value) === 'string')
                    return value.replace(/[?']/g, "");
                else
                    return value;
            })
        };
        self.L.info('getBillsData billsData',JSON.stringify(billsData))
        return billsData;
    }

    /**
     * Returns active users for which smsparsing data will be updated
     * @param {*} dbRecords 
     */
    getActiveRecords(records) {
        let activeRecords = 0;
        for (let record of records) {
            if (record.status != _.get(this.config, 'COMMON.bills_status.DISABLED', 7) && record.status != _.get(this.config, 'COMMON.bills_status.NOT_IN_USE', 13)) {
                activeRecords++;
            }
        }
        return activeRecords;
    }


    getNextBillFetchDate(record) {
        let self = this;
        let billDateBasedGateways = _.get(self.config, 'SUBSCRIBER_CONFIG.BILL_FETCH_BASED_ON_BILL_DATE_GATEWAYS', []);
        let dateToBeUsed = billDateBasedGateways.indexOf(record.operator) > -1 && record.billDate ? record.billDate : record.dueDate;

        if (billDateBasedGateways.indexOf(record.operator) > -1 && !record.billDate) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_DTH", 
                `SERVICE:${_.get(record, 'category', null)}`, 
                'STATUS:BILLDATE_ABSENT_BILLDATEBASEDGATEWAY', 
                "OPERATOR:" + record.operator
            ]);
        }
        let nextBillFetchDate = (self.billSubscriber.getFirstBillFetchInterval(record.operator) < 0 && record.service != 'dth')? MOMENT(dateToBeUsed).add(Math.abs(Number(self.billSubscriber.getFirstBillFetchInterval(record.operator))), 'months') : MOMENT(dateToBeUsed).add(self.billSubscriber.getFirstBillFetchInterval(record.operator), 'days')
        self.L.error(`getNextBillFetchDate:: For debug key ${record.debugKey} NBFD: ${nextBillFetchDate}`);
        
        if (nextBillFetchDate < MOMENT()) {
            self.L.error(`getNextBillFetchDate:: Change NBFD, currently set in past debugKey: ${record.debugKey} NBFD: ${nextBillFetchDate}`);
            nextBillFetchDate = MOMENT().add(1, 'days');
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:SMS_PARSING_DTH", 
                `SERVICE:${_.get(record, 'category', null)}`, 
                'STATUS:NBFD_SETTING_IN_PAST', 
                "OPERATOR:" + record.operator
            ]);
        }
        return nextBillFetchDate.format('YYYY-MM-DD HH:mm:ss');
    }

    getServiceCategoryFromRecord(record) {
        let key = _.get(record, 'category', null) ? _.get(record, 'category', null).toLowerCase() : null,
            serviceCategory = null;
        
        switch (key) {
            case 'dth_bill_payment':
                serviceCategory = 'DTH';
                break;
            default:
                // Set default in case of when category won't be coming in DWH flow
                serviceCategory = ''
                break;
        }
        return serviceCategory;
    }

    
     /**
     * @param {*} record 
     */

    publishInKafka(done, record, action) {
        let self = this;
        try {
            self.publishCtEvents(function (err) {
                done(err);
            }, record);
        }
        catch (error) {
            self.L.error('Error occurred during dwhSmsParsing publishCtEvents task:', error);
            done(error);
        }
    }

    /**
     * @param {*} record 
     */

    publishCtEvents(done,record) {
        let self = this;
        self.L.log(`11. publishCtEvents:: Record Category - ${record.category}`);
        const productId = _.get(record, 'productId', 0);
        let dbData = _.get(record, 'dbData', []);
        ASYNC.eachLimit(dbData, 3, (dataRow, cb) => {
            let eventName = _.get(this.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'CT_EVENTS', 'BILLGEN'], 'reminderBillGen');

            let dbDebugKey = `rech:${dataRow.recharge_number}::cust:${dataRow.customer_id}::op:${dataRow.operator}`;
            if ((dataRow.status == 13 || dataRow.status == 7)) {
                self.L.log('publishInKafka', `Skipping pulish CT for ${record.debugKey}, dbRow::${dbDebugKey} | due to inactice record`);
                return cb();
            }

            if(self.commonLib.isCTEventBlocked(eventName)){
                self.L.info(`Blocking CT event ${eventName}`)
                return cb()
            }

            let billsData = record.billsData;
            dataRow.due_date = MOMENT(record.dueDate, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('YYYY-MM-DD HH:mm:ss');
        
            if(dataRow.notification_status == 0){
                self.L.error(`stop publishing data on CleverTap via the Kafka pipeline for notification status : ${dataRow.notification_status} debugKey::`, dbDebugKey);
                return cb();                         
            }
            
            if(dataRow.notification_status == null)
                dataRow.notification_status = 1
            dataRow.bill_date = record.billDate ? MOMENT.utc(record.billDate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') : null;
            dataRow.amount = record.amount;
            dataRow.bill_fetch_date = MOMENT.utc(_.get(billsData, 'billFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.next_bill_fetch_date = MOMENT.utc(_.get(billsData, 'nextBillFetchDate', null)).format('YYYY-MM-DD HH:mm:ss');
            dataRow.status = _.get(billsData, 'commonStatus', 0);
            dataRow.extra = _.get(billsData, 'extra', null);
            dataRow.updated_at = MOMENT.utc().format('YYYY-MM-DD HH:mm:ss');
            dataRow.customerOtherInfo = _.get(billsData, 'customerOtherInfo', null);
            dataRow.rtspClassId = _.get(record, 'rtspClassId', null);
            dataRow.dwhClassId = _.get(record, 'dwhClassId', _.get(record, 'dwh_classId', null));

            ASYNC.waterfall([
                next => {
                    self.commonLib.getRetailerData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, dataRow.customer_id, dataRow);
                },
                next => {
                    self.commonLib.getCvrData((error) => {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, productId, dataRow);
                },
                next => {          
                    let mappedData = self.reminderUtils.createCTPipelinePayload(dataRow, eventName, dbDebugKey);
                    let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
                    self.parent.ctKafkaPublisher.publishData([{
                        topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                        messages: JSON.stringify(mappedData)
                    }], (error) => {
                        if (error) {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_DTH", 
                                `SERVICE:${_.get(record, 'category', null)}`, 
                                'STATUS:ERROR', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            self.L.critical('dthSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                        } else {
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:SMS_PARSING_DTH", 
                                `SERVICE:${_.get(record, 'category', null)}`, 
                                'STATUS:PUBLISHED', 
                                "TYPE:KAFKA_PUBLISH",
                                "TOPIC:CT_EVENTS", 
                                "OPERATOR:" + dataRow.operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`,
                                `EVENT_NAME:${eventName}`
                            ]);
                            self.L.log('dthSmsParsing :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                        }
                        return next(error);
                    }, [200, 800]);
                }
            ], (err) => {
                if(err)
                    self.L.log('dthSmsParsing :: publishCtEvents', 'Error while publishing message in Kafka: ', err)
                return cb(err)
            })
        }, (error, res) => {
            if (error) {
                self.L.error("dthSmsParsing :: publishCtEvents ", "Error occured", error);
            }
            return done(error);
        });
    }

    publishFailedRecordInKafka(record, cb){
        let self = this;
        if(self.parent.failedSMSParsingPublisher){
            self.parent.failedSMSParsingPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.FAILED_SMS_PARSING_PUBLISHER.TOPIC', ''),
                messages: JSON.stringify({"data": [record], "kafka_topic": ["FAILED_SMS_PARSING_REGEX_BACKUP"]})
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_DTH", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:FAILED_SMS_PARSING_REGEX_BACKUP", 
                        "OPERATOR:" + _.get(record, 'operator', null),
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.critical('dthSmsParsing :: publishFailedRecordInKafka', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(record), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_DTH", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:FAILED_SMS_PARSING_REGEX_BACKUP", 
                        "OPERATOR:" + _.get(record, 'operator', null),
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.log('dthSmsParsing :: publishFailedRecordInKafka', 'Message published successfully in Kafka', ' on topic FAILED_SMS_PARSING_REGEX_BACKUP');
                }
                return cb();
            }, [200, 800]);
        }
    }

    createRecordForAnalytics(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source           = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type        = user_type;
        recordForAnalytics.customer_id      = _.get(record, 'cId', null);
        recordForAnalytics.service          = (self.getServiceCategoryFromRecord(record)).toLocaleLowerCase();
        recordForAnalytics.recharge_number  = self.reduceLengthOfRechargeNumber(recordForAnalytics.service, record);
        recordForAnalytics.operator         = _.get(record, 'smsOperator', null);
        recordForAnalytics.due_amount       = _.get(record, 'dueAmt', null);
        recordForAnalytics.additional_info  = null;
        recordForAnalytics.sms_id           = _.get(record, 'msg_id', null);
        recordForAnalytics.paytype          =  "postpaid";
        recordForAnalytics.updated_at       = _.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id        = _.get(record, 'smsSenderID', null);
        recordForAnalytics.sms_date_time    = self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null), recordForAnalytics);
        recordForAnalytics.sms_class_id     = _.get(record, 'level_2_category', null);
        recordForAnalytics.due_date         = _.get(record, 'dueDate', utility.getFilteredDate(_.get(record, 'telecom_details.due_date', _.get(record, 'dueDate', null))).value);
        recordForAnalytics.bill_date        = _.get(record, 'billDate', utility.getFilteredDate(_.get(record, 'telecom_details.bill_date', _.get(record, 'billDate',null))).value || MOMENT(record.smsDateTime));
        recordForAnalytics.bill_fetch_date  = _.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    reduceLengthOfRechargeNumber(category, record) {
        let self = this;
        let rechargeNumber = _.get(record, 'rechargeNumber', null);
        return rechargeNumber;
    }

    createRecordForAnalytics1(record, source_subtype_2, user_type) {
        let recordForAnalytics={},
            self = this;
        recordForAnalytics.source           = self.smsParsingBillsDwhRealtime ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH";
        recordForAnalytics.source_subtype_2 = source_subtype_2;
        recordForAnalytics.user_type        = user_type;
        recordForAnalytics.customer_id      = _.get(record, 'customerId', null);
        recordForAnalytics.service          = (_.get(record, 'category', null)).toLowerCase();
        recordForAnalytics.recharge_number  = self.reduceLengthOfRechargeNumber(recordForAnalytics.service, record);
        recordForAnalytics.operator         = _.get(record, 'operator', null);
        recordForAnalytics.due_amount       = _.get(record, 'amount', null);
        recordForAnalytics.additional_info  = null;
        recordForAnalytics.sms_id           =_.get(record, 'msgId', null);
        recordForAnalytics.paytype          =_.get(record, 'paytype', null);
        recordForAnalytics.updated_at       =_.get(record, 'updatedAt', null);
        recordForAnalytics.sender_id        =_.get(record, 'sender_id', null);
        recordForAnalytics.sms_date_time    =self.getEpochminiToTimestampString(_.get(record, 'smsDateTime', null), recordForAnalytics);
        recordForAnalytics.sms_class_id     =_.get(record, 'dwh_classId', null);
        recordForAnalytics.due_date         =_.get(record, 'dueDate', null);
        recordForAnalytics.bill_date        =_.get(record, 'billDate', null);
        recordForAnalytics.bill_fetch_date  =_.get(record, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        return recordForAnalytics;
    }

    async saveAndPublishBillFetchAnalyticsData(record, error, cb, cbParam) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record,error);
        } catch(e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        if(cbParam) {
            cb(error,cbParam);
        } else {
            cb(error);
        }
    }

    async saveAndPublishBillFetchAnalyticsDataWithoutError(record, error, cb) {
        let self = this;
        try {
            await self.billFetchAnalytics.saveAndPublishBillFetchAnalyticsData(record,error);
        } catch(e) {
            self.L.critical('saveAndPublishBillFetchAnalyticsData :: Error while publishing in analytics cassandra and kafka');
        }
        cb();
    }

    getEpochminiToTimestampString(time, recordForAnalytics) {
            if (typeof time == "number" || (Number(time) != NaN && Number(time) > 0)) {
                return MOMENT(Number(time)).format('YYYY-MM-DD HH:mm:ss');
            }
            return MOMENT().format('YYYY-MM-DD HH:mm:ss')

    }

    saveForAnalyticsInCassandraAndKafka(record) {
        let self = this;
        if(self.saveForAnalyticsInCassandraDbAndKafka){   
            let classId = _.get(record, 'dwh_classId', null);
            if(!classId) classId = _.get(record, 'level_2_category', null);
            self.L.info('checkig for analytics for class id', classId);
            if(classId) {
                classId = (typeof classId == "string" ) ? classId : `${classId}`;
                let allowedClassIdsForAnalytics = _.get(self.config, ['DYNAMIC_CONFIG', 'SMSPARSING', 'AllowedDwhClassIdsForAnalytics','classIds'],["1","5","6","8","11"]);
                if(allowedClassIdsForAnalytics.includes(classId)) return true;
            }
        }
        return false;
    }

}
export default DthSmsParsing;
