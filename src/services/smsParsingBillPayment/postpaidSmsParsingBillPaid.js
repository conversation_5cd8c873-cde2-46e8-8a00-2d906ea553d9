import CATALOGVERTICALRECHARGE from '../../models/catalogVerticalRecharge';
import BILLS from '../../models/bills';
import recentBillLibrary from '../../lib/recentBills';
import utility from '../../lib';
import MOMENT from 'moment';
import <PERSON>Y<PERSON> from 'async';
import RecentsLayerLib from '../../lib/recentsLayer';
import _ from 'lodash';
import VALIDATOR from 'validator';
import OS from 'os';
import BILLS_SUBSCRIBER from './../billSubscriber';
import digitalUtility from 'digital-in-util'
import SmsParsingLagDashboard from '../../lib/smsParsingLagDashboard'
import postpaidSmsParsing from './postpaidSmsParsing';
import DynamicSmsParsingRegexExecutor from './dynamicSmsParsingRegexExecutor'
import Q from 'q'
import PrepaidFlowManager from '../../lib/prepaidFlowManager';
import InternalCustIdNonRUFlowTagger from '../../lib/InternalCustIdNonRUFlowTagger';

class postpaidSmsParsingBillPaid{
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.blockedPaytype = _.get(this.config, ['DYNAMIC_CONFIG', 'VALIDATION_SYNC', 'COMMON', 'BLOCKED_PAYTYPES'], ['credit card']);
        this.billSubscriber = new BILLS_SUBSCRIBER(options);
        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.bills = new BILLS(options);
        this.commonLib = new utility.commonLib(options);
        this.activePidLib = options.activePidLib;
        this.consentData = {};
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.recentBillLibrary = new recentBillLibrary(options);
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.infraUtils = options.INFRAUTILS;
        this.cvrReloadInterval = _.get(this.config, 'COMMON.CVR_RELOAD_INTERVAL', 86400000);
        this.reminderUtils = new digitalUtility.ReminderUtils();
        this.greyScaleEnv = options.greyScaleEnv;
        this.rechargeNumberAlreadySeen = []
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.timestamps = {};
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.smsParsingBillsDwhRealtime = _.get(options, 'smsParsingBillsDwhRealtime', false);
        this.postpaidSmsParsing = new postpaidSmsParsing(options, this);
        this.regexExecutor = new DynamicSmsParsingRegexExecutor(options);
        this.prepaidFlowManager = new PrepaidFlowManager(options);
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
        this.internalCustIdNonRUFlowTagger = new InternalCustIdNonRUFlowTagger(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    getOriginOfPayloadCurrentlyBeingProcessed() {
        let self = this;
        if(self.smsParsingBillsDwhRealtime) {
            return "SMS_PARSING_DWH_REALTIME"
        }
        return 'SMS_PARSING_DWH';
    }

    initializeVariable(){
        this.L.verbose("Reinitializing variables")
        this.operators = _.get(this.config, ['RECENT_BILL_CONFIG', 'OPERATORS'], {});
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.recent_bills_operators = this.recentBillLibrary._initRecentBillSpecificData(this.bills_operator_table, this.operators);
        this.allowedOperatorForPrepaidBillFetch = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'PREPAID_ENABLED_OPERATORS', 'OPERATORS'], []);
    }

    executeStrategy(done, record, ref) {
        let self = this;
        this.parent = ref;
        self.timestamps = {};
        self.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('1. executeStrategy:: start executing bill paid service.');

        try {
            if (!record) {
                self.L.log(`executeStrategy:: null smsData`);
                return done();
            }
            self.processRecord(record, function (err) {
                if (err) {
                    self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID :: Error for record :${JSON.stringify(record)}, ${err}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                        `SERVICE:${category}`, 
                        'STATUS:PROCESSING_ERROR', 
                        'SOURCE:POSTPAID_SMS_BILL_PAID',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ])
                }
                return done();
            });
        }
        catch (err) {
            self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID :: Error for record :${JSON.stringify(record)}, ${err}`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                `SERVICE:${category}`, 
                'STATUS:PROCESSING_ERROR', 
                'SOURCE:POSTPAID_SMS_BILL_PAID',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            return done();
        }
    }

    async processRecord(record, done) {
        let self = this;
        let category = self.getServiceCategoryFromRecord(record);
        self.L.log('2. processRecord:: start processing current record', JSON.stringify(record));

        try{
            ASYNC.waterfall([
                (next)=>{
                    self.validateAndProcessRecord(async (errorResponse,processedRecord)=>{
                        let operator = processedRecord.operator || "NoOpertor";
                        if (errorResponse) {
                            self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID :: VALIDATION_FAILURE`, `Invalid record received ${JSON.stringify(record)} with error ${errorResponse}`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                                `SERVICE:${category}`, 
                                'STATUS:ERROR', 
                                'TYPE:VALIDATION_FAILURE', 
                                'OPERATOR:' + operator, 
                                'REASON:' + errorResponse,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(errorResponse,processedRecord);
                        }else {
                            self.L.log(`SMS_PARSING_POSTPAID_BILL_PAID :: VALIDATION_SUCCESS`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                                `SERVICE:${category}`, 
                                'STATUS:SUCCESS',
                                'TYPE:VALIDATION_SUCCESS', 
                                'OPERATOR:' + operator,
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                            next(null,processedRecord);
                        }
                    }, record);
                },
                (processedRecord, next)=>{
                        self.getForwardActionFlow((err,action)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_POSTPAID :: getForwardActionFlow`, `invalid action found for: ${processedRecord.debugKey} with error ${err}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', `SERVICE:${category}`, 'STATUS:NO_ACTION',`ORIGIN:${self.smsParsingBillsDwhRealtime == true ? "SMS_PARSING_DWH_REALTIME" :(_.get(record,'isRuSmsParsing', false)==true? 'SMS_PARSING_REALTIME':'SMS_PARSING_DWH')}`]);
                                next(err,processedRecord)
                            }else{
                                self.L.log(`6. SMS_PARSING_POSTPAID_BILL_PAID :: getForwardActionFlow`, `action: ${action}`);
                                next(null ,action ,processedRecord);
                            }
                        },processedRecord)
                },
                (action, processedRecord, next)=>{
                    if(action === 'handlePrepaidRecord'){
                        try {
                            self.setCustInfoExtraDetails(processedRecord);
                        } catch (error) {
                            self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID ::  handlePrepaidRecord :: setCustInfoExtraDetails for ${processedRecord.debugKey}`, `failed with error ${error}`);
                        }
                        self.prepaidFlowManager.handlePrepaidRecord((err, processedRecord) => {
                            if (err) {
                                self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID :: handlePrepaidRecord`, `failed with error ${err}`);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', `SERVICE:${category}`, 'STATUS:ERROR','TYPE:HANDLE_PREPAID_RECORD',`ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`]);
                                return next(err, processedRecord);
                            } else {
                                const isPostpaidRecordExist = _.get(processedRecord, 'isRecordExist', false);
                                const isSameCustomerId = _.get(processedRecord,'recordFoundOfSameCustId',false);
                                const isPrepaidRecordExist = _.get(processedRecord, 'isPrepaidDataFound', false);
                                
                                const publishInNonRu = (!isPostpaidRecordExist && !isPrepaidRecordExist) || (isPostpaidRecordExist && !isSameCustomerId);                          
                                self.L.log(`handlePrepaidRecord billPaid :: callback :: Flags :: publishInNonRu: ${publishInNonRu} for ${processedRecord.debugKey}`);

                                _.set(processedRecord, 'publishInNonRu', publishInNonRu);
                                next(null, action, processedRecord);
                            }
                        }, processedRecord);
                    }else{
                        next(null, action, processedRecord);
                    }
                },
                (action, processedRecord, next)=>{
                    if(action == 'findAndCreateToCassandra' || _.get(processedRecord, 'publishInNonRu', false)){    
                        if(processedRecord.recordFoundOfSameCustId != undefined && !processedRecord.recordFoundOfSameCustId){
                            self.L.log(`SMS_PARSING_POSTPAID_BILL_PAID :: updateCassandra | Record found for same RN,but with new custId`);
                            utility._sendMetricsToDD(1, [
                                'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                                `SERVICE:${category}`, 
                                'STATUS:RECORD_NOT_FOUND_OF_SAME_CID',
                                'TYPE:NON_PAYTM_EVENTS',
                                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                                `APP_VERSION:${_.get(record,'appVersion', null)}`
                            ]);
                        }
                        self.updateCassandra((err)=>{
                            if(err){
                                self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID :: updateCassandra`, `error while updating for : ${processedRecord.debugKey} with error: ${err}`);
                                next(err,processedRecord);
                            }else{
                                self.L.log(`SMS_PARSING_POSTPAID_BILL_PAID ::  updateCassandra`, `updated successfully for : ${processedRecord.debugKey}`);
                                next(null, processedRecord);
                            }
                        }, processedRecord);
                    }
                    else{
                        next(null, processedRecord);
                    }
                },
                (processedRecord, next)=>{
                    let service = _.get(processedRecord, 'service', null);
                    let source = `SMS_${service}_POSTPAID`;
                    self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
                        next(null,processedRecord);
                    },source,self.timestamps, processedRecord.operator, processedRecord);
                },
            ], async function (error,processedRecord) {
                    if (error) {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                            `SERVICE:${category}`, 
                            'STATUS:PROCESS_RECORD_FAILURE', 
                            'SOURCE:POSTPAID_SMS', 
                            'TYPE:' + error,
                            `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                            `APP_VERSION:${_.get(record,'appVersion', null)}`
                        ]);
                        self.L.verbose(`SMS_PARSING_POSTPAID_BILL_PAID :: processRecords`, `Exception occured Error Msg:: ${error}`);
                    } else {
                        self.L.log(`SMS_PARSING_POSTPAID_BILL_PAID :: processRecords`, `Record processed `);
                    }
                return done();
            });
        }catch (err) {
            self.L.error('processRecord:: ', err);
            return done();
        }
    }

    async updateCassandra(done, processedRecord) {
        let self = this;
        let extra = {};
        extra.eventState = "bill_paid";
        extra.billSource = "sms_parsed";
        extra.updated_data_source = "SMS_PARSING_DWH_PAYMENT";
        extra.created_source = "SMS_PARSING_DWH_PAYMENT";
        if (self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord)) {
            extra.isPrepaid = "1";
        }
        if (self.smsParsingBillsDwhRealtime) {
            extra.updated_data_source = "SMS_PARSING_DWH_REALTIME_PAYMENT";
            extra.created_source = "SMS_PARSING_DWH_REALTIME_PAYMENT";
        }
        self.L.log('5. updateCassandra:: starting updateCassandra');
        try {
            let dataToBeInsertedInDB = {
                customerId: _.get(processedRecord,'customerId',null),
                rechargeNumber: _.get(processedRecord,'rechargeNumber',null),
                productId: _.get(processedRecord,'productId',null),
                operator: _.get(processedRecord,'operator',null),
                amount: _.get(processedRecord,'currentPaidAmount',0),
                dueDate : _.get(processedRecord,'dueDate',null) ? MOMENT(processedRecord.dueDate).format() : null,
                billDate : _.get(processedRecord,'billDate',null) ? (MOMENT(processedRecord.billDate).format()) : null,   
                billFetchDate : _.get(processedRecord,'billFetchDate',null) ? MOMENT(processedRecord.billFetchDate).format() : null,
                paytype: _.get(processedRecord,'paytype',null),
                service: _.get(processedRecord,'service',null),
                circle: _.get(processedRecord,'circle',null),
                customer_mobile:  _.get(processedRecord,'customerMobile',null),
                customer_email: _.get(processedRecord,'customerEmail',null),
                status : _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14),
                notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),  
                customerOtherInfo: JSON.stringify(_.clone(processedRecord)),   
                extra : JSON.stringify(extra),
                paymentDate : _.get(processedRecord,'paymentDate',null),                     
                dbEvent: "findAndCreate",
                source: self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord),
                nonpaytm_onBoardTime : new Date().getTime(),
                dwhKafkaPublishedTime : _.get(processedRecord,'dwhKafkaPublishedTime',null),
            }

            if(_.get(processedRecord,'service',null)=="mobile" && !(self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord))){
                _.set(dataToBeInsertedInDB, 'toBeNotified', true);
            }

            if(self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord)) {
                _.set(dataToBeInsertedInDB, 'toBeNotified', false);
            }

            let nonRuDataToPublish = await self.internalCustIdNonRUFlowTagger.mapInternalCustIdToNonRUFlow(dataToBeInsertedInDB);

            self.parent.nonPaytmKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
                messages: nonRuDataToPublish
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.critical('SMS_PARSING_POSTPAID_BILL_PAID :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + nonRuDataToPublish, error);
                    return done('Error while publishing message in Kafka');
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID", 
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:NON_PAYTM_EVENTS",
                        "TOPIC:NON_PAYTM_RECORDS_DWH",
                        "OPERATOR:" + dataToBeInsertedInDB.operator,
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    self.L.log('SMS_PARSING_POSTPAID_BILL_PAID :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS', nonRuDataToPublish);
                    return done(null);
                }
            })
        } catch (error) {
            self.L.error(" updateCassandra error:", error);
            done(error);
        }
    }

    async getForwardActionFlow(done, processedRecord){
        let self = this;
        const isPrepaidFlowAllowed = self.prepaidFlowManager.isPrepaidFlowAllowed(processedRecord);
        self.L.log('5. getForwardActionFlow:: starting getForwardActionFlow');
        self.postpaidSmsParsing.getRecordsFromDb((err, recordsFound) =>{
            if(err){
                self.L.error(`SMS_PARSING_POSTPAID_BILL_PAID: ERROR in getRecordsFromDb with error: ${err} for ${processedRecord.debugKey}`);
                return done(err);
            }else{
                if(isPrepaidFlowAllowed) {
                    self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound : handlePrepaidRecord action` , `for the processedRecord: ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID_PREPAID',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:SUCCESS', 
                        `OPERATOR:${_.get(processedRecord,'operator','NO_OPERATOR')}`,
                        'TYPE:RECORD_FOUND_IN_DB', 
                        'SOURCE:POSTPAID_SMS_BILL_PAID',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(processedRecord)}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    const now = MOMENT().startOf('day');
                    const nfbDays = _.get(self.config, ['DYNAMIC_CONFIG', 'PREPAID_BILL_FETCH_CONFIG', 'NBFD_CYCLE_BILL_PAID', _.get(processedRecord, 'operator', null)], 20);
                    processedRecord.commonDueDate = null;
                    processedRecord.dueDate = null;
                    processedRecord.nextBillFetchDate = now.add(nfbDays, 'days').format('YYYY-MM-DD HH:mm:ss');
                    processedRecord.commonAmount = processedRecord.currentPaidAmount;
                    self.L.verbose(`getForwardActionFlow:: going to handlePrepaidRecord action for ${processedRecord.debugKey}, returning payload ${JSON.stringify(processedRecord)}`);
                    return done(null, 'handlePrepaidRecord', processedRecord);
                } else if (processedRecord.recordFoundOfSameCustId){
                    self.L.log(`processRecord:: ${processedRecord.noOfFoundRecord} recordsFound :` , `for the processedRecord: ${processedRecord.debugKey}`);
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID',
                        `SERVICE:${_.get(processedRecord, 'category', null)}`, 
                        'STATUS:SUCCESS', 
                        'TYPE:RECORD_FOUND_IN_DB', 
                        'SOURCE:POSTPAID_SMS',
                        `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                        `APP_VERSION:${_.get(processedRecord,'appVersion', null)}`
                    ]);
                    return done('record found in table');
                } else {
                    self.L.log(`processRecord:: No recordsFound in DB for the processedRecord: ${processedRecord.debugKey}`);
                    return done(null, 'findAndCreateToCassandra' , processedRecord);
                }
            }
        }, processedRecord);
    }

    validateAndProcessRecord(done,record) {
        let self = this;
        let dateFormat = 'YYYY-MM-DD';
        self.L.log('3. validateAndProcessRecord :: convert payload to record and validate');
        if (!record) return done('invalid_record', record);
        if(self.smsParsingBillsDwhRealtime== true && (typeof _.get(record, 'smsDateTime', null) == "string") && _.get(record, 'smsDateTime', null) != null){
            _.set(record, 'smsDateTime', parseInt(_.get(record, 'smsDateTime', null)));
        }
        if(_.get(record, 'smsDateTime', null)){
            if(Number(record.smsDateTime) && record.smsDateTime.toString().length===10){
                record.smsDateTime=record.smsDateTime*1000
            }}
        let smsDateTime_fromPayload = Number(record.smsDateTime); 
            const   timestamp = new Date(record.timestamp).getTime(),
                    smsDateTime = new Date(smsDateTime_fromPayload).getTime(),
                    smsParsingEntryTime = new Date(record.producerEntryTime).getTime(),
                    smsParsingExitTime = new Date(record.consumerExitTime).getTime(),
                    deviceDateTime = new Date(record.deviceDateTime).getTime(),
                    uploadTime = new Date(record.uploadTime).getTime(),
                    collector_timestamp = new Date(record.collector_timestamp).getTime(),
                    dwhKafkaPublishedTime = new Date(record.dwhKafkaPublishedTime).getTime();
            _.set(self.timestamps,'data_smsDateTime',smsDateTime);
            _.set(self.timestamps,'data_timestamp',timestamp);
            _.set(self.timestamps,'data_deviceDateTime',deviceDateTime);
            _.set(self.timestamps,'data_uploadTime',uploadTime);
            _.set(self.timestamps,'collector_timestamp',collector_timestamp);
            _.set(self.timestamps, 'RUreadsKafkaTime', self.RUreadsKafkaTime);
            _.set(self.timestamps, 'smsParsingEntryTime', smsParsingEntryTime);
            _.set(self.timestamps, 'smsParsingExitTime', smsParsingExitTime);
            _.set(self.timestamps, 'dwhKafkaPublishedTime', dwhKafkaPublishedTime);

        if (_.get(record,'smsDateTime',null) == null || _.get(record,'smsDateTime',null).toString().length == 0) {
            _.set(record, 'smsDateTime', MOMENT().format('YYYY-MM-DD'));
        }

        let customerId =  (typeof record.cId === 'number') ? record.cId : (typeof record.cId === 'string' && VALIDATOR.isNumeric(record.cId)) ? VALIDATOR.toInt(record.cId) : null,
            amount, dueDate=null, billDate=null, operator,rechargeNumber,
            category = self.getServiceCategoryFromRecord(record);;

        if(category == 'MOBILE'){
            amount = utility.getFilteredAmount(_.get(record, 'telecom_details.bill_due_amount', _.get(record, 'amount', '0')));
            dueDate = utility.getFilteredDate(_.get(record, 'telecom_details.due_date', _.get(record, 'dueDate', null))).value;
            billDate = utility.getFilteredDate(_.get(record, 'telecom_details.bill_date', _.get(record, 'billDate',null))).value || MOMENT(record.smsDateTime);
            operator = _.toLower(_.get(record, 'telecom_details.operator', _.get(record, 'smsOperator', '')));
            rechargeNumber = _.get(record, 'telecom_details.mobile_number', null);
            if(!rechargeNumber || rechargeNumber == 'null') 
                rechargeNumber = _.get(record, 'smsReceiver', null);
            if(!rechargeNumber) {
                return done('rechargeNumber Empty', record);
            }
            rechargeNumber = rechargeNumber.toString();
            rechargeNumber = rechargeNumber.replace(/\s/g, '');
            if (rechargeNumber.length >= 10) {
                rechargeNumber = rechargeNumber.slice(-10);
            }else{
                return done('rechargeNumber Invalid', record);
            }
            // check recharge_number contains only digit
            if(!(/^\d+$/.test(rechargeNumber)))
            {
                return done('rechargeNumber Invalid', record);
            }
        }
        else{
            amount = _.get(record, 'electricity_details.bill_due_amount', _.get(record, 'amount', null)) ? utility.getFilteredAmount(_.get(record, 'electricity_details.bill_due_amount', _.get(record, 'amount', null))) : null;
            operator = _.toLower(_.get(record,'electricity_details.operator',null));
            rechargeNumber = _.get(record, 'electricity_details.primary_ca_no', null);
            operator = _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'DWH_OPERATOR_MAPPING', operator], operator);
        }
        if (_.get(record, 'electricity_details.is_prepaid', '0') == "1") {
            if (_.get(record, 'electricity_details.bill_due_amount', _.get(record, 'amount', null)) == 0) {
                amount = 0;
            } else if (amount != null) {
                amount = - amount;
            }
        }

        let demergerOperatorsList = null;
        if(operator == 'uttar pradesh power corporation ltd. (uppcl)'){
            demergerOperatorsList =  _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'DEMERGER_OPERATOR', operator], _.get(record,'operator',null));
        }

        let paymentDate = record.smsDateTime? new Date(record.smsDateTime) : null;
        paymentDate = MOMENT(paymentDate).isValid() ? MOMENT(paymentDate).format('YYYY-MM-DD HH:mm:ss') : null;
        let processedRecord = {
            "operator": operator,
            "customerId": customerId,
            "rechargeNumber": rechargeNumber,
            "gateway": null,
            "billFetchDate": null,
            "billDate": billDate ? billDate.endOf('day').format('YYYY-MM-DD') : null, 
            "dueDate": dueDate ? dueDate.endOf('day').format('YYYY-MM-DD HH:mm:ss') : null,
            "currentPaidAmount": amount ? - amount : amount,
            "status": _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14),
            "commonStatus": _.get(self.config, 'COMMON.bills_status.PAID_ON_OTHER_PLATFORM', 14),
            "paytype": 'postpaid',
            "customerMobile": (category == 'MOBILE') ? rechargeNumber : null,
            "customerEmail": _.get(record, 'smsReceiverEmail', null),
            "extra": null,
            "paymentDate": paymentDate,
            "msgId" : _.get(record, 'msg_id', ''),
            "senderId": _.get(record, 'smsSenderID', null),
            "dwh_classId" : _.get(record, 'level_2_category', null),
            "sms_date_time" : _.get(record, 'smsDateTime', null),
            "category": category,
            "appVersion":  _.get(record, 'appVersion', null),
            "smsSenderID": _.get(record, 'smsSenderID', ''),
            "demergerOperatorsList":demergerOperatorsList,
            "isPrepaid": String(_.get(record, 'electricity_details.is_prepaid', '0') || '0'),
        };

        let maxAmount;
        
        if(category == 'MOBILE'){
            maxAmount= _.get(self.config, ['DYNAMIC_CONFIG', 'MOBILE_POSTPAID_SMS_PARSING', 'COMMON', 'MAX_AMOUNT'], 10000);
        }

        if(operator && category == 'MOBILE'){
            _.set(processedRecord, 'circle' , 'all circles');
            _.set(processedRecord, 'productId' , _.get(self.config, ['DYNAMIC_CONFIG', 'POSTPAID_SMS_PARSING', operator, 'PRODUCT_ID'], null));   
        }
        self.L.log('3. validateAndProcessRecord :: payload after processing', processedRecord);

        let rechargeNumber2 = _.toLower(_.get(record, 'electricity_details.secondary_ca_no', null));
        let pidMapKey = (operator + (rechargeNumber2 != null && rechargeNumber2 != '' ? `_${rechargeNumber2}` : '')).replace(/ /g, '_'); 
        if (!_.get(processedRecord, 'productId', null)) {
            // Set productId in case of electricity category
            self.L.log('3. validateAndProcessRecord :: pidMapKey',pidMapKey);
            _.set(processedRecord, 'productId' , _.get(self.config,['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', pidMapKey], _.get(self.config, ['DYNAMIC_CONFIG', 'DWH_ELECTRICITY_SMS_PARSING_CONFIG', 'OPERATOR_PRODUCT_ID_MAPPING', 'PID_MAP', operator], null)));
        }

        if(_.get(processedRecord, 'productId', null)){
            try {
                _.set(processedRecord, 'paytype', _.get(this.config,['CVR_DATA',processedRecord.productId,'paytype']),null),
                _.set(processedRecord, 'service', _.toLower(_.get(this.config,['CVR_DATA',processedRecord.productId,'service'])), null);
            } catch(err) {
                self.L.error("Couldn't set paytype and service from cvr for record ", JSON.stringify(processedRecord))
            }
        }

        let isValidRechargeNumber = this.regexExecutor.checkValidityOfRechargeNumberByRegex(processedRecord);
        if(!isValidRechargeNumber){
            self.publishFailedRecordInKafka(record, function(){});
            return done('rechargeNumber Invalid (Regex)', processedRecord);
        }

        let debugKey = `rech_num:${processedRecord.rechargeNumber}::operator:${processedRecord.operator}::service:${processedRecord.service}::productId:${processedRecord.productId}`;
        _.set(processedRecord, 'debugKey', debugKey);
        let mandatoryParams = ['customerId', 'rechargeNumber', 'operator', 'productId'];
        if (_.get(processedRecord, 'isPrepaid', '0') == '0') {
            mandatoryParams.push('currentPaidAmount');
        }
        let fieldsNotPresent = [];
            mandatoryParams.forEach(function (key) {
                if (!processedRecord[key]) fieldsNotPresent.push(key);
            });

            // check for mandatory fields
        if (fieldsNotPresent.length > 0) {
            return done(`Mandatory Params ${fieldsNotPresent} is Missing / Invalid`, processedRecord);
        }

        let activePid = self.activePidLib.getActivePID(processedRecord.productId);
            self.L.verbose('processRecord', `Found active Pid ${activePid} against PID ${processedRecord.productId}`);
            processedRecord.oldProductId = processedRecord.productId; // Keeping track of original PID
            processedRecord.productId = activePid;    // Replacing active PID

        let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', processedRecord.operator], null);

        if(self.checkIfOldSMSRejectionCase(processedRecord)){
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR', 
                'OPERATOR:' + processedRecord.operator,
                'TYPE:OLD_SMS_REJECTION',
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed(record)}`
            ]);
            return done('Old SMS Date Found', processedRecord);
        }

        if (!tableName){
            self.L.error(`processRecord:: ${processedRecord.operator} not migrated`);
            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID', 
                `SERVICE:${category}`, 
                'STATUS:ERROR',
                'TYPE:TABLE_NOT_FOUND', 
                'OPERATOR:' + processedRecord.operator,
                `ORIGIN:${self.getOriginOfPayloadCurrentlyBeingProcessed()}`,
                `APP_VERSION:${_.get(record,'appVersion', null)}`
            ]);
            return done(`Table not found for ${processedRecord.operator}`,processedRecord);
        }


        self.L.log(`4. processRecord:: table_name found for operator: ${processedRecord.operator}:${tableName}`);
        _.set(processedRecord, 'tableName', tableName);

        self.L.log('validateAndProcessRecord :: final payload returning',JSON.stringify(processedRecord));

        return done(null, processedRecord);
    }

    getServiceCategoryFromRecord(record) {
        let key = _.get(record, 'predicted_category', null) ? _.get(record, 'predicted_category', null).toLowerCase() : null,
            serviceCategory = null;
        
        switch (key) {
            case 'mobile prepaid':
                serviceCategory = 'MOBILE';
                break;
            case 'electricity':
                serviceCategory = 'ELECTRICITY';
                break;
            default:
                // Set default in case of when category won't be coming in DWH flow
                serviceCategory = 'ELECTRICITY'
                break;
        }
        return serviceCategory;
    }

    publishFailedRecordInKafka(record, cb){
        let self = this;
        if(self.parent.failedSMSParsingPublisher){
            self.parent.failedSMSParsingPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.FAILED_SMS_PARSING_PUBLISHER.TOPIC', ''),
                messages: JSON.stringify({"data": [record], "kafka_topic": ["FAILED_SMS_PARSING_REGEX_BACKUP"]})
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:ERROR', 
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:FAILED_SMS_PARSING_REGEX_BACKUP", 
                        "OPERATOR:" + _.get(record, 'operator', null),
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.critical('SMS_PARSING_POSTPAID_BILL_PAID :: publishFailedRecordInKafka', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(record), error);
                } else {
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:SMS_PARSING_POSTPAID_BILL_PAID", 
                        `SERVICE:${_.get(record, 'category', null)}`, 
                        'STATUS:PUBLISHED', 
                        "TYPE:KAFKA_PUBLISH",
                        "TOPIC:FAILED_SMS_PARSING_REGEX_BACKUP", 
                        "OPERATOR:" + _.get(record, 'operator', null),
                        `APP_VERSION:${_.get(record,'appVersion', null)}`
                    ]);
                    self.L.log('SMS_PARSING_POSTPAID_BILL_PAID :: publishFailedRecordInKafka', 'Message published successfully in Kafka', ' on topic FAILED_SMS_PARSING_REGEX_BACKUP');
                }
                return cb();
            }, [200, 800]);
        }
    }

    setCustInfoExtraDetails(record) {
        let self = this;
        let dbRecord,
            dbExtra = {}
        if(record.isRecordExist){
            try{
            dbRecord = _.get(record, 'dbData[0]', {});
            dbExtra = JSON.parse(_.get(dbRecord, 'extra', {}));
            if(!dbExtra) dbExtra = {};
            }catch(err){
                self.L.error("getBillsData", "Error in JSON parsing" + err);
            }
        }
        let custInfoValues = {};
        let extraDetails = dbExtra;
        extraDetails.billSource = 'sms_parsed';
        extraDetails.updated_source = 'sms';
        extraDetails.updated_data_source = "SMS_PARSING_DWH_PAYMENT";
        extraDetails.created_source = "SMS_PARSING_DWH_PAYMENT";
        if (self.smsParsingBillsDwhRealtime) {
            extraDetails.updated_data_source = "SMS_PARSING_DWH_REALTIME_PAYMENT";
            extraDetails.created_source = "SMS_PARSING_DWH_REALTIME_PAYMENT";
        }
        let recon_id = utility.generateReconID(_.get(record , 'rechargeNumber',_.get(record,'recharge_number','')), _.toLower(record.service) == "financial services" ? _.get(record, 'bankName', null) : _.get(record, 'operator', null) ,_.get(record, 'amount', null) ,  _.get(record, 'dueDate', null) , _.get(record, 'billDate', null));
        extraDetails.recon_id = recon_id;
        extraDetails.user_type = "RU";
        extraDetails.lastSuccessBFD = _.get(dbRecord, 'bill_fetch_date', null);
        extraDetails.billFetchDate = MOMENT(record.smsTimeStamp).isValid() ? MOMENT(record.smsTimeStamp).format('YYYY-MM-DD HH:mm:ss') : MOMENT().format('YYYY-MM-DD HH:mm:ss'),
        extraDetails.lastDueDt = _.get(dbRecord, 'due_date', null);
        extraDetails.lastBillDt = _.get(dbRecord, 'bill_date', null);
        extraDetails.lastAmount = _.get(dbRecord, 'amount', null);
        if(_.get(record, 'isRuSmsParsing', false)==true){
            _.set(extraDetails,'isRuSmsParsing', true);
        }
        if(_.get(record,'isDwhSmsParsing',false) == true){
            _.set(extraDetails,'isDwhSmsParsing',true)
        } else if(_.get(record,'isDwhSmsParsingRealtime',false) == true) {
            _.set(extraDetails,'isDwhSmsParsingRealtime',true)
        }
        if(_.get(record, 'partialRecordFound', false)){
            _.set(extraDetails, 'source_subtype_2', 'PARTIAL_BILL');
        }else{
            _.set(extraDetails, 'source_subtype_2', 'FULL_BILL');
        }
        if(_.get(extraDetails, 'errorCounters', null)){
            extraDetails.errorCounters = {};
        }
        delete extraDetails.upmsRegistrationNumber;
        delete extraDetails.upmsBillPaymentToken;
        if(self.prepaidFlowManager.isPrepaidFlowAllowed(record)) {
            _.set(extraDetails, 'isPrepaid', "1");
            if (!(_.get(record, 'partialRecordFound', false)) && _.get(extraDetails, 'partialBillState') === "LOW_BALANCE_PARTIAL_RECORD") {
                delete extraDetails.partialBillState;
            }
        }

        custInfoValues.msgId = _.get(record,'msgId','');
        custInfoValues.sms_id = _.get(record,'msgId','');
        custInfoValues.sms_date_time = _.get(record,'sms_date_time','');
        custInfoValues.sender_id = _.get(record,'senderId','');
        custInfoValues.dwh_classId = _.get(record,'dwh_classId',null);
        record.extra = JSON.stringify(extraDetails, function (key, value) {
            if (key && typeof (value) === 'string')
                return value.replace(/[?']/g, "");
            else
                return value;
        }),
        record.customerOtherInfo = JSON.stringify(custInfoValues, function (key, value) {
            if (key && typeof (value) === 'string')
                return value.replace(/[?']/g, "");
            else
                return value;
        })
    }

    checkIfOldSMSRejectionCase(processedRecord){
        let self = this;
        let smsDateTime = _.get(processedRecord, 'paymentDate', null);
        let oldSMSRejectAllowedDays = _.get(self.config, ['DYNAMIC_CONFIG', 'OLD_SMS_BILL_PAID_REJECTION_CONFIG', _.get(processedRecord, 'category', 'N.A'), 'DAY_MARGIN'], null);

        if(smsDateTime && oldSMSRejectAllowedDays != null && MOMENT().startOf('day').diff(MOMENT(smsDateTime).startOf('day'), 'days') > oldSMSRejectAllowedDays && processedRecord.dueDate == null){
            return true;
        }
        return false;
    }
}

export default postpaidSmsParsingBillPaid;