import ASYNC from 'async'
import _ from 'lodash';
import utility from '../../lib';
import OS from 'os';
import DthSmsParsing from './dthSmsParsing';
import Q from 'q'
import KafkaConsumerChecks from '../../lib/kafkaConsumerChecks';
import KafkaConsumer from '../../lib/KafkaConsumer';

class DthSmsParsingBillsIndex {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.dthDwhRealTimeSmsParsing = _.get(options,'dthDwhRealTimeSmsParsing',false);
        if(this.dthDwhRealTimeSmsParsing) _.set(options,'smsParsingBillsDwhRealtime',true);
        this.dthSmsParsing = new DthSmsParsing(options, this);
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'BATCHSIZE'], 2) : 500;
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SMS_PARSING_CC_BILLS', 'DELAY'], 5 * 60 * 1000) : 0;
        this.RUreadsKafkaTime =  new Date().getTime();       // Time at which RU reads from the KAFKA
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);

    }

    async start() {
        let self = this;
        // self.smsParsingSyncCCBillLib.refreshDCATCacheData(self.refereshIntervalForCategoryData);
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('SMS_PARSING_DTH_BILL :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('SMS_PARSING_DTH_BILL :: start', 'Kafka Configured successfully !!');
            }
        });
      
    }

    configureKafka(done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSER_DTH_CABLE_CYLINDER", 'STATUS:ERROR','TYPE:CT_EVENTS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update events to non paytm bills pipeline 
                 */
                self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
                });
                this.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSER_DTH_CABLE_CYLINDER", 'STATUS:ERROR','TYPE:NON_PAYTM_RECORDS_PUBLISHER','SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.billFetchKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                this.billFetchKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            next => {
                /**
                 * Kafka publisher to update reminder bill fetch
                 */
                self.billFetchRealTimeKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE_REALTIME.HOSTS
                });
                this.billFetchRealTimeKafkaPublisher.initProducer('high', function (error) {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DWH_REALTIME", 'STATUS:ERROR', 'TYPE:PRODUCER', 'TOPIC:REMINDER_BILL_FETCH_REALTIME', 'SOURCE:MAIN_FLOW']);
                    }
                    return next(error)
                });
            },
            
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : SMS_PARSER_DTH_CABLE_CYLINDER');

                let kafkaConsumerObj = {
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.SMS_PARSING_DTH_DWH.HOSTS'),
                        "groupId": "smsParsingBillPayment-dth-consumer",
                        "topics": _.get(self.config.KAFKA, 'SERVICES.SMS_PARSING_DTH_DWH.TOPIC'),
                        "id": `smsParsingBillPayment-consumer_${OS.hostname()}_${process.pid}`,
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
                    };
                
                
                self.kafkasmsParsingBillPaymentConsumer = new KafkaConsumer(kafkaConsumerObj);
                self.kafkasmsParsingBillPaymentConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if(error){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:ERROR','TYPE:SMS_PARSING_DTH_CONSUMER','SOURCE:MAIN_FLOW']);
                    }
                    if (!error) {
                        self.L.log("configureKafka", `consumer of topic : SMS_PARSING_DTH Configured`);
                    }
                    return next(error);
                });
            },

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }

    execSteps(records, resolveOffset , topic , partition , cb) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        self.RUreadsKafkaTime = new Date().getTime();

        let startTime = new Date().getTime();
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
        } else {
            self.L.critical('SMS_PARSING_DTH:execSteps', `No valid kafka records found. Received records-`, records);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return cb();
        }


        self.L.log('SMS_PARSING_DTH:execSteps:: ', `Processing ${records.length} Dth SMS Parsing Bill Payment data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:SMS_PARSING_DTH', 'STATUS:TRAFFIC', 'SOURCE:MAIN_FLOW']);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 2);
                });
            },
            async (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("DthSmsParsingBills", records, topic , partition);

                await resolveOffset(lastMessage.offset)
                self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));

                    // Resume consumer now


                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time: ', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:SMS_PARSING_DTH_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        return cb();
                    }, self.kafkaBatchDelay);
        }) 
    }

    processBatch(records, done) {
        let self = this,
            currentPointer = 0;
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let record = records[currentPointer];
                currentPointer = currentPointer + 1;
                self.processData(record, () => {
                    setTimeout(() => {
                        callback();
                    }, 1);
                });
            },
            (err) => {
                if (err) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                    self.L.error("Invalid records found! :", JSON.stringify(records));
                }
                return done()
            }
        );
    }
    processData(record, done) {
        let self = this;
        try {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:TRAFFIC', `PARTITION:${_.get(record, 'partition', null)}`, `TOPIC:${_.get(record, 'topic', null)}`]);
            record = JSON.parse(_.get(record, 'value', null));
            if (!record.data) {
                self.L.critical('SMS_PARSING_DTH:processData', `Invalid Kafka record received. data key is missing`, record);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:INVALID_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                return done();
            }
        }
        catch (error) {
            if (error) {
                self.L.critical('SMS_PARSING_DTH:processData', `Invalid Kafka record received`, record,' with error ', error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            }
            return done();
        }


        let kafkaTopic = _.get(record, 'kafka_topic', null);
        if (!_.isArray(kafkaTopic) || !_.isArray(_.get(record, 'data', null))) {
            self.L.critical('SMS_PARSING_DTH:processData', `Invalid Kafka record received`, JSON.stringify(record));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSER_DTH_CABLE_CYLINDER", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
            return done();
        }
        else if (kafkaTopic[0] == "SMS_PARSER_DTH_CABLE_CYLINDER") {
            let record_data = _.get(record, 'data', null);

            if(record_data.length < 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:EMPTY_DATA', 'SOURCE:MAIN_FLOW']);
                self.L.log(`DthSmsParsingBills :: Empty sms Data found`);
                return done();
            }

            let level_2_category = _.get(record_data[0], 'level_2_category', null);

            utility._sendMetricsToDD(1, [
                'REQUEST_TYPE:DTH_SMS_PARSING', 
                `SERVICE:DTH`, 
                'STATUS:TRAFFIC', 
                'TYPE:LEVEL_2_CATEGORY',
                'LEVEL_2_CATEGORY:' + level_2_category, 
                'ORIGIN:'+kafkaTopic[0]
            ]);

            if(level_2_category != 1){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_DTH", 'STATUS:LEVEL_2_CATEGORY_NOT_1', 'SOURCE:MAIN_FLOW']);
                self.L.log(`DthSmsParsingBills :: level_2_category is not 1`);
                return done();
            }
            
            self.L.log(`DthSmsParsingBills :: executing smsSmsParsing flow as per data level_2_category : ${level_2_category}`);
            if(level_2_category == 1){
                return ASYNC.map(
                    record.data,
                    (smsData, next) => {
                        self.L.log(`DthSmsParsingBills :: ready to execute strategy for  :`,JSON.stringify(smsData));
                        smsData = self.formatData(smsData);
                        self.dthSmsParsing.executeStrategy(() => {
                            return next();
                        }, smsData,self);
                    },
                    err => {
                        if (err) {
                            self.L.critical('SMS_PARSING_POSTPAID:processData', `Invalid Kafka record received`, err);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING_POSTPAID", 'STATUS:INVALID_JSON_PAYLOAD', 'SOURCE:MAIN_FLOW']);
                        }
                        return done();
                    }
                )
            }
            else{
                return done();
            }
        }
        else {
            return ASYNC.map(
                record.data,
                (smsData, next) => {
                    self.defaultStrategy(() => {
                        return next();
                    }, smsData);
                },
                err => {
                    self.L.error("Invalid records found! :", JSON.stringify(record));
                    return done();
                }
            )
        }

    }

    defaultStrategy(done, record) {
        this.L.log('SMS_PARSER_DTH_CABLE_CYLINDER:defaultStrategy:: ', "Not applying any logic for record ", JSON.stringify(record));
        utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSER_DTH_CABLE_CYLINDER", 'STATUS:NO_STRATEGY', 'SOURCE:MAIN_FLOW']);
        return done();
    }

    getTimeInMs(date) {
        return date ? new Date(date).getTime() : new Date().getTime();
    }

    suspendOperations() {

        var self = this,
            deferred = Q.defer();
        self.L.log(`smsParsingBillPayment::suspendOperations kafka consumer shutdown initiated`);

        Q()
            .then(function () {
                self.kafkasmsParsingBillPaymentConsumer.close(function (error, res) {
                    if (error) {
                        self.L.error(`smsParsingBillPayment::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                        return error;
                    }
                    self.L.info(`smsParsingBillPayment::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                })
            })
            .then(function () {
                self.L.log(`smsParsingBillPayment::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function (err) {
                self.L.error(`smsParsingBillPayment::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
        return deferred.promise;
    }

    formatRechargeNumber(formattedRecord) {
        //perform validation on prepared recorld
        // let operator = _.toLower(_.get(formattedRecord,'operator',null));
        // let rechargeNumber = _.get(formattedRecord,'rechargeNumber',null);
        // if(operator == "assam power distribution company ltd. (apdcl)" && rechargeNumber && rechargeNumber.length === 11){
        //     formattedRecord.rechargeNumber = '0' + rechargeNumber;
        // }
        // if(operator == "tneb" && rechargeNumber && rechargeNumber.length >= 9 && rechargeNumber.length <= 12) {
        //     formattedRecord.rechargeNumber = '0' + rechargeNumber;
        // }
        return formattedRecord;
    }

    formatData(smsData) {
        let self = this;
        let parsedDetails = _.get(smsData, 'dth_cable_cylinder_details', null);

        let formattedRecord = {
            "cId": _.get(smsData, 'cId', null),
            "smsDateTime": _.get(smsData, 'smsDateTime', null),
            "timestamp": _.get(smsData, 'timestamp', null),
            "deviceDateTime": _.get(smsData, 'deviceDateTime', null),
            "collector_timestamp": _.get(smsData, 'collector_timestamp', null),
            "uploadTime": _.get(smsData, 'uploadTime', null),
            "smsOperator": _.get(smsData, 'smsOperator', null),
            "smsReceiver": _.get(smsData, 'smsReceiver', null),
            "smsSenderID": _.get(smsData, 'smsSenderID', null),
            "smsUUID": _.get(smsData, 'smsUUID', null),
            "category": _.get(smsData, 'predicted_category', null),
            "rechargeNumber": _.get(parsedDetails, 'recharge_number', null),
            "operator": _.get(parsedDetails, 'operator', null),
            "amount": _.get(parsedDetails, 'amount', null),
            "dueDate": _.get(parsedDetails, 'due_date', null),
            "rechargeNumber2": _.get(parsedDetails, 'recharge_number_2', null),
            "level_2_category": _.get(smsData, 'level_2_category', null),
            "circle": _.get(smsData, 'circle', null)
        };

        formattedRecord = self.formatRechargeNumber(formattedRecord);

            if(self.dthDwhRealTimeSmsParsing) {
                formattedRecord.isDwhSmsParsingRealtime = true;
            }else {
                formattedRecord.isDwhSmsParsing = true;
            }
        if(typeof _.get(formattedRecord,"smsDateTime") == "string" && _.get(formattedRecord,"smsDateTime") != null && self.isDwhSmsParsingRealtime == true) {
            _.set(formattedRecord,"smsDateTime", parseInt(_.get(formattedRecord,"smsDateTime")));
        }
            
        utility._sendMetricsToDD(1, [
            'REQUEST_TYPE:DTH_INDEX', 
            `SERVICE:DTH`, 
            'STATUS:TRAFFIC', 
            'TYPE:OPERATOR',
            'LEVEL_2_CATEGORY:' + formattedRecord.level_2_category, 
            `OPERATOR:${_.get(formattedRecord,'operator',"NO_OPERATOR")}`,
            `ORIGIN:${self.isDwhSmsParsingRealtime == true ? "SMS_PARSING_DWH_REALTIME" : "SMS_PARSING_DWH"}`
        ]);
        return formattedRecord;
    }
}

export default DthSmsParsingBillsIndex;
