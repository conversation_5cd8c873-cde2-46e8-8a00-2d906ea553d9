import VALIDATOR from 'validator'
import MOMENT    from 'moment'
import _         from 'lodash'
import <PERSON>Y<PERSON>     from 'async'
import BILLS     from '../models/bills'

let L = null;

/*
   This service pulls the new records from Elasticsearch
 */
class Poller {
   
    constructor(options) {
        L = options.L;
        L.info('Poller', 'initialising Poller...');
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.esInstance = options.esInstance;
        this.bills = new BILLS(options);
        this.esBatchSize = 1000;
        this.servicePeriod = 10; //2 min
        this.queries = this._prepareBaseQueries();
    }

    start () {
    	let self = this;
        L.info('Poller','starting the service loop');
        function startMainLoop() {
            self._updateTimeFilterInQueries();
            self._runQueriesAndSaveData(function() {
                L.log('Poller','session finished successfully');
                setTimeout(function() {
                    startMainLoop();
                }, self.servicePeriod * 1000);  //converting period in milli seconds
            });
        }
        startMainLoop();
    }

    /*
       Method to get template Es Query
    */
    _getESQuery (argument) {
        return {
            query: {
                filtered: {
                    filter  : {
                        bool : {
                            must : [
                                {
                                    range: {
                                        timestamps_init: {
                                          gte : '',
                                          lte : ''
                                        }
                                    } 
                                },
                                {
                                    term : {
                                        productInfo_paytype : ''
                                    }
                                }
                            ],
                            should : [] 
                        } 
                    } 
                } 
            }
        }
    }

    /*
       This method will prepare queries according to the paytypes
       It can be possible for a gateway to appear in more than 1 paytype groups
       Once, the below code executes, Its (paytypeSets) structure will become:
            queries :  {
                              prepaid : <prepaid query>,

                              postpaid : {
                                 .. same ..
                              },

                              recharge : {

                              }           
                           }
    */
    _prepareBaseQueries() {
        L.info('_prepareBaseQueries', 'preparing queries for  elasticsearch')
        let self          = this,
            paytypeGwMap  = {},
            queries       = {},
            templateQuery = self._getESQuery(),
            gateways      = _.get(self.config, 'POLLER_CONFIG.BILL_FETCH_GATEWAYS', {});
         
        Object.keys(gateways).forEach( gwName => {
            var gwObj = _.get(self.config, ['SUBSCRIBER_CONFIG', 'BILL_FETCH_GATEWAYS', gwName], null);
            var operators = _.get(gwObj, 'operators' , null);
            if(operators) {
                var paytypeOpMap = {};
                Object.keys(operators).forEach(function(operator) {
                    operators[operator].forEach(function(paytype){
                        if(!queries[paytype]) {
                            queries[paytype] = _.cloneDeep(templateQuery);
                            _.set(queries[paytype],"query.filtered.filter.bool.must[1].term.productInfo_paytype", paytype);
                        }
                        if(!paytypeOpMap[paytype]){
                            _.set(paytypeOpMap, paytype, []);
                        }
                        paytypeOpMap[paytype].push(operator);
                    });
                });
                Object.keys(paytypeOpMap).forEach(function(paytype){
                    var andJson = {
                        and : [
                            {
                                term: { currentGw: gwName } 
                            }, 
                            {
                                terms: { productInfo_operator: paytypeOpMap[paytype] }
                            } 
                        ] 
                    }
                    _.get(queries[paytype], 'query.filtered.filter.bool.should', []).push(andJson);
                });
            }
            else {
                var paytypes = _.get(gwObj, 'paytypes', ['postpaid']); //using 'postpaid' as default paytype for a gateway'
                paytypes.forEach(function(paytype) {
                    if(!paytypeGwMap[paytype]){
                        _.set(paytypeGwMap, paytype, []);
                    }
                    _.get(paytypeGwMap, paytype, []).push(gwName);
                    if(!queries[paytype]) {
                        queries[paytype] = _.cloneDeep(templateQuery);
                        _.set(queries[paytype], 'query.filtered.filter.bool.must[1].term.productInfo_paytype', paytype);
                    }
                });
            }    
        });

        Object.keys(paytypeGwMap).forEach(function(paytype){
            var gwJson = {
                terms : {
                    currentGw: paytypeGwMap[paytype] 
                } 
            };
            _.get(queries, paytype+'.query.filtered.filter.bool.should', []).push(gwJson);
        });

        return queries; 
    }

    _updateTimeFilterInQueries() {
        let self = this,
            from = MOMENT().subtract(self.servicePeriod + 10, 'seconds').toISOString(),
            to   = MOMENT().toISOString();
        L.info('_updateTimeFilterInQueries', 'from: ', from, ', to: ', to);
        Object.keys(self.queries).forEach(queryId => {
            _.set(self.queries[queryId], 'query.filtered.filter.bool.must[0].range.timestamps_init.gte', from);
            _.set(self.queries[queryId], 'query.filtered.filter.bool.must[0].range.timestamps_init.lte', to);
        });
    }

    _runQueriesAndSaveData(done) {
        let self = this;
        ASYNC.eachOf(
            self.queries,

            (query, queryId, next) => {
                self._fetchDataFromES(query, (err, records) => {
                    if(err) {
                        L.error('_runQueriesAndSaveData', 'Error occurred for fetching data for paytype: ', queryId, err);
                        //Intentionally not sending err to next, bcoz it will stop remaining queries
                        next();
                    }
                    else {
                        self._saveData(records, next);
                    }
                });
            },

            err => {
                if (err) {
                   L.error('_runQueriesAndSaveData', err);
                }
                done();
            }
        );
    }

    _fetchDataFromES(query, done) {
        let self    = this,
            records = [];
        self.esInstance.search({
            index        : _.get(self.config, 'ELASTICSEARCH.RECHARGE_INDEX_NAME', 'recharge_mis'),
            // Set to 30 seconds because we are calling right back
            scroll       : '30s',
            search_type  : 'scan',
            body         : query
        }, function getMoreUntilDone (error, response) {
            if(error) {
                done(error);
            }
            else {
                // collect the doc from each response
                response.hits.hits.forEach( hit => {
                    records.push(hit._source);
                });
                if (response.hits.total !== records.length) {
                    // now we can call scroll over and over
                    self.esInstance.scroll({
                      scrollId: response._scroll_id,
                      scroll: '30s'
                    }, getMoreUntilDone);
                } 
                else {
                    L.info('Total records fetched from this query => ', records.length);
                    done(null, records);
                }
            }
        });
    }

    _saveData (records, done) {
        L.info('saving records to DB')
        let self      = this         
        
        ASYNC.map(
            records,

            (record, next) => {
                let params    = self._extractParams(record),
                    tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', params.operator], null)
                if(tableName) {
                    self.bills.createBill(function() {
                        next()
                    }, tableName, params)
                }
                else {
                    L.error('_saveData','tableName not found for record => ', record.customer_id, record.recharge_number);
                }
            },

            err => {
                if(err) {
                    return done(err)
                }
                done()
            }
        );  
    }

    _extractParams (doc) {
        let currentGw      = _.get(doc,'currentGw',''),
            firstBillDelay = _.get(this.config, ['SUBSCRIBER_CONFIG', 'BILL_FETCH_GATEWAYS', currentGw, 'firstBillDelay'], 5),
            userData       = {};

        for (let key in doc) {
            if (key.indexOf('userData_recharge_number', 0) > -1) {
                let parsedKey = key.slice(9);
                userData[parsedKey] = doc[key];
            }
        }
        
        return {
            customer_id         : _.get(doc,'customerInfo_customer_id',''),
            recharge_number     : _.get(doc,'userData_recharge_number',''),
            product_id          : _.get(doc,'catalogProductID',''),
            operator            : _.get(doc,'productInfo_operator',''),
            service             : _.get(doc,'productInfo_service',''),
            paytype             : _.get(doc,'productInfo_paytype',''),
            circle              : _.get(doc,'productInfo_circle',''),
            customer_mobile     : _.get(doc,'customerInfo_customer_phone',''),
            customer_email      : _.get(doc,'customerInfo_customer_email',''),
            payment_channel     : _.get(doc,'customerInfo_channel_id',''),
            gateway             : currentGw,
            next_bill_fetch_date: firstBillDelay < 0 ? MOMENT().add(Math.abs(Number(firstBillDelay)),'months').format('YYYY-MM-DD HH:mm:ss'): MOMENT().add(firstBillDelay,'days').format('YYYY-MM-DD HH:mm:ss'),
            amount              : 0,
            due_date            : null,
            bill_fetch_date     : null,
            retry_count         : 0,
            status              : 'pending',
            reason              : '',
            user_data           : userData
        }
    }

}


export default Poller