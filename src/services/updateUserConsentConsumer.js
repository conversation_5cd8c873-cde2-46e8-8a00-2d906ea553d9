'use strict';

import MOMENT from 'moment';
import _ from 'lodash';
import MODELS from '../models'
import OS from 'os'
import ASYNC from 'async'
import utility from '../lib'
import UpdateUserConsentLibrary from '../lib/updateUserConsent'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class updateUserConsentConsumer {
    constructor(options) {
        this.L = options.L;
        this.stopService = false;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.transactionCounterModel = new MODELS.AllTransactionsCounter(options);
        this.updateUserConsentLibrary = new UpdateUserConsentLibrary(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        // TODO entry 
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'UPDATE_USER_CONSENT_CONSUMER', 'BATCHSIZE'], 2) : 500; 
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'UPDATE_USER_CONSENT_CONSUMER', 'DELAY'], 5 * 60 * 1000) : 0;
    }

    start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('updateUserConsentConsumer :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('updateUserConsentConsumer :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : VALIDATION_TOPICS');

                // Initialize consumer of topic VALIDATION_TOPICS
                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.RECHARGE.HOSTS'),
                    "groupId": "updateUserConsentConsumer-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.VALIDATION_SYNC.VALIDATION_TOPICS'),
                    "id": `updateUserConsentConsumer-consumer_${OS.hostname()}_${process.pid}`, 
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };

                self.kafkaUpdateUserConsentConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkaUpdateUserConsentConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : VALIDATION_TOPICS Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            }
            return done(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
             startTime = new Date().getTime(),
            currentPointer = 0, lastMessage;

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaUpdateUserConsentConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} VALIDATION_TOPICS data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_TRAFFIC']);
        
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("updateUserConsentConsumer", records);
                
                self.kafkaUpdateUserConsentConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ', records.length);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:updateUserConsentConsumer", "TIME_TAKEN:" + executionTime]);


                    // Resume consumer now
                    if(self.stopService) {
                        self.L.log("Gracefull shutdown, stop resuming consumer ");
                        return;
                    }
                    setTimeout(function () {
                        self.kafkaUpdateUserConsentConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processRecords(() => {
                    return next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processRecords(done, record) {
        let self = this;
            
        try {
            let error = null,
                processedRecord = null;

            [error, record] = self.validateRecord(record);

            if (error) { 
                self.L.error(`processRecords`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:VALIDATION_FAILED']);
                return done(error);
            }
            processedRecord = record;

            ASYNC.waterfall([
                next => {
                    let dbFormatData = self.updateUserConsentLibrary.getDbRecordToUpdate(processedRecord);
                    self.L.log('processRecords', `Updating table for record having debugkey: ${processedRecord.debugKey}`);
                    self.transactionCounterModel.writeCustomerDetailsForUpdateUserConsent(function (error, response) {
                        if (error) {
                            self.L.critical(`processRecords`,`Error occured while updating table debugkey: ${processedRecord.debugKey} Error Msg:${error}`);
                            return next(error);
                        }
                        return next();
                    }, dbFormatData);
                },
                (next) => {
                    let requestData = self.updateUserConsentLibrary.getRequestDataToUpdate(processedRecord);
                    self.L.log('processRecords', `Updating UPS for record having debugkey: ${processedRecord.debugKey}`);
                    self.updateUserConsentLibrary.updateUserPreferences(function (error, response) {
                        if (error) {
                            self.L.critical(`processRecords`,`Error occured while updating UPS debugkey: ${processedRecord.debugKey} Error Msg:${error}`);
                            return next(error);
                        }
                        return next();
                    }, requestData, null, 'updateUserConsentConsumer');
                    /**
                     * Don't do anything after update in UPS
                     * retry in case of UPS update API failure
                     */
                }
            ], function (error) {
                if(error) {
                    self.L.critical(`processRecords`,`Error occured for record:${record}`,error);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:MAIN_FLOW_EXECUTION_FAILED']);
                }
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:SUCCESS', 'TYPE:MAIN_FLOW_EXECUTION']);
                return done(null);
            });
        } catch(err) {
            self.L.critical(`processRecords`,`Exception occured for record:${record}`,err);
            return done(null);
        }
    }

    /**
     * 
     * @param {*} record 
     */
     validateRecord(record){
        if (!record) return ['Invalid record', record];
        let self = this;
        try {
            if (!_.has(record, 'value')) return ['Invalid Kafka record received', record];
            
            record = JSON.parse(_.get(record, 'value', null));
        } catch (error) {
            if (error) {
                self.L.critical('validateAndProcessRecord', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR' , 'TYPE:PARSING_ERROR']);
                return ['Kafka record Parsing Error', record];
            }
        }
        
        let customer_id = _.get(record, 'customerInfo_customer_id', 0);
        let productId = _.get(record, 'catalogProductID', 0);   // check for product id is Required or not
        let preferenceValue = _.get(record, ['metaData' , 'consent' , 'whatsapp'], null);
        
        record = {
            "debugKey" : `customer_id:${customer_id}_preferenceValue:${preferenceValue}`
        };

        if (!customer_id) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_CUSTOMER_ID']);
            return ['Missing customer_id', record];
        }
        if (typeof preferenceValue != 'boolean') {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR' , 'TYPE:INVALID_CONSENT_VALUE']);
            return ['Invalid/ Missing metaData.consent.whatsapp data', record];
        }
        preferenceValue = preferenceValue == true ? 1 : 0;

        /**
         * Incorporate any further key like timestamp etc.
         */
        record = { 
            "customer_id": customer_id,
            "preferenceValue" : preferenceValue,
            "debugKey" : `customer_id:${customer_id}_preferenceValue:${preferenceValue}`
        };
        return [null , record];
    }

    suspendOperations(){
        this.stopService = true;
        this.L.info("updateUserConsentConsumer::stopService");
    }

}

export default updateUserConsentConsumer;
