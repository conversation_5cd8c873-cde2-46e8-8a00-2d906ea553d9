import _        from 'lodash';
import <PERSON>YNC    from 'async';
import MOMEN<PERSON>   from 'moment';
import OS from 'os';

import utility from '../lib';
import KafkaConsumer from '../lib/KafkaConsumer';
import KafkaConsumerChecks  from '../lib/kafkaConsumerChecks';
import cassandraBills       from '../models/cassandraBills'

const DATE_FORMAT = "YYYY-MM-DD HH:mm:ss";

class CronWhatsAppWhitelistCustomerConsumer {
    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        self.cassandraBills = new cassandraBills(options);
        self.infraUtils = options.INFRAUTILS;
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', 'BATCH_DELAY'], 5*60*1000) : 500;
    }
    start() {
        let self = this;
        self.L.log('cronWhatsAppWhitelistCustomerConsumer::', `Starting service`);
        ASYNC.waterfall([
            
            (next) => {
                try {
                    self.consumer = new KafkaConsumer({
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS'), 
                        "groupId": 'whatsappCustomerWhitelistConsumer',
                        "topics": _.get(self.config.KAFKA, 'SERVICES.WHATSAPP_CUSTOMER_WHITELIST_INGESTOR.TOPIC'),
                        "id": "whatsapp-customer-whitelist-consumer-" + OS.hostname(),
                        "maxBytes" : _.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.WHATSAPP_CUSTOMER_WHITELIST_INGESTOR.BATCHSIZE',1000000),
                        sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                        maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT.WHATSAPP_CUSTOMER_WHITELIST_INGESTOR',30*60*1000)
                    });
        
                    self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                        if (error)
                            self.L.critical("cronWhatsAppWhitelistCustomerConsumer:: consumer Configured cannot start.", error);
                        else if (!error)
                            self.L.log("cronWhatsAppWhitelistCustomerConsumer:: consumer Configured");
                        return next(error);
                    });
                } catch (error) {
                    return next(error)
                }
            }
        ], (error) => {
            if (error) {
                self.L.critical('cronWhatsAppWhitelistCustomerConsumer :: start', 'Error while starting service...', error);
                process.exit(0)
            } else {
                self.L.log('cronWhatsAppWhitelistCustomerConsumer:: start', 'Service started....');
            }
        })
    }
    _processKafkaData(records, resolveOffset, topic, partition, cb) {
        let startTime = new Date().getTime();
        let self = this;
        let chunkSize = _.get(self.config, ['DYNAMIC_CONFIG', 'CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', 'COMMON', 'CHUNKSIZE'], 1);
        let lastMessage,
        rowData = null,
        recordsToProcess = [];
        
        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('c::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('cronWhatsAppWhitelistCustomerConsumer ::_processKafkaData error while reading kafka');
            return cb();
        }
        
        records.forEach(row => {
            if(row && row.value) {
                try {
                    rowData = JSON.parse(row.value);
                    recordsToProcess.push(rowData);
                } catch (error) {
                    utility._sendMetricsToDD(1, [
                        'REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', 
                        'STATUS:INVALID_JSON_PAYLOAD'
                    ]);
                    self.L.error("cronWhatsAppWhitelistCustomerConsumer::_processKafkaData", "Failed to parse whatsapp customer whitelist ingestion data topic, partition, offset, timestamp ::", row.topic, row.partition, row.offset, row.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR', 
                    'STATUS:INVALID_PAYLOAD'
                ]);
                self.L.error("cronWhatsAppWhitelistCustomerConsumer::_processKafkaData", "Unable to get valid data from kafka topic, partition, offset, timestamp ::", row.topic, row.partition, row.offset, row.timestamp);
            }
        });

        self.L.log('cronWhatsAppWhitelistCustomerConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} data !!`);
        
        ASYNC.eachLimit(recordsToProcess, chunkSize, self.processWhatsappCutomerWhitelistRecord.bind(self), async (err) => {
            
            self.kafkaConsumerChecks.findOffsetDuplicates("CronWhatsAppWhitelistCustomer", records,topic , partition);
            
            if (err) {
                self.L.error("cronWhatsAppWhitelistCustomerConsumer::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            } else {
                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                recordsToProcess = [];
                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds ',records.length);
                utility._sendMetricsToDD(1, [
                    'REQUEST_TYPE:CONSUMER_STATUS', 
                    'STATUS:SUCCESS', 
                    "SOURCE:CRON_WHATSAPP_WHITELIST_CUSTOMER", 
                    "TIME_TAKEN:" + executionTime
                ]);
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
            }
        });
    } 
    async processWhatsappCutomerWhitelistRecord(record,done){
        let self = this,
        customer_id= record["customer_id"],
        service =   _.get(record, 'service', null)?.toLowerCase(),
        paytype =  _.get(record, 'paytype', null)?.toLowerCase(),
        template_name= record["template_name"],
        notification_type= _.get(record, 'notification_type', null)?.toUpperCase(),
        day_no= record["day_no"],
        operator= _.get(record, 'operator', 'ALL'),
        expiry_date= record["expiry_date"],
        template_id= record["template_id"],
        fileName = record["fileName"];

        if (operator.trim() == '') {
            operator = 'ALL';
        }
        
        if(!customer_id || _.isEmpty(customer_id)  || !service || _.isEmpty(service) || !paytype || _.isEmpty(paytype) || !template_name || _.isEmpty(template_name) 
            || !notification_type || _.isEmpty(notification_type) || isNaN(day_no) || !operator || _.isEmpty(operator) ) {
            self.L.error("processRecords:: ","error while processing record:",JSON.stringify(record), customer_id, service, paytype, template_name, notification_type, day_no, operator);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_PAYLOAD_PROCESSING", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
            return done();
        }
        let formattedExpiryDate;
        try {
            const dateFormats = ["DD-MM-YYYY", "YYYY-MM-DD"];
            formattedExpiryDate = MOMENT(expiry_date, dateFormats, true);
            let threeMonthsFromNow = MOMENT().add(6, 'months');
            if (!formattedExpiryDate.isValid() || formattedExpiryDate.isAfter(threeMonthsFromNow)) {
                self.L.error("processRecords:: ", "expiry_date is not within the next 3 months:", expiry_date);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:INVALID_EXPIRY_DATE", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
                return done();
            }
        } catch (error) {
            self.L.error("processRecords:: ", "error while formatting date:", expiry_date);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_EXIPRY_DATE", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
            return done();
        }


        let params = [customer_id,service,paytype,template_name,notification_type,day_no,operator,template_id,formattedExpiryDate.format(DATE_FORMAT),MOMENT().format(DATE_FORMAT),MOMENT().format(DATE_FORMAT)];

        try {
            let existingRecords = await self.cassandraBills.getCustomerData(customer_id, service, paytype);
            for (let existingRecord of existingRecords) {
                if (existingRecord.operator === operator) {
                     // Check if the existing record is the same as the new record
                     self.L.log("existing templatename {} new templatename {} existing notification_type {} new notification_type {} existing day_no {} new day_no {} existing template_id {} new template_id {} existing expiry_date {} new expiry_date {}", existingRecord.template_name, template_name, existingRecord.notification_type, notification_type, existingRecord.day_value, day_no, existingRecord.template_id, template_id, existingRecord.expiry_date, formattedExpiryDate.format(DATE_FORMAT));
                    if (existingRecord.template_name === template_name &&
                        existingRecord.notification_type === notification_type &&
                        existingRecord.day_value === parseInt(day_no, 10) &&
                        existingRecord.template_id === template_id) {
                        let existingRecordExpiryDate = MOMENT(existingRecord.expiry_date).format(DATE_FORMAT);

                        if (existingRecordExpiryDate === formattedExpiryDate.format(DATE_FORMAT)) {
                            self.L.log("processRecords::", "record is identical, skipping:", JSON.stringify(existingRecord));
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:SUCCESS', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_RECORD_IDENTICAL", `SERVICE:${service}`, `PAYTYPE:${paytype}`, `FILE_NAME:${fileName}`]);
                            return done();
                        } else {
                            self.L.log("processRecords::", "record is identical except for expiry date, updating:", JSON.stringify(existingRecord));
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:SUCCESS', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_RECORD_EXPIRY_UPDATE", `SERVICE:${service}`, `PAYTYPE:${paytype}`, `FILE_NAME:${fileName}`]);
                            break; // No need to delete, just update
                        }
                    }

                    let deleteQueryParams = [existingRecord.customer_id,existingRecord.service,existingRecord.paytype,existingRecord.notification_type,existingRecord.day_value,existingRecord.template_name,existingRecord.operator];
                    self.L.log("processRecords::", "record already exists:", JSON.stringify(existingRecord));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:SUCCESS', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_RECORD_EXIST", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
                    await self.cassandraBills.deleteWhatsappCustomerWhitelistRecord(deleteQueryParams);
                    self.L.log("processRecords::", "record deleted successfully:", JSON.stringify(existingRecord));
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:SUCCESS', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_RECORD_DELETED", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
                }
            }
        } catch (error) {
            self.L.error("processRecords:: ", "error while fetching existing records:", error.stack);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_GET", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
            return done();
        }
        try {
            await self.cassandraBills.insertWhatsappCustomerWhitelistRecord(params);
            self.L.log("processRecords::","record processed successfully:",JSON.stringify(params));
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:SUCCESS', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_INSERT", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
            return done();
        } catch (error) {
            self.L.error("processRecords:: ","error while saving record:",JSON.stringify(params),  "Error stack:", error.stack);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_WHATSAPP_CUSTOMER_WHITELIST_INGESTOR", 'STATUS:ERROR', "TYPE:WHATSAPP_CUSTOMER_WHITELIST_INSERT", `SERVICE:${service}`,`PAYTYPE:${paytype}`,`FILE_NAME:${fileName}`]);
            return done();
        }

    }

}
export default CronWhatsAppWhitelistCustomerConsumer;