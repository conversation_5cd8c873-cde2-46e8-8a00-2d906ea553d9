'use strict';

import MOMENT from 'moment';
import _ from 'lodash';
import BILLS from '../models/bills'
import OS from 'os'
import ASYNC from 'async'
import utility from '../lib'
import RecentsLayerLib from '../lib/recentsLayer';
import Q from 'q'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class syncAutomaticRecent {
    constructor(options) {
        this.L = options.L;
        this.infraUtils = options.INFRAUTILS;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.recentsLayerLib = new RecentsLayerLib(options);
        this.bills_operator_table = _.get(this.config, 'OPERATOR_TABLE_REGISTRY', {});
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SYNC_AUTOMATIC_RECENT', 'BATCHSIZE'], 2) : 500; 
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'SYNC_AUTOMATIC_RECENT', 'DELAY'], 5 * 60 * 1000) : 0;
    }

    start() {
        let self = this;
        self.L.log('start', 'Going to configure Kakfa..');

        self.cvrData = _.get(self.config, 'CVR_DATA', {});

        if (_.isEmpty(self.cvrData)) {
            self.L.critical('syncAutomaticRecent service: CVR data is empty');
            process.exit(0);
        }
        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('syncAutomaticRecent :: start', 'unable to configure kafka', error);
                process.exit(0);
            }
            else {
                self.L.log('syncAutomaticRecent :: start', 'Kafka Configured successfully !!');
            }
        });
    }

    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : AUTOMATIC_SYNC_RECENT_TOPIC');

                // Initialize consumer of topic VALIDATION_TOPICS
                let kafkaConsumerObj = {
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.AUTOMATIC_SYNC_RECENT.HOSTS'),
                    "groupId": "updateUserConsentConsumer-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.AUTOMATIC_SYNC_RECENT.AUTOMATIC_SYNC_RECENT_TOPIC'),
                    "id": `syncAutomaticRecent-consumer_${OS.hostname()}_${process.pid}`, 
                    "fromOffset": "earliest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize
                };

                self.kafkasyncAutomaticRecentConsumer = new self.infraUtils.kafka.consumer(kafkaConsumerObj);
                self.kafkasyncAutomaticRecentConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (!error)
                        self.L.log("configureKafka", "consumer of topic : AUTOMATIC_SUBS_DATA Configured");
                    return next(error);
                });
            }
        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize AUTOMATIC_SUBS_DATA Kafka', error);
            }
            return done(error);
        });
    }

    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;

        let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkasyncAutomaticRecentConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }


        self.L.log('execSteps:: ', `Processing ${records.length} AUTOMATIC_SUBS_DATA data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC']);
        
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("syncAutomaticRecent", records);

                self.kafkasyncAutomaticRecentConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000 ;
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time: ' , executionTime , 'seconds' );
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:EMI_DUE_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        self.kafkasyncAutomaticRecentConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.processRecords(() => {
                    return next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    processRecords(done, record) {
        let self = this;
            
        try {
            let error = null,
                billsObj = {},
                source = 'syncAutomaticRecent',
                processedRecord = null;

            [error, record] = self.validateRecord(record);
            if (error) { 
                self.L.error(`processRecords`, `Invalid record received ${JSON.stringify(record)} with error ${error}`);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:VALIDATION_FAILED']);
                return done(error);
            }
            let productInfo = self.cvrData[_.get(record, 'productId', null)];
            record.operator = _.get(productInfo, 'operator').toLowerCase();
            record.paytype = _.get(productInfo, 'paytype').toLowerCase();
            record.service = _.get(productInfo, 'service').toLowerCase();
            let rechargeCardId = _.get(record, 'rechargeCardId', null);

            let tableName = _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.productId], null) || _.get(self.config, ['OPERATOR_TABLE_REGISTRY', record.operator], null);
            if(_.toLower(_.get(record, 'service',''))=='paytm postpaid'){
                tableName='bills_paytmpostpaid'
            }
            else if(_.toLower(_.get(record, 'service',''))=='financial services'){
                
                if( rechargeCardId == null){
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_RECHARGE_CARD_ID']);
                    return done('Missing rechargeCardId', record);
                }
                tableName='bills_creditcard'
            }
            processedRecord = record;

            ASYNC.waterfall([
                next => {
                    _.set(billsObj, 'automaticState', processedRecord.isAutomatic);
                    _.set(billsObj, 'automaticDate', processedRecord.automaticDate);
         
                    let queryParameters = {
                        customer_id: processedRecord.customerId,
                        recharge_number: processedRecord.rechargeNumber,
                        operator: processedRecord.operator,
                        service: processedRecord.service,
                        paytype: processedRecord.paytype
                    };
                    
                    let fieldValue = [billsObj];
                    // self.recentsLayerLib.update(function (error) {
                        // self.L.log('updateBillsInRecent::recentsLayerLib.update', `update recents request completed for ${processedRecord.debugKey},error if any is:${error}`);
                        return next();
                    // }, queryParameters,'automaticDetails',fieldValue,source);
                
                },

                (next) => {
                    self.bills.updateAutomaticStatusForSameCustId(function (error, data) {
                        if(error) {
                            return next(error)
                        } else {
                            return next(null)
                        }
                    }, tableName, processedRecord.isAutomatic, processedRecord.operator, processedRecord.productId, processedRecord.rechargeNumber, processedRecord.customerId, processedRecord.rechargeCardId);
                
                },
                (next) => {
                    if(processedRecord.isAutomatic==1  || processedRecord.isAutomatic==5){
                        self.bills.updateAutomaticStatusForDiffCustId(function (error, data) {
                            if(error) {
                                return next(error)
                            } else {
                                return next(null);
                            }
                        }, tableName, processedRecord.isAutomatic, processedRecord.operator, processedRecord.productId, processedRecord.rechargeNumber, processedRecord.customerId, processedRecord.rechargeCardId);
                    }else{
                        return next(null)
                    }
                },


                
            ], function (error) {
                if(error) {
                    self.L.critical(`processRecords`,`Error occured for record:${record}`,error);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:MAIN_FLOW_EXECUTION_FAILED']);
                }
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_USER_CONSENT_CONSUMER_RECORD_VALIDATION', 'STATUS:SUCCESS', 'TYPE:MAIN_FLOW_EXECUTION']);
                return done(null);
            });
        } catch(err) {
            self.L.critical(`processRecords`,`Exception occured for record:${record}`,err);
            return done(null);
        }
    }

    /**
     * 
     * @param {*} record 
     */
     validateRecord(record){
        if (!record) return ['Invalid record', record];
        let self = this;
        try {
            if (!_.has(record, 'value')) return ['Invalid Kafka record received', record];
            
            record = JSON.parse(_.get(record, 'value', null));
        } catch (error) {
            if (error) {
                self.L.critical('validateAndProcessRecord', `Invalid Kafka record received`, record);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:PARSING_ERROR']);
                return ['Kafka record Parsing Error', record];
            }
        }
        
        let isAutomatic = _.get(record, 'is_automatic', null);
        let customerId = _.get(record, 'customerId', null);
        let productId = _.get(record, 'productId', null);
        let rechargeNumber = _.get(record, 'rechargeNumber', null);
        let operatorName = _.get(record, 'operatorName', null);
        
        let automaticDate = utility.getFilteredDate( _.get(record, ['updatedData' , 'fulfillmentTime' , 'new'], null)).value;
 
        if (isAutomatic == null) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_AUTOMATIC_STATUS']);
            return ['Missing is_automatic', record];
        }
        if (!customerId) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_AUTOMATIC_CUSTID']);
            return ['Missing customerId', record];
        }
        if (!productId) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_AUTOMATIC_PRODUCTID']);
            return ['Missing productId', record];
        }
        if (!rechargeNumber) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_AUTOMATIC_RN']);
            return ['Missing rechargeNumber', record];
        }

        if (!automaticDate || !MOMENT(record.dueDate).isValid()) {
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:UPDATE_SYNC_AUTOMATIC_RECENT_TRAFFIC_VALIDATION', 'STATUS:ERROR' , 'TYPE:MISSING_AUTOMATIC_DATE']);
            return ['Missing/Invalid automaticDate', record];
        }
        automaticDate = automaticDate.format('YYYY-MM-DD HH:mm:ss')


        /**
         * Incorporate any further key like timestamp etc.
         */
        record = { 
            "customerId": customerId,
            "isAutomatic" : isAutomatic,
            "productId" : productId,
            "rechargeNumber" : rechargeNumber,
            "automaticDate"  : automaticDate,
            "operator"       : operatorName,
            "debugKey" : `customerId:${customerId}_rechargeNumber:${rechargeNumber}`
        };
        return [null , record];
    }

    suspendOperations(){
        var self = this,
        deferred = Q.defer();
        self.L.log(`syncAutomaticRecent::suspendOperations kafka consumer shutdown initiated`);
        Q()
        .then(function(){
            self.kafkasyncAutomaticRecentConsumer.close(function(error, res){
                if(error){
                    self.L.error(`syncAutomaticRecent::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`syncAutomaticRecent::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`syncAutomaticRecent::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`syncAutomaticRecent::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;

    }

}

export default syncAutomaticRecent;
