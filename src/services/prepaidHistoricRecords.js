import ASYNC from 'async';
import _ from 'lodash';
import OS from 'os';
import BILLS from '../models/bills';
import utility from '../lib';
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

class PrepaidHistoricRecords {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.infraUtils = options.INFRAUTILS;
        this.bills = new BILLS(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.includedTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'TABLES', 'INCLUDE'], 'plan_validity');
        this.includedTables = this.includedTablesList.split(',').map((e) => e.trim());
        this.greyScaleEnv = options.greyScaleEnv;
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'BATCHSIZE'],2) : 500;
        this.chunkSize = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'CHUNK', 'SIZE'], 10);

        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariable(){
        this.includedTablesList = _.get(this.config, ['DYNAMIC_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'TABLES', 'INCLUDE'], 'plan_validity');
        this.includedTables = this.includedTablesList.split(',').map((e) => e.trim());
    }

    start() {
        let self = this;

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('start :: prepaidHistoricRecords', 'unable to configure kafka', error);
                process.exit(0);
            } else {
                self.L.log('start :: PrepaidHistoricRecords', 'Kafka Confugured successfully !!');
            }
        });
    }

    configureKafka(done) {
        let self = this;
        try {
            self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : PLAN_VALIDITY_MAXWELL');
            // Initialize consumer of topic PLAN_VALIDITY_MAXWELL
            self.kafkaConsumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.PLAN_VALIDITY_MAXWELL.HOSTS'),
                "groupId": "planValidityMaxwell-consumer",
                "topics": _.get(self.config.KAFKA, 'SERVICES.PLAN_VALIDITY_MAXWELL.TOPIC'),
                "id": 'planValidityMaxwellConsumer_' + OS.hostname() + '_' + process.pid,
                "fromOffset": "earliest",
                "autoCommit": false,
                "batchSize": self.kafkaBatchSize
            });

            self.kafkaConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                if (error) {
                    self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                }
                self.L.log("configureKafka", "consumer of topic : PLAN_VALIDITY_MAXWELL Configured");
                return done(error);
            });
        } catch (error) {
            self.L.critical('configureKafka', 'Could not initialize Kafka', error);
            return done(error);
        }
    }

    execSteps(records) {
        let self = this,
            chunkSize = self.chunkSize,
            currentPointer = 0, lastMessage;
        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} Bill Fetch data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:STORE_PREPAID_HISTORIC_RECORDS', 'STATUS:CONSUMED']);
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 10);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("PrepaidHistoricRecords", records);

                self.kafkaConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now
                    if(self.greyScaleEnv) {
                        setTimeout(function(){
                            self.kafkaConsumer._resumeConsumer();
                        },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'PREPAID_HISTORIC_RECORDS', 'BATCH_DELAY'],10));
                    } else {
                        self.kafkaConsumer._resumeConsumer();
                    }
                });
            }
        );
    }


    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.storePrepaidHistoricRecords(() => {
                    next();
                }, record);
            },
            err => {
                done();
            }
        )
    }

    storePrepaidHistoricRecords(done, data) {
        let self = this;
        try {
            const record = self.validatePayload(data);
            if (!record) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_PREPAID_HISTORIC_RECORDS', 'STATUS:SKIP']);
                return done();
            }
            self.L.log('storeHistoricalRecords :: prepaidHistoricRecords', 'Insert Into plan_validity_history', `Recharge Number - ${record.recharge_number}`, `Customer ID - ${record.customer_id}`, `Operator - ${record.operator}`);
            /**
             * Inserting data into bills_history table
             */
            self.bills.insertPrepaidHistoricRecords((error, data) => {
                if (error) {
                    self.L.critical('storeHistoricalRecords :: prepaidHistoricRecords', 'Unable to insert historical data', JSON.stringify(record));
                }
            }, record);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_PREPAID_HISTORIC_RECORDS', 'STATUS:INSERT']);
            return done();
        } catch (error) {
            self.L.error('storeHistoricalRecords :: prepaidHistoricRecords', error.message);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_PREPAID_HISTORIC_RECORDS', 'STATUS:ERROR']);
            return done();
        }
    }

    validatePayload(kafkaPayload) {
        let self = this;
        try {
            const record = self.convertKafkaPayloadToRecord(kafkaPayload);
            return record;
        } catch(error) {
            self.L.error('validatePayload :: prepaidHistoricRecords', error.message);
            throw new Error(error);
        }
    }


    convertKafkaPayloadToRecord(kafkaPayload) {

        let self = this,
            kafkaPayloadData, data, action;
        try {
            kafkaPayloadData = JSON.parse(_.get(kafkaPayload, 'value', null));
            if (!kafkaPayloadData) {
                throw Error('No data found in the payload');
            }
            action = _.get(kafkaPayloadData, 'type', null);
            const tableName = _.get(kafkaPayloadData, 'table', null);
            /**
             * To check if payload falls under allowed actions
             */
            const allowedActions = ['insert', 'update'];
            if (!allowedActions.includes(action)) {
                self.L.log('convertKafkaPayloadToRecord :: prepaidHistoricRecords', `Skip due to action delete ${tableName}`);
                return null;
            }
            /**
             * To check if tablename exist in payload
             */
            if (!tableName) {
                throw Error('Table not found in kafka payload');
            }

            /**
             * To check for required tabele
             */
            if (!self.includedTables.includes(tableName)) {
                self.L.log('convertKafkaPayloadToRecord :: prepaidHistoricRecords', `Table is not included ${tableName}`);
                return null;
            }

            data = _.get(kafkaPayloadData, 'data', {});

        } catch (error) {
            self.L.error('convertKafkaPayloadToRecord :: prepaidHistoricRecords', error.message, JSON.stringify(kafkaPayload));
            throw new Error(error);
        }

        return {
            customer_id: _.get(data, 'customer_id', null),
            recharge_number: _.get(data, 'recharge_number', null),
            product_id: _.get(data, 'product_id', null),
            service: _.get(data, 'service', null),
            operator: _.get(data, 'operator', null),
            circle: _.get(data, 'circle', null),
            amount: _.get(data, 'amount', null),
            validity_expiry_date: _.get(data, 'validity_expiry_date', null),
            order_date: _.get(data, 'order_date', null),
            latest_recharge_date: _.get(data, 'latest_recharge_date', null),
            plan_bucket: _.get(data, 'plan_bucket', null),
            category_name: _.get(data, 'category_name', null),
            created_at: _.get(data, 'created_at', null),
            updated_at: _.get(data, 'updated_at', null),
            cust_mobile: _.get(data, 'cust_mobile', null),
            cust_email: _.get(data, 'cust_email', null),
            rn_customer_id: _.get(data, 'rn_customer_id', 0),
            order_ids: _.get(data, 'order_ids', null),
            status: _.get(data, 'status', 1),
            extra: _.get(data, 'extra', null),
            notification_status: _.get(data, 'notification_status', 1),
            cust_rech_meta: _.get(data, 'cust_rech_meta', null),
        };
    }


}
export default PrepaidHistoricRecords;