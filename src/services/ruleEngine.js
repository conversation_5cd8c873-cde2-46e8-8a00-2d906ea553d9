import _ from 'lodash'
import UTIL from 'util'
import Q from 'q'
import RULES from 'rule-engine'

let L = null;

const
    RULE_TAG            = {
        operatorgatewaymapping: "operatorgatewaymapping"
    };


class ruleEngine{
    constructor(options){
        L   = options.L;
        this.L = options.L;
        this.dbInstance = options.dbInstance;
    }
    /*fetch operator and gateway mapping from rul-engine and save them into config file*/
    fetchOperatorGwMapFromRuleEngine(options_param){
        /*local variables*/
        let 
            self        = this,
            ruleMaps    = {}, //object to map rule-engine objects
            ruleTag     = "",
            deferred    = Q.defer(),
            result;

        self.rules = new RULES();
        self.rules.sqlwrapEngine    = self.sqlwrapEngine;

        Q(undefined)
        .then(function(){
            ruleTag = RULE_TAG.operatorgatewaymapping;
            return Q.resolve();
        })
        .then(function(){
            /*load all rules and error codes*/
            L.verbose('digital-reminder Rule-Engine', 'LoadRules initiated');
            return self.rules._loadRuleEngine(ruleMaps, false, RULE_TAG);
        })
        .then(function(){
            /*load productInfo from cvr*/
            L.verbose('digital-reminder Rule-Engine', '_loadProductInfo initiated');
            return self.rules._loadProductInfo();
        })
        .then(function(){
            L.verbose('Rules of Tag loaded', ruleTag);
            /*fetch rule details by tag, rule-engine module method called*/
            result = self.rules.fetchRulesByTags(ruleTag,ruleMaps);
            return result;
        })
        .then(function(result){
            L.verbose('Result from rule-engine fetched');
            /* fetch operator (key) and gateway (value) from rule-engine response */
            return self.mapOperatorGwNames(result);
        })
        .then(function(allMapping){
            /* set new config into the options set in app.js */
            _.set(options_param.config,'RULEENGINE_CONFIG',allMapping);
            _.set(options_param.config,'productInfoMap',self.rules.maps.productInfo);
            return Q.resolve();
        })
        .then(function(){
            return self.reloadRuleEngine(ruleMaps,ruleTag,options_param);
        })
        .then(function(){
            deferred.resolve();
            return deferred.promise;
        })
        .fail(function(err){
            L.error('digital-reminder ruleEngine','Failure occurred while fetching rule-engine mapping, Static config loaded',err);
            deferred.resolve();
        })
        .catch(function(error){
            L.error('digital-reminder ruleEngine','Error Caught while fetching rule-engine mapping, Static config loaded',error);
            deferred.resolve();
        })
        return deferred.promise;
    }

    mapOperatorGwNames(result){
        var allMapping = {};
        Object.keys(result).forEach(function(key){
            let op_key = (result[key].operator).toString(),
            gw = (result[key].gateway).toString();
            allMapping[op_key] = gw;
        });
        return allMapping;
    }

    /*method to reload rule-engine config after every 9 min */
    reloadRuleEngine(ruleMaps,ruleTag,options_param){
        var
            self                        = this,
            miliTime                    = 1 * 1000, // milisec
            
            reUnits                     = 9 * 60, // seconds
    
            passedUnit                  = 60;
    
        var loadAll = function() {
    
            Q(undefined)
                .then(function(){
    
                    passedUnit+= 1;
    
                    // check if 9*60 seconds (9 min) have passed
                    if(passedUnit >= reUnits) {
                        passedUnit = 0;
                        self.loadAndSetReloadedRuleEngine(ruleMaps,ruleTag,options_param);
                    }
                    return Q.resolve();
                })
                .then(function(){
                    /*recall load all function*/
                    setTimeout(loadAll, miliTime);
                });
        };
    
        setTimeout(loadAll, miliTime);

        return Q.resolve();
    };
    /* method to call load rule engine function and map result fetched*/
    loadAndSetReloadedRuleEngine(ruleMaps,ruleTag,options_param){
        var self=this,
            deferred = Q.defer(),
            result;
        
        Q(undefined)
        .then(function(){
            self.rules._loadRuleEngine(ruleMaps,false,RULE_TAG);
            return Q.resolve();
        })
        .then(function(){
            L.verbose('Rules of Tag loaded', ruleTag);
            /*fetch rule details by tag, rule-engine module method called*/
            result = self.rules.fetchRulesByTags(ruleTag,ruleMaps);
            return Q.resolve(result);
        })
        .then(function(result){
            L.verbose('Result from rule-engine fetched');
            /* fetch operator (key) and gateway (value) from rule-engine response */
            if(!UTIL.isNullOrUndefined(result))
                return self.mapOperatorGwNames(result);
            return Q.resolve();
        })
        .then(function(allMapping){
            /* set new config into the options set in app.js */
            _.set(options_param.config,'RULEENGINE_CONFIG',allMapping);
            return Q.resolve();
        })
        .fail(function(){
            L.error('load And Set ReloadedRuleEngine promise failure');
            deferred.resolve();
        })
        .catch(function(ex){
            L.error('Exception caught during Reloading RuleEngine config',ex);
            deferred.resolve();
        })
        return deferred.promise;
    };
}

export default ruleEngine