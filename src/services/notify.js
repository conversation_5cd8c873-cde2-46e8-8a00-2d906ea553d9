import NOTIFICATION from '../models/notification'
import REQUEST from 'request'
import MOMENT from 'moment'
import ASY<PERSON> from 'async'
import _, { update } from 'lodash'
import utility from '../lib'
import OS from 'os'
import SCHEDULE from 'node-schedule'
import notificationLib from '../lib/notification'
import cassandraBills from '../models/cassandraBills'
import SmsParsingLagDashboard from '../lib/smsParsingLagDashboard'
import billsLib from '../lib/bills'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import Q from 'q'
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';
import logger from '../lib/logger';
let L = null;

class Notify {
    constructor(options) {
        this.L = options.L;
        L = this.L;
        this.config = options.config;
        this.cassandraBills = new cassandraBills(options);
        this.status_list = _.get(this.config, ['NOTIFICATION', 'status']);
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');
        this.dbInstance = options.dbInstance;
        this.notification = new NOTIFICATION(options);
        this.chunkSize = _.get(options, 'tps', _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFY_SERVICE_CONFIG', 'NOTIFY', 'CHUNKSIZE'],_.get(this.notificationConfig, ['notificationapi', 'CHUNKSIZE'], 20)));
        this.categoryId = options.categoryId;
        this.infraUtils = _.get(options, 'INFRAUTILS', null);
        this.pauseConsumer = false;
        this.notificationBillSource = 'RU';
        this.greyScaleEnv = options.greyScaleEnv;
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NOTIFY', 'BATCHSIZE'],2) : _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFY_SERVICE_CONFIG', 'NOTIFY', 'BATCHSIZE'],90);
        this.templatesIgnoreImgUrl = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'TEMPLATES_NO_IMG'], ['27274', '32823', '32824']);
        this.notificationLib = new notificationLib(options);
        this.smsParsingLagDashboard = new SmsParsingLagDashboard(options);
        this.whatsappNotificationType = 'WHATSAPP';
        this.billsLib = new billsLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        this.EncryptionDecryptioinHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new logger(options);
        this.serviceNotificationCountObj = {};
        this.serviceNotificationBatchSizeObj = {};
        this.statusToBlock = [0,1]; //pending , sent
        this.realTimeHost = null;
        this.initializeVariable();

        setInterval(() => {
            this.initializeVariable(options);
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    initializeVariable(options) {
        let self = this;
        self.L.log("initializeVariable :: billReminderNotification", "Re-initializing variable after interval");
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NOTIFY', 'BATCHSIZE'],2) : _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_SERVICE_CONFIG', 'NOTIFY', 'BATCHSIZE'],90);
        self.chunkSize = _.get(options, 'tps', _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFY_SERVICE_CONFIG', 'NOTIFY', 'CHUNKSIZE'],_.get(self.notificationConfig, ['notificationapi', 'CHUNKSIZE'], 20)));
        self.duplicacyCheckWithBasicApproach = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','DUPLICACY_CHECK_WITH_BASIC_APPROACH'], ['WHATSAPP']);
        self.duplicacyCheckWithAdvancedApproach = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','DUPLICACY_CHECK_WITH_ADVANCED_APPROACH'], ['PUSH']);
        self.dbQueryDuplicacyCheckBatchSize = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','DUPLICACY_CHECK_BATCH_SIZE'], 50);
    }

    start() {
        let self = this,
            categoryId = self.categoryId,
            configurations = _.get(self.notificationConfig, ['notificationConsumer', categoryId], null);

            if (categoryId === 100 || categoryId === 101 || categoryId === 102 || categoryId === 103) {
                self.notificationBillSource = 'NONRU';
            }

        if (!configurations) {
            L.error('processNotificationsFromKafka', `Invalid category ID ${categoryId}`);
            return;
        }
        else {
            ASYNC.waterfall([
                (callback) => {
                    self.rejectNotificationkafkaPublisher = new self.infraUtils.kafka.producer({
                        "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.NOTIFICATION_REJECTS.HOSTS')
                    });
                    self.rejectNotificationkafkaPublisher.initProducer('high', function (error) {
                        if (!error)
                            self.L.log("rejectNotificationkafkaPublisher::", "publisher Configured");
                        else{
                            self.L.error("rejectNotificationkafkaPublisher::","Error while configuring Publusher",error)
                        }
                        return callback(error);
                    }); 
                },
                (callback) => {
                    L.log('processNotificationsFromKafka', `Starting Notification Service for categoryId: ${categoryId} and configurations : ${JSON.stringify(configurations)}`);
                    self.kafkaConsumerConfig = configurations;

                    // schedule start and stop consumer timings
                    if (_.get(configurations, 'SCHEDULE_ON_INTERVAL', false) === true) {
                        L.log('start', 'setting up dummy logs..');
                        self.startDummyLogs();

                        L.log('start', 'Starting scheduler...');
                        self.startScheduler();
                    }

                    // If this is 24x7 consumer or this is the instant time to start the consumer 
                    if (self.startInstantConsumer()) {
                        L.log('start', 'Time to start consumer..lets start it!!');
                        self.startConsumer();
                    }
                    callback(null);
                }
            ], (error, result) => {
                if(error){
                    this.L.critical('notify :: start', 'Error:', error);
                    // exiting in case of error
                    process.exit(0);
                }
            });
        }
    }

    /**
     * Kafka publisher : publish data to notification service via Kafka
     */
    configureKafkaPublisher(done) {
        ASYNC.parallel([
            callback => {
                this.ctKafkaPublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                })
        
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    if (!error)
                        L.log("notify :: ctKafkaPublisher", "publisher Configured");
                    else
                        L.error("notify :: ctKafkaPublisher", "Error while  configuring publisher")
                    callback(error);        
                });
            },
            callback => {
                this.kafkaNotificationServicePublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS
                });
                this.kafkaNotificationServicePublisher.initProducer('high', function (error) {
                    if (!error)
                        L.log("notify :: configureKafkaPublisher", "publisher Configured");
                    callback(error);        
                });
            },
            callback => {
                this.reminderKafkaNotificationServicePublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS
                });
                this.reminderKafkaNotificationServicePublisher.initProducer('high', function (error) {
                        if (!error)
                            L.log("notify :: configureKafkaPublisher", "publisher Configured");
                    callback(error);
                });
            },
            callback => {
                this.reminderAuxKafka = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.AUXILIARY_KAFKA.HOSTS
                });
                this.reminderAuxKafka.initProducer('high', function (error) {
                        if (!error)
                            L.log("notify :: configure aux KafkaPublisher", "publisher Configured");
                    callback(error);
                });
            },
            callback => {
                this.rejectNotificationkafkaPublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": _.get(this.config.KAFKA, 'TOPICS.NOTIFICATION_REJECTS.HOSTS')
                });
                this.rejectNotificationkafkaPublisher.initProducer('high', function (error) {
                    if (!error)
                        L.log("rejectNotificationkafkaPublisher::", "publisher Configured");
                    else{
                        L.error("rejectNotificationkafkaPublisher::","Error while configuring Publusher",error)
                    }
                    callback(error);
                }); 
            },
            callback => {
                this.kafkaPVPublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_NOTIFICATION.HOSTS
                });
                this.kafkaPVPublisher.initProducer('high', function(error){
                    if(!error)
                        L.log("notify :: kafkaPVPublisher", "publisher Configured");
                    callback(error);
                });
            },
            callback => {
                this.cassandraCdcPublisher = new this.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CASSANDRA_CDC.HOSTS
                });
                this.cassandraCdcPublisher.initProducer('high', function (error) {
                    if (error)
                        L.critical('error in initialising cassandraCdcPublisher Producer :: ', error);
                    else 
                        L.log("NON_PAYTM_BILLS :: cassandraCdcPublisher KAFKA PRODUCER STARTED....");
                    callback(error);
                   
                });
            }

        ], (error, result) => {
            return done(error);
        })
    }

    configureRejectNotificationKafkaPublisher(done) {
        this.rejectNotificationkafkaPublisher = new this.infraUtils.kafka.producer({
            "kafkaHost": _.get(this.config.KAFKA, 'TOPICS.NOTIFICATION_REJECTS.HOSTS')
        });
        this.rejectNotificationkafkaPublisher.initProducer('high', function (error) {
            if (!error)
                L.log("rejectNotificationkafkaPublisher::", "publisher Configured");
            else{
                L.error("rejectNotificationkafkaPublisher::","Error while configuring Publisher",error)
            }
            done(error);
        });
    }

    startScheduler() {
        let self = this,
            from = _.get(self.kafkaConsumerConfig, 'FROM').split(":"),
            to = _.get(self.kafkaConsumerConfig, 'TO').split(":");

        let startRule = new SCHEDULE.RecurrenceRule();
        startRule.hour = from[0];
        startRule.minute = from[1];
        SCHEDULE.scheduleJob(startRule, self.startConsumer.bind(self));

        let stopRule = new SCHEDULE.RecurrenceRule();
        stopRule.hour = to[0];
        stopRule.minute = to[1];
        SCHEDULE.scheduleJob(stopRule, self.stopConsumer.bind(self));

        L.log('startScheduler', 'Scheduler configured !!');
    }

    startRetryScheduler() {
        let self = this,
            from = _.get(self.notificationConfig, 'FROM').split(":"),
            to = _.get(self.notificationConfig, 'TO').split(":");

        let startRule = new SCHEDULE.RecurrenceRule();
        startRule.hour = from[0];
        startRule.minute = from[1];
        SCHEDULE.scheduleJob(startRule, self.updateRetryNotificationServiceStatus.bind(self));

        let stopRule = new SCHEDULE.RecurrenceRule();
        stopRule.hour = to[0];
        stopRule.minute = to[1];
        SCHEDULE.scheduleJob(stopRule, self.updateRetryNotificationServiceStatus.bind(self));

        L.log('startScheduler', 'Scheduler configured !!');
    }

    /**
     * configure kafka consumer for Notification service based on category
     * @param {*} done 
     */
    _configureAndStartConsumer() {
        let self = this;

        self.L.log('_configureAndStartConsumer', `Going to initialize Kakfa Consumer for topic ${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`);

        // Initialize consumer of topic : REMINDER_BILL_FETCH
        let topic = _.get(self.kafkaConsumerConfig, 'TOPIC', null);
        let kafkaHost = _.get(self.config.KAFKA, 'TOPICS.REMINDER_BILLFETCH_PIPELINE.HOSTS');
        if(topic.includes('NOTIFICATION_') || topic.includes('RT_NOTIFICATION') || topic.includes('HEURISTIC_NOTIFICATION') || topic.includes('WA_NOTIFICATION') ) kafkaHost = _.get(self.config.KAFKA, 'TOPICS.REMINDER_KAFKA_CLUSTER.HOSTS');
        
        if (topic == _.get(self.notificationConfig, 'nonRurealTimeSendNotificationTopic') ) {
            kafkaHost =  _.get(self.config.KAFKA, 'TOPICS.AUXILIARY_KAFKA.HOSTS');
            self.realTimeHost = _.get(self.config.KAFKA, 'TOPICS.AUXILIARY_KAFKA.HOSTS');
        }

        if(self.notificationBillSource == 'NONRU') {
            self.kafkaNotificationConsumer = new self.infraUtils.kafka.consumer({
                "kafkaHost": kafkaHost,
                "groupId": "nonru-notificationService-consumer",
                "topics": topic,
                "id": 'notificationServiceConsumer_' + OS.hostname(),
                "fromOffset": "earliest",
                "autoCommit": false,
                "batchSize": self.kafkaBatchSize
            });

            self.kafkaNotificationConsumer.initConsumer(self.processNotificationsNONRU.bind(self), (error) => {
                if (error) {
                    if(self.realTimeHost){
                        self.L.critical("_configureAndStartConsumer", "error while configuring auxiliary kafka consumer", error);
                    }
                    else{
                    self.L.critical("_configureAndStartConsumer", "error while configuring consumer", error);
                    }
                } else {
                    if(self.realTimeHost){
                        self.L.log("_configureAndStartConsumer", `auxiliary kafka consumer of topic : ${_.get(self.kafkaConsumerConfig, 'TOPIC', null)} Configured`);
                    }
                    else{
                        self.L.log("_configureAndStartConsumer", `consumer of topic : ${_.get(self.kafkaConsumerConfig, 'TOPIC', null)} Configured`);
                    }
                }
                return;
            });
        
        } else {
            self.kafkaNotificationConsumer = new KafkaConsumer({
                "kafkaHost": kafkaHost,
                "groupId": "notificationService-consumer-" + self.categoryId,
                "topics": topic,
                "id": 'notificationServiceConsumer_' + OS.hostname(),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
            });

            // Call multi-parameter version for other cases
            self.kafkaNotificationConsumer.initConsumer(self.processNotifications.bind(self), (error) => {
                if (error) {
                    self.L.critical("_configureAndStartConsumer", "error while configuring consumer", error);
                } else {
                    self.L.log("_configureAndStartConsumer", `consumer of topic : ${_.get(self.kafkaConsumerConfig, 'TOPIC', null)} Configured`);
                }
                return;
            });
        }
    }

    startConsumer() {
        let self = this;
        L.log('startConsumer', 'Going to start consumer...!!');
        if (self.pauseConsumer) {
            self.pauseConsumer = false;
            L.log('startConsumer', 'Resuming consumer...!!');
            self.kafkaNotificationConsumer._resumeConsumer();
        } else {
            L.log('startConsumer', 'Configuring and starting consumer...!!');
            self._configureAndStartConsumer();
        }
    }

    stopConsumer() {
        let self = this;
        self.pauseConsumer = true;
        self.kafkaNotificationConsumer._pauseConsumer();
        self.L.log('processNotifications', 'Done for the day, Time to take a rest.....!!');
    }

    startDummyLogs() {
        let self = this;

        let dummyLogs = setInterval(function () {
            self.L.log('startDummyLogs', 'Consumer will start shortly...');

            if (self.kafkaNotificationConsumer) {
                clearInterval(dummyLogs);
            }
        }, 2000);
    }


    processNotificationsNONRU(notificationData) {
        let self = this,
            lastMessage = notificationData[notificationData.length - 1],
            data = [];
            let alreadyProcessedNotifications=[];
            let alreadyProcessedRedisKeys=[];

        let startTime = new Date().getTime();

        if (!_.isEmpty(notificationData) && _.isArray(notificationData)) {
            self.kafkaNotificationConsumer._pauseConsumer();
            self.L.log('processNotifications:: ', `Processing ${notificationData.length} notification data !!`);

            utility._sendMetricsToDD(notificationData.length, [
                'STATUS:CONSUMED',
                'TOPIC:' + _.get(self.kafkaConsumerConfig, 'TOPIC', null),
                'REQUEST_TYPE:NOTIFY_CONSUMER',
                'BILL_SOURCE_TYPE:' + self.notificationBillSource
            ])



            self.L.log("Checking data length before putting in array :: ",notificationData.length);

            notificationData.forEach(row => {
                try {
                    // self.L.log("Checking data coming from kafka",row);
                    let value = _.get(row, 'value', {}),
                        content = typeof value === 'string' ? JSON.parse(value) : value;
                        _.set(content, ['data','timestamps','notify_acknowledgeTime'], new Date().getTime());
                        if(_.get(content, 'data.partialBillState', null)){
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NOTIFY', 'TYPE:COUNT', `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,`PARTIAL_BILL:${_.get(data, 'partialBillState', 'NO_STATE')}`,`CATEGORY_ID: ${_.get(content, 'data.category_id', null)}`,`SOURCE_ID :${_.get(content, 'data.source_id', null)}`,`TEMPLATE_ID: ${_.get(content, 'data.template_id', null)}`]);
                        }
                    self.validate(async function (error) {
                        if (error) {
                           await self.insertRejectedNotificationsViaPromise(self.billsLib.createErrorMessage(error), _.get(content, 'data',null));
                            self.L.error('_processKafkaData:: ', `Validation failed-${error}`, 'for offset:', _.get(row, 'offset'), ` topic : ${_.get(row, 'topic')} , partition : ${_.get(row, 'partition')} , timestamp : ${_.get(row, 'timestamp')} `, 'DataObj :', JSON.stringify(content));
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                                'STATUS:ERROR',
                                'TYPE:VALIDATION_FAILED',
                                `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                                `CATEGORY_ID: ${_.get(content, 'data.category_id', null)}`,
                                `SOURCE_ID :${_.get(content, 'data.source_id', null)}`,
                                `CORRELATION_ID:${_.get(row, 'correlationId', null)}`,
                                `PARTIAL_BILL:${_.get(content, 'data.partialBillState', "NO_STATE")}`,

                            ]);
               
                        } else if(content && _.get(content,'data',null) && _.get(content,['data','redisKey'],null) && _.get(content,['data','redisKey'],null)!='' && alreadyProcessedRedisKeys.includes(_.get(content,['data','redisKey'],null))){
                            self.L.log('_processKafkaData:: Not processing same notification twice', JSON.stringify(content));
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:DUPLICATE_NOTIFICATION_FOUND", 
                                'STATUS:ERROR',
                                'TYPE:DUPLICATE',
                                'REJECT_SOURCE:PROCESS_CACHE',
                                `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                                `CATEGORY_ID: ${_.get(content, 'data.category_id', null)}`,
                                `SOURCE_ID :${_.get(content, 'data.source_id', null)}`,
                                `CORRELATION_ID:${_.get(row, 'correlationId', null)}`,
                                `PARTIAL_BILL:${_.get(content, 'data.partialBillState', "NO_STATE")}`,
                            ]);
                            await self.insertRejectedNotificationsViaPromise('Found db entry recently for same notification', _.get(content, 'data',null));
                            utility.sendNotificationMetricsFromSend(_.get(content,'data' ,{}), "DROPPED","DUPLICATE_NOTIFICATION_FOUND")
                        }
                        else{
                            data.push(content.data);
                            if(content && _.get(content,'data',null) && _.get(content,['data','redisKey'],null) && _.get(content,['data','redisKey'],null)!=''){
                                alreadyProcessedRedisKeys.push(_.get(content,['data','redisKey'],null))
                            }
                            self.L.log("Check alreadyProcessedNotifications length::", alreadyProcessedNotifications.length);
                        }
                    }, content.data, row);
                }
                catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 'STATUS:ERROR','TYPE:PARSING_ERROR', `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,`CORRELATION_ID:${_.get(row, 'correlationId', null)}`]);
                    self.L.error('_processKafkaData::', `Error while parsing ${error} for data ${JSON.stringify(row)}`);
                }
            });
            self.L.log("Checking data length after putting in array :: ",data.length);
            ASYNC.waterfall([
                next => {
                    if (data.length > 0) {
                        let
                            idealDelay = _.get(self.notificationConfig, 'IDEAL_DELAY', 1),
                            actualDelay = MOMENT().diff(MOMENT(_.get(lastMessage, 'timestamp')), 'seconds');

                        if (idealDelay > actualDelay) {
                            L.log('processNotifications', `Got message with time - ${_.get(lastMessage, 'timestamp')}. Taking a pause for ${idealDelay - actualDelay} seconds !!`);
                            setTimeout(next, (idealDelay - actualDelay) * 1000);
                        } else {
                            return next();
                        }
                    } else {
                        return next();
                    }
                },
                next => {
                    self.execSteps(data, self.chunkSize, () => {
                        return next();
                    });
                },
                next => {
                    self.kafkaNotificationConsumer.commitOffset(lastMessage, (error) => {
                        if (error) {
                            L.error('processNotifications::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        else {
                            L.log('processNotifications::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                        }
                        return next();
                    });
                }
            ], function () {

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :', notificationData.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:NOTIFY_CONSUMER", "TIME_TAKEN:" + executionTime]);
              

                if (self.pauseConsumer) {
                    L.log('processNotifications', 'Done for the day, Time to take a rest.....!!');
                } else if(self.greyScaleEnv) {
                    setTimeout(function(){
                        self.kafkaNotificationConsumer._resumeConsumer();
                    },_.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NOTIFY', 'BATCH_DELAY'],10));
                }
                else {
                    self.kafkaNotificationConsumer._resumeConsumer();
                }
            });

        }
        else {
            self.L.critical('processNotifications:: ', `Invalid format received: ${notificationData}`);
        }

    }

    processNotifications(notificationData,resolveOffset , topic , partition , cb) {

        
        let self = this,
            lastMessage = notificationData[notificationData.length - 1],
            data = [];
            let alreadyProcessedNotifications=[];
            let alreadyProcessedCassandraKeys=[];

        let startTime = new Date().getTime();

        if (!_.isEmpty(notificationData) && _.isArray(notificationData)) {
            
            self.L.log('processNotifications:: ', `Processing ${notificationData.length} notification data !!`);

            utility._sendMetricsToDD(notificationData.length, [
                'STATUS:CONSUMED',
                'TOPIC:' + _.get(self.kafkaConsumerConfig, 'TOPIC', null),
                'REQUEST_TYPE:NOTIFY_CONSUMER',
                'BILL_SOURCE_TYPE:' + self.notificationBillSource
            ])

            self.kafkaConsumerChecks.findOffsetDuplicates("Notify", notificationData,topic, partition);

            self.L.log("Checking data length before putting in array :: ",notificationData.length);

            notificationData.forEach(async(row) => {
                try {
                    //self.L.log("debugX Checking data coming from kafka",row);
                    let value = _.get(row, 'value', {});

                    let content = JSON.parse(value);
   
                    _.set(content, ['data','timestamps','notify_acknowledgeTime'], new Date().getTime()); 

                    let contentService = _.toLower(_.get(content, 'data.service') || _.get(content, 'data.data.dynamicParams.service') || _.get(content, 'data.data.options.data.service'));
                        
                    self.validate(async function (error) {
                        if (error) {
                            self.L.error('_processKafkaData:: ', `Validation failed-${error}`, 'for offset:', _.get(row, 'offset'), ` topic : ${_.get(row, 'topic')} , partition : ${_.get(row, 'partition')} , timestamp : ${_.get(row, 'timestamp')} `);
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                                'STATUS:ERROR',
                                'TYPE:VALIDATION_FAILED',
                                `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                                `CATEGORY_ID: ${_.get(content, 'data.category_id', null)}`,
                                `SOURCE_ID :${_.get(content, 'data.source_id', null)}` ,
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ]);
                            await self.insertRejectedNotificationsViaPromise(self.billsLib.createErrorMessage(error), _.get(content, 'data',null));
                        } else if(content && content.id && alreadyProcessedNotifications.includes(content.id)){
                            self.logger.log('_processKafkaData:: Not processing same notification twice', content, contentService);
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:DUPLICATE_NOTIFICATION_FOUND", 
                                'STATUS:ERROR',
                                'TYPE:DUPLICATE',
                                'REJECT_SOURCE:PROCESS_CACHE',
                                `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                                `CATEGORY_ID: ${_.get(content, 'data.category_id', null)}`,
                                `SOURCE_ID :${_.get(content, 'data.source_id', null)}` ,
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ]);
                            await self.insertRejectedNotificationsViaPromise('Found db entry recently for same notification', _.get(content, 'data',null));
                            utility.sendNotificationMetricsFromSend(_.get(content,'data' ,{}), "DROPPED","DUPLICATE_NOTIFICATION_FOUND")
                        } else if(content && _.get(content,'data',null) && _.get(content,['data','cassandraKey'],null) && _.get(content,['data','cassandraKey'],null)!='' && alreadyProcessedCassandraKeys.includes(_.get(content,['data','cassandraKey'],null))){
                            self.logger.log('_processKafkaData:: Not processing same notification twice', content, contentService);
                            utility._sendMetricsToDD(1, [
                                "REQUEST_TYPE:DUPLICATE_NOTIFICATION_FOUND", 
                                'STATUS:ERROR',
                                'TYPE:DUPLICATE',
                                'REJECT_SOURCE:PROCESS_CACHE',
                                `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                                `CATEGORY_ID: ${_.get(content, 'data.category_id', null)}`,
                                `SOURCE_ID :${_.get(content, 'data.source_id', null)}` ,
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ]);
                            await self.insertRejectedNotificationsViaPromise('Found db entry recently for same notification', _.get(content, 'data',null));
                            utility.sendNotificationMetricsFromSend(content , "ERROR","DUPLICATE_NOTIFICATION_FOUND")
                        }
                        else{
                            data.push(content.data);
                            if(content && content.id){
                                alreadyProcessedNotifications.push(content.id);
                            }
                            if(content && _.get(content,'data',null) && _.get(content,['data','cassandraKey'],null) && _.get(content,['data','cassandraKey'],null)!=''){
                                alreadyProcessedCassandraKeys.push(_.get(content,['data','cassandraKey'],null))
                            }
                            self.L.log("Check alreadyProcessedNotifications length::", alreadyProcessedNotifications.length);
                        }
                    }, content.data);
                }
                catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 'STATUS:ERROR', 'TYPE:PARSING_ERROR', `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`, `BILL_SOURCE_TYPE:${self.notificationBillSource}`]);
                    utility.sendNotificationMetricsFromSend({}, "ERROR", "UNABLE_TO_PARSE");
                    //will have to encrypt blindly as we cannot get service due to parsing error
                    self.L.error('_processKafkaData::', `Error while parsing ${error} for data ${self.EncryptionDecryptioinHelper.encryptData(JSON.stringify(row))}`);
                    await self.insertRejectedNotificationsViaPromise(self.billsLib.createErrorMessage(error), row);                                   
                }
            });
            self.L.log("Checking data length after putting in array :: ",data.length);
            ASYNC.waterfall([
                next => {
                    if (data.length > 0) {
                        let
                            idealDelay = _.get(self.config , ['DYNAMIC_CONFIG', 'NOTIFY_SERVICE_CONFIG', 'NOTIFY', 'IDEAL_DELAY'],_.get(self.notificationConfig, 'IDEAL_DELAY', 2)),
                            actualDelay = MOMENT().diff(MOMENT(_.get(lastMessage, 'timestamp')), 'seconds');

                        if (idealDelay > actualDelay) {
                            L.log('processNotifications', `Got message with time - ${_.get(lastMessage, 'timestamp')}. Taking a pause for ${idealDelay - actualDelay} seconds !!`);
                            setTimeout(next, (idealDelay - actualDelay) * 1000);
                        } else {
                            return next();
                        }
                    } else {
                        return next();
                    }
                },
                next => {
                    self.execSteps(data, self.chunkSize, () => {
                        return next();
                    });
                },
                async (next) => {
                    await resolveOffset(lastMessage.offset)
                    self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    return next();
                }
            ], function () {

                let endTime = new Date().getTime();
                let executionTime = (endTime - startTime) / 1000;      //in seconds
                executionTime = Math.round(executionTime);
                self.L.log('execSteps::', 'per batchSize record Execution time :', executionTime, 'seconds', 'recordLength :', notificationData.length);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:NOTIFY_CONSUMER", "TIME_TAKEN:" + executionTime, "BILL_SOURCE_TYPE:" + self.notificationBillSource]);
              
                return cb();
            });

        }
        else {
            self.L.critical('processNotifications:: ', `Invalid format received: ${notificationData}`);
            return cb();
        }

    }

    /**
     * Check if consumer needs to be started immediately
     * Start if 
     * 1. 24x7 consumer
     * 2. from < now < to
     */
    startInstantConsumer() {
        let
            self = this,
            from = _.get(self.kafkaConsumerConfig, 'FROM'),
            to = _.get(self.kafkaConsumerConfig, 'TO'),
            now = MOMENT().format('HH:mm:ss');

        if (!_.get(self.kafkaConsumerConfig, 'SCHEDULE_ON_INTERVAL', false) || (from < now && now < to)) { // 24x7 service
            return true;
        } else {
            return false;
        }
    }

    startRetryNotifications() {
        let self = this,
            configurations = _.get(self.notificationConfig, ['RETRY_NOTIFICATION'], null);

        self.rejectNotificationkafkaPublisher = new self.infraUtils.kafka.producer({
            "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.NOTIFICATION_REJECTS.HOSTS')
        });
        self.rejectNotificationkafkaPublisher.initProducer('high', function (error) {
            if (!error){
                self.L.log("rejectNotificationkafkaPublisher::", "publisher Configured");
                self.notificationConfig = configurations;

                // schedule start and stop consumer timings
                if (_.get(configurations, 'SCHEDULE_ON_INTERVAL', false) === true) {

                    self.updateRetryNotificationServiceStatus();
                    L.log('start', 'setting up dummy logs..');
                    self.startDummyLogs();

                    L.log('start', 'Starting retry scheduler...');
                    self.startRetryScheduler();
                }
                    
                self.retryNotifications();
            }
            else{
                self.L.error("rejectNotificationkafkaPublisher::","Stopping service - Error while configuring Publusher",error)
                process.exit(0);
            }
        }); 
    }

    retryNotifications() {
        let self = this,
            startTime = MOMENT().subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            queryLimit = 2000;
        const retryStatusList = self.getRetryNotificationStatusList();
        if (self.service_status == true){
            self.notification.getRetryNotifications(function _doUntilNoMoreRecords(error, data) {
                
                    if (!error && data && data.length > 0) {
                        utility._sendMetricsToDD(data.length, ['REQUEST_TYPE:RETRY_NOTIFY_SERVICE', 'STATUS:TRAFFIC', 'BILL_SOURCE_TYPE:' + self.notificationBillSource]);
                        self.execSteps(data, self.chunkSize, () => {
                            self.retryNotifications();
                        });
                    }
                    else {
                        L.info("No data found");
                        setTimeout(self.retryNotifications.bind(self), 10*60 * 1000);
                    }
                
            
            }, retryStatusList, startTime, queryLimit);
        }
        else {
            L.info("service has been stopped...............");
            setTimeout(self.retryNotifications.bind(self), 10*60 * 1000);
        }
    }

    execSteps(records, chunkSize, done) {
        let self = this,
            currentPointer = 0;

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 500);
                });
            },
            (err) => {
                done();
            }
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.waterfall([
            next => {
                self.processNotificationBatch(records, next);
            },
            (result, next) => {
                self.updateNotification(result, () => {
                    next();
                })
            }
        ],
            err => {
                done();
            })
    }

    processNotificationBatch(records, done) {
        let self = this,
            result = {};

        ASYNC.map(
            records,
            (record, next) => {
                self.sendNotification(response => {
                    if (response) {
                        let resultObj = {
                            record: record,
                            response: response
                        }
                        self.L.log('notify::sendNotificationCallback::', `sourceId is ${_.get(record, 'source_id', null)} and customerId is ${_.get(record, 'data.additional_data.customer_id', null)}`);
                        if (self.isNotificationMigratedToCassandra(_.get(record, 'source_id', null), _.get(record, 'data.additional_data.customer_id', null))) {
                            result[record.record_key] = resultObj;
                        } else {
                            result[record.id] = resultObj;
                        }
                        // If the status return is RETRY, then skip the reschuleNotification 
                        if (response.status !== _.get(self.config, ['NOTIFICATION', 'status', 'RETRY'], 6) && response.status !== _.get(self.config, ['NOTIFICATION', 'status', 'DISABLED'], 8)) {
                            self.rescheduleNotification(record);
                        }
                    }
                    next();
                }, record);
            },
            err => {
                done(null, result);
            }
        )
    }

    getRNForCCCache(recharge_number, customer_id, bank_name, card_network){
        var self = this;
        recharge_number = recharge_number.replace(/ /g,'');
        let last4MCN = recharge_number.substr(recharge_number.length -4);

        if(self.notificationBillSource == 'NONRU') {
            return last4MCN+'_'+customer_id+'_'+bank_name;
        }
        else 
            return last4MCN+'_'+customer_id+'_'+bank_name+'_'+card_network;
    }

    paymentAlreadyDone(row,cb){
        var self = this;
        let blockSourceIds = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG', 'SKIP_NOTIFICATION', 'BLOCK_SOURCE_ID_IF_CACHE_FOUND'], []);
        if(blockSourceIds.indexOf(_.get(row, 'source_id', 0).toString()) < 0){
            self.L.log(`paymentAlreadyDone:: Not blocking notifications for source_id: ${_.get(row, 'source_id', 0)} as this is not in config`)
            return cb(null);
        }

        let params;
        if(_.get(row, ['data','dynamicParams'], null)) {
            let recharge_number = _.get(row, ['data', 'dynamicParams', 'recharge_number'], null),
            paytype = _.toLower(_.get(row, ['data', 'dynamicParams', 'paytype'], null)),
            service = _.toLower(_.get(row, ['data', 'dynamicParams', 'service'], null)),
            operator = _.toLower(_.get(row, ['data', 'dynamicParams', 'operator'], null)),
            bank_name = _.toLower( _.get(row, ['data', 'dynamicParams', 'bank_name'], null)),
            customer_id =  _.get(row, ['data', 'dynamicParams', 'customer_id'], null),
            card_network = _.toLower( _.get(row, ['data', 'dynamicParams', 'card_network'], null));
            params = {
                paytype : paytype,
                service : service,
                operator : service=='financial services' && self.notificationBillSource == 'NONRU' ? bank_name : operator,
                recharge_number : service=='financial services'?  self.getRNForCCCache(recharge_number,customer_id,bank_name,card_network) : recharge_number,
                customer_id : customer_id
            }
        }
        else {
            let recharge_number = _.get(row, ['data', 'options', 'data', 'recharge_number'], null),
                paytype = _.toLower(_.get(row, ['data', 'options', 'data', 'paytype'], null)),
                service = _.toLower(_.get(row, ['data', 'options', 'data', 'service'], null)),
                operator = _.toLower(_.get(row, ['data', 'options', 'data', 'operator'], null)),
                bank_name = _.toLower( _.get(row, ['data', 'options', 'data', 'bank_name'], null)),
                customer_id =  _.get(row, ['data', 'options', 'data', 'customer_id'], null),
                card_network = _.toLower( _.get(row, ['data', 'options', 'data', 'card_network'], null));
            params = {
                paytype : paytype,
                service : service,
                operator : service=='financial services' && self.notificationBillSource == 'NONRU' ? bank_name : operator,
                recharge_number : service=='financial services'?  self.getRNForCCCache(recharge_number,customer_id,bank_name,card_network) :  recharge_number,
                customer_id : customer_id
            }
        }

        self.cassandraBills.getPaymentRemindLaterDateCache(params).then((result)=>{
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", "STATUS:SUCCESS", 'TYPE:CACHE_FETCHED',`OPERATOR:${_.get(params,'operator',null)}`,`SERVICE:${_.get(params,'service',null)}`, `BILL_SOURCE_TYPE:${self.notificationBillSource}`]);
            self.L.log(`paymentAlreadyDone:: cache fetched for the record having debug key: recharge_number:${_.get(params, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) : params.recharge_number}_operator:${params.operator}`);
            if(Array.isArray(result) && result.length>0){
                let cacheTimePeriod = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG', 'SKIP_NOTIFICATION', 'PAYMENT_CACHE_TIME'], 24);
                let cacheTimeUnit = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SKIP_NOTIFICATION', 'PAYMENT_CACHE_UNIT'], 'hour');
                let paymentDateValue = _.get(result[0], 'payment_date', null);
                let paymentDate = paymentDateValue ? MOMENT(paymentDateValue).utc() : null;                
                self.L.log(`paymentAlreadyDone:: payment done for the bill on ${paymentDate} and max valid cache is for duration ${cacheTimePeriod} ${cacheTimeUnit} debug key: recharge_number:${_.get(params, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) : params.recharge_number}_operator:${params.operator}`);
                if(paymentDate && paymentDate.diff(MOMENT().subtract(cacheTimePeriod, cacheTimeUnit).utc()) > 0){
                    self.L.log(`paymentAlreadyDone:: notification skipped for the record having debug key: recharge_number:${_.get(params, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) : params.recharge_number}_operator:${params.operator}`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", "STATUS:NOTIFICATION_SKIPPED", 'TYPE:CACHE_FOUND',`OPERATOR:${_.get(params,'operator',null)}`,`SERVICE:${_.get(params,'service',null)}`, `BILL_SOURCE_TYPE:${self.notificationBillSource}`]);
                    return cb('payment is recieved for this bill, hence skipping notification')
                } else{                 
                    let customerRemindLaterMap = _.get(result[0], 'remind_later_data', {});
                    let customerId = params.customer_id;
                    let remindLaterDate = customerRemindLaterMap[customerId] || null;
                    if(remindLaterDate && MOMENT.utc(remindLaterDate).startOf('day').isAfter(MOMENT.utc().startOf('day'))){
                        self.L.log(`remindLaterDate:: notification skipped for the record having debug key: recharge_number:${_.get(params, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) : params.recharge_number}_operator:${params.operator}`);
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", "STATUS:NOTIFICATION_SKIPPED", 'TYPE:REMIND_LATER_DATE',`OPERATOR:${_.get(params,'operator',null)}`,`SERVICE:${_.get(params,'service',null)}`, `BILL_SOURCE_TYPE:${self.notificationBillSource}`]);
                        return cb('Remind later date is of future, hence skipping notification')
                    } else {
                        return cb(null);
                    }
                }                
            }
            else return cb(null);
        })
        .catch(error =>{
            self.L.error(`paymentAlreadyDone::`, `cache fetching query failed with error ${error} for record having debug key recharge_number:${_.get(params, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(params.recharge_number) : params.recharge_number}_operator:${params.operator}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NOTIFICATION_SERVICE", "STATUS:ERROR", 'TYPE:CACHE_FETCHED',`REASON:${error}`,`OPERATOR:${_.get(params,'operator',null)}`,`SERVICE:${_.get(params,'service',null)}`, `BILL_SOURCE_TYPE:${self.notificationBillSource}`]);
            return cb(null);
        });

    }

    async checkNotificationInCassandra(row){
        let self=this;
        return new Promise((resolve,reject)=>{
            try{
                let time_interval= 1200;

                if(_.get(row, ['data','dynamicParams'], null)) { 
                    time_interval = _.get(row, ['data','dynamicParams', 'time_interval'], null)? _.get(row, ['data','dynamicParams', 'time_interval'], null) : 1200;
                }else{ 
                    time_interval = _.get(row, ['data', 'options', 'data', 'time_interval'], null)? _.get(row, ['data', 'options', 'data', 'time_interval'], null):1200;
                }

                let last_ref_time = MOMENT().subtract(time_interval, 'minutes').format('YYYY-MM-DD HH:mm:ss');

                _.set(row, 'last_ref_time', last_ref_time);
                let service = _.toLower(_.get(row, 'service') || _.get(row, 'data.dynamicParams.service') || _.get(row, 'data.options.data.service'));
                self.getDataFromCassandraForDuplicacy(row,function(err,data){
                    self.logger.log(`checkNotificationInCassandra:: value recieved ${JSON.stringify(data)} for row`, row, service);
                    if(err){
                        reject(err);
                    }else{
                        _.set(row, 'loggerCassandraKey', service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(row, 'cassandraKey', null)) : _.get(row, 'cassandraKey', null));
                        if(self.shouldNotificationBeBlocked(data, row, 'notify')){
                            resolve(true);
                        } else resolve(false);
                    }
                })
            }catch(error){
                resolve(false);
            }
        })
    }

    async checkNotificationInDB(row){
        let self=this;
        return new Promise((resolve,reject)=>{
            try{
               
                let id = _.get(row, 'id', '');
               
                // recipient = _.get(row, 'recipient', ''),
                // template_id =_.toNumber(_.get(row, 'template_id', null)),
                // source_id =_.toNumber(_.get(row, 'source_id', null)),
                // category_id =_.toNumber(_.get(row, 'category_id', null)),
                // status=1,
                // time_interval= 1200;
                // if(_.get(row, ['data','dynamicParams'], null)) {
                //     time_interval = _.get(row, ['data','dynamicParams', 'time_interval'], null)? _.get(row, ['data','dynamicParams', 'time_interval'], null) : 1200;
                // } else {
                // time_interval = _.get(row, ['data', 'options', 'data', 'time_interval'], null)? _.get(row, ['data', 'options', 'data', 'time_interval'], null):1200;
                // }
                // let last_ref_time = MOMENT().subtract(time_interval, 'minutes').format('YYYY-MM-DD HH:mm:ss');
               
                let query= `SELECT status FROM notification WHERE id = ?`;// recipient=? and recharge_number=? and template_id=? and source_id=? and category_id=? and status=? and send_at>?`;
                let params = [id];
                    // recipient,
                    // recharge_number,
                    // template_id,
                    // source_id,
                    // category_id,
                    // status,
                    // last_ref_time
                    // ]
                self.L.log('checkNotificationInDB',self.dbInstance.format(query,params));
                var latencyStart = new Date().getTime();
                self.dbInstance.exec(function (err, data) {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'DB_QUERY', 'TYPE':'checkNotificationInDB', 'BILL_SOURCE_TYPE': self.notificationBillSource });
                    if (err || !(data)) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:DB_QUERY", 'STATUS:ERROR', `TYPE:checkNotificationInDB`, `BILL_SOURCE_TYPE:${self.notificationBillSource}`]);
                        self.L.critical('checkNotificationInDB::', 'error occurred while getting data from DB: ', err);
                        resolve(false);
                    }else if (data && _.isArray(data) && data.length>0 && data[0].status == 1){
                        self.L.log('checkNotificationInDB::', 'entries found with same notfication hence skipping notification ', JSON.stringify(data)); //db data already encrypted
                        resolve(true);
                    }else{
                        resolve(false);
                    }
                }, 'DIGITAL_REMINDER_SLAVE', query, params);
            }catch(error){
                resolve(false);
            }
        })
    }

    async checkTimingForNotificationDrop(row){
        let self=this;
        return new Promise((resolve,reject)=>{
            try{
                let notificationPublishTime = _.get(row, 'send_at', new Date().getTime());
                if(MOMENT(notificationPublishTime).isBefore(MOMENT(), 'day')) {
                    //removing log from here as it is being logged in main function
                    //self.L.log('notify :: not sending notification as it was created yesterday', _.get(row,'recharge_number', null));
                    resolve(true);
                }else resolve(false);
            }catch(error){
                resolve(false);
            }
        })
    }

    async sendNotification(callback, row) {
        let self = this;
        // self.L.log("Sending notification for row:: ", JSON.stringify(row));
        var data = {};
        let apiOpts = {};
        let categoryId;
        let type;
        let customerId;
        if (_.get(row, 'status', null) == _.get(self.status_list, 'RETRY', 6) || _.get(row, 'status', null) == _.get(self.status_list, 'RETRY_FALLBACK', 9)) {
            row.send_at = MOMENT(_.get(row, 'send_at', null)).utc().format('YYYY-MM-DD HH:mm:ss');
            
            if (_.get(row, 'is_encrypted', 0) == 1) {
                self.L.log('sendNotification::', `encrypted record received, returning decrypted row for ${JSON.stringify(row)}`);
                row = self.EncryptionDecryptioinHelper.parseDbResponse([row], 1)[0];
            }
        }
        try {
            data = typeof row.data == 'string' ? JSON.parse(row.data) : row.data;
            row.data = data;
            categoryId = _.get(row, 'category_id', null);
            type = _.get(row, 'type');
        }
        catch (error) {
            L.error("Error in parsing of JSON data fetched: ", error);
            return callback({});
        }

        customerId = _.get(row, 'data.dynamicParams.customer_id', _.get(row, 'data.options.data.customer_id', null));
        //format amount with commas
        try {
            let amount = self.formatAmountWithCommas(_.get(row, 'data.dynamicParams.amount', null));
            if (amount != null && amount != undefined && amount != '') {
                _.set(row, 'data.dynamicParams.amount', amount);
            }
            let dueDate = self.formatDueDate(_.get(row, 'data.dynamicParams.due_date', null));
            console.log("🚀 ~ Notify ~ sendNotification ~ dueDate:", dueDate)
            if (dueDate != null) {
                _.set(row, 'data.dynamicParams.due_date', dueDate);
            }
        } catch (error) {
            self.L.error("sendNotification:: error while formatting amount", error);
        }
        let recordKey = `${_.get(row, 'recharge_number', null)}_${_.get(row, 'recipient', null)}_${_.get(row, 'type', null)}_${_.get(row, 'template_id', null)}_${_.get(row, 'source_id', null)}_${_.get(row, 'category_id', null)}_${_.get(row, 'product_id', null)}_${_.get(row, 'send_at', null)}`;
        _.set(row, 'record_key', recordKey);
        let payloadService = _.toLower(_.get(row, 'service') || _.get(row, 'data.dynamicParams.service') || _.get(row, 'data.options.data.service'));
        _.set(row, 'data.additional_data.customer_id', customerId); 

        try{
            let shouldDropNotificationIfCreatedBefore = await self.checkTimingForNotificationDrop(row);
            if(shouldDropNotificationIfCreatedBefore){
                self.logger.log('notify :: not sending notification as it was created yesterday', _.get(row,'recharge_number', null), payloadService);
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NOTIFY_SERVICE", 
                    'STATUS:OLD_REMINDER_REJECTED',
                    `CATEGORY_ID: ${_.get(row, 'category_id', null)}`,
                    `SOURCE_ID :${_.get(row, 'source_id', null)}`,
                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
            ]);
                return callback({
                    "status": _.get(self.config, ['NOTIFICATION', 'status', 'CANCELED'], 2),
                    "error_msg": `Notification created yesterday, hence discarding it`,
                });
            }
        }catch(err){
            self.L.error("sendNotification:: error while checking timing for notification drop", err)
        }

        if(_.get(row,'source_id',null)==26){
            let notificationPublishTime = _.get(row, ['timestamps', 'billFetchReminder_onBoardTime'], new Date().getTime());
            if(MOMENT(notificationPublishTime).isBefore(MOMENT(), 'day')) {
                utility.sendNotificationMetricsFromSend(row , "ERROR","OLD_NOTIFICATION")
                self.logger.log('notify :: not sending notification as it was created yesterday', _.get(row,'recharge_number', null), payloadService);
                await self.insertRejectedNotificationsViaPromise('not sending notification as it was created yesterday', row);
                return callback(null);
            }
        }
        // let checkCacheFromCassandra= _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_DUPLICACY_ENABLE_FOR_ALL_SOURCE_IDS'], false) ||
        // (_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_DUPLICACY_ENABLE_FOR_SOURCE_IDS'], []).includes(_.toString(_.get(row,'source_id',null))));

        let notificationCreatedFreshly=false;
      
       

        if(!notificationCreatedFreshly){
            self.L.log(`sendNotification:: checkNotificationInCassandra: checking notification duplicacy from cassandra`)
            try {

                notificationCreatedFreshly = await self.checkNotificationInCassandra(row);
            } catch (err) {
                self.L.error("sendNotification:: error while executing notification check from cassandra cache", err);
                if (_.get(row, 'status', null) == _.get(self.status_list, 'RETRY', 6) || _.get(row, 'status', null) == _.get(self.status_list, 'RETRY_FALLBACK', 9)) {
                    self.L.log('notify::sendNotification::', `checking duplicacy from mysql db for retry/retry_fallback case for id ${_.get(row, 'id', null)}`);
                    try {
                        notificationCreatedFreshly = await self.checkNotificationInDB(row)
                    } catch (e) {
                        self.L.error('notify::sendNotification::', `error while executing duplicacy check from checkNotificationInDB for id ${_.get(row, 'id', null)}`, e);
                        self.sendDatabaseErrorMetrics('MYSQL', 'SEND_NOTIFICATION');
                        await self.publishNotificationDropsDueToDatabaseError(row, 'SEND_NOTIFICATION');
                        await self.insertRejectedNotificationsViaPromise('Failed to check duplicacy from cassandra cache', row);
                        return callback(null);
                    }
                } else {
                    self.sendDatabaseErrorMetrics('CASSANDRA', 'SEND_NOTIFICATION');
                    await self.publishNotificationDropsDueToDatabaseError(row, 'SEND_NOTIFICATION');
                    await self.insertRejectedNotificationsViaPromise('Failed to check duplicacy from cassandra cache', row);
                    return callback(null);  
                }
            }
            if(notificationCreatedFreshly==true){
                // self.L.log('_processKafkaData:: Found db entry recently for same notification', JSON.stringify(row));
                    utility._sendMetricsToDD(1, [
                        "REQUEST_TYPE:DUPLICATE_NOTIFICATION_FOUND", 
                        'STATUS:ERROR',
                        'TYPE:DUPLICATE',
                        'REJECT_SOURCE:DB_RECORD_FOUND',
                        `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                        `CATEGORY_ID: ${_.get(row, 'category_id', null)}`,
                        `SOURCE_ID :${_.get(row, 'source_id', null)}`,
                        `QUERY_ON:  CASSANDRA`,
                        `BILL_SOURCE_TYPE:${self.notificationBillSource}`
    
                ]);
                await self.insertRejectedNotificationsViaPromise('Found db entry recently for same notification', row);
                utility.sendNotificationMetricsFromSend(row , "ERROR","DUPLICATE_NOTIFICATION_FOUND")
                return callback(null);
            }
        }

        

        ASYNC.waterfall([
            (next) => {
                self.paymentAlreadyDone(row, function(err,result){
                    if(err){
                        utility.sendNotificationMetricsFromSend(row , "ERROR","PAYMENT_DONE")
                        next(err);
                    }
                    else{
                        next(null);
                    }
                })
                // next(null);
            },
            /*(next) => {
                self.checkServiceLevelNotificationCapping(row, (err, status) => {
                    if (err) {
                        next(err);
                    } else if (status == 'DROP') {
                        next('service level notification capping reached, dropping notification');
                    } else {
                        next(null);
                    }
                });
            },*/
            (next) => {
                try {
                    self.handleEmojisInPayload(data, type);
                    apiOpts = self.processNotificationApiOpts(data, categoryId, type, row);
      
                }
                catch(e) {
                    self.L.error('sendNotification::', 'error while processing notification api opts', e);
                    let responseObj = self.prepareResponseForPayloadNotSupportedByV1(type);
                    utility.sendNotificationMetricsFromSend(row ,_.get(responseObj,'status'))
                    return callback(responseObj);
                }
        
                var latencyStart = new Date().getTime();
                REQUEST(apiOpts, (error, response, body) => {
                    utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'NOTIFICATION', 'URI': apiOpts.uri });
                    self.prepareResponse((responseObj) => {
                        utility.sendNotificationMetricsFromSend(row ,_.get(responseObj,'status'))
                        next(null,responseObj);
                    }, row, error, response, body, _.get(apiOpts, 'uri', null));
                });
            },
            (responseObj,next) => {
          
                if(_.get(row, 'entry_created_in_notification_log', false)){
                    return self.cassandraBills.updateEntryInNotificationTable(next, responseObj, row);
                }else{
                    return next(null,responseObj);
                }
            },
            (responseObj,next) => {
                
                    let value = {
                        priority: _.get(row, 'priority', null),
                        status: _.get(responseObj, 'status', 0),
                        send_at: MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                        sent_at: _.get(responseObj, 'sent_at', MOMENT().format('YYYY-MM-DD HH:mm:ss'))
                    };
            
                    self.setDataInCassandraForDuplicacy(row, value, function(err, data) {
                        if (err) {
                            self.L.error('sendNotification:: setDataInCassandra error received while setting data from cassandra error:', err);
                        }
                        next(null, responseObj);
                    });
         
                  

                  

            }, 
            (responseObj, next) => {
                let sourceId = _.get(row, 'source_id', null);
                let customerId = _.get(row, 'data.additional_data.customer_id', null);
                if (_.get(responseObj, 'job_id', null) && self.isNotificationMigratedToCassandra(sourceId, customerId)) {
                    self.cassandraBills.createJobIdNotificationMappingRecord((err) => {
                        if (err) {
                            self.L.error('notify::sendNotification::createJobIdNotificationMappingRecordCb::', `error occurred for record ${recordKey}`, err);
                            utility._sendMetricsToDD(1, [
                                `REQUEST_TYPE:CREATE_JOBID_NOTIFICATION_MAPPING`,
                                `STATUS:ERROR`,
                                `SOURCE_ID:${_.get(row, 'source_id', 'UNKNOWN')}`,
                                `CATEGORY_ID:${_.get(row, 'category_id', 'UNKNOWN')}`,
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ]);
                        } else {
                            utility._sendMetricsToDD(1, [
                                `REQUEST_TYPE:CREATE_JOBID_NOTIFICATION_MAPPING`,
                                `STATUS:RECORD_CREATED`,
                                `SOURCE_ID:${_.get(row, 'source_id', 'UNKNOWN')}`,
                                `CATEGORY_ID:${_.get(row, 'category_id', 'UNKNOWN')}`,
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ]);
                        }
                        next(err, responseObj);
                    }, _.get(responseObj, 'job_id', null), row);
                } else {
                    next(null, responseObj);
                }
            }
        ], async function(error, result){
            if(error){
                self.L.critical('notify :: sendNotification', 'Error:', error);
                await self.insertRejectedNotificationsViaPromise(self.billsLib.createErrorMessage(error), row);
            }
            return callback(result);
        });
    }

    processNotificationApiOpts(data, categoryId, type, row) {
        let self = this;

        let templateName = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(data, 'template_id', null), 'TEMPLATE_NAME'], null);
        let apiOpts = {};
        if (!templateName && (categoryId == 13 || categoryId == 14 || type == 'WHATSAPP') ){
            templateName = _.get(data, 'template_name', _.get(data, 'templateName', null));
            
        }
        if (self.validatev3ApiBody(data)) {

            self.notificationLib.append2CTAPayload(data, row);
            apiOpts = self.getv3ApiOpts(data, type,categoryId);
            if(type == 'CHAT') { // Adding refId 
                let custId = _.get(row, 'recipient', null);
                _.set(data, 'referenceId', _.isNull(_.get(row, 'id', null)) ? self.getReferenceIdForChat(custId) : _.get(row, 'id'));
            }
        }
        else if (templateName) {
           
            apiOpts = self.processv3ApiOpts(data, templateName, categoryId, type, row);
            self.L.log("templateName not null apiOpts is {}", JSON.stringify(apiOpts));
            if (!apiOpts) {
                L.critical("notify::processNotificationApiOpts", "Error while processing v3 notification- ", JSON.stringify(data));
                // if we get error while processing v3 api body then send v1 notification
                if (type != self.whatsappNotificationType) {
                    apiOpts = self.getv1ApiOpts(data);
                } else {
                    throw 'Whatsapp notification not supported by v1 API';
                }
            }
        } else {
            if (type != self.whatsappNotificationType) {
                apiOpts = self.getv1ApiOpts(data);
                self.L.log("templateName nuull apiOpts is {}", JSON.stringify(apiOpts));
            } else {
                throw 'Whatsapp notification not supported by v1 API';
            }
        }


        return apiOpts;
    }

    getReferenceIdForChat(custId) {
        let epochTimeInMillis = Date.now();
        let referenceId = `${epochTimeInMillis}_${custId}`;
        return referenceId;
    }

    validatev3ApiBody(data) {
        return _.get(data, 'templateName', null) && _.get(data, 'notificationReceiver', null);
    }

    getv3ApiOpts(body, type,category_id) {
        let self = this;
        const callbackURL = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'NOTIFICATION_CALLBACK_URL'], null);
        let v3ApiOpts = {
            "uri": _.get(self.config, ['NOTIFICATION', 'notificationapi', _.toUpper(type) + 'APIURL']),
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "client_id": _.get(self.config, ['NOTIFICATION', 'notificationapi', 'V3_API_CLIENT_ID']),
                "secret_key": _.get(self.config, ['NOTIFICATION', 'notificationapi', 'V3_API_SECRET_KEY'])
            },
            "callback":{
                "method":"post",
                "url": callbackURL
            },
            json: body
        };
        if(category_id && category_id == 41){
            v3ApiOpts.headers["client_id"] = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'V3_API_CLIENT_ID_FOR_HEURISTIC']);
            v3ApiOpts.headers["secret_key"] = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'V3_API_SECRET_KEY_FOR_HEURISTIC']);
        }
        return v3ApiOpts;
    }

    getv1ApiOpts(body) {
        let self = this;
        let v1ApiOpts = {
            "uri": _.get(self.config, ['NOTIFICATION', 'notificationapi', 'APIURL']),
            "method": "POST",
            json: body
        };
        return v1ApiOpts;
    }

    processv3ApiOpts(data, templateName, categoryId, type, notificationRecord) {
        let self = this;
        let templateType = _.get(data, 'template_type');
        if (templateType == 'push') {
            return self.getPushNotiData(data, templateName, categoryId, type, notificationRecord);
        } else if (templateType == 'sms') {
            return self.getSmsNotiData(data, templateName, type);
        } else if (templateType == 'email') {
            return self.getEmailNotiData(data, templateName, type);
        } else if (templateType == 'whatsapp') {
            return self.getWhatsappNotiData(data, templateName, type);
        } else {
            return null;
        }
    }

    handleEmojisInPayload(data, type) {
        let self = this;
        try {
            let paramsWithEmojiFields = _.get(data, 'dynamicParams', null) ? _.get(data, 'dynamicParams', null) : _.get(data, 'options.data', null);
            let service = _.toLower(_.get(paramsWithEmojiFields, 'service', null));
            Object.keys(paramsWithEmojiFields).forEach(key => {
                if (key.includes("emoji")) {
                    paramsWithEmojiFields[key] = JSON.parse('"' + paramsWithEmojiFields[key] + '"');
                }
            });
            self.logger.log(`handleEmojisInPayload:: modified data is and type is ${type}`, data, service);
        } catch(e) {
            let paramsWithEmojiFields = _.get(data, 'dynamicParams', null) ? _.get(data, 'dynamicParams', null) : _.get(data, 'options.data', null);
            let service = _.toLower(_.get(paramsWithEmojiFields, 'service', null));
            self.logger.error(`handleEmojisInPayload:: error occurred for data ${e} and type ${type}`, data, service);
        }
    }

    async checkServiceLevelNotificationCapping(row, cb) {
        /**
        * 1. when new consumer starts initially 
        *      - serviceBatchCount = 0
        *      - serviceNotificationCount = 0
        * 2. if serviceNotificationCount == 0
        *      - fetch latest service notification count from cassandra
        * 3. check serviceNotificationCount >= serviceNotificationCapping
        *      - drop notification
        * 4. else 
        *      - serviceNotificationCount++
        *      - serviceBatchCount++;
        *      - if (serviceBatchCount == 100)
        *              - update service_notification_capping table with latest count
        *              - serviceBatchCount = 0
        *              - serviceNotificationCount = fetchLatestCount from cassandra
        */
        
        let self = this;
        let type = _.get(row, 'type', null);
        let service = _.toLower(_.get(row, ['data', 'dynamicParams', 'service'], null));
        try {
            if (type == 'WHATSAPP') {
                self.L.log('checkServiceLevelNotificationCapping::', `checking service notification capping for ${type} notifications`);
                let date = MOMENT().format('YYYY-MM-DD');
                let serviceNotificationCount = Number(_.get(self.serviceNotificationCountObj, service, 0));
                if (serviceNotificationCount == 0) {
                    serviceNotificationCount = await self.cassandraBills.fetchServiceNotificationCount(service, date, type);
                    _.set(self.serviceNotificationCountObj, service, serviceNotificationCount);
                }
                
                let serviceNotificationCapping = Number(_.get(self.config, ['DYNAMIC_CONFIG', 'SERVICE_LEVEL_NOTIFICATION_CAPPING', _.toUpper(service), type], 0));
                if (Number(_.get(self.serviceNotificationCountObj, service, 0)) >= serviceNotificationCapping) {
                    self.L.log('checkServiceLevelNotificationCapping::', `service notification capping reached for ${type} notifications, dropping current notification`);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE: SERVICE_NOTIFICATION_CAPPING`,
                        `STATUS: DROP_NOTIFICATION`,
                        `TYPE: ${type}`,
                        `SERVICE: ${service ? _.toUpper(service) : 'UNKNOWN'}`
                    ]);
                    return cb(null, 'DROP');
                }
                _.set(self.serviceNotificationCountObj, service, serviceNotificationCount + 1);
                
                let serviceNotificationBatchSize = Number(_.get(self.serviceNotificationBatchSizeObj, service, 0));
                serviceNotificationBatchSize += 1;
                _.set(self.serviceNotificationBatchSizeObj, service, serviceNotificationBatchSize);
                let batchSizeConfig = Number(_.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'SERVICE_NOTIFICATION_CAPPING', 'BATCH_UPDATE_SIZE'], 100));
                if (serviceNotificationBatchSize >= batchSizeConfig) {
                    self.L.log('checkServiceLevelNotificationCapping::', `current batch size ${serviceNotificationBatchSize}, updating count and fetching latest service notification count`);
                    await self.cassandraBills.updateServiceNotificationCount(service, date, type, 0);
                    serviceNotificationCount = await self.cassandraBills.fetchServiceNotificationCount(service, date, type);
                    _.set(self.serviceNotificationCountObj, service, serviceNotificationCount);
                    self.logger.log('checkServiceLevelNotificationCapping:: current service notification count obj is', self.serviceNotificationCountObj, service);
                    // setting service notification batch size to 0 again
                    _.set(self.serviceNotificationBatchSizeObj, service, 0);
                }

                self.L.log('checkServiceLevelNotificationCapping::', `check passed, sending ${type} notification for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(row, 'record_key', null)) : _.get(row, 'record_key', null)}`);
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE: SERVICE_NOTIFICATION_CAPPING`,
                    `STATUS: SEND_NOTIFICATION`,
                    `TYPE: ${type}`,
                    `SERVICE: ${service ? _.toUpper(service) : 'UNKNOWN'}`
                ]);
                return cb(null, 'SEND');
            } else {
                return cb(null, 'SEND');
            }
        } catch (error) {
            self.L.error('checkServiceLevelNotificationCapping::', `error occurred for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(row, 'record_key', null)) : _.get(row, 'record_key', null)}, err ${error}`);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: SERVICE_NOTIFICATION_CAPPING`,
                `STATUS: ERROR_OCCURRED`,
                `TYPE: ${type}`,
                `SERVICE: ${service ? _.toUpper(service) : 'UNKNOWN'}`
            ]);
            return cb(error);
        }
    }

    getEmailNotiData(data, templateName, type) {
        let self = this;
        let body = {
            "debug": false,
            "notificationReceiver": {
                "notificationReceiverType": "EMAIL",
                "notificationReceiverIdentifier": [_.get(data, ['options', 'notificationOpts', 'recipients'])]
            },
            "templateName": templateName,
            "sender": {
                "name": "Paytm",
                "email": "<EMAIL>"
            },
            "replyTo": "<EMAIL>",
            "dynamicParams": _.get(data, ['options', 'data'])
        };

        let apiOpts = self.getv3ApiOpts(body, type);
        return apiOpts;
    }

    getPushNotiData(data, templateName, categoryId, type, notificationRecord) {
        let self = this;
        let notiExpiry;
        let hrs;
        if (categoryId == 1) {
            hrs = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(data, 'template_id'), 'NOTIFICATION_EXPIRY'], self.evalBillNotiExpiry(data)) || 48;
            notiExpiry = MOMENT().add(hrs, 'hour').unix();
        } else {
            hrs = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_CONFIG', _.get(data, 'template_id'), 'NOTIFICATION_EXPIRY'], 48);
            notiExpiry = MOMENT().add(hrs, 'hour').unix();
        }
        let styleValueImg = _.get(data, ['options', 'data', 'thumbnail'],'');
        let body = {
            "templateName": templateName,
            "debug": false,
            "sendBroadcastPush": true,
            "messageCentrePush": {
                "iconImage": _.get(data, ['options', 'data', 'thumbnail']),
                "expiry": notiExpiry,
                "extraParams": _.get(data, ['options', 'notificationOpts', 'deepLinkObj', 'extra'])
            },
            "notificationReceiver": {
                "notificationReceiverType": "CUSTOMERID",
                "notificationReceiverIdentifier": [_.get(data, ['options', 'notificationOpts', 'recipients'])]
            },
            "extraCommonParams": _.get(data, ['options', 'notificationOpts', 'deepLinkObj', 'extra']),
            "deviceType": ["ANDROIDAPP", "IOSAPP"],
            "dynamicParams": _.get(data, ['options', 'data'])
        };
       

        let template_id =  _.get(data, 'template_id');

        if (template_id && self.templatesIgnoreImgUrl.includes(template_id.toString())){
                        
            body.messageCentrePush.iconImage = null;
        }else{
            if(styleValueImg && styleValueImg != ''){
                body.stylePush=  { 
                        "styleType": "BIG_PICTURE",
                        "styleValue": styleValueImg
                    }
            }
        }

        self.notificationLib.append2CTAPayload(body, notificationRecord);

        let apiOpts = self.getv3ApiOpts(body, type,categoryId);
      

        return apiOpts;
    }

    evalBillNotiExpiry(data) {
        let self = this;
        try {
            let dueDate = MOMENT(_.get(data, ['options', 'data', 'due_date']), 'Do MMM YYYY', true);
            let expiryTime;
            if (dueDate.diff(MOMENT(), 'days') <= 1) {
                expiryTime = dueDate.add(2, 'days').diff(MOMENT(), 'hour');
            } else {
                expiryTime = dueDate.subtract(1, 'days').diff(MOMENT(), 'hour');
            }
            return expiryTime;
        } catch (error) {
            self.L.error('notify :: evalBillExpiry', 'Error while evaluating notification expiry time', error);
            return 48; // default 48 hrs
        }
    }

    getSmsNotiData(data, templateName, type) {
        let self = this;
        let body = {
            "templateName": templateName,
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [_.get(data, ['options', 'notificationOpts', 'recipients'])]
            },
            "dynamicParams": _.get(data, ['options', 'data'])
        };

        let apiOpts = self.getv3ApiOpts(body, type);
        return apiOpts;
    }

    getWhatsappNotiData(data, templateName, type) {
        let self = this;
        let body = {
            "templateName": templateName,
            "notificationReceiver": {
                "notificationReceiverType": "MOBILENUMBER",
                "notificationReceiverIdentifier": [_.get(data, ['options', 'notificationOpts', 'recipients'])]
            },
            "dynamicParams": _.get(data, ['options', 'data'])
        };

        let apiOpts = self.getv3ApiOpts(body, type);
        return apiOpts;
    }

    prepareResponse(callback, row, error, response, body, uri) {
        let self = this;
        // self.L.log("Preparing response for row:: ", JSON.stringify(row), body)
        _.set(row,['timestamps','centralNotifyApiHitTime'], new Date().getTime())
        let service = _.toLower(_.get(row, 'service') || _.get(row, 'data.dynamicParams.service') || _.get(row, 'data.options.data.service'));
        self.smsParsingLagDashboard.publishDelaysMetrics((err)=>{
            let responseObj = {};
            let retryStatusCodes = [429, 502, 504];
            let invalidTemplatesBodyCodes = [408];
            responseObj.sent_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');

            if (error || retryStatusCodes.indexOf(_.get(response, 'statusCode', null)) > -1) {
                if (row.max_retry_count === row.retry_count) {
                    responseObj.status = _.get(self.status_list, 'ERROR', null);
                } else {
                    responseObj.retry_count = row.retry_count + 1;
                    responseObj.send_at = MOMENT().add(row.retry_interval, 'minutes').format('YYYY-MM-DD HH:mm:ss');
                    responseObj.status = _.get(self.status_list, 'RETRY', null);
                }
                responseObj.error_msg = (error || 'Invalid statcode: ') + (response && response.statusCode ? response.statusCode : '');
            } else if (body && (_.get(body, 'error', null) || _.get(body, 'status', null) == 'FAILURE')) {
                let error = _.get(body, 'error', null) || _.get(body, 'message', body);
                responseObj.status = _.get(self.status_list, 'ERROR', null);
                responseObj.error_msg = "API ERROR: " + JSON.stringify(error) + (body.code ? body.code : '');
                if (invalidTemplatesBodyCodes.includes(_.get(body, 'code', null))) {
                    self.L.log('prepareResponse::', `invalid template id ${_.get(row, 'template_id', null)} found`);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:TEMPLATE_VALIDATION`,
                        `STATUS:INVALID_TEMPLATE_NOTIFICATION_FAILED`,
                        `TEMPLATE_ID:${_.get(row, 'template_id', null)}`
                    ]);
                }
            } else if (body && (body.data || body.jobId)) {  // body.data in v1 api response and body.jobId in v3 api response
                responseObj.status = _.get(self.status_list, 'SENT', null);
                responseObj.job_id = _.get(body, ['data', 'job_id'], _.get(body, 'jobId', null));
                L.info('Notification sent for record: ' + (service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(row, 'record_key', null)) : _.get(row, 'record_key', null)) + ' and job_id: ' + responseObj.job_id);
            } else {
                responseObj.status = _.get(self.status_list, 'ERROR', null);
            }
            if (responseObj.error_msg) {
                L.error('Not able to notify for record: ', (service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(row, 'record_key', null)) : _.get(row, 'record_key', null)), responseObj.error_msg);
            }

            var status = '' + (response && response.statusCode ? response.statusCode : '5xx');
            var statusMask = status[0] + 'xx';
            var bodyCode = '' + (body && body.code ? body.code: 500)

            self.L.log('prepareResponse::',`NOTIFICATION_TYPE:${_.get(row,'type','UNKNOWN')}_CATEGORY_ID:${_.get(row,'category_id','UNKNOWN')}_SOURCE_ID:${_.get(row,'source_id','UNKNOWN')}_STATUS:${status}_URL:${uri}_RECHARGE_NUMBER:${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(row, 'recharge_number', '')) : _.get(row, 'recharge_number', '')}`);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:NOTIFICATION',  `NOTIFICATION_TYPE:${_.get(row,'type','UNKNOWN')}` , `CATEGORY_ID:${_.get(row,'category_id','UNKNOWN')}`, `SOURCE_ID:${_.get(row,'source_id','UNKNOWN')}` ,'CODE:'+ bodyCode, 'STATUS:' + status, 'STATUS_MASK:' + statusMask, 'URI:' + uri, 'BILL_SOURCE_TYPE:' + self.notificationBillSource, `TEMPLATE_ID:${_.get(row,'template_id','UNKNOWN')}`,'PARTIAL_BILL:'+`${_.get(row,"partialBillState")}`]);

          callback(responseObj);
        },_.get(row,'source_id',null),_.get(row,'timestamps',{}),'',null);
    }

    prepareResponseForPayloadNotSupportedByV1(type) {
        let self = this;
        let responseObj = {
            "status": _.get(self.config, ['NOTIFICATION', 'status', 'DISABLED'], 8),
            // "sent_at": MOMENT().format('YYYY-MM-DD HH:mm:ss'),
            "error_msg": `Notification type ${type} not supported for v1 API`,
        }

        return responseObj;
    }

    /** ccbp encryption changes to be done here */
    updateNotification(results, done) {
        let self = this;
        ASYNC.eachOf(
            results,
            (result, recordKey, next) => {
                let fields = [];
                let whereCondition = 'id = ?';
                let params = [];
                let updatedRecord = _.cloneDeep(result.record);
                for (let key in result.response) {
                    fields.push(key);
                    params.push(result.response[key]);
                    _.set(updatedRecord, key, result.response[key]);
                }
                let sourceId = _.get(updatedRecord, 'source_id', null);
                let customerId = _.get(updatedRecord, 'data.additional_data.customer_id', null);
                let service = _.toLower(_.get(updatedRecord, 'service') || _.get(updatedRecord, 'data.dynamicParams.service') || _.get(updatedRecord, 'data.options.data.service'));
                self.L.log('notify::updateNotification::', `sourceId is ${sourceId} customerId is ${customerId} for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`);
                /**
                 * if status == RETRY
                 *      DELETE OLD ENTRY with old send_at value in Cassandra
                 *      CREATE NEW ENTRY with new send_at value in Cassandra
                 *      UPDATE record in mysql
                 */

                if (self.isNotificationMigratedToCassandra(sourceId, customerId)) {
                    let record = result.record;
                    let notif_params = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(updatedRecord);
                    let isCCEncryptionEnabled = self.EncryptionDecryptioinHelper.isWhitelistedForCC(notif_params.service, notif_params.paytype, notif_params.customerId);
                    let rec_recharge_number = _.get(record, 'recharge_number', null);
                    let rec_recipient = _.get(record, 'recipient', null);
                    if(isCCEncryptionEnabled && self.cassandraBills.checkIfNotifRecordIsEncrypted(record)) {
                        rec_recharge_number = self.EncryptionDecryptioinHelper.encryptData(rec_recharge_number);
                        rec_recipient = self.EncryptionDecryptioinHelper.encryptData(rec_recipient);
                    }

                    let whereConditionCassandra = `recharge_number = ? AND recipient = ? AND type = ? AND template_id = ? AND source_id = ? AND category_id = ? AND product_id = ? AND send_at = ?`;
                    
                    let whereConditionCassandraParams = [
                        String(rec_recharge_number), String(rec_recipient), _.get(record, 'type', null),
                        _.get(record, 'template_id', null), _.get(record, 'source_id', null), _.get(record, 'category_id', null),
                        _.get(record, 'product_id', null), _.get(record, 'send_at', null) 
                    ];
                    utility._sendMetricsToDD(1, [
                        `FLOW:UPDATE_NOTIFICATION`,
                        `DATABASE:CASSANDRA`,
                        `STATUS:TRAFFIC`,
                        `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                        `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                        `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                    ]);

                    if (updatedRecord.status === _.get(self.status_list, 'RETRY', null)) {
                        self.cassandraBills.deleteNotification((err, result) => {
                            if (err) {
                                self.L.error('notify::updateNotification::deleteNotificationCallback::', `error occurred for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`, err);
                                self.sendDatabaseErrorMetrics('CASSANDRA', 'UPDATE_NOTIFICATION');
                                next(err);
                            }
                            let isDeleted = _.get(result.rows[0], '[applied]', false);
                            if (isDeleted) {
                                self.L.log('notify::updateNotification::deleteNotificationCallback::', `deleted prev record from cassandra for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`);
                            } else {
                                self.L.critical('notify::updateNotification::deleteNotificationCallback::', `couldn't find prev record in cassandra hence no delete action occurred for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`);
                                utility._sendMetricsToDD(1, [
                                    `FLOW:UPDATE_NOTIFICATION`,
                                    `DATABASE:CASSANDRA`,
                                    `STATUS:PREV_RECORD_NOT_DELETED`,
                                    `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                    `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                ]);
                            }
                            _.set(updatedRecord, 'notificationStatus', _.get(updatedRecord, 'status'));
                            self.createNotificationInCassandra((err) => {
                                let updatedRecordKey = `${_.get(updatedRecord, 'recharge_number', null)}_${_.get(updatedRecord, 'recipient', null)}_${_.get(updatedRecord, 'type', null)}_${_.get(updatedRecord, 'template_id', null)}_${_.get(updatedRecord, 'source_id', null)}_${_.get(updatedRecord, 'category_id', null)}_${_.get(updatedRecord, 'product_id', null)}_${_.get(updatedRecord, 'send_at', null)}`;
                                if (err) {
                                    self.L.error('notify::updateNotification::createNotificationInCassandraCallback::', `error occurred for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(updatedRecordKey) : updatedRecordKey}`, err);
                                    self.sendDatabaseErrorMetrics('CASSANDRA', 'UPDATE_NOTIFICATION');
                                    next(err);
                                }
                                    self.L.log('notify::updateNotification::createNotificationInCassandraCallback::', `new row created in cassandra for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(updatedRecordKey) : updatedRecordKey}`);
                                // create or update record in mysql for retry mechanism
                                if (_.get(updatedRecord, 'id', null) !== null) {
                                    params.push(_.get(updatedRecord, 'id', null));
                                    self.L.log('notify::updateNotification::createNotificationInCassandraCallback::', `updating notification entry in mysql db for retry for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey} with mysql id ${_.get(updatedRecord, 'id', null)}`);
                                    self.notification.updateNotification((err, data) => {
                                        if (err) {
                                            self.L.error('notify::updateNotification::', `error occurred for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(updatedRecordKey) : updatedRecordKey}`, err);
                                            self.sendDatabaseErrorMetrics('MYSQL', 'UPDATE_NOTIFICATION');
                                        } else {
                                            utility._sendMetricsToDD(1, [
                                                `FLOW:UPDATE_NOTIFICATION`,
                                                `DATABASE:CASSANDRA`,
                                                `STATUS:RECORD_UPDATE_SUCCESS`,
                                                `NOTIFICATION_STATUS:RETRY`,
                                                `MYSQL_RECORD:EXISTS_FOR_RETRY`,
                                                `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                                `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                            ]);
                                        }
                                        next(err);
                                    }, fields, whereCondition, params);
                                } else {
                                    self.L.log('notify::updateNotification::createNotificationInCassandraCallback::', `creating notification entry in mysql db for retry for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(updatedRecordKey) : updatedRecordKey}`);
                                    self.createNotificationMysqlForRetry((err) => {
                                        if (err) {
                                            self.sendDatabaseErrorMetrics('MYSQL', 'UPDATE_NOTIFICATION');
                                        } else {
                                            utility._sendMetricsToDD(1, [
                                                `FLOW:UPDATE_NOTIFICATION`,
                                                `DATABASE:CASSANDRA`,
                                                `STATUS:RECORD_UPDATE_SUCCESS`,
                                                `NOTIFICATION_STATUS:RETRY`,
                                                `MYSQL_RECORD:CREATE_FOR_RETRY`,
                                                `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                                `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                            ]);
                                        }
                                        next();
                                    }, updatedRecord);
                                }
                            }, updatedRecord);
                        }, whereConditionCassandra, whereConditionCassandraParams, result.record.send_at);
                    } else if (updatedRecord.status === _.get(self.status_list, 'SENT', 1) || updatedRecord.status === _.get(self.status_list, 'ERROR', 3) || updatedRecord.status === _.get(self.status_list, 'CANCELED', 2)) {
                        let updated_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
                        fields.push('updated_at');
                        params.push(updated_at);
                        _.set(updatedRecord, 'updated_at', updated_at);
                        let paramsCassandra = params.concat(whereConditionCassandraParams);
                        self.updateNotificationInCassandra((err) => {
                            if (err) {
                                self.L.error('notify::updateNotification::updateNotificationInCassandraCallback::', `update failed for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`, err);
                                utility._sendMetricsToDD(1, [
                                    `FLOW:UPDATE_NOTIFICATION`,
                                    `DATABASE:CASSANDRA`,
                                    `STATUS:RECORD_UPDATE_FAILURE`,
                                    `NOTIFICATION_STATUS:SENT_OR_ERROR`,
                                    `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                    `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                ]);
                                next(err);
                            }
                            self.L.log('updateNotification::', `notification record updated in cassandra notification table for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`);
                            // if status = SENT or ERROR or CANCELED - delete entry from mysql if present
                            if (_.get(updatedRecord, 'id', null) !== null) {
                                // implies record's entry exists in mysql notification table
                                self.L.log('updateNotification::', `deleting notification entry from mysql db for ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey} since status is ${updatedRecord.status} mysql id is ${_.get(updatedRecord, 'id', null)}`);
                                self.notification.deleteNotification((err) => {
                                    if (err) {
                                        self.L.error('notify::updateNotification::', `error occurred while delete notification from mysql for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`, err);
                                        self.sendDatabaseErrorMetrics('MYSQL', 'UPDATE_NOTIFICATION');
                                    } else {
                                        utility._sendMetricsToDD(1, [
                                            `FLOW:UPDATE_NOTIFICATION`,
                                            `DATABASE:CASSANDRA`,
                                            `STATUS:RECORD_UPDATE_SUCCESS`,
                                            `NOTIFICATION_STATUS:SENT_OR_ERROR`,
                                            `MYSQL_RECORD:DELETED_FROM_MYSQL`,
                                            `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                            `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                            `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                        ]);
                                    }
                                    next(err);
                                }, 'id = ?', [_.get(updatedRecord, 'id', null)]);
                            } else {
                                // no entry present in mysql notification table
                                utility._sendMetricsToDD(1, [
                                    `FLOW:UPDATE_NOTIFICATION`,
                                    `DATABASE:CASSANDRA`,
                                    `STATUS:RECORD_UPDATE_SUCCESS`,
                                    `NOTIFICATION_STATUS:SENT_OR_ERROR`,
                                    `MYSQL_RECORD:NO_ENTRY_FOUND`,
                                    `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                    `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                ]);
                                next();
                            }
                        }, fields, whereConditionCassandra, paramsCassandra, updatedRecord);
                    }
                    else {
                        self.L.error('notify::updateNotification::', `obtained unknown status ${updatedRecord.status} for record ${service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`);
                        utility._sendMetricsToDD(1, [
                            `FLOW:UPDATE_NOTIFICATION`,
                            `DATABASE:CASSANDRA`,
                            `STATUS:UNKNOWN_NOTIF_STATUS_OBTAINED`,
                            `NOTIFICATION_STATUS:${updatedRecord.status}`,
                            `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                            `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                            `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                        ]);
                        next();
                    }
                }  else if(recordKey && recordKey!='undefined'){
                    // recordKey will contain mysql notification record id in this case
                    params.push(recordKey);
                    self.notification.updateNotification((error, data) => {
                        if (error) {
                            self.L.error('notify::updateNotification::', `error occurred during update notification in mysql for id ${recordKey}`, error);
                            self.sendDatabaseErrorMetrics('MYSQL', 'UPDATE_NOTIFICATION');
                        } else {
                            L.info('updateNotification: notification entries is updated in the database for id: ', service == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey);
                            utility._sendMetricsToDD(1, [
                                `FLOW:UPDATE_NOTIFICATION`,
                                `DATABASE:MYSQL`,
                                `STATUS:RECORD_UPDATE_SUCCESS`,
                                `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                                `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ]);
                        }
                        next();
                    }, fields, whereCondition, params);
                } else {
                    self.logger.error('updateNotification:: Not updating DB as recordKey is not present for record' , updatedRecord, service);
                    utility._sendMetricsToDD(1, [
                        `FLOW:UPDATE_NOTIFICATION`,
                        `STATUS:RECORD_KEY_NOT_FOUND`,
                        `SOURCE_ID:${_.get(updatedRecord, 'source_id', 'UNKNOWN')}`,
                        `CATEGORY_ID:${_.get(updatedRecord, 'category_id', 'UNKNOWN')}`,
                        `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                    ]);
                    next();
                }
            },
            (err) => {
                if (err) {
                    self.L.error('updateNotification::', `error occurred`, err);
                }
                done();
            }
        );
    }

    createNotificationMysqlForRetry(cb, record) {
        let insertRecord = _.cloneDeep(record);
        let data = _.get(insertRecord, 'data', null);
        let self = this;
        let paramsWithEmojiFields = _.get(data, 'dynamicParams', null) ? _.get(data, 'dynamicParams', null) : _.get(data, 'options.data', null);
        let payloadService = _.toLower(_.get(paramsWithEmojiFields, 'service', null));
        Object.keys(paramsWithEmojiFields).forEach(key => {
            if (key.includes("emoji")) {
                paramsWithEmojiFields[key] = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES', key], null);
            }
        });
        let recordKey = `${_.get(record, 'recharge_number', null)}_${_.get(record, 'recipient', null)}_${_.get(record, 'type', null)}_${_.get(record, 'template_id', null)}_${_.get(record, 'source_id', null)}_${_.get(record, 'category_id', null)}_${_.get(record, 'product_id', null)}_${_.get(record, 'send_at', null)}`;
        self.notification.createNotificationForRetry((err, data, status) => {
            if (err) {
                self.L.error('notify::createNotificationMysqlForRetry::', `error occurred while notification create for record ${payloadService == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`, err);
            } else {
                self.L.log('notify::createNotificationMysqlForRetry::', `successfully created entry in notification table for retry for ${payloadService == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(recordKey) : recordKey}`);
            }
            cb(err);
        }, insertRecord);
    }

    validate(callback, notifier) {

        let self = this,
            source_id = _.get(notifier, 'source_id', null),
            category_id = _.get(notifier, 'category_id', null),
            type = _.get(self.notificationConfig, ['type', _.get(notifier, 'type', null).toUpperCase()], null),
            template_id = _.get(notifier, 'template_id', null),
            recipient = _.get(notifier, 'recipient', null),

            source = _.get(self.notificationConfig, ['source', source_id], null),
            category = _.get(self.notificationConfig, ['category', category_id], null),
            priority = _.get(self.notificationConfig, ['categorySourceMap', category_id, source_id], null),

            data = _.get(notifier, 'data', null),
            send_at = _.get(notifier, 'send_at', null),
            
            last_ref_time = MOMENT().subtract(_.get(notifier, 'time_interval', 1440), 'minutes').format('YYYY-MM-DD HH:mm:ss');
            if(self.notificationBillSource == 'NONRU'){
                source = _.get(self.notificationConfig, ['nonRuSource', source_id], null);
                category = _.get(self.notificationConfig, ['nonRuCategory', category_id], null);
                priority = _.get(self.notificationConfig, ['nonRuCategorySourceMap', category_id, source_id], null);
            }

        try {
            _.set(notifier, 'priority', priority);
            _.set(notifier, 'last_ref_time', last_ref_time);
            let isRU= self.notificationBillSource !== 'NONRU' ? true : false;
            _.set(notifier, 'meta.isRU', isRU);

            let paramsKey = ['type', 'template_id', 'recipient', 'source', 'category', 'priority', 'data', 'send_at'],
                paramsValue = [type, template_id, recipient, source, category, priority, data, send_at];

            if (_.get(notifier, 'type', null) == 'CHAT') {
                // Required params for chat
                paramsKey = paramsKey.concat(['templateName', 'channelId']);
                paramsValue = paramsValue.concat([_.get(notifier, 'data.templateName', null), _.get(notifier, 'data.senderData.identifier', null)]);
            }

            let missingParam = null;
            let isMandatoryParamsMissing = paramsValue.some((value, index) => {
                if (!value) {
                    missingParam = paramsKey[index];
                    utility.sendNotificationMetricsFromSend(notifier, "ERROR","MISSING_PARAMS")
                    L.error('required param ' + missingParam + ' is null');
                    return true;
                }
            });

            // Validate next_schedules array as future dates
            let errorInSchedules = false,
                errorInSchedulesTemplate =false,
                errorDates = [];
            if (notifier.next_schedules) {
                notifier.next_schedules.forEach((schedule) => {
                    if(typeof schedule === 'object'){
                        if (!(schedule && schedule.template_details  && schedule.template_details.template_id) || !(schedule && schedule.deepLinkObj)){
                            errorInSchedulesTemplate = true;
                            errorDates.push(JSON.stringify(schedule));
                        }
                    }
                    schedule = typeof schedule === 'object' ? schedule.date : schedule;
                    if (!MOMENT(schedule).isAfter(MOMENT().format('YYYY-MM-DD HH:mm:ss'))) {
                        errorInSchedules = true;
                        errorDates.push(schedule);
                    }
                });
            }

           
            if(source_id == '4' && _.get(notifier, 'type', null) == 'WHATSAPP'){
                utility.sendNotificationMetricsFromSend(notifier, "ERROR","INVALID_SOURCE_ID_FOR_WHATSAPP")
                return callback('not sending whatsapp notification for source id 4')

            }

            if (isMandatoryParamsMissing) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                    'STATUS:ERROR',
                    'TYPE:MANDATORY_PARAM_MISSING', 
                    `MISSING_PARAM:${missingParam}`, 
                    `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                    `CATEGORY_ID: ${category_id}`,
                    `SOURCE_ID :${source_id}` ,
                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                ]);
                utility.sendNotificationMetricsFromSend(notifier, "ERROR","MISSING_PARAMS")
                return callback(missingParam + ' can not be null');
            } else if (errorInSchedulesTemplate) {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                    'STATUS:ERROR',
                    'TYPE:ERROR_IN_SCHEDULED_TEMPLATE', 
                    `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                    `CATEGORY_ID: ${category_id}`,
                    `SOURCE_ID :${source_id}` ,
                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                ]);
                utility.sendNotificationMetricsFromSend(notifier, "ERROR","MISSING_TEMPLATE_DETAILS")
                return callback(`next_schedules: template_details/deepLinkObj should be in present - ${errorDates.join()}`)
            }
             else if (errorInSchedules) {
                utility.sendNotificationMetricsFromSend(notifier, "ERROR","ERROR_IN_SCHEDULED_DATE")
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                    'STATUS:ERROR',
                    'TYPE:ERROR_IN_SCHEDULED_DATE', 
                    `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                    `CATEGORY_ID: ${category_id}`,
                    `SOURCE_ID :${source_id}` ,
                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                ]);
                return callback(`next_schedules: Dates should be in future - ${errorDates.join()}`)
            } else {
                utility._sendMetricsToDD(1, [
                    "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                    'STATUS:SUCCESS','TYPE:MAIN_FLOW_EXECUTION',
                    `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                    `CATEGORY_ID: ${category_id}`,
                    `SOURCE_ID :${source_id}` ,
                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                ]);
                callback(null);
            }
        } catch (error) {
            utility.sendNotificationMetricsFromSend(notifier, "ERROR","ERROR_WHILE_VALIDATE")
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:NOTIFICATION_RECORD_VALIDATION", 
                'STATUS:ERROR',
                'TYPE:ERROR_WHILE_VALIDATE',
                `TOPIC:${_.get(self.kafkaConsumerConfig, 'TOPIC', null)}`,
                `CATEGORY_ID: ${category_id}`,
                `SOURCE_ID :${source_id}` ,
                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
            ]);
            callback(error.message);
        }
    }

    getDataFromCassandra(key,cb,param={}){
        var self=this;
        if(!key || key=='') return cb('cassandraKey not found');
        var latencyStart = new Date().getTime(); 
        self.cassandraBills.getNotificationCache((err, cassandraData) => {
            if(new Date().getTime()!=latencyStart){
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_QUERY', 'TYPE':'GET'});
            }
            if(err){
                self.L.error('Notify:: getDataFromCassandra: error while getting data from cassandra for key', key,err);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_QUERY", 'STATUS:ERROR', `TYPE:GET`]);
                return cb(err);
            } else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_QUERY", 'STATUS:SUCCESS', `TYPE:GET`]);
                return cb(null,cassandraData);
            }
        }, {key: key}, param);
    }

    getDataFromCassandraForDuplicacy(notifier, cb, getSetQuery=false){
        let self = this;
        let enabledSources = getSetQuery ? _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','WRITE_NOTIFICATION_VIA_UNIQUE_KEY_ENABLED'], []) : _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_NOTIFICATION_VIA_UNIQUE_KEY_ENABLED'], []);
        /** ccbp encrytion changes needed here, to check if key contains recharge_number or any other param */
        let key = _.get(notifier, 'cassandraKey', null);
        if(enabledSources.includes('ALL') || enabledSources.includes(_.toString(_.get(notifier,'source_id',null)))){
            key = _.get(notifier, 'notificationUniqueKey', null);
        }
        let param = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(notifier);
        return self.getDataFromCassandra(key, cb, param);
    }

    setDataInCassandraForDuplicacy(notifier, value, cb){
        let self=this;
        let enabledSources = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','WRITE_NOTIFICATION_VIA_UNIQUE_KEY_ENABLED'], []);
        let key1 = _.get(notifier, 'cassandraKey', null);
        let key2 = null;
        if(enabledSources.includes('ALL') || enabledSources.includes(_.toString(_.get(notifier,'source_id',null)))){
            key2 = _.get(notifier, 'notificationUniqueKey', null);
        }
        if(_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','STOP_WRITE_NOTIFICATION_VIA_CASSANDRA_KEY'], false)){
            key1 = null;
        }
        let param = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(notifier);
        
        let ttl = _.get(notifier, 'source_id') == 4 ? 
        _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','TTL_DATA_EXHAUST'], 10800) : // 3 hours in seconds
        null;

        self.L.log( 'source id is ' + _.get(notifier, 'source_id') + ' ttl is ' + ttl);

        ASYNC.parallel([
            function(callback){
                if(key1){
                    self.setDataInCassandra(key1, value, callback, param, ttl);
                }else{
                    callback(null);
                }
            },
            function(callback){
                if(key2){
                    self.setDataInCassandra(key2, value, callback, param, ttl);
                }else{
                    callback(null);
                }
            }
        ], function(err, results){
            return cb(err, results);
        })
    }

    setDataInCassandra(key, value, cb, param={}, ttl){
        var self=this;
        if(!key || key=='') return cb('cassandraKey not found');
        var latencyStart = new Date().getTime();
        let loggingKey = key;
        if(_.get(param, 'service', null) == 'financial services') {
            loggingKey = self.EncryptionDecryptioinHelper.encryptData(key);
        } 

      
        self.cassandraBills.updateNotificationCache((error, result) => {
            if(new Date().getTime()!=latencyStart){
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'CASSANDRA_QUERY', 'TYPE':'SET'});
            }
            if(error){
                this.L.critical('billReminderNotification :: setDataInCassandra', 'Unable to set key in cassandra', loggingKey, error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_QUERY", 'STATUS:ERROR', `TYPE:SET`]);
            }
            else{
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_QUERY", 'STATUS:SUCCESS', `TYPE:SET`]);
                this.L.info('billReminderNotification :: setDataInCassandra', 'cassandra key added/updated for', loggingKey);
            }
            return cb(error,result);
        }, {
            key     : key,
            value   : value ,
            ttl : ttl       
        }, param)
    }

    // checkPriority(cb,notifier,key){
    //     var self=this;
    //     let checkPriorityFromCassandra = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_PRIORITY_ENABLE_FOR_ALL_SOURCE_IDS'], false) ||
    //     (_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_PRIORITY_ENABLE_FOR_SOURCE_IDS'], []).includes(_.toString(_.get(notifier,'source_id',null))));
        
    //     if (checkPriorityFromCassandra) {
    //         self.L.log(`checkPriority:: checking priority through cassandra for source_id: ${_.get(notifier,'source_id',null)}`)
    //         self.getDataFromCassandra(key, function(err, data){
    //             self.L.verbose("key and data from cassandra for priority", key, data);
    //             if (err) {
    //                 self.L.log(`checkPriority:: err occurred from Cassandra for key: ${key}`);
    //                 self.sendDatabaseErrorMetrics('CASSANDRA', 'CREATE_NOTIFICATION');
    //                 if (self.isNotificationMigratedToCassandra(_.get(notifier,'source_id',null), _.get(notifier, 'data.additional_data.customer_id', null))) {
    //                     return cb(err);
    //                 } else {
    //                     // fallback to mysql notification table for checking priority
    //                     self.L.log(`checkPriority:: re-routing to sql-db to checkPriority for key: ${key}`)
    //                     self.notification.checkPriority((error, data) => {
    //                         if (error) {
    //                             self.L.error('notify::checkPriority::', `error occurred for ${key}`, error);
    //                             self.sendDatabaseErrorMetrics('MYSQL', 'CREATE_NOTIFICATION');
    //                         }
    //                         return cb(error, data);
    //                     }, notifier);
    //                 }
    //             } else {
    //                 if(data && _.get(data,'priority',null)!=null && _.get(data,'send_at',null)){
    //                     self.L.log(`checkPriority:: recieved value: ${JSON.stringify(data)} for key: ${key}`)
    //                     if((_.get(data,'priority',null) < _.get(notifier,'priority',null)) && (_.get(data,'send_at',null) > _.get(notifier,'last_ref_time',null))){
    //                         self.L.log('High priority notification exists for key: ', key);
    //                         return cb(null,[data]);
    //                     }else {
    //                         return cb(null);
    //                     }
    //                 }else {
    //                     return cb(null);
    //                 }
    //             }
    //         })
    //     } else {
    //         cb(null);
    //     }
    // }

    // wrapper to createNotification method to call it after data validation
    __createNotification(callback, notifier) {
        let self = this;

        self.validate(function (error) {
            if (error) {
                return callback(error);
            } else {
                return self.createNotification(callback, notifier);
            }
        }, notifier);
    }

    async insertRejectedNotificationsViaPromise(error, record, notificationRecord) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.insertRejectedNotifications(resolve, error, record, notificationRecord);
        });
    }

    additionalDuplicacyChecks(notifier, action, idsWithPendingStatus, next) {
        let self = this;
        
        // Early return if action is not create
        if (action !== 'create') {
            return next(null, action, idsWithPendingStatus);
        }

        let notificationType = _.get(notifier, 'type', null);

        let duplicacyCheckWithBasicApproach = self.duplicacyCheckWithBasicApproach;
        let duplicacyCheckWithAdvancedApproach = self.duplicacyCheckWithAdvancedApproach;

        // Helper function to handle duplicacy check results
        const handleDuplicacyCheck = (err, result) => {
            if (err) {
                return next(null, action, idsWithPendingStatus);
            }
            return next(null, result, idsWithPendingStatus);
        };

        // Determine which check to perform
        if (duplicacyCheckWithBasicApproach.includes(notificationType)) {
            self.performDuplicacyCheck(notifier, 'basic', handleDuplicacyCheck);
        } else if (duplicacyCheckWithAdvancedApproach.includes(notificationType)) {
            self.performDuplicacyCheck(notifier, 'advanced', handleDuplicacyCheck);
        } else {
            return next(null, action, idsWithPendingStatus);
        }
    }

    isWhitelistedService(service, checkType) {
        let self = this;

        let configPath = checkType === 'advanced' ? 
            'ADVANCED_APPROACH_DUPLICACY_CHECK_WHITELISTED_SERVICES' : 
            'BASIC_APPROACH_DUPLICACY_CHECK_WHITELISTED_SERVICES';
        
        let whitelistedServices = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'NOTIFICATION_CASSANDRA', configPath], ['electricity']);
        
        if(!(whitelistedServices.includes(service) || whitelistedServices.includes('ALL'))) {
            self.L.error(`notify::${checkType}DuplicacyCheck::`, `service not whitelisted ${service}`);
            return false;
        }
        return true;
    }

    async performDuplicacyCheck(notifier, checkType, cb) {
        let self = this;
        let service = _.toLower(self.extractParamFromDynamicParams(notifier, 'service'));
        if(!service) {
            self.L.error(`notify::${checkType}DuplicacyCheck::`, `service not found ${service}`);
            return cb('service not found');
        }

        if(!self.isWhitelistedService(service, checkType)) {
            self.L.error(`notify::${checkType}DuplicacyCheck::`, `service not whitelisted ${service}`);
            return cb('service not whitelisted');
        }

        try {
            let duplicateNotificationFound = await self.checkNotificationsInBatches(notifier, checkType);
            self.L.verbose(`${checkType}DuplicacyCheck::`, `duplicateNotificationFound ${duplicateNotificationFound}`);
            
            if(duplicateNotificationFound) {
                self.L.log(`notify::${checkType}DuplicacyCheck::`, `duplicateNotificationFound ${duplicateNotificationFound}`);
                utility._sendMetricsToDD(1, [
                    `FLOW:CREATE_NOTIFICATION`,
                    `REQUEST_TYPE:ADDITIONAL_DUPLICACY_CHECK_FROM_NOTIFICATIONS_TABLE`,
                    `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                    `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                    `TYPE:${_.get(notifier, 'type', 'UNKNOWN')}`,
                    `STATUS:DUPLICATE_NOTIFICATION_FOUND`,
                    `TEMPLATE_ID:${_.get(notifier, 'template_id', 'UNKNOWN')}`,
                ]);
                return cb(null, 'drop');
            } else {
                return cb(null, 'create');
            }
        } catch(error) {
            utility._sendMetricsToDD(1, [
                `FLOW:CREATE_NOTIFICATION`,
                `REQUEST_TYPE:ADDITIONAL_DUPLICACY_CHECK_FROM_NOTIFICATIONS_TABLE`,
                `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                `TYPE:${_.get(notifier, 'type', 'UNKNOWN')}`,
                `STATUS:ERROR`
            ]);
            self.L.error(`notify::${checkType}DuplicacyCheck::`, `error occurred during ${checkType}DuplicacyCheck for ${notifier}`, error);
            return cb(error);
        }
    }

    async checkNotificationsInBatches(row, checkType){
        let self=this;
        return new Promise((resolve,reject)=>{
            let [batchSize, time_interval, buffer_time_interval] = self.getBatchSizeAndTimeInterval(row);
            let currentTime = MOMENT();
            let last_ref_time = currentTime.subtract(buffer_time_interval, 'hours').valueOf();
            let customer_id = self.extractParamFromDynamicParams(row, 'customer_id');
            let service = self.extractParamFromDynamicParams(row, 'service');

            let [query, queryParams] = self.cassandraBills.getNotificationsQuery(customer_id, service, last_ref_time, row, batchSize);
            self.L.verbose(`checkNotificationsInBatches:: query ${query} and queryParams ${JSON.stringify(queryParams)}`);
            self.cassandraBills.getNotificationsFromCassandraTable(self,query,queryParams,row, checkType, time_interval, function(err){
                if(err){
                    resolve(true);
                }else{
                    resolve(false);
                }
            })
        })
    }

    getBatchSizeAndTimeInterval(row){
        let self = this;
        let source_id = _.toString(_.get(row, 'source_id', null));
        let category_id = _.toString(_.get(row, 'category_id', null));
        let notificationType = _.get(row, 'type', null);

        let specificKey = `${source_id}_${category_id}_${notificationType}`;
        let generalKey = `${category_id}_${notificationType}`;
        let defaultKey = `${notificationType}`;

        let batchSize = self.dbQueryDuplicacyCheckBatchSize;
        let time_interval = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','DUPLICACY_CHECK_TIME_INTERVAL', specificKey], 
            _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','DUPLICACY_CHECK_TIME_INTERVAL', generalKey], 
                _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','DUPLICACY_CHECK_TIME_INTERVAL', defaultKey], 24*7)
            )
        );

        self.L.log('getBatchSizeAndTimeInterval::', `specificKey ${specificKey}, time_value ${_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','DUPLICACY_CHECK_TIME_INTERVAL', specificKey],null)}`);
        self.L.log('getBatchSizeAndTimeInterval::', `generalKey ${generalKey}, time_value ${_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','DUPLICACY_CHECK_TIME_INTERVAL', generalKey],null)}`);
        self.L.log('getBatchSizeAndTimeInterval::', `defaultKey ${defaultKey}, time_value ${_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','DUPLICACY_CHECK_TIME_INTERVAL', defaultKey],null)}`);   
        self.L.log('getBatchSizeAndTimeInterval::', `batchSize ${batchSize}, time_interval ${time_interval}`);


        /* there can be difference in created_at and sent_at time, so we are adding 24 hours to the time_interval
        to avoid false positive of duplicate notification, after fetching rows, we will be filtering out rows where sent_at is between time_interval
        */
        return [batchSize, time_interval, time_interval + 24];
    }

    extractParamFromStringifiedData(item, param, paramsArray = null){
        let self = this;
        let paramValue = null;
        try{
            let item_data = JSON.parse(item.data);
            if(param) paramValue = _.get(item_data, param, null);
            else if(paramsArray){
                paramValue = paramsArray.map(param => _.get(item_data, param, null));
            }

        }catch(error){
            self.L.error('notify::extractParamFromStringifiedData::', `error occurred during extractParamFromStringifiedData for ${item}`, error);
            utility._sendMetricsToDD(1, [
                `FLOW:CREATE_NOTIFICATION`,
                `TYPE:ADDITIONAL_DUPLICACY_CHECK_FROM_NOTIFICATIONS_TABLE`,
                `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                `STATUS:ERROR`
            ]);
        }
        return paramValue;
    }

    filterResult(result,row, checkType, time_interval){
        let self=this;
        let filteredResult = [];

        //result is in array, we have to run a map function
        result.map((item)=>{
            let item_operator = self.extractParamFromStringifiedData(item, 'operator'),
                item_recharge_number = _.get(item, 'recharge_number', null),
                recharge_number = _.get(row, 'recharge_number', null);

            let params = {
                item_template_id : _.get(item, 'template_id', null),
                item_type : _.get(item, 'type', null),
                item_status : _.get(item, 'status', null),
                item_operator : item_operator,
                operator : self.extractParamFromAdditionalData(row, 'operator'),
                template_id : _.get(row, 'template_id', null),
                type : _.get(row, 'type', null),
                status : _.get(row, 'status', null)
            }

            // self.L.verbose('filterResult::', `params ${JSON.stringify(params)}, item_operator ${item_operator}, recharge_number ${recharge_number}, item_recharge_number ${item_recharge_number}`); 

            if(_.get(self.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR', _.toLower(item_operator), 'PREFIX'], "N.A") != "N.A"){
            let prefix = _.get(self.config, ['DYNAMIC_CONFIG', 'DUPLICATE_CA_NUMBER_OPERATOR', _.toLower(item_operator), 'PREFIX'], "N.A");
                if(item_recharge_number && item_recharge_number.startsWith(prefix)){
                    item_recharge_number = item_recharge_number.substring(prefix.length);
                }
                if(recharge_number && recharge_number.startsWith(prefix)){
                    recharge_number = recharge_number.substring(prefix.length);
                }
            }
            _.set(params, 'item_recharge_number', item_recharge_number);
            _.set(params, 'recharge_number', recharge_number);
            if(checkType === 'advanced'){
                self.filterResultForAdvancedApproach(item, row, filteredResult, params, time_interval);
            }else{
                self.filterResultForBasicApproach(item, row, filteredResult, params, time_interval);
            }
        })
        return filteredResult;
    }

    existingNotificationTiming(item, row, time_interval, isBasicApproach = false) {
        let self = this;
        try {
            let currentTime = MOMENT(),
                time_interval_timestamp = currentTime.subtract(time_interval, 'hours').valueOf();

            let sent_at = _.get(item, 'sent_at', null);
            let send_at = _.get(item, 'send_at', null);
            let current_send_at = _.get(row, 'send_at', null);
            let dateToBeUsed = send_at;

            self.L.log("existingNotificationTiming::", "sent_at", sent_at, "send_at", send_at, "dateToBeUsed", dateToBeUsed, "current_send_at", current_send_at, "time_interval", time_interval, "for recharge_number", row.recharge_number);
        
            if(sent_at && MOMENT(sent_at).isValid()) {
                dateToBeUsed = sent_at;
            }

            if(MOMENT(dateToBeUsed).isAfter(MOMENT(time_interval_timestamp))) {
                let hoursDifference = MOMENT(current_send_at).diff(MOMENT(dateToBeUsed), 'hours');
                
                if(isBasicApproach) {
                    return hoursDifference <= time_interval;
                }

                // Advanced approach - includes date comparison
                current_send_at = MOMENT(current_send_at).format('YYYY-MM-DD');
                dateToBeUsed = MOMENT(dateToBeUsed).format('YYYY-MM-DD');
                return MOMENT(current_send_at).diff(MOMENT(dateToBeUsed), 'days') <= 0 && hoursDifference <= time_interval;
            }
            
            return false;
        } catch(error) {
            self.L.error('notify::existingNotificationTiming::', `error occurred during existingNotificationTiming for ${item}`, error);
            return false;
        }
    }

    existingNotificationTimingForBasicApproach(item, row, time_interval) {
        return this.existingNotificationTiming(item, row, time_interval, true);
    }

    filterResultForBasicApproach(item, row, filteredResult, params, time_interval){
        let self = this,
        {item_recharge_number, recharge_number, item_operator, operator, item_template_id, template_id, item_type, type, item_status, status} = params;

        if(self.existingNotificationTimingForBasicApproach(item, row, time_interval) &&
            item_recharge_number == recharge_number && 
            _.toLower(item_operator) == _.toLower(operator) && 
            item_template_id == template_id && item_type == type && self.statusToBlock.includes(item_status)){
            filteredResult.push(item);
        }
    }

    filterResultForAdvancedApproach(item, row, filteredResult, params, time_interval){
        let self = this;
        try{
            let {item_recharge_number, recharge_number, item_operator, operator, item_template_id, template_id, item_type, type, item_status, status} = params;

            let [item_due_date, item_notif_type, item_timepoint] = self.extractParamFromStringifiedData(item, null, ['due_date', 'notif_type', 'timepoint']);
            let [row_due_date, row_notif_type, row_timepoint] = self.extractParamFromAdditionalData(row, null, ['due_date', 'notif_type', 'timepoint']);

            /*
            1. operator check
            2. recharge_number check
            3. template_id check
            4. type check
            5. status check
            6. due_date check -> 
                if item_due_date is less than row_due_date -> return false
                if item_due_date is greater or equal to row_due_date -> return true
                if item_due_date is null -> return true
                if row_due_date is null -> return true
            7. bill_date check
            */

            let existingNotificationTiming = self.existingNotificationTiming(item, row, time_interval);
            let operatorCheck = _.toLower(item_operator) == _.toLower(operator);
            let recharge_numberCheck = item_recharge_number == recharge_number;
            let typeCheck = item_type == type;
            let due_dateAndTimepointCheck = self.checkDueDateAndTimepoint(item_due_date, row_due_date, item_timepoint, row_timepoint, item_notif_type, row_notif_type);
            let template_idCheck = item_template_id == template_id;
            let statusCheck = self.statusToBlock.includes(item_status);

            let finalCheck = existingNotificationTiming && operatorCheck && recharge_numberCheck && typeCheck && due_dateAndTimepointCheck && template_idCheck && statusCheck;
            self.L.log("filterResultForAdvancedApproach", "existingNotificationTiming", existingNotificationTiming, "operatorCheck", operatorCheck, "recharge_numberCheck", recharge_numberCheck, "typeCheck", typeCheck, "due_dateAndTimepointCheck", due_dateAndTimepointCheck, "template_idCheck", template_idCheck, "statusCheck", statusCheck, "for recharge_number", recharge_number);
            if(finalCheck){
                filteredResult.push(item);
            }
        }catch(error){
            self.L.error('notify::filterResultForAdvancedApproach::', `error occurred during filterResultForAdvancedApproach for ${item}`, error);
            utility._sendMetricsToDD(1, [
                `FLOW:CREATE_NOTIFICATION`,
                `TYPE:ADDITIONAL_DUPLICACY_CHECK_FROM_NOTIFICATIONS_TABLE`,
            ]);
        }
    }

    /*
    due_date check -> 
            if item_due_date is less than row_due_date -> return false
            if item_due_date is greater or equal to row_due_date -> return true
            if item_due_date is null -> return false
            if row_due_date is null -> return true
    */

    checkDueDateAndTimepoint(item_due_date, row_due_date, item_timepoint, row_timepoint, item_notif_type, row_notif_type){
        let self = this;
        if(item_due_date  && row_due_date ){
            if(MOMENT(item_due_date).diff(MOMENT(row_due_date), 'days') > 0){
                return false;
            }else if(MOMENT(item_due_date).diff(MOMENT(row_due_date), 'days') == 0){
                if(item_timepoint == row_timepoint && item_notif_type == row_notif_type){
                    return true;
                }else{
                    return false;
                }
            }else{
                return true;
            }
        }
        return false;
    }

    extractParamFromDynamicParams(notifier, param, paramsArray = null){
        let self = this;
        let paramValue = null;
        if(_.get(notifier, ['data','dynamicParams'], null)){
            if(param) paramValue = _.get(notifier, ['data','dynamicParams',param], null);
            else if(paramsArray){
                paramValue = paramsArray.map(param => _.get(notifier, ['data','dynamicParams',param], null));
            }
        }else {
            if(param) paramValue = _.get(notifier, ['data', 'options', 'data', param], null);
            else if(paramsArray){
                paramValue = paramsArray.map(param => _.get(notifier, ['data', 'options', 'data', param], null));
            }
        }
        if(!paramValue){
            utility._sendMetricsToDD(1, [
                `FLOW:CREATE_NOTIFICATION`,
                `TYPE:ADDITIONAL_DUPLICACY_CHECK_FROM_NOTIFICATIONS_TABLE`,
                `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                `STATUS:${param}_NOT_FOUND_IN_DYNAMIC_PARAMS`
            ]);
        }
        return paramValue;
    }

    createNotification(callback, notifier) {
        let self = this,
            rules = _.get(notifier, 'rules', null);
            let notifyParams = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(notifier);
            let cassandraKey = `${_.get(notifier,'recharge_number','')}_${_.get(notifier,'recipient','')}_${_.get(notifier,'product_id','')}_${_.get(notifier,'source_id','')}_${_.get(notifier,'category_id','')}_${_.get(notifier,'template_id','')}_${_.get(notifier,'type','')}`;
            let loggerCassandraKey = `${_.get(notifyParams, 'service', null)=='financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(notifier,'recharge_number','') ) : _.get(notifier,'recharge_number','')}_${_.get(notifyParams, 'service', null)=='financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(notifier,'recipient','') ) : _.get(notifier,'recipient','')}_${_.get(notifyParams, 'product_id', null)}_${_.get(notifyParams, 'source_id', null)}_${_.get(notifyParams, 'category_id', null)}_${_.get(notifyParams, 'template_id', null)}_${_.get(notifyParams, 'type', null)}`;

            /**
             * Unique key for notification {notificationUniqueKey} - This will be more relaxed key for notification
             * This will block duplicate notifications for different source/category/product_id as well
             */
            let notificationUniqueKey = `${_.get(notifier,'recharge_number','')}_${_.get(notifier,'recipient','')}_${_.get(notifier,'template_id','')}`;
            let loggerNotificationUniqueKey = `${_.get(notifyParams, 'service', null)=='financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(notifier,'recharge_number','') ) : _.get(notifier,'recharge_number','')}_${_.get(notifyParams, 'service', null)=='financial services' ? self.EncryptionDecryptioinHelper.encryptData(_.get(notifier,'recipient','') ) : _.get(notifier,'recipient','')}_${_.get(notifyParams, 'template_id', null)}`;
            let notificationUniqueKeyNONRU = `NONRU_${_.get(notifier,'recharge_number','')}_${_.get(notifier,'recipient','')}_${_.get(notifier,'template_id','')}`;

            _.set(notifier, 'cassandraKey', cassandraKey);
            _.set(notifier, 'notificationUniqueKey', notificationUniqueKey);
            _.set(notifier, 'notificationUniqueKeyNONRU', notificationUniqueKeyNONRU);
            
        let errFromDB = false;
        ASYNC.waterfall(
            [
                next => {
                //     self.checkPriority((error, data) => {
                //         if (error) {
                //             errFromDB = true;
                //             next(error)
                //         } else {
                //             if (data && data.length > 0) {
                //                 next(null, false);
                //             } else {
                //                 next(null, true);
                //             }
                //         }
                // }, notifier,cassandraKey);
                return next(null, true);
            },

                (isValidNotification,next) => {
                   if(_.get(notifier,'modify_deeplink', false)){
                       self.notificationLib.createDeepLink(notifier, function(error, data){
                           if(error){
                               next(error, isValidNotification);
                           } else{
                               next(null, isValidNotification);
                           }
                       })
                   }
                   else{
                       next(null, isValidNotification);
                   }
                },
                
                (isValidNotification, next) => {
                    if (isValidNotification) {
                        if (rules && rules.condition) {
                            let checkConditionCallback = (error, data) => {
                                if (error) {
                                    errFromDB = true;
                                    next(error);
                                } else if (data && data.length > 0) {
                                    let idsWithPendingStatus = [];
                                    let idsWithSentStatus = [];
                                    let idsWithErrorStatus = [];
                                    data.forEach(row => {
                                        if (row.status === _.get(self.status_list, 'PENDING', null)) {
                                            idsWithPendingStatus.push(row.id);
                                        } else if (row.status === _.get(self.status_list, 'SENT', null) ||
                                            row.status === _.get(self.status_list, 'SUCCESS', null)) {
                                            idsWithSentStatus.push(row.id);
                                        } else if (row.status === _.get(self.status_list, 'ERROR', null))
                                            idsWithErrorStatus.push(row.id);
                                    }
                                    );
                                    let notificationAction = 'create';

                                    if (rules.actions) {
                                        for (let i = 0; i < rules.actions.length; i++) {
                                            if (rules.actions[i]['status'] === 'pending' && idsWithPendingStatus.length > 0) {
                                                notificationAction = rules.actions[i]['action'];
                                                break;
                                            } else if (rules.actions[i]['status'] === 'sent' && idsWithSentStatus.length > 0) {
                                                notificationAction = rules.actions[i]['action'];
                                                break;
                                            } else if (rules.actions[i]['status'] === 'error' && idsWithErrorStatus.length > 0) {
                                                notificationAction = rules.actions[i]['action'];
                                                break;
                                            }
                                        }
                                    }

                                    next(null, notificationAction, idsWithPendingStatus);
                                } else {
                                    next(null, 'create', null);
                                }
                            };
                            // let checkDuplicacyFromCassandra = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_DUPLICACY_ENABLE_FOR_ALL_SOURCE_IDS'], false) ||
                            //     (_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','FETCH_DUPLICACY_ENABLE_FOR_SOURCE_IDS'], []).includes(_.toString(_.get(notifier,'source_id',null))));
                            
                            // if (checkDuplicacyFromCassandra) {
                                self.L.log(`checkCondition :: checking condition through cassandra for source_id: ${_.get(notifier,'source_id',null)}`)
                                self.getDataFromCassandraForDuplicacy(notifier,function(err,data){
                                    self.L.verbose("key and data from cassandra for condition check", loggerCassandraKey, data);
                                    if (err) {
                                        self.L.error('notify::checkDuplicacy::', `error occurred during duplicacy check for ${loggerCassandraKey}`, err);
                                        self.sendDatabaseErrorMetrics('CASSANDRA', 'CREATE_NOTIFICATION');
                                        if (self.isNotificationMigratedToCassandra(_.get(notifier,'source_id',null), _.get(notifier, 'data.additional_data.customer_id', null))) {
                                            self.L.log(`checkCondition:: re-routing to cassandra main notification tables for duplicate check for key: ${loggerCassandraKey} and source_id ${_.get(notifier, 'source_id', null)}`);
                                            self.checkDuplicacyFromCassandraNotificationTable((err, data) => {
                                                if (err) {
                                                    self.L.error('notify::checkDuplicacyFromCassandraNotificationTable::', `error occurred for ${loggerCassandraKey}`, err);
                                                    self.sendDatabaseErrorMetrics('CASSANDRA', 'CREATE_NOTIFICATION');
                                                } 
                                                return checkConditionCallback(err, data);
                                            }, notifier);
                                        } else {
                                            self.L.log(`checkCondition:: re-routing to sql notification tables for duplicate check for key: ${loggerCassandraKey} and source_id ${_.get(notifier, 'source_id', null)}`);
                                            // IN-50720: no need to change in this block as notification are migrated to cassandra, also condition is always coming as string, so function will not execute.
                                            self.notification.checkConditionV2((err, data) => {
                                                if (err) {
                                                    self.L.error('notify::checkConditionV2::', `error occurred during duplicate check for ${loggerCassandraKey}`, err);
                                                    self.sendDatabaseErrorMetrics('MYSQL', 'CREATE_NOTIFICATION');
                                                }
                                                return checkConditionCallback(err, data);
                                            }, rules.condition, notifier);
                                        }
                                    } else {
                                        _.set(notifier, 'loggerCassandraKey', loggerCassandraKey);
                                        if(self.shouldNotificationBeBlocked(data, notifier)){
                                            self.logger.log(`checkCondition:: key: ${loggerCassandraKey} got value:`, data, _.get(notifyParams, _.get(notifyParams, 'service', '')));
                                            return checkConditionCallback(null, [data],rules.condition, notifier)
                                        }
                                        else return checkConditionCallback(null, null,rules.condition, notifier)
                                    }
                                })
                            // } else {
                            //     if (self.isNotificationMigratedToCassandra(_.get(notifier,'source_id',null), _.get(notifier, 'data.additional_data.customer_id', null))) {
                            //         self.L.log(`checkCondition:: checking duplicacy from cassandra notification table for key ${cassandraKey} and source_id \
                            //         ${_.get(notifier,'source_id',null)}`);
                            //         self.checkDuplicacyFromCassandraNotificationTable((err, data) => {
                            //             if (err) {
                            //                 self.L.error('notify::checkDuplicacyFromCassandraNotificationTable::', `error occurred for ${cassandraKey}`, err);
                            //                 self.sendDatabaseErrorMetrics('CASSANDRA', 'CREATE_NOTIFICATION');
                            //             }
                            //             return checkConditionCallback(err, data);
                            //         }, notifier);
                            //     } else {
                            //         self.L.log(`checkCondition :: checking condition through sql-db for source_id: ${_.get(notifier,'source_id',null)}`)
                            //         if (notifier.version == 'v2') {
                            //             self.notification.checkConditionV2((err, data) => {
                            //                 if (err) {
                            //                     self.L.error('notify::checkConditionV2::', `error occurred during duplicacy check for ${cassandraKey}`, err);
                            //                     self.sendDatabaseErrorMetrics('MYSQL', 'CREATE_NOTIFICATION');
                            //                 }
                            //                 return checkConditionCallback(err, data);
                            //             }, rules.condition, notifier);
                            //         }else{
                            //             self.notification.checkCondition((err, data) => {
                            //                 if (err) {
                            //                     self.L.error('notify::checkCondition::', `error occurred during duplicacy check for ${cassandraKey}`, err);
                            //                     self.sendDatabaseErrorMetrics('MYSQL', 'CREATE_NOTIFICATION');
                            //                 }
                            //                 return checkConditionCallback(err, data);
                            //             }, rules.condition, notifier);
                            //         }
                            //     }
                            // }                             
                        } else {
                            next(null, 'create', null);
                        }
                    } else {
                        next(null, 'drop', null);
                    }
                },

                (action, idsWithPendingStatus, next) => {
                    return self.additionalDuplicacyChecks(notifier, action, idsWithPendingStatus, next);
                },

                (action, idsWithPendingStatus, next) => {
                    action = action.toLowerCase();
                    switch (action) {
                        case 'create':
                            
                            if (notifier.next_schedules){
                                if(typeof notifier.next_schedules[0] === 'object'){
                                    notifier.next_schedules = _.uniqBy(notifier.next_schedules, 'date');
                                    if (notifier.next_schedules.length > 1) {
                                        notifier.next_schedules = notifier.next_schedules.map(schedule => ({ date:MOMENT(MOMENT(schedule.date).format('YYYY-MM-DD HH:mm:ss')) , template_details: schedule.template_details, deepLinkObj: schedule.deepLinkObj || null }));
                                        notifier.next_schedules.sort((first, second) => first.date - second.date);
                                        notifier.next_schedules = notifier.next_schedules.map(schedule => ({ date:schedule.date.format('YYYY-MM-DD HH:mm:ss') , template_details: schedule.template_details, deepLinkObj: schedule.deepLinkObj || null }));
                                    }
                                    _.has(notifier, 'data') ? 
                                        _.set(notifier.data, 'nextSchedules', notifier.next_schedules) : 
                                        _.set(notifier, 'data.nextSchedules', notifier.next_schedules);
                                }
                                else{// not removing it for backward compatibility. will remove after some time once gateway also went live
                                    // Removing dupicate dates from array
                                    notifier.next_schedules = [...new Set(notifier.next_schedules)];
                                    if (notifier.next_schedules.length > 1) {
                                        notifier.next_schedules = notifier.next_schedules.map((schedule) => MOMENT(MOMENT(schedule).format('YYYY-MM-DD')));
                                        notifier.next_schedules.sort((first, second) => first - second);
                                        notifier.next_schedules = notifier.next_schedules.map((schedule) => schedule.format('YYYY-MM-DD'));
                                    }
                                    _.has(notifier, 'data') ? 
                                        _.set(notifier.data, 'nextSchedules', notifier.next_schedules) : 
                                        _.set(notifier, 'data.nextSchedules', notifier.next_schedules);
                                }

                            }


                            if (self.isNotificationMigratedToCassandra(_.get(notifier, 'source_id', null), _.get(notifier, 'data.additional_data.customer_id', null))) {
                                self.createNotificationInCassandra((err) => {
                                    if (err) {
                                        errFromDB = true;
                                        self.sendDatabaseErrorMetrics('CASSANDRA', 'CREATE_NOTIFICATION');
                                    } else {
                                        utility._sendMetricsToDD(1, [
                                            `FLOW:CREATE_NOTIFICATION`,
                                            `DATABASE:CASSANDRA`,
                                            `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                                            `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                                            `STATUS:CREATED`,
                                            `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                        ]);
                                    }
                                    next(err, 'action: create', true, null);
                                }, notifier);
                            } else {
                                let DBStartTime =  new Date().getTime();
                                self.notification.createNotification((error, data, status) => {
                                    utility._sendLatencyToDD(DBStartTime, ['REQUEST_TYPE:DB_QUERY', 'TYPE:CREATE_NOTIFICATION']);
                                    let DBEndTime   =  new Date().getTime();
                                    let dbDuration = DBEndTime - DBStartTime; 
                                    self.L.log(`Database v1/notify latency - Querytook - ${dbDuration} milliseconds`);
                                    if (error) {
                                        errFromDB = true;
                                        self.sendDatabaseErrorMetrics('MYSQL', 'CREATE_NOTIFICATION');
                                    } else {
                                        _.set(notifier, 'status', status);
                                        utility._sendMetricsToDD(1, [
                                            `FLOW:CREATE_NOTIFICATION`,
                                            `DATABASE:MYSQL`,
                                            `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                                            `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                                            `STATUS:CREATED`,
                                            `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                        ]);   
                                    }
                                    next(error, 'action: create', true, _.get(data, 'insertId'));  
                                }, notifier);
                            }
                            break;
                        case 'drop':
                            utility.sendNotificationMetricsFromSend(notifier ,"DROPPED","DROPPED_DURING_CREATE")
                            self.logger.log('Notify Drop notification for ', notifier, _.get(notifyParams, 'service', ''));
                            self.insertRejectedNotifications(function(err,data){
                                next(null, 'action: drop', false, null);
                            }, 'Found db entry recently for same notification', notifier, null);
                            break;
                        default:
                            utility.sendNotificationMetricsFromSend(notifier ,"ERROR","ERROR_DURING_CREATE")
                            next(new Error('Invalid action for notification'));
                    }
                },
                (action, kafkaPublish, dbInsertId, next)=>{
                    // let useCassandraToUpsert= _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','CREATE_CACHE_ENABLE_FOR_ALL_SOURCE_IDS'], false) ||
                    // (_.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_CONFIG','NOTIFICATION_CASSANDRA','CREATE_CACHE_ENABLE_FOR_SOURCE_IDS'], []).includes(_.toString(_.get(notifier,'source_id',null))));
                    if(kafkaPublish==true){
                        let status = self.notification.getStatusForNotificationCreation(notifier);
                        let cassandraValue = {
                            send_at: _.get(notifier,'send_at',''),
                            priority: _.get(notifier,'priority',''),
                            status: status
                        }
                        self.setDataInCassandraForDuplicacy(notifier, cassandraValue,function(err,data){
                            self.L.verbose("setDataInCassandra :: key and data for cassandra err and data::", err, data);
                            //encrypt keys for ccbp as they will be encrypted in db as well
                            if(err){
                                self.L.error(`setDataInCassandra ::createNotification data could not be updated on cassandra with err ${err} for casskey ${_.get(notifyParams, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(cassandraKey) : cassandraKey} notificationUniqueKey ${_.get(notifyParams, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(notificationUniqueKey) : notificationUniqueKey}`)
                            }else{
                                self.L.log(`setDataInCassandra ::createNotification Data updated on cassandra succesfully for casskey ${_.get(notifyParams, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(cassandraKey) : cassandraKey} notificationUniqueKey ${_.get(notifyParams, 'service', null) == 'financial services' ? self.EncryptionDecryptioinHelper.encryptData(notificationUniqueKey) : notificationUniqueKey}`);
                            }
                        });
                       
                        next(null, action, kafkaPublish, dbInsertId);

                    }else{
                        next(null, action, kafkaPublish, dbInsertId);
                    }
                },
            (action, kafkaPublish, dbInsertId, next) => {
                /**
                 * This is another notification table, which will be getting used in future for the notification duplicacy check
                 * In this table, we have more relaxed primary key, for now we will be saving data only no duplicacy checks are applicable for now
                 */
                if(self.trafficEnabled(notifier) && kafkaPublish ){
                    self.setParamsInNotifier(notifier);
                    let data = self.prepareDataForCassandraNotificationTable(notifier);
                    let tableName = self.cassandraBills.getTableNameForNotification(notifier);
                    return self.cassandraBills.createEntryInNotificationTable(action, kafkaPublish, dbInsertId, notifier, data, tableName, next);
                }else{
                    return next(null, action, kafkaPublish, dbInsertId);
                }
            },
            (action, kafkaPublish, dbInsertId, next) => {                    
                let kafkaStartTime = new Date().getTime();
                if (kafkaPublish == true) {
                    let preparedKafkaPayload = self.prepareKafkaPayload(notifier, dbInsertId);
                    if(!preparedKafkaPayload.topic){
                        return next(null, action);
                    }
                    _.set(preparedKafkaPayload, ['payload', 'data','timestamps', 'notify_onBoardTime'], new Date().getTime());   
                    // self.L.log("Kafka message going to be published:: ", JSON.stringify(preparedKafkaPayload));                         
                    async function publisherCallbackFunction(error) {
                        let kafkaEndTime = new Date().getTime();
                        let kafkaDuration = kafkaStartTime - kafkaEndTime;
                        self.L.log(`kafka v1/notify latency - publish - ${kafkaDuration} milliseconds`);
                        if (error) {
                            await self.insertRejectedNotificationsViaPromise(self.billsLib.createErrorMessage(error), notifier, preparedKafkaPayload);
                        } else {
                            self.logger.log(`Message published successfully in Kafka  on topic ${preparedKafkaPayload.topic}`, preparedKafkaPayload, _.get(notifyParams, 'service', ''));
                            self.L.log('createNotification::', `STATUS:PUBLISHED_TOPIC:${preparedKafkaPayload.topic}_SOURCE_ID:${_.get(preparedKafkaPayload, 'payload.source_id', 0)}_CATEGORY_ID:${_.get(preparedKafkaPayload, 'payload.category_id', 0)}_TYPE:${_.get(preparedKafkaPayload, 'payload.type', 'NOT_PASSED')}`);
                            utility._sendMetricsToDD(1, [
                                'STATUS:PUBLISHED',
                                'TOPIC:' + preparedKafkaPayload.topic,
                                'SOURCE_ID:' + _.get(preparedKafkaPayload, 'payload.source_id', 0),
                                'CATEGORY_ID:' + _.get(preparedKafkaPayload, 'payload.category_id', 0),
                                'TYPE:' + _.get(preparedKafkaPayload, 'payload.type', 'NOT_PASSED'),
                                `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                            ])
                        }  
                        return next(null, action);
                    }
                    var publisherObject = [{
                            topic: preparedKafkaPayload.topic,
                            messages: JSON.stringify(preparedKafkaPayload.payload),
                            key: _.get(preparedKafkaPayload, 'payload.data.customer_id', '')
                        }];
                    
                    if( _.get(self.notificationConfig, 'nonRurealTimeSendNotificationTopic') && preparedKafkaPayload.topic == _.get(self.notificationConfig, 'nonRurealTimeSendNotificationTopic') ){
                        self.L.log("createNotification:: kafkaNotificationServicePublisherAuxiliary publish data");
                        self.reminderAuxKafka.publishData(publisherObject, publisherCallbackFunction , [200, 800]);
                    }
                    else if(preparedKafkaPayload.topic.includes('NOTIFICATION_') || preparedKafkaPayload.topic.includes('RT_NOTIFICATION') || preparedKafkaPayload.topic.includes('HEURISTIC_NOTIFICATION') || preparedKafkaPayload.topic.includes('WA_NOTIFICATION')) {
                        self.reminderKafkaNotificationServicePublisher.publishData(publisherObject, publisherCallbackFunction, [200, 800]);
                    }
                    else {
                        self.kafkaNotificationServicePublisher.publishData(publisherObject, publisherCallbackFunction , [200, 800]);
                    }
                } else {
                    return next(null, action);
                }
            }
            ],
            async (err, data) => {
                if (err && errFromDB) {
                    self.L.error('notify::createNotification::', `err from cassandra or mysql`, err);
                    await self.publishNotificationDropsDueToDatabaseError(notifier, 'CREATE_NOTIFICATION');
                }
                //Lets invoke the main callback to end the cycle
                callback(err, data);   
            }
        );
    }

    extractParamFromAdditionalData(notifier, param, paramsArray = null){
        let self = this;
        let paramValue = null;
        try{
            let data = _.get(notifier, 'data', null);
            if(typeof data == 'string'){
                data = JSON.parse(data);
            }
            let additionalData = _.get(data, 'additional_data', null);
            if(paramsArray){
                paramValue = paramsArray.map(param => _.get(additionalData, param, null));
            }else{
                paramValue = _.get(additionalData, param, null);
            }
            return paramValue;
        }catch(e){
            self.L.error('extractParamFromAdditionalData::', `error while extracting param from additional data`, e);
            utility._sendMetricsToDD(1, [
                `FLOW:CREATE_NOTIFICATION`,
                `TYPE:ADDITIONAL_DUPLICACY_CHECK_FROM_NOTIFICATIONS_TABLE`,
                `SOURCE_ID:${_.get(notifier, 'source_id', 'UNKNOWN')}`,
                `CATEGORY_ID:${_.get(notifier, 'category_id', 'UNKNOWN')}`,
                `STATUS:${param}_NOT_FOUND_IN_ADDITIONAL_PARAMS`
            ]);
            return null;
        }
    }

    prepareDataForCassandraNotificationTable(notifier){
        let self = this;
        let operator = self.extractParamFromDynamicParams(notifier, 'operator');
        let [timepoint, due_date, notif_type, bill_date, bill_fetch_date] = self.extractParamFromAdditionalData(notifier, null, ['timepoint', 'due_date', 'notif_type', 'bill_date', 'bill_fetch_date']);
        let data = {
            operator: operator,
            timepoint: timepoint,
            due_date: due_date,
            notif_type: notif_type,
            bill_date: bill_date,
            bill_fetch_date: bill_fetch_date
        }
        console.log("data", data);
        return JSON.stringify(data);
    }

    trafficEnabled(notifier){
        let self = this;
        let source = _.toString(_.get(notifier, 'source_id', null));
        let enabledSources = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_LOGS','MERGED_NOTIFICATION_TABLE','ENABLED_SOURCES'], []);
        self.L.verbose(`trafficEnabled:: enabledSources ${enabledSources} and current source is ${source}`);
        if(enabledSources.includes('ALL')) return true; //enabled if no config exists 

        let customerId = null;
        if(_.get(notifier, ['data', 'dynamicParams'],null)){
            customerId= _.get(notifier, ['data', 'dynamicParams', 'customer_id'], null);
        }else{
            customerId= _.get(notifier, ['data', 'options', 'data','customer_id'], null);
        }
        

        if(enabledSources.includes(source)){
            let percentageTrafficForSource = _.get(self.config, ['DYNAMIC_CONFIG','NOTIFICATION_LOGS','PERCENTAGE_TRAFFIC_LIVE_SOURCE_WISE',source], 100);
            let threshold = 100 / percentageTrafficForSource;
            return customerId % threshold === 0;
        }
    }
    //{send_at, sent_at, status, priority}
    shouldNotificationBeBlocked(data, notifier, service=null){
        let self = this;
        let loggerCassandraKey = _.get(notifier, 'cassandraKey', null);
        if(_.get(notifier, 'loggerCassandraKey', null)) loggerCassandraKey = _.get(notifier, 'loggerCassandraKey', null);
        try{
            let dateToBeUsed = _.get(data, 'send_at', null);
            let currrentSendAt = _.get(notifier, 'send_at', null);
            if(_.get(data, 'sent_at', null) && MOMENT(_.get(data, 'sent_at', null)).isValid()){
                dateToBeUsed = _.get(data, 'sent_at', null);
            }
            let statusToBlock = service=='notify'? [_.get(self.status_list, 'SENT', null)] : [_.get(self.status_list, 'PENDING', null),_.get(self.status_list, 'SENT', null)]
            let last_ref_time = _.get(notifier, 'last_ref_time', null);

            if(!last_ref_time){
                let time_interval= _.get(notifier, 'time_interval', 1200);
                last_ref_time = MOMENT().subtract(time_interval, 'minutes').format('YYYY-MM-DD HH:mm:ss');
            }

            self.L.log(`shouldNotificationBeBlocked :: data- ${JSON.stringify(data)}, dateToBeUsed- ${dateToBeUsed}, currrentSendAt- ${currrentSendAt}, statusToBlock- ${statusToBlock}, last_ref_time- ${last_ref_time} for key: ${_.get(notifier, 'loggerCassandraKey', null)}`);
            if(statusToBlock.includes(_.get(data, 'status', null))){
                if(data && dateToBeUsed && 
                    MOMENT(currrentSendAt, 'YYYY-MM-DD HH:mm:ss').isSameOrBefore(MOMENT(dateToBeUsed, 'YYYY-MM-DD HH:mm:ss'), 'day')  
                    && MOMENT(dateToBeUsed).utc() > MOMENT(last_ref_time).utc()){
                    return true;
                }else{
                    return false;
                }
            }else{
                return false;
            }
        }catch(e){
            self.L.error('shouldNotificationBeBlocked::', 'error while checking shouldNotificationBeBlocked', e);
            return false;
        }
    }

    setParamsInNotifier(notifier){
        let self = this;
        try{
            if(_.get(notifier, ['data', 'dynamicParams'],null)){
                _.set(notifier, 'customer_id', _.get(notifier, ['data', 'dynamicParams', 'customer_id'], null));
                _.set(notifier, 'service', _.toLower(_.get(notifier, ['data', 'dynamicParams', 'service'], null)));
                _.set(notifier, 'paytype', _.get(notifier, ['data', 'dynamicParams', 'paytype'], null));
            }else{
                _.set(notifier, 'customer_id', _.get(notifier, ['data', 'options', 'data','customer_id'], null));
                _.set(notifier, 'service', _.toLower(_.get(notifier, ['data', 'options', 'data','service'], null)));
                _.set(notifier, 'paytype', _.toLower(_.get(notifier, ['data', 'options', 'data','paytype'], null)));
            }
            _.set(notifier, 'app_created_at', MOMENT().valueOf());
            _.set(notifier, 'user_type', 'RU');
            _.set(notifier, 'entryCreatedInNotificationLog', true);
        }catch(e){
            self.L.error('Error while setting params in notifier', e);
        }
    }

    async publishNotificationDropsDueToDatabaseError(record, notificationDropSource) {
        let self = this;
        let payload = _.cloneDeep(record);
        _.set(payload, 'notificationDropSource', notificationDropSource);
        let publisherObject = [{
            topic: _.get(self.config, ['KAFKA', 'SERVICES', 'NOTIFICATIONS_DLQ', 'TOPIC'], null),
            messages: JSON.stringify(payload),
            key: _.get(record, 'recharge_number', null)
        }]

        return new Promise((resolve, reject) => {
            self.rejectNotificationkafkaPublisher.publishData(publisherObject, function(err) {
                if (err) {
                    self.L.error('notify::publishNotificationDropsDueToDatabaseError::', `err publishing data in kafka topic ${publisherObject[0].topic} for payload ${publisherObject[0].messages}`, err);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:NOTIFICATIONS_DLQ_KAFKA_PUBLISHER`,
                        `STATUS:ERROR`,
                        `FLOW:${notificationDropSource}`,
                        `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                    ]);
                } else {
                    self.L.log('notify::publishNotificationDropsDueToDatabaseError::', `msg published successfully in kafka topic ${publisherObject[0].topic} for payload ${publisherObject[0].messages}`);
                    utility._sendMetricsToDD(1, [
                        `STATUS:PUBLISHED`,
                        `TOPIC:${_.get(self.config, ['KAFKA', 'SERVICES', 'NOTIFICATIONS_DLQ', 'TOPIC'], 'UNKNOWN')}`,
                        `FLOW:${notificationDropSource}`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`,
                        `CATEGORY_ID:${_.get(record, 'category_id', 'UNKNOWN')}`,
                        `TYPE:${_.get(record, 'type', 'UNKNOWN')}`,
                        `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                    ]);
                }
                resolve();
            }, [200, 800]); 
        });
    }

    createParamsForNotificationRejection(error, record, notificationRecord) {
        let self = this;
        let date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        let raw_expiry_date = _.get(record, 'due_date', null) || _.get(record, 'validity_expiry_date', null) || _.get(record, ['data','additional_data','due_date'], null);
        let timepoint = _.get(record, 'timepoint', _.get(record, ['data','additional_data','timepoint'], null));
        let notif_type = _.get(record, 'notificationType', null) || _.get(record, ['data','additional_data','notif_type'], null);
        let recon_id = _.get(record, 'recon_id', null) || _.get(record, ['data','additional_data','recon_id'], null);
        let billSource = 'RU';
        if(self.notificationBillSource == 'NONRU') {
            billSource = 'NONRU';
        }
        if(MOMENT(raw_expiry_date).isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date).format('YYYY-MM-DD HH:mm:ss');
        }else if(MOMENT(raw_expiry_date, 'Do MMM YYYY', true).isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date, 'Do MMM YYYY', true).format('YYYY-MM-DD HH:mm:ss');
        }

        if(_.get(record, 'requestType', null) && _.get(record, 'requestType', null).includes('PLAN_VALIDITY_NOTIFICATION')){
            timepoint = -_.get(record, 'dayValue',null);
            notif_type = 'DUEDATE';
        }

        let isTimepointAbsent = (timepoint===null || timepoint===undefined || timepoint==='');

        if(_.get(record, 'dataFrom',null)=='billDueCron' || _.get(record, 'dataFrom',null)=='pvCron' || _.get(record, 'dataFrom',null)=='billGenCron' || isTimepointAbsent){
            if(_.get(record, 'dataFrom',null)=='billGenCron' || notif_type=='BILLGEN'){
                notif_type = 'BILLGEN';
                let bFdate = MOMENT(_.get(record, 'bill_fetch_date', null)).isValid()? MOMENT(_.get(record, 'bill_fetch_date', null)).format('YYYY-MM-DD') :null;
                let currDate = MOMENT().format('YYYY-MM-DD');
                if(bFdate) timepoint = MOMENT(currDate).diff(MOMENT(bFdate), 'days');
            }else{
                notif_type = 'DUEDATE';
                let dDate = MOMENT(_.get(record, 'due_date', _.get(record, 'validity_expiry_date', null))).isValid()? MOMENT(_.get(record, 'due_date', _.get(record, 'validity_expiry_date', null))).format('YYYY-MM-DD') :null;
                let currDate = MOMENT().format('YYYY-MM-DD');
                console.log('dueDate and currDate', dDate, currDate);
                if(dDate) timepoint = MOMENT(dDate).diff(MOMENT(currDate), 'days');
            }
            try{
                let extra = _.get(record, 'extra', null);
                if(typeof extra == 'string'){
                    extra = JSON.parse(extra);
                }
                if(_.get(extra, 'recon_id', null)){
                    recon_id = _.get(extra, 'recon_id', null);
                }else{
                    recon_id = utility.generateReconID(_.get(record, 'recharge_number', null), _.get(record, 'operator', null), _.get(record, 'amount', null), _.get(record, 'due_date', null) || _.get(record, 'validity_expiry_date'), _.get(record, 'bill_date', null));
                }
            }catch(e){
                self.L.error('Error while parsing extra for recon_id', e);
                recon_id = utility.generateReconID(_.get(record, 'recharge_number', null), _.get(record, 'operator', null), _.get(record, 'amount', null), _.get(record, 'due_date', null) || _.get(record, 'validity_expiry_date'), _.get(record, 'bill_date', null));
            }
        }


        /** ccbp encryption changes needed here 
         * NO CHANGES needed since this record is not getting inserted in calling function 
         * removing 0 indexed value from the array*/
        return [
        {
            recharge_number: _.get(record, 'recharge_number', null),
            customer_id: _.toNumber(_.get(record, 'customer_id', null) || _.get(record, ['data','additional_data','customer_id'], null)),
            source_id: _.get(record, 'source_id', null),
            category_id: _.get(record, 'category_id', null),
            product_id: _.get(record, 'product_id', null),
            recon_id: recon_id,
            type: _.get(notificationRecord, 'type', null) || _.get(record, 'type', null) || 'UNKNOWN',
            template_id: _.get(notificationRecord, 'template_id', null) || _.get(record, 'template_id', null),
            recipient: _.get(record, 'recipient', null) || _.get(notificationRecord, 'recipient', null),
            data: _.get(record, 'data', null),
            priority: _.get(record, 'priority', null),
            error_msg: error,
            status: _.get(self.config,['NOTIFICATION', 'status', 'CANCELED'], 2),
            notif_type: notif_type,
            operator: _.get(record, 'operator', null) || _.get(record, ['data','additional_data','operator'], null),
            amount: _.get(record, 'amount', null) || _.get(record, ['data','additional_data','amount'], null),
            service: _.get(record, 'service', null) || _.get(record, ['data','additional_data','service'], null),
            raw_expiry_date: raw_expiry_date,
            bill_source: _.get(record, 'bill_source', null) || _.get(record, ['data','additional_data','bill_source'], null) || billSource,
            promocode: _.get(record, 'promocode', null) || _.get(record, ['data','additional_data','promocode'], null),
            msg_type: _.get(record, 'msg_type', null) || _.get(record, ['data','additional_data','msg_type'], null),
            timepoint: _.toString(timepoint),
            template_name: _.get(record, 'template_name', null) || _.get(record, ['data','additional_data','templateName'], null),
            created_at: date,
            updated_at: date,
            job_id:null,
            send_at:null,
            sent_at:null,
            retry_interval:30,
            max_retry_count: 3,
            retry_count: 0,
            message_id: _.get(record, 'message_id', null)
        }
    ];
    }

    async insertRejectedNotifications(cb, error, record, notificationRecord) {
        let self = this;
        if(_.get(record, 'value', null) && _.get(record, 'partition',null)){ //direct kafka payload
            try{
                if(typeof record.value == 'string')
                record = JSON.parse(record.value);
            }catch(e){
                self.L.error('Error while parsing kafka payload', e);
                return cb();
            }
        }
        let [kafkaPayload] = self.createParamsForNotificationRejection(error, record, notificationRecord);
        ASYNC.parallel([
            function (callback) {
                self.rejectNotificationkafkaPublisher.publishData([
                    { topic: _.get(self.config, ['KAFKA', 'SERVICES', 'NOTIFICATION_REJECTS', 'TOPIC'], null), 
                    messages: JSON.stringify(kafkaPayload), 
                    key: _.get(record, 'recharge_number', null) }], function (error) {
                    if (error) {
                        utility.sendNotificationMetricsFromSend(record, "ERROR","PUBLISHING_TO_NOTIFICATION_REJECT_KAFKA")
                        self.logger.error(`Error while publishing message in Kafka rejectNotificationkafkaPublisher ${error} - MSG:- `, kafkaPayload, _.get(kafkaPayload, 'service', ''));
                    } else {
                        utility.sendNotificationMetricsFromSend(record, "SUCCESS","PUBLISHING_TO_NOTIFICATION_REJECT_KAFKA")
                        self.logger.log(`Message published successfully in Kafka  on topic ${_.get(self.config, ['KAFKA', 'SERVICES', 'NOTIFICATION_REJECTS', 'TOPIC'], null)}`, kafkaPayload, _.get(kafkaPayload, 'service', ''));
                    }
                    callback(error);
                }, [200, 800]);
            },
            // function (callback) {
            //     self.cassandraBills.insertRejectedNotifications(function(err){
            //         if(err){
            //             utility.sendNotificationMetricsFromSend(record, "ERROR","INSERTING_INTO_CASSANDRA_REJECTED_NOTIS")
            //         }else{
            //             utility.sendNotificationMetricsFromSend(record, "SUCCESS","INSERTING_INTO_CASSANDRA_REJECTED_NOTIS")
            //         }
            //         callback(err);
            //     }, paramsForDb);
            // },
        ]
        , function (err, results) {
            return cb();
        })
    }

    getTopicToPublish(category, source, type, template_id, cohortDetails,notificationType, timepoint) {
        let self = this;
        let topicToPublish;
        if(self.notificationBillSource == 'NONRU'){
            topicToPublish = _.get(self.notificationConfig,['nonRuCategoryTopic', `${category}_${source}`], _.get(self.notificationConfig,['nonRuChannelWiseTopic', type], null));
        } else {
            topicToPublish = _.get(self.notificationConfig, ['categoryTopic', `${category}_${source}`], null) ||
                _.get(self.notificationConfig, ['categoryTopic', category], null);
        }
        if (type == 'WHATSAPP') {
            if(self.notificationBillSource == 'NONRU')
                topicToPublish = topicToPublish == 'NONRU_NOTIFICATION_REALTIME' ? 'NONRU_NOTIFICATION_WHATSAPP_REALTIME' : 'NONRU_NOTIFICATION_WHATSAPP';
            else 
                topicToPublish = topicToPublish == 'RT_NOTIFICATION' ? 'WA_NOTIFICATION_RT' : 'WA_NOTIFICATION';
            return topicToPublish;
        }

        if((self.notificationBillSource !== 'NONRU')  && topicToPublish!='RT_NOTIFICATION'){
            let topicToPublishByType = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_TIME_MAPPING', 'TYPE', type], null);
            if(topicToPublishByType) topicToPublish = topicToPublishByType;
            else
            {
                let notification_time = _.get(self.config, ['DYNAMIC_CONFIG', 'TEMPLATE_TIME_MAPPING', 'COMMON', template_id], 0);
                if(notification_time ) topicToPublish = 'NOTIFICATION_' + notification_time;
            }
        }
        if(type == 'PUSH' && topicToPublish!='RT_NOTIFICATION' && cohortDetails && cohortDetails.templateTime ){
            let notification_time = cohortDetails.templateTime
            if(notification_time ) topicToPublish = 'NOTIFICATION_' + notification_time;
        }
        return topicToPublish;
    }

    prepareKafkaPayload(notifier, dbInsertId) {
        let
            self = this,
            topicToPublish = null,
            category = _.get(notifier, 'category_id', null),
            source = _.get(notifier, 'source_id', null),
            notificationType = _.get(notifier, 'data.additional_data.notif_type', null),
            timepoint = _.get(notifier, 'data.additional_data.timepoint', null),
            type = _.get(notifier, 'type', null),
            template_id = _.get(notifier, 'template_id', null),
            cohortDetails = _.get(notifier, 'cohortDetails', null);

        topicToPublish = self.getTopicToPublish(category, source, type, template_id, cohortDetails,notificationType, timepoint);
        if (category == 13 && source == 13) {
            try {
                let start_date = _.get(notifier, 'data.options.data.start_date', null);
                if (start_date == null) {
                    start_date = _.get(notifier, 'data.dynamicParams.start_date', null);
                }
                let notif_date = new Date(start_date);
                let startHour = notif_date.getUTCHours();

                let minutes = notif_date.getUTCMinutes();
                if (minutes > 0) {
                    startHour += 1;
                }
                if (startHour >= 7 && startHour <= 21) {
                    topicToPublish = `NOTIFICATION_${startHour}`;

                }
                else {
                    topicToPublish = null;

                }

            }
            catch (error) {
                self.L.error('Error while fetching topic to publish', error);
            }
        }
        let param = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(notifier);
        let is_encrypted = 0;
        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(param.service, param.paytype, param.customerId)) {
            is_encrypted = 1;
        }
        return {
            topic: topicToPublish,
            payload: {
                id: dbInsertId && _.isNumber(dbInsertId) ? dbInsertId : null,
                recharge_number: _.get(notifier, 'recharge_number', null),
                type: _.get(notifier, 'type', null),
                product_id: _.get(notifier, 'product_id', null),
                source_id: _.get(notifier, 'source_id', null),
                category_id: _.get(notifier, 'category_id', null),
                machineId: OS.hostname(),
                correlationId: _.get(notifier, 'correlationId', null),
                data: {
                    correlationId: _.get(notifier, 'correlationId', null),
                    id: dbInsertId && _.isNumber(dbInsertId) ? dbInsertId : null,
                    type: _.get(notifier, 'type', null),
                    template_id: _.get(notifier, 'template_id', null),
                    recipient: _.get(notifier, 'recipient', null),
                    source_id: _.get(notifier, 'source_id', null),
                    category_id: _.get(notifier, 'category_id', null),
                    recharge_number: _.get(notifier, 'recharge_number', null),
                    product_id: _.get(notifier, 'product_id', null),
                    data: _.get(notifier, 'data', null),
                    max_retry_count: _.get(notifier, 'max_retry_count', null),
                    retry_count: _.get(notifier, 'retry_count', 0),
                    retry_interval: _.get(notifier, 'retry_interval', null),
                    priority: _.get(notifier, 'priority', null),
                    send_at: _.get(notifier, 'send_at', null),
                    status: _.get(notifier, 'status', null),
                    timestamps: _.get(notifier, 'timestamps',{}),
                    cassandraKey: _.get(notifier, 'cassandraKey',''),
                    customer_id: _.get(notifier, 'customer_id', null),
                    notificationType:_.get(notifier, 'notificationType',null),
                    recon_id: _.get(notifier, 'recon_id', null),
                    dayValue: _.get(notifier, 'dayValue', null),
                    app_created_at: _.get(notifier, 'app_created_at', null),
                    user_type: _.get(notifier, 'user_type', null),
                    service: _.get(notifier, 'service', null),
                    remindLaterDate: _.get(notifier, 'remind_later_date', null),
                    entry_created_in_notification_log: _.get(notifier, 'entryCreatedInNotificationLog', null),
                    partialBillState: _.get(notifier, 'partialBillState', null),
                    dataExhaustExtra: _.get(notifier, 'dataExhaustExtra', null),
                    notificationUniqueKey: _.get(notifier, 'notificationUniqueKey', null),
                    notificationUniqueKeyNONRU: _.get(notifier, 'notificationUniqueKeyNONRU', null),
                    is_encrypted: is_encrypted,
                    previous_job_id: _.get(notifier, 'previous_job_id', null)
                },
                partialBillState: _.get(notifier, 'partialBillState', null),
                dataExhaustExtra: _.get(notifier, 'dataExhaustExtra', null) 
            }
        }
    }

    getRetryNotificationStatusList () {
        const self = this;
        return [
            _.get(self.status_list, 'RETRY', 3),
            _.get(self.status_list, 'RESCHEDULE', 7),
            _.get(self.status_list, 'RETRY_FALLBACK', 9)
        ];
    }

    createNotificationInCassandra(cb, notifier) {
        let self = this;
        let date = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        let isRU= self.notificationBillSource !== 'NONRU' ? true : false;
        let meta = {
            isRU: isRU
        };
        let insertNotifier = _.cloneDeep(notifier);
        _.set(insertNotifier, 'created_at', date);
        _.set(insertNotifier, 'updated_at', date);
        _.set(insertNotifier, 'meta', JSON.stringify(meta));
        ASYNC.waterfall(
            [
                (callback) => {
                    let DBStartTime = new Date().getTime();
                    self.cassandraBills.createNotification((error, status) => {
                        utility._sendLatencyToDD(DBStartTime, ['REQUEST_TYPE:CASSANDRA_QUERY', 'TYPE:CREATE_NOTIFICATION']);
                        let DBEndTime = new Date().getTime();
                        let dbDuration = DBEndTime - DBStartTime;
                        self.L.log(`Cassandra db create notification latency - Querytook - ${dbDuration} milliseconds`);
                        _.set(notifier, 'status', status);
                        _.set(insertNotifier, 'status', status);
                        callback(error);
                    }, insertNotifier);
                },
                (callback) => {
                    self.publishRecordInKafkaForDwhSync(callback, insertNotifier);
                }
            ], 
            (err) => {
                if (err) {
                    self.L.error('createNotificationInCassandra::', `error occurred for payload ${JSON.stringify(notifier)}`, err);
                }
                cb(err);
            }
        )
    }

    updateNotificationInCassandra(cb, fields, whereCondition, params, record) {
        let self = this;
        ASYNC.parallel(
            [
                (callback) => {
                    let DBStartTime = new Date().getTime();
                    self.cassandraBills.updateNotification((err, result) => {
                        let DBEndTime = new Date().getTime();
                        let dbDuration = DBEndTime - DBStartTime;
                        self.L.log('updateNotificationInCassandra::', `query took ${dbDuration} ms`);
                        if (!err && result) {
                            let isRecordUpdated = _.get(result.rows[0], '[applied]', false);
                            if (isRecordUpdated) {
                                self.L.log('updateNotificationInCassandra::', `record successfully updated for debugKey ${record.record_key}`);
                                utility._sendMetricsToDD(1, [
                                    `REQUEST_TYPE:UPDATE_NOTIFICATION_IN_CASSANDRA_DB`,
                                    `STATUS:RECORD_UPDATED`,
                                    `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`,
                                    `CATEGORY_ID:${_.get(record, 'category_id', 'UNKNOWN')}`,
                                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                ]);
                            } else {
                                self.L.critical('updateNotificationInCassandra::', `update failed as no such entry exists for debugKey ${record.record_key}`);
                                utility._sendMetricsToDD(1, [
                                    `REQUEST_TYPE:UPDATE_NOTIFICATION_IN_CASSANDRA_DB`,
                                    `STATUS:RECORD_NOT_FOUND`,
                                    `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`,
                                    `CATEGORY_ID:${_.get(record, 'category_id', 'UNKNOWN')}`,
                                    `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                                ]);
                            }
                        } else if (err) {
                            self.L.error('notify::updateNotificationInCassandra::', `error occurred while updating record in cassandra for ${record.record_key}`, err);
                            self.sendDatabaseErrorMetrics('CASSANDRA', 'UPDATE_NOTIFICATION');
                        }
                        callback(err);
                    }, fields, whereCondition, params, _.get(record, 'send_at', null));
                },
                (callback) => {
                    let publishRecord = _.cloneDeep(record);
                    let data = _.get(publishRecord, 'data', null);
                    let paramsWithEmojiFields = _.get(data, 'dynamicParams', null) ? _.get(data, 'dynamicParams', null) : _.get(data, 'options.data', null);
                    if(paramsWithEmojiFields){
                        Object.keys(paramsWithEmojiFields).forEach(key => {
                            if (key.includes("emoji")) {
                                paramsWithEmojiFields[key] = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES', key], null);
                            }
                        });
                    }
                    self.publishRecordInKafkaForDwhSync(callback, publishRecord);
                }
            ],
            (err) => {
                cb(err);
            }
        )
    }

    publishRecordInKafkaForDwhSync(cb, record) {
        let self = this;
        let kafkaPayload = self.prepareKafkaPayloadForDwhSync(record);
        if(kafkaPayload){
            let publisherObject = [{
                topic: _.get(self.config, ['KAFKA', 'SERVICES', 'CASSANDRA_NOTIFICATION_DWH', 'TOPIC'], null),
                messages: JSON.stringify(kafkaPayload),
                key: _.get(record, 'recharge_number', null)
            }];
            let service = _.toLower(_.get(kafkaPayload, 'data.additional_data.service', null));
            self.rejectNotificationkafkaPublisher.publishData(publisherObject, (error) => {
                if (error) {
                    self.logger.error(`notify::publishRecordInKafkaForDwhSync:: error while publishing message in kafka topic ${publisherObject[0].topic}, error ${error}`, kafkaPayload, service);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:CASSANDRA_NOTIFICATION_DWH_SYNC_PUBLISHER`,
                        `STATUS:ERROR`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`,
                        `CATEGORY_ID:${_.get(record, 'category_id', 'UNKNOWN')}`,
                    	 `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                ]);
                } else {
                    self.logger.log(`notify::publishRecordInKafkaForDwhSync:: msg published successfully in kafka topic ${publisherObject[0].topic} payload`, kafkaPayload, service);
                    utility._sendMetricsToDD(1, [
                        `REQUEST_TYPE:CASSANDRA_NOTIFICATION_DWH_SYNC_PUBLISHER`,
                        `STATUS:PUBLISHED`,
                        `SOURCE_ID:${_.get(record, 'source_id', 'UNKNOWN')}`,
                        `CATEGORY_ID:${_.get(record, 'category_id', 'UNKNOWN')}`,
                        `BILL_SOURCE_TYPE:${self.notificationBillSource}`
                ]);
                }
                cb(error);
            }, [200, 800]);
        }else{
            self.L.error('notify::publishRecordInKafkaForDwhSync::', `error while preparing kafka payload for DWH sync`);
            cb();
        }
    }

    convertToSpecificDatatype(value, type) {
        let self=this;
        try{
            if(value == null || value == undefined) return null;
            if(type == 'number'){
                if(_.isNumber(value)) return value;
                if(_.isString(value) && !isNaN(value)) return _.toNumber(value);
                else return null;
            }
            if(type == 'string'){
                return _.toString(value);
            }
        }catch(e){
            self.L.error('Error while converting to specific datatype', e);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:CONVERT_TO_SPECIFIC_DATATYPE`,
                `STATUS:ERROR`
            ]);
            return null;
        }
    }

    prepareKafkaPayloadForDwhSync(record) {
        let self = this;
        try{
            let data = _.get(record, 'data', null);

            let notificationType = _.get(record, 'notificationType', null);
            if(notificationType == 'BILLDUE') notificationType = 'DUEDATE';

            
            _.set(data, 'timepoint', self.convertToSpecificDatatype(_.get(data, 'additional_data.timepoint', null), 'number'));
            _.set(data, 'msg_type', self.convertToSpecificDatatype(_.get(data, 'additional_data.msg_type', null), 'string'));
            _.set(data, 'promocode', self.convertToSpecificDatatype(_.get(data, 'additional_data.promocode', null), 'string'));
            _.set(data, 'notification_type', self.convertToSpecificDatatype(_.get(data, 'additional_data.notif_type', null), 'string'));
            _.set(data, 'bill_source', self.convertToSpecificDatatype(_.get(data, 'additional_data.bill_source', null), 'string'));

            if(self.notificationBillSource == 'NONRU') {
                if (notificationType) {
                    _.set(data, 'notification_type', self.convertToSpecificDatatype(notificationType, 'string'));
                }
                _.set(data,'bill_source',"NON_RU");
                self.setAdditionalDataForMetrics(record);
            }


            if (_.get(record, 'type', null) == 'WHATSAPP' && (_.get(data, 'dynamicParams.convertBGtoDueDate', false) || _.get(data, 'options.data.convertBGtoDueDate', false))){
                self.L.log('prepareKafkaPayloadForDwhSync:: setting when conversion is true');
                _.set(data, 'notification_type', self.convertToSpecificDatatype('BILLGEN', 'string'));
                let billFetchDate = MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD').isValid() ? MOMENT(_.get(record, 'bill_fetch_date', null), 'YYYY-MM-DD') : null;
                let dayValue=null;
                if(billFetchDate){
                    dayValue = MOMENT().diff(billFetchDate, 'days');
                }      
                _.set(data, 'timepoint', self.convertToSpecificDatatype(dayValue, 'number'));

            }

            return {
                type: self.convertToSpecificDatatype(_.get(record, 'type', null), 'string'),
                template_id: self.convertToSpecificDatatype(_.get(record, 'template_id', null), 'string'),
                recipient: self.convertToSpecificDatatype(_.get(record, 'recipient', null), 'string'),
                source_id: self.convertToSpecificDatatype(_.get(record, 'source_id', null), 'string'),
                category_id: self.convertToSpecificDatatype(_.get(record, 'category_id', null), 'number'),
                recharge_number: self.convertToSpecificDatatype(_.get(record, 'recharge_number', null), 'string'),
                product_id: self.convertToSpecificDatatype(_.get(record, 'product_id', null), 'string'),
                job_id: self.convertToSpecificDatatype(_.get(record, 'job_id', null), 'string'),
                send_at: self.convertToSpecificDatatype(_.get(record, 'send_at', null), 'string'),
                sent_at: self.convertToSpecificDatatype(_.get(record, 'sent_at', null), 'string'),
                error_msg: self.convertToSpecificDatatype(_.get(record, 'error_msg', null), 'string'),
                retry_interval: self.convertToSpecificDatatype(_.get(record, 'retry_interval', null), 'number'),
                priority: self.convertToSpecificDatatype(_.get(record, 'priority', null), 'number'),
                max_retry_count: self.convertToSpecificDatatype(_.get(record, 'max_retry_count', null), 'number'),
                retry_count: self.convertToSpecificDatatype(_.get(record, 'retry_count', null), 'number'),
                status: self.convertToSpecificDatatype(_.get(record, 'status', null), 'number'),
                created_at: self.convertToSpecificDatatype(_.get(record, 'created_at', null), 'string'),
                updated_at: self.convertToSpecificDatatype(_.get(record, 'updated_at', MOMENT().format('YYYY-MM-DD HH:mm:ss')), 'string'),
                previous_job_id: self.convertToSpecificDatatype(_.get(record, 'previous_job_id', null), 'string'),
                user_response: self.convertToSpecificDatatype(_.get(record, 'user_response', null), 'string'),
                event_timestamp: self.convertToSpecificDatatype(_.get(record, 'event_timestamp', null), 'string'),
                data: data,
                additional_data: {
                    templateName: self.convertToSpecificDatatype(_.get(record, 'data.additional_data.templateName', null), 'string'),
                    operator: self.convertToSpecificDatatype(_.get(record, 'data.additional_data.operator', null), 'string'),
                    amount: self.convertToSpecificDatatype(_.get(record, 'data.additional_data.amount', null), 'number'),
                    service: _.toLower(self.convertToSpecificDatatype(_.get(record, 'data.additional_data.service', null), 'string')),
                    raw_expiry_date: self.convertToSpecificDatatype(_.get(record, 'data.additional_data.due_date', null), 'string'),
                    customer_id: self.convertToSpecificDatatype(_.get(record, 'data.additional_data.customer_id', null), 'string'),
                    recon_id: self.convertToSpecificDatatype(_.get(record, ['data', 'additional_data','recon_id'], null), 'string'),
                },
                dataExhaustExtra: {
                    data_exhaust_value: self.convertToSpecificDatatype(_.get(record, "dataExhaustExtra.data_exhaust_value", null), 'string'),
                    data_exhaust_date: self.convertToSpecificDatatype(_.get(record, "dataExhaustExtra.data_exhaust_date", null), 'string')
                }
            }
        }catch(e){
            self.L.error('Error while preparing kafka payload for DWH sync', e);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE:PREPARE_KAFKA_PAYLOAD_FOR_DWH_SYNC`,
                `STATUS:ERROR`
            ]);
            return null;
        }
    }


    setAdditionalDataForMetrics(record) {
        let self = this;

        try {
            let data = _.get(record, 'data', null);

            // Early return if notification is NONRU
            if (_.get(record,'data.notificationUniqueKeyNONRU')) {
                utility._sendMetricsToDD(1, [
                    `REQUEST_TYPE:DWH_METRICS`,
                    `STATUS:IGNORE`  // Fixed typo
                ]);
                return;
            }

            let additional_data = {};
            
            // Get due date from various possible locations
            let duedate = _.get(record, 'due_date', null) || 
                        _.get(record,'data.dynamicParams.due_date',null) || 
                        _.get(record,'data.options.data.due_date',null);
            
            // Format due date if valid
            try {
                if(duedate && MOMENT(duedate).isValid()){
                    duedate = MOMENT(duedate).format('YYYY-MM-DD');
                }else if (duedate && MOMENT(duedate, 'Do MMM YYYY').isValid()){
                    duedate = MOMENT(duedate, 'Do MMM YYYY').format('YYYY-MM-DD');
                }
            } catch(e) {
                self.L.error('Error formatting due date:', e);
            }

            // Set additional data based on notification structure
            if(!_.get(record, 'data', null)){
                // Handle case where data is not present
                _.set(data,'additional_data.templateName', self.convertToSpecificDatatype(_.get(record, 'templateName', null), 'string'));
                _.set(data, 'additional_data.operator', self.convertToSpecificDatatype(_.get(record, 'operator', null), 'string'));
                _.set(data, 'additional_data.amount', self.convertToSpecificDatatype(_.get(record, 'amount', null), 'number'));
                _.set(data, 'additional_data.service', _.toLower(self.convertToSpecificDatatype(_.get(record, 'service', null), 'string')));
                _.set(data, 'additional_data.due_date', self.convertToSpecificDatatype(duedate, 'string'));
                _.set(data, 'additional_data.customer_id', self.convertToSpecificDatatype(_.get(record, 'customer_id', null), 'string'));
                _.set(data, 'additional_data.recon_id', self.convertToSpecificDatatype(_.get(record, 'recon_id', null), 'string'));
            } else {
                // Handle case where data is present
                additional_data = _.get(record,'data.dynamicParams',null) || _.get(record,'data.options.data',null);

                _.set(data, 'additional_data.due_date', self.convertToSpecificDatatype(duedate, 'string'));
                _.set(data, 'additional_data.templateName', self.convertToSpecificDatatype(_.get(record, 'data.templateName', null), 'string'));
                _.set(data, 'additional_data.recon_id', self.convertToSpecificDatatype(_.get(record, 'recon_id', null), 'string'));
                _.set(data, 'additional_data.operator', self.convertToSpecificDatatype(_.get(additional_data, 'operator', null), 'string'));
                _.set(data, 'additional_data.amount', self.convertToSpecificDatatype(_.get(additional_data, 'amount', null), 'number'));
                _.set(data, 'additional_data.service', _.toLower(self.convertToSpecificDatatype(_.get(additional_data, 'service', null), 'string')));
                _.set(data, 'additional_data.customer_id', self.convertToSpecificDatatype(_.get(additional_data, 'customer_id', null), 'string'));
            }
        } catch (e) {
            self.L.error('setAdditionalDataForMetrics:: Error:', {
                error: e.message,
                stack: e.stack,
                record: JSON.stringify(record)
            });
        }
    }
    checkDuplicacyFromCassandraNotificationTable(cb, record) {
        let self = this;
        let encryptedRecord = _.cloneDeep(record);

        let param = self.EncryptionDecryptioinHelper.getNotificationParamsForCCBP(record);

        if(self.EncryptionDecryptioinHelper.isWhitelistedForCC(param.service, param.paytype, param.customerId)) {
            _.set(encryptedRecord, 'recharge_number', self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recharge_number', null)));
            _.set(encryptedRecord, 'recipient', self.EncryptionDecryptioinHelper.encryptData(_.get(record, 'recipient', null)));
            this.checkDuplicacyFromCassandraNotificationTableDB((err, result) => {
                if (err) {
                    self.L.error('checkDuplicacyFromCassandraNotificationTable:: DB error', err);
                    return cb(err);
                }
                if (result && result.length > 0) {
                    if (self.cassandraBills.checkIfDbRecordIsEncrypted(result[0])) {
                        result[0].recharge_number = self.EncryptionDecryptioinHelper.decryptData(result[0].recharge_number);
                        result[0].data = self.EncryptionDecryptioinHelper.decryptData(result[0].data);
                        result[0].recipient = self.EncryptionDecryptioinHelper.decryptData(result[0].recipient);
                    }
                } else {
                    self.L.log('encrypted record not found');
                }
                return cb(null, result);
            }, encryptedRecord);
        } else {
            self.L.log('not whitelisted or NON CC Record found');
            return this.checkDuplicacyFromCassandraNotificationTableDB(cb, record);
        }
    }

    checkDuplicacyFromCassandraNotificationTableDB(cb, record) {
        let self = this;
        let tableSuffix = self.cassandraBills.getNotificationTableSuffix(_.get(record, 'send_at', null));
        let notificationTable = `notification${tableSuffix}`;
        let whereCondition = `recharge_number=? AND recipient=? AND type=? AND template_id=? AND source_id=? AND category_id=? AND product_id=? AND send_at > ?`;
        let queryParams = [
            _.get(record, 'recharge_number', null), _.get(record, 'recipient', null), _.get(record, 'type', null), 
            _.get(record, 'template_id', null), _.get(record, 'source_id', null), _.get(record, 'category_id', null), 
            _.get(record, 'product_id', null), _.get(record, 'last_ref_time', null)
        ]
        self.cassandraBills.getNotifications((err, result) => {
            if (err) {
                self.L.error('checkDuplicacyFromCassandraNotificationTable::', `error occurred for queryParams ${queryParams}`, err);
                return cb(err);
            }
            return cb(err, result.rows);
        }, notificationTable, whereCondition, queryParams);
    }

    isNotificationMigratedToCassandra(sourceId, customerId) {
        let self = this;
        if (sourceId == null || customerId == null) {
            return false;
        }
        let cassandraNotificationConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'NOTIFICATION_CASSANDRA', 
        'NOTIFICATION_MIGRATED_FOR_ALL_SOURCE_IDS'], false) || (_.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'NOTIFICATION_CASSANDRA', 
        'NOTIFICATION_MIGRATED_FOR_SOURCE_IDS'], [])).includes(_.toString(sourceId));
        let custIdOnePcConfig = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'NOTIFICATION_CASSANDRA', 'ENABLE_ONE_PC_CUSTOMERS_FOR_SOURCE_IDS'], []).includes(_.toString(sourceId));
        
        if (cassandraNotificationConfig && !custIdOnePcConfig) {
            return true;
        }
        if (cassandraNotificationConfig && custIdOnePcConfig && Number(customerId) % 100 == 1) {
            return true;
        }
        return false;
    }

    sendDatabaseErrorMetrics(db, flow) {
        var self = this;
        utility._sendMetricsToDD(1, [
            `FLOW:${flow}`,
            `TYPE:DATABASE_ERROR`,
            `DATABASE:${db}`,
            `BILL_SOURCE_TYPE:${self.notificationBillSource}`
        ]);
    }

    rescheduleNotification(record) {
        const self = this;
        let payloadService = _.toLower(_.get(record, 'service') || _.get(record, 'data.dynamicParams.service') || _.get(record, 'data.options.data.service'));
        self.logger.log('Notify :: rescheduleNotification Reschedule Notification -', record, payloadService);
        const recordDetails = typeof record.data === 'string' ? JSON.parse(record.data) : record.data;
        if (Object.keys(recordDetails).length > 0) {
            const nextSchedules = recordDetails.nextSchedules && recordDetails.nextSchedules.length > 0 ? recordDetails.nextSchedules : null;
            if (nextSchedules) {
                if(typeof nextSchedules[0] === 'object'){
                    let nextScheduleDate = nextSchedules.shift();
                    // Check if the next schedule date is of future, 
                    if (MOMENT(nextScheduleDate.date).isAfter(MOMENT().format('YYYY-MM-DD'))) {
                        record.send_at = nextScheduleDate.date;
                        record.template_id = nextScheduleDate.template_details.template_id;
                        recordDetails.template_id = nextScheduleDate.template_details.template_id;
                        if(nextScheduleDate.deepLinkObj){
                            recordDetails.options.notificationOpts.deepLinkObj = nextScheduleDate.deepLinkObj;
                        }
                        record.data = recordDetails;
                        record.notificationStatus = _.get(self.config, ['NOTIFICATION', 'status', 'RESCHEDULE'], 7);
                        self.notification.createNotification((error, data) => {
                            if (!error) {
                                L.info('Notify :: rescheduleNotification', 'Created Entry - ',data.insertId);
                            }
                        }, record)
                        if (self.isNotificationMigratedToCassandra(_.get(record, 'source_id', null), _.get(record, 'data.additional_data.customer_id', null))) {
                            self.createNotificationInCassandra(cb, record);
                        }
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:RESCHEDULE_NOTIFICATIONS',
                            'STATUS:SKIP_DUE_TO_PAST',
                            'BILL_SOURCE_TYPE:' + self.notificationBillSource
                        ]);
                    }

                }
                else{//not removing it for backward compatibility. will remove after some time once gateway also went live
                    const nextScheduleDate = nextSchedules.shift();
                    // Check if the next schedule date is of future, 
                    if (MOMENT(nextScheduleDate).isAfter(MOMENT().format('YYYY-MM-DD'))) {
                        record.send_at = nextScheduleDate;
                        record.data = recordDetails;
                        record.notificationStatus = _.get(self.config, ['NOTIFICATION', 'status', 'RESCHEDULE'], 7);
                        self.notification.createNotification((error, data) => {
                            if (!error) {
                                L.info('Notify :: rescheduleNotification', 'Created Entry - ',data.insertId);
                            }
                        }, record)
                        if (self.isNotificationMigratedToCassandra(_.get(record, 'source_id', null), _.get(record, 'data.additional_data.customer_id', null))) {
                            self.createNotificationInCassandra(cb, record);
                        }
                    } else {
                        utility._sendMetricsToDD(1, [
                            'REQUEST_TYPE:RESCHEDULE_NOTIFICATIONS',
                            'STATUS:SKIP_DUE_TO_PAST',
                            'BILL_SOURCE_TYPE:' + self.notificationBillSource
                        ]);
                    }
                }
            } else {
                self.L.log('notify::rescheduleNotification', `no nextSchedules found, not rescheduling any future notification`);
            }
        }
        return record;
    }
    updateRetryNotificationServiceStatus() {
        let self = this,
            currentDate = MOMENT().format('YYYY-MM-DD'),
            startDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['FROM'], '07:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            endDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['TO'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss');
       
        // schedule start and stop consumer timings
        if (_.get(self.notificationConfig, 'SCHEDULE_ON_INTERVAL', false) === true) {
            let now = MOMENT().add(1, 'minutes').format('YYYY-MM-DD HH:mm:ss');

            if(MOMENT(now).isBetween(startDateTime, endDateTime)) {
                self.service_status = true;// started
            }
            else {
                self.service_status = false;// stoped
            }      
        }
        L.log('updateRetryNotificationServiceStatus', 'start_service: ', self.service_status);
    }

    formatDueDate(dueDate) {
        console.log("🚀 ~ Notify ~ formatDueDate ~ dueDate:", dueDate)
        let self = this;
        try {
            if (dueDate == null || dueDate == undefined || dueDate == '') {
                return null;
            }

            // Handle ordinal date format like "29th Jul 2025"
            let dueDateObj;
            if (typeof dueDate === 'string' && dueDate.match(/^\d{1,2}(st|nd|rd|th)\s+[A-Za-z]{3}\s+\d{4}$/)) {
                // Extract day with ordinal suffix and month abbreviation
                const match = dueDate.match(/^(\d{1,2}(st|nd|rd|th))\s+([A-Za-z]{3})/);
                if (match) {
                    return `${match[1]} ${match[3]}`; // Returns "29th Jul"
                }
            } else {
                // For other date formats, return the original dueDate
                return dueDate;
            }

            console.log("🚀 ~ Notify ~ formatDueDate ~ dueDateObj:", dueDateObj)
            //dueDate: 29th Jul 2025 format this to remove year and return only month and day
        } catch (error) {
            self.L.error("formatDueDate:: error while formatting due date", error);
            return null;
        }
    }

    /**
     * Formats a number with comma separators for better readability
     * @param {number|string} amount - The amount to format
     * @param {string} locale - The locale to use for formatting (default: 'en-IN')
     * @param {number} minimumFractionDigits - Minimum decimal places (default: 0)
     * @param {number} maximumFractionDigits - Maximum decimal places (default: 2)
     * @returns {string} Formatted amount with comma separators
     * 
     * @example
     * formatAmountWithCommas(1234567.89) // Returns "12,34,567.89"
     * formatAmountWithCommas(1000) // Returns "1,000"
     * formatAmountWithCommas(1000.5) // Returns "1,000.50"
     * formatAmountWithCommas(1000, 'en-US') // Returns "1,000.00"
     */
    formatAmountWithCommas(amount, locale = 'en-IN', minimumFractionDigits = 0, maximumFractionDigits = 2) {
        let self = this;
        try {
            self.L.log(`formatAmountWithCommas:: formatting amount ${amount}`);
            // Convert to number if it's a string
            if (amount == null || amount == undefined || amount == '') {
                return null;
            }
            let numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

            // Check if it's a valid number
            if (isNaN(numericAmount)) {
                L.error("formatAmountWithCommas:: invalid amount received", amount);
                return amount;
            }

            // Format the number with locale-specific comma separators
            return numericAmount.toLocaleString(locale, {
                minimumFractionDigits: minimumFractionDigits,
                maximumFractionDigits: maximumFractionDigits
            });
        } catch (error) {
            L.error("formatAmountWithCommas:: error formatting amount", amount, error);
            return '0';
        }
    }

        suspendOperations(){
            var self = this,
            deferred = Q.defer();
            self.L.log(`notifyConsumer::suspendOperations kafka consumer shutdown initiated`);

            Q()
            .then(function(){
                if (self.kafkaNotificationConsumer) {
                    self.kafkaNotificationConsumer.close(function(error, res){
                        if(error){
                            self.L.error(`notifyConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                            return error;
                        }
                        self.L.info(`notifyConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
                    })
                }
                else {
                    self.L.log('notifyConsumer::suspendOperations', 'No active Kafka consumer to close');
                }
                
            })
            .then(function(){
                self.L.log(`notifyConsumer::suspendOperations kafka consumer shutdown successful`);
                deferred.resolve();
            })
            .catch(function(err){
                self.L.error(`notifyConsumer::suspendOperations error in shutting kafka consumer`, err);
                deferred.reject(err);
            });
            return deferred.promise;
        }
}

export default Notify;
