'use strict'

import MOMENT from 'moment'
import L from 'lgr'
import INFRAUTILS from 'infra-utils'

import { promisify } from 'util'
import { get, set, flatten, isNaN } from 'lodash'

import CatalogVerticalRecharge from '../models/catalogVerticalRecharge'
import PublishStats from '../lib/publishStats'

import utility from '../lib';
import startup from '../lib/startup'
import DigitalCatalog from '../lib/digitalCatalog'
import PROC from '../lib/ptm_proc'


import DigitalReminderConfig from '../models/digitalReminderConfig'
import { option } from 'commander'

class PlanValiditySyncDbPublisher {
    constructor(options) {
        this.L = options.L;
        options.appName = "VI_SYNC_DB_PUBLISHER";

        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.infraUtils = options.INFRAUTILS;

        this.digitalCatalog = new DigitalCatalog(options);

        this.digitalReminderConfig = new DigitalReminderConfig(options);

        this.activePidLib = options.activePidLib;

        this.commonLib = new utility.commonLib(options);
        this.catalogVerticalRecharge = new CatalogVerticalRecharge(options);
        this.cvrDataPidMap = {}

        this.group = options.group || "vil";
        this.operators = this.config.PLAN_VALIDITY_NOTIFICATION[this.group.toUpperCase()];

        this.service = options.service || ["mobile"];

        let { MONGO_DB, MYSQL, KAFKA } = this.config.VIL_SYNC_DB;

        // this.mongoDbInstance = new INFRAUTILS.mongo(this.config.MONGO.HIDDEN_SLAVE);
        this.rechargeSagaCassandraDb = options.rechargeSagaCassandraDb;
        this.rechargeSagaCassandraDbRecentKeySpace = options.rechargeSagaCassandraDbRecentKeySpace;

        // this.mongoCollection = 'users';
        this.retryCountForMongo = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'RETRY_COUNT'], MONGO_DB.RETRY_COUNT);

        this.mongoDbTps = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS'], MONGO_DB.TPS);
        this.timeThresholdForMongo = parseInt(1000 / this.mongoDbTps);  //this.mongoDbTps = 3 or 4;
        this.lastMongoFetchTime = this.getTimeInMs() - this.timeThresholdForMongo;

        this.tpsSwitchTime = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS_SWITCH_HOUR'], MONGO_DB.TPS_SWITCH_HOUR);

        this.mongoDbFetchRecordsLimit = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FETCH_LIMIT'], MONGO_DB.FETCH_LIMIT);


        this.mongoDbFetchRecordsFailureRetryInterval = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FAILURE_RETRY_INTERVAL'], MONGO_DB.FAILURE_RETRY_INTERVAL);


        this.mysqlSyncTable = "vil";   // need to be changes after dba confirmation
        this.mysqlSyncTemporaryTable = this.getTempTableName();   // need to be changes after dba confirmation

        this.setExpiryDateRange();

        this.fromId = options.id || get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MYSQL', 'FROM_ID'], MYSQL.FROM_ID);

        this.greyScaleEnv = get(options, 'greyScaleEnv', false);

        this.mysqlDbFetchRecordsLimit = this.greyScaleEnv ? get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MYSQL_FETCH_LIMIT'], 2) :
            get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MYSQL', 'FETCH_LIMIT'], MYSQL.FETCH_LIMIT);


        this.mysqlSkipRows = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MYSQL', 'SKIP_LIMIT'], 0);

        this.insertQueryForMainSyncTable = `insert into ${this.mysqlSyncTable}(recharge_number, expiry_date, amount, plan_bucket, current_status, final_status, circle, circle_from_operator, is_ul, report_date) values(?,?,?,?,?,?,?,?,?,?);`;

        this.selectQueryRetryTime = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'RETRY_AFTER', 'SELECT_QUERY'], 2000);

        this.TEMP_TABLE_ROWS_THRESHOLD_LIMIT = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MYSQL_TEMP_TABLE', 'ROWS_THRESHOLD_LIMIT'], 200000);

        this.validAmount = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'PLAN', 'VALID_AMOUNT'], this.config.VIL_SYNC_DB.VALID_AMOUNT);
        this.validAmount = this.validAmount.split(',');
        this.dcatMapRefreshInterval = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'DCAT', 'MAP_REFRESH_INTERVAL'], this.config.VIL_SYNC_DB.DCAT_MAP_REFRESH_INTERVAL);

        /**
         * Old Record Updation Config
         * where old records are that records which are not come today.
         */
        this.isFetchAllowed = true;
        this.isUpdatationAlowedOnOldRecord = true;

        let OLD_RECORD_CONFIG = this.config.VIL_SYNC_DB.OLD_RECORD;

        this.updatationOldRecordLimit = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'UPDATE_OLD_RECORD', 'LIMIT'], OLD_RECORD_CONFIG.UPDATION_LIMIT);
        this.updatationOldRecordSleepTime = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'UPDATE_OLD_RECORD', 'SLEEP_TIME'], OLD_RECORD_CONFIG.SLEEP_TIME);

        /**
         * map for digital catalog information
         */

        this.validPlan = {};
        this.inValidPlan = {};


        this.publishingRate = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'KAFKA', 'PUBLISHING_RATE'], KAFKA.PUBLISHING_RATE);
        this.timeThresholdForKafkaPublisher = 1000;
        this.lastStartPublishingTime = this.getTimeInMs() - this.timeThresholdForKafkaPublisher;

        this.rnToCidMappingTable = "recharge_to_customer_map";
        this.recentsTable = "recents";
    }

    async start() {
        this.L.log("start", "plan validity Sync Db service started");

        this.L.log("start", "Mongo DB connectivity!!")


        
        // await promisify(this.mongoDbInstance.connect.bind(this.mongoDbInstance))();

        this.L.log("Mongo DB Connected!!")

        this.L.log("start", "set CVR data");
        await this.setCvrData();

        setInterval(() => {
            this.refreshCvrData();
        }, 1800000);

        this.L.log('start', 'Going to configure Publisher');
        await this.startPublisher();

        this.L.log('Publisher has Started!!');

        this.nextDayExit();
        this.setWindowStartTime();

        await this.updateDynamicConfig();

        await this.fetchAndProcessRecords();

        this.killThisProcess();
    }

    killThisProcess() {
        if (this.updateRecordFinalStatusFlag == "COMPLETED") {
            this.publishStats({ type: "UPDATE_FINAL_STATUS_DONE", count: 1, STATE: "INFO" });

            this.L.log("Service will stopped after 2 minute!!");

            setTimeout(() => {
                process.exit(0);
            }, 120 * 1000);

        } else {
            this.publishStats({ type: "UPDATE_FINAL_STATUS_CALL_ONGOING", count: 1, STATE: "INFO" });
            this.L.log("killThisProcess, call after 5 minute");

            setTimeout(() => {
                this.killThisProcess();
            }, 300000); // calling after 5 minute
        }
    }

    getTempTableName() {

        let name = "vil_temp_";

        let currentHour = new Date().getHours(),
            day, nextDayStoppedHour = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'NEXT_DAY', 'SERVICE_STOP_HOUR'], this.config.VIL_SYNC_DB.nextDayStoppedHour);

        if (currentHour > nextDayStoppedHour) {
            day = 0;
        } else {
            day = 1;
        }

        let date = MOMENT().subtract(day, 'day').format("YYYY_MM_DD");

        return name + date;
    }

    setExpiryDateRange() {
        let currentHour = new Date().getHours(),
            day,
            nextDayStoppedHour = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'NEXT_DAY', 'SERVICE_STOP_HOUR'], this.config.VIL_SYNC_DB.nextDayStoppedHour);

        if (currentHour > nextDayStoppedHour) {
            day = 0;
        } else {
            day = 1;
        }

        let fromdays = this.config.VIL_SYNC_DB.fromDays;
        let toDays = this.config.VIL_SYNC_DB.toDays;

        this.fromDate = MOMENT().subtract(day + fromdays, 'day').startOf('day').format('YYYY-MM-DD');
        this.toDate = MOMENT().add(toDays - day, 'day').endOf('day').format('YYYY-MM-DD');
        this.today = MOMENT().subtract(day, 'day').startOf('day').format('YYYY-MM-DD');

    }

    async updateDynamicConfig() {
        let query = 'update digital_reminder_config set value=? where name = ? and node = ? and key_name = ?';
        let queryParms = [];

        try {
            let count = await this.fetchTotalCount();

            this.publishStats({ type: "TEMP_TABLE_SIZE", count: 1, size: count, STATE: "INFO" })

            this.L.log("records threshold limit at our side: ", this.TEMP_TABLE_ROWS_THRESHOLD_LIMIT, " and today records have come : ", count);

            if (count == 0 || !count || count < this.TEMP_TABLE_ROWS_THRESHOLD_LIMIT) {
                this.isUpdatationAlowedOnOldRecord = false;
                queryParms.push(0);
            } else {
                queryParms.push(1);
            }

            queryParms.push('PLAN_VALIDTY_NOTIFICATION_CONSUMER', 'VALIDATION_WITH_VIL_SYNC_DB', 'ENABLE');

            let response = await this.digitalReminderConfig.updateDynamicConfig(query, queryParms);

            this.L.log(`updateDynamicConfig:: response of updation: `, response);

        } catch (error) {
            this.L.critical(`updateDynamicConfig, please check the publisher,  error:`, error, ` for query: `, query, `, for queryParms: `, queryParms);
        }
    }
    /**
     * we need to stop this service on next day at 9 am because fresh data will available in new temp table.
     */

    nextDayExit() {

        let currentHour = new Date().getHours(),
            day;

        let startTime = MOMENT().format("YYYY-MM-DD HH:mm:ss"),
            nextDayStoppedHour = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'NEXT_DAY', 'SERVICE_STOP_HOUR'], this.config.VIL_SYNC_DB.nextDayStoppedHour),
            nextDayExitTime;

        if (currentHour > nextDayStoppedHour) {
            day = 1
        } else {
            day = 0;
        }

        nextDayExitTime = MOMENT().hour(nextDayStoppedHour).minute(0).seconds(0).add(day, 'day')

        this.nextDayExitTime = nextDayExitTime;

        setInterval(async () => {
            if (MOMENT().diff(nextDayExitTime) > 0) {

                this.L.log("nextDayExit, now time over!!");
                if (this.isFetchAllowed == true) {
                    this.isFetchAllowed = false;
                    await this.updateRecordFinalStatusWhichNotCameToday();
                }

            } else {

                this.validAmount = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'PLAN', 'VALID_AMOUNT'], this.config.VIL_SYNC_DB.VALID_AMOUNT);
                this.validAmount = this.validAmount.split(',');

                let { MONGO_DB, KAFKA } = this.config.VIL_SYNC_DB;

                this.tpsSwitchTime = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS_SWITCH_HOUR'], MONGO_DB.TPS_SWITCH_HOUR);

                let switchHours = this.tpsSwitchTime.split(':');
                let currentHour = MOMENT().format("HH");

                /**
                 * +(plus) operator convert string to integer
                 */
                if (+currentHour >= +switchHours[0] && +currentHour < +switchHours[1]) {
                    this.mongoDbTps = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'SWITCH_TPS'], MONGO_DB.SWITCH_TPS);
                    this.mongoDbFetchRecordsLimit = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'SWITCH_FETCH_LIMIT'], MONGO_DB.SWITCH_FETCH_LIMIT);

                    this.publishingRate = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'KAFKA', 'SWITCH_PUBLISHING_RATE'], KAFKA.SWITCH_PUBLISHING_RATE);
                } else {
                    this.mongoDbTps = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'TPS'], MONGO_DB.TPS);
                    this.mongoDbFetchRecordsLimit = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'MONGO_DB', 'FETCH_LIMIT'], MONGO_DB.FETCH_LIMIT);

                    this.publishingRate = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'KAFKA', 'PUBLISHING_RATE'], KAFKA.PUBLISHING_RATE);
                }

                this.timeThresholdForMongo = parseInt(1000 / this.mongoDbTps);


                this.L.log("nextDayExit, timeThresholdForMongo: ", this.timeThresholdForMongo, " publishingRate: ", this.publishingRate);
            }
        }, 5 * 60 * 1000);
    }

    setWindowStartTime() {
        let currentHour = new Date().getHours(),
            nextDayStoppedHour = get(this.config, ['DYNAMIC_CONFIG', 'VIL_SYNC_DB_PUBLISHER', 'NEXT_DAY', 'SERVICE_STOP_HOUR'], 9),
            day;

        if (currentHour > nextDayStoppedHour) {
            day = 0
        } else {
            day = 1;
        }

        day += 2;

        this.windowStartTime = MOMENT().subtract(day, 'day').startOf('d').format("YYYY-MM-DD HH:mm:ss");
    }

    setCvrData() {
        return new Promise((resolve, reject) => {
            this.catalogVerticalRecharge.getCvrData((error, data) => {
                if (!error && data.length) {
                    this.L.log('preparing cvr data', data.length);
                    let i = 0;
                    data.forEach(row => {
                        i++;
                        this.cvrDataPidMap[row.product_id] = row;
                        if (i == data.length) {
                            resolve();
                        }

                    });
                } else {
                    reject(error || "get empty response from CVR");
                }
            }, " paytype = 'prepaid' and status=1"); // getting only active PIDs which will ensure use of fresh/Active PID
        });
    }

    refreshCvrData() {
        let subquery = ` and updated_at > '${MOMENT().subtract(1, 'hour').subtract(40, 'minute').format("YYYY-MM-DD HH:mm:ss")}'`;

        this.L.log('refreshCvrData:: refresh process has started for cvr data!!');

        this.catalogVerticalRecharge.getCvrData((error, data) => {
            if (!error) {
                this.L.log('refreshCvrData:: preparing cvr data', data.length);

                data.forEach(row => {
                    this.cvrDataPidMap[row.product_id] = row;
                });

                this.L.log("refreshCvrData:: CVR data has refreshed now!!");
            } else {
                this.L.critical("refreshCvrData, error in getting cvr data from db!!");
            }
        }, " paytype = 'prepaid' and status=1 " + subquery);

    }

    async startPublisher() {
        this.kafkaPublisher = new this.infraUtils.kafka.producer({
            "kafkaHost": this.config.KAFKA.TOPICS.PLAN_VALIDITY_SYNC_DB.HOSTS
        });
        await this.kafkaPublisher.initProducer('high');
    }

    /**
     * data fetch from temp table
     * -> data will fetch from main table
     * -> update data in the main table
     * -> data fetch from plan_validity table
     * -> make entry data for already PAID_AT_PAYTM
     * -> create data in the main table after validation for paid_at_other_platform
     * -> publish data
     * 
     */

    async fetchAndProcessRecords() {
        try {
            this.L.log('start', 'fetch Records from temp Table');

            let records = [];

            do {
                this.L.log(`fetch from id: ${this.fromId} and it's current fetch limit: ${this.mysqlDbFetchRecordsLimit}`);

                records = await this.fetchRecordFromTempTable();

                if (records.length == 0) break;

                let newRecords = await this.removeAlreadyProcessedRecords(records);

                this.L.verbose(`fetchAndProcessRecords:: valid record for publish for this phase and fromId: ${this.fromId} `, newRecords);

                this.L.verbose(`fetchAndProcessRecords:: validPlan: `, this.validPlan, " and invalid plan: ", this.inValidPlan);

                if (newRecords.length) {
                    await this.processNewRecords(newRecords);
                }

                this.fromId += this.mysqlDbFetchRecordsLimit;
            } while (records.length == this.mysqlDbFetchRecordsLimit && this.isFetchAllowed);

            if (this.fromId == 0) {
                this.L.critical("Temp table is empty and there should be some issue!!");
            } else {
                this.L.info("All records has fetched and processed!!");
            }

            if (this.isFetchAllowed) {
                this.isFetchAllowed = false;
                await this.updateRecordFinalStatusWhichNotCameToday();
            }

        } catch (error) {
            this.L.error(`fetchAndProcessRecords:: current_fetch_from_id=${this.fromId} and fetch limit: ${this.mysqlDbFetchRecordsLimit} error: `, error);
            await this.commonLib.calledAfterTime(this.selectQueryRetryTime);
            await this.fetchAndProcessRecords();
        }
    }


    /**
     * 
     * @param {*} records these has come from temp table
     * we need to validate with main table(already exists records need to upadted) and plan_valaidity table(paid at paytm records need to direct insert in VIL_main table for history purpose, we don't need to publish them.) 
     */
    async removeAlreadyProcessedRecords(records) {
        try {
            let recordsMap = {};

            /**
             * create query for checking existing rows in Vil_SYNC_MAIN_TABLE 
            */

            let finalQuery = '',
                query = `select id, recharge_number,expiry_date,amount, final_status  from ${this.mysqlSyncTable} where recharge_number = ? and expiry_date = ? and amount = ?;`;

            for (let record of records) {
                let { recharge_number, expiry_date, amount } = record;

                if (this.expiryDateAndAmountValidation(record)) {
                    let key = `${recharge_number}_${MOMENT(expiry_date).format('YYYY-MM-DD')}_${amount}`;

                    recordsMap[key] = record;

                    record.expiry_date = expiry_date = MOMENT(expiry_date).format("YYYY-MM-DD");

                    finalQuery += this.dbInstance.format(query, [recharge_number, expiry_date, amount]);
                }
            }

            let responses = [];
            if (finalQuery.length) {
                responses = await this.runQuery('OPERATOR_SYNC_SLAVE', finalQuery, [], this.mysqlSyncTable);
            }

            this.L.verbose("removeAlreadyProcessedRecords:: responses from VI main table: ", responses);

            /**
             * handle array of array use case 
             * */

            responses = flatten(responses);

            finalQuery = '';
            let time = MOMENT().format('YYYY-MM-DD HH:mm:ss'),
                updateQuery = `update ${this.mysqlSyncTable} set updated_at = ? where id = ?;`;

            for (let response of responses) {
                let { id, recharge_number, expiry_date, amount, final_status } = response;

                expiry_date = MOMENT(expiry_date).format("YYYY-MM-DD");

                let key = `${recharge_number}_${expiry_date}_${amount}`;

                this.L.verbose("removeAlreadyProcessedRecords:: delete old vi record from today recordsMap: ", recordsMap[key]);

                if (response.final_status != 0) {
                    this.L.log("removeAlreadyProcessedRecords, FINAL_STATUS_HAS_ALREADY_CHANGED, RECORD: ", recordsMap[key]);
                    this.publishStats({ type: "FINAL_STATUS_HAS_ALREADY_CHANGED", count: 1, STATE: "INFO" });
                }

                delete recordsMap[key];

                this.publishStats({ type: "ALREADY_PROCESSED", count: 1, STATE: "INFO" });

                finalQuery += this.dbInstance.format(updateQuery, [time, id]);
            }


            if (finalQuery.length) {
                await this.runQuery('OPERATOR_SYNC', finalQuery, [], this.mysqlSyncTable).catch(err => this.L.critical("removeAlreadyProcessedRecords:: error in update updated_at in pre-exits Records in VilSync Main table: ", err, ` we need to run this query: ${finalQuery}`));
            }

            return Object.values(recordsMap);

        } catch (error) {
            this.L.error("error in removeAlreadyProcessedRecords: ", error);

            this.publishStats({ type: "ERROR_IN_VALIDATION_WITH_VI", count: 1, STATE: "ERROR" });

            await this.commonLib.calledAfterTime(this.selectQueryRetryTime);
            await this.removeAlreadyProcessedRecords(records)
        }
    }

    expiryDateAndAmountValidation(record) {
        let validationStatus = true;

        try {
            let { expiry_date, recharge_number, amount } = record;

            if (isNaN(Date.parse(expiry_date))) {
                validationStatus = false;

                this.L.error("expiryDateAndAmountValidation:: date is not in the correct format so ignoring this record", record);
                this.publishStats({ type: "INVALID_EXPIRY_DATE", count: 1, STATE: "INFO" })
            }

            if (MOMENT(expiry_date).diff(this.fromDate, 'day') < 0 || MOMENT(expiry_date).diff(this.toDate, 'day') >= 1) {
                validationStatus = false;
                this.publishStats({ type: "EXPIRY_DATE_IS_OUT_OF_RANGE", count: 1, STATE: "INFO" })
                this.L.log("expiryDateAndAmountValidation:: Expiry-Date is out of range from VI, recharge_number: ", recharge_number, ", expiry_date:", expiry_date, ", amount: ", amount);
            }

            if (amount == 0) {
                validationStatus = false;
                this.publishStats({ type: "AMOUNT_IS_ZERO", count: 1, STATE: "INFO" })
                this.L.log("expiryDateAndAmountValidation:: AMOUNT_IS_ZERO, recharge_number: ", recharge_number, ", expiry_date:", expiry_date, ", amount: ", amount);
            }

        } catch (error) {
            validationStatus = false;
            this.L.error("error in expiryDateAndAmountValidation: ", error);
            this.publishStats({ type: "SOME_ERROR", count: 1, STATE: "ERROR" });
        }

        return validationStatus;
    }

    /**
     * we need to valadate records with recents, set correct active pid and publish and insert in vil_sync main table
     * @param {*} records: which need to publish 
     */

    async processNewRecords(records) {

        let sliceStartIndex = 0,
            sliceEndIndex = this.mongoDbFetchRecordsLimit,
            recordsLength = records.length;

        try {
            do {
                let mongoSlices = records.slice(sliceStartIndex, sliceEndIndex);
                let rechargeNumbers = [];
                let rnAndExpiryDateMap = {};

                let key = '';
                let syncDBMap = {};

                for (let row of mongoSlices) {
                    rechargeNumbers.push(row.recharge_number + "");   // need to update accrodingly sync db table
                    rnAndExpiryDateMap[row.recharge_number + ""] = row;

                    let { recharge_number, amount } = row;

                    key = `${recharge_number}_${amount}`;

                    syncDBMap[key] = row;
                }

                let rechargeNumberMapFromRecents = {};

                if (rechargeNumbers.length) {
                    rechargeNumberMapFromRecents = await this.fetchRecordsFromRecents(rechargeNumbers, rnAndExpiryDateMap);
                } 

                this.L.verbose("processNewRecords: rechargeNumberMapFromRecents: ", rechargeNumberMapFromRecents);

                let recordForPlanValidityValidation = [];

                for (let rechargeNumber in rechargeNumberMapFromRecents) {
                    let record = rechargeNumberMapFromRecents[rechargeNumber];
                    let recordFromSyncDb = rnAndExpiryDateMap[rechargeNumber];

                    this.L.log("processNewRecords: record from recents: ", JSON.stringify(record));

                    let setStatus = await this.setActivePidAndCircle(record, recordFromSyncDb);

                    if (setStatus) {

                        let isPublishable = await this.validateAmountAndSetPlanBucket(record, recordFromSyncDb);

                        if (isPublishable) {
                            recordFromSyncDb.recentData = record;
                            recordForPlanValidityValidation.push(recordFromSyncDb)
                        }
                        this.L.verbose("processNewRecords: publishing result: ", isPublishable);
                    }
                    this.L.verbose("processNewRecords: validationResult: ", setStatus);
                }

                if (recordForPlanValidityValidation.length) {
                    let finalRecordsForPublished = await this.filterPaidAtPaytmRecords(recordForPlanValidityValidation);

                    if (finalRecordsForPublished.length) {
                        await this._publishedRecords(finalRecordsForPublished);
                    } else {
                        this.L.log("processNewRecords, there is no records for published in this phase.");
                    }
                } else {
                    this.L.log("processNewRecords, there is no records to validate with Plan Validity in this phase.");
                }

                await this.missedRecords(rnAndExpiryDateMap, rechargeNumberMapFromRecents);

                sliceStartIndex = sliceEndIndex;
                sliceEndIndex += this.mongoDbFetchRecordsLimit;
            } while (sliceStartIndex < recordsLength);

        } catch (error) {
            this.L.error(`processNewRecords:: from: ${records[0].recharge_number} to: ${records[recordsLength - 1].recharge_number}  error: `, error);
            this.publishStats({ type: "ERROR_IN_PROCESS_RECORDS", STATE: "ERROR", count: 1 });
        }
    }

    /**
     * final decision will take on valid amount array, not on dcat API
     * @param {*} record 
     * @param {*} recordFromSyncDb 
     */
    async validateAmountAndSetPlanBucket(record, recordFromSyncDb) {
        let isPublishable = false;
        let bucket = '';

        let { amount, recharge_number, expiry_date } = recordFromSyncDb;

        try {
            let validationResult = await this.planValidation({
                amount: amount,
                circle: record.active_circle,
                operator: record.active_operator,
                service: record.service
            });

            bucket = validationResult.data.plan_bucket;

            // amount is valid in our system, it would be publish
            if (validationResult.status) {
                isPublishable = true;
            }

        } catch (error) {
            this.L.error("validateAmountAndSetPlanBucket:: error, recharge_number: ", error, " for records", JSON.stringify(record), "", JSON.stringify(recordFromSyncDb));
        }

        //validate amount, we publish on basis of amount
        if (this.validAmount.includes(amount.toString())) {
            isPublishable = true;
            this.publishStats({ type: "COUNT_OF_VALID_AMOUNT", count: 1, STATE: "ERROR", AMOUNT: amount })
        } else {
            this.publishStats({ type: "COUNT_OF_INVALID_AMOUNT", count: 1, STATE: "ERROR", AMOUNT: amount })
        }

        recordFromSyncDb.plan_bucket = (bucket || amount);

        if (!isPublishable) {
            this.publishStats({ type: "INVALID_AMOUNT_RECORD_NOT_PUBLISHABLE", count: 1, STATE: "ERROR", AMOUNT: amount })
            this.L.log("validateAmountAndSetPlanBucket:: amount is not valid for publish, recharge_number: ", recharge_number, ", expiry_date:", expiry_date, ", amount: ", amount);

            let { is_ul, report_date, circle, plan_bucket } = recordFromSyncDb;
            let { active_circle, } = record;
            let paidAtOtherPlatform = this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM,
                notInUse = this.config.COMMON.bills_status.NOT_IN_USE;

            this.runQuery('OPERATOR_SYNC', this.insertQueryForMainSyncTable, [recharge_number, expiry_date, amount, plan_bucket, paidAtOtherPlatform, notInUse, circle, active_circle, is_ul, report_date], this.mysqlSyncTable).
                catch(err => this.L.critical(`setActivePidAndCircle:: error in insert missed Records in VilSync Main table`, err));
        }

        return isPublishable;
    }

    /**
     * here is only handling for mobile:service
     * should we need to bucket check also, we need to take same bucket records from various customer ????
     * you need to know reason behind to update the mongo recents ??
     */

    async queryRecentCassDbOnMappingTable(query, params) {
        this.L.log("queryRecentCassDbOnMappingTable:: query: ", query, " and params: ", params);
        return new Promise((resolve, reject) => {
            let latencyStart = new Date();
            this.rechargeSagaCassandraDb.execute(query, params, { prepare: true})
            .then((result) => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'VIL_SYNC_DB_PUBLISHER', 'QUERY_TYPE': 'queryRecentCassDbOnMappingTable' });
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VIL_SYNC_DB_PUBLISHER", "QUERY_TYPE:queryRecentCassDbOnMappingTable", "STATUS:SUCCESS"])
                this.L.verbose("queryRecentCassDbOnMappingTable:: result: ", result.rows);
                resolve(result.rows);
            })
            .catch((err) => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VIL_SYNC_DB_PUBLISHER", "QUERY_TYPE:queryRecentCassDbOnMappingTable", "STATUS:FAILURE"])
                this.L.error("queryRecentCassDbOnMappingTable:: error: ", err);
                reject(err);
            })
        });
    }

    async queryRecentCassDbOnRecentsTable(query, params) {
        this.L.log("queryRecentCassDbOnRecentsTable:: query: ", query, " and params: ", params);
        return new Promise((resolve, reject) => {
            let latencyStart = new Date();
            this.rechargeSagaCassandraDbRecentKeySpace.execute(query, params, { prepare: true})
            .then((result) => {
                utility._sendLatencyToDD(latencyStart, { 'REQUEST_TYPE': 'VIL_SYNC_DB_PUBLISHER', 'QUERY_TYPE': 'queryRecentCassDbOnRecentsTable' });
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VIL_SYNC_DB_PUBLISHER", "QUERY_TYPE:queryRecentCassDbOnRecentsTable", "STATUS:SUCCESS"])
                this.L.verbose("queryRecentCassDbOnRecentsTable:: result: ", result.rows);
                resolve(result.rows);
            })
            .catch((err) => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:VIL_SYNC_DB_PUBLISHER", "QUERY_TYPE:queryRecentCassDbOnRecentsTable", "STATUS:FAILURE"])
                this.L.error("queryRecentCassDbOnRecentsTable:: error: ", err);
                reject(err);
            })
        });
    }



    async getDataFromRecentsInBatch(rechargeNumbers, retryCount = 0) {
        try{
            let customerIds = [];
            let rnPlaceHolder = rechargeNumbers.map(() => '?').join(', ');
            let operatorPlaceHolder = this.operators.map(() => '?').join(', ');

            let mappingTableQuery = 
            `SELECT customerid 
                FROM ${this.rnToCidMappingTable} 
                WHERE service=? AND recharge_number_1 IN (${rnPlaceHolder}) AND operator IN (${operatorPlaceHolder})`;

            let data = await this.queryRecentCassDbOnMappingTable(mappingTableQuery, ['mobile', ...(rechargeNumbers), ...(this.operators)]);
            data.forEach((record) => {
                customerIds.push(record.customerid);
            })
            let cidPlaceHolderRecents = customerIds.map(() => '?').join(', ');
            let rnPlaceHolderRecents = rechargeNumbers.map(() => '?').join(', ');
            let operatorPlaceHolderRecents = this.operators.map(() => '?').join(', ');

            let recentsTableQuery = 
            `SELECT customerid,recharge_number,operator,service,paytype,product_id,plan_bucket,circle,created_at,updated_at
                FROM ${this.recentsTable} 
                WHERE customerid IN (${cidPlaceHolderRecents}) AND service=? AND recharge_number IN (${rnPlaceHolderRecents}) AND operator IN (${operatorPlaceHolderRecents})`;

            let recentRecords = await this.queryRecentCassDbOnRecentsTable(recentsTableQuery, [...(customerIds), 'mobile', ...(rechargeNumbers), ...(this.operators)]);

            return recentRecords;
        }catch(error){
            this.L.error(`getDataFromRecentsInBatch:: error in fetchRecords `, error);
            if (retryCount < this.retryCountForMongo) {
                await this.getDataFromRecentsInBatch(rechargeNumbers, retryCount + 1);
            } else {
                this.L.error(`getDataFromRecentsInBatch:: error in fetchRecords:`, error);
                throw error;
            }
        }
    }
    async fetchRecordsFromRecents(rechargeNumbers, syncDBRechargeNumberMap) {
        try {
            // let queryObj = {
            //     query: {
            //         'recharge_number': { $in: rechargeNumbers },
            //         'operator': { $in: this.operators }
            //     }
            // };

            // let data = await this.mongoThrottleWapper(queryObj);
            let data = await this.getDataFromRecentsInBatch(rechargeNumbers);


            let rechargeNumberMapFromRecents = {};

            /**
             * do service based filatration, for VIL, service is mobile
             * take latest update records for customerId notification
             */
            for (let record of data) {
                if (this.service.includes(record.service)) {

                    this.L.log("fetchRecordsFromRecents, record ", JSON.stringify(record));

                    let prevValaue = rechargeNumberMapFromRecents[record.recharge_number];

                    if (prevValaue) {
                        if (MOMENT(MOMENT(prevValaue.updated_at)).diff(record.updated_at) < 0) {

                            rechargeNumberMapFromRecents[record.recharge_number] = record;
                        }
                    } else {
                        rechargeNumberMapFromRecents[record.recharge_number] = record;
                    }
                }
            }

            return rechargeNumberMapFromRecents;
        } catch (error) {
            this.L.error(`fetchRecordsFromRecents:: error in fetchRecords from table: ${this.mongoCollection}`, error);
            throw error;
        }
    }

    /**
     * Records has come from VIL but does not exist in recents.
     * we marked them as NOT_IN_USE and insert in VIL_SYNC_MAIN table.
     * @param {*} rnAndExpiryDateMap 
     * @param {*} rechargeNumberMapFromRecents 
     */

    async missedRecords(rnAndExpiryDateMap, rechargeNumberMapFromRecents) {
        let missedRecords = [];

        let paidAtOtherPlatform = this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM,
            notInUse = this.config.COMMON.bills_status.NOT_IN_USE;

        for (let rechargeNumber in rnAndExpiryDateMap) {
            if (!rechargeNumberMapFromRecents[rechargeNumber]) {
                this.L.log("missedRecords:: recharge number: ", rechargeNumber, " missed");
                missedRecords.push(rechargeNumber);
                let { recharge_number, expiry_date, amount, circle, is_ul, report_date } = rnAndExpiryDateMap[rechargeNumber];

                await this.runQuery('OPERATOR_SYNC', this.insertQueryForMainSyncTable, [recharge_number, expiry_date, amount, amount, paidAtOtherPlatform, notInUse, circle, circle, is_ul, report_date], this.mysqlSyncTable).
                    catch(err => this.L.critical(`missedRecords:: error in insert missed Records in VilSync Main table`, err));
            }
        }

        let noOfMissedRecords = missedRecords.length;
        if (noOfMissedRecords) {
            let stats = { type: "NOT_FOUND_IN_RECENTS", STATE: "ERROR", count: noOfMissedRecords }
            this.publishStats(stats);
        }
    }

    /**
     * expirydate validation,
     * set active pid of circle and set correct operator name
     * 
     * @param {*} recordFromRecents 
     * @param {*} recordFromSyncDb 
     */
    async setActivePidAndCircle(recordFromRecents, recordFromSyncDb) {
        let isRecordValid = true;

        this.L.verbose("setActivePidAndCircle: recordFromRecents: ", recordFromRecents, " recordFromSyncDb: ", recordFromSyncDb);
        
        let activeProductId = this.activePidLib.getActivePID(recordFromRecents.product_id), currentStatus,
            operator, circle, stats, finalStatus = this.config.COMMON.bills_status.PAYMENT_DONE;

        if (activeProductId) {
            set(recordFromSyncDb, "product_id", activeProductId);
            operator = get(this.cvrDataPidMap, [activeProductId, "operator"], null);
            circle = get(this.cvrDataPidMap, [activeProductId, "circle"], null);

            if (!operator) {
                stats = { type: "PID_NOT_EXITS_IN_CVR", STATE: "ERROR", count: 1 };
                isRecordValid = false;
                finalStatus = this.config.COMMON.bills_status.VALIDATION_FAILED;
            } else {
                set(recordFromRecents, "active_operator", operator.toLowerCase());
                set(recordFromRecents, "active_circle", circle.toLowerCase());
            }
        } else {
            stats = { type: "NO_ACTIVE_PID", STATE: "ERROR", count: 1 };
            isRecordValid = false;
            finalStatus = this.config.COMMON.bills_status.VALIDATION_FAILED;
        }

        if (isRecordValid == false) {
            this.L.log(`setActivePidAndCircle:: record(key=${recordFromRecents.recharge_number}_${recordFromRecents.operator}) is invalid, reason:`, stats, ", product_id of  record: ", recordFromRecents.product_id);

            this.publishStats(stats);

            let { recharge_number, expiry_date, circle, is_ul, report_date, amount } = recordFromSyncDb;

            let paidAtOtherPlatformStatus = this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM;

            currentStatus = currentStatus || paidAtOtherPlatformStatus;  // we always mark these type of record as paid_at_other_platform

            this.runQuery('OPERATOR_SYNC', this.insertQueryForMainSyncTable, [recharge_number, expiry_date, amount, amount, currentStatus, finalStatus, circle, circle, is_ul, report_date], this.mysqlSyncTable).
                catch(err => this.L.critical(`setActivePidAndCircle:: error in insert missed Records in VilSync Main table`, err));
        }

        return isRecordValid;
    }

    /**
     * 
     * @returns {object} validationResult
     * @returns {boolean} validationResult.status  true in case of valid plan or false in case of invalid plan
     * @returns {object} validationResult.data it has bucket info
     */
    async planValidation({ operator, amount, circle, service }) {
        let validationResult = {
            status: true,
            data: {},
        };

        let key = `${operator}_${service}_${circle}_${amount}`;

        try {

            let validatationData = {};

            if (this.validPlan[key] && !this.isExpired(this.validPlan, key)) {
                validatationData = this.validPlan[key];
            } else if (this.inValidPlan[key] && !this.isExpired(this.inValidPlan, key)) {
                validationResult.status = false;
                validatationData = this.inValidPlan[key];
            } else {
                let categoryId = this.config.DIGITAL_CATALOG.DCAT_CATEGORY_MAP[service];

                let { data: planData, status: planApiStatus } = await this.digitalCatalog.getPlanDetail({ categoryId, operator, circle, amount });

                this.L.log("planValidation, planData from digital catalog: ", planData, " planApiStatus", planApiStatus, " for key", key);

                if (planApiStatus) {
                    let planValidity = planData.validity;

                    this.L.verbose("planValidation, planValidity", planValidity, " IN_VALID_VALIDITY: ", this.config.DIGITAL_CATALOG.IN_VALID_VALIDITY);

                    if (!planValidity || this.config.DIGITAL_CATALOG.IN_VALID_VALIDITY.includes(planValidity)) {
                        this.inValidPlan[key] = {
                            plan_bucket: planData.plan_bucket,
                            time: Date.now(),
                        }

                        validatationData = this.inValidPlan[key];
                        validationResult.status = false;
                    } else {
                        this.validPlan[key] = {
                            plan_bucket: planData.plan_bucket,
                            time: Date.now(),
                        }
                        validatationData = this.validPlan[key];
                    }
                } else {
                    validationResult.status = false;
                }
            }

            validationResult.data = validatationData;

        } catch (error) {
            this.L.error(`planValidation, error for key: ${key}, `, error);
            validationResult.status = false;
        }

        return validationResult;
    }

    isExpired(obj, key) {
        let data = obj[key];

        if(!data || !data.time || typeof data.time != 'number') {
            return true;
        }

        if (Date.now() - data.time > this.dcatMapRefreshInterval) { 
            delete obj[key];

            return true;
        }

        return false;
    }

    async filterPaidAtPaytmRecords(records) {
        try {
            let alreadyPaidStatus = this.config.COMMON.bills_status.PAYMENT_DONE,
                invalidStatus = this.config.COMMON.bills_status.VALIDATION_FAILED;

            let planValiditySelectQuery = `select * from plan_validity where recharge_number = ?;`

            let finalQuery = '';
            let amountBasedMap = {}, bucketBasedMap = {};

            for (let row of records) {
                finalQuery += this.dbInstance.format(planValiditySelectQuery, [row.recharge_number + ""]);

                let { recharge_number, plan_bucket, amount } = row;

                if (plan_bucket != amount) {
                    bucketBasedMap[recharge_number + "_" + plan_bucket] = row;
                }

                amountBasedMap[recharge_number + "_" + amount] = row;
            }

            let planValidityResponse = [];

            if (finalQuery.length) {
                planValidityResponse = await this.runQuery('RECHARGE_ANALYTICS_SLAVE', finalQuery, [], 'plan_validity');
            }

            this.L.verbose("filterPaidAtPaytmRecords, planValidityResponse:", planValidityResponse, ", amountBasedMap: ", amountBasedMap, ", bucketBasedMap: ", bucketBasedMap);

            planValidityResponse = flatten(planValidityResponse);

            let deletedSyncDbMap = {};
            finalQuery = '';
            for (let response of planValidityResponse) {
                try {
                    let { recharge_number, amount, operator, service, validity_expiry_date, latest_recharge_date, circle, plan_bucket } = response;
                    let invalidRecord = false;

                    let bucketKey = recharge_number + "_" + plan_bucket;
                    let amountkey = recharge_number + "_" + amount;

                    if (deletedSyncDbMap[bucketKey] || deletedSyncDbMap[amountkey]) {
                        this.L.log("filterPaidAtPaytmRecords:: already deleted: ", JSON.stringify(response), " for bucketKey: ", bucketKey, " amountkey", amountkey);
                        continue;
                    }

                    let syncDbRecord = bucketBasedMap[bucketKey] || amountBasedMap[amountkey];

                    if (!syncDbRecord) {

                        if (MOMENT(validity_expiry_date).diff(this.fromDate, 'day') >= 0 && MOMENT(validity_expiry_date).diff(this.toDate, 'day') < 1) {
                            this.L.log("filterPaidAtPaytmRecords:: PV_RECORD_DOES_NOT_COME_FROM_VI, for exists recharge number and bucket: ", JSON.stringify(response));
                            this.publishStats({ type: "PV_RECORD_DOES_NOT_COME_FROM_VI", count: 1, STATE: "INFO" });
                        }
                        continue;
                    }

                    this.L.log("filterPaidAtPaytmRecords:: response from Plan Validity table: ", JSON.stringify(response));

                    recharge_number = +recharge_number;

                    let { is_ul, expiry_date, report_date, amount: syncDbAmount } = syncDbRecord;

                    if (this.operators.includes(operator.toLowerCase())) {
                        if (this.service.includes(service.toLowerCase())) {
                            let dateDiff = MOMENT(MOMENT(validity_expiry_date).format('YYYY-MM-DD')).diff(expiry_date, 'day');

                            let isRechargeInTheWindow = MOMENT(MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss')).diff(this.windowStartTime) >= 0;

                            let needToPublish = true;

                            if (dateDiff == 0) {
                                needToPublish = false;
                            }

                            if (dateDiff > 0) {
                                needToPublish = false;
                                this.publishStats({ type: "GREATER_EXPIRY_AT_PAYTM", count: 1, STATE: "INFO", DATE_DIFF: dateDiff });
                                this.L.log("filterPaidAtPaytmRecords:: GREATER_EXPIRY_AT_PAYTM, sync Db Record: ", JSON.stringify(syncDbRecord));
                            }

                            if (isRechargeInTheWindow) {
                                needToPublish = false;
                                this.publishStats({ type: "RECHARGE_IN_THE_WINDOW_TIME", count: 1, STATE: "INFO", DATE_DIFF: dateDiff });
                                this.L.log("filterPaidAtPaytmRecords:: RECHARGE_IN_THE_WINDOW_TIME, sync Db Record: ", JSON.stringify(syncDbRecord));
                            }

                            if (needToPublish) {
                                this.L.log("filterPaidAtPaytmRecords:: WRONG_EXPIRY_DATE_AT_PAYTM: sync db record:", JSON.stringify(syncDbRecord), " pv record: ", JSON.stringify(response));

                                this.publishStats({ type: "WRONG_EXPIRY_DATE_AT_PAYTM", count: 1, STATE: "INFO", DATE_DIFF: dateDiff });
                                syncDbRecord.paidAtPaytm = true;
                            } else {
                                finalQuery += this.dbInstance.format(this.insertQueryForMainSyncTable, [recharge_number, expiry_date, syncDbAmount, syncDbRecord.plan_bucket, alreadyPaidStatus, 0, circle, syncDbRecord.circle, is_ul, report_date]);

                                this.L.log("filterPaidAtPaytmRecords:: delete paid at paytm record: ", JSON.stringify(syncDbRecord));

                                this.publishStats({ type: "PAID_AT_PAYTM", count: 1, STATE: "INFO", DATE_DIFF: dateDiff });

                                if (bucketBasedMap[bucketKey]) {
                                    this.manageBucketMap(bucketBasedMap, amountBasedMap, deletedSyncDbMap, bucketKey, recharge_number);
                                }

                                if (amountBasedMap[amountkey]) {
                                    this.manageAmountMap(bucketBasedMap, amountBasedMap, deletedSyncDbMap, amountkey, recharge_number)
                                }
                                
                            }

                        } else {
                            // not valid for current process
                            invalidRecord = true;
                        }
                    } else {
                        //insert query not valid for process
                        invalidRecord = true;
                    }

                    if (invalidRecord) {
                        finalQuery += this.dbInstance.format(this.insertQueryForMainSyncTable, [recharge_number, expiry_date, syncDbAmount, syncDbRecord.plan_bucket, alreadyPaidStatus, invalidStatus, circle, syncDbRecord.circle, is_ul, report_date]);

                        if (amountBasedMap[amountkey]) {
                            this.manageAmountMap(bucketBasedMap, amountBasedMap, deletedSyncDbMap, amountkey, recharge_number)
                        }
                        
                        if (bucketBasedMap[bucketKey]) {
                            this.manageBucketMap(bucketBasedMap, amountBasedMap, deletedSyncDbMap, bucketKey, recharge_number);
                        }

                        this.L.log("filterPaidAtPaytmRecords:: deleted INVALID_RECORD(service or operator level mis-match): ", JSON.stringify(syncDbRecord));

                        this.publishStats({ type: "INVALID_RECORD", count: 1, STATE: "ERROR" });

                    }
                } catch (error) {
                    this.L.error("filterPaidAtPaytmRecords:: ERROR_IN_VALIDATION_WITH_PV ", error, " for pv record: ", JSON.stringify(response));
                    this.publishStats({ type: "ERROR_IN_VALIDATION_WITH_PV", count: 1, STATE: "ERROR" });
                }
            }

            if (finalQuery.length) {
                this.runQuery('OPERATOR_SYNC', finalQuery, [], this.mysqlSyncTable).catch(err => this.L.critical("removeAlreadyProcessedRecords:: error in inserting paidAtPaytm Records in VilSync Main table: ", err, ` we need to run this query: ${finalQuery}`));
            }


            let finalRecords = Object.values(amountBasedMap);

            this.L.verbose("filterPaidAtPaytmRecords, finalRecords", finalRecords, ", amountBasedMap: ", amountBasedMap, ", bucketBasedMap: ", bucketBasedMap);

            return finalRecords;
        } catch (error) {
            this.L.error("error in filterPaidAtPaytmRecords: ", error);
            this.publishStats({ type: "ERROR_IN_VALIDATION_WITH_PV", count: 1, STATE: "ERROR" });
        }
    }

    manageBucketMap(bucketBasedMap, amountBasedMap, deletedSyncDbMap, bucketKey, recharge_number) {
        let syncDbBucketAmount = bucketBasedMap[bucketKey]["amount"];
        delete bucketBasedMap[bucketKey];

        let amountKeyForSyncDB = recharge_number + '_' + syncDbBucketAmount;

        if (amountBasedMap[amountKeyForSyncDB]) {
            delete amountBasedMap[amountKeyForSyncDB];

            deletedSyncDbMap[amountKeyForSyncDB] = true;
        }
        deletedSyncDbMap[bucketKey] = true;
    }

    manageAmountMap(bucketBasedMap, amountBasedMap, deletedSyncDbMap, amountkey, recharge_number) {
        let syncDBAmountBucket = amountBasedMap[amountkey]["plan_bucket"];
        delete amountBasedMap[amountkey];

        let bucketKeyForSyncDB = recharge_number + '_' + syncDBAmountBucket;

        if (bucketBasedMap[bucketKeyForSyncDB]) {
            delete bucketBasedMap[bucketKeyForSyncDB];
            deletedSyncDbMap[bucketKeyForSyncDB] = true;
        }
        deletedSyncDbMap[amountkey] = true;
    }

    /**
     * it does throttling on publisher and called publishedRecords publisher.
     * @param {*} records 
     */
    async _publishedRecords(records) {
        try {
            let sliceStartIndex = 0,
                sliceEndIndex = this.publishingRate,
                recordsLength = records.length;

            do {
                let sleep = (this.getTimeInMs() - this.lastStartPublishingTime);

                if (sleep < this.timeThresholdForKafkaPublisher) {
                    await this.commonLib.calledAfterTime(this.timeThresholdForKafkaPublisher - sleep);// need to correct, use new value and assign 1000
                }

                this.lastStartPublishingTime = this.getTimeInMs();

                let kafkaSlices = records.slice(sliceStartIndex, sliceEndIndex);

                await this.publishedRecords(kafkaSlices);

                sliceStartIndex = sliceEndIndex;
                sliceEndIndex += this.publishingRate;
            } while (sliceStartIndex < recordsLength);

        } catch (error) {
            this.L.error("_publishedRecords:: error: ", error);
            let stats = { type: "ERROR_ON_KAFKA_PUBLISHER_THROTTLING", STATE: "ERROR", count: 1 };
            this.publishStats(stats);
        }
    }

    async publishedRecords(records) {
        try {

            for (let record of records) {
                let { recharge_number, expiry_date, amount, plan_bucket, is_ul, report_date, circle, recentData } = record;
                let kafkaPayload = this.kafkaPayload(record);

                let publishStatus = false,
                    finalStatus = 0;

                this.L.verbose("publsihedRecord:: kafkaPayload: ", kafkaPayload);

                if (kafkaPayload) {
                    publishStatus = await this.publishInKafka(kafkaPayload);

                    if (publishStatus) {
                        finalStatus = 0;

                        if (!this.validAmount.includes(amount.toString())) {
                            let stats = { type: "PUBLISHED_INVALID_AMOUNT_RECORD_ON_BASIS_OF_DCAT", STATE: "INFO", count: 1 };
                            this.publishStats(stats);
                        }

                        if (amount == plan_bucket) {
                            let stats = { type: "PLAN_BUCKET_AS_AN_AMOUNT", STATE: "INFO", count: 1 };
                            this.publishStats(stats);

                            this.L.log("publsihedRecord:: PLAN_BUCKET_AS_AN_AMOUNT, record: ", record);
                        }

                    } else {
                        finalStatus = this.config.COMMON.bills_status.ERROR_WHILE_PUBLISHING;
                    }
                } else {
                    finalStatus = this.config.COMMON.bills_status.VALIDATION_FAILED;
                }

                /**
                 * db insert query
                 */

                let currentStatus = record.paidAtPaytm ? this.config.COMMON.bills_status.PAYMENT_DONE : this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM;

                await this.runQuery('OPERATOR_SYNC', this.insertQueryForMainSyncTable, [recharge_number, expiry_date, amount, plan_bucket, currentStatus, finalStatus, recentData.active_circle, circle, is_ul, report_date], this.mysqlSyncTable).
                    catch(err => this.L.critical(`publishedRecords:: error in insert missed Records in VilSync Main table`, err));

            }

        } catch (error) {
            this.L.error("publishedRecords:: error: ", error);
            let stats = { type: "ERROR_KAFKA_PAYLOAD_CREATION", STATE: "ERROR", count: 1 };
            this.publishStats(stats);
        }
    }
    /**
     * create kafka payload for publish
     * @param {*} record 
     */
    kafkaPayload(record) {
        try {

            let { recharge_number, amount, plan_bucket, recentData, expiry_date, product_id: active_product_id} = record;
            let { product_id, active_operator, operator, service, active_circle, paytype, customer_id, updated_at } = recentData;

            let bill = this.sameAmountBill(record);
            let metaData = {};

            if (bill.plan) {
                metaData.category_name = bill.plan;
            }

            if (plan_bucket != amount) {
                metaData.plan_bucket = plan_bucket;
            }

            let kafkaPayload = {
                metaData: JSON.stringify(metaData),
                expiryDateFromSyncDb: MOMENT(expiry_date).endOf('day').format("YYYY-MM-DD HH:mm:ss"),
                windowTime: this.windowStartTime,
                userData_recharge_number: recharge_number + "",
                catalogProductID: active_product_id,
                userData_amount: amount,
                productInfo_operator: active_operator,
                productInfo_old_operator: operator.toLowerCase(),
                productInfo_circle: active_circle, // use own cirle of recents
                productInfo_service: service.toLowerCase(),
                productInfo_paytype: paytype,
                customerInfo_customer_id: customer_id,
                inStatusMap_transactionStatus: "SUCCESS",
                originalPid: product_id,
                triedPids: [],
                timestamps_init: MOMENT(updated_at).format("YYYY-MM-DD HH:mm:ss"),
                updated_source: "OPERATOR_VI_DATA"
            }

            return kafkaPayload;
        } catch (error) {
            this.L.error("kafkaPayload:: error ", error);

            let stats = { type: "KAFKA_PAYLOAD_CREATION_FAILED", STATE: "ERROR", count: 1 };
            this.publishStats(stats);

            return null;
        }
    }

    /**
     * publish payload in kafka
     * @param {} payload 
     */
    async publishInKafka(payload) {
        try {
            let topic = this.config.KAFKA.SERVICES.PLAN_VALIDITY_SYNC_DB.TOPIC;

            this.L.verbose("publishInKafka: topic", topic, ", payload", payload);

            await this.kafkaPublisher.publishData([{
                topic: topic,
                messages: JSON.stringify(payload)
            }], null, [200, 800]);

            this.L.log('Message published successfully in Kafka', ` on topic ${topic}`, JSON.stringify(payload));

            let stats = { type: "PUBLISHED", STATE: "INFO", count: 1 };
            this.publishStats(stats);

            return true;
        } catch (error) {
            this.L.critical('Error while publishing message in Kafka - MSG:- ' + JSON.stringify(payload), error)

            let stats = { type: "KAFKA_NOT_PUBLISHED", STATE: "ERROR", count: 1 };
            this.publishStats(stats);

            return false;
        }
    }

    getTimeInMs(date) {
        return date ? new Date(date).getTime() : new Date().getTime();
    }

    /**
     * 
     * @param {*} param0 it is a recents Object
     * @param {*} param1 it a syncDb Object
     */

    sameAmountBill(record) {
        let bills = record.recentData;

        let size = bills.length;

        let key = record.recharge_number + "_" + record.plan_bucket;

        this.L.verbose("sameAmountBill: bills", bills, " for key", key);

        for (let i = 0; i < size; i++) {
            if (bills[i].amount == record.amount) {  // previous if (bills[i].amount == amount && recentsCircle == circle)
                return bills[i];
            }
        }

        return {};
    }
    /**
     * run mysql query on VIL_SYNC_TEMP table and return data
     */

    fetchRecordFromTempTable() {

        let query = `select * from ${this.mysqlSyncTemporaryTable} where id > ? limit ?`;
        let params = [this.fromId, this.mysqlDbFetchRecordsLimit];

        return this.runQuery('OPERATOR_SYNC_SLAVE', query, params, this.mysqlSyncTemporaryTable);
    }

    async fetchTotalCount(current_retry = 0) {
        let resp;
        try {
            let query = `select id from ${this.mysqlSyncTemporaryTable} order by id desc limit 1`;
            let params = [];

            let resp = await this.runQuery('OPERATOR_SYNC_SLAVE', query, params, this.mysqlSyncTemporaryTable);

            return Array.isArray(resp) && resp.length && resp[0].id;
        } catch (error) {
            this.L.critical(`fetchTotalCount:: error: `, error);

            if (current_retry < 5) {
                await this.commonLib.calledAfterTime(60000);
                resp = await this.fetchTotalCount(current_retry + 1);
                return resp;
            } else {
                let query = 'update digital_reminder_config set value=? where name = ? and node = ? and key_name = ?';
                let queryParms = [];
                queryParms.push(0, 'PLAN_VALIDTY_NOTIFICATION_CONSUMER', 'VALIDATION_WITH_VIL_SYNC_DB', 'ENABLE');

                await this.digitalReminderConfig.updateDynamicConfig(query, queryParms);
                process.exit(1);
            }
        }
    }

    /**
     * run mongoDb query on recents after specified time, 
     *  retry if query faied after specified time
     */
    async mongoThrottleWapper(queryObj, retryCount = 0) {
        try {
            let sleep = (this.getTimeInMs() - this.lastMongoFetchTime);

            if (sleep < this.timeThresholdForMongo) {
                await this.commonLib.calledAfterTime(this.timeThresholdForMongo - sleep);
            }

            let data = await this.runMongoQuery(queryObj);

            this.L.verbose("mongoThrottleWapper: data from mongo", data);

            this.lastMongoFetchTime = this.getTimeInMs();

            return data;
        } catch (error) {
            this.L.error(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
            this.lastMongoFetchTime = this.getTimeInMs();

            /**
             * you can apply checks on error message, what ever here we are fetching data, so there should be no issue on fetching
             * if there is possible some other issue 
             */

            if (retryCount < this.retryCountForMongo) {
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            } else {
                this.L.critical(`mongoThrottleWapper:: error in fetchRecords from table: ${this.mongoCollection}`, error);
                await this.commonLib.calledAfterTime(this.mongoDbFetchRecordsFailureRetryInterval);
                await this.mongoThrottleWapper(queryObj, retryCount + 1);
            }
        }
    }

    async updateRecordFinalStatusWhichNotCameToday() {

        try {
            if (this.isUpdatationAlowedOnOldRecord) {

                this.L.info("updateRecordFinalStatusWhichNotCameToday: start");
                let ctr = 0;

                let fetchQuery = `select id from vil where id > ? and  expiry_date >= "${this.fromDate}" and updated_at < "${this.today}" and final_status=0 limit ${this.mongoDbFetchRecordsLimit};`

                let updateQuery = `update ${this.mysqlSyncTable} set final_status=14 where  id= ?;`;

                this.L.info("updateRecordFinalStatusWhichNotCameToday: counter: ", ctr);

                let query, data;
                let lastId = 0;
                do {
                    query = this.dbInstance.format(fetchQuery, [lastId])

                    data = await this.runQuery('OPERATOR_SYNC', query, [], this.mysqlSyncTable);

                    this.L.verbose("updateRecordFinalStatusWhichNotCameToday:: db response", data);

                    query = '';
                    for (let row of data) {
                        query += this.dbInstance.format(updateQuery, [row.id])
                    }

                    if (query.length) {
                        await this.runQuery('OPERATOR_SYNC', query, [], this.mysqlSyncTable);
                        await this.commonLib.calledAfterTime(this.updatationOldRecordSleepTime);
                    }

                    ctr++;
                    this.L.info("updateRecordFinalStatusWhichNotCameToday: counter: ", ctr);

                    this.publishStats({ type: "UPDATE_FINAL_STATUS_DONE_FOR_ROWS", count: this.mongoDbFetchRecordsLimit, STATE: "INFO" });

                    if (data.length) lastId = data[data.length - 1].id;

                } while (data.length == this.mongoDbFetchRecordsLimit);

                this.L.info("updateRecordFinalStatusWhichNotCameToday: Completed!");

            } else {
                this.L.info("updateRecordFinalStatusWhichNotCameToday: today updation not allowed!!")
            }

            this.updateRecordFinalStatusFlag = "COMPLETED";

        } catch (error) {
            this.L.critical("updateRecordFinalStatusWhichNotCameToday: error:", error);
        }
    }

    runMongoQuery(queryObj) {
        return new Promise((resolve, reject) => {

            this.L.verbose("runMongoQuery: ", queryObj);

            this.mongoDbInstance.fetchDataFromCollection((err, results) => {
                if (err) {
                    let stats = { type: "MONGO_QUERY_FAILED", STATE: "ERROR", count: 1 }
                    this.publishStats(stats);
                    reject(err);
                } else {
                    resolve(results);
                }
            }, this.mongoCollection, queryObj);
        });
    }

    /**
     * run mysql query
     * @param {*} poolName 
     * @param {*} query 
     * @param {*} params 
     * @param {*} table 
     */
    runQuery(poolName, query, params, table = '') {
        return new Promise((resolve, reject) => {
            this.dbInstance.exec((error, results) => {

                if (error) {
                    let stats = {
                        type: "MYSQL_QUERY_FAILED" + (table ? "_" + table : ""),
                        STATE: "ERROR",
                        count: 1
                    };

                    if (query.indexOf('insert into') < 0 || query.indexOf('update ') < 0) {
                        this.publishStats(stats);
                    }

                    reject(error);
                } else {
                    resolve(results);
                }
            }, poolName, query, params);
        });
    }

    publishStats({ type, count, STATE, size, AMOUNT, DATE_DIFF }) {
        let stats = {
            REQUEST_TYPE: "PV_SYNC_DB_PUBLISHER",
            TYPE: type,
            STATE: STATE
        };

        if (AMOUNT == 0 || AMOUNT) {
            stats.AMOUNT = AMOUNT
        }

        if (DATE_DIFF == 0 || DATE_DIFF) {
            stats.DATE_DIFF = DATE_DIFF;
        }

        if (size == 0 || size) {
            stats.SIZE = size
        }

        PublishStats.publishCounter(count, stats);
    }
}

export default PlanValiditySyncDbPublisher;

(function () {
    if (require.main === module) {
        var commander = require('commander');
        console.time('Execution Time');
        commander
            .version('0.0.1')
            .option('-v, --verbose', 'Run in verbose mode')
            .option('-g, --group <value>', 'group', String)
            .option('-i, --id <value>', 'group', String)
            .parse(process.argv);

        if (commander.verbose) {
            L.setLevel('verbose');
        }

        startup.init({
            vil : true,
            exclude: {
                cvr: true,
                ruleEngine: true,
                activePidLib: false,
                mongoDb: true,
                dynamicConfig: false,
                rechargeSagaCassandraDb : false,
            }
        }, function (err, options) {
            try {
                // console.log(options);
                if (err) {
                    L.critical(err);
                    process.exit(1);
                }
                options.group = commander.group;
                options.id = +commander.id;

                if (MOMENT().weekday() == 0) {
                    L.error("Today is sunday and on sunday there is no data in the VI sync db. So We have stopped our publisher on sunday!!");
                    process.exit(0);
                }

                let serviceInstance = new PlanValiditySyncDbPublisher(options);

                serviceInstance.start().catch(err => {
                    L.critical("Sync Db Plan Validity Publisher has crashed. Please check this on proiority.", err);
                    process.exit(1);
                });

            } catch (error) {

                L.critical("Sync Db Plan Validity Publisher has crashed on the startup!! Please check this on proiority.", error);
                process.exit(1);
            }

        });
    }
})();


/*
node dist/services/planValiditySyncDbPublisher.js -g vil
*/