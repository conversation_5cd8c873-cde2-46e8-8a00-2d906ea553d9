"use strict";

import OS from 'os'
import _ from 'lodash'
import utility from '../lib'
import ASYNC from 'async'
import MOMENT from 'moment'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import KafkaConsumer from '../lib/KafkaConsumer';
import CassandraBills from '../models/cassandraBills';
import Q from 'q';

/**
 * This service check if a user is active or not by querying active_paytm_users table
 * If active, it publishes the payload to NON_PAYTM_RECORDS (nonPaytmBillsConsumer)
 */
class CheckActiveUsersConsumer {
    /**
     * @param { object } options Contains configuration and dependencies
     * @param { object } options.L Paytm Logger (lgr) object
     * @param { object } options.config Local config object
     * @param { object } options.INFRAUTILS Contains util libraries like kafka
     */

    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.infraUtils = options.INFRAUTILS;
        self.cassandraBills = new CassandraBills(options);
        self.greyScaleEnv = options.greyScaleEnv;
        self.kafkaBatchSize = self.greyScaleEnv ? 2 : _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'CHECK_ACTIVE_USERS_CONSUMER', 'KAFKA_BATCHSIZE'], 500);
        self.kafkaResumeTimeout = self.greyScaleEnv ? 30*1000 : 500;
        self.kafkaConsumerChecks = new KafkaConsumerChecks(options);
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);    
    }

    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: checkActiveUsersConsumer", "Re-initializing variable after interval");
        self.kafkaBatchSize = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'RECENTS', 'BATCHSIZE'], 2) : _.get(self.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'CHECK_ACTIVE_USERS_CONSUMER', 'KAFKA_BATCHSIZE'], 500);
        self.kafkaResumeTimeout = self.greyScaleEnv ? _.get(self.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'NON_PAYTM_BILLS', 'BATCH_DELAY'], 30*1000) : 500;
    }

    start() {
        let self = this;

        self.L.log("Start Configuration: checkActiveUsersConsumer is starting........");

        ASYNC.waterfall([
            next => {  
                self._initializeNonPaytmPublisher(next);
            },
            next => {
                self._initializeCheckActiveUsersConsumer(next);
            }
        ],(error) => {
            if (error) {
                self.L.critical('checkActiveUsersConsumer :: Error while starting service...', error);
                process.exit(0)
            } else {
                self.L.log('checkActiveUsersConsumer :: Service started....');
            }
        });
        
    }

    _initializeNonPaytmPublisher(cb){
        let self = this;
        try {
            self.nonPaytmKafkaPublisher = new self.infraUtils.kafka.producer({
                "kafkaHost": this.config.KAFKA.TOPICS.NON_PAYTM_RECORDS.HOSTS
            });
            self.nonPaytmKafkaPublisher.initProducer('high', function (error) {
                if (error) {
                    self.L.critical("_initializeNonPaytmPublisher : NonPaytmRecords Publisher cannot start, error: ", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:KAFKA_PUBLISHER', 'TOPIC:NON_PAYTM_RECORDS_DWH']);
                }
                else if (!error)
                    self.L.log("_initializeNonPaytmPublisher : NonPaytmRecords Publisher Configured...");
                return cb(error);
            });
        } catch (error) {
            return cb(error);
        }
    }

    _initializeCheckActiveUsersConsumer(cb) {
        let self = this;
        try {
            self.consumer = new KafkaConsumer({
                "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.CHECK_ACTIVE_USERS.HOSTS'), 
                "groupId": 'check-active-users-consumer',
                "topics": _.get(self.config.KAFKA, 'SERVICES.CHECK_ACTIVE_USERS.TOPIC'),
                "id": "check-active-users-consumer-" + OS.hostname(),
                sessionTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.SESSION_TIMEOUT',2*60*1000),
                maxProcessTimeout:_.get(self.config,'DYNAMIC_CONFIG.KAFKA_CONFIG.MAX_PROCESS_TIMEOUT',3*60*1000)
            });

            self.consumer.initConsumer(self._processKafkaData.bind(self), (error) => {
                if (error){
                    self.L.critical("_initializeCheckActiveUsersConsumer : CheckActiveUsers Consumer cannot start, error: ", error);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:KAFKA_CONSUMER', 'TOPIC:CHECK_ACTIVE_USERS_TOPIC']);
                }
                else if (!error)
                    self.L.log("_initializeActivePaytmUsersConsumer : CheckActiveUsers Consumer Configured...");
                return cb(error);
            });
        } catch (error) {
            return cb(error);
        }
    }

    _processKafkaData(records, resolveOffset, topic ,partition ,cb) {
        let self = this,
            lastMessage,
            recordsToProcess = [];

        if (records && _.isArray(records)) {
            lastMessage = records[records.length -1];
            self.L.log('checkActiveUsersConsumer::_processKafkaData received data from kafka ', records.length);
        } else {
            self.L.critical('checkActiveUsersConsumer::_processKafkaData error while reading kafka');
            return cb();
        }

        records.forEach(function (data) {
            if (data && data.value) {
                try {             
                    let payload = JSON.parse(data.value);
                    recordsToProcess.push(payload);
                } catch (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:INVALID_JSON_PAYLOAD']);
                    self.L.error("_processKafkaData", "Failed to parse recharges data topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp + ", " + error);
                }
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:INVALID_PAYLOAD']);
                self.L.error("_processKafkaData", "Unable to get valid data from kafka topic,partition,offset,timestamp ::", data.topic, data.partition, data.offset, data.timestamp);
            }
        });

        self.L.log('checkActiveUsersConsumer::_processKafkaData', `Processing ${recordsToProcess.length} out of ${records.length} Recharge data !!`);

        ASYNC.eachLimit(recordsToProcess, 1, self._processBillsData.bind(self), async (err) => {

            self.kafkaConsumerChecks.findOffsetDuplicates("CheckActiveUsers", records, topic , partition);

            if(err) {
                self.L.error("checkActiveUsersConsumer::_prepareDataToInsert Error: ", err );
                setTimeout(() => {
                    // Resume consumer now
                    return cb(err);
                }, self.kafkaResumeTimeout);
            }else{

                await resolveOffset(lastMessage.offset)
                self.L.log('_processKafkaData::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + topic + ', partition :' + partition + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                setTimeout(() => {
                    // Resume consumer now
                    return cb();
                }, self.kafkaResumeTimeout);
                
            }
        });   
    }

    _processBillsData(billsKafkaRow, done) {
        let self = this,
            customerId = billsKafkaRow.customerId,
            service = billsKafkaRow.service;

        ASYNC.waterfall([
            //fetch payment date list and latest payment date from active_paytm_users_new table against customerId and mapped service
            next => self._fetchUserPaymentDates(customerId, service, next),

            //check if user is active
            (resultRow, mapped_service, next) => self._checkActiveUser(resultRow, service, mapped_service, next),

            //Publish to Non ru pipeline if user is active
            (isActive, latest_payment_date, service_payment_date_list, next) => {
                if (!isActive) {
                    return next(new Error('Inactive user...'));
                }
                self._publishToNonRuPipeline(billsKafkaRow, latest_payment_date, service_payment_date_list, next);
            }
        ], (err) => {
            if (err) {
                self.L.error("checkActiveUsersConsumer::_processBillsData", "Publishing to Non Ru pipeline failed for customerId: ", customerId, " service: ", service, " error: ", err.message);
            }
            done();
        });
    }

    _fetchUserPaymentDates(customerId, service, callback) {
        let self = this;
        let mapped_service = _.get(this.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_TO_SERVICE_MAPPING', service], null);

        if (!mapped_service) {
            self.L.error("checkActiveUsersConsumer::_fetchUserPaymentDates", "No service mapping found for service:", service);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:NO_SERVICE_MAPPING_FOUND']);
            return callback(new Error(`No service mapping found for service: ${service}`));
        }
           
        self.cassandraBills.getPaymentDatesFromActivePaytmUsers(customerId, mapped_service, (err, result) => {
            if (err) {
                self.L.error("checkActiveUsersConsumer::_fetchUserPaymentDates", "DB exception for customer_id: " + customerId,", mapped_service: " + mapped_service);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:DB_QUERY_ERROR']);
                return callback(new Error('DB exception: ' + err));
            }
    
            if (result.rows.length === 0) {
                self.L.warn("checkActiveUsersConsumer::_fetchUserPaymentDates", `No rows found for the given customer_id: ${customerId} and mapped_service: ${mapped_service}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:NO_ROWS_FOUND']);
                return callback(new Error('No rows found for the given customer_id and mapped service: ' + mapped_service));
            }
    
            callback(null, result.rows, mapped_service);
        });
    }

    _checkActiveUser(resultRow, service, mapped_service, callback) {
        let self = this;
        let is_active = null;
        let row = resultRow[0];
        let latest_payment_date = _.get(row, 'latest_payment_date', null);
        let mapped_service_payment_date_list = _.get(row, 'payment_date_list', []);
        let service_payment_date_list = {};
        
        try {
            let service_wise_threshold_days = _.get(this.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_THRESHOLD_DAYS', service], null);
            let service_wise_threshold_transactions = _.get(this.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'SERVICE_WISE_THRESHOLD_TRANSACTIONS', service], null);
            let common_threshold_days = _.get(this.config, ['DYNAMIC_CONFIG', 'SMART_FETCH_CONFIG', 'ACTIVE_PAYTM_USER', 'COMMON_THRESHOLD_DAYS'], null);

            if(!service_wise_threshold_days || !service_wise_threshold_transactions || !common_threshold_days){
                self.L.error("checkActiveUsersConsumer::_checkIsActive", "No threshold days or transactions config found for service: ", service);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ERROR', 'TYPE:NO_THRESHOLD_DAYS_OR_TRANSACTIONS_CONFIG']);
                return callback(new Error(`No threshold days or transactions config found for service: ${service}`));
            }

            // Filter to keep only recent transactions
            let recent_transactions = mapped_service_payment_date_list.filter(date => {
                return MOMENT(date).isAfter(MOMENT().subtract(service_wise_threshold_days, 'days'));
            });

            //Condition 1: Check if user has made at least 2 transactions (service_wise_threshold_transactions) in the last 60 days(service_wise_threshold_days) within the mapped service
            is_active = recent_transactions.length >= service_wise_threshold_transactions;

            //Condition 2: Check if user has made a transaction in the last 30 days across all services (common_threshold_days)
            is_active = is_active && MOMENT(latest_payment_date).isAfter(MOMENT().subtract(common_threshold_days, 'days'));

            if (!is_active) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:INACTIVE', 'TYPE:IS_ACTIVE_USER']);
                self.L.info("checkActiveUsersConsumer::_checkIsActive", "User is inactive...");
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", 'STATUS:ACTIVE', 'TYPE:IS_ACTIVE_USER']);
                self.L.info("checkActiveUsersConsumer::_checkIsActive", "User is active...");

                recent_transactions = recent_transactions
                    .sort((a, b) => MOMENT(a).valueOf() - MOMENT(b).valueOf()) // Sort ascending (oldest first)
                    .slice(-service_wise_threshold_transactions) // Keep only the required number of transactions

                service_payment_date_list = {[mapped_service]: recent_transactions};
            }

            callback(null, is_active, latest_payment_date, service_payment_date_list);
        } catch (error) {
            self.L.error("checkActiveUsersConsumer::_checkIsActive", "Failed to process payment dates", error);
            return callback(new Error(`Failed to process payment dates: ${error.message}`));
        }
    }
    
    _publishToNonRuPipeline(billsKafkaRow, latest_payment_date, service_payment_date_list, callback) {
        let self = this;
        
        billsKafkaRow.is_active_expired_user = true;
        billsKafkaRow.latest_payment_date = latest_payment_date;
        billsKafkaRow.service_payment_date_list = service_payment_date_list;
    
        self.nonPaytmKafkaPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.NON_PAYTM_RECORDS_DWH.TOPIC', ''),
            messages: JSON.stringify(billsKafkaRow)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", "STATUS:ERROR", "TYPE:NON_PAYTM_EVENTS", "TOPIC:NON_PAYTM_RECORDS_DWH"]);
                self.L.critical('checkActiveUsersConsumer :: nonPaytmKafkaPublisher', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(billsKafkaRow), error);
                return callback(new Error('Error while publishing message in Kafka: ' + error));
            }
            
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:CHECK_ACTIVE_USERS", "STATUS:SUCCESS", "TYPE:NON_PAYTM_EVENTS", "TOPIC:NON_PAYTM_RECORDS_DWH"]);
            self.L.log('checkActiveUsersConsumer :: nonPaytmKafkaPublisher', 'Message published successfully in Kafka', ' on topic NON_PAYTM_RECORDS_DWH', JSON.stringify(billsKafkaRow));
            callback(null);
        });
    }

    suspendOperations() {
        let self = this,
            deferred = Q.defer();
        
        self.L.log("checkActiveUsersConsumer::suspendOperations", "kafka consumer shutdown initiated...");

        Q()
        .then(function(){
            self.consumer.close(function(error, res){
                if(error){
                    self.L.error(`checkActiveUsersConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`checkActiveUsersConsumer::stopConsumer :: Consumer Stopped! Response : ${res}`);
            })
        })
        .then(function() {
            self.L.log(`checkActiveUsersConsumer::suspendOperations kafka consumer shutdown successful...`);
            deferred.resolve();
        })
        .catch(function(err) {
            self.L.error(`checkActiveUsersConsumer::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });

        return deferred.promise;
    }
}
export default CheckActiveUsersConsumer;
