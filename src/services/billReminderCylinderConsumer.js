import _ from 'lodash'
import utility from '../lib'
import VA<PERSON><PERSON><PERSON><PERSON> from 'validator'
import <PERSON>Y<PERSON> from 'async'
import OS from 'os'
import MOMENT from 'moment'
import BILLS from '../models/bills'
import digitalUtility from 'digital-in-util'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';
import Q from 'q'


class BillReminderCylinderConsumer {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.bills = new BILLS(options);
        this.activePidLib = options.activePidLib;
        this.infraUtils = options.INFRAUTILS;   
        this.tableName = 'bills_cylinder';  
        this.commonLib = new utility.commonLib(options);  
        this.reminderUtils = new digitalUtility.ReminderUtils();               
        this.kafkaBatchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'REMINDER_CYLINDER', 'BATCHSIZE'], 2) : 500; 
        this.kafkaBatchDelay = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'REMINDER_CYLINDER', 'DELAY'], 5 * 60 * 1000) : 0;
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);
    }

    start() {
        let self = this;

        self.configureKafka(function (error) {
            if (error) {
                self.L.critical('start :: billReminderCylinderConsumer', 'unable to configure kafka', error);
                process.exit(0);
            } else {
                self.L.log('start :: billReminderCylinderConsumer', 'Kafka Confugured successfully !!');
            }
        });
    }
    configureKafka(done) {
        /**
         * maintain this sequence
         * 1) Initialize all publisher
         * 2) Initialize all consumers
         */
        let self = this;
        ASYNC.waterfall([
            next => {
                /**
                 * Kafka publisher to publish events to CT publisher pipeline 
                 */
                 self.ctKafkaPublisher = new self.infraUtils.kafka.producer({
                    "kafkaHost": this.config.KAFKA.TOPICS.CT_EVENTS_PUBLISHER.HOSTS
                });
                this.ctKafkaPublisher.initProducer('high', function (error) {
                    return next(error)
                });
            },
            next => {
                self.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : REMINDER_CYLINDER');
                // Initialize consumer of topic REMINDER_Cylinder
                self.kafkaConsumer = new self.infraUtils.kafka.consumer({
                    "kafkaHost": _.get(self.config.KAFKA, 'TOPICS.REMINDER_CYLINDER.HOSTS'),
                    "groupId": "remiderCylinder-consumer",
                    "topics": _.get(self.config.KAFKA, 'SERVICES.REMINDER_CYLINDER.TOPIC'),
                    "id": 'remiderCylinderConsumer_' + OS.hostname() + '_' + process.pid,
                    "fromOffset": "latest",
                    "autoCommit": false,
                    "batchSize": self.kafkaBatchSize                
                });
    
                self.kafkaConsumer.initConsumer(self.execSteps.bind(self), (error) => {
                    if (error) {
                        self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                    }
                    self.L.log("configureKafka", "consumer of topic : REMINDER_CYLINDER Configured");
                    return done(error);
                });
            }

        ], function (error) {
            if (error) {
                self.L.critical('configureKafka', 'Could not initialize Kafka', error);
                return done(error)
            }
            return done(null);
        });
    }


    execSteps(records) {
        let self = this,
            chunkSize = 50,
            currentPointer = 0, lastMessage;   
            
            let startTime = new Date().getTime();

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaConsumer._pauseConsumer();            
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }
        self.L.log('execSteps:: ', `Processing ${records.length} Bill Fetch data !!`);
        utility._sendMetricsToDD(records.length, ['REQUEST_TYPE:REMINDER_CYLINDER_TRAFFIC']);
        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;
                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 200);
                });
            },
            (err) => {
                self.kafkaConsumerChecks.findOffsetDuplicates("BillReminderCylinderConsumer", records);

                self.kafkaConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now

                    let endTime = new Date().getTime();
                    let executionTime = (endTime - startTime) / 1000;      //in seconds
                    executionTime = Math.round(executionTime);
                    self.L.log('execSteps::', 'per chunkSize record Execution time :', executionTime, 'seconds');
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:CONSUMER_STATUS", 'STATUS:SUCCESS', "SOURCE:BILL_REMINDER_CYLINDER_CONSUMER", "TIME_TAKEN:" + executionTime]);

                    setTimeout(function () {
                        self.kafkaConsumer._resumeConsumer();
                    }, self.kafkaBatchDelay);
                });
            }          
        );
    }

    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            (record, next) => {
                self.storeBillReminderCylinderConsumer(() => {
                    next();
                }, record);                                           
            },
            err => {
                done();
            }
        )
    }

    storeBillReminderCylinderConsumer(done, data) {
        let self = this,
            tableName = self.tableName;
            
        try {
            const record = self.validatePayload(data);
            if (!record) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:STORE_RMINDER_Cylinder_CONSUMER', 'STATUS:SKIP']);
                return done();
            }
            self.L.log('storeBillReminderCylinderConsumer :: billReminderCylinderConsumer',record.debugKey);
            ASYNC.series([
                function (cb) {
                    self.bills.createBill(function (error, data) {                        
                        cb(error, data);
                    }, tableName, record, false,'billCylinderService');
                },
                function (cb) {                                    
                    self.bills.updateBillsForSameRechargeNum(function (updatedError, updatedData) {                        
                        cb(updatedError, updatedData);
                    }, tableName, record);                    
                },
                function (cb) {                                    
                    self.publishCtEvents(function (error) {
                        if(error){
                            cb(error);
                        } else {
                            cb(null)
                        }
                    }, record);                    
                },
            ], function (error) {
                if (error) {
                    self.L.critical('storeBillReminderCylinderConsumer :: billReminderCylinderConsumer', `Error in create api :: ${error} ` );                    
                }
                return done();
            });

        } catch (error) {
            self.L.error('billReminderCylinderConsumer ::', error.message);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:BILL_REMINDER_CYLINDER_CONSUMER', 'STATUS:ERROR']);
            return done();
        }
    }


    validatePayload(kafkaPayload) {
        let self = this;
        try {
            const record = self.convertKafkaPayloadToRecord(kafkaPayload);
            return record;
        } catch(error) {
            self.L.error('validatePayload :: billReminderCylinderConsumer', error.message);
            throw new Error(error);
        }
    }
/** payload  
  {"mobile_number":"6261534477","lpg_id":"7000000022649288","order_id":"2-001684459820","amount":"926","booking_date":"11/30/2021 14:13:53","additional_1":null,"additional_2":null,"product_id":322951957,"customer_id":1206163060,"auth_response":"{\"basicInfo\":{\"countryCode\":\"91\",\"displayName\":\"\",\"firstName\":\"\",\"lastName\":\"\",\"phone\":\"6261534477\"},\"userId\":1206163060}"}     
**/

    convertKafkaPayloadToRecord(kafkaPayload) {
        let self = this,
            dateFormat = 'YYYY-MM-DD HH:mm:ss',
            data,processedRecord;        
        try {            
            data = JSON.parse(_.get(kafkaPayload, 'value', null));
            if (!data) {
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_CYLINDER_RECORD_VALIDATION', 'STATUS:ERROR' , 'TYPE:NO_DATA']);
                throw Error('No data found in the payload');
            }
            data.auth_response=JSON.parse(data.auth_response);
            _.unset(data, 'auth_response.basicInfo.displayName');

            data.customerOtherInfo = JSON.stringify(_.clone(data));            
            let service = _.get(data,'service',_.get(this.config,['CVR_DATA',data.product_id,'service']),null),
                paytype = _.get(data,'paytype',_.get(this.config,['CVR_DATA',data.product_id,'paytype']),null),
                operator = _.get(data,'operator',_.get(this.config,['CVR_DATA',data.product_id,'operator']),null),
                circle = _.get(data,'circle',_.get(this.config,['CVR_DATA',data.product_id,'circle']),null);                
            data.service = _.toLower(service);
            data.paytype = _.toLower(paytype);
            data.operator = _.toLower(operator);
            data.circle = _.toLower(circle);
            data.email = _.toLower(_.get(data,'auth_response.basicInfo.email', null));                          
            

            processedRecord = {
                customerId: _.get(data, 'customer_id', null),
                rechargeNumber: _.get(data, 'lpg_id', null),
                productId: _.get(data, 'product_id', null),
                operator: _.get(data, 'operator', null),
                amount: self.parseAmount(_.get(data, 'amount', 2)),
                dueDate: MOMENT().endOf('day').format(dateFormat),
                billDate: MOMENT().format(dateFormat),
                bill_fetch_date: MOMENT().format(dateFormat),
                billFetchDate: MOMENT().format(dateFormat),
                nextBillFetchDate: null,
                gateway: _.get(data, 'gateway', null),
                paytype: _.get(data, 'paytype', null),
                service: _.get(data, 'service', null),
                circle: _.get(data, 'circle', null),
                customerMobile: _.get(data, 'mobile_number', null),
                customerEmail: _.get(data, 'email', null),
                paymentChannel: _.get(data, 'payment_channel', null),            
                retryCount: 0,
                status: _.get(data, 'COMMON.bills_status.BILL_FETCHED', 4),
                reason: '',
                extra: '',
                published_date: MOMENT().format(dateFormat),
                publishedDate: MOMENT().format(dateFormat),
                user_data: _.get(data, 'user_data', null),
                userData: _.get(data, 'user_data', null),
                notification_status: 1,
                notificationStatus: 1,
                paymentDate: null,
                service_id: _.get(data, 'service_id', 0),
                serviceId: _.get(data, 'service_id', 0),
                customerOtherInfo : _.get(data, 'customerOtherInfo', ''),                            
                is_automatic: _.get(data, 'is_automatic', 0),   
                isAutomatic: _.get(data, 'is_automatic', 0),// need it for sending data in CT pipeline                         
            };            

            let mandatoryParams = ['customerId','rechargeNumber','productId'];

            let invalidParams = [];
            mandatoryParams.forEach(function (key) {
                if (!processedRecord[key]) {
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_CYLINDER_RECORD_VALIDATION', 'STATUS:ERROR', 'TYPE:MANDATORY_PARAM_MISSING', `MISSING_PARAM:${key}`]);
                    invalidParams.push(key);
                }
            });
            // checking amount exists and has value > 0
            if(typeof processedRecord['amount'] != 'number' || processedRecord['amount'] <= 0) invalidParams.push('amount');

            _.set(processedRecord, 'billCylinderService',true); 
            processedRecord.debugKey = `customerId:${processedRecord.customerId}_rechargeNumber:${processedRecord.rechargeNumber}_amount:${processedRecord.amount}_customerEmail:${processedRecord.customerEmail}_customerMobile:${processedRecord.customerMobile}`;

            if (invalidParams.length > 0) throw Error(`Mandatory Params ${invalidParams} is Missing / Invalid`);

            utility._sendMetricsToDD(1, ['REQUEST_TYPE:REMINDER_CYLINDER_RECORD_VALIDATION', 'STATUS:SUCCESS', 'TYPE:MAIN_FLOW_EXECUTION']);
            return processedRecord;                       
        } catch (error) {
            self.L.error('convertKafkaPayloadToRecord :: billReminderCylinderConsumer', error.message, ': Record :: ', JSON.stringify(kafkaPayload));
            throw new Error(error);
        }             
    }
    /**
     * 
     * @param {*} amountStr 
     */
    parseAmount(amountStr) { 
    if (amountStr <= 0 ) return 2;
    if (!amountStr) return 2;
    if (typeof amountStr === 'number') return amountStr;
    if (typeof amountStr === 'string' && !isNaN(amountStr) && amountStr !== ' ') return parseFloat(VALIDATOR.toFloat(amountStr).toFixed(2));
    
    let foundMatch = amountStr.match(new RegExp(/Rs[\s.]*([-+]?\d*(?:\.\d*)?)/));
    let parsedAmount = (foundMatch && foundMatch[1]) ? VALIDATOR.toFloat(foundMatch[1]) : null;
    return parsedAmount;
}

publishCtEvents(done, dbRecordResp) {
    let self = this;

    const customerId = _.get(dbRecordResp, 'customerId', '');
    const operator = _.get(dbRecordResp, 'operator', '');
    const rechargeNumber = _.get(dbRecordResp, 'rechargeNumber', '');
    const eventName = _.get(self.config, ['DYNAMIC_CONFIG', 'CT_CONFIG', 'IVR_CT_EVENTS', 'BILLGEN'], 'cylinderbookingIVRS')
    const dbDebugKey = `rech:${rechargeNumber}::cust:${customerId}::op:${operator}`;

    let productId = _.get(dbRecordResp, 'productId', '');
    productId = self.activePidLib.getActivePID(productId);

    if(self.commonLib.isCTEventBlocked(eventName)){
        self.L.info(`Blocking CT event ${eventName}`)
        return done()
    }
    
    ASYNC.waterfall([
        next => {
            self.commonLib.getRetailerData((error) => {
                if(error) {
                    return next(error)
                } else {
                    return next(null)
                }
            }, customerId, dbRecordResp);
        },
        next => {
            self.commonLib.getCvrData((error) => {
                if(error) {
                    return next(error)
                } else {
                    return next(null)
                }
            }, productId, dbRecordResp);
        },
        next => {
            let mappedData = self.reminderUtils.createCTPipelinePayload(dbRecordResp, eventName, dbDebugKey);
            let clonedData = _.cloneDeep(mappedData); // needed for correct async logging
            self.ctKafkaPublisher.publishData([{
                topic: _.get(self.config.KAFKA, 'SERVICES.CT_EVENTS_PUBLISHER.TOPIC', ''),
                messages: JSON.stringify(mappedData)
            }], (error) => {
                if (error) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING", 'STATUS:ERROR', "TYPE:CT_EVENTS", "OPERATOR:" + operator]);
                    self.L.critical('publishInKafka :: publishCtEvents', 'Error while publishing message in Kafka - MSG:- ' + JSON.stringify(clonedData), error);
                } else {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:SMS_PARSING", 'STATUS:PUBLISHED', "TYPE:CT_EVENTS", "OPERATOR:" + operator,`EVENT_NAME:${eventName}`]);
                    self.L.log('prepareKafkaResponse :: publishCtEvents', 'Message published successfully in Kafka', ' on topic REMINDER_CT_EVENTS', JSON.stringify(clonedData));
                }
                return next(error);
            }, [200, 800]);
        }
    ], error => {
        if(error) {
            self.L.error('publishCtEvents',`Exception occured Error Msg:: ${error} for record::${JSON.stringify(dbRecordResp)} debugKey::`, dbDebugKey);
        } else {
            self.L.log(`publishCtEvents`,`Record processed having debug key`, dbDebugKey);
        }
        return done(error);
    })
}

suspendOperations(){
    var self = this,
    deferred = Q.defer();
    self.L.log(`billReminderCylinderConsumer::suspendOperations kafka consumer shutdown initiated`);

    Q()
    .then(function(){
        self.kafkaConsumer.close(function(error, res){
            if(error){
                self.L.error(`billReminderCylinderConsumer::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                return error;
            }
            self.L.info(`billReminderCylinderConsumer::stopConsumer :: Consumer Stopped!  Response : ${res}`);
        })
    })
    .then(function(){
        self.L.log(`billReminderCylinderConsumer::suspendOperations kafka consumer shutdown successful`);
        deferred.resolve();
    })
    .catch(function(err){
        self.L.error(`billReminderCylinderConsumer::suspendOperations error in shutting kafka consumer`, err);
        deferred.reject(err);
    });
    return deferred.promise;
}
}



export default BillReminderCylinderConsumer;