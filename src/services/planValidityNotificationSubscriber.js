/*jshint esversion: 8 */
"use strict";

import ASYNC from 'async'
import MOMENT from 'moment'
import OS from 'os'
import { promisify } from 'util'
import _ from 'lodash'
import SCHEDULE from 'node-schedule'

import CATALOGVERTICALRECHARGE from '../models/catalogVerticalRecharge'
import PlanValidity from '../models/planValidity'

import NotificationLibrary from '../lib/notification'
import OperatorValidation from "../lib/operatorValidation"
import utility from '../lib'
import PublishStats from '../lib/publishStats'

import NOTIFIER from './notify'
import cassandraBills from '../models/cassandraBills'
import billsLib from '../lib/bills'
import KafkaConsumerChecks from '../lib/kafkaConsumerChecks';

import Q from 'q'

class PlanValidityNotificationSubscriber {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.infraUtils = options.INFRAUTILS;
        this.notify = new NOTIFIER(options);

        this.catalogVerticalRecharge = new CATALOGVERTICALRECHARGE(options);
        this.activePidLib = options.activePidLib;
        this.cvrDataPidMap = {};

        this.planValidity = new PlanValidity(options);

        this.notificationLibrary = new NotificationLibrary(options);
        this.notificationConfig = _.get(this.config, 'NOTIFICATION');

        this.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');

        this.tinyUrlObject = new utility.TinyUrl();

        this.consumerGroupId = options.consumerGroupId;
        this.consumerGroup = _.get(this.config.PLAN_VALIDITY_NOTIFICATION, ["CONSUMER_GROUP", options.consumerGroupId], null);

        this.cvrData = {};

        this.operatorValidation = new OperatorValidation({
            options: options,
            group: this.consumerGroup,
            type: "PLAN_VALIDITY_NOTIFICATION"
        });
        this.requestType = "PLAN_VALIDITY_NOTIFICATION_CONSUMER_" + this.consumerGroup.toUpperCase();
        this.commonLib = new utility.commonLib(options);
        this.cassandraBills = new cassandraBills(options);
        this.billsLib = new billsLib(options);
        this.kafkaConsumerChecks = new KafkaConsumerChecks(options);

        this.greyScaleEnv = _.get(options, 'greyScaleEnv', false);
        this.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'ignoreAmountInURLForMobilePrepaidOperators'], {'airtel' : 1, 'jio' : 1, 'idea' : 1, 'vodafone' : 1, 'vodafone idea' : 1});
        this.duplicateNotificationOnSameDayValues = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON','DUPLICATE_NOTIFICATION'], []);

        this.consumerResumeTimeout = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'PLAN_VALIDTY_NOTIFICATION_CONSUMER', 'RESUME_TIMEOUT'], 1200000) : 0;
        setInterval(() => {
            this.initializeVariable();
        }, _.get(this.config, 'COMMON.INTERVAL_FOR_REINITIALIZE', 15) * 60 * 1000);
    }

    /**
     * This method will intialize the dynamic operator config specific variables after certain interval
     */
    initializeVariable() {
        let self = this;
        self.L.log("initializeVariable :: planValidityNotificationSubscriber", "Re-initializing variable after interval");
        self.operatorTemplateMappingConfig = _.get(this.config, 'OPERATOR_TEMPLATE_MAPPING');
        self.notificationConfig = _.get(this.config, 'NOTIFICATION');
        self.ignoreAmountInURLForMobilePrepaidOperators = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'COMMON', 'ignoreAmountInURLForMobilePrepaidOperators'], {'airtel' : 1, 'jio' : 1, 'idea' : 1, 'vodafone' : 1, 'vodafone idea' : 1});
        this.consumerResumeTimeout = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'PLAN_VALIDTY_NOTIFICATION_CONSUMER', 'RESUME_TIMEOUT'], 1200000) : 0;
    }

    async start() {
        try {
            this.L.log("start", "Plan validity notification subscriber service started");
            this.L.log("start", "set CVR data");
            await this.setCvrData();

            setInterval(() => {
                this.refreshCvrData();
            }, 1800000);

            this.L.log('start', 'Going to configure Publisher');
            await this.startPublisher();

            if (!this.consumerGroup) {
                throw (`Invalid Consumer Group ID ${this.consumerGroupId}`);
            } else {

                let configurations = _.get(this.config.PLAN_VALIDITY_NOTIFICATION, ["CONSUMER_SCHEDULER", this.consumerGroup], null);
                if (!configurations) {
                    throw (`there is no configuration for Group ID ${this.consumerGroupId}`);
                } else {

                    this.L.log('processNotificationsFromKafka', `Starting Notification Service for operator Group: ${this.consumerGroup} and configurations : ${JSON.stringify(configurations)}`);
                    this.kafkaConsumerConfig = configurations;

                    // schedule start and stop consumer timings
                    if (_.get(configurations, 'SCHEDULE_ON_INTERVAL', false) === true) {
                        this.L.log('start', 'setting up dummy logs..');
                        this.startDummyLogs();

                        this.L.log('start', 'Starting scheduler...');
                        this.startScheduler();
                    }

                    // If this is 24x7 consumer or this is the instant time to start the consumer 
                    if (this.startInstantConsumer()) {
                        this.L.log('start', 'Time to start consumer..lets start it!!');
                        await this.startConsumer();
                    }
                }
            }

        } catch (error) {
            this.L.critical("[src/services/planValidityNotificationSubscriber] error: ", error);
            throw error;
        }
    }

    async startPublisher() {
        await promisify(this.notify.configureKafkaPublisher.bind(this.notify))();
        this.L.log('Publisher Started!!');
    }

    setCvrData() {
        return new Promise((resolve, reject) => {
            this.catalogVerticalRecharge.getCvrData((error, data) => {
                if (!error && data && data.length) {
                    this.L.log('preparing cvr data', data.length);
                    let i = 0;
                    data.forEach(row => {
                        i++;
                        let key = row.service.toLowerCase() + '_' + row.operator.toLowerCase() + '_' + row.circle.toLowerCase() + '_prepaid';

                        this.cvrData[key] = row;
                        this.cvrDataPidMap[row.product_id] = row;
                        if (i == data.length) {
                            resolve();
                        }

                    });
                } else {
                    reject(error || "get empty response from CVR");
                }
            }, " paytype = 'prepaid' and status=1"); // getting only active PIDs which will ensure use of fresh/Active PID
        });
    }

    refreshCvrData() {
        let subquery = ` and updated_at > '${MOMENT().subtract(1, 'hour').subtract(40, 'minute').format("YYYY-MM-DD HH:mm:ss")}'`;

        this.L.log('refreshCvrData:: refresh process has started for cvr data!!');

        this.catalogVerticalRecharge.getCvrData((error, data) => {
            if (!error) {
                this.L.log('refreshCvrData:: preparing cvr data', data.length);
                
                data.forEach(row => {
                    let key = row.service.toLowerCase() + '_' + row.operator.toLowerCase() + '_' + row.circle.toLowerCase() + '_prepaid';

                    this.cvrData[key] = row;
                    this.cvrDataPidMap[row.product_id] = row;
                });

                this.L.log("refreshCvrData:: CVR data has refreshed now!!");
            } else {
                this.L.critical("refreshCvrData, error in getting cvr data from db!!");
            }
        }, " paytype = 'prepaid' and status=1 " + subquery);

    }

    startScheduler() {
        let from = _.get(this.kafkaConsumerConfig, 'FROM').split(":"),
            to = _.get(this.kafkaConsumerConfig, 'TO').split(":");

        let startRule = new SCHEDULE.RecurrenceRule();
        startRule.hour = from[0];
        startRule.minute = from[1];
        SCHEDULE.scheduleJob(startRule, this.startConsumer.bind(this));

        let stopRule = new SCHEDULE.RecurrenceRule();
        stopRule.hour = to[0];
        stopRule.minute = to[1];
        SCHEDULE.scheduleJob(stopRule, this.stopConsumer.bind(this));

        this.L.log('startScheduler', 'Scheduler configured !!');
    }

    async startConsumer() {
        this.L.log('startConsumer', 'Going to start consumer...!!');
        if (this.pauseConsumer) {
            this.pauseConsumer = false;
            this.L.log('startConsumer', 'Resuming consumer...!!');
            this.kafkaConsumer._resumeConsumer();
        } else {
            this.L.log('startConsumer', 'Configuring and starting consumer...!!');
            await this._configureAndStartConsumer();
        }
    }

    stopConsumer() {
        this.pauseConsumer = true;
        this.kafkaConsumer._pauseConsumer();
    }

    startDummyLogs() {
        let dummyLogs = setInterval(() => {
            this.L.log('startDummyLogs', 'Consumer will start shortly...');

            if (this.kafkaConsumer) {
                clearInterval(dummyLogs);
            }
        }, 2000);
    }

    /**
     * Check if consumer needs to be started immediately
     * Start if 
     * 1. 24x7 consumer
     * 2. from < now < to
     */
    startInstantConsumer() {
        let
            from = _.get(this.kafkaConsumerConfig, 'FROM'),
            to = _.get(this.kafkaConsumerConfig, 'TO'),
            now = MOMENT().format('HH:mm:ss');

        if (!_.get(this.kafkaConsumerConfig, 'SCHEDULE_ON_INTERVAL', false) || (from < now && now < to)) { // 24x7 service
            return true;
        } else {
            return false;
        }
    }

    async _configureAndStartConsumer() {
        let topic = this.kafkaConsumerConfig.TOPIC;

        this.L.log('configureKafka', 'Going to initialize Kakfa Consumer for topic : ' + topic);

        let batchSize = this.greyScaleEnv ? _.get(this.config, ['DYNAMIC_CONFIG', 'GREYSCALE_CONFIG', 'PLAN_VALIDTY_NOTIFICATION_CONSUMER', 'BATCHSIZE'], 5) :
            _.get(this.config, ['PLAN_VALIDITY_NOTIFICATION', 'CONSUMER', 'BATCH_SIZE'], 100)

        // Initialize consumer of topic Plan validity expiry notify
        this.kafkaConsumer = new this.infraUtils.kafka.consumer({
            "kafkaHost": _.get(this.config.KAFKA, 'TOPICS.PLAN_VALIDITY_NOTIFICATION.HOSTS'),
            "groupId": "planExpiryNotify-consumer",
            "topics": topic,
            "id": 'planExpiryNotifyConsumer_' + OS.hostname() + "_" + process.pid,
            "fromOffset": "earliest",
            "autoCommit": false,
            "batchSize": batchSize
        });

        await promisify(this.kafkaConsumer.initConsumer.bind(this.kafkaConsumer))(this.execSteps.bind(this));
    }

    execSteps(records) {
        let self = this,
            chunkSize = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'BR_CHUNKSIZE'], 50),
            currentPointer = 0, lastMessage;

        if (records && _.isArray(records)) {
            lastMessage = records[records.length - 1];
            self.kafkaConsumer._pauseConsumer();
        } else {
            self.L.critical('execSteps', `No valid kafka records found. Received records-`, records);
            return;
        }

        self.L.log('execSteps:: ', `Processing ${records.length} PLAN EXPIRY data !!`);

        ASYNC.whilst(
            () => {
                return currentPointer < records.length;
            },
            (callback) => {
                let nextChunk = records.slice(currentPointer, (currentPointer + chunkSize));
                currentPointer += chunkSize;

                self.L.verbose("currentPointer-- ", currentPointer, " records--", records.length);

                self.processBatch(nextChunk, () => {
                    setTimeout(() => {
                        callback();
                    }, 100);
                });
            },
            (err) => {
                if (err) {
                    this.L.error("[execSteps] error: ", err);
                }

                self.kafkaConsumerChecks.findOffsetDuplicates("PlanValidityNotificationSubscriber", records);

                self.kafkaConsumer.commitOffset(lastMessage, (error) => {
                    if (error) {
                        self.L.error('execSteps::', 'Error while commit for-', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'), " error: ", error);
                    }
                    else {
                        self.L.log('execSteps::', 'Commit success for offset :', _.get(lastMessage, 'offset') + ', topic :' + _.get(lastMessage, 'topic') + ', partition :' + _.get(lastMessage, 'partition') + ', timestamp :' + _.get(lastMessage, 'timestamp'));
                    }

                    // Resume consumer now
                    if (self.pauseConsumer) {
                        self.L.log('processNotifications', 'Done for the day, Time to take a rest.....!!');
                    } else {
                        setTimeout(() => {
                            self.kafkaConsumer._resumeConsumer();
                        }, this.consumerResumeTimeout)
                    }
                });
            }
        );
    }

    findMax(content, latest_recharge_date, sms_date_time) {
        content.operator_validated_at = null;
        if (sms_date_time == null && latest_recharge_date == null) {
            content.operator_validated_at = null;
        }
        if (sms_date_time == null) {
            content.operator_validated_at = MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss');
        }
        else if (latest_recharge_date == null) {
            content.operator_validated_at = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
        }
        else {
            content.operator_validated_at = MOMENT(sms_date_time).isAfter(MOMENT(latest_recharge_date)) ? MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss') : MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss');
        }
    }

    addOperatorValidatedAt(extra, content){
        let self = this;
        let sms_date_time = _.get(extra, 'sms_date_time', null);
        let latest_recharge_date = _.get(content, "latest_recharge_date", null);
        let updated_source = _.get(extra, "updated_data_source", null);

        try{
            if(sms_date_time){
                sms_date_time = parseInt(sms_date_time);
                sms_date_time = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
            }
        }
        catch(err){
            this.L.log("planValidityNotificationSubscriber :: process Batch :: sms_date_time parsing issue", err);
            sms_date_time = null;
        }
        self.L.log('latest_recharge_date:', latest_recharge_date, 'updated_source:', updated_source, 'sms_date_time:', sms_date_time, "cust_id and RN", _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
        if(updated_source){
            if (['SMS_PARSING_DWH_REALTIME', 'SMS_PARSING_DWH_MANUAL', 'SMS_PARSING_DWH', 'SMS_PARSING_REALTIME'].includes(updated_source)) {
                if (sms_date_time) {
                    content.operator_validated_at = MOMENT(sms_date_time).format('YYYY-MM-DD HH:mm:ss');
                }
                else{
                    self.findMax(content, latest_recharge_date, sms_date_time);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_source}`]);
                    self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
                }
            }
            else if (updated_source == "transaction") {
                if (latest_recharge_date) {
                    content.operator_validated_at = MOMENT(latest_recharge_date).format('YYYY-MM-DD HH:mm:ss');
                }
                else{
                    self.findMax(content, latest_recharge_date, sms_date_time);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_source}`]);
                    self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
                }
            }
            else{
                self.findMax(content, latest_recharge_date, sms_date_time);
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:${updated_source}`]);
                self.L.log('convertKafkaPayloadToRecord', 'updated_data_source date not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
            }
        }
        else{
            self.findMax(content, latest_recharge_date, sms_date_time);
            utility._sendMetricsToDD(1, ['REQUEST_TYPE:INCORRECT_DATA_FOR_UPDATED_DATA_SOURCE', 'STATUS:ERROR',`UPDATED_DATA_SOURCE:UPDATED_DATA_SOURCE_NOT_PRESENT`]);
            self.L.log('convertKafkaPayloadToRecord', 'updated_data_source not present', updated_source, _.get(content, 'customer_id', null), _.get(content, 'recharge_number', null));
        }
    }

    /**
     * Processing plan validity record in batch of 1000
     * @param {*} dayValue contains the day on which plan expired i.e 1 => d+1
     * @param {*} records contains all records
     * @param {*} done
     */
    processBatch(records, done) {
        let self = this;
        ASYNC.map(
            records,
            async (record, next) => {
                utility.sendNotificationMetricsFromCreate({} , {} , "RECVD")
                utility._sendMetricsToDD(1, [`REQUEST_TYPE:${self.requestType}`, 'STATUS:TRAFFIC']);
                try {
                    let value = _.get(record, 'value', null);
                    if (value) {
                        let content = typeof value === 'string' ? JSON.parse(value) : value;
                        let reconIdFromLibrary = utility.generateReconID(_.get(content, 'recharge_number', null), _.get(content, 'operator', null), _.get(content, 'amount', null), _.get(content, 'validity_expiry_date', null), _.get(content, 'bill_date', null));
                        try{
                            let extra = _.get(content, 'extra', null);
                            if(typeof extra == "string"){
                                extra = JSON.parse(extra)
                            }
                            let reconIdFromPayload = _.get(extra, 'recon_id', null);
                            if(_.get(content, 'service') && _.get(content,'service').toLowerCase() == 'mobile')
                            self.addOperatorValidatedAt(extra, content);

                            if(reconIdFromPayload && reconIdFromPayload!=reconIdFromLibrary){
                                this.L.log("processBatch:: recon_id mismatch for record: ", record);
                                utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_NOTI_RECONID_MISMATCH', 'STATUS:ERROR']);
                            }
                            if(!reconIdFromPayload){
                                _.set(content, 'recon_id', reconIdFromLibrary);
                            }else{
                                _.set(content, 'recon_id', reconIdFromPayload);
                            }
                        }catch(err){
                            this.L.error("processBatch:: error while setting recon_id for record: ", record);
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_NOTI_RECONID_MISMATCH', 'STATUS:ERROR']);
                            _.set(content, 'recon_id', reconIdFromLibrary);
                        }

                        let [validationResult,errorMsg] = await this.operatorValidation.validate(content);
                        if (validationResult) {
                            _.set(content, 'source', _.get(content, 'requestType', 'PLAN_VALIDITY_NOTIFICATION'));
                            _.set(content, ['timestamps', 'dwhKafkaPublishedTime'], _.get(content,'dwhKafkaPublishedTime', null));
                            _.set(content, ['timestamps','billFetchReminder_acknowledgeTime'], new Date().getTime());
                            _.set(content, ['timestamps','billFetchReminder_onBoardTime'], _.get(content, 'billFetchReminder_onBoardTime', null));
                            let dayValue = content.dayValue;
                            self.prepareNotification(dayValue, content, () => {
                                next();
                            });

                        } else {
                            utility.sendNotificationMetricsFromCreate(content , {} , "VALIDATION_FAILED")
                            this.L.log(`processBatch:: operator validation result: ${validationResult} for record: `, JSON.stringify(content));
                            self.notify.insertRejectedNotifications(next, errorMsg, content);
                        }
                    } else {
                        utility.sendNotificationMetricsFromCreate({} , {} , "VALIDATION_FAILED")
                        this.L.error("not a valid record ", record);
                        PublishStats.publishCounter(1, {
                            REQUEST_TYPE: this.requestType,
                            STATUS: "ERROR",
                        });
                        self.notify.insertRejectedNotifications(next, 'not recived a valid from kafka in create notification', record);
                    }
                } catch (error) {
                    this.L.error(`processBatch:: error for record: `, record, error);
                    PublishStats.publishCounter(1, {
                        REQUEST_TYPE: this.requestType,
                        STATUS: "ERROR",
                    });
                    self.notify.insertRejectedNotifications(next, self.billsLib.createErrorMessage(error), record);
                }
            },
            err => {
                if (err) {
                    this.L.error("processBatch:: error:", err);
                    PublishStats.publishCounter(1, {
                        REQUEST_TYPE: this.requestType,
                        STATUS: "ERROR",
                    });
                }
                done();
            }
        )
    }

    /**
     *
     * @param {*} callback
     * @param {*} dayValue contains the day on which plan expired i.e 1 => d+1
     * @param {*} record contains information about customers
     * Function will prepare PUSH notification for the  customer_id and rn_customer_id
     * if rechange number is not a paytm customer then send a message
     */
    async prepareNotification(dayValue, record, callback) {
        let self = this,
            key = self.getKeyForActivePid(record);

        self.L.log("Process notification, rn_customer_id :" + _.get(record, 'rn_customer_id', null) +
            " , cust_mobile: " + _.get(record, 'cust_mobile', null) +
            ",customer_id: " + _.get(record, 'customer_id', null) +
            ", recharge_number:" + _.get(record, 'recharge_number', null));

        // Lets log critical and stop execution for this record in case of PID is not found from map
        if (!_.get(self.cvrData, key, null)) {
            self.L.critical('prepareNotification', `PID not found for key ${key} and recharge_number ${record.recharge_number} and cust_id ${record.customer_id}`);
            return self.notify.insertRejectedNotifications(callback, 'PID not found', record);
        }

        let payLoad = {
            amount: _.get(record, 'amount', null),
            recharge_number: _.get(record, 'recharge_number', null),
            operator_label: _.get(self.cvrData, key + '.operator_label', null),
            operator: _.toLower(_.get(self.cvrData, key +  '.operator', null)),
            service: _.get(self.cvrData, key + '.service', null),
            paytype: _.get(self.cvrData, key + '.paytype', null),
            brand: _.get(self.cvrData, key + '.brand', null),
            category_id: _.get(self.cvrData, key + '.category_id', null),
            thumbnail: _.get(self.cvrData, key + '.thumbnail', null),
            paytype: "prepaid",
            customer_id : _.get(record, 'customer_id', null)
        };

        /**
         *  for d- notiifcation 
         */
        payLoad.due_date = record.validity_expiry_date;
        let dueDate = MOMENT(payLoad.due_date).isValid() ? MOMENT(payLoad.due_date).format('Do MMM YYYY') : null;
        _.set(payLoad, 'due_date', dueDate);


        let short_operator_name = null;

        try {
            let attributes = JSON.parse(_.get(self, ['cvrData', key], {}).attributes);
            short_operator_name = _.get(attributes, 'short_operator_name', null);
        } catch (error) {
            self.L.error('prepareNotification', 'error encountered while parsing attributes for operator label', _.get(payLoad, 'operator_label', null));
        }

        short_operator_name = short_operator_name || _.get(payLoad, 'operator_label', null);
        _.set(payLoad, 'short_operator_name', short_operator_name);
        let emojiData = _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'EMOJI_JAVA_ESCAPE_CODES'], null);
        _.extend(payLoad, emojiData);

        let operatorKeyForTemplate = this.getOperatorKeyForTemplate(record);
        let notificationRecords = [];
        record.timepoint = dayValue;
        if(record.timepoint && record.timepoint != null){
            record.timepoint=(-1*record.timepoint);
        }
        // push notification for customer who transacted
        let pushNotification_cust = {
            type: 'PUSH',
            recipients: _.get(record, 'customer_id', null)?(_.get(record, 'customer_id', null)).toString():null,
            notificationType: 'DUEDATE',
            template_id: _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_${_.toUpper(_.get(record, 'circle'))}_CUSTOMER_PUSH`],   // adding circle for localization
            _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_CUSTOMER_PUSH`],
                _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_CUSTOMER_PUSH`],
                    _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, `CUSTOMER_PUSH`], null))))
        };
        let operatorClause = this.identifyOperatorClause(_.get(payLoad, 'operator', null));
        let dueDateIntervals = [];
        if(operatorClause){ 
            let dueDateIntervalsFromConfig = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CUSTOM_SCHEDULE', operatorClause]);
            // Convert string list to array of numbers
            if (Array.isArray(dueDateIntervalsFromConfig)) {
                dueDateIntervals = dueDateIntervalsFromConfig.map(day => parseInt(day, 10)).filter(day => !isNaN(day));
            }
            this.L.log(`Using day values for operator "${_.get(payLoad, 'operator', null)}" from clause ${operatorClause}:`, dueDateIntervals);
        }
        let cohortDetails = self.notificationLibrary.getTemplateDetailsFromConfig(record, "BILLDUE", 'PUSH');
            if (cohortDetails) {
                _.set(record, 'cohortDetails', cohortDetails);
                console.log(cohortDetails.templateId);
                pushNotification_cust.template_id = cohortDetails.templateId;
                pushNotification_cust.cohortDetails = cohortDetails;
            } else {
                
                    let timePointList = self.notificationLibrary.getTimepontListFromCohortConfig(record, 'BILLDUE');
                    if (timePointList.includes(_.get(record, 'timepoint', null))) {
                        if (!dueDateIntervals.includes(_.get(record, 'timepoint', null))) {
                            return self.notify.insertRejectedNotifications(callback, 'Template not found', record);;
                        }
                    }
            }

        let chatNotification_cust = {
            type: 'CHAT',
            recipients: _.get(record, 'customer_id', null)?(_.get(record, 'customer_id', null)).toString():null,
            template_id: _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_CUSTOMER_CHAT`],
                _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_CUSTOMER_CHAT`],
                    _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, `CUSTOMER_CHAT`], null)))
        };

        notificationRecords.push(pushNotification_cust);
        notificationRecords.push(chatNotification_cust);
        // sms notification for customer who transacted for only On Day(d1) plan expiry case otherwise it should be null
        notificationRecords.push({
            type: 'SMS',
            recipients: _.get(record, 'cust_mobile', null),
            template_id: _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_CUSTOMER_SMS`],
                _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_CUSTOMER_SMS`],
                    _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, `CUSTOMER_SMS`], null)))
        })

        try {
            let whatsappNotification_cust = await self.notificationLibrary.checkForWhatsappNotification(record, payLoad, 'BILLDUE');
            if (!_.isNull(whatsappNotification_cust) && !whatsappNotification_cust.error ) {
                notificationRecords.push(whatsappNotification_cust);
            } 
            else{
                let rejectedRecord = _.cloneDeep(record);
                _.set(rejectedRecord, 'type', 'WHATSAPP');
                await self.notify.insertRejectedNotificationsViaPromise(whatsappNotification_cust.error?whatsappNotification_cust.error:'checks for whatsapp notification failed, dropping notification', rejectedRecord);
            }
        } catch (error) {
            self.L.error('prepareNotification::', `error occurred while checking for whatsapp notification for cust id ${_.get(payLoad, 'customer_id', null)}, rech number ${_.get(payLoad, 'recharge_number', null)}`, error);
            utility._sendMetricsToDD(1, [
                `REQUEST_TYPE: CHECK_FOR_WHATSAPP_NOTIFICATION`,
                `STATUS: ERROR_OCCURRED`,
                `SERVICE: ${_.get(payLoad, 'service', 'UNKNOWN')}`,
                `PAYTYPE: ${_.get(payLoad, 'paytype', 'UNKNOWN')}`
            ]);
        }

        /*
            check recharge number is different from customer_mobile number
         */
        if (_.get(record, 'recharge_number', null) != _.get(record, 'cust_mobile', null)) {
            // if it is paytm customer
            let rechNumStatus = _.get(record, 'rn_customer_id', null) ? 'PAYTM_USER' : 'NONPAYTM_USER';

            // push notification for recharge number 
            // recipient is null for nonpaytm_user
            let pushNotification_rnCust = {
                type: 'PUSH',
                recipients: _.get(record, 'rn_customer_id', null)?(_.get(record, 'rn_customer_id', null)).toString():null,
                notificationType: 'DUEDATE',
                rnCustId: true,
                template_id: _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_RN_CUSTOMER_PUSH`],
                    _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_RN_CUSTOMER_PUSH`],
                        _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, `RN_CUSTOMER_PUSH`], null)))
            };



            let chatNotification_rnCust = {
                type: 'CHAT',
                recipients: _.get(record, 'rn_customer_id', null)?(_.get(record, 'rn_customer_id', null)).toString():null,
                rnCustId: true,
                template_id: _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_RN_CUSTOMER_CHAT`],
                    _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_RN_CUSTOMER_CHAT`],
                        _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, `RN_CUSTOMER_CHAT`], null)))
            };

            notificationRecords.push(pushNotification_rnCust);
            notificationRecords.push(chatNotification_rnCust);

            // sms notification for recharge number 
            notificationRecords.push({
                type: 'SMS',
                recipients: _.get(record, 'recharge_number', null),
                template_id: _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_RN_CUSTOMER_SMS_${rechNumStatus}`],
                    _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_RN_CUSTOMER_SMS_PAYTM_USER`],
                        _.get(self.notificationConfig, ['PVREMINDER_TEMPLATE_CONFIG', dayValue, `RN_CUSTOMER_SMS_${rechNumStatus}`], null)))
            })
        }

        if(self.duplicateNotificationOnSameDayValues.indexOf(dayValue) > -1){
            let duplicate_pushNotification_cust = {
                type: 'PUSH',
                recipients: _.get(record, 'customer_id', null) ? (_.get(record, 'customer_id', null)).toString() : null,
                notificationType: 'DUEDATE',
                template_id: _.get(self.operatorTemplateMappingConfig, [operatorKeyForTemplate, `${dayValue}_CUSTOMER_PUSH_DUP`],
                    _.get(self.notificationConfig, ['TEMPLATE_ID_BY_SERVICE', `PV_${_.toUpper(_.get(payLoad, 'service'))}_${dayValue}_CUSTOMER_PUSH_DUP`], null))
            };
            if (duplicate_pushNotification_cust.template_id) notificationRecords.push(duplicate_pushNotification_cust);
        }

        ASYNC.map(
            notificationRecords,
            (notificationRecord, next) => {
                self.sendNotification(dayValue, record, notificationRecord, payLoad, () => {
                    next();
                });
            },
            err => {
                callback();
            }
        )
    }
    /**
     * Identifies the operator clause (FIRST, SECOND, etc.) based on operator name
     * @param {string} operator - The operator name
     * @returns {string} The clause identifier (FIRST, SECOND, THIRD, FOURTH, FIFTH) or null if not found
     */
    identifyOperatorClause(operator) {
        if (!operator) {
            return null;
        }

        const operatorLower = operator.toLowerCase();
        
        // Check clauses 1-5 (including FIFTH)
        for (let clauseNum = 1; clauseNum <= 5; clauseNum++) {
            const clauseKey = clauseNum === 1 ? 'FIRST' : 
                             clauseNum === 2 ? 'SECOND' : 
                             clauseNum === 3 ? 'THIRD' : 
                             clauseNum === 4 ? 'FOURTH' : 'FIFTH';
            
            const operatorsInClause = _.get(this.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'CUSTOM_PREPAID_OPERATOR', clauseKey], []);
            
            if (Array.isArray(operatorsInClause)) {
                const found = operatorsInClause.some(op => op.toLowerCase() === operatorLower);
                if (found) {
                    this.L.log(`identifyOperatorClause: Operator "${operator}" found in clause ${clauseKey}`);
                    return clauseKey;
                }
            }
        }
        
        this.L.log(`identifyOperatorClause: Operator "${operator}" not found in any clause`);
        return null;
    }
    getKeyForActivePid(record) {
        let self = this;
        let key = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid';

        let productId = _.get(self.cvrData, [key, 'product_id'], null);
        if (!productId) { // Handling when PID will be disabled and complete migration will happen for VIL
            let migratedOperators = _.get(self.config, ['COMMON', 'MIGRATED_OPERATORS', record.operator.toLowerCase()], null);
            if (migratedOperators) {
                record.operator = 'vodafone idea';
                let vilKey = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid';
                self.L.log("getKeyForActivePid", `PID for ${key} is disabled...using vil key:${vilKey}`);
                return vilKey;
            } else {
                return null;
            }
        }

        let activeProductId = self.activePidLib.getActivePID(productId);
        if (activeProductId == productId) return key;

        let activeProductIdCvrData = _.get(self.cvrDataPidMap, activeProductId, null);

        if (!activeProductIdCvrData) return null;
        let activeProductIdKey = activeProductIdCvrData.service.toLowerCase() + '_' + activeProductIdCvrData.operator.toLowerCase() + '_' + activeProductIdCvrData.circle.toLowerCase() + '_' + 'prepaid';
        record.operator = _.get(activeProductIdCvrData, 'operator', null);
        return activeProductIdKey;
    }

    setUtmParamsInWpValue(wpValue, payLoad, notificationRecord) {
        let self = this;
        try{
            let short_operator_name = _.get(payLoad, "short_operator_name", null);
            let template_id = _.get(notificationRecord, "template_id", null);
            let operator = _.get(payLoad, "operator", null);
            let product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null;
            let utm = self._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);
            return wpValue + utm;
        }catch(e){
            self.L.error('setUtmParamsInWpValue::Error setting utm params in wp value::Err:Msg', e);
            return wpValue;
        }
    }

    /**
     * Use to send SEND notification for all plan validity
     * @param {*} dayValue contains the day on which plan expired i.e 1 => d+1
     * @param {*} record  contains information about customer_id and recharge_number
     * @param {*} notificationRecord type and templates details
     * @param {*} payLoad contains notification details
     * @param {*} callback
     */
    sendNotification(dayValue, record, notificationRecord, payLoad, callback) {
        if (!_.get(notificationRecord, 'template_id', null)) {
            PublishStats.publishCounter(1, {
                REQUEST_TYPE: record.requestType,
                STATUS: "NO_TEMPLATE_ID"
            });
            this.L.info("no template_id for record: ", record);
            utility.sendNotificationMetricsFromCreate(payLoad ,notificationRecord , "INVALID_TEMPLATE_ID")
            return this.notify.insertRejectedNotifications(callback, 'no template id for record', record, notificationRecord);
        }
        if (_.get(notificationRecord, 'recipients', null)) {
            let self = this,
                key = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid.product_id';

            if (_.get(notificationRecord, 'type', null) == 'PUSH') {
                let deeplinkUrl = self.getDeepLinkUrl(record, notificationRecord, payLoad, key);
                let deepLinkData = {
                    "extra": {
                        "url": deeplinkUrl,
                        "url_type": "external"
                    },
                    "payLoad": payLoad
                }
                let pushNotificationData = self.notificationLibrary.getPushNotiData(deepLinkData, notificationRecord, 2);
                self.sendProcessedNotification(record, pushNotificationData, notificationRecord, key, callback);
            } else if (_.get(notificationRecord, 'type', null) == 'CHAT') {
                let deeplinkUrl = self.getDeepLinkUrl(record, notificationRecord, payLoad, key);

                let newPayload = _.cloneDeep(payLoad);
                _.set(newPayload, 'deeplink', deeplinkUrl);

                let [error, chatNotificationData] = self.notificationLibrary.getChatNotiData(newPayload, notificationRecord);
                if (error) {
                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_PAYLOADGEN")
                    self.L.error('sendNotification', `Error generating chat payload for ${_.get(record, 'recharge_number', null)}, templateId:${_.get(notificationRecord, 'template_id', null)} with error - ${error}`);
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:PV_NOTI_CHAT_V3PAYLOAD', 'STATUS:ERROR']);
                    return self.notify.insertRejectedNotifications(callback, 'error generating chat payload for record', record, notificationRecord);
                }
                self.sendProcessedNotification(record, chatNotificationData, notificationRecord, key, callback);
            }

            /*
            get the short_sms_url(tiny url) for SMS notification and send notification
            */
            else if (_.get(notificationRecord, 'type', null) == 'SMS') {
                self.notificationLibrary.disableSmsForUser(record, (error, res) => {
                    if(error) {
                        utility.sendNotificationMetricsFromCreate(record,notificationRecord,"SMS_DISABLED")
                        return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record, notificationRecord);
                    }
                    let isSmsBlockedForUser = res;
                    if(isSmsBlockedForUser) {
                        return self.notify.insertRejectedNotifications(callback, 'sms is blocked for user', record, notificationRecord);
                    }
                    let circleCVRKey = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid.circle';
                    let operatorCVRKey = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid.operator';

                    let paytypeKey = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid.paytype',
                    recharge_number = _.get(payLoad, 'recharge_number', null),
                    amount = _.get(payLoad, 'amount', null),
                    product_id = _.get(self.cvrData, key, null),
                    paytype = _.get(self.cvrData, paytypeKey, null),
                    category_id = _.get(payLoad, 'category_id', null),
                    product_service = _.get(payLoad, 'service', null) != null ? _.get(payLoad, 'service', null).toLowerCase() : null,
                    user_data = _.get(record, 'user_data', null),
                    paramsForTinyUrl = {
                        "product_id": product_id,
                        "recharge_number": recharge_number,
                        "paytype": paytype,
                        "category_id": category_id,
                        "user_data": user_data,
                        "amount": amount,
                        "product_service": product_service,
                        "notificationRecord": notificationRecord,
                        "short_operator_name": _.get(payLoad, 'short_operator_name', null),
                        "operator" : _.get(payLoad, 'operator' , null),
                        "operator_validated_at": _.get(record, "operator_validated_at",null),
                        "operatorKey": _.get(self.cvrData, operatorCVRKey, null),
                        "circleKey": _.get(self.cvrData, circleCVRKey, null),
                    };
                    self.createTinyUrl(paramsForTinyUrl, function (err, tiny_url) {
                        if (!err && tiny_url) {
                            let newPayload = _.cloneDeep(payLoad);
                            _.set(newPayload, 'sms_short_link', tiny_url);

                            let smsNotificationData = self.notificationLibrary.getSmsNotiData(newPayload, notificationRecord);
                            self.sendProcessedNotification(record, smsNotificationData, notificationRecord, key, callback);
                        } else {
                            self.L.error('sendNotification: Error in creating Tiny Url for recharge_number ' + recharge_number + ' and product_id ' + product_id + ' :: ' + err);
                            utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_URLCREATE")
                            utility._sendMetricsToDD(1, ['REQUEST_TYPE:PLAN_VALIDITY_CREATE_TINY_URL', 'STATUS:ERROR']);
                            self.notify.insertRejectedNotifications(callback, 'error in url create', record, notificationRecord);
                        }
                    });
                })
            } else if (_.get(notificationRecord, 'type', null) == 'WHATSAPP') {
                let product_id = _.get(self.cvrData, key, null);
                let recharge_number = _.get(payLoad, 'recharge_number', null);
                let wpValue = '';
                let service = _.get(record, 'service', null);
                if(service && _.toUpper(service) == 'LOAN'){
                    wpValue = `1&product_id=${product_id}`;
                } else {
                    wpValue = `1&recharge_number=${recharge_number}&product_id=${product_id}&toAmount=true` + self.notificationLibrary.getWpPriceSuffix(record);
                }

                wpValue = self.setUtmParamsInWpValue(wpValue, payLoad, notificationRecord);

                _.set(payLoad, 'wp', wpValue);
                let [err,whatsappNotificationData] = self.notificationLibrary.getWhatsappNotiData(payLoad, notificationRecord);
                if (err) {
                    self.L.error('sendNotification', `Error generating whatsapp payload for rn ${_.get(record, 'recharge_number', null)}, templateId:${_.get(notificationRecord, 'template_id', null)} with error - ${err}`);
                    utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_PAYLOADGEN")
                    utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTI_WHATSAPP_V3PAYLOAD', 'STATUS:ERROR']);
                    return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(err), record, notificationRecord);
                } else {
                    self.sendProcessedNotification(record, whatsappNotificationData, notificationRecord, key, callback);
                }
            } else {
                utility.sendNotificationMetricsFromCreate(record,notificationRecord,"TYPE_NOT_SUPPORTED")
                // self.sendProcessedNotification(record,notificationData,notificationRecord,key,callback);
                // as we only send sms and push notifications for plan validity;
                self.notify.insertRejectedNotifications(callback, 'type not supported', record, notificationRecord);
            }

        }
        else {
            utility.sendNotificationMetricsFromCreate(payLoad ,notificationRecord , "INVALID_RECIPIENTS")
            PublishStats.publishCounter(1, {
                REQUEST_TYPE: record.requestType,
                STATUS: "NO_RECIPIENTS"
            });
            this.L.info("no recipients for record: ", record);
            let error_msg = 'no recipients found for record';
            if(_.get(notificationRecord, 'rnCustId', null)){
                error_msg = 'no recipients found for record for rn_customer_id';
            }
            this.notify.insertRejectedNotifications(callback, error_msg, record, notificationRecord);
        }
    }

    /**
     * Generate DeepLink Url
     * @param {*} record 
     * @param {*} notificationRecord 
     * @param {*} payLoad 
     * @param {*} key 
     */
    getDeepLinkUrl(record, notificationRecord, payLoad, key) {
        let self = this;
        let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(payLoad, 'category_id', null)], null);
        let deeplink_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DIGITAL_DEEPLINK_URL'], null);
        let deeplink_api = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEEPLINK_API'], null) + "/" + _.get(payLoad, 'category_id', null);
        let product_service = _.get(payLoad, 'service', null) ? _.get(payLoad, 'service', null).toLowerCase() : null;
        let circleCVRKey = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid.circle';
        let operatorCVRKey = record.service.toLowerCase() + '_' + record.operator.toLowerCase() + '_' + record.circle.toLowerCase() + '_' + 'prepaid.operator';
        let operatorKey = _.get(self.cvrData, operatorCVRKey, null); 
        let circleKey = _.get(self.cvrData, circleCVRKey, null);
        if (!landing_path) {
            if (product_service === 'mobile') {
                landing_path = "mobile_prepaid";
            } else if (product_service === 'datacard') {
                landing_path = "datacard_prepaid";
            } else if (product_service === 'dth') {
                landing_path = "dth";
            } else {
                landing_path = 'utility';
            }
        }

        let url = `paytmmp://${landing_path}?url=${deeplink_url}${deeplink_api}`;

        /**
         * get utm config for deeplink data for customer or recharge number customer
         */
        let template_id = _.get(notificationRecord, 'template_id', null);
        let short_operator_name = _.get(payLoad, 'short_operator_name', null);
        let operator = _.get(payLoad, 'operator', null);
        let utmConfig = self._utmByTemplateId(template_id, short_operator_name, '$', product_service, operator);
       
        let deeplinkUrl = null;
         if (product_service=='mobile' && _.get(self.ignoreAmountInURLForMobilePrepaidOperators, operator, null)) {
            // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
            deeplinkUrl = url + "?$product_id=" + _.get(self.cvrData, key, null) + "$recharge_number=" + _.get(record, 'recharge_number', null) + "$expandBrowsePlan=true" + utmConfig;
        } else {
            
        // let deeplinkUrl =null;
        if(product_service=='mobile'){
            deeplinkUrl = url + "?$product_id=" + _.get(self.cvrData, key, null) + "$recharge_number=" + _.get(record, 'recharge_number', null) + "$price=" + _.get(record, 'amount', 0) + utmConfig;
        } 
        else{
            deeplinkUrl = url + "?product_id=" + _.get(self.cvrData, key, null) + "$recharge_number=" + _.get(record, 'recharge_number', null) + "$price=" + _.get(record, 'amount', 0) + utmConfig;
        }
        }

        if(_.get(record,'operator_validated_at', null)){
            deeplinkUrl = deeplinkUrl +  "$operator_validated_at=" + _.get(record,'operator_validated_at', null)+ "$operatorKey=" + operatorKey + "$circleKey=" + circleKey;
        }
         
        return deeplinkUrl;
    }

    getDataForMeasurementAndTracking(record, notificationRecord, notificationData) {
        let self = this;
        let raw_expiry_date = _.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.due_date', null):_.get(notificationData, 'options.data.due_date', null);
        if(MOMENT(raw_expiry_date).isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date).format('YYYY-MM-DD');
        }else if( MOMENT(raw_expiry_date, 'Do MMM YYYY').isValid()){
            raw_expiry_date = MOMENT(raw_expiry_date, 'Do MMM YYYY').format('YYYY-MM-DD');
        }

        return {
            recon_id: _.get(record, 'recon_id', null),
            type: _.get(notificationRecord, 'type', null),
            notif_type: _.get(record, 'notificationType', null) || 'DUEDATE',
            promocode: _.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.promo_code', null):_.get(notificationData, 'options.data.promo_code', null),
            msg_type: null,
            timepoint: -(_.get(record, 'dayValue', null)),
            templateName:_.get(notificationData, 'templateName', null),
            operator: _.toLower(_.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.operator', null):_.get(notificationData, 'options.data.operator', null)),
            amount: _.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.amount', null):_.get(notificationData, 'options.data.amount', null),
            service: _.toLower(_.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.service', null):_.get(notificationData, 'options.data.service', null)),
            due_date: raw_expiry_date,
            customer_id:_.get(notificationData, 'dynamicParams',null)? _.get(notificationData, 'dynamicParams.customer_id', null):_.get(notificationData, 'options.data.customer_id', null),
            bill_source: 'RU'
        }
    }

    /**
     *
     * @param {*} record contains information about customer_id and recharge_number
     * @param {*} notificationData  contains data to be sent in notification
     * @param {*} notificationRecord contains type and templates details
     * @param {*} callback callback function
     */
    sendProcessedNotification(record, notificationData, notificationRecord, cvrKey, callback) {
        let self = this;

        let
                source = _.get(record, 'source', null),
                sourceIdMapping = _.invert(_.get(self.notificationConfig, 'source'), {}),
                source_id = _.get(sourceIdMapping, source, 2);
        

        let additionalData = self.getDataForMeasurementAndTracking(record, notificationRecord, notificationData);
        _.set(notificationData, 'additional_data', additionalData);
        let send_at = self.getSendAtForNotification(record, notificationRecord, source_id);
        if (!send_at) {
            return callback();
        }
         
        let body = {
            "source_id": source_id,
            "category_id": 2,
            "recharge_number": _.get(record, 'recharge_number', null),
            "product_id": _.get(self.cvrData, cvrKey, null),
            "max_retry_count": 2,
            "retry_interval": 30,
            "type": _.get(notificationRecord, 'type', null),
            "template_id": _.get(notificationRecord, 'template_id', null),
            "recipient": _.get(notificationRecord, 'recipients', null),
            "cohortDetails": _.get(notificationRecord, 'cohortDetails', null),
            "send_at": send_at,
            "data": notificationData,
            "rules": {
                "condition": `category_id=2 and source_id=2 and recharge_number='${_.get(record, 'recharge_number', null)}' and product_id=${_.get(self.cvrData, cvrKey, null)} 
                                and type='${_.get(notificationRecord, 'type', null)}' and template_id=${_.get(notificationRecord, 'template_id', null)}`,
                "actions": [
                    {
                        "status": "pending",
                        "action": "drop"
                    },
                    {
                        "status": "sent",
                        "action": "drop"
                    },
                    {
                        "status": "error",
                        "action": "drop"
                    }
                ]
            },
            "operator_validated_at": _.get(record,'operator_validated_at', null)
        };
        _.set(body, 'timestamps', _.get(record, 'timestamps', null));

        if (record.skipNotification) {
            utility.sendNotificationMetricsFromCreate(record,notificationRecord,"SKIP_NOTIFICATION")
            self.L.log(`Skip::SendProcessedNotification, payload: `, JSON.stringify(body));
            return self.notify.insertRejectedNotifications(callback, 'skipNotification=1 for record', record);
        }

        var latencyStart = new Date().getTime();
        // Calling notify operation internally instead of API hit
        self.notify.__createNotification(function (error, data) {
          
            let reqType = record.requestType + "_created";

            utility._sendLatencyToDD(latencyStart, {
                'REQUEST_TYPE': reqType,
            });
            if (error) {
                utility.sendNotificationMetricsFromCreate(record,notificationRecord,"ERROR_IN_CREATE")
                utility._sendMetricsToDD(1, [`REQUEST_TYPE:${reqType}`, 'STATUS:ERROR', 'PLATFORM_PAID_STATUS:' + record.status]);

                self.L.error('Error in sending ' + body.type + ' notification to the recipient: ' + body.recipient + ' ' + error);
                return self.notify.insertRejectedNotifications(callback, self.billsLib.createErrorMessage(error), record);
            } else {
                utility.sendNotificationMetricsFromCreate(record,notificationRecord,"CREATED")
                utility._sendMetricsToDD(1, [`REQUEST_TYPE:${reqType}`, 'STATUS:CREATE', 'PLATFORM_PAID_STATUS:' + record.status]);

                self.L.log("time taken by db query= ", new Date().getTime() - latencyStart, " ms,", " for recipient= ", body.recipient, ", for notification type= ", body.type);

                self.L.verbose(body.type + ' notification sent to the notify service: ' + body.recipient + ' ' + body.data, " response from service ", data);
                return callback();
            }
        }, body);
    }

    getSendAtForNotification(record, notificationRecord, source_id) {
        let self = this;
        let send_at = null;

        if (_.get(notificationRecord, 'type', null) == 'WHATSAPP') {
            return self.getSendAtForWhatsappNotification(record, notificationRecord);
        }
        try{
            let topicToPublish = self.notify.getTopicToPublish(2, source_id, _.get(notificationRecord, 'type', null),_.get(notificationRecord, 'template_id', null),_.get(notificationRecord, 'cohortDetails', null))
            let hourTosendNotification = topicToPublish.includes('_NOTIFICATION')? null : topicToPublish.split('_')[1];
            
            send_at =  MOMENT(_.get(record,['timestamps', 'billFetchReminder_onBoardTime'],null)).isValid()? MOMENT(_.get(record,['timestamps', 'billFetchReminder_onBoardTime'],null)).format('YYYY-MM-DD HH:mm:ss.SSS'):MOMENT().format('YYYY-MM-DD HH:mm:ss.SSS');
            let currentDate = MOMENT().format('YYYY-MM-DD'),
            startDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_START_TIME'], '09:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            endDateTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'BR_NOTIFICATION_END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            dueDateExpiryTime = MOMENT(currentDate + ' ' + _.get(self.notificationConfig, ['scheduletimeinterval', 'DUE_DATE_NOTIFICATOION_EXPIRY_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss'),
            startOfDay = MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            startOfHour =hourTosendNotification? MOMENT().startOf('day').add(hourTosendNotification, 'hours').format('YYYY-MM-DD HH:mm:ss'):null
            
            if(!hourTosendNotification && topicToPublish!='RT_NOTIFICATION'){
                if(!MOMENT(send_at).isBetween(startDateTime, endDateTime)){
                    if (MOMENT(send_at).isBetween(startOfDay, startDateTime)) {
                        send_at = startDateTime;
                    } else {
                        send_at = MOMENT(startDateTime).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
                    }
                }
            }else if (hourTosendNotification){
                if(!MOMENT(send_at).isBetween(startOfHour, endDateTime)){ 
                    if (MOMENT(send_at).isBetween(startOfDay, startOfHour)) {
                        send_at = startOfHour;
                    } else {
                        send_at = MOMENT(startOfHour).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
                    }
                }
            }
            
            if(MOMENT().isAfter(dueDateExpiryTime) && topicToPublish!='RT_NOTIFICATION'){
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:CREATE_NOTIFICATION', 'STATUS:ERROR', 'TYPE:DUEDATE_NOTIFICATOION_EXPIRY_TIME']);
                self.L.log(`sendProcessedNotification:: DUEDATE notification for ${_.get(record, 'debugKey')} is not sent because of DUEDATE_NOTIFICATOION_EXPIRY_TIME`);
                return null;
            }
            
        } catch(e){
            self.L.error(`Error in calculating send at:: setting default send_at as now()`, e);
            send_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        } 
        return send_at; 
    }

    getSendAtForWhatsappNotification(record, notificationRecord) {
        /**
         * 1. if consumerGroup == 'common' - set send_at = 9am 
         * 2. if consumerGroup == 'vil' - set send_at = current time
         * 3. if consumerGroup == 'common_realtime' -
         *      a. if Non-DND hour - send notification in realtime
         *      b. if DND hour - send notification the next day
         */

        let self = this;
        let send_at = null;
        let currentDate = MOMENT().format('YYYY-MM-DD');
        if (self.consumerGroup == 'common') {
            send_at = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'COMMON_PV_CONSUMER', 'START_TIME'], '09:00:00')).format('YYYY-MM-DD HH:mm:ss');
        } else if (self.consumerGroup == 'vil') {
            send_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        } else {
            // consumerGroup is common_realtime
            let rtStartDateTime = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'COMMON_REALTIME_CONSUMER', 'START_TIME'], '07:00:00')).format('YYYY-MM-DD HH:mm:ss');
            let rtEndDateTime = MOMENT(currentDate + ' ' + _.get(self.config, ['DYNAMIC_CONFIG', 'WHATSAPP_NOTIFICATION_CONFIG', 'COMMON_REALTIME_CONSUMER', 'END_TIME'], '22:00:00')).format('YYYY-MM-DD HH:mm:ss');
            if (MOMENT().isBetween(MOMENT(rtStartDateTime), MOMENT(rtEndDateTime))) {
                send_at = MOMENT().format('YYYY-MM-DD HH:mm:ss');
            } else {
                send_at = MOMENT(rtStartDateTime).add(1, 'day').format('YYYY-MM-DD HH:mm:ss');
            }
        }
        self.L.log('getSendAtForWhatsappNotification::', `send_at set to ${send_at} for record rech num ${_.get(record, 'recharge_number', null)}`);
        return send_at;
    }

    /**
     *
     * @param {*} paramsForTinyUrl contains the required data(i.e. product id , recharge number , amount etc) to create tiny url
     * @param {*} callback callback function
     */
    createTinyUrl(paramsForTinyUrl, callback) {
        let self = this,
            product_id = _.get(paramsForTinyUrl, 'product_id', null),
            category_id = _.get(paramsForTinyUrl, 'category_id', null),
            paytype = _.toLower(_.get(paramsForTinyUrl, 'paytype', null)),
            recharge_number = _.get(paramsForTinyUrl, 'recharge_number', null),
            amount = _.get(paramsForTinyUrl, 'amount', null),
            product_service = _.toLower(_.get(paramsForTinyUrl, 'product_service', null)),
            notificationRecord = _.get(paramsForTinyUrl, 'notificationRecord', null),
            operator = _.get(paramsForTinyUrl, 'operator', null),
            smart_url =
                _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_PRODUCT_ID', `${product_id}`],
                    _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_CATEGORY_ID', `${category_id}`],
                        _.get(self.config, ['DYNAMIC_CONFIG', 'NOTIFICATION_CONFIG', 'SMART_URL_BY_SERVICE_PAYTYPE', `${product_service}::${paytype}`], null))),
            sms_url,
            operatorKey = _.get(paramsForTinyUrl, "operatorKey"),
            circleKey =  _.get(paramsForTinyUrl, "circleKey");

        if (product_id == null || recharge_number == null || amount == null || product_service == null || notificationRecord == null) {
            return callback("paramsForTinyUrl cannot have null values", null);
        }

        if (smart_url && smart_url.length) {
            if(product_service=='mobile'){
                sms_url = smart_url[0] + `?$category_id=${category_id}&`;
            }else{
                sms_url = smart_url[0] + `?category_id=${category_id}&`;
            } 
        } else {
            self.L.error('createTinyUrl::', `smart url not created for product_service::paytype ${product_service}::${paytype} category_id:${category_id} product_id:: ${product_id} recharge_number:: ${recharge_number}`);
            utility._sendMetricsToDD(1, [`REQUEST_TYPE:SMART_URL_NOT_CREATED`, 'STATUS:ERROR']);
            let product_service_sms_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'PRODUCT_SERVICE_SMS_URL'], null);
            if(product_service=='mobile'){
                sms_url = _.get(product_service_sms_url, product_service, null) + '?$';
            }else{
                sms_url = _.get(product_service_sms_url, product_service, null) + '?';
            }
        }
        if (sms_url == null) {
            sms_url = _.get(self.config, ['NOTIFICATION', 'notificationapi', 'DEFAULT_SMS_URL'], null) + "?";
        }
        let landing_path = _.get(self.config, ['NOTIFICATION', 'category_url_type_map', _.get(paramsForTinyUrl, 'category_id', null)], null);
        if (!landing_path) {
            if (product_service === 'mobile') {
                landing_path = "mobile_prepaid";
            }
        }
        if(product_service === 'mobile' && _.get(self.ignoreAmountInURLForMobilePrepaidOperators, operator, null) ) {
            // For mobile_prepaid category and for specific operators we are removing price attribute from url & instead adding expandBrowsePlan=true 
            sms_url = sms_url +  `product_id=${product_id}&recharge_number=${recharge_number}&expandBrowsePlan=true`;
        } else {
            sms_url = sms_url + "product_id=" + product_id + "&recharge_number=" + recharge_number + "&price=" + amount;
        }

        if(_.get(paramsForTinyUrl, "operator_validated_at",null)){
            sms_url = sms_url + `&operator_validated_at=${_.get(paramsForTinyUrl, "operator_validated_at",null)}&operatorKey=${operatorKey}&circleKey=${circleKey}`;
        }
        let template_id = _.get(notificationRecord, 'template_id', null);
        let short_operator_name = _.get(paramsForTinyUrl, 'short_operator_name', null);
        sms_url = sms_url + self._utmByTemplateId(template_id, short_operator_name, '&', product_service, operator);
        self.L.log(`createTinyUrl:: url ${product_id} ${product_service}::${paytype} `, sms_url);
        self.tinyUrlObject.createShortUrl(callback, _.get(self.config, ['TINYURL_CONFIG'], null), sms_url);
    }

    _utmByTemplateId(template_id, short_operator_name, operator = '&', product_service, operator_name) {
        let self = this;
        //fetch utm details by notificationType
        //utm_source, utm_medium, utm_campaign
        let utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', template_id]);
        if (!utm) {
            self.L.critical(`UTM config not found for template: ${template_id}`);
            utm = _.get(self.config, ['NOTIFICATION', 'TEMPLATE_UTM', 'notFound'], {});
        }
        if (short_operator_name) {
            short_operator_name = short_operator_name.toLowerCase();
        } else if (operator_name) {
            short_operator_name = operator_name.toLowerCase();
        } else {
            short_operator_name = "default";
        }
        let utmCampaign =  (product_service ? product_service.toString().toLowerCase() : "default") + "_" + short_operator_name + "_" + (template_id ? template_id : "default");
        
        return `${operator}utm_source=${self.getUtmParam(utm.utm_source)}${operator}utm_medium=${self.getUtmParam(utm.utm_medium)}${operator}utm_campaign=${self.commonLib.getUtmParam(utmCampaign, "_")}`;
    }

    getUtmParam(utmparam) {
        let utmProcessed = encodeURIComponent(utmparam.replace(/[^a-zA-Z0-9 ]/g, ''))
        return utmProcessed;
    }

    getOperatorKeyForTemplate(record) {

        if (this.config.COMMON.VI_GROUP.includes(record.operator)) {
            let paidStatus = "paid_at_paytm";
            if (record.status == this.config.COMMON.bills_status.PAID_ON_OTHER_PLATFORM) {
                paidStatus = "paid_at_other_platform";
            }

            return "vi" + "_" + paidStatus;
        } else {
            return _.get(record, 'operator', null);
        }
    }

     suspendOperations(){
        var self        = this,
        deferred = Q.defer();

        self.L.log(`PlanValidityNotificationSubscriber::suspendOperations kafka consumer shutdown initiated`);
    
        Q()
        .then(function(){
            self.kafkaConsumer.close(function(error, res){
                if(error){
                    self.L.error(`PlanValidityNotificationSubscriber::stopConsumer :: Error Stopping Consumer Err : ${error}`);
                    return error;
                }
                self.L.info(`PlanValidityNotificationSubscriber::stopConsumer :: Consumer Stopped!  Response : ${res}`);
            })
        })
        .then(function(){
            self.L.log(`PlanValidityNotificationSubscriber::suspendOperations kafka consumer shutdown successful`);
            deferred.resolve();
        })
        .catch(function(err){
            self.L.error(`PlanValidityNotificationSubscriber::suspendOperations error in shutting kafka consumer`, err);
            deferred.reject(err);
        });
        return deferred.promise;
    }

}

export default PlanValidityNotificationSubscriber;

