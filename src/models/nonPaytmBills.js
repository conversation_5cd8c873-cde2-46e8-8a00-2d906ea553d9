import MOMENT from 'moment'
import _ from 'lodash'
import <PERSON><PERSON><PERSON> from 'async'
import utility from '../lib'
import EncryptorDecryptor from 'encrypt_decrypt';
import CRYPTO from 'crypto';
import { param } from 'express-validator';
import CustomTableResolver from '../utils/customTableResolver';
import AES256Encryption from '../utils/aes256encryption'
import EncryptionD<PERSON>ryptioinHelper from '../lib/EncryptionDecryptioinHelper'
const CC_SERVICE = "financial services";
import Logger from '../lib/logger';
const MAX_AMOUNT = 2147483647;
const MIN_AMOUNT = -2147483648;

class NonPaytmBillsModel {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.client = options.cassandraDbClient;
        this.notificationNewClusterClient = options.notificationNewClusterClient;
        this.cryptr = new EncryptorDecryptor();
        this.retryLimit = 5;
        this.nonPaytmBillsTable = 'bills_non_paytm';
        this.nonPaytmRecentsTable = 'bills_recent_records';
        this.nonPaytmDueDateTable = 'bills_due_date';
        this.billGenTable = 'billgen_bills_non_paytm_';
        this.billDueTable = 'billdue_bills_non_paytm_';
        this.nonPaytmFallbackCustomerIdTable = 'fallback_customer_id';
        this.nonPaytmBillFetch = "bills_non_paytm_bill_fetch";
        this.pidMappingTable = 'pid_mapping';
        this.tableResolver = new CustomTableResolver(options);
        this.aes256Encryption = new AES256Encryption(options);
        this.encryptHelper = new EncryptionDecryptioinHelper(options);
        this.logger = new Logger(options);
        this.commonLib = new utility.commonLib(options);
   }

    async readPidMappingData(params) {
        const self = this;
        const query = `SELECT * FROM ${self.pidMappingTable} WHERE recharge_number = ? AND service = ? AND operator = ? `;
        const queryParams = [params.rechargeNumber, params.service, params.operator];
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
                .then(result => {
                    this.L.log(`nonPaytmBills::readRecordFromPidMapping fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
                    resolve(result.rows);
                })
                .catch(error => {
                    reject(new Error('nonPaytmBills::readRecordFromPidMapping DB exception!' + JSON.stringify(queryParams) + error));
                })
        })
    }

    checkIfDbRecordIsEncrypted(record) {
        return _.get(record, 'is_encrypted', 0) === 1;
    }

    //change table
    async readBills(params){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'rechargeNumber',self.encryptHelper.encryptData(params.rechargeNumber));
            let result = await self.readBillsDefault(copyParams);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                return result;
            }
        }
        return await self.readBillsDefault(params);
    }

    async readBillsWithoutCustId(params){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'rechargeNumber',self.encryptHelper.encryptData(params.rechargeNumber));
            let result = await self.readBillsWithoutCustIdDefault(copyParams);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                return result;
            }
        }
        return await self.readBillsWithoutCustIdDefault(params);
    }

    async readBillsWithoutCustIdDefault(params){
        const self = this;
        let query = `SELECT * FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number = ? LIMIT 50`;
        let queryParams = [params.rechargeNumber];
        if (params.isDuplicateCANumberOperator && params.alternateRechargeNumber) {
            [query, queryParams] = self.getQueryAndParamsForDuplicateCANumberOperator(params);
        }
        self.logger.log(`readBillsWithoutCustIdDefault :: query: ${query}, queryParams:`,queryParams, _.get(params, 'service', null));
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.logger.log(`nonPaytmBills::readBills fetched ${result.rows.length} for `, params, _.get(params, 'service', null));
                if(params.isDuplicateCANumberOperator && params.alternateRechargeNumber){
                    let alternateCAExistsInDB = result.rows.filter((dataValue) => dataValue.recharge_number == params.alternateRechargeNumber).length > 0 ? true : false;
                    _.set(params, 'alternateCAExistsInDB', alternateCAExistsInDB);
                    result.rows = result.rows.filter((dataValue) => dataValue.recharge_number == params.rechargeNumber)
                }
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
            })
        })
    }
    async readBillsDefault(params){
        const self = this;
        let query = `SELECT * FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number = ? AND customer_id = ? \
                        AND service = ? AND operator = ?`;
        let queryParams = [params.rechargeNumber, params.customerId, params.service, params.operator];
        if (params.isDuplicateCANumberOperator && params.alternateRechargeNumber) {
            [query, queryParams] = self.getQueryAndParamsForDuplicateCANumberOperator(params);
        }
        self.logger.log(`readBillsDefault :: query: ${query}, queryParams:`,queryParams, _.get(params, 'service', null));
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.logger.log(`nonPaytmBills::readBills fetched ${result.rows.length} for `, params, _.get(params, 'service', null));
                if(params.isDuplicateCANumberOperator && params.alternateRechargeNumber){
                    let alternateCAExistsInDB = result.rows.filter((dataValue) => dataValue.recharge_number == params.alternateRechargeNumber).length > 0 ? true : false;
                    _.set(params, 'alternateCAExistsInDB', alternateCAExistsInDB);
                    result.rows = result.rows.filter((dataValue) => dataValue.recharge_number == params.rechargeNumber)
                }
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
        })
        })
    }

    async readBillsWithoutCustId(params) {
        const self = this;
        if (self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams, 'rechargeNumber', self.encryptHelper.encryptData(params.rechargeNumber));
            let result = await self.readBillsWithoutCustIdDefault(copyParams);
            if (result && result.length > 0) {
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                return result;
            }
        }
        return await self.readBillsWithoutCustIdDefault(params);
    }

    async readBillsWithoutCustIdDefault(params) {
        const self = this;
        let query = `SELECT * FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number = ? LIMIT 50`;
        let queryParams = [params.rechargeNumber];
        if (params.isDuplicateCANumberOperator && params.alternateRechargeNumber) {
            [query, queryParams] = self.getQueryAndParamsForDuplicateCANumberOperator(params);
        }
        self.logger.log(`readBillsWithoutCustIdDefault :: query: ${query}, queryParams:`, queryParams, _.get(params, 'service', null));
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare: true })
                .then(result => {
                    this.logger.log(`nonPaytmBills::readBills fetched ${result.rows.length} for `, params, _.get(params, 'service', null));
                    if (params.isDuplicateCANumberOperator && params.alternateRechargeNumber) {
                        let alternateCAExistsInDB = result.rows.filter((dataValue) => dataValue.recharge_number == params.alternateRechargeNumber).length > 0 ? true : false;
                        _.set(params, 'alternateCAExistsInDB', alternateCAExistsInDB);
                        result.rows = result.rows.filter((dataValue) => dataValue.recharge_number == params.rechargeNumber)
                    }
                    resolve(result.rows);
                })
                .catch(error => {
                    reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
                })
        })
    }

    getQueryAndParamsForDuplicateCANumberOperator(params){
        const self = this;
        let
            query = `SELECT * FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number IN (?,?) AND customer_id = ? \
        AND service = ? AND operator = ?`,
            queryParams = [params.rechargeNumber, params.alternateRechargeNumber, params.customerId, params.service, params.operator];
        return [query, queryParams];
    }

    async readBillsWithOperators(params,operators){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'rechargeNumber',self.encryptHelper.encryptData(params.rechargeNumber));
            let result = await self.readBillsWithOperatorsDefault(copyParams, operators);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                self.L.log(`[nonPaytmBills.readBillsWithOperators Default] found encrypted returning decrypted ${JSON.stringify(result)}`);
                return result;
            }
        }
        return await self.readBillsWithOperatorsDefault(params, operators);
    }



    async readBillsWithOperatorsDefault(params,operators){
        const self = this;
        const operatorArr = new Array();
        if(operators.length < 1){
            operators.push(params.operator);
        }
        const query = `SELECT * FROM ${self.nonPaytmBillsTable} WHERE recharge_number = ? AND customer_id = ? \
                        AND service = ? AND operator IN ?`;
        const queryParams = [params.rechargeNumber, params.customerId, params.service, operators];
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.L.log(`nonPaytmBills::readBills fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
        })
        })
    }

    async readGenAndDueBills(params, dueGenFlag){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customer_id)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'recharge_number',self.encryptHelper.encryptData(params.recharge_number));
            let result = await self.readGenAndDueBillsDefault(copyParams, dueGenFlag, true);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customer_id);
                return result;
            }
        }
        return await this.readGenAndDueBillsDefault(params, dueGenFlag);
    }

    async readGenAndDueBillsDefault(params, dueGenFlag, isCCEncryptionEnabled = false){
        const self = this;
        let tableName;
        let query, queryParams;
        let flag = true;
        if(dueGenFlag == 0) {
            let billDateTimeStamp = _.get(params,'bill_date',null);
            billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
            let monthName = this.getMonthName(billDateTimeStamp);
            let yearName = this.yearName(billDateTimeStamp);
            tableName = self.tableResolver.getBillGenTablePrefix(_.get(params,'customer_id',null)) + monthName + '_' + yearName;
            if(this.checkTimestampFlag(billDateTimeStamp) == false) {
                flag = false;
            }
            let bucketName = await this.generateBucketNameFromTimestampAndHash(_.get(params,'customer_id',null), _.get(params,'recharge_number',null), billDateTimeStamp);
            query = `SELECT * FROM ${tableName} WHERE operator = ? AND service = ? \
                        AND bill_date = ? AND bucket_id = ? AND recharge_number = ? AND customer_id = ?`;
            queryParams = [_.get(params,'operator',null), _.get(params,'service',null), billDateTimeStamp, bucketName, _.get(params,'recharge_number',null),_.get(params,'customer_id',null) ];
        }else {

            let dueDateTimeStamp = _.get(params,'due_date',null);
            dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
            let monthName = this.getMonthName(dueDateTimeStamp);
            let yearName = this.yearName(dueDateTimeStamp);
            tableName = self.tableResolver.getBillDueTablePrefix(_.get(params,'customer_id',null),_.get(params,'service',null),_.get(params,'paytype',null), !isCCEncryptionEnabled) +monthName+'_'+yearName;
            if(this.checkTimestampFlag(dueDateTimeStamp) == false) {
                flag = false;
            }
            let bucketName = await this.generateBucketNameFromTimestampAndHash(_.get(params,'customer_id',null), _.get(params,'recharge_number',null), dueDateTimeStamp);
            let ccbpDueDateTimestamp = dueDateTimeStamp;
            if(isCCEncryptionEnabled) {
                ccbpDueDateTimestamp = self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled);
                ccbpDueDateTimestamp = self.encryptHelper.encryptData(ccbpDueDateTimestamp);
            }

            query = `SELECT * FROM ${tableName} WHERE operator = ? AND service = ? \
                        AND due_date = ? AND bucket_id = ? AND recharge_number = ? AND customer_id = ?`;
            queryParams = [_.get(params,'operator',null), _.get(params,'service',null), isCCEncryptionEnabled ? ccbpDueDateTimestamp : dueDateTimeStamp, bucketName, _.get(params,'recharge_number',null),_.get(params,'customer_id',null) ];
        }
        if(flag == false){
            return null;
        }

        this.L.log(`nonPaytmBills::readGenAndDueBills, customer_id : ${params.customer_id}, tableName : ${tableName}`);


        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.logger.log(`nonPaytmBills::readGenAndDueBills fetched ${result.rows.length} for`, params, _.get(params, 'service', null));
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readGenAndDueBills DB exception!' + JSON.stringify(queryParams) + error));
        })
        })
    }

    async readBillsWithRechargeNumber(params){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'rechargeNumber',self.encryptHelper.encryptData(params.rechargeNumber));
            self.L.log(`[nonPaytmBills.readBillsWithRechargeNumberDefault] encrypted copyParams ${JSON.stringify(copyParams)}`);
            let result = await self.readBillsWithRechargeNumberDefault(copyParams);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                self.L.log(`[nonPaytmBills.readBillsWithRechargeNumberDefault] found encrypted returning decrypted ${JSON.stringify(result)}`);
                return result;
            }
        }
        return await this.readBillsWithRechargeNumberDefault(params);
    }

    async readBillsWithRechargeNumberDefault(params){
        const self = this;
        const query = `SELECT customer_id, service, operator, product_id FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number = ?`;
        const queryParams = [params.rechargeNumber];
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.L.log(`nonPaytmBills::readBills fetched ${result.rows.length} for ${JSON.stringify(params)}`);
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readBills DB exception!' + JSON.stringify(queryParams) + error));
        })
        })
    }

    async readRecentBills(params){
        const self = this;
        const query = `select customer_id,rn_count from ${self.tableResolver.getNewBillsRecentTable(params.customerId)} WHERE customer_id = ? AND service = ? \
            AND paytype = ? AND operator = ?`;
        const queryParams = [params.customerId, params.service, params.paytype, params.operator];

        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.logger.log(`nonPaytmBills::readRecentBills fetched ${result.rows.length} for`, params, _.get(params, 'service', null));
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readRecentBills DB exception!' + JSON.stringify(queryParams) + error));
        })
        })
    }

    async readRecentRecords(params, cb){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'rechargeNumber',self.encryptHelper.encryptData(params.rechargeNumber));
            self.L.log(`[nonPaytmBills.readRecentRecordsDefault] encrypted copyParams ${JSON.stringify(copyParams)}`);
            let result = await self.readRecentRecordsDefault(copyParams, cb);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                self.L.log(`[nonPaytmBills.readRecentRecordsDefault Default] found encrypted returning decrypted ${JSON.stringify(result)}`);
                return result;
            }
        }
        return this.readRecentRecordsDefault(params, cb);
    }

    async readRecentRecordsDefault(params, cb){
        const self = this;
        let query = `SELECT recharge_number FROM ${self.tableResolver.getNewBillsRecentTable(params.customerId)} WHERE customer_id = ? AND service = ? `;
        let queryParams = [params.customerId, params.service];

        if(params.paytype){
            query += 'AND paytype = ?';
            queryParams.push(params.paytype)
        }

        if(params.operator){
            query += 'AND operator = ?';
            queryParams.push(params.operator)
        }


        ASYNC.waterfall([
            next => {
                self.client.execute(query, queryParams,  { prepare : true }, (err, result) => {
                    if(!err){
                        return next(null, result.rows)
                    }
                    return next('nonPaytmBills::readRecentRecords DB exception!' + JSON.stringify(params) + err);
                })
            },
            (resultRows, next) => {
                if(resultRows.length == 0){
                    return next(null, [])
                }
                let rechargeNumbers = new Array();
                resultRows.forEach(row => {
                    let numbers = _.get(row, 'recharge_number', '[]')
                    if(!Array.isArray(numbers)){
                        return;
                    }
                    rechargeNumbers.push(...numbers);
                });

                return next(null, rechargeNumbers);
            },
            (rechargeNumbers, next) => {
                if(rechargeNumbers.length < 1){
                    return cb(null, [])
                }
                let query = `SELECT * FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number IN ? AND customer_id = ? `
                self.client.execute(query, [rechargeNumbers, params.customerId], { prepare : true }, (err, result) => {
                    if(!err){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SUCCESS','SOURCE:API_RECENT_READ']);
                        return cb(null, result.rows);
                    }
                    return cb('nonPaytmBills::readRecentRecords DB exception!' + JSON.stringify(queryParams) + err);
                })
            }
        ], (error) => {
            if(error){
                self.L.error(`readRecentRecords`, `Failed with error ${error} for data ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:API_RECENT_READ']);
                return cb(error)
            }
            return;
        })
    }

    async readFallbackCustomerId(params){
        const self = this;
        const query = `SELECT * FROM ${self.nonPaytmFallbackCustomerIdTable} WHERE customer_id = ?`;
        const queryParams = [params.customerId];
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result => {
                this.L.log(`nonPaytmBills::readFallbackCustomerId fetched ${result.rows.length} for ${JSON.stringify(params)}`);
                resolve( result.rows);
            })
            .catch(error => {
                reject(new Error('nonPaytmBills::readFallbackCustomerId DB exception!' + JSON.stringify(queryParams) + error));
            })
        })
    }

    async insertFallbackCustomerId(params){
        const self = this;
        const date = self.getDate();
        let query = `INSERT INTO ${self.nonPaytmFallbackCustomerIdTable} (customer_id, create_at, update_at) VALUES (?, ?, ?)`;
        const queryParams = [params.customerId, date, date];

        return new Promise((resolve, reject) => {
            this.client.execute(query, queryParams, {prepare: true})
            .then(() => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:INSERTED','SOURCE:INSERT_FALLBACK_CUSTOMER_ID']);
                self.L.log(`nonPaytmBills::INSERT_FALLBACK_CUSTOMER_ID :: Data updated on cluster for ${JSON.stringify(params)}`);
                resolve(null);
            })
            .catch(error => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:FAILED_INSERT','SOURCE:INSERT_FALLBACK_CUSTOMER_ID']);
                self.L.error(`nonPaytmBills::INSERT_FALLBACK_CUSTOMER_ID Record not updated for ${JSON.stringify(params)}, error ${error}`);
                reject(`nonPaytmBills::INSERT_FALLBACK_CUSTOMER_ID Record not updated for ${JSON.stringify(params)}, error ${error}`);
            });
        });
    }

    async deleteUsingRecentRecords(params, cassandraCdcPublisher, cb, bypassEncryption = false){
        const self = this;
        let rnCountMap = {};

        let query = `SELECT recharge_number,rn_count,is_encrypted FROM ${self.tableResolver.getNewBillsRecentTable(params.customerId)} WHERE customer_id = ? AND service = ? `;
        let queryParams = [params.customerId, params.service];

        if(params.paytype){
            query += 'AND paytype = ?';
            queryParams.push(params.paytype)
        }

        if(params.operator){
            query += 'AND operator = ?';
            queryParams.push(params.operator)
        }

        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);

        ASYNC.waterfall([
            next => {
                self.client.execute(query, queryParams,  { prepare : true }, (err, result) => {
                    if(!err){
                        return next(null, result.rows)
                    }
                    return next('nonPaytmBills::deleteUsingRecentRecords DB exception!' + JSON.stringify(params) + err);
                })
            },
            (resultRows, next) => {
                if(resultRows.length == 0){
                    return next('No matching record')
                }
                let rechargeNumbers = new Array();
                let is_encrypted_row = false;
                resultRows.forEach(row => {
                    let numbers = _.get(row, 'recharge_number', '[]')
                    if(!Array.isArray(numbers)){
                        return;
                    }
                    rechargeNumbers.push(...numbers);

                    let rnCountMapKey = self.generateRnCountMapKey(params);

                    if(rnCountMapKey){
                        if(!rnCountMap[rnCountMapKey]){
                            rnCountMap[rnCountMapKey] = 0;
                        }
                        rnCountMap[rnCountMapKey] += numbers.length;
                    }
                    is_encrypted_row = _.get(row, 'is_encrypted', false);
                });

                return next(null, rechargeNumbers, is_encrypted_row);
            },
            (rechargeNumbers, is_encrypted_row, next) => {
                rechargeNumbers = rechargeNumbers.filter(num => {
                    if(params.service == 'mobile' || params.service == 'dth') {
                        return num == params.rechargeNumber;
                    }
                    if(params.service == 'loan' || params.service == 'rent payment' ){
                        return true;
                    }

                    const subst = `$1`;
                    const regex = /(\d)\s+(?=\d)/g;
                    let spaceContainingNumber = params.rechargeNumber;
                    //decrypt the number for CCBP
                    if(!bypassEncryption && isCCEncryptionEnabled && is_encrypted_row) {

                        self.L.log("nonPaytmBills::deleteUsingRecentRecords :: encrypted RN " + num + " spaceContainingNumber = " + spaceContainingNumber);
                        let decrypted_num = self.encryptHelper.decryptData(num);
                        if(decrypted_num) {
                            num = decrypted_num;
                        }
                    }
                    let spaceFreeNumber = spaceContainingNumber.replace(regex, subst);
                    let digitsToMatch;
                    try{
                      digitsToMatch=num.match(/\d+/g).pop();// XXXX XXXX XXXX 5412 will give 5412
                    }
                    catch(error){
                        self.L.error(`nonPaytmBills::deleteUsingRecentRecords digitsToMatch ${num}, error ${error}`);
                        return false;
                    }
                    params.rechargeNumber=spaceFreeNumber;
                    return digitsToMatch == params.rechargeNumber.substr(-digitsToMatch.length) //match 12 match with last 2 digits of XXXX XXXX XXXX 2212 using above case
                });
                if(rechargeNumbers.length < 1){
                    return next('Matching records not found')
                }
                const billsQuery = `DELETE FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number IN ? AND customer_id = ? \
                AND service = ? AND operator = ?`;
                const billsQueryParams = [rechargeNumbers, params.customerId, params.service, params.operator];
                this.processRecordsforDeletion(rechargeNumbers, params, bypassEncryption);

                let rnCountMapKey = self.generateRnCountMapKey(params);

                let rnCount = rnCountMap[rnCountMapKey] ;
                if(rnCount && rnCount > 0){
                    rnCount = rnCount-1;
                } else
                    rnCount = 0;


                // let recentsQuery = `DELETE recharge_number [?] FROM ${self.tableResolver.getNewBillsRecentTable(params.customerid)} WHERE customer_id = ? AND service = ?`;
                let recentsQuery = `UPDATE ${self.tableResolver.getNewBillsRecentTable(params.customerId)} SET recharge_number = recharge_number - ?, update_at = ?, rn_count = ?  WHERE customer_id = ? AND service = ?`
                let date = this.getDate();
                let recentsQueryParams = [rechargeNumbers, date, rnCount, params.customerId, params.service];

                if(params.paytype){
                    recentsQuery += ' AND paytype = ?';
                    recentsQueryParams.push(params.paytype)
                }

                if(params.operator){
                    recentsQuery += ' AND operator = ?';
                    recentsQueryParams.push(params.operator)
                }


                if(params.service == 'loan'){
                    recentsQuery = `DELETE FROM ${self.tableResolver.getNewBillsRecentTable(params.customerId)} where customer_id = ? AND service = ? AND paytype = ? AND operator = ?`;
                    recentsQueryParams = [params.customerId, params.service, params.paytype, params.operator];
                }

                let bucketId = _.get(params,'bucketId',_.get(params,'bucket_id',null));

                const deleteQuery = `DELETE FROM ${self.nonPaytmBillFetch} WHERE operator = ? AND service = ? \
                        AND bucket_id = ? AND next_bill_fetch_date = ? AND customer_id = ? AND recharge_number = ?`;
                const deleteQueryParams = [
                    _.get(params,'operator',_.get(params,'operator',null)),
                    _.toLower(_.get(params,'service',_.get(params,'service',null))),
                    bucketId,
                    params.nextBillFetchDate,
                    _.get(params,'customerId',_.get(params,'customer_id',null)),
                    _.get(params,'rechargeNumber',_.get(params,'recharge_number',null))
                ];

                const batchQuery = [
                    {
                        query: billsQuery,
                        params: billsQueryParams
                    },
                    {
                        query: recentsQuery,
                        params: recentsQueryParams
                    }
                ]

                if(params.nextBillFetchDate != null && bucketId){
                    batchQuery.push({
                        query: deleteQuery,
                        params: deleteQueryParams
                    })
                }

                self.client.batch(batchQuery, { prepare: true })
                .then(() => {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SUCCESS','SOURCE:DELETE_TRX_DATA']);
                    self.logger.log(`deleteUsingRecentRecords Data deleted on cluster for`, params, _.get(params, 'service', null));
                    params.cdcEventType = 'd';
                    params.updateAt = date;
                    params.cdcPayload = 'y';
                    return Promise.resolve();
                })
                .then(() =>{
                    self.publishCassandraCDCvents((err, data) => {
                        if (err) {
                            self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing: ${err}`);
                            reject(err);
                        }
                        else
                            return next(null);
                    }, params, cassandraCdcPublisher)
                })
                .catch(error => {
                    if(error == "Error in publishing to CDC_RECOVERY Topic"){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:WRITE_BATCH_RECORDS']);
                        return next(error);
                    }
                    else{
                        return next('nonPaytmBills::deleteUsingRecentRecords DB exception!' + JSON.stringify(queryParams) + error);
                    }
                })
            }
        ], (error) => {
            if(error){
                self.logger.error(`deleteUsingRecentRecords Not deleting record with error ${error} for data`, params, _.get(params, 'service', null));
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:DELETE_TRX_DATA']);
                return cb()
            }
            else{
                params.updateAt=this.getDate();
                params.cdcEventType='d';
                return cb(params);
            }
        })
    }


    async generateRnCountMapKey(params) {
        let rnCountMapKey = params.service;
        if (params.paytype) {
            rnCountMapKey = `${rnCountMapKey}_${params.paytype}`;
        }
        if (params.operator) {
            rnCountMapKey = `${rnCountMapKey}_${params.operator}`;
        }
        return rnCountMapKey;
    }


    async deleteRecentAndBillsRecords(params, cb){
        const self = this;
        ASYNC.waterfall([
            next => {
                const billsQuery = `DELETE FROM ${self.nonPaytmBillsTable} WHERE recharge_number = ? AND customer_id = ? \
                AND service = ? AND operator = ?`;
                const billsQueryParams = [params.recharge_number, params.customer_id, params.service, params.operator];
                // let recentsQuery = `DELETE recharge_number [?] FROM ${self.nonPaytmRecentsTable} WHERE customer_id = ? AND service = ?`;
                const recentsQuery = `DELETE FROM ${self.nonPaytmRecentsTable} where customer_id = ? AND service = ? AND paytype = ? AND operator = ?`;
                const  recentsQueryParams = [params.customer_id, params.service, params.paytype, params.operator];
                const batchQuery = [
                    {
                        query: billsQuery,
                        params: billsQueryParams
                    },
                    {
                        query: recentsQuery,
                        params: recentsQueryParams
                    }
                ]
                self.client.batch(batchQuery, { prepare: true })
                .then(() => {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SUCCESS','SOURCE:DELETE_TRX_DATA']);
                    self.L.log(`deleteRecentAndBillsRecords Data deleted on cluster for ${JSON.stringify(params)}`);
                    cb();
                })
                .catch(error => {
                    if(error == "Error in publishing to CDC_RECOVERY Topic"){
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:WRITE_BATCH_RECORDS']);
                        return next(error);
                    }
                    else{
                        return next('nonPaytmBills::deleteRecentAndBillsRecords DB exception!' + JSON.stringify(batchQuery) + error);
                    }
                })
            }
        ], (error) => {
            if(error){
                self.L.log(`deleteRecentAndBillsRecords`, `Not deleting record with error ${error} for data ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:DELETE_TRX_DATA']);
                return cb()
            }
        })
    }

    /** No change is required, as it is fetching the recharge_number based on cust_id and then deleting */
    async checkInRecentAndDelete(params, cassandraCdcPublisher, cb){
        const self = this;
        let query = `SELECT recharge_number FROM ${self.tableResolver.getNewBillsRecentTable(params.customerId)} WHERE customer_id = ? AND service = ? `;
        let queryParams = [params.customerId, params.service];

        if(params.paytype){
            query += 'AND paytype = ?';
            queryParams.push(params.paytype)
        }

        if(params.operator){
            query += 'AND operator = ?';
            queryParams.push(params.operator)
        }

        ASYNC.waterfall([
            next => {
                self.client.execute(query, queryParams,  { prepare : true }, (err, result) => {
                    if(!err){
                        return next(null, result.rows)
                    }
                    return next('nonPaytmBills::checkInRecentAndDelete DB exception!' + JSON.stringify(params) + err);
                })
            },
            (resultRows, next) => {
                if(resultRows.length == 0){
                    return next('No matching record')
                }
                let rechargeNumbers = new Array();
                //let rechargeNumbers = ["110480110","113584601","114253889"];
                resultRows.forEach(row => {
                    let numbers = _.get(row, 'recharge_number', '[]')
                    if(!Array.isArray(numbers)){
                        return;
                    }
                    rechargeNumbers.push(...numbers);
                });

                return next(null, rechargeNumbers);
            },
            (rechargeNumbers, next) => {
                const billsQuery = `DELETE FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number IN ? AND customer_id = ? \
                AND service = ? AND operator = ?`;
                const billsQueryParams = [rechargeNumbers, params.customerId, params.service, params.operator];
                this.processRecordsforDeletion(rechargeNumbers, params);

                let recentsQuery = `DELETE FROM ${self.tableResolver.getNewBillsRecentTable(params.customerId)} where customer_id = ? AND service = ? AND paytype = ? AND operator = ?`;
                let recentsQueryParams = [params.customerId, params.service, params.paytype, params.operator];

                const batchQuery = [
                    {
                        query: billsQuery,
                        params: billsQueryParams
                    },
                    {
                        query: recentsQuery,
                        params: recentsQueryParams
                    }
                ]
                self.client.batch(batchQuery, { prepare: true })
                .then(() => {
                    return Promise.resolve();
                })
                .then(() =>{
                    self.publishCassandraCDCvents((err, data) => {
                        return next(null);
                    }, params, cassandraCdcPublisher)
                })
                .catch(error => {
                    return next('nonPaytmBills::checkInRecentAndDelete DB exception!' + JSON.stringify(queryParams) + error);
                })
            }
        ], (error) => {
            if(error){
                self.L.log(`checkInRecentAndDelete`, `Not deleting record with error ${error} for data ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPDATE_NON_PAYTM_DATA']);
                if(error == "Error in publishing to CDC_RECOVERY Topic"){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:checkInRecentAndDelete']);
                    resolve(null)
                }
                else{
                    return cb(error);
                }
            }else{
                self.L.log(`checkInRecentAndDelete`, `Deleting record for data ${JSON.stringify(params)}`);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:SUCCESS','SOURCE:UPDATE_NON_PAYTM_DATA']);
                return cb(null);
            }
        })
    }

    async processRecordWithDifferentMonthDue(params) {
        const self = this;
        let dueDateTimeStamp = _.get(params,'due_date',null);
        dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
        let monthName = this.getMonthName(dueDateTimeStamp);
        let yearName = this.yearName(dueDateTimeStamp);
        let tableName = self.tableResolver.getBillDueTablePrefix(_.get(params,'customer_id',null),_.get(params,'service',null),_.get(params,'paytype',null)) +monthName+'_'+yearName;
        let bucketName = await this.generateBucketNameFromTimestampAndHash(_.get(params,'customer_id',null), _.get(params,'recharge_number',null), dueDateTimeStamp);
        let query = this.generateUpdateQuery(tableName, 1);

        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let ccbpDueDateTimestamp = dueDateTimeStamp;
        let rechargeNumber = _.get(params,'recharge_number',null);
        if(isCCEncryptionEnabled) {
            ccbpDueDateTimestamp = self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled);
            ccbpDueDateTimestamp = self.encryptHelper.encryptData(ccbpDueDateTimestamp);
            rechargeNumber = self.encryptHelper.encryptData(rechargeNumber);
            self.L.log(`[nonPaytmBills.processRecordWithDifferentMonthDue] ccbpDueDateTimestamp = ${ccbpDueDateTimestamp} enc rechargeNumber = ${rechargeNumber}`);
        }
        let queryParams = [_.get(params,'operator',null), _.get(params,'service',null), isCCEncryptionEnabled ? ccbpDueDateTimestamp : dueDateTimeStamp, bucketName, rechargeNumber,_.get(params,'customer_id',null) ];
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, queryParams, { prepare : true })
            .then(result =>{
            this.L.log(`nonPaytmBills::processRecordWithDifferentMonthDue updated ${result.rows.length} for ${JSON.stringify(params)}`);
            resolve( result.rows);
        })
        .catch(error=>{
            reject(new Error('nonPaytmBills::processRecordWithDifferentMonthDue DB exception!' + JSON.stringify(queryParams) + error));
        })
    })

    }

    async processRecordWithDifferentMonthGen(params) {
        const self = this;
        let billDateTimeStamp = _.get(params,'bill_date',null);
        billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
        let monthName = this.getMonthName(billDateTimeStamp);
        let yearName = this.yearName(billDateTimeStamp);
        let tableName = self.tableResolver.getBillGenTablePrefix(_.get(params,'customer_id',null)) + monthName + '_' + yearName;
        let bucketName = await this.generateBucketNameFromTimestampAndHash(_.get(params,'customer_id',null), _.get(params,'recharge_number',null), billDateTimeStamp);
        let query = this.generateUpdateQuery(tableName, 0);
        let queryParams = [_.get(params,'operator',null), _.get(params,'service',null), billDateTimeStamp, bucketName, _.get(params,'recharge_number',null),_.get(params,'customer_id',null) ];
        return new Promise((resolve, reject) => {
            self.notificationNewClusterClient.execute(query, queryParams, { prepare : true })
            .then(result =>{
            this.L.log(`nonPaytmBills::processRecordWithDifferentMonthGen updated ${result.rows.length} for ${JSON.stringify(params)}`);
            resolve( result.rows);
        })
        .catch(error=>{
            reject(new Error('nonPaytmBills::processRecordWithDifferentMonthGen DB exception!' + JSON.stringify(queryParams) + error));
        })
    })

    }

    async processRecordsforDeletion(rechargeNumbers, params, bypassEncryption = false) {
        const self = this;
        for(let i=0; i<rechargeNumbers.length; i++) {
            let recharge_number = rechargeNumbers[i];
            params.rechargeNumber = recharge_number;
            let existingRecord = null;
            if(bypassEncryption) {
                existingRecord = await this.readBillsDefault(params);
            } else {
                existingRecord = await this.readBills(params);
            }

            if(existingRecord && existingRecord.length == 1 && _.get(existingRecord[0],'due_date',null) != null){
                let dueDateTimeStamp = _.get(existingRecord[0], 'due_date', null);
                dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
                let monthName = this.getMonthName(dueDateTimeStamp);
                let yearName = this.yearName(dueDateTimeStamp);
                let tableName = self.tableResolver.getBillDueTablePrefix(params.customerId, params.service, params.paytype, bypassEncryption)+monthName+'_'+yearName;
                let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, recharge_number, dueDateTimeStamp);
                let query = this.generateUpdateQuery(tableName, 1);

                let customerId = _.get(existingRecord[0], 'customer_id', null);
                let service = _.get(existingRecord[0], 'service', null);
                let paytype = _.get(existingRecord[0], 'paytype', null);
                let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(service, paytype, customerId);
                let ccbpDueDateTimestamp = dueDateTimeStamp;
                if(!bypassEncryption && isCCEncryptionEnabled) {
                    ccbpDueDateTimestamp = self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled);
                    ccbpDueDateTimestamp = self.encryptHelper.encryptData(ccbpDueDateTimestamp);
                    recharge_number = self.encryptHelper.encryptData(recharge_number);
                    self.L.log(`[nonPaytmBills.processRecordsforDeletion] ccbpDueDateTimestamp = ${ccbpDueDateTimestamp} enc rechargeNumber = ${recharge_number}`);
                }

                let queryParams = this.generateQueryParamsForRecentAndDelete(params, ccbpDueDateTimestamp, bucketName, recharge_number);
                if(this.checkTimestampFlag(dueDateTimeStamp)==true){
                    await this.updateDueAndBillTable(params, query, queryParams);
                    self.L.log(`processRecordsforDeletion :: delete from non ru dueDate table :: deleted successfully`);
                }

            }
            if(existingRecord && existingRecord.length == 1 && _.get(existingRecord[0],'bill_date',null) != null){
                let billDateTimeStamp = _.get(existingRecord[0], 'bill_date', null);
                billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
                let monthName = this.getMonthName(billDateTimeStamp);
                let yearName = this.yearName(billDateTimeStamp);
                let tableName = self.tableResolver.getBillGenTablePrefix(params.customerId) + monthName+'_'+yearName;
                let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, params.rechargeNumber, billDateTimeStamp);
                let query = this.generateUpdateQuery(tableName, 0);
                let queryParams = this.generateQueryParamsForRecentAndDelete(params, billDateTimeStamp, bucketName, recharge_number);
                if(this.checkTimestampFlag(billDateTimeStamp)==true){
                    await this.updateDueAndBillTable(params, query, queryParams);
                    self.L.log(`processRecordsforDeletion :: query ${query} :: queryParams ${JSON.stringify(queryParams)}`);
                    self.L.log(`processRecordsforDeletion :: delete from non ru bill gen table :: deleted successfully`);
                }

            }

        }
    }
    async updateDueAndBillTable(params, query, queryParams) {
        const self = this;

        return new Promise((resolve, reject) => {
        self.notificationNewClusterClient.execute(query, queryParams, { prepare : true })
            .then(data => {
                resolve(data);
            })
            .catch(error => {
                self.L.critical("updateDueAndBillTable", "Error occurred while deleting record ", error, params);
                reject(error);
            })
        });
    }
    async readDueDateRecords(params){
        const self = this;
        const query = 'SELECT * FROM bills_due_date WHERE due_date > ? AND due_date < ? AND service = ? AND paytype = ? AND operator = ?';
        const queryParams = [params.minDueDate, params.maxDueDate, params.service, params.paytype, params.operator]
        const options = { fetchSize : 1000 };

        self.client.eachRow(query, queryParams, options,
        (tempRowNumer, rows) =>{
            // insert in kafka when needed directly
            // reminders not implemented in current scope of IN-20736
        },
        (err, result)=>{
            if(err){
                throw new Error('nonPaytmBills::readDueDateRecords DB exception!' + JSON.stringify(queryParams) + error);
            }
            if(result.nextPage) { // true if cassandra has more results
                setTimeout(() => {
                    self.L.log(`nonPaytmBills::readDueDateRecords fetching another batch of data`);
                    result.nextPage();
                }, 5000)
            }
        })
    }

    processExistingRecord(billsKafkaRow, existingRecord){
        billsKafkaRow.amount = existingRecord.amount;
        billsKafkaRow.bankName = _.get(existingRecord,'bank_name','')
        billsKafkaRow.billDate = _.get(existingRecord,'bill_date','')
        billsKafkaRow.billFetchDate = _.get(existingRecord,'bill_fetch_date','')
        billsKafkaRow.cardNetwork = _.get(existingRecord,'card_network','')
        billsKafkaRow.circle = _.get(existingRecord,'circle','')
        billsKafkaRow.createAt = _.get(existingRecord,'create_at',null)
        billsKafkaRow.customerEmail = _.get(existingRecord,'customer_email','')
        billsKafkaRow.customerMobile = _.get(existingRecord,'customer_mobile','')
        billsKafkaRow.customerOtherInfo = _.get(existingRecord,'customer_other_info','')
        billsKafkaRow.dueAmount = _.get(existingRecord,'due_amount','')
        billsKafkaRow.dueDate = _.get(existingRecord,'due_date','')
        billsKafkaRow.extra = _.get(existingRecord,'extra','')
        billsKafkaRow.paymentDate = _.get(existingRecord,'payment_date','')
        billsKafkaRow.billDate = _.get(existingRecord,'bill_date','')
        billsKafkaRow.paytype = _.get(existingRecord,'paytype','')
        billsKafkaRow.productId = _.get(existingRecord,'product_id','')
        billsKafkaRow.userData = _.get(existingRecord,'user_data','')
        billsKafkaRow.status = existingRecord.status;
        billsKafkaRow.nextBillFetchDate = _.get(existingRecord,'next_bill_fetch_date',null);
        billsKafkaRow.nextBillFetchStartDate = _.get(existingRecord,'next_bill_start_date',null);
        billsKafkaRow.publishedDate = _.get(existingRecord,'published_date',null);
        billsKafkaRow.retryCount = _.get(existingRecord,'retry_count',null);
        billsKafkaRow.expiryDateStart = _.get(existingRecord,'expiry_date_start',null);
        billsKafkaRow.remindLaterDate = _.get(existingRecord,'remind_later_date',null);
        return billsKafkaRow;
    }

    processExistingRecordForAmount(billsKafkaRow, existingRecord){
        billsKafkaRow.bankName = _.get(existingRecord,'bank_name','')
        billsKafkaRow.billDate = _.get(existingRecord,'bill_date','')
        billsKafkaRow.billFetchDate = _.get(existingRecord,'bill_fetch_date','')
        billsKafkaRow.cardNetwork = _.get(existingRecord,'card_network','')
        billsKafkaRow.circle = _.get(existingRecord,'circle','')
        billsKafkaRow.createAt = _.get(existingRecord,'create_at',null)
        billsKafkaRow.customerEmail = _.get(existingRecord,'customer_email','')
        billsKafkaRow.customerMobile = _.get(existingRecord,'customer_mobile','')
        billsKafkaRow.dueDate = _.get(existingRecord,'due_date','')
        billsKafkaRow.notificationStatus = _.get(existingRecord,'notification_status','')
        billsKafkaRow.paytype = _.get(existingRecord,'paytype','')
        billsKafkaRow.productId = _.get(existingRecord,'product_id','')
        billsKafkaRow.userData = _.get(existingRecord,'user_data','')
        billsKafkaRow.dueAmount = billsKafkaRow.amount;
        billsKafkaRow.nextBillFetchDate = _.get(existingRecord,'next_bill_fetch_date',null);
        billsKafkaRow.nextBillFetchStartDate = _.get(existingRecord,'next_bill_start_date',null);
        billsKafkaRow.publishedDate = _.get(existingRecord,'published_date',null);
        billsKafkaRow.retryCount = _.get(existingRecord, 'retry_count', null);
        billsKafkaRow.expiryDateStart = _.get(existingRecord, 'expiry_date_start', null);
        billsKafkaRow.remindLaterDate = _.get(existingRecord, 'remind_later_date', null);
        return billsKafkaRow;
    }

    async insertData(billsKafkaRow, cassandraCdcPublisher) {
        let params = _.clone(billsKafkaRow)
        if(params.rechargeNumber == null && _.get(billsKafkaRow,'recharge_number',null))
            params.rechargeNumber = _.get(billsKafkaRow,'recharge_number',null);
        if(params.customerId == null && _.get(billsKafkaRow,'customer_id',null))
            params.customerId = _.get(billsKafkaRow,'customer_id',null);
        if(params.customerOtherInfo == null && _.get(billsKafkaRow,'customer_other_info',null))
            params.customerOtherInfo = _.get(billsKafkaRow,'customer_other_info',null);
        if(params.paymentDate == null && _.get(billsKafkaRow,'payment_date',null))
            params.paymentDate = _.get(billsKafkaRow,'payment_date',null);
        const self = this;
        let date = self.getDate();
        let extra = _.get(params, 'extra', {});
        let recon_id = utility.generateReconID(params.rechargeNumber, params.operator , _.get(params,'amount',0) , _.get(params, 'dueDate', null) ,  _.get(params, 'billDate', null))
        if(typeof extra == 'object'){
            _.set(extra , 'recon_id',recon_id)
            _.set(extra , 'user_type',"NON_RU")
            _.set(params , 'extra',extra)
            extra = JSON.stringify(extra);
        }else{
            extra = utility.setReconIdInString(extra , recon_id,"NON_RU")
            _.set(params , 'extra',extra)
        }

        const billsQuery = `INSERT INTO ${self.tableResolver.getNonRUTableName(params.customerId)} (
            recharge_number, 
            customer_id, 
            operator, 
            service, 
            product_id, 
            bill_date, 
            paytype, 
            circle, 
            customer_mobile, 
            customer_email, 
            amount, 
            status, 
            user_data, 
            notification_status, 
            due_date, 
            extra, 
            bank_name, 
            card_network, 
            customer_other_info, 
            bill_fetch_date, 
            due_amount, 
            create_at, 
            update_at, 
            payment_date,
            next_bill_fetch_date,
            next_bill_fetch_start_date,
            published_date,
            retry,
            expiry_date_start,
            remind_later_date,
            enc_amount, enc_due_date, is_encrypted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let rootLevelKeysToEncrypt = ['rechargeNumber', 'refId', 'customerMobile', 'customerEmail'];
        let encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt);
        const queryParams = self.generateQueryParamsForInsertQuery(encrypted_params, extra, isCCEncryptionEnabled);

        let dueDateCheck = _.get(params, 'dueDate', null);
        let billDateCheck = _.get(params, 'billDate', null);
        return new Promise((resolve, reject) => {
            self.L.log(`nonPaytmBills::INSERT_DATA :: query: ${billsQuery}, queryParams: ${JSON.stringify(queryParams)}`);
            this.client.execute(billsQuery, queryParams, { prepare: true })
                .then(() => {
                    if (dueDateCheck != null) {
                        this.insertDueDateMappingTableData(billsKafkaRow);
                    }
                    if (billDateCheck != null) {
                        this.insertBillDateMappingTableData(billsKafkaRow);
                    }
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:INSERTED', 'SOURCE:INSERT_DATA']);
                    self.logger.log("nonPaytmBills::INSERT_DATA :: Data updated on cluster for", params, _.get(params, 'service'));
                    params.cdcEventType = 'u';
                    params.updateAt = date;
                    params.convertPayload = 'y';
                    resolve(null)
                })
                .then(() => {
                    self.publishCassandraCDCvents((err, data) => {
                        if (err) {
                            self.logger.error(`nonPaytmBills::INSERT_DATA :: Error while executing: ${err}`, params, _.get(params, 'service', null));
                            reject(err);
                        }
                        else
                            resolve(params);
                    }, params, cassandraCdcPublisher)
                })
                .catch(error => {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:FAILED_INSERT', 'SOURCE:INSERT_DATA']);
                    if (JSON.stringify(error).includes('does not exist') && JSON.stringify(error).includes('table')) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", 'STATUS:TABLE_NOT_FOUND', 'SOURCE:INSERT_MODEL']);
                    }
                    if (error == "Error in publishing to CDC_RECOVERY Topic") {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:WRITE_BATCH_RECORDS']);
                        resolve(params)
                    }
                    else {
                        self.logger.error(`nonPaytmBills::INSERT_DATA Record not updated for error ${error}`, params, _.get(params, 'service', null));
                        reject(`nonPaytmBills::INSERT_DATA Record not updated for ${JSON.stringify(params)}, error ${error}`);
                    }
                })
        })
    }

    generateQueryParamsForInsertQuery(params, extra, isCCEncryptionEnabled = false) {
        const self = this;
        let date = this.getDate();

        const queryParams = [
            params.rechargeNumber,
            params.customerId,
            params.operator,
            _.toLower(params.service),
            _.get(params, 'productId', null),
            _.get(params, 'billDate', null),
            _.get(params, 'paytype', null),
            _.get(params, 'circle', null),
            _.get(params, 'customerMobile', null),
            _.get(params, 'customerEmail', null),
            isCCEncryptionEnabled ? null : _.get(params, 'amount', null),
            _.get(params, 'status', null),
            _.get(params, 'userData', null),
            _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
            isCCEncryptionEnabled ? null : _.get(params, 'dueDate', null),
            extra,
            _.get(params, 'bankName', null),
            _.get(params, 'cardNetwork', null),
            _.get(params, 'customerOtherInfo', null),
            _.get(params, 'billFetchDate', null),
            isCCEncryptionEnabled ? null : _.get(params, 'dueAmount', null),
            _.get(params,'createAt',date),
            date,
            _.get(params,'paymentDate',null),
            _.get(params,'nextBillFetchDate',null),
            _.get(params,'nextBillFetchStartDate',null),
            _.get(params,'publishedDate',null),
            _.get(params,'retryCount',0),
            _.get(params, 'expiryDateStart',null),
            _.get(params, 'remindLaterDate', null),
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.toString(_.get(params,'amount',0))) : null,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.get(params, 'dueDate', null)) : null,
            isCCEncryptionEnabled ? 1 : null
        ];

        return queryParams;

    }

    readBillFromRecentRecords(params) {
        const self = this;
        const query = `select * from ${self.tableResolver.getNewBillsRecentTable(params.customerId)} WHERE customer_id = ? AND service = ? AND paytype = ? AND operator = ?`;
        const queryParams = [params.customerId, params.service, params.paytype, params.operator];

        self.L.log(`readBillFromRecentRecords :: query: ${query}, queryParams: ${JSON.stringify(queryParams)}`);
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                this.logger.log(`nonPaytmBills::readBillFromRecentRecords fetched ${result.rows.length} for`, params, params.service);
                resolve( result.rows);
            })
            .catch(error=>{
                reject(new Error('nonPaytmBills::readBillFromRecentRecords DB exception!' + JSON.stringify(queryParams) + error));
            })
        })
    }

    insertInRecentRecordsTable(params, newRowInsertion = false, is_encrypted = 0) {
        let self = this;
        let query, queryParams;
        if(newRowInsertion == true) {
            query = `INSERT INTO ${self.tableResolver.getNewBillsRecentTable(params.customerId)} (customer_id, service, paytype, operator, recharge_number, is_encrypted, rn_count, create_at, update_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            queryParams = [params.customerId, params.service, params.paytype, params.operator, [params.rechargeNumber], is_encrypted, 1, self.getDate(), self.getDate()];
        } else {
            query = `UPDATE ${self.tableResolver.getNewBillsRecentTable(params.customerId)} SET recharge_number = recharge_number + ?, is_encrypted = ?, rn_count = ?, update_at = ? WHERE customer_id = ? AND service = ? AND paytype = ? AND operator = ?`;
            queryParams = [[params.rechargeNumber], is_encrypted, params.rnCount + 1, self.getDate() ,params.customerId, params.service, params.paytype, params.operator];
        }

        self.L.log(`insertInRecentRecordsTable :: query: ${query}, queryParams: ${JSON.stringify(queryParams)}`);
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                self.L.log(`insertInRecentRecordsTable :: record inserted query : ${query}, params : ${queryParams}`);
                resolve( result);
            })
            .catch(error=>{
                self.L.critical(`insertInRecentRecordsTable :: DB exception! for ${query}, ${JSON.stringify(queryParams)} : `, error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:INSERT_IN_RECENT_RECORDS_TABLE"]);
                reject(new Error('insertInRecentRecordsTable :: DB exception!' + JSON.stringify(queryParams) + error));
            })
        })

    }

    async insertRecordInBillsRecentRecords(params, cb, insertEncryptedRecord = false) {
        let self = this;
        try{
            let recordsInRecentsTable = await self.readBillFromRecentRecords(params);
            let encryptedRechargeNumber = self.encryptHelper.encryptData(params.rechargeNumber);
            let rechargeNumbersInDb = _.get(recordsInRecentsTable[0], 'recharge_number', '[]');
            let encryptedParams = _.cloneDeep(params);
            let is_encrypted = 0;
            if(insertEncryptedRecord == true) {
                is_encrypted = 1;
                _.set(encryptedParams, 'rechargeNumber', encryptedRechargeNumber);
            } else {
                is_encrypted = 0;
                encryptedRechargeNumber = params.rechargeNumber;
            }
            _.set(encryptedParams, 'rnCount', rechargeNumbersInDb.length);

            if(!Array.isArray(rechargeNumbersInDb)){
                //insert record in db
                await self.insertInRecentRecordsTable(encryptedParams, true, is_encrypted);
            } else if(rechargeNumbersInDb.length > 0) {

                //checking if encrypted record exists
                for(let index = 0; index< rechargeNumbersInDb.length; index++) {
                    if(rechargeNumbersInDb[index] == encryptedRechargeNumber) {
                        return cb(null);
                    }
                }
                //insert record in db
                await self.insertInRecentRecordsTable(encryptedParams, false, is_encrypted);
            } else {
                //insert record in db
                await self.insertInRecentRecordsTable(encryptedParams, true, is_encrypted);
            }
            return cb(null);
        } catch(e) {
            return cb(e);
        }
    }

    updateNonPaytmRecords(billsKafkaRow, cdcRecoveryPublisher, cb){// api changes
        const self = this;
        if(_.get(billsKafkaRow, 'paytype', null) == null) _.set(billsKafkaRow, 'paytype', _.get(self.config, ['CVR_DATA', billsKafkaRow.productId, 'paytype'],'').toLowerCase());
        let params = _.clone(billsKafkaRow),
            dbRecord;
        self.readBills(params)
        .then(existingRecord => {
            if(existingRecord.length == 1){
                dbRecord = existingRecord[0];
                self.L.log("updateNonPaytmRecords::  existing records found, inserting new data");
                billsKafkaRow =  self.processExistingRecord(billsKafkaRow, existingRecord[0])
                return Promise.resolve()
            }
            else{
                return Promise.reject("Record not found in DB");
            }
        })
        .then(() => {
            self.insertData(billsKafkaRow,cdcRecoveryPublisher).then(()=>{
                if(_.get(dbRecord, 'is_encrypted', 0) != 1 && self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
                    self.insertRecordInBillsRecentRecords(params, (err) => {
                        if(err) {
                            self.logger.error(`Error while executing: ${err} of record`, billsKafkaRow, params.service);
                            return cb(err);
                        }
                        self.deleteUsingRecentRecords(params, cdcRecoveryPublisher, (resp) => {
                            if(resp == undefined) {
                                return cb(err);
                            }
                            self.deleteBillsNonPaytmBillFetch((err,data)=>{
                                if(err){
                                    self.logger.error(`Error while executing: ${err} of record`, billsKafkaRow, params.service);
                                }
                                return cb(err);
                            },billsKafkaRow);
                        }, true);
                    }, true);
                } else {
                    self.deleteBillsNonPaytmBillFetch((err,data)=>{
                        if(err){
                            self.L.error(`Error while executing: ${err} of record ${JSON.stringify(billsKafkaRow)}`);
                        }
                        return cb(err);
                    },billsKafkaRow)
                }
            })
            .catch(error =>{
                self.L.error(`updateNonPaytmRecords`, `Failed with error ${error}`);

                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPDATE_NON_PAYTM_RECORDS','TYPE:DB_EXCEPTION']);
                return cb(error)
            })
        })
        .catch(error => {
            self.L.error(`updateNonPaytmRecords`, `Failed with error ${error}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','SOURCE:UPDATE_NON_PAYTM_RECORDS']);
            return cb(error)
        })
    }

    async updateAmountOfNonPaytmRecords(cb,billsKafkaRow,existingRecord,cassandraCdcPublisher){
        const self = this;
        let date = this.getDate();
        let params = _.clone(billsKafkaRow);
        try {
            const self = this;
            billsKafkaRow = await self.processExistingRecordForAmount(billsKafkaRow, existingRecord);
            await self.insertData(billsKafkaRow,cassandraCdcPublisher);
            let params = _.clone(billsKafkaRow);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:AMOUNT_UPDATED', 'SOURCE:UPDATEAMOUNT_NON_PAYTM_RECORDS']);
            return cb(null,params);
        }
        catch (error) {
            const self = this;
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPDATEAMOUNT_NON_PAYTM_RECORDS']);
            return cb(error);
        }
    }

    async updateAmountOfNonPaytmRecordsNew(billsKafkaRow, existingRecord, cassandraCdcPublisher) {
        const self = this;
        try {
            // Process the existing record for amount update
            billsKafkaRow = await self.processExistingRecordForAmount(billsKafkaRow, existingRecord);

            // Insert the updated record
            await self.insertDataNew(billsKafkaRow, cassandraCdcPublisher);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:AMOUNT_UPDATED', 'SOURCE:UPDATEAMOUNT_NON_PAYTM_RECORDS']);
            return;
        }
        catch (error) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:UPDATEAMOUNT_NON_PAYTM_RECORDS']);
            self.L.error(`nonPaytmBills::UPDATE_AMOUNT Record not updated for error ${error}`, billsKafkaRow);
            throw error;
        }
    }

    async insertDataNew(billsKafkaRow, cassandraCdcPublisher) {
        let params = this.prepareInsertDataParams(billsKafkaRow);
        const self = this;
        let date = self.getDate();
        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let rootLevelKeysToEncrypt = ['rechargeNumber', 'refId', 'customerMobile', 'customerEmail'];
        let encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt);

        const billsQuery = `INSERT INTO ${self.tableResolver.getNonRUTableName(params.customerId)} (
            recharge_number, 
            customer_id, 
            operator, 
            service, 
            product_id, 
            bill_date, 
            paytype, 
            circle, 
            customer_mobile, 
            customer_email, 
            amount, 
            status, 
            user_data, 
            notification_status, 
            due_date, 
            extra, 
            bank_name, 
            card_network, 
            customer_other_info, 
            bill_fetch_date, 
            due_amount, 
            create_at, 
            update_at, 
            payment_date,
            next_bill_fetch_date,
            next_bill_fetch_start_date,
            published_date,
            retry,
            expiry_date_start,
            remind_later_date,
            enc_amount, enc_due_date, is_encrypted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        let extra = _.get(params, 'extra', {});
        const queryParams = self.generateQueryParamsForInsertQuery(encrypted_params, extra, isCCEncryptionEnabled);
        return this.executeInsertDataQuery(billsQuery, queryParams, params, billsKafkaRow, cassandraCdcPublisher, date);
    }

    /**
     * Prepare parameters for insert data operation
     * @param {Object} billsKafkaRow - Kafka row data
     * @returns {Object} Prepared parameters
     */
    prepareInsertDataParams(billsKafkaRow) {
        let params = _.clone(billsKafkaRow)
        if (params.rechargeNumber == null && _.get(billsKafkaRow, 'recharge_number', null))
            params.rechargeNumber = _.get(billsKafkaRow, 'recharge_number', null);
        if (params.customerId == null && _.get(billsKafkaRow, 'customer_id', null))
            params.customerId = _.get(billsKafkaRow, 'customer_id', null);
        if (params.customerOtherInfo == null && _.get(billsKafkaRow, 'customer_other_info', null))
            params.customerOtherInfo = _.get(billsKafkaRow, 'customer_other_info', null);
        if (params.paymentDate == null && _.get(billsKafkaRow, 'payment_date', null))
            params.paymentDate = _.get(billsKafkaRow, 'payment_date', null);
        let extra = utility.parseExtra(_.get(params, 'extra', {}));
        let recon_id = utility.generateReconID(params.rechargeNumber, params.operator, _.get(params, 'amount', 0), _.get(params, 'dueDate', null), _.get(params, 'billDate', null))
        _.set(extra, 'recon_id', recon_id)
        _.set(extra, 'user_type', "NON_RU")
        _.set(params, 'extra', extra)
        params.extra = utility.stringifyExtra(extra);
        return params;
    }

    /**
     * Execute insert data query with proper error handling and metrics
     * @param {string} billsQuery - SQL query to execute
     * @param {Array} queryParams - Query parameters
     * @param {Object} params - Input parameters
     * @param {Object} billsKafkaRow - Kafka row data
     * @param {string} dueDateCheck - Due date check value
     * @param {string} billDateCheck - Bill date check value
     * @param {Object} cassandraCdcPublisher - CDC publisher
     * @param {string} date - Current date
     * @returns {Promise} Promise resolving when query is executed
     */
    async executeInsertDataQuery(billsQuery, queryParams, params, billsKafkaRow, cassandraCdcPublisher, date) {
        const self = this;
        var latencyStart = new Date().getTime();
        try {
            // self.L.log(`nonPaytmBills::INSERT_DATA :: query: ${billsQuery}, queryParams: ${JSON.stringify(queryParams)}`);
            await this.client.execute(billsQuery, queryParams, { prepare: true });
            self.L.log(`nonPaytmBills::INSERT_DATA :: query executed successfully for debugKey: ${_.get(params, 'debugKey', null)}`);
            //insert matrice data
        } catch (error) {
            self.L.error(`nonPaytmBills::INSERT_DATA :: Error while executing: ${error}`, params, _.get(params, 'service', null));
            //insert matrice data
            throw new Error(`nonPaytmBills::INSERT_DATA Record not updated for ${JSON.stringify(params)}, error ${error}`);
        }
        try {
            let dueDateCheck = _.get(params, 'dueDate', null);
            let billDateCheck = _.get(params, 'billDate', null);
            if (dueDateCheck != null) {
                await this.insertDueDateMappingTableData(billsKafkaRow);
            }
            if (billDateCheck != null) {
                await this.insertBillDateMappingTableData(billsKafkaRow);
            }
            return;
        } catch (error) {
            self.L.error(`nonPaytmBills::INSERT_DATA :: Error while executing: ${error} for debugKey: ${_.get(params, 'debugKey', null)}`);
            throw new Error(`nonPaytmBills::INSERT_DATA Record not updated error ${error} for debugKey: ${_.get(params, 'debugKey', null)}`);
        }
    }

    convertCdcRecovery(record) {
        const self = this;
        let debugKey = `rech_num:${_.get(record, 'recharge_number', _.get(record, 'rechargeNumber', null))}::operator:${record.operator}::productId:${_.get(record, 'product_id', _.get(record, 'productId', null))}::custId:${_.get(record, 'customer_id', _.get(record, 'customerId', null))}`;
        _.set(record, 'debugKey', debugKey);
        let customerId = _.get(record, 'customer_id', _.get(record, 'customerId', null));
        let service = _.get(record, 'service', null);
        let paytype = _.get(record, 'paytype', null);
        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(service, paytype, customerId);

        let cdcPayload = {
            'customerId': _.get(record, 'customer_id', _.get(record, 'customerId', null)),
            'rechargeNumber': _.get(record, 'recharge_number', _.get(record, 'rechargeNumber', null)),
            'productId': _.get(record, 'product_id', _.get(record, 'productId', null)),
            'operator': _.get(record, 'operator', null),
            'amount': _.get(record, 'amount', _.get(record, 'amount', null)),
            'dueDate': _.get(record, 'due_date', _.get(record, 'dueDate', null)),
            'billDate': _.get(record, 'bill_date', _.get(record, 'billDate', null)),
            'billFetchDate': _.get(record, 'bill_fetch_date', _.get(record, 'billFetchDate', null)),
            'nextBillFetchDate': _.get(record, 'next_bill_fetch_date', _.get(record, 'nextBillFetchDate', null)),
            'customerOtherInfo': _.get(record, 'customer_other_info', _.get(record, 'customerOtherInfo', null)),
            'paytype': _.get(record, 'paytype', null),
            'service': _.get(record, 'service', null),
            'categoryId': _.get(record, 'category_id', _.get(record, 'categoryId', null)),
            'customerMobile': _.get(record, 'customer_mobile', _.get(record, 'customerMobile', null)),
            'customerEmail': _.get(record, 'customer_email', _.get(record, 'customerEmail', null)),
            'notificationStatus': _.get(record, 'notification_status', _.get(record, 'notificationStatus', null)),
            'bankName': _.get(record, 'bank_name', _.get(record, 'bankName', null)),
            'cardNetwork': _.get(record, 'card_network', _.get(record, 'cardNetwork', null)),
            'status': _.get(record, 'status', null),
            'dbEvent': _.get(record, 'dbEvent', null),
            'debugKey': _.get(record, 'debugKey', null),
            'extra': _.get(record, 'extra', null),
            'circle': _.get(record, 'circle', null),
            'cdcEventType': _.get(record, 'cdcEventType', null),
            'paymentDate': _.get(record, 'payment_date', _.get(record, 'paymentDate', null)),
            'userData': _.get(record, 'user_data', _.get(record, 'userData', null)),
            'isAutomatic': _.get(record, 'is_automatic', _.get(record, 'isAutomatic', null)),
            'updateAt': _.get(record, 'updateAt', _.get(record, 'update_at', null)),
            'publishedDate': _.get(record, 'published_date', _.get(record, 'publishedDate', null)),
            'retry': _.get(record, 'retry_count', _.get(record, 'retryCount', null)),
            'nextBillFetchDate': _.get(record, 'next_bill_fetch_date', _.get(record, 'nextBillFetchDate', null)),
            'nextBillFetchStartDate': _.get(record, 'next_bill_fetch_start_date', _.get(record, 'nextBillFetchStartDate', null)),
            'dueAmount': _.get(record, 'amount', _.get(record, 'amount', null)),
            'createdAt': _.get(record, 'createAt', _.get(record, 'create_at',null))? MOMENT.utc(_.get(record, 'createAt', _.get(record, 'create_at',))).format('YYYY-MM-DD HH:mm:ss'):null,
            'remindLaterDate': _.get(record, 'remindLaterDate', null),
            'remindLaterFlow': _.get(record, 'remindLaterFlow', null),
            'oldBillFetchDate': _.get(record, 'oldBillFetchDate', null),
            'is_encrypted' : isCCEncryptionEnabled ? 1 : 0
        };
        return cdcPayload;
    }

    publishCassandraCDCvents(done, billsKafkaRow, cassandraCdcPublisher) {
        const self = this; 
        billsKafkaRow = self.convertCdcRecovery(billsKafkaRow);

        if(_.toLower(_.get(billsKafkaRow, 'service',null)) == 'mobile'){
            try{
                let RN = _.get(billsKafkaRow, 'rechargeNumber', null);
                let decryptedRechargeNumber = self.cryptr.decrypt(RN);
                _.set(billsKafkaRow, 'decryptedRechargeNumber', decryptedRechargeNumber);
            }catch(e){
                self.L.error('publishCassandraCDCvents :: CASSANDRA_CDC', 'Error while decrypting recharge number', e);
            }
        }else{
            _.set(billsKafkaRow, 'decryptedRechargeNumber', _.get(billsKafkaRow, 'rechargeNumber', null));
        }
        if (typeof _.get(billsKafkaRow, 'extra', {}) != 'string') {
            billsKafkaRow.extra = JSON.stringify(_.get(billsKafkaRow, 'extra',{}));
        }

        if (typeof _.get(billsKafkaRow, 'customerOtherInfo', null) == 'string' && _.get(billsKafkaRow, 'customerOtherInfo', null) === "null") {
            _.set(billsKafkaRow, 'customerOtherInfo', null);
        }

        cassandraCdcPublisher.publishData([{
            topic: _.get(self.config.KAFKA, 'SERVICES.CASSANDRA_CDC.TOPIC', 'CDC_RECOVERY'),
            messages: JSON.stringify(billsKafkaRow),
            key:self.getCDCPartitionKey(billsKafkaRow)
        }], (error) => {
            if (error) {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_CDC", 'STATUS:ERROR', "TYPE:CCASSANDRA_CDC", "OPERATOR:" + billsKafkaRow.operator, "IS_VALIDITY_EXPIRED:"+_.get(billsKafkaRow, 'isValidityExpired', false)]);
                self.logger.critical(`publishInKafka :: CASSANDRA_CDC Error while publishing message in Kafka ${error} - MSG:- `, billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                done("Error in publishing to CDC_RECOVERY Topic")
            } else {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:CASSANDRA_CDC", 'STATUS:PUBLISHED', "TYPE:CASSANDRA_CDC", "OPERATOR:" + billsKafkaRow.operator, "IS_VALIDITY_EXPIRED:"+_.get(billsKafkaRow, 'isValidityExpired', false)]);
                self.logger.log(`publishInKafka :: CASSANDRA_CDC Message published successfully in Kafka - MSG:- `, billsKafkaRow, _.get(billsKafkaRow, 'service', null));
                done();
            }
        }, [200, 800]);

    }

    async readBillsByRN(params){
        const self = this;
        if(self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId)) {
            let copyParams = _.cloneDeep(params);
            _.set(copyParams , 'rechargeNumber',self.encryptHelper.encryptData(params.rechargeNumber));
            self.L.log(`[nonPaytmBills.readBillsByRN] encrypted copyParams ${JSON.stringify(copyParams)}`);
            let result = await self.readBillsByRNDefault(copyParams);
            if(result && result.length > 0){
                //set the encrypted fields to original decrypted value
                result = self.encryptHelper.parseDbResponse(result, params.customerId);
                self.L.log(`[nonPaytmBills.readBillsByRN Default] found encrypted returng decrypted ${JSON.stringify(result)}`);
                return result;
            }
        }
        return await this.readBillsByRNDefault(params);
    }

    async readBillsByRNDefault(params){
        const self = this;
        let tableName = self.tableResolver.getNonRUTableName(params.customerId);
        const query = `SELECT service,operator,customer_id,customer_mobile,customer_email,user_data,customer_other_info FROM ${tableName} WHERE recharge_number = ? limit 50`;
        const queryParams = [params.rechargeNumber];
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams, { prepare : true })
            .then(result =>{
                self.L.log(`nonPaytmBills::readBillsByRN fetched ${result.rows.length} for ${JSON.stringify(queryParams)}`);
                resolve( result.rows);
            })
            .catch(error=>{
                self.L.critical(`nonPaytmBills::readBillsByRN DB exception! for ${JSON.stringify(queryParams)} : `, error);
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:READ_NON_PAYTM_RECORDS"]);
                reject(new Error('nonPaytmBills::readBillsByRN DB exception!' + JSON.stringify(queryParams) + error));
            })
        })
    }

    checkIfSameCustomerExistsInSql(params) {
        if (_.get(params, 'recordFoundOfSameCustId', null) == true) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:UPDATED_IN_MYSQL_ALREADY", "OPERATOR:" + params.operator, `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`]);
            return true;
        }
        else
            return false;
    }

    async writeBatchRecords(params, recentRecords, updateDueTable = null, updateGenTable = null, dueParams = null, genParams = null, cassandraCdcPublisher, isUpdateAll = false, existingDBRecord = null) {
        let self = this;
        return new Promise((resolve, reject) => {
            if (_.get(params, 'amount', 0) > MAX_AMOUNT) {
                self.L.error("Record rejected due to amount exceeding the maximum limit");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:MAX_AMOUNT_LIMIT', 'SOURCE:WRITE_BATCH_RECORDS', 'SERVICE:' + _.get(params, 'service', "NO_SERVICE")]);
                return reject('Amount exceeds the maximum limit');
            }
            if (_.get(params, 'amount', 0) < MIN_AMOUNT) {
                self.L.error("Record rejected due to amount exceeding the minimum limit");
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:MIN_AMOUNT_LIMIT', 'SOURCE:WRITE_BATCH_RECORDS', 'SERVICE:' + _.get(params, 'service', "NO_SERVICE")]);
                return reject('Amount exceeds the minimum limit');
            }
            _.set(params, 'otherCustomerNotificationPayloads', []);
            if (isUpdateAll) {
                self.L.log("🚀 ~ NonPaytmBillsModel ~ writeBatchRecords ~ kafkaData: params: ", params);
                self.readBillsByRN(params)
                    .then(async existingRecords => {
                        if (existingRecords.length > 0) {
                            ASYNC.each(existingRecords, function (recordToProcess, innerCb) {
                                let kafkaData = _.clone(params);
                                if (recordToProcess.service == _.toLower(kafkaData.service) && recordToProcess.operator == kafkaData.operator && kafkaData.customerId != recordToProcess.customer_id) {
                                    kafkaData.customerId = recordToProcess.customer_id;
                                    kafkaData.customerMobile = recordToProcess.customer_mobile;
                                    kafkaData.customerEmail = recordToProcess.customer_email;
                                    kafkaData.user_data = recordToProcess.user_data;
                                    kafkaData.customerOtherInfo = recordToProcess.customer_other_info;
                                    self.writeBatchRecordsByCustomer(kafkaData, recentRecords, updateDueTable, updateGenTable, dueParams, genParams, cassandraCdcPublisher, existingDBRecord).then((data) => {
                                        params.otherCustomerNotificationPayloads.push({ 'customerId': kafkaData.customerId, 'customerOtherInfo': kafkaData.customerOtherInfo });
                                        return innerCb();
                                    })
                                        .catch(error => {
                                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:UPDATE_MULTIPLE_CUSTOMERS"]);
                                            return innerCb();
                                        })
                                } else {
                                    return innerCb();
                                }
                            }, function (err) {
                                if (self.checkIfSameCustomerExistsInSql(params)) {
                                    self.L.error(`1003.writeBatchRecords`, `Record already exists for the same customer id in MySql`);
                                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:UPDATED_IN_MYSQL", "OPERATOR:" + params.operator, `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`]);
                                    return reject('Record already exists for the same customer id in MySql');
                                }
                                else {
                                    self.writeBatchRecordsByCustomer(params, recentRecords, updateDueTable, updateGenTable, dueParams, genParams, cassandraCdcPublisher, existingDBRecord).then((data) => {
                                        return resolve();

                                    })
                                        .catch(error => {
                                            self.L.log("writeBatchRecords, Error occured in writeBatchRecordsByCustomer (first block)", error);
                                            return reject();
                                        })
                                }
                            })
                        } else {
                            if (self.checkIfSameCustomerExistsInSql(params)) {
                                self.L.error(`1013.writeBatchRecords`, `Record already exists for the same customer id in MySql`);
                                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:UPDATED_IN_MYSQL", "OPERATOR:" + params.operator, `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`]);
                                return reject('Record already exists for the same customer id in MySql');
                            }
                            else {
                                self.writeBatchRecordsByCustomer(params, recentRecords, updateDueTable, updateGenTable, dueParams, genParams, cassandraCdcPublisher, existingDBRecord).then((data) => {
                                    return resolve();
                                })
                                    .catch(error => {
                                        self.L.log("writeBatchRecords, Error occured in writeBatchRecordsByCustomer (second block)", error);
                                        return reject();
                                    })
                            }
                        }
                    })
                    .catch(error => {
                        if (self.checkIfSameCustomerExistsInSql(params)) {
                            self.L.error(`1024.writeBatchRecords`, `Record already exists for the same customer id in MySql`);
                            utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:UPDATED_IN_MYSQL", "OPERATOR:" + params.operator, `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`]);
                            return reject('Record already exists for the same customer id in MySql');
                        }
                        else {
                            self.writeBatchRecordsByCustomer(params, recentRecords, updateDueTable, updateGenTable, dueParams, genParams, cassandraCdcPublisher, existingDBRecord).then((data) => {
                                return resolve();
                            })
                                .catch(error => {
                                    self.L.log("writeBatchRecords, Error occured in writeBatchRecordsByCustomer (third block)", error);
                                    return reject();
                                })
                        }
                    });
            } else {
                if (self.checkIfSameCustomerExistsInSql(params)) {
                    self.L.error(`1.writeBatchRecords`, `Record already exists for the same customer id in MySql`);
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', "TYPE:UPDATED_IN_MYSQL", "OPERATOR:" + params.operator, `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`]);
                    return reject('Record already exists for the same customer id in MySql');
                }
                else {
                    return self.writeBatchRecordsByCustomer(params, recentRecords, updateDueTable, updateGenTable, dueParams, genParams, cassandraCdcPublisher, existingDBRecord).then((data) => {
                        return resolve();
                    })
                        .catch(error => {
                            self.L.log("writeBatchRecords, Error occured in writeBatchRecordsByCustomer (fourth block)", error);
                            return reject();
                        })
                }
            }
        })
    }

    /**
     * Process due date related operations
     * @param {Object} params - Input parameters
     * @param {Object} encrypted_params - Encrypted parameters
     * @param {boolean} isCCEncryptionEnabled - CC encryption flag
     * @param {boolean} isExistingCCBPDBRecordDecrypted - Existing CCBP record decryption flag
     * @returns {Promise<Object>} Due date related data
     * @private
     */
    async processDueDateOperations(params, encrypted_params, isCCEncryptionEnabled, isExistingCCBPDBRecordDecrypted, updateDueTable = null, dueParams = null, encrypted_dueParams = null) {
        const self = this;
        let dueDate = _.get(params, 'dueDate', null);
        let dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDate);
        let billDate = _.get(params, 'billDate', null);
        if (!dueDate) return { dueDateFlag: false };
        const { monthName, yearName } = this.generateMonthAndYearNames(dueDate);
        const tableName = self.tableResolver.getBillDueTablePrefix(params.customerId, params.service, params.paytype) + monthName + '_' + yearName;
        const bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, encrypted_params.rechargeNumber, dueDateTimeStamp);

        let dueDateFlag = this.checkTimestampFlag(dueDateTimeStamp);
        if (!dueDateFlag) {
            self.L.log("not updating due date table for debugKey", _.get(params, 'debugKey', null));
            dueDateFlag = false;
        }

        if (isExistingCCBPDBRecordDecrypted) {
            self.L.log("[nonPaytmBills.writeBatchRecordsByCustomer] isExistingCCBPDBRecordDecrypted is true, not updating due date table");
            dueDateFlag = false;
        }

        let dueDateUpdateQuery = null;
        let dueDateUpdateParams = null;

        // Handle due date update if needed
        if ((updateDueTable && dueParams != null && dueParams != undefined && !_.isArray(dueParams)) || (_.isArray(dueParams) && dueParams.length > 0)) {
            self.L.log(`processDueDateOperations :: dueDateTimeStamp = ${dueDateTimeStamp} for debugKey ${_.get(params, 'debugKey', null)}`);
            let dueTimeStamp = _.get(dueParams, 'due_date', null);
            dueTimeStamp = await this.convertStringtoTimeStamp(dueTimeStamp);
            dueDateUpdateQuery = this.generateUpdateQueryForUpsert(tableName, 1);
            let bucket = await this.generateBucketNameFromTimestampAndHash(_.get(dueParams, 'customer_id', null), _.get(encrypted_dueParams, 'recharge_number', null), dueTimeStamp);
            let ccbpDueTimestamp = self.getFormattedDueDateForCCBPEncryption(dueTimeStamp, isCCEncryptionEnabled);
            dueDateUpdateParams = this.generateUpdateQueryParams(encrypted_dueParams, ccbpDueTimestamp, bucket, isCCEncryptionEnabled);
        }

        dueDateFlag = true;
        if (this.checkTimestampFlag(dueDateTimeStamp) == false) {
            dueDateFlag = false;
        }
        let billDateTimeStamp = billDate;
        if (billDateTimeStamp != null) {
            billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
        }

        const ccbpDueDateTimestamp = self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled);
        const dueDateBillsQuery = this.generateQuery(tableName, true);
        const dueDateQueryParams = this.generateInsertQueryandParams(
            encrypted_params,
            this.processExtraField(params),
            billDateTimeStamp,
            isCCEncryptionEnabled ? ccbpDueDateTimestamp : dueDateTimeStamp,
            bucketName,
            isCCEncryptionEnabled,
            true
        );

        return {
            dueDateFlag,
            dueDateBillsQuery,
            dueDateQueryParams,
            dueDateUpdateQuery,
            dueDateUpdateParams,
            monthName,
            yearName,
            tableName
        };
    }

    /**
     * Process bill date related operations
     * @param {Object} params - Input parameters
     * @param {Object} encrypted_params - Encrypted parameters
     * @param {boolean} isCCEncryptionEnabled - CC encryption flag
     * @param {boolean} isExistingCCBPDBRecordDecrypted - Existing CCBP record decryption flag
     * @param {boolean} updateGenTable - Update gen table flag
     * @param {Object} genParams - Gen parameters
     * @param {Object} encrypted_genParams - Encrypted gen parameters
     * @returns {Promise<Object>} Bill date related data
     * @private
     */
    async processBillDateOperations(params, encrypted_params, isCCEncryptionEnabled, isExistingCCBPDBRecordDecrypted, updateGenTable = null, genParams = null, encrypted_genParams = null) {
        const self = this;
        let billDate = _.get(params, 'billDate', null);
        let billDateTimeStamp = await this.convertStringtoTimeStamp(billDate);
        let dueDateTimeStamp = _.get(params, 'dueDate', null);
        if (!billDate) return { billDateFlag: false };
        const { monthName, yearName } = this.generateMonthAndYearNames(billDate);
        const tableName = self.tableResolver.getBillGenTablePrefix(params.customerId) + monthName + '_' + yearName;
        const bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, encrypted_params.rechargeNumber, billDateTimeStamp);
        let billDateFlag = this.checkTimestampFlag(billDateTimeStamp);
        if (!billDateFlag) {
            self.L.log("processBillDateOperations :: not updating bill date table for debugKey", _.get(params, 'debugKey', null));
            billDateFlag = false;
        }

        if (isExistingCCBPDBRecordDecrypted) {
            self.L.log("[nonPaytmBills.writeBatchRecordsByCustomer] isExistingCCBPDBRecordDecrypted is true, not updating billgen table for debugKey", _.get(params, 'debugKey', null));
            billDateFlag = false;
        }

        let billDateUpdateQuery = null;
        let billDateUpdateParams = null;

        // Handle bill date update if needed
        if ((updateGenTable && genParams != null && genParams != undefined && !_.isArray(genParams)) || (_.isArray(genParams) && genParams.length > 0)) {
            let billTimeStamp = _.get(genParams, 'bill_date', null);
            billTimeStamp = await this.convertStringtoTimeStamp(billTimeStamp);
            billDateUpdateQuery = this.generateUpdateQueryForUpsert(tableName, 0);
            let bucket = await this.generateBucketNameFromTimestampAndHash(_.get(genParams, 'customer_id', null), _.get(encrypted_genParams, 'recharge_number', null), billTimeStamp);
            billDateUpdateParams = this.generateUpdateQueryParams(encrypted_genParams, billTimeStamp, bucket);
        }

        if (this.checkTimestampFlag(billDateTimeStamp) == false) {
            billDateFlag = false;
        }
        if (dueDateTimeStamp != null) {
            dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
        }
        const billDateBillsQuery = this.generateQuery(tableName);
        const billDateQueryParams = this.generateInsertQueryandParams(
            encrypted_params,
            this.processExtraField(params),
            billDateTimeStamp,
            dueDateTimeStamp,
            bucketName,
            isCCEncryptionEnabled
        );

        return {
            billDateFlag,
            billDateBillsQuery,
            billDateQueryParams,
            billDateUpdateQuery,
            billDateUpdateParams,
            monthName,
            yearName,
            tableName
        };
    }

    /**
     * Generate recent records query and parameters
     * @param {Object} params - Input parameters
     * @param {Array} recentRecords - Recent records
     * @param {boolean} isCCEncryptionEnabled - CC encryption flag
     * @returns {Object} Query and parameters for recent records
     * @private
     */
    generateRecentRecordsQuery(params, recentRecords, isCCEncryptionEnabled) {
        const self = this;
        const date = this.getDate();
        const agentThreshold = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONSUMER', 'THRESHOLD', 'AGENT_THRESHOLD'], 50);
        const rnCount = recentRecords?.length > 0 ? recentRecords[0].rn_count : 0;

        if (rnCount >= agentThreshold) {
            self.L.log(`writeBatchRecords::Ignoring bill recent record writes :: Agent threshold reached for customer ${params.customerId}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_RECENT_RECORDS", "STATUS:IGNORED", "REASON:AGENT_THRESHOLD_REACHED"]);
            return null;
        }

        if (recentRecords?.length > 0) {
            return {
                query: `UPDATE ${self.tableResolver.getNewBillsRecentTable(params.customerId)}
                       SET recharge_number = recharge_number + ?, update_at = ?, rn_count = ?, is_encrypted = ?
                       WHERE customer_id = ? AND service = ? AND paytype = ? AND operator = ?`,
                params: [
                    isCCEncryptionEnabled ? [self.encryptHelper.encryptData(params.rechargeNumber)] : [params.rechargeNumber],
                    date,
                    rnCount + 1,
                    isCCEncryptionEnabled ? 1 : null,
                    params.customerId,
                    _.toLower(params.service),
                    params.paytype,
                    params.operator
                ]
            };
        }

        return {
            query: `INSERT INTO ${self.tableResolver.getNewBillsRecentTable(params.customerId)}
                   (customer_id, service, paytype, operator, recharge_number, create_at, update_at, rn_count, is_encrypted)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            params: [
                params.customerId,
                _.toLower(params.service),
                params.paytype,
                params.operator,
                isCCEncryptionEnabled ? [self.encryptHelper.encryptData(params.rechargeNumber)] : [params.rechargeNumber],
                date,
                date,
                1,
                isCCEncryptionEnabled ? 1 : null
            ]
        };
    }

    //
    async writeBatchRecordsByCustomer(params, recentRecords, updateDueTable = null, updateGenTable = null, dueParams = null, genParams = null, cassandraCdcPublisher, existingDBRecord = null) {
        const self = this;
        let monthName, yearName;
        let date = this.getDate();
        if (dueParams != null && _.isArray(dueParams) && dueParams.length > 0) {
            dueParams = dueParams[0];
        }
        if (genParams != null && _.isArray(genParams) && genParams.length > 0) {
            genParams = genParams[0];
        }
        const billsQuery = `INSERT INTO ${self.tableResolver.getNonRUTableName(params.customerId)} (recharge_number, customer_id, operator , service, product_id, bill_date, paytype,
            circle, customer_mobile, customer_email, amount, status, user_data, notification_status, due_date, extra ,bank_name ,card_network, customer_other_info, bill_fetch_date,
            due_amount, create_at, update_at, payment_date, next_bill_fetch_date, next_bill_fetch_start_date, published_date, retry, remind_later_date, enc_amount, enc_due_date, is_encrypted, old_bill_fetch_date)                
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        let extra = _.get(params, 'extra', {});
        let recon_id = utility.generateReconID(params.rechargeNumber, params.operator, _.get(params, 'amount', 0), _.get(params, 'dueDate', null), _.get(params, 'billDate', null))
        self.L.log(`writeBatchRecordsByCustomer :: ${recon_id}, updateDueTable : ${updateDueTable}, updateGenTable : ${updateGenTable}, dueParams : ${dueParams && JSON.stringify(dueParams)}, genParams : ${genParams && JSON.stringify(genParams)}`);
        if (typeof extra == 'object') {
            _.set(extra, 'recon_id', recon_id)
            _.set(extra, 'user_type', "NON_RU")
            _.set(params, 'extra', extra)
            extra = JSON.stringify(extra);

        } else {
            extra = utility.setReconIdInString(extra, recon_id, "NON_RU")
            _.set(params, 'extra', extra)
        }
        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        //isExistingCCBPDBRecordDecrypted = true if existing ccbp record is decrypted and encryption is enabled
        let isExistingCCBPDBRecordDecrypted = isCCEncryptionEnabled && existingDBRecord && _.isArray(existingDBRecord) && existingDBRecord.length == 1 && !self.checkIfDbRecordIsEncrypted(existingDBRecord[0]);

        //this encrypted_params will return encrypted fields only in case of ccbp else it will return params as before
        let rootLevelKeysToEncrypt = ['rechargeNumber', 'refId', 'customerMobile', 'customerEmail'];
        let encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt);
        let rootLevelKeysToEncryptDueBill = ['recharge_number', 'reference_id', 'customer_mobile', 'customer_email'];
        let encrypted_dueParams = self.encryptHelper.getEncryptedParamsFromGenericParams(dueParams, rootLevelKeysToEncryptDueBill);
        let encrypted_genParams = self.encryptHelper.getEncryptedParamsFromGenericParams(genParams, rootLevelKeysToEncryptDueBill);


        const billsQueryParams = self.generateNonRUBillsQueryParam(encrypted_params, extra, isCCEncryptionEnabled);

        let dueDateTimeStamp = _.get(params, 'dueDate', null);
        let billDateTimeStamp = _.get(params, 'billDate', null);
        let dueDateFlag = false, billDateFlag = false;
        let dueDateBillsQuery, billDateBillsQuery, dueDateQueryParams, billDateQueryParams, dueDateUpdateQuery, billDateUpdateQuery, dueDateUpdateParams, billDateUpdateParams;
        try {
            if (dueDateTimeStamp != null) {
                dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);

                monthName = this.getMonthName(dueDateTimeStamp);
                yearName = this.yearName(dueDateTimeStamp);
                let tableName = self.tableResolver.getBillDueTablePrefix(params.customerId, params.service, params.paytype) + monthName + '_' + yearName;
                let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, encrypted_params.rechargeNumber, dueDateTimeStamp);
                if (this.checkTimestampFlag(dueDateTimeStamp) == false) {
                    self.L.log("not updating due date table");
                    updateDueTable = false;
                }
                if (isExistingCCBPDBRecordDecrypted) {
                    // if existing ccbp record is decrypted and encryption is enabled
                    // then we do not set status = 13 for existing record, it would be done later on in the flow
                    self.L.log("[nonPaytmBills.writeBatchRecordsByCustomer] isExistingCCBPDBRecordDecrypted is true, not updating due date table");
                    updateDueTable = false;
                }

                if ((updateDueTable && dueParams != null && dueParams != undefined && !_.isArray(dueParams)) || (_.isArray(dueParams) && dueParams.length > 0)) {
                    let dueTimeStamp = _.get(dueParams, 'due_date', null);
                    dueTimeStamp = await this.convertStringtoTimeStamp(dueTimeStamp);
                    dueDateUpdateQuery = this.generateUpdateQueryForUpsert(tableName, 1);
                    let bucket = await this.generateBucketNameFromTimestampAndHash(_.get(dueParams, 'customer_id', null), _.get(encrypted_dueParams, 'recharge_number', null), dueTimeStamp);
                    let ccbpDueTimestamp = self.getFormattedDueDateForCCBPEncryption(dueTimeStamp, isCCEncryptionEnabled);
                    dueDateUpdateParams = this.generateUpdateQueryParams(encrypted_dueParams, ccbpDueTimestamp, bucket, isCCEncryptionEnabled);
                }
                dueDateFlag = true;
                if (this.checkTimestampFlag(dueDateTimeStamp) == false) {
                    dueDateFlag = false;
                }
                if (billDateTimeStamp != null) {
                    billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
                }
                let ccbpDueDateTimestamp = self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled);

                dueDateBillsQuery = this.generateQuery(tableName, true);
                dueDateQueryParams = this.generateInsertQueryandParams(encrypted_params, extra, billDateTimeStamp, isCCEncryptionEnabled ? ccbpDueDateTimestamp : dueDateTimeStamp, bucketName, isCCEncryptionEnabled, true);

            }
            dueDateTimeStamp = _.get(params, 'dueDate', null);
            billDateTimeStamp = _.get(params, 'billDate', null);
            if (billDateTimeStamp != null) {
                billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
                monthName = this.getMonthName(billDateTimeStamp);
                yearName = this.yearName(billDateTimeStamp);
                let tableName = self.tableResolver.getBillGenTablePrefix(params.customerId) + monthName + '_' + yearName;
                let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, encrypted_params.rechargeNumber, billDateTimeStamp);
                if (this.checkTimestampFlag(billDateTimeStamp) == false) {
                    self.L.log("not updating bill date table");
                    updateGenTable = false;
                }
                if (isExistingCCBPDBRecordDecrypted) {
                    // if existing ccbp record is decrypted and encryption is enabled
                    // then we do not set status = 13 for existing record, it would be done later on in the flow
                    self.L.log("[nonPaytmBills.writeBatchRecordsByCustomer] isExistingCCBPDBRecordDecrypted is true, not updating billgen table");
                    updateGenTable = false;
                }
                if ((updateGenTable && genParams != null && genParams != undefined && !_.isArray(genParams)) || (_.isArray(genParams) && genParams.length > 0)) {
                    let billTimeStamp = _.get(genParams, 'bill_date', null);
                    billTimeStamp = await this.convertStringtoTimeStamp(billTimeStamp);
                    billDateUpdateQuery = this.generateUpdateQueryForUpsert(tableName, 0);
                    let bucket = await this.generateBucketNameFromTimestampAndHash(_.get(genParams, 'customer_id', null), _.get(encrypted_genParams, 'recharge_number', null), billTimeStamp);
                    billDateUpdateParams = this.generateUpdateQueryParams(encrypted_genParams, billTimeStamp, bucket);
                }
                if (params.billGenNotUpdated != null || params.billGenNotUpdated != undefined) {
                    if (params.billGenNotUpdated == 0) {
                        billDateFlag = false;
                        self.L.log("Bill Gen flag set to false");
                    }
                    else {
                        billDateFlag = true;
                        self.L.log("Bill Gen flag set to true");
                    }
                }
                else {
                    billDateFlag = true;
                }
                if (this.checkTimestampFlag(billDateTimeStamp) == false) {
                    billDateFlag = false;
                }
                if (dueDateTimeStamp != null) {
                    dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
                }
                billDateBillsQuery = this.generateQuery(tableName);
                billDateQueryParams = this.generateInsertQueryandParams(encrypted_params, extra, billDateTimeStamp, dueDateTimeStamp, bucketName, isCCEncryptionEnabled);
            }

        } catch (err) {
            this.L.error(`error in writeBatchRecords during duedateTimeStamp/billdateTimeStamp fetch`, err);
        }


        let recentsQuery = `INSERT into ${self.tableResolver.getNewBillsRecentTable(params.customerId)} (customer_id, service, paytype, operator, recharge_number, create_at, update_at,rn_count,is_encrypted) 
            VALUES (?,?,?,?,?,?,?,?,?)`;

        let recentsQueryParams = [
            params.customerId,
            _.toLower(params.service),
            params.paytype,
            params.operator,
            isCCEncryptionEnabled ? [self.encryptHelper.encryptData(params.rechargeNumber)] : [params.rechargeNumber],
            date,
            date, 1,
            isCCEncryptionEnabled ? 1 : null
        ];

        const agentThreshold = _.get(self.config, ['DYNAMIC_CONFIG', 'NON_PAYTM_CONSUMER', 'THRESHOLD', 'AGENT_THRESHOLD'], 50);


        let rnCount = 0;
        if (recentRecords != null && recentRecords.length > 0) {
            rnCount = recentRecords[0].rn_count;
        }

        if (recentRecords != null && recentRecords.length > 0) {

            recentsQuery = `UPDATE ${self.tableResolver.getNewBillsRecentTable(params.customerId)} SET recharge_number = recharge_number + ?, update_at = ?, rn_count = ? , is_encrypted = ? where customer_id = ? 
                AND  service = ? and paytype = ? and operator = ?;`

            recentsQueryParams = [
                isCCEncryptionEnabled ? [self.encryptHelper.encryptData(params.rechargeNumber)] : [params.rechargeNumber],
                date,
                rnCount + 1,
                isCCEncryptionEnabled ? 1 : null,
                params.customerId,
                _.toLower(params.service),
                params.paytype,
                params.operator
            ];
        }

        const batchQuery = [
            {
                query: billsQuery,
                params: billsQueryParams
            }
        ]

        if (rnCount < agentThreshold) {
            batchQuery.push({
                query: recentsQuery,
                params: recentsQueryParams
            });
        } else {
            self.L.log(`writeBatchRecords::Ignoring bill recent record writes :: Agent threshold reached for customer ${params.customerId}`);
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_RECENT_RECORDS", "STATUS:IGNORED", "REASON:AGENT_THRESHOLD_REACHED"]);
        }


        const batchQueryNotification = [];

        if (updateDueTable) batchQueryNotification.push({
            query: dueDateUpdateQuery,
            params: dueDateUpdateParams
        })

        if (updateGenTable) batchQueryNotification.push({
            query: billDateUpdateQuery,
            params: billDateUpdateParams
        })

        let customerIDRange = self.tableResolver.getCustomerIDRange(params.customerId);

        if (dueDateFlag) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", `MONTH:${monthName}`, `YEAR:${yearName}`, `CUSTOMERID_RANGE: ${customerIDRange}`]);
        }
        if(billDateFlag && !(_.toLower(params.service)=='mobile' && _.toLower(params.paytype)=='prepaid' && (_.get(params, 'partialBillState', 'NO_STATE') === 'NO_STATE' || _.get(params, 'partialBillState') === null))) {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLGEN_NON_PAYTM_BILLS", `MONTH:${monthName}`, `YEAR:${yearName}`,`CUSTOMERID_RANGE: ${ customerIDRange}`]);
            self.L.verbose(`nonPaytmBills::writeBatchRecords :: updateGenTable flag : ${billDateFlag} for params ${JSON.stringify(params)}`)
        } else {
            self.L.verbose(`nonPaytmBills::writeBatchRecords :: updateGenTable not required flag : ${billDateFlag} for params ${JSON.stringify(params)}`)
        }

        if (dueDateFlag) batchQueryNotification.push({
            query: dueDateBillsQuery,
            params: dueDateQueryParams
        })
        if(billDateFlag && !(_.toLower(params.service)=='mobile' && _.toLower(params.paytype)=='prepaid' && (_.get(params, 'partialBillState', 'NO_STATE') === 'NO_STATE' || _.get(params, 'partialBillState') === null)))
            batchQueryNotification.push({
                query: billDateBillsQuery,
                params: billDateQueryParams
            })
        return new Promise((resolve, reject) => {
            this.client.batch(batchQuery, { prepare: true })
                .then(() => {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:INSERTED', 'SOURCE:WRITE_BATCH_RECORDS', `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(params, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(params, 'operator', 'NO_OPERATOR')}`, `PARTIAL_BILL:${_.get(params, 'partialBillState', 'NO_STATE')}`, `DATA_EXHAUST:${_.get(params, "isRealTimeDataExhausted", false)}`, `IS_VALIDITY_EXPIRED:${_.get(params, "isValidityExpired", false)}`]);
                    self.logger.log("nonPaytmBills::writeBatchRecords Data updated on cluster for", params, _.get(params, 'service', null));
                    params.cdcEventType = 'u';
                    params.convertPayload = 'y';
                    params.updateAt = date;
                    params.createAt = _.get(params, 'create_at', date);
                    if (batchQueryNotification.length === 0)
                        resolve(params);
                    else
                        return self.notificationNewClusterClient.batch(batchQueryNotification, { prepare: true });
                })
                .then(() => {
                    self.publishCassandraCDCvents((err, data) => {
                        if (err) {
                            self.logger.error(`nonPaytmBills::writeBatchRecords :: Error while executing: ${err} of record`, params, _.get(params, 'service', null));
                            reject(err);
                        } else
                            resolve(params);
                    }, params, cassandraCdcPublisher)
                })
                .then(() => {
                    if (isExistingCCBPDBRecordDecrypted) {
                        self.deleteUsingRecentRecords(params, cassandraCdcPublisher, (err, data) => {
                            if (err) {
                                self.logger.error(`nonPaytmBills::writeBatchRecords :: Error while deleteUsingRecentRecords: ${err} of record`, params, "financial services");
                                reject(err);
                            }
                            else {
                                self.L.log(`nonPaytmBills::writeBatchRecords :: deleteUsingRecentRecords older decrypted record deleted`);
                                resolve(params);
                            }

                        }, true)
                    } else {
                        self.L.log(`nonPaytmBills::writeBatchRecords :: deleteUsingRecentRecords not required as encryption is disabled for this category or existing ccbp reccord is not decrypted`);
                        resolve(params);
                    }
                })
                .catch(error => {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'SOURCE:WRITE_BATCH_RECORDS', 'TYPE:DB_EXCEPTION', `ORIGIN:${_.get(params, 'source', "NO_SOURCE")}`, `SERVICE:${_.get(params, 'service', "NO_SERVICE")}`, `OPERATOR:${_.get(params, 'operator', 'NO_OPERATOR')}`, `DATA_EXHAUST:${_.get(params, "isRealTimeDataExhausted", false)}`, `PARTIAL_BILL:${_.get(params, 'partialBillState', 'NO_STATE')}`, `IS_VALIDITY_EXPIRED:${_.get(params, "isValidityExpired", false)}`]);
                    if (JSON.stringify(error).includes('does not exist')) {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", 'STATUS:TABLE_NOT_FOUND', 'SOURCE:INSERT_MODEL']);
                    }
                    if (error == "Error in publishing to CDC_RECOVERY Topic") {
                        utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR', 'TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:WRITE_BATCH_RECORDS']);
                        resolve(params)
                    }
                    else {
                        self.logger.error(`nonPaytmBills::writeBatchRecords :: Record not updated with error ${error} of record`, params, _.get(params, 'service', null));
                        reject(`nonPaytmBills::writeBatchRecords Record not updated for ${JSON.stringify(params)}, error ${error}`);
                        resolve(params);
                    }
                })
        })

    }

    generateNonRUBillsQuery(tableName) {
        return `INSERT INTO ${tableName}
                (recharge_number, customer_id, operator, service, product_id, bill_date, paytype, circle,
                customer_mobile, customer_email, amount, status, user_data, notification_status, due_date,
                extra, bank_name, card_network, customer_other_info, bill_fetch_date, due_amount, create_at,
                update_at, payment_date, next_bill_fetch_date, next_bill_fetch_start_date, published_date,
                retry, remind_later_date, enc_amount, enc_due_date, is_encrypted, old_bill_fetch_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    }

    getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled){
        let dueDate = dueDateTimeStamp;
        if(isCCEncryptionEnabled) {
            dueDate = MOMENT(dueDateTimeStamp).format('YYYY-MM-DD')
        }
        return dueDate;
    }

    generateNonRUBillsQueryParamNew(params, extra, isCCEncryptionEnabled) {
        const self = this;
        let date = this.getDate();
        let billsQueryParams = [
            params.rechargeNumber,
            params.customerId,
            params.operator,
            _.toLower(params.service),
            params.productId,
            _.get(params, 'billDate', null),
            params.paytype,
            _.get(params, 'circle', null),
            _.get(params, 'customerMobile', null),
            _.get(params, 'customerEmail', null),
            isCCEncryptionEnabled ? null : _.get(params, 'amount', 0),
            params.status,
            _.get(params, 'user_data', null),
            _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
            isCCEncryptionEnabled ? null : _.get(params, 'dueDate', null),
            extra,
            _.get(params, 'bankName', null),
            _.get(params, 'cardNetwork', null),
            params.customerOtherInfo,
            _.get(params, 'billFetchDate', _.get(params, 'bill_fetch_date', null)),
            isCCEncryptionEnabled ? null : params.amount,
            _.get(params, 'create_at', date),
            date,
            _.get(params, 'paymentDate', null),
            _.get(params, 'nextBillFetchDate', _.get(params, 'next_bill_fetch_date', null)),
            _.get(params, 'nextBillFetchStartDate', _.get(params, 'next_bill_fetch_start_date', null)),
            _.get(params, 'publishedDate', _.get(params, 'published_date', null)),
            _.get(params, 'retryCount', _.get(params, 'retry_count', null)),
            _.get(params, 'remind_later_date', null),
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.toString(_.get(params, 'amount', 0))) : null,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.get(params, 'dueDate', null)) : null,
            isCCEncryptionEnabled ? 1 : null,
            _.get(params, 'oldBillFetchDate', null),
        ];
        return billsQueryParams;
    }

    generateNonRUBillsQueryParam(params, extra, isCCEncryptionEnabled) {
        const self = this;
        let date = this.getDate();
        let billsQueryParams = [
            params.rechargeNumber,
            params.customerId,
            params.operator,
            _.toLower(params.service),
            params.productId,
            _.get(params, 'billDate', _.get(params, 'bill_date', null)),
            params.paytype,
            _.get(params, 'circle', null),
            _.get(params, 'customerMobile', null),
            _.get(params, 'customerEmail', null),
            isCCEncryptionEnabled ? null : _.get(params, 'amount', 0),
            params.status,
            _.get(params, 'user_data', null),
            _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
            isCCEncryptionEnabled ? null : _.get(params, 'dueDate', _.get(params, 'due_date', null)),
            extra,
            _.get(params, 'bankName', null),
            _.get(params, 'cardNetwork', null),
            params.customerOtherInfo,
            _.get(params, 'billFetchDate', _.get(params, 'bill_fetch_date', null)),
            isCCEncryptionEnabled ? null : params.amount,
            _.get(params, 'create_at', date),
            date,
            _.get(params,'paymentDate',null),
            _.get(params,'nextBillFetchDate',_.get(params,'next_bill_fetch_date',null)),
            _.get(params,'nextBillFetchStartDate',_.get(params,'next_bill_fetch_start_date',null)),
            _.get(params,'publishedDate',_.get(params,'published_date',null)),
            _.get(params, 'retryCount', _.get(params, 'retry_count', null)),
            _.get(params,'remind_later_date',null),
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.toString(_.get(params,'amount',0))) : null,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.get(params, 'dueDate', _.get(params, 'due_date', null))) : null,
            isCCEncryptionEnabled ? 1 : null,
            _.get(params,'oldBillFetchDate',null)
        ];
        return billsQueryParams;
    }

    /** function not being used, check if its being used from somewhere, can we remove */
    async deleteBatchRecords (params){
        const self = this
        let encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params);
        const billsQuery = `DELETE FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number = ? AND customer_id = ? \
                        AND service = ? AND operator = ?`;
        const billsQueryParams = [params.rechargeNumber, params.customerId, params.service, params.operator];

        let recentsQuery = `DELETE recharge_number [?] FROM ${self.tableResolver.getNewBillsRecentTable(params.customerId)} WHERE customer_id = ? AND service = ?`;
        let recentsQueryParams = [params.rechargeNumber, params.customerId, params.service];

        if(params.paytype){
            recentsQuery += ' AND paytype = ?';
            recentsQueryParams.push(params.paytype)
        }

        if(params.operator){
            recentsQuery += ' AND operator = ?';
            recentsQueryParams.push(params.operator)
        }

        const batchQuery = [
            {
                query: billsQuery,
                params: billsQueryParams
            },
            {
                query: recentsQuery,
                params: recentsQueryParams
            }
        ]
        return new Promise((resolve, reject) => {
            this.client.batch(batchQuery, { prepare: true })
            .then(() => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:DELETED','SOURCE:DELETE_MODEL']);
                self.L.log(`nonPaytmBills::deleteBatchRecords Data deleted on cluster for ${JSON.stringify(params)}`);
                params.cdcEventType='d';
                params.updateAt = date;
                params.cdcPayload = 'y';
                resolve(params);
            })
            .then(() =>{
                self.publishCassandraCDCvents((err, data) => {
                    if (err) {
                        self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing: ${err} of record ${JSON.stringify(params)}`);
                        reject(err);
                    }
                    else
                        resolve(params);
                }, params, cassandraCdcPublisher)
            })
            .catch(error => {
                if(error == "Error in publishing to CDC_RECOVERY Topic"){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:deleteBatchRecords']);
                    resolve(params)
                }
                else
                    throw new Error(`nonPaytmBills::deleteBatchRecords Record not deleted for ${JSON.stringify(params)}, error ${error}`)
            })
        })
    }

    /** function not being used, if its being used from somewhere can we remove */
    async deleteBills(params){
        const self = this;
        const query = `DELETE FROM ${self.tableResolver.getNonRUTableName(params.customerId)} WHERE recharge_number = ? AND customer_id = ? \
                        AND service = ? AND operator = ?`;
        const queryParams = [params.rechargeNumber, params.customerId, params.service, params.operator];
        return new Promise((resolve, reject) => {
            self.client.execute(query, queryParams)
            .then(result =>{
                this.L.log(`nonPaytmBills::deleteBills for ${JSON.stringify(params)}`);
                params.cdcEventType='d';
                params.updateAt=this.getDate();
                resolve(params);
            })
            .then(() =>{
                self.publishCassandraCDCvents((err, data) => {
                    if (err) {
                        self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing: ${err} of record ${JSON.stringify(params)}`);
                        reject(err);
                    }
                    else
                        resolve(params);
                }, params, cassandraCdcPublisher)
            })
            .catch(error=>{
                if(error == "Error in publishing to CDC_RECOVERY Topic"){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:NON_PAYTM_BILLS", 'STATUS:ERROR','TYPE:ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'SOURCE:deleteBills']);
                    resolve(params)
                }
                throw new Error('nonPaytmBills::deleteBills DB exception!' + JSON.stringify(queryParams) + error);
            })
        })
    }

    getDate(){
        return MOMENT().format('YYYY-MM-DD HH:mm:ss');
    }

    getCDCPartitionKey(billsKafkaRow){
        let partionKey='';
        let customerIdForCDCPartitionKey=_.get(billsKafkaRow, 'customerId', null);
        let rnForCDCPartitionKey=_.get(billsKafkaRow, 'rechargeNumber', null);
        if(customerIdForCDCPartitionKey && rnForCDCPartitionKey ){
            partionKey=customerIdForCDCPartitionKey+"_"+rnForCDCPartitionKey;
        }
        return partionKey;

    }

    /** ccbp encryption change not needed, as bill fetch is disabled for ccbp*/
    async deleteBillsNonPaytmBillFetch(cd,params){
        const self = this;
        if(params.nextBillFetchDate == null || params.nextBillFetchDate ==''){
            return cd(null);
        }
        params.bucketId = await self.generateBucketNameUsingHash(params,params);
        let date = new Date(params.nextBillFetchDate);
        date.setHours(date.getHours() + 5);
        date.setMinutes(date.getMinutes() + 30);
        params.nextBillFetchDate = date;
        const query = `DELETE FROM ${self.nonPaytmBillFetch} WHERE operator = ? AND service = ? \
                        AND bucket_id = ? AND next_bill_fetch_date = ? AND customer_id = ? AND recharge_number = ?`;
        const queryParams = [
            _.get(params,'operator',null),
            _.toLower(_.get(params,'service',null)),
            params.bucketId,
            params.nextBillFetchDate,
            _.get(params,'customerId',null),
            _.get(params,'rechargeNumber',null)
        ];
        self.client.execute(query, queryParams, {prepare:true}, (err) => {
            if(err){
                self.L.error("bills_non_next_bill_fetch:: error while deleting from table");
            }else{
                this.logger.log(`bills_non_next_bill_fetch::deleteBills for`, params, params.service);
            }
            cd(err);
        })
    }

    async generateBucketNameUsingHash(params,currentRecord) {
        const self = this;
        let customer_id = _.get(params,'customerId',_.get(currentRecord,'customer_id',null)),
            recharge_number =  _.get(params,'rechargeNumber',_.get(currentRecord,'recharge_number',null));
        try{
            const inputString = `${customer_id}${recharge_number}`;
            const hash = CRYPTO.createHash('md5').update(inputString).digest('hex');

            let value = 40;
            const hashValue = parseInt(hash, 16) % value;
            const resultString = hashValue.toString();
            return resultString;
        } catch(err) {
            self.L.error(`error in generating bucketname`, err);
            return null;
        }
    }

    async insertBillDateMappingTableData(billsKafkaRow) {
        let params = _.clone(billsKafkaRow)
        if(params.rechargeNumber == null && _.get(billsKafkaRow,'recharge_number',null))
            params.rechargeNumber = _.get(billsKafkaRow,'recharge_number',null);
        if(params.customerId == null && _.get(billsKafkaRow,'customer_id',null))
            params.customerId = _.get(billsKafkaRow,'customer_id',null);
        if(params.customerOtherInfo == null && _.get(billsKafkaRow,'customer_other_info',null))
            params.customerOtherInfo = _.get(billsKafkaRow,'customer_other_info',null);
        if(params.paymentDate == null && _.get(billsKafkaRow,'payment_date',null))
            params.paymentDate = _.get(billsKafkaRow,'payment_date',null);
        const self = this;
        let date = self.getDate();
        let dueDateTimeStamp = _.get(params, 'dueDate', null);
        let billDateTimeStamp = _.get(params, 'billDate', null);
        billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
        if(dueDateTimeStamp != null) {
            dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
        }

        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let rootLevelKeysToEncrypt = ['rechargeNumber', 'refId', 'customerMobile', 'customerEmail'];
        let encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt);

        let monthName = this.getMonthName(billDateTimeStamp);
        let yearName = this.yearName(billDateTimeStamp);
        let tableName = self.tableResolver.getBillGenTablePrefix(params.customerId) + monthName + '_' + yearName;
        let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, encrypted_params.rechargeNumber, billDateTimeStamp);
        const billsQuery = `INSERT INTO ${tableName} (
            recharge_number, customer_id, operator, service, product_id, bill_date, paytype, circle, customer_mobile, customer_email, amount, status, user_data, notification_status, due_date, extra, bank_name, card_network,
            customer_other_info, bill_fetch_date, due_amount, create_at, update_at, payment_date,bucket_id,enc_amount, enc_due_date, is_encrypted)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?)`;

        const queryParams = self.generateQueryParamsForInsertBillDateQuery(encrypted_params, billDateTimeStamp, dueDateTimeStamp, bucketName, isCCEncryptionEnabled);

    if(this.checkTimestampFlag(billDateTimeStamp)==false) {
        return null;
    }

    return new Promise((resolve, reject) => {
        self.notificationNewClusterClient.execute(billsQuery,queryParams, { prepare: true })
        .then(() => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLGEN_NON_PAYTM_BILLS", 'STATUS:INSERTED','SOURCE:INSERT_DATA']);
            if(monthName && yearName){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLGEN_NON_PAYTM_BILLS", `MONTH:${monthName}`, `YEAR:${yearName}`]);
            }
            self.logger.log(`billgenNonPaytmBills::INSERT_DATA :: Data updated on cluster for`, params, params.service);
            resolve(null)
        })
        .catch(error => {
            utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLGEN_NON_PAYTM_BILLS", 'STATUS:FAILED_INSERT','SOURCE:INSERT_DATA']);
            if(JSON.stringify(error).includes('does not exist') && JSON.stringify(error).includes('table')){
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLGEN_NON_PAYTM_BILLS", 'STATUS:TABLE_NOT_FOUND', 'SOURCE:INSERT_MODEL', `TABLE:${tableName}`]);
            }
            // self.logger.error(`billgenNonPaytmBills::INSERT_DATA Record not updated for error ${error}`, params, params.service);
            reject(`billgenNonPaytmBills::INSERT_DATA Record not updated for ${JSON.stringify(params)}, error ${error}`);
        })
    })

    }

    generateQueryParamsForInsertBillDateQuery(params, billDateTimeStamp, dueDateTimeStamp, bucketName, isCCEncryptionEnabled = false) {
        const self = this;
        let date = this.getDate();

        const queryParams = [
            params.rechargeNumber,
            params.customerId,
            params.operator,
            _.toLower(params.service),
            _.get(params, 'productId', null),
            billDateTimeStamp,
            _.get(params, 'paytype', null),
            _.get(params, 'circle', null),
            _.get(params, 'customerMobile', null),
            _.get(params, 'customerEmail', null),
            isCCEncryptionEnabled ? null : _.get(params, 'amount', null),
            _.get(params, 'status', null),
            _.get(params, 'userData', null),
            _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
            isCCEncryptionEnabled ? null : dueDateTimeStamp,
            _.get(params, 'extra', {}),
            _.get(params, 'bankName', null),
            _.get(params, 'cardNetwork', null),
            _.get(params, 'customerOtherInfo', null),
            _.get(params, 'billFetchDate', null),
            isCCEncryptionEnabled ? null : _.get(params, 'dueAmount', null),
            _.get(params,'createAt', date),
            date,
            _.get(params,'paymentDate',null),
            bucketName,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.toString(_.get(params,'amount',0))) : null,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.get(params, 'dueDate', null)) : null,
            isCCEncryptionEnabled ? 1 : null
        ];
        return queryParams;
    }


    async insertDueDateMappingTableData(billsKafkaRow) {
        let params = _.clone(billsKafkaRow)
        if(params.rechargeNumber == null && _.get(billsKafkaRow,'recharge_number',null))
            params.rechargeNumber = _.get(billsKafkaRow,'recharge_number',null);
        if(params.customerId == null && _.get(billsKafkaRow,'customer_id',null))
            params.customerId = _.get(billsKafkaRow,'customer_id',null);
        if(params.customerOtherInfo == null && _.get(billsKafkaRow,'customer_other_info',null))
            params.customerOtherInfo = _.get(billsKafkaRow,'customer_other_info',null);
        if(params.paymentDate == null && _.get(billsKafkaRow,'payment_date',null))
            params.paymentDate = _.get(billsKafkaRow,'payment_date',null);
        const self = this;
        let date = self.getDate();
        let dueDateTimeStamp = _.get(params, 'dueDate', null);
        let billDateTimeStamp = _.get(params, 'billDate', null);
        dueDateTimeStamp = await this.convertStringtoTimeStamp(dueDateTimeStamp);
        if(billDateTimeStamp != null) {
            billDateTimeStamp = await this.convertStringtoTimeStamp(billDateTimeStamp);
        }

        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        let rootLevelKeysToEncrypt = ['rechargeNumber', 'refId', 'customerMobile', 'customerEmail'];
        let encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt);

        let ccbpDueDateTimestamp = self.encryptHelper.encryptData(self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled));

        let monthName = this.getMonthName(dueDateTimeStamp);
        let yearName = this.yearName(dueDateTimeStamp);
        let tableName = self.tableResolver.getBillDueTablePrefix(params.customerId, params.service, params.paytype) +monthName+'_'+yearName;
        let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, encrypted_params.rechargeNumber, dueDateTimeStamp);
        const billsQuery = `INSERT INTO ${tableName} (
        recharge_number, customer_id, operator, service, product_id, bill_date, paytype, circle, customer_mobile, customer_email, amount, status, user_data, notification_status, due_date, extra, bank_name, card_network,
        customer_other_info, bill_fetch_date, due_amount, create_at, update_at, payment_date,bucket_id,enc_amount, enc_due_date, is_encrypted)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?)`;

        const queryParams = self.generateQueryParamsForInsertDueDateQuery(encrypted_params, billDateTimeStamp, isCCEncryptionEnabled ? ccbpDueDateTimestamp : dueDateTimeStamp, bucketName, isCCEncryptionEnabled);

    if(this.checkTimestampFlag(dueDateTimeStamp)==false) {
        return null;
    }
        return new Promise((resolve, reject) => {
            this.notificationNewClusterClient.execute(billsQuery,queryParams, { prepare: true })
            .then(() => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", 'STATUS:INSERTED','SOURCE:INSERT_DATA']);
                if(monthName && yearName) {
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", `MONTH:${monthName}`, `YEAR:${yearName}`]);
                }

                self.logger.log(`billdueNonPaytmBills::INSERT_DATA :: Data updated on cluster for`, params, params.service);
                resolve(null)
        })
            .catch(error => {
                utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", 'STATUS:FAILED_INSERT','SOURCE:INSERT_DATA']);
                if(JSON.stringify(error).includes('does not exist') && JSON.stringify(error).includes('table')){
                    utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS", 'STATUS:TABLE_NOT_FOUND', 'SOURCE:INSERT_MODEL', `TABLENAME:${tableName}`]);
                }
                // self.logger.error(`billdueNonPaytmBills::INSERT_DATA Record not updated for error ${error}`, params, params.service);
                reject(`billdueNonPaytmBills::INSERT_DATA Record not updated for ${JSON.stringify(params)}, error ${error}`);
        })
    })
    }

    generateQueryParamsForInsertDueDateQuery(params, billDateTimeStamp, dueDateTimeStamp, bucketName, isCCEncryptionEnabled = false) {
        const self = this;
        let date = this.getDate();

        const queryParams = [
            params.rechargeNumber,
            params.customerId,
            params.operator,
            _.toLower(params.service),
            _.get(params, 'productId', null),
            billDateTimeStamp,
            _.get(params, 'paytype', null),
            _.get(params, 'circle', null),
            _.get(params, 'customerMobile', null),
            _.get(params, 'customerEmail', null),
            isCCEncryptionEnabled ? null : _.get(params, 'amount', null),
            _.get(params, 'status', null),
            _.get(params, 'userData', null),
            _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
            dueDateTimeStamp,
            _.get(params, 'extra', {}),
            _.get(params, 'bankName', null),
            _.get(params, 'cardNetwork', null),
            _.get(params, 'customerOtherInfo', null),
            _.get(params, 'billFetchDate', null),
            isCCEncryptionEnabled ? null : _.get(params, 'dueAmount', null),
            _.get(params,'createAt', date),
            date,
            _.get(params,'paymentDate',null),
            bucketName,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.toString(_.get(params,'amount',0))) : null,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.get(params, 'dueDate', null)) : null,
            isCCEncryptionEnabled ? 1 : null
        ];
        return queryParams;
    }

    async convertStringtoTimeStamp(dateString) {
        let date = new Date(dateString);
        let utcTime = date.getTime();
        let localDate = new Date(utcTime);
        localDate.setHours(0, 0, 0, 0);
        return (localDate.getTime());
    }

    checkTimestampFlag(timestampMillis) {
        const nowMillis = Date.now(); // Current date in milliseconds
        const nowDate = new Date(nowMillis);
        const yearFromNowMillis = nowMillis + 31536000000; // 1 year in milliseconds
        const currentMonth = nowDate.getMonth();
        const currentDay = nowDate.getDate();

        if (currentDay > 10) {
          // Check if the timestamp is between the 1st of the current month and a year from now
            const firstOfCurrentMonthMillis = new Date(nowDate.getFullYear(), currentMonth, 1).getTime();
          if (
            timestampMillis >= firstOfCurrentMonthMillis &&
            timestampMillis <= yearFromNowMillis
          ) {
            return true;
          }
        } else {
          // Check if the timestamp is between the 1st of the previous month and a year from now
          const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1; // Handle January
            const firstOfPreviousMonthMillis = previousMonth == 11 ? new Date(nowDate.getFullYear() - 1, previousMonth, 1).getTime() : new Date(nowDate.getFullYear(), previousMonth, 1).getTime();
          if (
            timestampMillis >= firstOfPreviousMonthMillis &&
            timestampMillis <= yearFromNowMillis
          ) {
            return true;
          }
        }

        return false;
      }


    yearName(timestamp) {
        let date = new Date(timestamp);
        let fullYear = date.getFullYear();
        let lastTwoDigits = (fullYear % 100).toString();

        return lastTwoDigits;
    }

    getMonthName(timestamp) {
        let date = new Date(timestamp);
        let shortMonthNames = [
          "jan", "feb", "mar", "apr", "may", "jun",
          "jul", "aug", "sep", "oct", "nov", "dec"
        ];

        let monthIndex = date.getMonth();
        let shortMonthName = shortMonthNames[monthIndex];

        return shortMonthName;
      }
      async generateBucketNameFromTimestampAndHash(customer_id, recharge_number, timestamp) {
        const self = this;
        try{
            const inputString = `${customer_id}${recharge_number}`;
            const hash = CRYPTO.createHash('md5').update(inputString).digest('hex');

            // const date = new Date(timestamp);

            // const day = date.getDate().toString().padStart(2, '0'); // 2-digit day
            // const month = new Intl.DateTimeFormat('en-US', { month: 'short' }).format(date); // Short month name
            // const year = date.getFullYear();
            let value;
            if(process.env.NODE_ENV=='production') {
                value = 2000;
            } else {
                value = 40;
            }

            const truncatedHash = hash.substring(0, 8);

            const hashValue = parseInt(truncatedHash, 16) % value;
            const resultString = `${hashValue}`;


            return resultString;
        } catch(err) {
            self.L.error(`error in generating bucketname`, err);
            return null;
        }

      }

    async convertBillDatetoTime(dateString) {
        let date = new Date(dateString);
        let utcTime = date.getTime();
        let localDate = new Date(utcTime);

        return (localDate.getTime());
    }

    async convertDueDatetoTime(dateString) {
        let date = new Date(dateString);
        let utcTime = date.getTime();
        let localDate = new Date(utcTime);

        return (localDate.getTime());
    }
    generateUpdateQuery(tableName, flag) {
        let query;
        if(flag == 1) {
            query = `UPDATE ${tableName} SET status= 13 WHERE operator = ? AND service = ? AND due_date = ? AND bucket_id = ? AND recharge_number = ? AND customer_id = ? IF EXISTS`;
        } else {
            query = `UPDATE ${tableName} SET status= 13 WHERE operator = ? AND service = ? AND bill_date = ? AND bucket_id = ? AND recharge_number = ? AND customer_id = ? IF EXISTS`;
        }

        return query;
    }
    generateUpdateQueryForUpsert(tableName, flag) {
        let query;
        if(flag == 1) {
            query = `UPDATE ${tableName} SET status= 13 WHERE operator = ? AND service = ? AND due_date = ? AND bucket_id = ? AND recharge_number = ? AND customer_id = ?`;
        } else {
            query = `UPDATE ${tableName} SET status= 13 WHERE operator = ? AND service = ? AND bill_date = ? AND bucket_id = ? AND recharge_number = ? AND customer_id = ?`;
        }

        return query;
    }

    generateUpdateQueryParams(params, dueDateTimeStamp, bucketName, isCCEncryptionEnabled = false) {
        const self = this;
        let QueryParams = [
            _.get(params,'operator',null),
            _.get(params,'service',null),
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(dueDateTimeStamp) : dueDateTimeStamp,
            bucketName,
            _.get(params,'recharge_number',null),
            _.get(params,'customer_id',null)
        ];
        return QueryParams;
    }
    generateQueryParamsForRecentAndDelete(params, dueDateTimeStamp, bucketName, recharge_number) {
        const self = this;
        let QueryParams = [
            _.get(params,'operator',null),
            _.get(params,'service',null),
            dueDateTimeStamp,
            bucketName,
            recharge_number,
            params.customerId
        ];
        return QueryParams;
    }
    generateQuery(tableName, isDueDateTable = false) {
        let Query = `INSERT INTO ${tableName} (recharge_number, customer_id, operator , service, product_id, bill_date, paytype,
            circle, customer_mobile, customer_email, amount, status, user_data, notification_status, due_date, extra ,bank_name ,card_network, customer_other_info, bill_fetch_date,
            due_amount, create_at, update_at, payment_date, bucket_id, enc_amount, enc_due_date, is_encrypted)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            return Query;
    }
    generateInsertQueryandParams(params, extra, billDateTimeStamp, dueDateTimeStamp, bucketName, isCCEncryptionEnabled = false, isDueDateTable = false) {
        const self = this;
        let date = this.getDate();
        let QueryParams = [
            params.rechargeNumber,
            params.customerId,
            params.operator,
            _.toLower(params.service),
            params.productId,
            billDateTimeStamp,
            params.paytype,
            _.get(params,'circle',null),
            _.get(params,'customerMobile',null),
            _.get(params,'customerEmail',null),
            isCCEncryptionEnabled ? null : _.get(params,'amount',0),
            params.status,
            _.get(params,'user_data',null),
            _.get(params, 'notificationStatus', _.get(self.config, 'COMMON.notification_status.ENABLED', 1)),
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(dueDateTimeStamp) : dueDateTimeStamp,
            extra,
            _.get(params, 'bankName', null),
            _.get(params, 'cardNetwork', null),
            params.customerOtherInfo,
            _.get(params, 'billFetchDate', _.get(params, 'bill_fetch_date', null)),
            isCCEncryptionEnabled ? null : params.amount,
            _.get(params,'create_at',date),
            date,
            _.get(params,'paymentDate',null),
            bucketName,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.toString(_.get(params,'amount',0))) : null,
            isCCEncryptionEnabled ? self.encryptHelper.encryptData(_.get(params, 'dueDate', null)) : null,
            isCCEncryptionEnabled ? 1 : null
        ];
        return QueryParams;
    }

    async updateRemindLaterDate(params, cassandraRowData, cassandraCdcPublisher) {

        var self = this;

        let query = `UPDATE ${self.tableResolver.getNonRUTableName(params.customer_id)} SET remind_later_date = ?, extra = ? WHERE`;
        let queryParams = [params.remindLaterDate, params.updatedExtra];

         [query, queryParams] = self.commonLib.formatQueryAndParams(query, queryParams, params.recharge_number, params.operator);

         query += ` AND customer_id = ? AND service = ? AND operator = ?`;
        queryParams.push(params.customer_id, _.toLower(params.service), _.toLower(params.operator));

         self.L.log(`nonPaytmBills::updateRemindLaterDate :: Updating remind_later_date for query ${query} and params ${JSON.stringify(queryParams)}`);

         try {
             await self.client.execute(query, queryParams, { prepare: true });
             cassandraRowData.remindLaterDate = MOMENT(params.remindLaterDate).format('YYYY-MM-DD HH:mm:ss');
             cassandraRowData.remindLaterFlow = true;
             cassandraRowData.extra = params.updatedExtra;
             await self.publishCDCEvents(cassandraRowData,cassandraCdcPublisher);

         } catch (error) {
             console.error(`Failed to update nonPaytmBills: ${error}`);
             throw error;
         }
     }

    async insertIntoRemindLaterTable(params) {
        let remindLaterDate = params.remindLaterDate ? MOMENT(params.remindLaterDate).utc().startOf('day').format('YYYY-MM-DD HH:mm:ss') : null;
        var self = this;
        let bucketName = await this.generateBucketNameFromTimestampAndHash(params.customerId, params.rechargeNumber, null);
        const ttl = remindLaterDate ? MOMENT(params.remindLaterDate).utc().add(7, 'days').diff(MOMENT(params.remindLaterDate).utc(), 'seconds') : null;
        const query = `INSERT INTO ${self.tableResolver.getRemindLaterDateEventTable()} (remind_later_date, bucket_id, service, operator, recharge_number, customer_id, type)
            VALUES (?, ?, ?, ?, ?, ?, ?) USING TTL ?`;
        const queryParams = [
            remindLaterDate,
            bucketName,
            _.toLower(_.get(params, 'service', null)),
            _.toLower(_.get(params, 'operator', null)),
            params.recharge_number,
            params.customer_id,
            params.type,
            ttl
        ];

        self.L.log(`nonPaytmBills::insertIntoRemindLaterTable :: Inserting remind_later_date for query ${query} and params ${JSON.stringify(queryParams)}`);

        try {
            await self.notificationNewClusterClient.execute(query, queryParams, { prepare: true });
        } catch (error) {
            console.error(`Failed to update into remindLaterDateEvent table: ${error}`);
            throw error;
        }
    }

    async publishCDCEvents(cassandraRowData, cassandraCdcPublisher, params) {
       var self = this;
        try {
            cassandraRowData.cdcEventType='u';
            cassandraRowData.updateAt = self.getDate();
            cassandraRowData.convertPayload='y';

            await new Promise((resolve, reject) => {
                self.publishCassandraCDCvents((err, data) => {
                    if (err) {
                        self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing: ${err} of record ${JSON.stringify(params)}`);
                        reject(new Error('Error publishing data to cdc'));
                    } else {
                        resolve(data);
                    }
                }, cassandraRowData, cassandraCdcPublisher);
            });
        } catch (error) {
            console.error('Error occurred:', error);
            throw error; // re-throw the error
        }
    }

    deleteUsingRecentRecordsPromise(params, cassandraCdcPublisher, bypassEncryption = true) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.deleteUsingRecentRecords(params, cassandraCdcPublisher, (resp) => {
                if(resp == undefined) {
                    reject('deleteUsingRecentRecordsPromise :: Error in deleting record');
                } else {
                    resolve();
                }
            }, bypassEncryption);
        })
    }

    insertRecordInBillsRecentRecordsPromise(params, insertEncryptedRecord = false) {
        let self = this;
        return new Promise((resolve, reject) => {
            self.insertRecordInBillsRecentRecords(params, (resp) => {
                if(resp == null) {
                    resolve();
                } else {
                    reject(resp);
                }
            }, insertEncryptedRecord);
        });
    }

    //existingRecord = existingRecordInBillsNonPaytm
    async deleteFromBillDueAndBillGenTable(cb, params, existingRecord, bypassEncryption = true) {
        let self = this;
        try {
            let recharge_number = params.rechargeNumber;
            if(existingRecord && existingRecord.length == 1 && _.get(existingRecord[0],'due_date',null) != null){
                let dueDateTimeStamp = _.get(existingRecord[0], 'due_date', null);
                dueDateTimeStamp = await self.convertStringtoTimeStamp(dueDateTimeStamp);
                let monthName = self.getMonthName(dueDateTimeStamp);
                let yearName = self.yearName(dueDateTimeStamp);
                let tableName = self.tableResolver.getBillDueTablePrefix(params.customerId, params.service, params.paytype, bypassEncryption)+monthName+'_'+yearName;
                let bucketName = await self.generateBucketNameFromTimestampAndHash(params.customerId, recharge_number, dueDateTimeStamp);
                let query = self.generateUpdateQuery(tableName, 1);

                let customerId = _.get(existingRecord[0], 'customer_id', null);
                let service = _.get(existingRecord[0], 'service', null);
                let paytype = _.get(existingRecord[0], 'paytype', null);
                let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(service, paytype, customerId);
                let ccbpDueDateTimestamp = dueDateTimeStamp;
                if(!bypassEncryption && isCCEncryptionEnabled) {
                    ccbpDueDateTimestamp = self.getFormattedDueDateForCCBPEncryption(dueDateTimeStamp, isCCEncryptionEnabled);
                    ccbpDueDateTimestamp = self.encryptHelper.encryptData(ccbpDueDateTimestamp);
                    recharge_number = self.encryptHelper.encryptData(recharge_number);
                    self.L.log(`[nonPaytmBills.processRecordsforDeletion] ccbpDueDateTimestamp = ${ccbpDueDateTimestamp} enc rechargeNumber = ${recharge_number}`);
                }

                let queryParams = self.generateQueryParamsForRecentAndDelete(params, ccbpDueDateTimestamp, bucketName, recharge_number);
                if(self.checkTimestampFlag(dueDateTimeStamp)==true){
                    await self.updateDueAndBillTable(params, query, queryParams);
                    self.L.log(`deleteFromBillDueAndBillGenTable :: delete from non ru dueDate table :: deleted successfully`);
                }

            }
            if(existingRecord && existingRecord.length == 1 && _.get(existingRecord[0],'bill_date',null) != null){
                let billDateTimeStamp = _.get(existingRecord[0], 'bill_date', null);
                billDateTimeStamp = await self.convertStringtoTimeStamp(billDateTimeStamp);
                let monthName = self.getMonthName(billDateTimeStamp);
                let yearName = self.yearName(billDateTimeStamp);
                let tableName = self.tableResolver.getBillGenTablePrefix(params.customerId) + monthName+'_'+yearName;
                let bucketName = await self.generateBucketNameFromTimestampAndHash(params.customerId, params.rechargeNumber, billDateTimeStamp);
                let query = self.generateUpdateQuery(tableName, 0);
                let queryParams = self.generateQueryParamsForRecentAndDelete(params, billDateTimeStamp, bucketName, recharge_number);
                if(self.checkTimestampFlag(billDateTimeStamp)==true){
                    await self.updateDueAndBillTable(params, query, queryParams);
                    self.L.log(`deleteFromBillDueAndBillGenTable :: delete from non ru bill gen table :: deleted successfully`);
                }

            }
            if(cb) {
                cb(null);
            }
        } catch (e) {
            if(cb) {
                cb(e);
            }
        }
    }

    getBillsRecord(cb, params){
        const self = this;
        const query = `SELECT * FROM ${self.nonPaytmBillsTable} WHERE recharge_number = ? AND customer_id = ? \
                        AND service = ? AND operator = ?`;
        const queryParams = [params.rechargeNumber, params.customerId, params.service, params.operator];
        self.client.execute(query, queryParams, { prepare : true }, (err, result) => {
            if(err){
                return cb('nonPaytmBills::getBillsRecord DB exception!' + JSON.stringify(queryParams) + err);
            } else {
                return cb(null, result.rows);
            }
        })
    }

    async processRecordsforOldBillCase(existingRecord, billsKafkaRow) {
        if(_.get(billsKafkaRow,'status',null) == _.get(this.config, 'COMMON.bills_status.OLD_BILL_FOUND', 5)){
            if (_.get(existingRecord, 'old_bill_fetch_date', null) != null) {
                _.set(billsKafkaRow, 'oldBillFetchDate', MOMENT(_.get(existingRecord, 'old_bill_fetch_date', null)).startOf('day').format('YYYY-MM-DD HH:mm:ss'));
            }
            else {
                _.set(billsKafkaRow, 'oldBillFetchDate', MOMENT().startOf('day').format('YYYY-MM-DD HH:mm:ss'));
            }
        }
    }

    processExtraField(params) {
        const self = this;
        let extra = _.get(params, 'extra', {});
        let recon_id = utility.generateReconID(
            params.rechargeNumber,
            params.operator,
            _.get(params, 'amount', 0),
            _.get(params, 'dueDate', null),
            _.get(params, 'billDate', null)
        );

        if (typeof extra === 'object') {
            _.set(extra, 'recon_id', recon_id);
            _.set(extra, 'user_type', "NON_RU");
            _.set(params, 'extra', extra);
            extra = JSON.stringify(extra);
        } else {
            extra = utility.setReconIdInString(extra, recon_id, "NON_RU");
            _.set(params, 'extra', extra);
        }

        return extra;
    }

    getDueAndGenParams(dueParams, genParams) {
        // Process parameters
        if (dueParams != null && _.isArray(dueParams) && dueParams.length > 0) {
            dueParams = dueParams[0];
        }
        if (genParams != null && _.isArray(genParams) && genParams.length > 0) {
            genParams = genParams[0];
        }
        return { dueParams: dueParams, genParams: genParams };
    }

    /**
         * Write batch records by customer
         * @param {Object} params - Input parameters
         * @param {Array} recentRecords - Recent records
         * @param {boolean} updateDueTable - Update due table flag
         * @param {boolean} updateGenTable - Update gen table flag
         * @param {Object} dueParams - Due parameters
         * @param {Object} genParams - Gen parameters
         * @param {Object} cassandraCdcPublisher - CDC publisher
         * @param {Object} existingDBRecord - Existing DB record
         * @returns {Promise<Object>} Processed parameters
         */
    async writeBatchRecordsNew(params, recentRecords, updateDueTable = null, updateGenTable = null, dueParams = null, genParams = null, cassandraCdcPublisher, existingDBRecord = null) {
        const self = this;
        const dueGenParams = this.getDueAndGenParams(dueParams, genParams);
        dueParams = dueGenParams.dueParams;
        genParams = dueGenParams.genParams;
        const extra = self.processExtraField(params);
        self.L.log(`writeBatchRecordsByCustomer :: ${extra}, updateDueTable : ${updateDueTable}, updateGenTable : ${updateGenTable}, dueParams : ${dueGenParams.dueParams && JSON.stringify(dueGenParams.dueParams)}, genParams : ${dueGenParams.genParams && JSON.stringify(dueGenParams.genParams)} for debugKey: ${params.debugKey}`);
        // Handle encryption
        const isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(params.service, params.paytype, params.customerId);
        const isExistingCCBPDBRecordDecrypted = isCCEncryptionEnabled && existingDBRecord && _.isArray(existingDBRecord) &&
            existingDBRecord.length == 1 && !self.checkIfDbRecordIsEncrypted(existingDBRecord[0]);
        // Get encrypted parameters
        const rootLevelKeysToEncrypt = ['rechargeNumber', 'refId', 'customerMobile', 'customerEmail'];
        const encrypted_params = self.encryptHelper.getEncryptedParamsFromGenericParams(params, rootLevelKeysToEncrypt);
        const rootLevelKeysToEncryptDueBill = ['recharge_number', 'reference_id', 'customer_mobile', 'customer_email'];
        const encrypted_dueParams = self.encryptHelper.getEncryptedParamsFromGenericParams(dueParams, rootLevelKeysToEncryptDueBill);
        const encrypted_genParams = self.encryptHelper.getEncryptedParamsFromGenericParams(genParams, rootLevelKeysToEncryptDueBill);
        // Process due date and bill date operations
        const [dueDateData, billDateData] = await Promise.all([
            this.processDueDateOperations(params, encrypted_params, isCCEncryptionEnabled, isExistingCCBPDBRecordDecrypted, updateDueTable, dueParams, encrypted_dueParams),
            this.processBillDateOperations(params, encrypted_params, isCCEncryptionEnabled, isExistingCCBPDBRecordDecrypted, updateGenTable, genParams, encrypted_genParams)
        ]);
        const batchQuery = this.generateBatchQuery(params, encrypted_params, isCCEncryptionEnabled, isExistingCCBPDBRecordDecrypted, updateDueTable, dueParams, encrypted_dueParams, updateGenTable, genParams, encrypted_genParams, recentRecords);
        const batchQueryNotification = this.generateBatchQueryNotifications(dueDateData, billDateData, updateDueTable, updateGenTable, params);
        this.sendMetricsForCustomerIDRange(dueDateData, billDateData, params);
        return this.executeBatchOperations(batchQuery, batchQueryNotification, params, cassandraCdcPublisher, isExistingCCBPDBRecordDecrypted);
    }

    /**
     * Generate batch queries for handling non-RU bills and recent records
     * @param {Object} params - Input parameters
     * @param {Object} encrypted_params - Encrypted parameters
     * @param {boolean} isCCEncryptionEnabled - CC encryption flag
     * @param {boolean} isExistingCCBPDBRecordDecrypted - Flag indicating if existing CCBP record is decrypted
     * @param {boolean} updateDueTable - Update due table flag
     * @param {Object} dueParams - Due parameters
     * @param {Object} encrypted_dueParams - Encrypted due parameters
     * @param {boolean} updateGenTable - Update gen table flag
     * @param {Object} genParams - Gen parameters
     * @param {Object} encrypted_genParams - Encrypted gen parameters
     * @param {Array} recentRecords - Recent records array
     * @returns {Array} Array of batch queries
     */
    generateBatchQuery(params, encrypted_params, isCCEncryptionEnabled, isExistingCCBPDBRecordDecrypted, updateDueTable, dueParams, encrypted_dueParams, updateGenTable, genParams, encrypted_genParams, recentRecords = []) {
        const self = this;
        const batchQuery = [{
            query: self.generateNonRUBillsQuery(self.tableResolver.getNonRUTableName(params.customerId)),
            params: self.generateNonRUBillsQueryParamNew(encrypted_params, self.processExtraField(params), isCCEncryptionEnabled)
        }];

        // Add recent records query if needed
        const recentRecordsQuery = this.generateRecentRecordsQuery(params, recentRecords, isCCEncryptionEnabled);
        if (recentRecordsQuery) {
            batchQuery.push(recentRecordsQuery);
        }

        return batchQuery;
    }

    /**
     * Check if record should be inserted into billgennonru table
     * @param {Object} billDateData - Bill date data
     * @param {Object} params - Input parameters
     * @returns {boolean} Whether to insert into billgennonru table
     */
    shouldInsertIntoBillGenNonRU(billDateData, params) {
        const billDateFlag = _.get(this.config, ['DYNAMIC_CONFIG', "NON_PAYTM_CONFIG", "BILL_GEN_NOT_UPDATED", `${params.service}::${params.paytype}`], null);
        billDateData.billDateFlag = billDateFlag;
        return (billDateData.billDateFlag && !(_.toLower(params.service) == 'mobile' &&
                _.toLower(params.paytype) == 'prepaid' &&
                (_.get(params, 'partialBillState', 'NO_STATE') === 'NO_STATE' ||
                _.get(params, 'partialBillState') === null)));
    }

    /**
     * Generate batch query notifications
     * @param {Object} dueDateData - Due date data
     * @param {Object} billDateData - Bill date data
     * @param {boolean} updateDueTable - Update due table flag
     * @param {boolean} updateGenTable - Update gen table flag
     * @param {Object} params - Input parameters
     * @returns {Array} Array of batch queries
     */
    generateBatchQueryNotifications(dueDateData, billDateData, updateDueTable, updateGenTable, params) {
        const batchQueryNotification = [];
        if (dueDateData.dueDateFlag) {
            batchQueryNotification.push({
                query: dueDateData.dueDateBillsQuery,
                params: dueDateData.dueDateQueryParams
            });
        }

        if (this.shouldInsertIntoBillGenNonRU(billDateData, params)) {
            batchQueryNotification.push({
                query: billDateData.billDateBillsQuery,
                params: billDateData.billDateQueryParams
            });
        }

        if (updateDueTable) batchQueryNotification.push({
            query: dueDateData.dueDateUpdateQuery,
            params: dueDateData.dueDateUpdateParams
        })

        if (updateGenTable) batchQueryNotification.push({
            query: billDateData.billDateUpdateQuery,
            params: billDateData.billDateUpdateParams
        })
        return batchQueryNotification;
    }

    /**
     * Send metrics for customer ID range
     * @param {Object} dueDateData - Due date data
     * @param {Object} billDateData - Bill date data
     * @param {Object} params - Input parameters
     */
    sendMetricsForCustomerIDRange(dueDateData, billDateData, params) {
        const self = this;
        const customerIDRange = self.tableResolver.getCustomerIDRange(params.customerId);

        if (dueDateData.dueDateFlag) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:BILLDUE_NON_PAYTM_BILLS",
                `MONTH:${dueDateData.monthName}`,
                `YEAR:${dueDateData.yearName}`,
                `CUSTOMERID_RANGE: ${customerIDRange}`
            ]);
        }

        if (billDateData.billDateFlag && !(_.toLower(params.service) == 'mobile' && _.toLower(params.paytype) == 'prepaid')) {
            utility._sendMetricsToDD(1, [
                "REQUEST_TYPE:BILLGEN_NON_PAYTM_BILLS",
                `MONTH:${billDateData.monthName}`,
                `YEAR:${billDateData.yearName}`,
                `CUSTOMERID_RANGE: ${customerIDRange}`
            ]);
            self.L.verbose(`nonPaytmBills::writeBatchRecords :: updateGenTable flag : ${billDateData.billDateFlag} for params ${JSON.stringify(params)}`);
        } else {
            self.L.verbose(`nonPaytmBills::writeBatchRecords :: updateGenTable not required flag : ${billDateData.billDateFlag} for params ${JSON.stringify(params)}`);
        }
    }

    /**
     * Execute main batch query
     * @param {Array} batchQuery - Batch query to execute
     * @param {Object} params - Input parameters
     * @returns {Promise} Promise resolving when batch query is executed
     */
    async executeMainBatchQuery(batchQuery, params) {
        const self = this;
        try {
            await this.client.batch(batchQuery, { prepare: true });
            // Send success metrics and update params
            utility.sendNonPaytmBillsMetrics('INSERTED', 'SUCCESS_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'WRITE_BATCH_RECORDS', params, 'Successfully updated data on cluster');
            self.L.log(`nonPaytmBills:: writeBatchRecords Data updated on reminder cluster for debugKey ${params.debugKey}`);
        } catch (error) {
            self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing batch query for debugKey ${params.debugKey} with error ${error}`);
            const errorMessage = error && error.message ? error.message : (error ? error.toString() : 'Unknown error');
            utility.sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params, errorMessage);
            throw error;
        }
    }

    /**
     * Execute notification batch query
     * @param {Array} batchQueryNotification - Notification batch query to execute
     * @param {Object} params - Input parameters
     * @returns {Promise} Promise resolving when notification batch query is executed
     */
    async executeNotificationBatchQuery(batchQueryNotification, params) {
        const self = this;
        if (batchQueryNotification.length > 0) {
            try {
                await self.notificationNewClusterClient.batch(batchQueryNotification, { prepare: true });
                utility.sendNonPaytmBillsMetrics('INSERTED', 'ERROR_WHILE_PUBLISHING_TO_CDC_RECOVERY', 'WRITE_BATCH_RECORDS', params, 'Successfully updated data on cluster');
                self.L.log(`nonPaytmBills:: writeBatchRecords:: Successfully updated data on notification cluster for debugKey ${params.debugKey}`);
            } catch (error) {
                self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing notification batch query for debugKey ${params.debugKey} with error ${error}`);
                const errorMessage = error && error.message ? error.message : (error ? error.toString() : 'Unknown error');
                try {
                    if (utility && typeof utility.sendNonPaytmBillsMetrics === 'function') {
                        utility.sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_EXECUTING_NOTIFICATION_BATCH_QUERY', 'WRITE_BATCH_RECORDS', params, errorMessage);
                    } else {
                        self.L.error('sendNonPaytmBillsMetrics function not available in utility');
                    }
                } catch (metricsError) {
                    self.L.error('Error while sending metrics:', metricsError);
                }
                throw error;
            }
        }
    }

    /**
     * Execute Delete using recent records for existing CCBP record
     * @param {Object} params - Input parameters
     * @param {Object} cassandraCdcPublisher - CDC publisher
        * @param {boolean} isExistingCCBPDBRecordDecrypted - Flag indicating if existing CCBP record is decrypted
     * @returns {Promise} Promise resolving when 
     */
    async executeDeleteUsingRecentRecordsForExistingCCBPRecord(params, cassandraCdcPublisher, isExistingCCBPDBRecordDecrypted) {
        const self = this;
        try {
            // Step 4: Handle existing CCBP record deletion if required
            if (isExistingCCBPDBRecordDecrypted) {
                await self.deleteUsingRecentRecordsPromise(params, cassandraCdcPublisher, true);
                self.L.log(`nonPaytmBills:: writeBatchRecords :: deleteUsingRecentRecords older decrypted record deleted`);
            } else {
                self.L.log(`nonPaytmBills:: writeBatchRecords :: deleteUsingRecentRecords not required as encryption is disabled for this category or existing ccbp record is not decrypted`);
            }
        } catch (error) {
            self.L.error(`nonPaytmBills::writeBatchRecords :: Error while executing deleteUsingRecentRecords for debugKey ${params.debugKey} with error ${error}`);
            const errorMessage = error && error.message ? error.message : (error ? error.toString() : 'Unknown error');
            utility.sendNonPaytmBillsMetrics('ERROR', 'ERROR_WHILE_DELETING_EXISTING_CCBP_RECORD', 'WRITE_BATCH_RECORDS', params, errorMessage);
            throw error;
        }
    }

    /**
     * Execute all batch operations
     * @param {Array} batchQuery - Main batch query
     * @param {Array} batchQueryNotification - Notification batch query
     * @param {Object} params - Input parameters
     * @param {Object} cassandraCdcPublisher - CDC publisher
     * @returns {Promise} Promise resolving when all operations are complete
     */
    async executeBatchOperations(batchQuery, batchQueryNotification, params, cassandraCdcPublisher, isExistingCCBPDBRecordDecrypted) {
        try {
            // Step 1: Execute main batch query
            await this.executeMainBatchQuery(batchQuery, params);

            // Step 2: Execute notification batch query if needed
            await this.executeNotificationBatchQuery(batchQueryNotification, params);

            // Step 3: Execute CDC events publishing
            await this.executeDeleteUsingRecentRecordsForExistingCCBPRecord(params, cassandraCdcPublisher, isExistingCCBPDBRecordDecrypted);

            return params;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Generate month and year names from timestamp using moment
     * @param {number} timestamp - Timestamp in milliseconds
     * @returns {Object} Object containing monthName and yearName
     */
    generateMonthAndYearNames(timestamp) {
        if (!timestamp) {
            return {
                monthName: null,
                yearName: null
            };
        }
        const momentDate = MOMENT(timestamp);
        return {
            monthName: momentDate.format('MMM').toLowerCase(),
            yearName: momentDate.format('YY')
        };
    }
}

export default NonPaytmBillsModel

// code for testing locally
// const cassandra = require('cassandra-driver');

// const client = new cassandra.Client({
//   contactPoints: [ '127.0.0.1'],
//   localDataCenter: 'datacenter1',
//   keyspace: 'test01'
// });

// const options = { fetchSize : 10000 };


// client.stream('SELECT * FROM bills_non_paytm', [])
//     .on('readable', function () {
//         let row;
//         while(row = this.read()){
//             console.log(row)
//             this.pause();
//             console.log('There will be no additional data for 1 second.');
//             setTimeout(() => {
//                 console.log('Now data will start flowing again.');
//                 this.resume();
//             }, 1000);
//         }
//     })
//     .on('end', ()=>{
//         console.log("end")
//     })
//     .on('error', (err) => {
//         console.log(err)
//     })

    // client.eachRow(
    //     'SELECT * FROM bills_non_paytm',
    //     [],
    //     options,
    //     (n, row) => {
    //       // The callback will be invoked per each row as soon as they are received
    //       console.log(n)
    //     },
//     (err, result) => {
    //         console.log(err)
    //         if(result.nextPage){
    //             setTimeout(() => {
    //                 console.log('Now data will start flowing again.');
    //                 result.nextPage();
    //             }, 5000);
    //         }
    //     }
    //   );


// const query = 'SELECT * FROM bills_non_paytm';

// client.execute(query, [  ])
//   .then(result => console.log('User with %s', JSON.stringify(result.rows)));
