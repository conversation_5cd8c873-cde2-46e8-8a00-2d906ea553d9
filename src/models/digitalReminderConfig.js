let L = null;
import MOMENT from "moment";
class DigitalReminderConfig {

    constructor(options) {
        L = options.L;
        this.dbInstance = options.dbInstance;
    }

    getDynamicConfig(done) {
        let self = this,
            query  = `select * from digital_reminder_config where status = ? OR (status = ? AND updated_at > "${MOMENT().subtract(45, 'minute').format("YYYY-MM-DD HH:mm:ss")}")`,
            queryParams = [
                1,0
            ];
        L.log(`Fetching configs from database digital_reminder_config:`,  self.dbInstance.format(query, queryParams));
   		self.dbInstance.exec(function (err, data){
            if(err || !data) {
                let errMsg = `DigitalReminderConfig config not found: ${err}`;
                L.critical(errMsg);
                return done(new Error(errMsg));
            }
            return done(null, data);
        }, 'DIGITAL_REMINDER_SLAVE', query, queryParams);
    }
    
    updateDynamicConfig(query, queryParams) {
        let self = this;

        L.log(`update digital_reminder_config:: query= ${query} and query-params= `, queryParams);

        return new Promise((resolve, reject) => {
            self.dbInstance.exec(function (err, data) {
                if (err || !data) {
                    return reject(err);
                }
                resolve(data);
            }, 'DIGITAL_REMINDER_MASTER', query, queryParams);
        })
        
    }
}

export default DigitalReminderConfig;
