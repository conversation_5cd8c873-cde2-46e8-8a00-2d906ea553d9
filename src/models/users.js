import MOMENT    from 'moment'
import _         from 'lodash'
import RecentsLayer from '../lib/recentsLayer';
import utility from '../lib'

let L = null;

class Users {
    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.config = options.config;
        this.mongoDbInstance = options.mongoDbInstance;
        this.recentsLayer = new RecentsLayer(options);

        this.includedOperatorList = _.get(this.config, ['DYNAMIC_CONFIG', 'OPERATOR_MONTHLY_PAYMENT', 'COMMON', 'INCLUDE_OPERATOR'], 'rent payment');
        this.includedOperator = this.includedOperatorList.split(',').map((e) => e.trim());
    }
    /**
     * Updates user's billReminder flag in users MongoCollection
     * Currently only latest bill is stored for a user
     * @param {function} cb 
     * @param {String} tableName 
     * @param {Object} params 
     */
    updateBillReminderFlag (cb, tableName, params, remindableFlag) {
        return cb(); // MONGO not in use now
        // let self = this,
        //     dataObj = {},
        //     query = {},
        //     set = {},
        //     options = {};
        // _.set(set, 'BillReminder', remindableFlag);
        
        // // if operator is migrated then update bill details for operator in [operator name list]
        // let operator = _.get(params, 'operator');
        // if (_.get(self.config, ['COMMON', 'MIGRATED_OPERATORS', operator], null)) {
        //     let operatorIn = {
        //         '$in' : _.get(self.config, ['COMMON', 'MIGRATED_OPERATORS', operator])
        //     } 
        //     _.set(query,'operator',operatorIn);
        // } else {
        //     _.set(query, 'operator',operator);            
        // }

        // _.set(query, 'recharge_number', _.get(params, 'rechargeNumber'));

        // if (_.get(params, 'isCreditCardOperator', null)) {
        //     _.set(query, 'customer_id', _.get(params, 'customerId'));
        // }
        // //only update value if exists in users collection 
        // _.set(options, 'upsert', false);
        // _.set(options, 'multi', true);
        // _.set(dataObj, 'query', query);
        // _.set(dataObj, 'update.$set', set);
        // _.set(dataObj, 'options', options);

        // try {
        //     self.mongoDbInstance.updateDataInCollection(function (err, data) {
        //         if (err || !(data)) {
        //             utility._sendMetricsToDD(1, ["REQUEST_TYPE:BILL_SUBSCRIBER", 'STATUS:ERROR',"TYPE:UPDATE_BILLREMINDER_FLAG"]);
        //             L.error('updateBillReminderFlag::', 'error occurred while dumping data in MongoDB: ', err);
        //         }
        //         else {
        //             L.log(`updateBillReminderFlag:: updated succesfully query: ${JSON.stringify(query)}`);
        //         }
        //         cb(err, data);

        //     }, tableName, dataObj);
        // } catch (ex) {
        //     L.critical("Exception encountered while updating users' bill in Mongo DB..", ex);
        //     //not returning any error in callback ,just creating log at critical level
        //     cb();
        // }
    
    }

    /**
     * Updates user's bill in users MongoCollection
     * Currently only latest bill is stored for a user
     * @param {function} cb 
     * @param {String} tableName 
     * @param {Object} params 
     */
    updateUserBill(cb, tableName, params) {
        return cb(); // MONGO not in use now
        // let self       = this,
        //     dataObj    = {},
        //     billsObj   = {},
        //     query      = {},
        //     set        = {},
        //     due_date   = null,
        //     updated_at = MOMENT().toISOString(),
        //     options    = {},
        //     amount;
        // /*
        //     Due date of bill can be obtained from one of the below mentioned keys in params object
        //     1.dueDate     -->from createBill / createMultipleBill API
        //     2.billDueDate --> billSubscribe service cron
        // */
        // due_date = _.get(params,'dueDate',null) ? _.get(params,'dueDate',null) : _.get(params,'billDueDate',null);
        // let isDueDateValid = MOMENT(due_date,'YYYY-MM-DD',true);
        // if (!isDueDateValid.isValid()) {
        //     L.error("updateUserBill::Invalid due date found ",`[recharge_number: ${params.rechargeNumber}, operator : ${params.operator} ,product_id: ${params.productId}, amount: ${params.amount}, billDueDate: ${due_date}`);
        //     due_date = null;
        // } else {
        //     due_date = MOMENT(due_date, 'YYYY-MM-DD', true).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        // }
        // amount = parseFloat(Number(_.get(params,'amount',0)).toFixed(2)),
        // _.set(billsObj,'due_date',due_date);
        // _.set(billsObj,'bill_date',_.get(params,'billDate',null));
        // _.set(billsObj,'amount',amount);
        // if(due_date && amount){
        //     _.set(billsObj,'label',"Bill Payment of Rs"+amount+" due on "+MOMENT(due_date).format('DD MMM YYYY'));
        // } else {
        //     _.set(billsObj,'label',null);
        // }

        // //if operator paytype is credit card then add customerID in query
        // if (_.get(params, 'isCreditCardOperator', null)) {
        //     _.set(query, 'customer_id', _.get(params, 'customerId'));
        // }
        
        // //bills and updated_at columns needs to be updated
        // _.set(set,'bills',[billsObj]);
        // // _.set(set,'updated_at',updated_at);
        // //setting filter query based upon which updation occurs

        // // if operator is migrated then update bill details for operator in [operator name list]
        // let operator = _.get(params, 'operator');
        // if (_.get(self.config, ['COMMON', 'MIGRATED_OPERATORS', operator], null)) {
        //     let operatorIn = {
        //         '$in' : _.get(self.config, ['COMMON', 'MIGRATED_OPERATORS', operator])
        //     } 
        //     _.set(query,'operator',operatorIn);
        // } else {
        //     _.set(query, 'operator',operator);            
        // }
        // // IN-5413 : No Need of product id when we are using the operator label.
        // //_.set(query,'product_id',_.get(params,'productId'));
        // _.set(query,'recharge_number',_.get(params,'rechargeNumber'));

        // // if(_.get(params,'customer_id',null)){
        // //     _.set(query,'customer_id',_.get(params,'customer_id'));
        // // }
        
        // //only update value if exists in users collection 
        // _.set(options, 'upsert', false );
        // _.set(options, 'multi', true );
        // _.set(dataObj,'query',query);
        // _.set(dataObj,'update.$set',set);
        // _.set(dataObj,'options',options);
        
        // try {
        //     self.mongoDbInstance.updateDataInCollection(function (err, data) {
        //         if(err || !(data)) {
        //             L.error('UpdateUserBill::', 'error occurred while dumping data in MongoDB: ', err);
        //         }
        //         else {
        //             L.log(`UpdateUserBill:: [recharge_number: ${params.rechargeNumber}, product_id: ${params.productId}, amount: ${params.amount}, billDueDate: ${params.billDueDate}, nextBillFetchDate: ${params.nextBillFetchDate}, query: ${JSON.stringify(query)}]`,',billObject:',JSON.stringify(billsObj))
        //         }
        //         cb(err,data);

        //     }, tableName, dataObj);
        // } catch(ex){
        //     L.critical("Exception encountered while updating users' bill in Mongo DB..",ex);
        //     //not returning any error in callback ,just creating log at critical level
        //     cb();
        // }
    }

    /**
     * 
     * @param {function} cb 
     * @param {Object} params 
     * @param {string} source 
     */
    updateBillsInRecents(cb, params,source='defaultSource') {
        return cb(); // MONGO not in use now
        // let self       = this,
        //     billsObj   = {},
        //     due_date   = null,
        //     amount;
        // /*
        //     Due date of bill can be obtained from one of the below mentioned keys in params object
        //     1.dueDate     -->from createBill / createMultipleBill API
        //     2.billDueDate --> billSubscribe service cron
        // */
        // due_date = _.get(params,'dueDate',null) ? _.get(params,'dueDate',null) : _.get(params,'billDueDate',null);
        // let isDueDateValid = MOMENT(due_date,'YYYY-MM-DD',true);
        // if (!isDueDateValid.isValid()) {
        //     L.error("updateUserBill::Invalid due date found ",`[recharge_number: ${params.rechargeNumber}, operator : ${params.operator} ,product_id: ${params.productId}, amount: ${params.amount}, billDueDate: ${due_date}`);
        //     due_date = null;
        // } else {
        //     due_date = MOMENT(due_date, 'YYYY-MM-DD', true).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        // }

        // if (!due_date) {
        //     console.log("updateUserBill:: Not updating recents as duedate is null for : ", params);
        //     return cb();
        // }

        // amount = parseFloat(Number(_.get(params, 'amount', 0)).toFixed(2));
        // _.set(billsObj,'due_date',due_date);
        // _.set(billsObj,'bill_date',_.get(params,'billDate',null));
        // _.set(billsObj, 'amount', amount);
        // _.set(billsObj, 'original_due_amount', amount);
    
        // if(due_date && amount){
        //     _.set(billsObj,'label',"Bill Payment of Rs"+amount+" due on "+MOMENT(due_date).format('DD MMM YYYY'));
        // } else {
        //     _.set(billsObj,'label',null);
        // }

        // if(params.plan_bucket){
        //     _.set(billsObj, 'plan_bucket', params.plan_bucket); 
        //     _.set(billsObj, 'plan', params.plan);
        // }
        
        // let queryParameters = {
        //     rechargeNumber: params.rechargeNumber,
        //     operator: params.operator,
        //     service: params.service,
        //     paytype: params.paytype
        // };

        // if(self.includedOperator.includes(params.operator)){
        //     _.set(queryParameters, 'customerId', _.get(params, 'customerId'));
        // }
    
        // if (_.get(params, 'isCreditCardOperator', null)) {
        //     _.set(queryParameters, 'customerId', _.get(params, 'customerId'));
        //     _.set(queryParameters, 'referenceId', _.get(params, 'referenceId'));
        //     _.set(billsObj, 'min_due_amount', _.get(params, 'min_due_amount', null));
        //     _.set(billsObj,'original_min_due_amount', _.get(params, 'min_due_amount', null));
        // }
        
        // let fieldValue = [billsObj];
        // self.updateFieldInRecents(function () {
        //     return cb();
        // }, queryParameters,'bills',fieldValue,source);
    }

    
    /**
     * 
     * @param { function } cb 
     * @param { string } tableName 
     * @param { object } params 
     */
    fetchAndUpdateUserBill(cb, tableName, params) {
        return cb(); // MONGO not in use now
        // let self = this;

        // try {
        //     let query    = {},
        //         queryObj = {};
        //     _.set(query,'operator',_.get(params,'operator'));
        //     _.set(query,'product_id',_.get(params,'productId'));
        //     _.set(query,'recharge_number',_.get(params,'rechargeNumber'));
        //     if(_.get(params,'customerId',0)){
        //         _.set(query,'customer_id',_.get(params,'customerId'));
        //     }
        //     _.set(queryObj,'query',query);
        //     _.set(queryObj,'limit',1);

        //     self.mongoDbInstance.fetchDataFromCollection(function(err,record){
        //         if (err || !record.length) {
        //             L.error('fetchAndUpdateUserBill::',`Either error found / no record exists in DB ....query : ${JSON.stringify(queryObj)} , error : ${err} `);
        //             cb();
        //         } else {
        //             let inputBillAmount   = _.get(params,'amount',0),
        //                 billAmountUpdated = inputBillAmount + _.get(record,'[0]bills[0].amount',0);
        //             _.set(params,'amount',billAmountUpdated);
        //             self.updateUserBill(cb, tableName, params);
        //         }
        //     },tableName,queryObj);
        // } catch (ex) {
        //     L.critical("fetchAndUpdateUserBill:: Exception encountered while fetching users' details from Mongo DB..",ex);
        //     cb();
        // }
    }

    updateFieldInRecents(cb, params,fieldName,fieldValue,source="defaultSource") {
        return cb(); // MONGO not in use now
        // let self = this;

        // let queryParams = {
        //     recharge_number: params.rechargeNumber,
        //     operator: params.operator,
        //     service: params.service,
        //     paytype: params.paytype
        // };
        // params.customerId ? _.set(queryParams, 'customer_id', params.customerId) : '';

        // params.referenceId ? _.set(queryParams, 'reference_id', params.referenceId) : '';
        // self.recentsLayer.update((err, results) => {
        //     return cb(err, results);
        // }, queryParams, fieldName, fieldValue, source);
    }
}

export default Users
