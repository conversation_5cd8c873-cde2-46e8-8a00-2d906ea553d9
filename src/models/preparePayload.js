import MOMENT from 'moment';
import _ from 'lodash'
import utility from '../lib';
import EncryptionDecryptioinHelper from '../lib/EncryptionDecryptioinHelper';

class preparePayload {
    constructor(options) {
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
        this.commonLib = new utility.commonLib(options);
        this.encryptHelper = new EncryptionDecryptioinHelper(options);
    }

    preparePayloadForNonPaytmBillsConsumer(payload, source) {
        let self = this;
        let finalPayload = {
            customerId: _.get(payload, 'customerId', null),
            rechargeNumber: _.get(payload, 'rechargeNumber', null),
            productId: _.get(payload, 'productId', null),
            operator: _.get(payload, 'operator', null),
            service: _.get(payload, 'service', null),
            amount: _.get(payload, 'amount', _.get(payload, 'currentBillAmount', null)),
            dueDate: MOMENT(payload.billDueDate).isValid() ? MOMENT(payload.billDueDate).format('YYYY-MM-DD HH:mm:ss') : null,
            billDate: _.get(payload, 'billDate', MOMENT().format('YYYY-MM-DD HH:mm:ss')),
            billFetchDate: _.get(payload, 'billFetchDate', MOMENT().format('YYYY-MM-DD HH:mm:ss')),
            paytype: _.get(payload, 'paytype', null),
            circle: _.get(payload, 'circle', null),
            customer_mobile: _.get(payload, 'customer_mobile', _.get(payload, 'customerMobile', null)),
            customer_email: _.get(payload, 'customer_email', _.get(payload, 'customerEmail', null)),
            status: _.get(self.config, ['COMMON', 'bills_status', 'BILL_FETCHED'], 4),
            notificationStatus: _.get(self.config, ['COMMON', 'notification_status', 'ENABLED'], 1),
            customerOtherInfo: _.get(payload, ['billsData', 'customerOtherInfo'], '{}'),
            extra: _.get(payload, ['billsData', 'extra'], '{}'),
            recordFoundOfSameCustId: _.get(payload, 'recordFoundOfSameCustId', null),
            source: 'UPMS',
            dbEvent: "upsert",
            isDuplicateCANumberOperator: _.get(payload, 'isDuplicateCANumberOperator', false),
            alternateRechargeNumber: _.get(payload, 'alternateRechargeNumber', null),
            alternateCAExistsInDB: _.get(payload, 'alternateCAExistsInDB', false),
            reason: _.get(payload, 'reason', null)
        };

        return finalPayload;
    }

    async prepareNonRuBillNotificationPayload(record, toBeNotifiedForRealTime = false) {
        let self = this;
        try {
            const payload = {
                topic: _.get(self.config, ['KAFKA', 'SERVICES', 'NONRU_BILL_FETCH', 'TOPIC'], ''),
                source: "nonRUbillDuePublisher",
                notificationType: "BILLGEN",
                data: {
                    customer_id: record.customerId,
                    recharge_number: record.rechargeNumber,
                    product_id: record.productId,
                    operator: record.operator,
                    due_amount: record.amount,
                    bill_fetch_date: MOMENT(),
                    paytype: record.paytype,
                    service: record.service,
                    circle: record.circle,
                    customer_mobile: null,
                    customer_email: null,
                    bank_name: record.operator,
                    status: this.config.COMMON.bills_status.BILL_FETCHED,
                    user_data: null,
                    bill_date: record.billDate,
                    notification_status: 1,
                    due_date: record.dueDate ? MOMENT(record.dueDate).utc().valueOf() : null,
                    extra: record.extra,
                    plan_bucket: record.planBucket,
                    partialBillState: _.get(record, "partialBillState", null),
                    templates: _.get(record, "templates", null),
                    customer_other_info: _.get(record, "customerOtherInfo", '{}'),
                    dwhKafkaPublishedTime: _.get(record, 'dwhKafkaPublishedTime', null),
                    nonpaytm_onBoardTime: _.get(record, 'nonpaytm_onBoardTime', null),
                    nonPaytmAcknowledgeTime: _.get(record, 'nonPaytmAcknowledgeTime', null),
                }
            };
            if (!toBeNotifiedForRealTime) {
                toBeNotifiedForRealTime = self.commonLib.decideTopicToPublishBillGen();
            }
            if (toBeNotifiedForRealTime) {
                _.set(payload, 'source', 'nonRUbillDuePublisherRealtime');
                _.set(payload, 'topic', _.get(self.config, ['KAFKA', 'SERVICES', 'NONRU_NOTIFICATION_PIPELINE_REALTIME', 'NONRU_NOTIFICATION_REALTIME'], ''));
            }
            utility.sendNotificationMetricsFromSource(record)
            return payload;
        } catch (error) {
            self.L.error("preparePayload::prepareNonRuBillNotificationPayload", `Error preparing non-RU bill notification payload: ${error}`);
            return null;
        }
    }

    async prepareNonRuBillFetchPublisherPayload(record, isMultiPid) {
        let self = this;
        let payload = {
            customerId: _.get(record, 'customerId', _.get(record, 'customer_id', null)),
            rechargeNumber: _.get(record, 'rechargeNumber', _.get(record, 'recharge_number', null)),
            productId: _.get(record, 'productId', _.get(record, 'product_id', null)),
            operator: _.get(record, 'operator', null),
            amount: _.get(record, 'amount', null),
            dueDate: _.get(record, 'dueDate', _.get(record, 'due_date', null)),
            billDate: _.get(record, 'billDate', _.get(record, 'bill_date', null)),
            billFetchDate: _.get(record, 'billFetchDate', _.get(record, 'bill_fetch_date', null)),
            next_bill_fetch_date: _.get(record, 'nextBillFetchDate', _.get(record, 'next_bill_fetch_date', null)),
            paytype: _.get(record, 'paytype', null),
            service: _.get(record, 'service', null),
            circle: _.get(record, 'circle', null),
            categoryId: _.get(record, 'categoryId', _.get(record, 'category_id', null)),
            is_active_expired_user: _.get(record, 'is_active_expired_user', null),
            service_payment_date_list: _.get(record, 'service_payment_date_list', null),
            latest_payment_date: _.get(record, 'latest_payment_date', null),
            customer_mobile: _.get(record, 'customer_mobile', _.get(record, 'customerMobile', null)),
            customer_email: _.get(record, 'customer_email', _.get(record, 'customerEmail', null)),
            status: _.get(record, 'status', null),
            notification_status: 1,
            customerOtherInfo: _.get(record, 'customerOtherInfo', null),
            extra: _.get(record, 'extra', null),
            dwhClassId: _.get(record, 'dwhClassId', null),
            rtspClassId: _.get(record, 'rtspClassId', null),
            source: _.get(record, 'source', "nonPaytmBillsConsumer")
        }
        record.topic = _.get(self.config.KAFKA, 'SERVICES.PUBLISHER_NON_RU.TOPIC', '')
        if (isMultiPid) {
            _.set(payload, 'topic', _.get(this.config, ['KAFKA', 'SERVICES', 'PUBLISHER_NON_RU_MULTIPLE_PID', 'TOPIC'], ''));
        }
        return payload;
    }


    preparePayloadForCassandraCDC(record) {
        const self = this;
        let customerId = _.get(record, 'customer_id', _.get(record, 'customerId', null));
        let service = _.get(record, 'service', null);
        let paytype = _.get(record, 'paytype', null);
        let isCCEncryptionEnabled = self.encryptHelper.isWhitelistedForCC(service, paytype, customerId);
        // Update CDC-related parameters
        record.cdcEventType = 'u';
        record.convertPayload = 'y';
        record.updateAt = MOMENT().format('YYYY-MM-DD HH:mm:ss');
        record.createAt = _.get(record, 'create_at', MOMENT().format('YYYY-MM-DD HH:mm:ss'));
        let cdcPayload = {
            'customerId': _.get(record, 'customer_id', _.get(record, 'customerId', null)),
            'rechargeNumber': _.get(record, 'recharge_number', _.get(record, 'rechargeNumber', null)),
            'productId': _.get(record, 'product_id', _.get(record, 'productId', null)),
            'operator': _.get(record, 'operator', null),
            'amount': _.get(record, 'amount', _.get(record, 'amount', null)),
            'dueDate': _.get(record, 'due_date', _.get(record, 'dueDate', null)),
            'billDate': _.get(record, 'bill_date', _.get(record, 'billDate', null)),
            'billFetchDate': _.get(record, 'bill_fetch_date', _.get(record, 'billFetchDate', null)),
            'nextBillFetchDate': _.get(record, 'next_bill_fetch_date', _.get(record, 'nextBillFetchDate', null)),
            'customerOtherInfo': _.get(record, 'customer_other_info', _.get(record, 'customerOtherInfo', null)),
            'paytype': _.get(record, 'paytype', null),
            'service': _.get(record, 'service', null),
            'categoryId': _.get(record, 'category_id', _.get(record, 'categoryId', null)),
            'customerMobile': _.get(record, 'customer_mobile', _.get(record, 'customerMobile', null)),
            'customerEmail': _.get(record, 'customer_email', _.get(record, 'customerEmail', null)),
            'notificationStatus': _.get(record, 'notification_status', _.get(record, 'notificationStatus', null)),
            'bankName': _.get(record, 'bank_name', _.get(record, 'bankName', null)),
            'cardNetwork': _.get(record, 'card_network', _.get(record, 'cardNetwork', null)),
            'status': _.get(record, 'status', null),
            'dbEvent': _.get(record, 'dbEvent', null),
            'debugKey': _.get(record, 'debugKey', null),
            'extra': _.get(record, 'extra', null),
            'circle': _.get(record, 'circle', null),
            'cdcEventType': _.get(record, 'cdcEventType', null),
            'paymentDate': _.get(record, 'payment_date', _.get(record, 'paymentDate', null)),
            'userData': _.get(record, 'user_data', _.get(record, 'userData', null)),
            'isAutomatic': _.get(record, 'is_automatic', _.get(record, 'isAutomatic', null)),
            'updateAt': _.get(record, 'updateAt', _.get(record, 'update_at', null)),
            'publishedDate': _.get(record, 'published_date', _.get(record, 'publishedDate', null)),
            'retry': _.get(record, 'retry_count', _.get(record, 'retryCount', null)),
            'nextBillFetchDate': _.get(record, 'next_bill_fetch_date', _.get(record, 'nextBillFetchDate', null)),
            'nextBillFetchStartDate': _.get(record, 'next_bill_fetch_start_date', _.get(record, 'nextBillFetchStartDate', null)),
            'dueAmount': _.get(record, 'amount', _.get(record, 'amount', null)),
            'createdAt': _.get(record, 'createAt', _.get(record, 'create_at', null)) ? MOMENT.utc(_.get(record, 'createAt', _.get(record, 'create_at',))).format('YYYY-MM-DD HH:mm:ss') : null,
            'remindLaterDate': _.get(record, 'remindLaterDate', null),
            'remindLaterFlow': _.get(record, 'remindLaterFlow', null),
            'oldBillFetchDate': _.get(record, 'oldBillFetchDate', null),
            'is_encrypted': isCCEncryptionEnabled ? 1 : 0
        };
        cdcPayload.topic = _.get(self.config.KAFKA, 'SERVICES.CASSANDRA_CDC.TOPIC', 'CDC_RECOVERY');
        return cdcPayload;
    }

    preparePayloadForDataExhaust(processedRecord) {
        let self = this;
        let payload = {
            source: "dataExhaust",
            notificationType: "BILLDUE",
            data: {
                customer_id: processedRecord.customerId,
                recharge_number: processedRecord.rechargeNumber,
                product_id: _.get(processedRecord, 'productId'),
                operator: processedRecord.operator,
                due_amount: _.get(processedRecord, "amount"),
                data_consumed: _.get(processedRecord, "dataConsumed"),
                bill_fetch_date: MOMENT(),
                paytype: "prepaid",
                service: _.get(processedRecord, "service"),
                circle: _.get(processedRecord, "circle"),
                customer_mobile: null,
                customer_email: null,
                status: self.config.COMMON.bills_status.BILL_FETCHED,
                user_data: null,
                bill_date: null,
                notification_status: 1,
                due_date: _.get(processedRecord, "dueDate"),
                customer_other_info: _.get(processedRecord, "customerOtherInfo", '{}'),
                plan_bucket: _.get(processedRecord, "planBucket"),
                templates: _.get(processedRecord, "templates"),
                isRealTimeDataExhausted: _.get(processedRecord, "isRealTimeDataExhausted"),
                time_interval: parseInt(_.get(processedRecord, "time_interval")),
                extra: _.get(processedRecord, "extra", '{}'),
                dwhKafkaPublishedTime: _.get(processedRecord, 'dwhKafkaPublishedTime', null),
                nonpaytm_onBoardTime: _.get(processedRecord, 'nonpaytm_onBoardTime', null),
                nonPaytmAcknowledgeTime: _.get(processedRecord, 'nonPaytmAcknowledgeTime', null),
                billFetchReminder_onBoardTime: new Date().getTime(),
                billFetchReminder_onBoardTime_dummy: _.get(processedRecord, "nonpaytm_onBoardTime", null),
                isRnDecrypted: true
            }
        }
        _.set(payload, 'topic', _.get(self.config.KAFKA, 'SERVICES.NONRU_NOTIFICATION_PIPELINE_REALTIME.NONRU_NOTIFICATION_REALTIME', ''));
        return payload;
    }

    preparePayloadForNotificationReject(processedRecord) {
        let self = this;
        try {
            let customerOtherInfo = utility.parseCustomerOtherInfo(processedRecord.customerOtherInfo);
            let payload = {
                "recharge_number": _.get(processedRecord, 'rechargeNumber', null),
                "customer_id": _.get(processedRecord, 'customerId', null),
                "operator": _.get(processedRecord, 'operator', null),
                "product_id": _.get(processedRecord, 'productId', null),
                "service": _.get(processedRecord, 'service', null),
                "paytype": _.get(processedRecord, 'paytype', null),
                "bill_source": "NONRU",
                "message_id": _.get(customerOtherInfo, 'msgId', null)
            }
            return payload;
        } catch (err) {
            self.L.error("preparePayloadForNotificationReject: ", "Error in preparing payload for notification reject", err);
            return null;
        }
    }
}

export default preparePayload