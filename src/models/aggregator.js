let L = null;

class Aggregator {

    constructor(options) {
        L = options.L;
        this.L = options.L;
        this.config = options.config;
        this.dbInstance = options.dbInstance;
    }

    getDailyReportData (tableName, startDate, endDate, done) {
        let self  = this,
          query = `SELECT
                    service,
                    operator,
                    status as statusLabel,
                    count(*) as statusCount
                  FROM
                    ${tableName}
                  WHERE
                    published_date >= ?
                  AND
                    published_date <= ?
                  GROUP BY
                    status,
                    operator,
                    service`;
        
   		self.dbInstance.exec(function (err, data){
            if(err || !(data)) {
                L.error('getBill::', 'error occurred while gettin Daily Report Data from DB: ', err);
            }
            done(err, data);

        }, 'DIGITAL_REMINDER_SLAVE', query, [
            startDate,
            endDate
        ]);
    }	

    fetchEligibleRecordsForPublishCount(tableName , params, done) {
        let self  = this,
        query =  `SELECT 
                    service,
                    operator,
                    count(1) as recordsCount
                  FROM 
                    ${tableName} 
                  WHERE 
                    status not in (${params.statuses.join(',')})
                  AND 
                    retry_count < ? 
                  AND 
                    next_bill_fetch_date > ? 
                  AND 
                    next_bill_fetch_date < ?`;

        if(tableName == "bills_creditcard") {
          query += ` AND consent_valid_till >= now()`;
        }

      query += ' GROUP BY service,operator';

        L.verbose('fetchFreshRecordsCount',query,params);

        self.dbInstance.exec(function(error, data) {
            if(error || !(data)) {
                L.error('fetchFreshRecordsCount','error in fetching data from db ->  ', error);
                return done(error);
            }
            else {
                done(null, data)
            }
        }, 'DIGITAL_REMINDER_SLAVE', query, [
            params.retryCount,
            params.nextBillFetchDateFrom,
            params.nextBillFetchDateTo
        ]);
    }
  
    getBillDateStats(tableName, startDate, endDate, billDate, done) {
      let self = this,
        query = `SELECT
                      service,
                      operator,
                      count(*) as totalbillfetched,
                      AVG(DATEDIFF(bill_fetch_date,bill_date)) as avgDayDiff,
                      count(CASE WHEN DATE(bill_date) = DATE(bill_fetch_date) THEN 1 END) AS sameDayCount
                    FROM
                      ${tableName}
                    WHERE
                      bill_fetch_date >= ?
                    AND
                      bill_fetch_date <= ?
                    AND
                      status=4
                    AND
                      bill_date > ?
                    GROUP BY
                      service,
                      operator`;
    
      L.verbose("getBillDateStats:", query, startDate, endDate, billDate);
    
      self.dbInstance.exec(
           function (error, data) {
                if (error || !data) {
                     L.error(" getBillDateStats", "error in fetching data from db ->  ", error);
                     return done(error);
                } else {
                     done(null, data);
                }
           },
           "DIGITAL_REMINDER_SLAVE",
           query,
           [startDate, endDate, billDate]
      );
    }
}

export default Aggregator