    import _ from 'lodash'

class CatalogVerticalRecharge {
    constructor (options) {
        this.L = options.L;
        this.dbInstance = options.dbInstance;
    }

    getCvrData(callback, condition) {
        let self = this,
            query = 'SELECT * from catalog_vertical_recharge';
        if(condition){
            query += ' where '+condition;
        }

        self.dbInstance.exec(function (err, data) {
            if (err) {
                self.L.error('getCvrData: ', 'error occurred while getting cvr data from the DB: ', query, err);
            }
            callback(err, data);
        }, 'FS_RECHARGE_SLAVE1', query, []);
    }

    /**
     * 
     * @param {*} callback of calling function
     * @param {*} condition on which we would fetch data
     * @param {*} columns string with comma seperated columns "id,product_id" - Default(*)
     */
    getSpecificDataFromCvr(callback, condition,columns) {
        let self = this,
            query = '';

        if(_.isEmpty(columns)) columns = "product_id, category_id, brand, paytype, service, operator, circle, operator_label, min_amount, attributes, thumbnail, status";
        
        query = `SELECT ${columns} from catalog_vertical_recharge`;
        
        if(condition){
            query += ' where '+condition;
        }

        self.dbInstance.exec(function (err, data) {
            if (err) {
                self.L.error('getCvrData: ', 'error occurred while getting cvr data from the DB: ', query, err);
            }
            callback(err, data);
        }, 'FS_RECHARGE_SLAVE1', query, []);
        
    }

}

export default CatalogVerticalRecharge;
