import _, { reject } from 'lodash';
import utility from '../lib/index.js'
class PaymentRemindLaterEvents {
  constructor(options) {
    this.L = options.L;
    this.recentRemindLaterEventsCache = 'payment_remind_later_events';
    this.notificationNewClusterClient = options.notificationNewClusterClient;
    this.commonLib = new utility.commonLib(options);
  }

  getDate() {
    return new Date();
  }

  async insertPaymentRemindLaterEvent(params) {
    const self = this;
    const now = new Date();
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    const ttl = Math.round((endOfDay.getTime() - now.getTime()) / 1000);

    let updateQuery = `UPDATE ${self.recentRemindLaterEventsCache} USING TTL ? SET remind_later_data[?] = ? WHERE`;
    let updateQueryParams = [ttl, params.customer_id, params.remindLaterDate];
    [updateQuery, updateQueryParams] = self.commonLib.formatQueryAndParams(updateQuery, updateQueryParams, params.recharge_number, params.operator);

    updateQuery += ` AND customer_id = ? AND service = ? AND operator = ?`;
    updateQueryParams.push(params.customer_id, _.toLower(params.service), _.toLower(params.operator));

    const batchQuery = [
        {
            query: updateQuery,
            params: updateQueryParams
        }
    ]

    return new Promise((resolve, reject) => {
        this.notificationNewClusterClient.batch(batchQuery, { prepare: true })
        .then(() => {
            self.L.log(`insertPaymentRemindLaterEvent::Data updated on cluster for ${JSON.stringify(params)}`);
            resolve(null)
        })
        .catch(error => {
            self.L.error(`insertPaymentRemindLaterEvent:: Record not updated for ${JSON.stringify(params)}, error ${error}`)
            reject(`insertPaymentRemindLaterEvent:: Record not updated for ${JSON.stringify(params)}, error ${error}`);
        })
    })
}
}

export default PaymentRemindLaterEvents;