import _ from 'lodash'
import utility from '../lib'
class AllTransactionsCounter {
    constructor(options) {
        let self = this;
        self.L = options.L;
        self.config = options.config;
        self.dbInstance = options.dbInstance;
    }

    fetchCustomerDetails(cb, customerId) {
        let self = this;
        self.L.info(`fetchCustomerDetails:: reading record in customer_info table for customerId:${customerId}`);
        const query = `SELECT * FROM customer_transaction_counter where customer_id = ${customerId};`

        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('fetchCustomerDetails::', query, error);
            }
            return cb(error,res);
        }, 'DIGITAL_REMINDER_SLAVE', query);
    }

    writeCustomerDetails(cb, customerInfo) {
        let self = this;
        self.L.info(`writeCustomerDetails:: writing record in customer_info table for customerId:${customerInfo.customer_id}_whatsapp_notification_status:${customerInfo.whatsapp_notification_status}_counter:${customerInfo.counter}`);
        const query = `INSERT INTO customer_transaction_counter \
        (customer_id, is_retailer, min_date, counter, whatsapp_notification_status) VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE \ 
        is_retailer=VALUES(is_retailer), min_date=VALUES(min_date), counter = VALUES(counter), whatsapp_notification_status = VALUES(whatsapp_notification_status);`

        const params = [
            customerInfo.customer_id,
            customerInfo.is_retailer,
            customerInfo.min_date,
            customerInfo.counter,
            customerInfo.whatsapp_notification_status
        ]

        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('writeCustomerDetails::', query, error);
            }
            return cb(error,res);
        }, 'DIGITAL_REMINDER_MASTER', query, params);
    }

    writeCustomerDetailsForUpdateUserConsent(cb, customerInfo) {
        let self = this;
        self.L.info(`writeCustomerDetailsForUpdateUserConsent:: writing record in customer_info table for customerId:${customerInfo.customer_id}_whatsapp_notification_status:${customerInfo.whatsapp_notification_status}_counter:${customerInfo.counter}`);
        const query = `INSERT INTO customer_transaction_counter \
        (customer_id, is_retailer, min_date, counter, whatsapp_notification_status) VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE \ 
        whatsapp_notification_status = VALUES(whatsapp_notification_status);`

        const params = [
            customerInfo.customer_id,
            customerInfo.is_retailer,
            customerInfo.min_date,
            customerInfo.counter,
            customerInfo.whatsapp_notification_status
        ]

        self.dbInstance.exec((error, res) => {
            if (error) {
                self.L.critical('writeCustomerDetailsForUpdateUserConsent::', query, error);
            }
            return cb(error,res);
        }, 'DIGITAL_REMINDER_MASTER', query, params);
    }

    fetchCustomerRetailerStatus(cb, customerId){
        let self = this;
        self.fetchCustomerDetails(function (error, result) {
            if(error) {
                console.log(error)
                return cb(error)
            }
            const isRetailer = _.get(result[0], 'is_retailer', 0);
            if(isRetailer == 1) {
                self.L.log(`disableSmsForUser:: SMS blocked for retailer like customer ${customerId}`)
                utility._sendMetricsToDD(1, ['REQUEST_TYPE:RETAILER_SMS_SKIPPED', 'STATUS:RETAILER_LIKE']);
            }
            return cb(null, isRetailer);
        }, customerId)
    }

    fetchCustomerWhatsAppNotificationStatus(cb, customerId){
        let self = this;
        self.L.info(`fetchCustomerWhatsAppNotificationStatus:: reading record in customer_info table for customerId:${customerId}`);
        const query = `SELECT whatsapp_notification_status FROM customer_transaction_counter where customer_id = ${customerId};`

        self.dbInstance.exec((error, result) => {
            if (error) {
                self.L.critical('fetchCustomerWhatsAppNotificationStatus::', query, error);
                return cb(error,null);
            }
            let whatsapp_notification_status = _.get(result, [0 , 'whatsapp_notification_status'] , 
                                                    _.get(self.config, ['COMMON', 'USER_PREFERENCES_WHATSAPP_NOTIFICATION_STATUS' , 'NOT_AVAILABLE'], -1)
                                                );
            self.L.log(`fetchCustomerWhatsAppNotificationStatus:: customer:${customerId}_whatsapp_notification_status:${whatsapp_notification_status}`)
            return cb(error,whatsapp_notification_status);
        }, 'DIGITAL_REMINDER_SLAVE', query);
    }
}

export default AllTransactionsCounter
