#!/usr/bin/env bash
#

ENV=$1

abort() {
  echo
  echo "  $@" 1>&2
  echo
  exit 1
}

log() {
  echo "  ○ $@"
}

#create symlink for logs
if test -e logs ; then
  log 'logs dir is present'
else
  ln -sf ../shared/logs
fi

log "creating release file"
echo "module.exports = '$(git describe)'" > release

echo "Running npm install in $PWD"
npm install
rc=$?
if [[ $rc != 0 ]] ; then
    abort 'NPM install failed ' $rc
fi

echo "Running npm run build in $PWD"
npm run build
rc=$?
if [[ $rc != 0 ]] ; then
    abort 'npm run build failed ' $rc
fi
 
log 'Post deploy script has been executed successfully.'

# if test -e '/etc/init/ffrecharge.conf' ; then
#   log 'upstart config is okay'
# else
#   log '**** Upstart configuration is missing ****'
#   exit 1
# fi

# Display nginx maintainence page
#log 'Starting maintainence window'
#cp public/system/.maintenance.html public/system/maintenance.html

#log 'Touching restart file'
touch public/system/restart
# wait for a max of 1 minute - this is the time to restart
sleep 15

# remove the file if it still exists
#rm -f public/system/maintenance.html
log 'Deployment complete'
