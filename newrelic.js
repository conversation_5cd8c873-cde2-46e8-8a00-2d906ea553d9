'use strict'

/**
 * New Relic agent configuration.
 *
 * See lib/config.default.js in the agent distribution for a more complete
 * description of configuration variables and their potential values.
 */
exports.config = {
  /**
   * Array of application names.
   */
  app_name: ['Digital Reminder'],
  /**
   * Your New Relic license key.
   */
  license_key: 'dd15ffc19c86684d75ca2936c084d0e6717dc0c5',
  logging: {
    /**
     * Level at which to log. 'trace' is most useful to New Relic when diagnosing
     * issues with the agent, 'info' and higher will impose the least overhead on
     * production applications.
     */
    level: 'info'
  },
  allow_all_headers: true,
  enforce_backstop: false
}

