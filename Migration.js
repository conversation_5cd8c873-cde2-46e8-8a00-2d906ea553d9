/*jshint multistr: true ,node: true*/
"use strict";

var
	//3rd party NPM
	ASYNC 	= require('async'),
	UTIL 	= require('util'),
	Q 		= require('q'),
	_ 		= require('lodash'),
	MOMENT  = require('moment'),
    FS      = require('fs'), 
    PATH    = require('path'),
    VALIDATOR = require('validator'),

	//PTM NPM
	L 		= require('lgr'),
	SQL 	= require('sqlwrap'),

	CONFIG = require('./dist/config'),

	//Global variable
	TOKEN 		= null;

	var sqlConfig = {
        MYSQL_CLUSTER: {
            RECHARGE_ANALYTICS       : {
                host    : 'rchrganadbs.mkt.paytm',
                // user    : '',
                // password: '',
                database: 'recharge_analytics',


                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true  
            },
            RECHARGE_BILLS       : {
                host    : 'digireminderdbp.mkt.paytm',
                // user    : '',
                // password: '',
                database: 'digital_reminder',


                connectionLimit     : 5,
                waitForConnections  : true,
                queueLimit          : 0,
                acquireTimeout      : 120000, // milliseconds
                multipleStatements  : true  
            }
        },
        MYSQL_CLUSTER_SETTING: {
            canRetry            : true,
            restoreNodeTimeout  : 3000,
            removeNodeErrorCount: 10000
        }
    }

function tem(gateway) {
	console.log(gateway);
	
	this.db = new SQL(sqlConfig);
	this.count = 0;
	this.gateway = gateway;


}

/*
	FLOW:
		Plan for Bill fetch of subscription reccords.
        Fetch records from subscription table where due date is null or due date is less than 5 days ahead.
        Fetch corresponding record in Bills table.
        If record found call Bill fetch for the same.
        Otherwise fetch record from ES on the basis of recharge number, product id and success status sort by timestamp.
        If gateway is cyberplat the replace it by reliancenew gateway.
        Create record in Bills table for the same.
        Call the Bill fetch for the same.
*/


tem.prototype.start12 = function(startId) {

	var
		self = this,
		defer = Q.defer();

		self.db.exec(function(err, rows, fields, poolName, query, params, absoluteQuery) {

	        L.verbose('renewalNotification :: querySlave', 'QUERY RESPONSE : ERR#', err, 'RESULT#',rows? rows.length: 0);
	        if (err) {
	            L.critical('renewalNotification :: querySlave', 'ERROR ENCOUNTERED: ERROR#', err, ' QUERY#',query);
	            defer.reject(err);
	        } else{
	           defer.resolve(rows);
	        }
	    }, 'RECHARGE_ANALYTICS', 'select       id,      cust_id,                      recharge_number,                      product_id,                      operator,                      currentGw,                      nextBillFetchDate,                      service,                      paytype,                      circle,                      cust_mobile,                      cust_email,                      payment_channel,                      amount, bill_fetch_date , due_date from bills where id > '+startId+' and product_id in (40703585,39211986) order by id limit 1000 ', []);
		return defer.promise;
}

tem.prototype.start = function(cb,startId) {

	var
		self = this;

	Q(undefined)
	.then(function(){

		return self.start12(startId);
	})
	.then(function(data){

		self.count += data.length;

		console.log('count = ',self.count, ', last id =',data[data.length-1].id );

		var data1 = [];

		data.forEach(function(d){
			var data2 = [];
			Object.keys(d).forEach(function(w){
				 
				if(w!=='id' && w!='nextBillFetchDate') {
					data2.push(d[w]);
				} else if (w === 'nextBillFetchDate' ) {
					data2.push(MOMENT(d[w]).format('YYYY-MM-DD 23:00:00'));
				}
			})

			data1.push(data2);
		});

	//	console.log(data1)

if( data && data.length <=0 ) return Q.resolve(data);
else {

	var defer = Q.defer();

		self.db.exec(function(err, rows, fields, poolName, query, params, absoluteQuery) {

	        L.verbose('abc :: querySlave', 'QUERY RESPONSE : ERR#', err, 'RESULT#',rows? rows.length: 0);

	        if (err) {
	            L.critical('def :: querySlave', 'ERROR ENCOUNTERED: ERROR#', err, ' QUERY#',query);
	            defer.reject(err);
	        } else{
	            defer.resolve(data);
	        }
	    }, 'RECHARGE_BILLS', 'INSERT IGNORE                     INTO bills_bses (                      customer_id,                      recharge_number,                      product_id,                      operator,                      gateway, next_bill_fetch_date,                                          service,                      paytype,                      circle,                      customer_mobile,                      customer_email,                      payment_channel,                      amount  , bill_fetch_date, due_date          )                    VALUES ?', [data1]);
}
	return defer.promise;
	})
	.then(function(data){

		if(data.length <=0 ) {
			cb();
		} else {
			return self.start(cb,data[data.length-1].id)
		}
	})

	
};


/*
insert into bills(cust_id, recharge_number, product_id, operator, created_at, currentGw, nextBillFetchDate) values(10854867, '0610915028', 33683541, 'bescom', '2016-04-21 01:00:12', 'bescomrapdrp', '2016-04-26 01:00:13');
*/

(function(){
	if(require.main === module) {
		L.setLevel('verbose');//have to comment while production deployment

		var gateway = process.env.GATEWAY;

		var cron = new tem(gateway);
		cron.start(function(){
			process.exit();
		},0);
	}
}());