swagger: '2.0'
info:
  title: <PERSON>minder Service
  description: <PERSON> service API Doc 
  version: "13.12.2017"
# the domain of the service
host: localhost:7000
# array of all schemes that your API supports
schemes:
  - https
basePath: /
produces:
  - application/json
paths:
  /v1/getBill:
    get:
      summary: Start "Fetch Bill Details" process
      description: Fetch Bill on the basis of operator,productId,rechargeNumber and customerId
      parameters: 
        - name: customerId
          in: query
          type: integer
          required: true
        - name: productId
          in: query
          type: integer
          required: true
        - name: rechargeNumber
          in: query
          type: string
          required: true
        - name: operator
          in: query
          type: string
          required: true
      tags:
        - Public API for Bills
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/GetBillDetails'
        responses:
        '422':
          description: ''
          schema:
            $ref: '#/definitions/GetBillInvalidResponse'
        '500':
          description: ''
          schema:
            $ref: '#/definitions/PublicErrorResponse'
  
  /v1/createBill:
    post:
      summary: Create Bill Record 
      description: Api will create the bill record on operator basis
      tags: 
        - Public API for Bills
      parameters:
        - name: Request
          in: body
          schema:
            $ref: '#/definitions/CreateBillRequest'
          required: true
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/CreateBillResponse'
        responses:
        '422':
          description: ''
          schema:
            $ref: '#/definitions/GetBillInvalidResponse'
        '500':
          description: ''
          schema:
            $ref: '#/definitions/PublicErrorResponse'
        
  /v1/createMultipleBill:
    post:
      summary: Create Multiple Bill Records 
      description: Api will create the multiple bill record on operator basis
      tags: 
        - Public API for Bills
      parameters:
        - name: Request
          in: body
          schema:
            type: array
            items: 
              type: schema
              $ref: "#/definitions/CreateBillRequest"
      responses:
        '200':
          description: ''
          schema:
            $ref: '#/definitions/CreateBillResponse'
        responses:
        '422':
          description: ''
          schema:
            $ref: '#/definitions/GetBillInvalidResponse'
        '500':
          description: ''
          schema:
            $ref: '#/definitions/PublicErrorResponse'
        

definitions:
  # Request objects
  PublicErrorResponse:
    properties:
      error:
        type: string
        description: 'error description' 
  GetBillInvalidResponse:
    properties:
      error_message:
        type: string
        description: 'error description' 
  GetBillDetails:
    properties:
      status:
        type: string
      data:
        type: objects
        $ref: '#/definitions/BillRecord'
  CreateBillResponse:
    properties:
      status:
        type: string
      data:
        type: string

  CreateBillRequest:
    properties:
      customerId:
        type: integer
        description: 'Customer Id'
      rechargeNumber:
        type: string
        description: 'Recharge Number'
      productId:
        type: integer
      operator:
        type: string
      amount:
        type: integer
      dueDate:
        type: string
      billFetchDate:
        type: string
      nextBillFetchDate:
        type: string
      gateway:
        type: string
      paytype:
        type: string
      service:
        type: string
      circle:
        type: string
      customerMobile:
        type: string
      customerEmail:
        type: string
      paymentChannel:
        type: string
  BillRecord:
    properties:
      id:
        type: integer
        description: 'Database generated id'
      customerId:
        type: integer
        description: 'Customer Id'
      rechargeNumber:
        type: string
        description: 'Recharge Number'
      productId:
        type: integer
      operator:
        type: string
      amount:
        type: integer
      dueDate:
        type: string
      billFetchDate:
        type: string
      nextBillFetchDate:
        type: string
      gateway:
        type: string
      paytype:
        type: string
      service:
        type: string
      circle:
        type: string
      customerMobile:
        type: string
      customerEmail:
        type: string
      paymentChannel:
        type: string
      retryCount:
        type: integer
      status:
        type: string
      reason:
        type: string
      createdAt:
        type: string
      updatedAt:
        type: string