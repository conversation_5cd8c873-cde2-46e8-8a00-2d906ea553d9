lua_package_path '$prefix/lua/?.lua;;';

server {
 listen 80;

 root /var/www/digital-reminder/releases/current/public;

 access_log /var/log/nginx/digitalreminder.access.log main;
 error_log  /var/log/nginx/digitalreminder.error.log;

log_by_lua_file lua/datadog/api_latency.lua;

location ~ ^/bills/ {
    rewrite /bills/(.*) /$1  break;
    proxy_pass http://127.0.0.1:7000;
}

 location / {
   proxy_pass http://127.0.0.1:7000;
 }
 }