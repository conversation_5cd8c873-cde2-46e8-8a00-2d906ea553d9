#!/bin/bash

# Relative folder path of the git hooks
GIT_HOOK_DIR=.git/hooks

# Custom hooks directory
CUSTOM_HOOK_DIR=git-hooks/src

# Relative folder path of the custom hooks to make symlinks 
CUSTOM_HOOK_RELATIVE_PATH=../../git-hooks/src

echo "Installing git hooks"

for hook_path in "$CUSTOM_HOOK_DIR"/*
do
   hook=$(basename "$hook_path")
   echo "setting hook $hook"
   ln -s -f $CUSTOM_HOOK_RELATIVE_PATH/$hook $GIT_HOOK_DIR/$hook
done
